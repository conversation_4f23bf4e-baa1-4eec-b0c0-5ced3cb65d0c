syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;
import "ResKeywords.proto";
import "common.proto";

message MasterPathInfo{
  optional int32 cycle = 1;
  optional int32 masterPatchEnum = 2;
  optional int32 unlockState = 3; //0未解锁 1解锁
  optional int32 progress = 4;  //当前进度
  repeated int32 rewardId = 5;  //领取过的奖励id
}


// 获取大师之路信息
message GetMasterPatchInfo_C2S_Msg {
  optional int32 masterPatchEnum = 1;
  optional int32 cycle = 2;//周目 传入0代表对应枚举大师之路所有周目信息
}

message GetMasterPatchInfo_S2C_Msg {
  repeated MasterPathInfo info = 1;
}


 //领取阶段奖励
message ReceiveMasterPatchReward_C2S_Msg {
  optional int32 id = 1;  //传入0代表一键领取
  optional int32 masterPatchEnum = 2;
  optional int32 cycle = 3;//周目
}

message ReceiveMasterPatchReward_S2C_Msg {
  optional MasterPathInfo masterPathInfo = 1;
}


// 大师之路解锁
message MasterPatchUnLockNtf {
  optional MasterPathInfo info = 1;
}


// 大师之路进度改变
message MasterPatchChangeNtf {
  optional int32 cycle = 1;
  optional int32 masterPatchEnum = 2;
  optional int32 progress = 3;  //改变后的进度
}

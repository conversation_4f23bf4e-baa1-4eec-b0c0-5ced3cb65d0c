syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "ResKeywords.proto";
import "common.proto";
import "attr_AlbumPicInfo.proto";
import "attr_AlbumPicNewLikeHis.proto";
import "attr_AlbumPicLikeHisInfo.proto";
import "attr_PicLikeCountInfo.proto";
import "attr_AlbumExtInfo.proto";
import "attr_AlbumPicExtInfo.proto";
import "attr_AlbumLimitInfo.proto";
import "attr_AlbumPicTargetAtInfo.proto";

// 增加照片
message PlayerAlbumPictureAdd_C2S_Msg {
  optional proto_AlbumPicInfo pic = 1;
}

message PlayerAlbumPictureAdd_S2C_Msg {
  optional uint32 totalNum = 1;                 // 已有图片数(新增图片前已经过审的图片数量+1)
}

// 删除照片
message PlayerAlbumPictureDel_C2S_Msg {
  optional string picKey = 1;
}

message PlayerAlbumPictureDel_S2C_Msg {
}

// 编辑照片
message PlayerAlbumPictureEdit_C2S_Msg {
  optional string picKey = 1;
  optional string cosPath = 2;
  //缩略图cos路径
  optional string thumbnailCosPath = 3;
}

message PlayerAlbumPictureEdit_S2C_Msg {
  optional uint32 totalNum = 1;                 // 已有图片数(编辑图片前已经过审的图片数量)
}

// 拉取个人相册图片信息列表请求
message PlayerAlbumPictureGet_C2S_Msg {
  optional uint32 page = 1;						          // 当前页码(废弃)
  optional uint32 pageSize = 2;					        // 每页数目(废弃)
  optional int64 targetUid = 3;					        // 客态用户uid
  optional int64 dataVersion = 4;               // 数据版本号
  optional int64 createTimeSec = 5;             // 图片创建时间, 拉取早于该时间的pageSize个图片(废弃)
}

// 拉取个人相册图片信息列表响应
message PlayerAlbumPictureGet_S2C_Msg {
  repeated proto_AlbumPicInfo picInfoList = 1;	// 图片信息, 已按照图片信息中的createTimeSec倒序排列
  optional uint32 totalNum = 2;					        // 总数
  optional uint32 totalPage = 3;				        // 分页总页码(废弃)
  optional int64 dataVersion = 4;               // 数据版本号
  optional int64 uid = 5;                       // 数据所属uid
  optional proto_AlbumExtInfo albumExtInfo = 6; // 个人相册扩展信息
}

enum PicFormatConvertType {
  PIC_FORMAT_CONVERT_TYPE_ASTC_TO_PNG = 0;
}

// 个人相册图片格式转换请求
message PlayerAlbumPictureFormatConvert_C2S_Msg {
  optional string sourceCosPath = 1;			      // 图片源格式cos路径
  optional uint32 type = 2;						          // 转换类型, 详情见PicFormatConvertType枚举所示
}

// 个人相册图片格式转换响应
message PlayerAlbumPictureFormatConvert_S2C_Msg {
  optional string targetCosPath = 1;			      // 图片目标格式cos路径
}

// 个人相册图片操作类型
enum PlayerAlbumPictureOperateType {
  PLAYER_ALBUM_PICTURE_OPERATE_TYPE_ADD = 0;    // 图片添加
  PLAYER_ALBUM_PICTURE_OPERATE_TYPE_DEL = 1;    // 图片删除
  PLAYER_ALBUM_PICTURE_OPERATE_TYPE_UPDATE = 2; // 图片更新
  PLAYER_ALBUM_PICTURE_OPERATE_TYPE_Like = 3; // 图片点赞
  PLAYER_ALBUM_PICTURE_OPERATE_TYPE_SETTING = 4; // 图片设置
  PLAYER_ALBUM_PICTURE_OPERATE_TYPE_EDIT = 5; // 图片编辑
}

// 个人相册图片操作信息
message PlayerAlbumPictureOperateInfo {
  optional int32 operateType = 1;               // 操作类型, 详情见PlayerAlbumPictureOperateType枚举所示
  optional proto_AlbumPicInfo picInfo = 2;      // 图片信息
  optional proto_AlbumPicExtInfo picExtInfo = 3; // 图片扩展信息
}

// 个人相册图片操作通知
message PlayerAlbumPictureOperateNtf {
  repeated PlayerAlbumPictureOperateInfo operateInfoList = 1; // 图片操作信息列表
  optional int64 uid = 2;                                     // 数据所属uid
  optional int64 dataVersion = 3;                             // 数据版本号
}

// 个人相册图片检测请求(用于图片保存时的前置判重检查)
message PlayerAlbumPictureCheck_C2S_Msg {
  optional string picKey = 1;                   // 图片key
}

// 个人相册图片检测响应
message PlayerAlbumPictureCheck_S2C_Msg {
  optional bool isExist = 1;                    // 图片是否存在
  optional uint32 totalNum = 2;                 // 已有图片数
}

// 个人相册图片检测请求(用于图片保存时的前置判重检查)
message PlayerAlbumPictureTextCheck_C2S_Msg {
  optional string checkText = 1;
}

// 个人相册图片检测响应
message PlayerAlbumPictureTextCheck_S2C_Msg {
  optional int32 ret = 1;
}


// 对图片点赞
message PlayerPictureLike_C2S_Msg {
  optional string picKey = 1;
  optional int64 uid = 2;           // 数据所属uid
}

message PlayerPictureLike_S2C_Msg {
}

/***************************** 照片墙 ***********************************/
// 添加照片到照片墙
message AddPicToPicWall_C2S_Msg {
  repeated string picKeyList = 1;
}
message AddPicToPicWall_S2C_Msg {

}

message DelPicFromPicWall_C2S_Msg {
  optional string picKey = 1;
}
message DelPicFromPicWall_S2C_Msg {

}

// 对照片墙图片点赞
message PlayerPicWallLike_C2S_Msg {
  optional string picKey = 1;
  optional int64 uid = 2;           // 数据所属uid
}
message PlayerPicWallLike_S2C_Msg {
}

// 照片墙的图片信息
message PicWallPicInfo {
  optional proto_AlbumPicInfo picInfo = 1;  // 图片信息
  optional int32 likeNum = 2;               // 点赞数
  optional bool isLiked = 3;                // 是否已点赞
  optional int64 uid = 4;                   // 拍摄人
}
// 获取最高点赞的照片墙图片
message GetPicWallTopLikeList_C2S_Msg {
  optional com.tencent.wea.xlsRes.AlbumPicLabelType labelType = 1;  // 标签类型
  optional int32 page = 2;
  optional int32 pageSize = 3;
}
message GetPicWallTopLikeList_S2C_Msg {
  repeated PicWallPicInfo picList = 1;
  optional int32 totalNum = 2;
}

// 获取最新发布的照片墙图片
message GetPicWallLatestList_C2S_Msg {
  optional com.tencent.wea.xlsRes.AlbumPicLabelType labelType = 1;  // 标签类型
  optional int32 page = 2;
  optional int32 pageSize = 3;
}
message GetPicWallLatestList_S2C_Msg {
  repeated PicWallPicInfo picList = 1;
  optional int32 totalNum = 2;
}

// 获取我的照片墙图片
message GetPicWallMyPicList_C2S_Msg {

}
message GetPicWallMyPicList_S2C_Msg {
  repeated PicWallPicInfo picList = 1;
}
/***********************************************************************/



//个人图片点赞记录
message PlayerPictureLikeHis_C2S_Msg {
  optional int32 type = 1;//0获取所有数据,1获取相册历史点赞,2获取单个图片历史点赞
  optional string picKey = 2;//type=2时生效
}

message PlayerPictureLikeHis_S2C_Msg {
  repeated proto_AlbumPicNewLikeHis picLikeHis = 1;//所有图片历史点赞记录取前50条
  repeated proto_AlbumPicLikeHisInfo picLikeHisList = 2;//所有单个点赞图片记录
}

// 个人相册图片点赞操作通知
message PlayerAlbumPictureLikeOperateNtf {
  optional int64 uid = 1;                                     // 数据所属uid
  optional string picKey = 2;                                 // picKey
  optional int64 dataVersion = 3;                             // 数据版本号
}


//个人图片点赞数
message PlayerPictureLikeCount_C2S_Msg {
  optional int64 uid = 1;     // 数据所属uid
  optional string picKey = 2; //单个图片最新点赞数
}

message PlayerPictureLikeCount_S2C_Msg {
  optional int64 uid = 1;     // 数据所属uid
  optional string picKey = 2; //单个图片最新点赞数
  optional int32 likeCount = 3;//点赞数
  optional bool isLike = 4;//是否点赞了
}

//批量获取个人图片点赞数
message PlayerBatchPictureLikeCountGet_C2S_Msg {
  optional int64 uid = 1;     // 数据所属uid
  repeated string picKey = 2;
}

message PlayerBatchPictureLikeCountGet_S2C_Msg {
  optional int64 uid = 1;     // 数据所属uid
  repeated proto_PicLikeCountInfo picLikeCountInfoList = 2;
}

//图片设置
message PictureSetting{
  optional string picKey = 1;
  optional bool isHide = 2;   //是否隐藏,true隐藏
  optional int32 topTime = 3; //置顶时间,0未置顶
}


//批量对图片操作
message PlayerBatchPictureSetting_C2S_Msg {
  repeated PictureSetting picSetting = 1;
}

//批量对图片操作返回
message PlayerBatchPictureSetting_S2C_Msg {
  optional int32 ret = 1;
}

//图片@好友
message PlayerAlbumPicBatchAtTarget_C2S_Msg {
  repeated int64 uid = 1;
  optional string picKey = 2;
}

//图片@好友返回
message PlayerAlbumPicBatchAtTarget_S2C_Msg {
  optional int32 ret = 1;
  optional proto_AlbumPicExtInfo albumPicExtInfo = 2;//图片扩展信息
}

//被好友@图片
message PlayerAlbumPicTargetAtNtf{
  optional proto_AlbumPicTargetAtInfo newAlbumPicTargetAtInfo = 1;//新的@信息
  optional int32 dateVersion = 2; //数据版本号,时间戳,提醒红点用
}

//获取玩家指定picKey图片信息
message PlayerAlbumPicInfo_C2S_Msg{
  optional int64 uid = 1;
  optional string picKey = 2;
}

message PlayerAlbumPicInfo_S2C_Msg{
  optional int64 uid = 1;
  optional proto_AlbumPicInfo albumPicInfo = 2;
  optional proto_AlbumPicExtInfo albumPicExtInfo = 3;
  optional int32 ret = 4;//0正常,-1图片不存在
}



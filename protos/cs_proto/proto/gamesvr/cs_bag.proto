syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;
import "base_common.proto";
import "common.proto";
import "ResCommon.proto";
import "ResKeywords.proto";
import "attr_Item.proto";

// 道具使用
message BagUseItemMsg_C2S_Msg {
  required int64 item_uuid = 1;
  required int32 num = 2;
  repeated int64 params = 3;
}

message BagUseItemMsg_S2C_Msg {
  required int32 res_code = 1; // 错误码 0 成功  其他失败
  required int64 item_uuid = 2;
  required int32 num = 3;
}

// 互动道具使用
message BagUseInteractItemMsg_C2S_Msg {
  required int32 itemId = 1;
  required int64 params = 2;
}

message BagUseInteractItemMsg_S2C_Msg {
  required int32 itemId = 1;
  required int64 params = 2;
}


// 道具出售(废弃)
message BagSellItemMsg_C2S_Msg {
  repeated int64 item_uuid = 1;
  repeated int32 num = 2;
}

message BagSellItemMsg_S2C_Msg {
  required int32 res_code = 1; // 错误码 0 成功  其他失败
  repeated int64 item_uuid = 2;
  repeated int32 num = 3;
  optional ItemArray items = 4;// 出售获得奖励
}


// 道具移动(废弃)
message BagMoveItemMsg_C2S_Msg {
  required int64 item_uuid = 1;
  required int32 bag_type = 2; //背包类型
  required int32 grid_id = 3; //背包格子ID
}

message BagMoveItemMsg_S2C_Msg {
  required int32 res_code = 1; // 错误码 0 成功  其他失败
  required int64 item_uuid = 2;
}

// 道具销毁(废弃)
message BagDestroyItemMsg_C2S_Msg {
  required int64 item_uuid = 1;
  required int64 destroy_num = 2;
}

message BagDestroyItemMsg_S2C_Msg {
  required int32 res_code = 1; // 错误码 0 成功  其他失败
  required int64 item_uuid = 2;
  required int64 destroy_num = 3;
}

// 检测穿戴的过期道具(废弃)
message CheckEquipItemsExpireMsg_C2S_Msg {
}

message CheckEquipItemsExpireMsg_S2C_Msg {
  repeated int64 expireItemUUIDs = 1; //过期的道具UUID
}

// 奖励通知弹窗结束(废弃)
message BagRewardNtfFinishMsg_C2S_Msg {
  repeated int32 rewardIdList = 1;
}
message BagRewardNtfFinishMsg_S2C_Msg {

}

//背包道具达到上限(废弃)
message BagItemNumUpToMaxNtf {
  repeated uint32 itemIds = 1;// 上限的道具ID
}


// 道具自动替换的原因
enum ItemAutoReplaceReason {
  IARR_None = 0;
  IARR_ExceedLimit = 1; // 超上限
  IARR_Expired = 2;     // 过期
  IARR_GiftPackageAutoOpen = 3; // 礼包自动打开
}

message BagGetItem {
  optional ItemInfo srcItem = 1;
  repeated ItemInfo actItem = 2;
  optional ItemAutoReplaceReason reason = 3;  // 转换的原因
}

//获得道具NTF
message BagCommonGetItemsNtf {
  repeated BagGetItem items = 1;
  optional int32 reason = 4;                // 道具获取的原因 枚举ItemChangeReason
  optional int64 seqId = 5;
}


message ReplaceItemInfo {
  optional ItemInfo srcItem = 1;              // 原来的道具
  optional ItemArray replaceItem = 2;         // 被转换成的道具
  optional ItemAutoReplaceReason reason = 3;  // 转换的原因
}

//道具拥有达到上限替换(废弃)
message BagItemNumUpToMaxReplaceNtf {
  repeated ReplaceItemInfo ReplaceItemInfo = 1;
}


//装扮道具穿戴
message BagDressUpItem_C2S_Msg {
  optional int64 itemUUID = 1;
  optional int32 targetPos = 2;
  optional int64 combinationId = 3;
  optional com.tencent.wea.xlsRes.InteractionType type = 4;
}

message BagDressUpItem_S2C_Msg {
  optional int64 itemUUID = 1;
  optional int32 targetPos = 2;
  optional int64 combinationId = 3;
  optional com.tencent.wea.xlsRes.InteractionType type = 4;
}
//载具佩饰
message VehicleAccessoriesItem_C2S_Msg {
  optional int64 vehicleUUID = 1;
  optional int32 targetPos = 2;
  optional int64 decorateId = 3;
  optional int64 decorateIdUUId = 4;
  optional int32 vehicleId = 5;
}
message VehicleAccessoriesItem_S2C_Msg {
  optional int32 code = 1; //0成功。1失败
  optional int64 vehicleUUID = 2;
  optional int32 targetPos = 3;
  optional int64 decorateId = 4;
  optional int64 decorateIdUUId = 5;
  optional int32 vehicleId = 6;
}
message FashionSkillUse_C2S_Msg {
  optional int32  fashionItemid = 1;
}
message FashionSkillUse_S2C_Msg {
  optional int32  code = 1;//0成功
  optional int32  fashionItemid = 2;
}

message BagDressUpSetting_C2S_Msg {
  optional bool actionSetting = 1; //
}
message BagDressUpSetting_S2C_Msg {
  optional bool actionSetting = 1; //
  optional int32 code = 2; //0成功。1失败
}
message DelItem {
  optional int32 itemId = 1;
  optional int64 uuid = 2;
}

message BagCheckItemExpire_C2S_Msg {

}

message BagCheckItemExpire_S2C_Msg {
  repeated DelItem delItems = 2;
}

message MidasBuyGoodsNtf {
  optional string urlParam = 1;
  optional string productId = 2;
  optional string signData = 3;
  optional string paySig = 4;
  optional string signature = 5;
  optional ClientRechargeMetaData clientMetaData = 6;
}

message MidasPayResult_C2S_Msg {
  optional int32 payResult = 1;
  optional string productId = 2;
}
message MidasPayResult_S2C_Msg {

}

message SelectStarterItems_C2S_Msg {
  repeated int32 itemIds = 1;
}

message SelectStarterItems_S2C_Msg {

}

message GetUnlockedSlotId_C2S_Msg {

}

message GetUnlockedSlotId_S2C_Msg {
  repeated int32 slotIds = 1;
}

message GetItemPackagePickPickedNum_C2S_Msg {
  optional int32 itemId = 1;
}

message GetItemPackagePickPickedNum_S2C_Msg {
  optional int32 itemId = 1;
  repeated int64 pickedNum = 2;
}

message BagCommonGetItemsNtfCommit_C2S_Msg {
  optional int64 seqId = 1;
  repeated int64 seqIds = 2;
}

message BagCommonGetItemsNtfCommit_S2C_Msg {
  optional int64 seqId = 1;
  repeated int64 seqIds = 2;
}

message BagItemRemoveNtf {
  repeated uint64 item_uuid = 1;
  repeated int32 itemId = 2;
}

message BagUnlockSlotId_C2S_Msg {
  optional int32 slotId = 1;
}

message BagUnlockSlotId_S2C_Msg {
  optional int32 slotId = 1;
}

message InteractionCombinationItem {
  repeated int64 itemId = 1;
  optional string content = 2;
  optional int32 orderId = 3;
}

// 设置自定义组合
message BagSetInteractionCombination_C2S_Msg {
  optional int32 targetPos = 1;   // 废弃
  optional string name = 2; // 组合名
  repeated int64 itemId = 3;      // 废弃
  optional string content = 4;    // 废弃
  repeated InteractionCombinationItem items = 5;
  optional int64 combinationId = 6; // 0: 新增  >0: 修改
  optional int32 iconId = 7; // getItemsList().get(0).getItemIdList().get(iconId)
  optional string iconName = 8; // 图标名
}

message BagSetInteractionCombination_S2C_Msg {
  optional int64 combinationId = 1;
}

// 删除自定义组合
message BagDelInteractionCombination_C2S_Msg {
  optional int32 targetPos = 1;     // 废弃
  optional int64 combinationId = 2;
}

message BagDelInteractionCombination_S2C_Msg {

}


// 更换背景图
message ChangeProfileThemeMsg_C2S_Msg {
  optional int32 id = 1;
}

message ChangeProfileThemeMsg_S2C_Msg {
  optional int32 id = 2;
}

message DressItemStatusChange_C2S_Msg {
  optional int64 itemUUID = 1;
  optional int32 status = 2;
}


message DressItemStatusChange_S2C_Msg {
  optional int64 itemUUID = 1;
  optional int32 status = 2;
}

message DressItemShowStatusChange_C2S_Msg {
  optional int64 itemUUID = 1;
  optional int32 showStatus = 2;
}

message DressItemShowStatusChang_S2C_Msg {
  optional int64 itemUUID = 1;
  optional int32 showStatus = 2;
}


// 外观搭配随机切换
message SlotRandomState {
  optional int32 slotId = 1;
  optional bool randomState = 2;
}

message ChangeSlotRandomState_C2S_Msg {
  repeated SlotRandomState slotRandomState = 1;
  optional bool randomOpen = 2; // 是否开启外观搭配随机
}

message ChangeSlotRandomState_S2C_Msg {
}

// 分解道具
message DecomposeItem_C2S_Msg {
  repeated int32 itemIdList = 1; // 道具id列表
  repeated int32 itemNumList = 2; // 道具数量列表
}
message DecomposeItem_S2C_Msg {

}


message ChangeSuitRandom_C2S_Msg {
  optional int32 slotId = 1; // 装扮方案ID
  optional int64 itemUUID = 2; // 道具UUID
  optional bool random = 3;
}

message ChangeSuitRandom_S2C_Msg {
}

// 更新背包收藏道具
message UpdateFavoriteItems_C2S_Msg {
  repeated int64 itemUuids = 1;
  optional int32 type = 2; // 0：add 1:remove
}

message UpdateFavoriteItems_S2C_Msg {
}

// 道具变化通知
message ItemChangeNtf {
  repeated proto_Item changeItemList = 1;   // 道具变化内容，isAll为true同步全量数据
  repeated int64 deleteItemList = 2;        // 删除的道具uuid
  optional bool isAll = 3;                  // 是否同步的全量数据
  optional com.tencent.wea.xlsRes.ItemBagType bagType = 4;         // 背包类型
}

// 礼包打开动画通知
message OpenGiftPackageAnimationNtf {
  optional int32 reason = 1;
  optional int32 itemId = 2;
  optional int32 itemNum = 3;
  repeated BagGetItem items = 4; //打开礼包实际获得道具
}
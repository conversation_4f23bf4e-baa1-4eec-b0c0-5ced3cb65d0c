syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;
import "common.proto";
import "ResTradingCard.proto";
import "ResKeywords.proto";
import "cs_bag.proto";
import "attr_WildCardInfo.proto";
import "attr_TradingCardClearRedDot.proto";
import "attr_TradingCardCycleCup.proto";
import "attr_TradingCardExchangeInfo.proto";

message TradingCardDisplayInfo {
  optional int32 id = 1;              // 卡牌id
  optional int32 num = 2;             // 展示的数量
  repeated com.tencent.wea.xlsRes.CardTradeType tradeTypeList = 3; // 支持的交易类型
}
message TradingCardAddInfo {
  optional com.tencent.wea.xlsRes.CardAddSourceType sourceType = 1;     // 获取来源类型
  optional int64 sourceId = 2;                                          // 获取来源id
  optional TradingCardDisplayInfo cardInfo = 3;                         // 卡牌数据
  optional int64 fromPlayerUid = 4;                                     // 来源玩家
  optional string fromPlayerName = 5;                                   // 来源玩家名
  optional int64 addTimeMs = 6;                                         // 获得时间
  repeated TradingCardDisplayInfo cardInfoList = 7;                     // 卡牌数据
}
message TradingCardRewardInfo {
  repeated BagGetItem bagGetItem = 1;                                     // 获得的道具
  optional com.tencent.wea.xlsRes.ItemChangeReason itemChangeReason = 2;  // 获得原因 卡组/卡牌
  optional int64 itemChangeReasonId = 3;                                  // 获得原因id 卡组id/卡牌id
}
message TradingCardDeckDisplayInfo {
  optional int32 id = 1;                                      // 卡组id
  repeated TradingCardDisplayInfo collectedCardInfo = 2;      // 已收集的卡牌信息
  optional int32 totalNum = 3;                                // 总数量
  optional bool rewarded = 4;                                 // 是否已领奖
  repeated RewardConfInfo rewardConf = 5;                     // 奖励配置
}
message TradingCardCollectionBriefInfo {
  optional int32 id = 1;                                          // 卡集id
  optional com.tencent.wea.xlsRes.CardCollectionType type = 2;    // 类型
  optional int64 beginTimeMs = 3;                                 // 开始时间
  optional int64 endTimeMs = 4;                                   // 结束时间
  optional int32 collectedNum = 5;                                // 已收集数量
  optional int32 totalNum = 6;                                    // 总数量
}

// 获取卡集列表
message TradingCardGetCollectionList_C2S_Msg {

}
message TradingCardGetCollectionList_S2C_Msg {
  repeated TradingCardCollectionBriefInfo runningCollectionList = 1;    // 未结束的卡集
  repeated TradingCardCollectionBriefInfo historyCollectionList = 2;    // 历史卡集
}

// 查看卡集信息
message TradingCardGetCollectionDetailInfo_C2S_Msg {
  optional int32 collectionId = 1;  // 卡集id
}
message TradingCardGetCollectionDetailInfo_S2C_Msg {
  repeated TradingCardDeckDisplayInfo collectedCardDeckInfo = 1;      // 已收集的卡牌卡组信息
  optional bool rewarded = 2;                                         // 是否已领奖
  repeated RewardConfInfo rewardConf = 3;                             // 奖励配置
  repeated proto_WildCardInfo wildCardList = 4;                             // 万能牌信息
}

// 获取卡牌通知
message TradingCardGetNtf {
  repeated TradingCardRewardInfo rewardInfoList = 1;      // 奖励列表
  repeated TradingCardAddInfo cardAddInfoList = 2;        // 获取卡牌
  repeated proto_WildCardInfo wildCardList = 3;           // 万能牌列表
  optional int32 collectionId = 4;                        // 卡集id
  repeated TradingCardAddInfo extraIPCardAddInfoList = 5; //额外卡牌
}

// 新获取卡牌通知
message NewTradingCardGetNtf {
  repeated TradingCardRewardInfo rewardInfoList = 1;      // 奖励列表
  repeated TradingCardAddInfo cardAddInfoList = 2;        // 获取卡牌
  repeated proto_WildCardInfo wildCardList = 3;           // 万能牌列表
  repeated TradingCardAddInfo extraIPCardAddInfoList = 4; //有数据时额外卡牌和cardAddInfoList一一对应
}

message TradingCardExchangeWildCard_C2S_Msg {
  optional int32 collectionId = 3;                // 卡集id
  optional int32 num = 4;                         // 兑换的数量
  repeated int32 cardIdList = 5;                  // 兑换的卡牌id列表
}
message TradingCardExchangeWildCard_S2C_Msg {

}

// 批量查询交易信息（有缓存）
message TradingCardGetTradeInfoListCache_C2S_Msg {
  repeated int64 tradeIdList = 1;     // 交易id列表
}
message TradingCardGetTradeInfoListCache_S2C_Msg {
  repeated TradingCardTradeInfo tradeInfoList = 1;    // 交易信息列表
}

// 查询单个交易信息
message TradingCardGetTradeInfo_C2S_Msg {
  required int64 tradeId = 1;     // 交易id
}
message TradingCardGetTradeInfo_S2C_Msg {
  optional TradingCardTradeInfo tradeInfo = 2;    // 交易信息
}

// 查看交易记录
message TradingCardGetTradeHistory_C2S_Msg {
  optional int32 collectionId = 1;                                    // 卡集id
  optional com.tencent.wea.xlsRes.CardTradeType tradeType = 2;        // 交易类型
}
message TradingCardGetTradeHistory_S2C_Msg {
  repeated TradingCardTradeInfo selfUnfinishedTradeInfoList = 1;  // 本人未完成的交易记录
  repeated TradingCardTradeInfo completedTradeInfoList = 2;  // 交易记录列表
}

// 查看卡牌活动
message TradingCardActivityInfo {
  optional int32 id = 1;                                            // 活动id
  optional com.tencent.wea.xlsRes.TradingCardActivityType type = 2; // 活动类型
  optional int64 beginTimeMs = 3;                                   // 生效开始时间
  optional int64 endTimeMs = 4;                                     // 生效结束时间
  repeated int64 paramList1 = 5;                                    // 活动参数1 (卡牌互动: 卡牌id)
  repeated int64 paramList2 = 6;                                    // 活动参数2 (卡牌互动: 互动类型)
}
message TradingCardGetActivity_C2S_Msg {
  optional int32 collectionId = 1;
}
message TradingCardGetActivity_S2C_Msg {
  repeated TradingCardActivityInfo activityList = 1;
}

// 发起索要请求
message TradingCardCreateRequireTrade_C2S_Msg {
  optional int32 collectionId = 1;
  optional int32 cardId = 2;      // 卡牌id
  optional com.tencent.wea.xlsRes.CardTradeShareChannel shareChannel = 3;  // 分享渠道
  repeated int64 uidList = 4;     // 指定好友
}
message TradingCardCreateRequireTrade_S2C_Msg {
  optional int64 tradeId = 1;     // 交易id
}

// 完成索要请求
message TradingCardCompleteRequireTrade_C2S_Msg {
  optional int64 tradeId = 1;     // 交易id
  optional int32 cardId = 2;      // 卡牌id
}
message TradingCardCompleteRequireTrade_S2C_Msg {

}

// 发起赠送请求
message TradingCardCreateGiveTrade_C2S_Msg {
  optional int32 collectionId = 1;
  repeated int32 cardIdList = 2;      // 卡牌列表
  optional com.tencent.wea.xlsRes.CardTradeShareChannel shareChannel = 3;  // 分享渠道
  optional int64 uid = 4;     // 指定好友
}
message TradingCardCreateGiveTrade_S2C_Msg {
  optional int64 tradeId = 1;     // 交易id
}

// 完成赠送请求
message TradingCardCompleteGiveTrade_C2S_Msg {
  optional int64 tradeId = 1;     // 交易id
}
message TradingCardCompleteGiveTrade_S2C_Msg {

}

// 发起交换请求
message TradingCardCreateExchangeTrade_C2S_Msg {
  optional int32 collectionId = 1;
  optional int32 cardId = 2;      // 卡牌
  optional com.tencent.wea.xlsRes.CardTradeShareChannel shareChannel = 3;  // 分享渠道
  repeated int64 uidList = 5;     // 指定好友
  repeated int32 requireCardIdList = 6; // 需要的卡牌列表
}
message TradingCardCreateExchangeTrade_S2C_Msg {
  optional int64 tradeId = 1;     // 交易id
}

// 完成交换请求
message TradingCardCompleteExchangeTrade_C2S_Msg {
  optional int64 tradeId = 1;     // 交易id
  optional int32 cardId = 2;      // 卡牌
}
message TradingCardCompleteExchangeTrade_S2C_Msg {

}

// 分享交易请求到聊天
message TradingCardShareToChat_C2S_Msg {
  optional int64 tradeId = 1;			// 交易id
}
message TradingCardShareToChat_S2C_Msg {

}

//清理红点
message TradingCardClearRedDot_C2S_Msg{
   repeated proto_TradingCardClearRedDot clearRedDots = 1; //清理的红点
   optional int32 collectionId = 2; //卡集ID
}

message TradingCardClearRedDot_S2C_Msg{

}

//红点变化通知
message TradingCardRedDotChangeNtf{
  repeated proto_TradingCardClearRedDot clearRedDots = 1; //清理的红点
  repeated proto_TradingCardClearRedDot addRedDots = 2; //新增的红点
  optional int32 collectionId = 3; //卡集ID
}

//领取循环奖杯奖励
message TradingCardDrawCycleCupsReward_C2S_Msg{
  optional int32 collectionId = 1; //卡集ID
}

message TradingCardDrawCycleCupsReward_S2C_Msg{
  optional proto_TradingCardCycleCup cupData = 1; //返回更新的数据
}

message TradingCardExchangeReward_C2S_Msg{
  optional int32 collectionId = 1; //卡集ID
  optional int32 exchangeId = 2; //兑换ID
  optional IntKVArray useCards = 3; //消耗的卡牌
}

message TradingCardExchangeReward_S2C_Msg{
  optional proto_TradingCardExchangeInfo exchangeInfo = 1; //返回更新的数据
}

//获取新手奖励
message TradingCardShowNoviceReward_C2S_Msg{
  optional int32 collectionId = 1; //卡集ID
}

message TradingCardShowNoviceReward_S2C_Msg{
  optional int32 cardId = 1; //卡牌ID
  optional int32 count = 2; //卡牌数量
}

//领取新手奖励
message TradingCardDrawNoviceReward_C2S_Msg{
  optional int32 collectionId = 1; //卡集ID
}

message TradingCardDrawNoviceReward_S2C_Msg{
  optional int32 cardId = 1; //卡牌ID
  optional int32 count = 2; //卡牌数量
}


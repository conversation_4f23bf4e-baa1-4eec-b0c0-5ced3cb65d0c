syntax = "proto2";

package com.tencent.wea.protocol;


option java_multiple_files = true;
option java_package = "com.tencent.wea.protocol.common";

import "base_common.proto";
import "ResCommon.proto";
import "ResKeywords.proto";
import "ResActivity.proto";
import "ResCups.proto";
import "ResTradingCard.proto";
import "ResMatch.proto";
import "google/protobuf/descriptor.proto";
import "competition_common.proto";
import "attr_DressItemInfo.proto";
import "attr_DressUpDetailInfo.proto";
import "attr_SquadMember.proto";
import "attr_PlayerStatusDetails.proto";
import "attr_PlayerPublicSceneData.proto";
import "attr_ActivityDetail.proto";
import "attr_ActivityRedDot.proto";
import "attr_CanStealTime.proto";
import "attr_FriendInteractDetailAttr.proto";
import "attr_CheckerboardGridInfo.proto";
import "attr_GroupingReturnData.proto";
import "attr_PakDownloadInfo.proto";

import "attr_FarmFishPoolInfo.proto";
import "attr_Grid.proto";
import "attr_FarmEvent.proto";
import "attr_FarmEventSync.proto";
import "attr_ChatGroupKey.proto";
import "attr_CompetitionBasicInfo.proto";
import "attr_QuizQuestionData.proto";
import "attr_TradingCardInfo.proto";
import "attr_SeasonReview.proto";
import "attr_CosImage.proto";
import "attr_RaffleCommonInfo.proto";
import "attr_UgcMapInfo.proto";
import "attr_StarPPet.proto";
import "attr_StarPItemUserDataUnion.proto";
import "attr_StarPBuff.proto";
import "attr_StarPGroupData.proto";
import "attr_StarPGuildData.proto";
import "attr_StarPGroupMemberData.proto";
import "attr_StarPGuildMemberData.proto";
import "attr_StarPBaseGroupMemberDataInfo.proto";
import "attr_StarPBaseGroupDataInfo.proto";
import "attr_StarPBaseGroupSimpleInfo.proto";
import "attr_StarPAssistMaterialInfo.proto";
import "attr_StarPAssistUserInfo.proto";
import "attr_StarPBaseGroupApplicationDataUnion.proto";
import "attr_StarPBaseGroupInvitationDataUnion.proto";
import "attr_StarPPetTradeInfo.proto";
import "attr_StarPGuildWishStatueData.proto";
import "attr_StarPPlayerSocInteractionRatioData.proto";
import "attr_StarPOneContactRecord.proto";
import "attr_StarPAssistOrderSimpleInfo.proto";
import "attr_StarPPetTradeWishInfo.proto";
import "attr_StarPCardInfo.proto";
import "attr_StarPGuildApplicationData.proto";
import "attr_StarPGuildInvitationData.proto";
import "attr_ArenaHeroCombatEffectivenessData.proto";
import "attr_ArenaHeroStatInfo.proto";

extend google.protobuf.MessageOptions {
  optional bool patch_cs_msg = 600001;
  optional bool forward_to_ugc = 800001;
  optional string cs_forward = 800002;
  optional com.tencent.wea.xlsRes.UgcCommandUrlType admin_url_type = 900001;
  optional bool forward_admin_call = 900002;
  optional com.tencent.wea.protocol.ServerType forward_to = 900003;
  optional bool ugc_app_forward = 111001;
  optional bool forward_to_farmsvr = 112001;
  optional string analyze_module = 112002;

  // coc options range 112101-112200
  optional string coc_ref_method = 112101;
  optional bool coc_one_way = 112102;
  optional bool coc_if_present = 112103;
  optional bool coc_need_caller = 112104;
}

extend google.protobuf.FieldOptions {
  optional int32 lod_level = 300001;
  optional string uiDesc = 300002;
  optional bool field_forward_hash_key = 300003; //cs_forward下指定转发时用的hashkey
}

enum UseSceneType{
  DanceOutfit = 1;//舞会道具
}

enum SafeStatus {
  PrePublish = 0; //待发布
  Review = 1; //审核
  Publish = 2; // 正式发布
  PublishFail = 3; //驳回
  TakeOff = 4; // 下架
  Recover = 5;     // 已回收(未使用)
  SelfTakeOff = 6;   // 主动下架
  BanTakeOff = 7;  // 封禁下架
  TakeOffAbdReview = 8;  // 审核过已下架(未使用)
  PlatformOnTheShelf = 9;  //管理端上架
  ResBan = 10; // 资源封禁 (云资源需求)
  MainTenance = 11; //维护中
}

enum PlayerBitType {
  PlayerBitTypeTower = 1; // 传送塔
  PlayerBitTypeFireBall = 2; // 火灵珠
  PlayerFlagType = 3;// 玩家状态标记
  PlayerAdventureGroupSpeedUp = 4; // 冒险团加速
  PlayerGuideFirstEnter = 5; // 玩家第一次进入房间引导
  PlayerLastMainProcessReportID = 6; // 玩家上一次上报的主流程TlogID
  PlayerCurrentMainProcessGroupID = 7; // 玩家当前的主流程组ID列表
  PlayerMainProcessGroupIDHistory = 8; // 玩家历史记录主流程ID列表
  PlayerExperienceLimitData = 9; // 玩家经验上限数据
  PlayerUnlockEquipmentSetIDList = 10;	// 玩家装备套装ID解锁列表
  PlayerLearningDiagramIDList = 11;	//	玩家已经学习的图纸ItemID
}

enum PlayerBitTower {
  PlayerBitTower1 = 1; // 传送塔解锁1
  PlayerBitTower2 = 2; // 传送塔解锁2
  PlayerBitTower3 = 3; // 传送塔解锁3
  PlayerBitTower4 = 4; // 传送塔解锁4
  PlayerBitTower5 = 5; // 传送塔解锁5
  PlayerBitTower6 = 6; // 传送塔解锁6
}

enum PlayerBitFireBall {
  PlayerBitFireBall1 = 1; // 火灵珠领取1
  PlayerBitFireBall2 = 2; // 火灵珠领取2
  PlayerBitFireBall3 = 3; // 火灵珠领取3
  PlayerBitFireBall4 = 4; // 火灵珠领取4
}

message LongArray {
  repeated int64 array = 1;
}

message IntArray {
  repeated int32 array = 1;
}
message StringArray {
  repeated string array = 1;
}

message KVEntry {
  optional string key = 1;
  optional string value = 2;
}

message ChildObject {
  optional string v = 1;
}
message ParentObject {
  optional ChildObject child = 1;
}
message IntKVEntry {
  optional int32 key = 1;
  optional int32 value = 2;
}

message IntLongKVEntry {
  optional int32 key = 1;
  optional int64 value = 2;
}

message KVArray {
  repeated KVEntry array = 1;
}

message IntLongMap {
  map<int32, int64> m = 1;
}
message IntIntMap {
  map<int32, int32> m = 1;
}

message Item {
  optional int32 Id = 1;
  optional string data = 2;
}

// 奖励信息
message ReceiveInfo {
  optional int32 index = 1;   // 活动奖励索引
  optional int32 count = 2;    // 领取奖励数量,默认为1
  repeated Item rewards = 3;  // 获取的道具信息(下行包中才会填充)
  optional int32 group = 4;   // 分组
}

// 心愿活动助理绑定-助力者额外信息
message WishActivityHelpBindParams {
  optional int64 loginTimeMs = 1;   // 登入时间
  optional int64 logoutTimeMs = 2;  // 登出时间
  optional int32 vipLevel = 3;      // vip等级
  optional int32 vipExp = 4;        // vip经验
  optional string roleName = 5;     // 玩家昵称
}

// 活动服务奖励结构
message ActivityReward {
  optional int32 taskId = 1;  //任务ID()-废弃
  optional ItemInfo items = 2;  //奖励物品
  optional int32 subReason = 3;
}

// 活动服务奖励数组
message ActivityRewardArray {
  repeated ActivityReward items = 1;
}

// 活动服务通知发奖奖励到背包
message PiiActivitySvrRewardParams {
  optional int32 activityId = 1;    // 活动ID
  optional int32 activityType = 2;  // 类型
  optional string orderNo = 3;      // 发奖订单号
  optional ItemArray rewards = 4;  // 奖励信息(24.09.24 废弃 by:leancjli)
  optional int32 reason = 5;
  optional int32 subReason = 6;
  optional bool notSendNtf = 7;
  optional int32 rewardType = 8;   // 奖励的类型 0.任务奖励 1.活动奖励
  optional ActivityRewardArray actRewardArray = 9; // 奖励内容
  repeated int64 changeReservedParams = 10;  //道具修改参数
}

// 扫雷 begin
enum MinesweeperCheckerboardGridStatus {
  MCGS_Init = 0;
  MCGS_Show = 1;
  MCGS_Excavated = 2;
}
// 格子信息请求
message ActivityMinesweeperReqInfo {

}
message ActivityMinesweeperResInfo {
  repeated proto_CheckerboardGridInfo gridInfo = 1;
}
// 挖掘
message ActivityMinesweeperExcavateReqInfo {
  // 索引从0开始
  optional int32 row = 1;
  optional int32 column = 2;
}
message ActivityMinesweeperExcavateResInfo {
  repeated proto_CheckerboardGridInfo gridInfo = 1;
}
// 扫雷 end

// 大富翁 begin
// 丢骰子
message ActivityMonopolyRunReqInfo {
  optional int32 step = 1; // 当指定点数骰子时上行
}
message ActivityMonopolyRunResInfo {
  repeated int32 step = 1; // 丢出的点数 可能有俩个
  optional int32 beforeIndex = 2;
  optional int32 afterIndex = 3;
}
// 抽奖格子的抽奖
message ActivityMonopolyDrawLotteryReqInfo {
}
message ActivityMonopolyDrawLotteryResInfo {
  optional int32 rewardId = 1;
}
// 领取格子奖励
message ActivityMonopolyReceiveGridRewardReqInfo {
}
message ActivityMonopolyReceiveGridRewardResInfo {
}
// 领取圈奖励
message ActivityMonopolyReceiveRoundRewardReqInfo {
  optional int32 rewardId = 1;
}
message ActivityMonopolyReceiveRoundRewardResInfo {
}
// 领取步数奖励
message ActivityMonopolyReceiveStepRewardReqInfo {
  optional int32 rewardId = 1;
}
message ActivityMonopolyReceiveStepRewardResInfo {
}
// 大富翁 end

//追击黑影人免费奖励
message CaptureShadowFreeRewardReqInfo{

}
message CaptureShadowFreeRewardResInfo{
  repeated RewardItemInfo rewardItems = 1;      // 获得的道具
}

//追击黑影人使用道具
message CaptureUseItemReqInfo{
  required int32 type=1;
}

message CaptureUseItemResInfo{
  optional int32 type= 1;                   //本次行动使用的类型
  optional bool captureRes= 2;              //本次行动是否抓捕成功
  repeated RewardItemInfo rewardItems = 3;  // 获取的格子奖励
  optional int32 playerGridBefore = 4;      // 行动前玩家格子
  optional int32 playerGrid = 5;            // 行动后玩家格子
  optional int32 targetGridBefore = 6;      // 行动前黑衣人格子
  optional int32 targetGrid = 7;            // 行动后黑衣人格子
  optional int32 beginIndex = 8;            // 当前起始格子
  optional int32 mapCount = 9;              // 地图刷新次数
  optional int32 targetBuff = 10;           // 手表buff层数
  repeated int32 receivedOneTimeRewardIds = 11;           // 领取过的一次性奖励
  repeated RewardItemInfo capRewardItems = 12;  // 捕获奖励
}

message MobaNewHeroTrialGetQuizReqInfo {

}
message QuestionDataInfo {
  optional int32 id = 1;              //题目id
  repeated int32 answeredChoice = 2;  //已答过的选项id
  repeated int32 correctChoice = 3;    // 正确选项
}
message MobaNewHeroTrialGetQuizResInfo {
  repeated QuestionDataInfo questionDataList = 1;
}

message MobaNewHeroTrialAnswerQuestionReqInfo {
  optional int32 questionId = 1;            // 问题id
  repeated int32 choiceIdList = 2;          // 选择的选项id
}
message MobaNewHeroTrialAnswerQuestionResInfo {
  optional bool correct = 1;                // 是否正确
}

message FlashRaceCheeringVoteReqInfo{

}
message FlashRaceCheeringVoteResInfo{
  repeated KeyValueInt32 voteInfo = 1;//返回助威信息 key-助威角色id  value-当前票数
}


message FlashRaceCheeringVoteOptionReqInfo{
  optional KeyValueInt32  voteOption = 1;//key-助威角色id value-投票张数
}
message FlashRaceCheeringVoteOptionResInfo{
  repeated KeyValueInt32 voteInfo = 1;//返回助威信息 key-助威角色id  value-当前票数
}


message QingShuangTrialGetQuizReqInfo{

}

message QingShuangTrialGetQuizResInfo{
  repeated QuestionDataInfo questionDataList = 1;
}

message QingShuangTrialAnswerQuestionReqInfo{
  optional int32 questionId = 1;            // 问题id
  repeated int32 choiceIdList = 2;          // 选择的选项id
}

message QingShuangTrialAnswerQuestionResInfo{
  optional bool correct = 1;                // 是否正确
}

// 用户选择阵营
message PlayerPickActivityFactionResInfo {
}

// 应援进度查询
message PlayerGetFactionSupportResInfo {
  optional int64 playerSupportValue = 1;        // 用户个人进度制
  optional string winFaction = 2;                      // 在活动结束后，获胜阵营会有值
  repeated PlayerFactionSupport playerFactionSupports = 3; // 每个阵营应援值
}

message PlayerFactionSupport {
  optional string faction = 1;
  optional int64 factionSupportValue = 2;
}

// 阵营大奖领取
message PlayerReceiveFactionBigRewardResInfo {
}

// 阵营结算领奖
message PlayerReceiveFactionSettlementRewardResInfo {
}

// 用户选择阵营
message PlayerPickActivityFactionReqInfo {
  optional int32 configId = 1;        // 阵营对决配置ID
  optional string faction = 2;        // 选择的阵营
}

// 应援进度查询
message PlayerGetFactionSupportReqInfo {
  optional int32 configId = 1;        // 阵营对决配置ID
}

// 阵营大奖领取
message PlayerReceiveFactionBigRewardReqInfo {
  optional int32 configId = 1;        // 阵营对决配置ID
}

// 阵营结算领奖
message PlayerReceiveFactionSettlementRewardReqInfo {
  optional int32 configId = 1;        // 阵营对决配置ID
}

// 单个问题的答题记录
message QuestionAnswerRecord {
  optional int32 questionId = 1;        // 题目id
  repeated int32 choseOptionId = 2;     // 选择的选项id (考虑通用性这里用 repeated)
}

enum ActivityGeneralMsgType {
  // 心愿活动-Begin
  AGMTWishSelect = 1;           // 选择心愿礼物
  AGMTWishTaskDone = 2;         // 完成心愿任务
  AGMTWishFriendsHelp = 3;      // 为好友助力
  AGMTWishGetAward = 4;         // 领取心愿奖励
  // 心愿活动-End

  // 特训营-Begin
  AGMTTrainingCampGetSportsmanData = 5; // 获取运动员数据
  AGMTTrainingCampTrainSportsman = 6;   // 训练运动员
  // 特训营-End

  // 训练活动-Begin
  AGMTTrainingGetData  = 10;			// 训练活动:获取训练对象数据
  AGMTTrainingTrain = 11;				// 训练活动:进行训练
  AGMTTrainingReceiveAward = 12;		// 训练活动:领取对象训练进度奖励
  // 训练活动-End

  // 大富翁-Begin
  AGMTMonopolyRun = 15;                // 丢骰子
  AGMTMonopolyDrawLottery = 16;        // 格子抽奖
  AGMTMonopolyReceiveGridReward = 17;  // 格子领奖
  AGMTMonopolyReceiveRoundReward = 18; // 领取圈奖励
  AGMTMonopolyReceiveStepReward = 19;  // 领取步数奖励
  // 大富翁-End

  // 扫雷-Begin
  AGMTMinesweeper = 20;                // 请求扫雷棋盘信息
  AGMTMinesweeperExcavate = 21;        // 挖掘
  // 扫雷-End

  // 团购返利-Begin
  AGMTGroupingReturnInfo = 22;         // 团购返利面板信息
  AGMTGroupingReturnReward = 23;       // 团购返利领奖
  AGMTGroupingReturnJoin = 24;         // 加入团购队伍
  AGMTGroupingReturnQuit = 25;         // 退出团购队伍
  AGMTGroupingReturnKick = 30;         // 团购队伍踢人
  AGMTGroupingReturnFriendList = 31;   // 好友团购信息
  // 团购返利-End

  // 星界奇遇-Begin
  AGMTThemeAdventureOpenMysteryBox = 26; // 星界奇遇开启盲盒
  // 星界奇遇-End

  // 减负季-Begin
  AGMTEaseBurdenAppointment = 27;      // 减负季预约
  // 减负季-End

  // 许愿树-Begin
  AGMTWishingTreeMakeWish = 28;        // 许愿树:许愿奖励
  AGMTWishingTreeReceiveWishReward = 29; // 许愿树:领取许愿奖励
  // 许愿树-End

  // 钓鱼名人堂-Begin
  AGMTFishingHallOfFameInfo = 32;      // 钓鱼名人堂:信息
  AGMTFishingFameLayer= 34;            // 钓鱼名人堂:层数排行榜
  // 钓鱼名人堂-End

  // 餐厅主题活动-Begin
  // AGMTRestaurantThemedInfo = 35;      // 餐厅主题活动:信息
  AGMTRestaurantThemedServing  = 36;  // 餐厅主题活动:上菜
  AGMTRestaurantThemedReceive  = 37;  // 餐厅主题活动:通过分享领取印章
  // 餐厅主题活动-End

  // 舞会活动-Begin
  AGMDanceOutfitGeneration = 40;       // 舞会:服装生成
  AGMDanceOutfitChange = 41;           // 舞会:服装更换
  AGMDanceOutfitCancel = 42;           // 舞会:取消服装
  AGMDanceOutfitCollect = 43;          // 舞会:收藏历史服装
  // 舞会活动-End

  // 柯南预热活动-Begin
  AGMTConanWarmupInfo = 44;            // 柯南预热活动:信息
  AGMTConanWarmupAnswer = 45;          // 柯南预热活动:答题(签到)
  // 柯南预热活动-End

  // 追击黑影人-Begin
  AGMCaptureShadowFreeReward = 47;     // 追击黑影人:免费奖励
  AGMCaptureShadowUseItemReqInfo = 48; // 追击黑影人:使用道具
  // 追击黑影人-End

  // MOBA新英雄试炼-Begin
  AGMTMobaNewHeroTrialGetQuiz = 49;    // MOBA新英雄试炼:获取测验
  AGMTMobaNewHeroTrialAnswerQuestion = 50; // MOBA新英雄试炼:回答问题
  // MOBA新英雄试炼-End

  // 旅行狗狗活动-Begin
  AGMTravelingDogStageInfo = 51;    // 旅行狗狗活动:阶段信息
  AGMTravelingDogReward = 52;       // 旅行狗狗活动:获取画片 （一期功能 已废弃）
  AGMTravelingDogDogTravel = 53;       // 旅行狗狗活动:狗狗出行
  AGMTravelingDogDogGoHome = 54;       // 旅行狗狗活动:狗狗回归
  //旅行狗狗活动-End


  // 阵营对决活动-Begin
  AGMPlayerPickActivityFaction = 55;            // 用户选择阵营
  AGMPlayerGetFactionSupport = 56;             // 应援进度查询
  AGMPlayerReceiveFactionBigReward = 57;       // 阵营大奖领取
  AGMPlayerReceiveFactionSettlementReward = 58;// 阵营结算领奖
  // 阵营对决活动-End

  //时装基金活动-Begin
  AGMFashionFundReceiveAward = 60; // 领取返还代币
  //时装基金活动-End
  // 农场美食节活动-Begin
  AGMFarmFoodFestivalInfo = 61;           //农场美食节活动:信息
  AGMFarmFoodFestivalSendGift = 62;       //农场美食节活动:赠送礼物
  AGMFarmFoodFestivalReceiveCoin = 63;    //农场美食节活动:获取好感度奖励
  AGMFarmFoodFestivalMadeItem = 64;    //农场美食节活动:物品合成
  AGMFarmFoodFestivalBigReward = 94;    //农场美食节活动:大奖奖励领取
  //农场美食节活动-End

  //峡谷随机投票-Begin
  AGMMobaRandomVoteInfo = 65;    //峡谷随机投票:活动信息
  AGMMobaRandomPlayerVote = 66;  //峡谷随机投票:玩家投票
  //峡谷随机投票-End

  //元宵灯谜活动-Begin
  AGMTLanternRiddlesAnswer = 67;  //元宵灯谜活动：答题
  //元宵灯谜活动-End

  //柯南二期广场活跃活动-Begin
  AGMConanIpActiveAddBuff = 68;//添加buff
  AGMConanIpActiveReceiveCardAward = 69;//领取卡片奖励
  //柯南二期广场活跃活动-End


  // 狼人召回-Begin
  AGMWolfReturnReceiveInvitation = 75;     // 接收邀请
  AGMWolfReturnReward = 76;     // 领取奖励
  AGMWolfReturnFriendList = 77;     // 获取好友列表
  // 狼人召回-End

  FeatureIntegrationAddPic = 78;//特色玩法整合模板-记录点击的图片id

  // 晴霜试炼-Begin
  AGMQingShuangTrialReward = 79;
  // 晴霜试炼-End

  AGMWolfReturnGetState = 80;     // 获取狼人召回状态

  AGMFarmAnwser=81;//农场答题
  AGMFarmAnwserInfo=82;//农场答题

  // 宝箱升级-Begin
  AGMTreasureLevelUpTakeBoxReward = 84;    // 领取宝箱奖励
  // 宝箱升级-End

  // 闪电助威 Begin
  AGMFlashRaceCheeringVote = 86;    // 请求闪电助威投票数据
  AGMFlashRaceCheeringVoteOption = 87;    // 闪电助威投票
  // 闪电助威-End

  AGMQingShuangTrialGetQuiz=88;
  AGMMQingShuangTrialAnswerQuestion=89;

  AGMMobaChallenge=90;//峡谷挑战活动（测试）
  AGMMobaChallengeInfo=91;  //峡谷挑战数据

  //活动抽奖-Begin
  AGMLotteryGift = 92;//请求抽奖
  //活动抽奖-End
  //暑期bP活动
  SummerVacationBPReward = 93;    // 领奖

  // 暑期导航栏-Begin
  AGMTSummerNavigationBarReward = 95;    // 领取吧唧奖励
  // 暑期导航栏-End

  //请求全量任务
  AGMGetSummerFlashAllTask = 96;    // 暑期快闪活动
}

message ActivityGeneralReqInfo {
  oneof GeneralReqInfo {
    // 心愿活动-Begin
    ACTWishSelectReqInfo wishSelectReqInfo = 1;           // 心愿活动:选择心愿礼物请求
    ACTWishTaskDoneReqInfo wishTaskDoneReqInfo = 2;       // 心愿活动:完成心愿任务请求
    ACTWishFriendsHelpReqInfo wishFriendsHelpReqInfo = 3; // 心愿活动:为好友助力请求
    ACTWishGetAwardReqInfo wishGetAwardReqInfo = 4;       // 心愿活动:领取心愿奖励请求
    // 心愿活动-End

    // 特训营-Begin
    ACTTrainingCampGetSportsmanDataReqInfo trainingCampGetSportsmanDataReqInfo = 5; // 特训营:获取运动员数据
    ACTTrainingCampTrainSportsmanReqInfo trainingCampTrainSportsmanReqInfo = 6;     // 特训营:训练运动员
    // 特训营-End

    // 训练活动-Begin
    ACTTrainingGetDataReqInfo trainingGetDataReqInfo = 10;		        // 训练活动:获取训练数据请求
    ACTTrainingTargetTrainingReqInfo targetTrainingReqInfo = 11;        // 训练活动:对目标进行培养请求
    ACTTrainingReceiveAwardReqInfo trainingReceiveAwardReqInfo = 12;    // 训练活动:领取训练目标奖励请求
    // 训练活动-End

    // 大富翁-Begin
    ActivityMonopolyRunReqInfo activityMonopolyRunReqInfo = 15;                     // 大富翁:运行
    ActivityMonopolyDrawLotteryReqInfo activityMonopolyDrawLotteryReqInfo = 16;     // 大富翁:抽奖
    ActivityMonopolyReceiveGridRewardReqInfo activityMonopolyReceiveGridRewardReqInfo = 17; // 大富翁:领取格子奖励
    ActivityMonopolyReceiveRoundRewardReqInfo activityMonopolyReceiveRoundRewardReqInfo = 18; // 大富翁:领取回合奖励
    ActivityMonopolyReceiveStepRewardReqInfo activityMonopolyReceiveStepRewardReqInfo = 19; // 大富翁:领取步数奖励
    // 大富翁-End

    // 扫雷-Begin
    ActivityMinesweeperReqInfo activityMinesweeperReqInfo = 20;                     // 扫雷:请求棋盘信息
    ActivityMinesweeperExcavateReqInfo activityMinesweeperExcavateReqInfo = 21;     // 扫雷:挖掘
    // 扫雷-End

    // 团购返利-Begin
    ActivityGroupingReturnReqInfo activityGroupingReturnReqInfo = 22;               // 团购返利:信息
    ActivityGroupingReturnReqReward activityGroupingReturnReqReward = 23;           // 团购返利:奖励
    ActivityGroupingReturnReqJoin activityGroupingReturnReqJoin = 24;               // 团购返利:加入
    ActivityGroupingReturnReqQuit activityGroupingReturnReqQuit = 25;               // 团购返利:退出
    ActivityGroupingReturnReqKick activityGroupingReturnReqKick = 30;               // 团购返利:踢出
    ActivityGroupingReturnReqFriendList activityGroupingReturnReqFriendList = 31;   // 团购返利:好友列表
    // 团购返利-End

    // 星界奇遇-Begin
    ThemeAdventureOpenMysteryBoxReqInfo themeAdventureOpenMysteryReqInfo = 26;      // 星界奇遇:打开神秘盒子
    // 星界奇遇-End

    // 减负季-Begin
    EaseBurdenAppointmentReqInfo easeBurdenAppointmentReqInfo = 27;                 // 减负季:预约
    // 减负季-End

    // 许愿树-Begin
    WishingTreeMakeWishReqInfo wishingTreeMakeWishReqInfo = 28;                     // 许愿树:许愿奖励
    WishingTreeReceiveWishRewardReqInfo wishingTreeReceiveWishRewardReqInfo = 29;   // 许愿树:领取许愿奖励
    // 许愿树-End

    // 钓鱼名人堂-Begin
    ActivityFishingHallOfFameReqInfo activityFishingHallOfFameReqInfo = 32;         // 钓鱼名人堂:信息
    ActivityFishingFameReqLayer fishingFameReqLayer = 34;                           // 钓鱼名人堂:层数排行榜
    // 钓鱼名人堂-End

    // 餐厅主题活动-Begin
    // ActivityRestaurantThemedReqInfo restaurantThemedInfo = 35;                      // 餐厅主题活动:信息
    ActivityRestaurantThemedReqServing restaurantThemedServing = 36;                // 餐厅主题活动:上菜
    ActivityRestaurantThemedReqReceive restaurantThemedReceive = 37;                // 餐厅主题活动:通过分享领取印章
    // 钓鱼名人堂-End

    // 舞会活动-Begin
    ACTDanceOutfitGenerationReqInfo outfitGenerationReq = 40;                       // 舞会:服装生成请求
    ACTDanceOutfitChangeReqInfo outfitChangeReq = 41;                               // 舞会:服装更换请求
    ACTDanceOutfitCancelReqInfo outfitCancelReq = 42;                               // 舞会:取消服装请求
    ACTDanceOutfitCollectReqInfo outfitCollectReq = 43;                             // 舞会:请求收藏

    // 舞会活动-End

    // 柯南预热活动-Begin
    ActivityConanWarmupReqInfo conanWarmupReqInfo = 44;         // 柯南预热活动:信息
    ActivityConanWarmupReqAnswer conanWarmupReqAnswer = 45;     // 柯南预热活动:答题(签到)
    // 柯南预热活动-End

    // 追击黑影人-Begin
    CaptureShadowFreeRewardReqInfo captureShadowFreeRewardReqInfo = 47; // 追击黑影人:免费奖励
    CaptureUseItemReqInfo captureShadowUseItemReqInfo = 48;             // 追击黑影人:使用道具
    // 追击黑影人-End

    // MOBA新英雄试炼-Begin
    MobaNewHeroTrialGetQuizReqInfo mobaNewHeroTrialGetQuizReqInfo = 49;     // MOBA新英雄试炼:获取测验
    MobaNewHeroTrialAnswerQuestionReqInfo mobaNewHeroTrialAnswerQuestionReqInfo = 50; // MOBA新英雄试炼:奖励测验
    // MOBA新英雄试炼-End
    // 旅行狗狗-Begin
    TravelingDogStageDataReqInfo stageInfoReqInfo = 51;			// 旅行狗狗阶段信息
    TravelingDogRewardDataReqInfo rewardInfoReqInfo = 52;			// 旅行狗狗画片奖励
    TravelingDogDogTravelReqInfo travelingDogDogTravelReqInfo = 53;			// 旅行狗狗狗狗出行
    TravelingDogDogGoHomeReqInfo travelingDogDogGoHomeReqInfo = 54;			// 旅行狗狗狗狗回归

    // 阵营对决活动-Begin
    PlayerPickActivityFactionReqInfo playerPickActivityFactionReqInfo = 55;     // 用户选择阵营
    PlayerGetFactionSupportReqInfo playerGetFactionSupportReqInfo = 56; // 应援进度查询
    PlayerReceiveFactionBigRewardReqInfo playerReceiveFactionBigRewardReqInfo = 57; // 阵营大奖领取
    PlayerReceiveFactionSettlementRewardReqInfo playerReceiveFactionSettlementRewardReqInfo = 58; // 阵营结算领奖
    // 阵营对决活动-End

    //时装基金
    FashionFundReturnGetReqInfo fashionFundReturnGetReqInfo = 60;//时装基金返利

    // 农场美食节活动-Begin
    FarmFoodFestivalReqInfo foodInfo = 61;           //农场美食节活动:信息
    FarmFoodFestivalReqSendGift foodSendGift = 62;       //农场美食节活动:赠送礼物
    FarmFoodFestivalReqReceiveCoin foodReceiveCoin = 63;    //农场美食节活动:获取收益
    FarmFoodFestivalReqMadeItem foodMade = 64;           //农场美食节活动:合成物品
    //农场美食节活动-End

    //随机事件投票-Begin
    ActivityMobaRandomVoteReqInfo activityMobaRandomVoteReqInfo = 65;    //随机事件投票:活动信息
    ActivityMobaRandomPlayerReqVote activityMobaRandomPlayerReqVote = 66; //随机事件投票:投票
    //随机事件投票-End

    // 元宵灯谜活动-Begin
    LanternRiddlesReqInfo lanternRiddlesReqInfo = 67;   // 元宵灯谜活动：答题信息
    // 元宵灯谜活动-End

    // 柯南二期ip活跃活动-Begin
    ConanPlazaActiveBuffReqInfo conanPlazaActiveBuffReqInfo = 68; // 柯南二期ip活跃活动-获得buff
    ConanPlazaActiveChallengeAwardReqInfo conanPlazaActiveChallengeAwardReqInfo = 69; // 柯南二期ip活跃活动-挑战领取奖励
    // 柯南二期ip活跃活动-End



    // 狼人召回-Begin
    WolfReturnReceiveInvitationReqInfo wolfReturnReceiveInvitationReqInfo = 75;                       // 接收邀请
    WolfReturnRewardReqInfo wolfReturnRewardReqInfo = 76;                                          // 领取奖励
    WolfReturnFriendListReqInfo wolfReturnFriendListReqInfo = 77;
    // 狼人召回-End



    //特色玩法内容整合模板-Begin
    FeatureIntegrationAddPicReqInfo featureIntegrationAddPicReqInfo = 79; //记录图片id
    // 特色玩法内容整合模板-End
    // 晴霜试炼-Begin
    QingShuangTrialRewardReqInfo qingShuangTrialRewardReqInfo = 80;
    // 晴霜试炼-End

    WolfReturnGetStateReqInfo wolfReturnGetStateReqInfo = 81;

    // 农场答题活动-Begin
    FarmAnwserReqAnswer farmAnwserReqAnswer = 82;
    FarmAnwserReqInfo farmAnwserReqInfo = 83;
    // 农场答题活动-End


    QingShuangTrialAnswerQuestionReqInfo qingShuangTrialAnswerQuestionReqInfo = 84; // MOBA新英雄试炼:奖励测验
    QingShuangTrialGetQuizReqInfo qingShuangTrialGetQuizReqInfo = 85;     // MOBA新英雄试炼:获取测验

    FlashRaceCheeringVoteReqInfo flashRaceCheeringVoteReqInfo = 86; // 闪电助威投票数据
    FlashRaceCheeringVoteOptionReqInfo flashRaceCheeringVoteOptionReqInfo = 87; // 闪电助威投票


    // 峡谷挑战活动-Begin
    MobaChallengeLevelReqInfo mobaChallengeLevelReqInfo = 88;
    MobaChallengeReqInfo mobaChallengeReqInfo = 89;
    // 峡谷挑战活动-End

    // 活动抽奖-Begin
    LotteryGiftReqInfo lotteryGiftReqInfo = 90; // 请求抽奖
    // 活动抽奖-End
    //暑期BP活动领奖
    SummerVacationBPRewardReqInfo summerVacationBPRewardReqInfo = 91; //

    // 暑期导航栏-Begin
    SummerNavigationBarRewardReqInfo summerNavigationBarRewardReqInfo = 92; // 领取吧唧奖励
    // 暑期导航栏-End


    //暑期快闪全量任务
    SummerFlashMobTaskReqInfo summerFlashMobTaskReqInfo = 93; //
    // 暑期导航栏-End
  }
}



message ActivityGeneralRspInfo {
  oneof GeneralRspInfo {
    // 心愿活动-Begin
    ACTWishSelectRspInfo wishSelectRspInfo = 1;            // 心愿活动:选择心愿礼物返回
    ACTWishTaskDoneRspInfo wishTaskDoneRspInfo = 2;        // 心愿活动:完成心愿任务返回
    ACTWishFriendsHelpRspInfo wishFriendsHelpRspInfo = 3;  // 心愿活动:为好友助力返回
    ACTWishGetAwardRspInfo wishGetAwardRspInfo = 4;        // 心愿活动:领取心愿奖励返回
    // 心愿活动-End

    // 特训营-Begin
    ACTTrainingCampGetSportsmanDataRspInfo trainingCampGetSportsmanDataRspInfo = 5; // 特训营:获取运动员数据
    ACTTrainingCampTrainSportsmanRspInfo trainingCampTrainSportsmanRspInfo = 6;     // 特训营:训练运动员
    // 特训营-End

    // 训练活动-Begin
    ACTTrainingGetDataRspInfo trainingGetDataRspInfo = 10; 		        // 训练活动: 获取训练数据返回
    ACTTrainingTargetTrainingRspInfo targetTrainingRspInfo = 11;        // 训练活动: 对目标进行培养返回
    ACTTrainingReceiveAwardRspInfo trainingReceiveAwardRspInfo = 12;    // 训练活动: 领取训练目标奖励返回
    // 训练活动-End

    // 大富翁-Begin
    ActivityMonopolyRunResInfo activityMonopolyRunResInfo = 15;                     // 大富翁:运行
    ActivityMonopolyDrawLotteryResInfo activityMonopolyDrawLotteryResInfo = 16;     // 大富翁:抽奖
    ActivityMonopolyReceiveGridRewardResInfo activityMonopolyReceiveGridRewardResInfo = 17; // 大富翁:领取格子奖励
    ActivityMonopolyReceiveRoundRewardResInfo activityMonopolyReceiveRoundRewardResInfo = 18; // 大富翁:领取回合奖励
    ActivityMonopolyReceiveStepRewardResInfo activityMonopolyReceiveStepRewardResInfo = 19; // 大富翁:领取步数奖励
    // 大富翁-End

    // 扫雷-Begin
    ActivityMinesweeperResInfo activityMinesweeperResInfo = 20;                     // 扫雷:请求棋盘信息
    ActivityMinesweeperExcavateResInfo activityMinesweeperExcavateResInfo = 21;     // 扫雷:挖掘
    // 扫雷-End

    // 团购返利-Begin
    ActivityGroupingReturnResInfo activityGroupingReturnResInfo = 22;               // 团购返利:信息
    ActivityGroupingReturnResReward activityGroupingReturnResReward = 23;           // 团购返利:奖励
    ActivityGroupingReturnResJoin activityGroupingReturnResJoin = 24;               // 团购返利:加入
    ActivityGroupingReturnResQuit activityGroupingReturnResQuit = 25;               // 团购返利:退出
    ActivityGroupingReturnResKick activityGroupingReturnResKick = 30;               // 团购返利:踢出
    ActivityGroupingReturnResFriendList activityGroupingReturnResFriendList = 31;   // 团购返利:好友列表
    // 团购返利-End

    // 星界奇遇-Begin
    ThemeAdventureOpenMysteryBoxResInfo themeAdventureOpenMysteryResInfo = 26;      // 星界奇遇:打开神秘盒子
    // 星界奇遇-End

    // 减负季-Begin
    EaseBurdenAppointmentResInfo easeBurdenAppointmentResInfo = 27;                 // 减负季:预约
    // 减负季-End

    // 许愿树-Begin
    WishingTreeMakeWishRspInfo wishingTreeMakeWishRspInfo = 28;                     // 许愿树:许愿奖励
    WishingTreeReceiveWishRewardRspInfo wishingTreeReceiveWishRewardRspInfo = 29;   // 许愿树:领取许愿奖励
    // 许愿树-End

    // 钓鱼名人堂-Begin
    ActivityFishingHallOfFameRspInfo activityFishingHallOfFameRspInfo = 32;         // 钓鱼名人堂:信息
    ActivityFishingFameRspLayer fishingFameRspLayer = 34;                           // 钓鱼名人堂:层数排行榜
    // 钓鱼名人堂-End

    // 餐厅主题活动-Begin
    // ActivityRestaurantThemedRspInfo restaurantThemedInfo = 35;                      // 餐厅主题活动:信息
    ActivityRestaurantThemedRspServing restaurantThemedServing = 36;                // 餐厅主题活动:上菜
    ActivityRestaurantThemedRspReceive restaurantThemedReceive = 37;                // 餐厅主题活动:通过分享领取印章
    // 钓鱼名人堂-End

    // 舞会活动-Begin
    ACTDanceOutfitGenerationRspInfo outfitGenerationRsp = 40; // 舞会:服装生成返回
    ACTDanceOutfitChangeRspInfo outfitChangeRsp = 41;         // 舞会:服装更换返回
    ACTDanceOutfitCancelRspInfo outfitCancelRsp = 42;         // 舞会:取消服装返回
    ACTDanceOutfitCollectRspInfo outfitCollectRsp = 43;       // 舞会:请求收藏
    // 舞会活动-End

    // 柯南预热活动-Begin
    ActivityConanWarmupRspInfo conanWarmupRspInfo = 44;         // 柯南预热活动:信息
    ActivityConanWarmupRspAnswer conanWarmupRspAnswer = 45;     // 柯南预热活动:答题(签到)
    // 柯南预热活动-End

    // 追击黑影人-Begin
    CaptureShadowFreeRewardResInfo captureShadowFreeRewardResInfo = 47; // 追击黑影人:免费奖励
    CaptureUseItemResInfo captureShadowUseItemResInfo = 48;             // 追击黑影人:使用道具
    // 追击黑影人-End

    // MOBA新英雄试炼-Begin
    MobaNewHeroTrialGetQuizResInfo mobaNewHeroTrialGetQuizResInfo = 49;			// MOBA新英雄试炼:获取测验
    MobaNewHeroTrialAnswerQuestionResInfo mobaNewHeroTrialAnswerQuestionResInfo = 50;	// MOBA新英雄试炼:奖励测验
    // MOBA新英雄试炼-End

    // 旅行狗狗-Begin
    TravelingDogStageData stageInfo = 51;			// 旅行狗狗阶段信息
    TravelingDogRewardData rewardInfo = 52;			// 旅行狗狗画片奖励
    TravelingDogDogTravelRspInfo travelingDogDogTravelRspInfo = 53;			// 狗狗出行
    TravelingDogDogGoHomeRspInfo travelingDogDogGoHomeRspInfo = 54;			// 狗狗回归 刷新狗狗状态

    // 阵营对决活动-Begin
    PlayerPickActivityFactionResInfo playerPickActivityFactionResInfo = 55;     // 用户选择阵营
    PlayerGetFactionSupportResInfo playerGetFactionSupportResInfo = 56; // 应援进度查询
    PlayerReceiveFactionBigRewardResInfo playerReceiveFactionBigRewardResInfo = 57; // 阵营大奖领取
    PlayerReceiveFactionSettlementRewardResInfo playerReceiveFactionSettlementRewardResInfo = 58; // 阵营结算领奖
    // 阵营对决活动-End

    //时装基金
    FashionFundReturnGetRspInfo fashionFundReturnGetRspInfo = 60;//时装基金返利

    // 农场美食节活动-Begin
    FarmFoodFestivalRspInfo foodInfo = 61;           //农场美食节活动:信息
    FarmFoodFestivalRspSendGift foodSendGift = 62;       //农场美食节活动:赠送礼物
    FarmFoodFestivalRspReceiveCoin foodReceiveCoin = 63;    //农场美食节活动:获取收益
    FarmFoodFestivalRspMadeItem foodMade = 64;    //农场美食节活动:物品合成
    //农场美食节活动-End

    //随机事件投票-Begin
    ActivityMobaRandomVoteResInfo activityMobaRandomVoteResInfo = 65;    //随机事件投票:活动信息
    ActivityMobaRandomPlayerResVote activityMobaRandomPlayerResVote = 66; //随机事件投票:投票
    //随机事件投票-End

    // 元宵灯谜活动-Begin
    LanternRiddlesRspInfo lanternRiddlesRspInfo = 67;   // 元宵灯谜活动：答题信息
    // 元宵灯谜活动-End

    // 柯南二期ip活跃活动-Begin
    ConanPlazaActiveBuffRspInfo conanPlazaActiveBuffRspInfo = 68; // 柯南二期ip活跃活动-获得buff返回
    ConanPlazaActiveChallengeAwardRspInfo conanPlazaActiveChallengeAwardRspInfo = 69; // 柯南二期ip活跃活动-挑战领取奖励返回
    // 柯南二期ip活跃活动-End



    // 狼人召回-Begin
    WolfReturnReceiveInvitationRspInfo wolfReturnReceiveInvitationRspInfo = 75;                       // 接收邀请
    WolfReturnRewardRsqInfo wolfReturnRewardRsqInfo = 76;                       // 领取奖励
    WolfReturnFriendListRsqInfo wolfReturnFriendListRsqInfo = 77;
    // 狼人召回-End



    //特色玩法内容整合模板-Begin
    FeatureIntegrationAddPicRspInfo featureIntegrationAddPicRspInfo = 79; //记录图片id
    // 特色玩法内容整合模板-End


    // 晴霜试炼-Begin
    QingShuangTrialRewardRspInfo qingShuangTrialRewardRspInfo = 80;
    // 晴霜试炼-End

    WolfReturnGetStateRsqInfo wolfReturnGetStateRsqInfo = 81;

    //农场答题 开始
    FarmAnwerRspAnswer farmAnwerRspAnswer = 82;
    FarmAnwerRspInfo farmAnwerRspInfo = 83;
    //农场答题 结束


    // 宝箱升级-Begin
    TreasureLevelUpTakeBoxRewardRspInfo treasureLevelUpTakeBoxRewardRspInfo = 85;    // 领取宝箱奖励
    // 宝箱升级-End

    FlashRaceCheeringVoteResInfo flashRaceCheeringVoteResInfo = 86; // 闪电助威投票数据
    FlashRaceCheeringVoteOptionResInfo flashRaceCheeringVoteOptionResInfo = 87; // 闪电助威投票

    QingShuangTrialGetQuizResInfo qingShuangTrialGetQuizResInfo = 88;     // 获取测验
    QingShuangTrialAnswerQuestionResInfo qingShuangTrialAnswerQuestionResInfo = 89; // M奖励测验

    // moba挑战Begin
    MobaChallengeRsqInfo mobaChallengeRsqInfo = 90;
    MobaChallengeLevelRspInfo mobaChallengeLevelRspInfo = 91;
    // moba挑战End

    //活动抽奖-Begin
    LotteryGiftRspInfo lotteryGiftRspInfo = 92; // 请求抽奖
    //活动抽奖-End
    //暑期BP活动领奖
    SummerVacationBPRewardRspInfo summerVacationBPRewardRspInfo = 93; //

    // 暑期导航栏-Begin
    SummerNavigationBarRewardRspInfo summerNavigationBarRewardRspInfo = 94;    // 领取吧唧奖励
    // 暑期导航栏-End

    //暑期快闪全量任务
    SummerFlashMobTaskRsqInfo summerFlashMobTaskRsqInfo = 95; //
    // 暑期导航栏-End
  }
}

enum ActivityGeneralNtyMsgType {
  //许愿树-领取许愿奖励结果Nty
  AGNY_WishingTreeReceiveWishReward = 1;
  // 团购返利-队伍信息变化通知
  AGNY_GroupingReturnChangeNtf = 2;
}

enum BagVehicleType {
  NORMAL_VEHICLE  = 0;
  // 表情
  FACE_VEHICLE = 1000;
}

message ActivityGeneralNtyInfo {
  oneof GeneralNtyInfo {
    //许愿树-领取许愿奖励结果Nty
    WishingTreeReceiveWishRewardNtyInfo wishingTreeReceiveWishRewardNtyInfo = 1;
    // 团购返利-队伍信息变化通知
    ActivityGroupingReturnResInfo groupingReturnChangeNtf = 2;
  }
}


//许愿树-领取许愿奖励 Nty
message WishingTreeReceiveWishRewardNtyInfo {
  repeated RewardItemInfo rewardItems = 1;      // 获得的道具
}

//许愿树-许愿奖励
message WishingTreeMakeWishReqInfo {
  optional int32 treeId = 1;  // 许愿树id
  optional int32 amsPackageGroupId = 2;   // ams礼包组id
}

//许愿树-许愿奖励
message WishingTreeMakeWishRspInfo {
  optional int32 amsPackageGroupId = 1;   // ams礼包组id
}

// 柯南预热活动-信息-rsp
message FarmAnwerRspInfo {
  optional int32 activityDays = 1;      // 当前属于活动第几天(从1开始)
  optional int32 answeredDays = 2;      // 已答题到第几天(也就是第几道题, 从1开始)
  repeated QuestionAnswerRecord answerRecords = 3; // 已经答过的题目答题记录
}
//许愿树-领取许愿奖励
message WishingTreeReceiveWishRewardReqInfo {
  optional int32 treeId = 1;  // 许愿树id
}

//许愿树-领取许愿奖励
message WishingTreeReceiveWishRewardRspInfo {

}

message TravelingDogStageDataReqInfo {

}

message TravelingDogRewardDataReqInfo {

}

message TravelingDogDogTravelReqInfo {

}

message TravelingDogDogGoHomeReqInfo {

}

message SummerNavigationBarRewardReqInfo {
  optional int32 taskId = 1;      //吧唧奖励对应的任务Id
}
message SummerFlashMobTaskReqInfo {
}
message SummerFlashMobTaskRsqInfo {

  repeated TaskStatusInfo allTaskList = 1; // 所有任务列表
  repeated TaskStatusInfo changeTask = 2; // 改变的任务

}

message FashionFundReturnGetReqInfo {
  optional int32 type = 1;//领取类型0购买结束前返利,1购买结束后返利
}

message FashionFundReturnGetRspInfo {

}

message IntKVArray {
  repeated IntKVEntry array = 1;
}

message IntLongKVArray {
  repeated IntLongKVEntry array = 1;
}

message IntMapData {
  map<int32, int32> data = 1;       // <key, value>
}

message ACTDanceOutfitGenerationReqInfo {
  required int32 keyword_id = 1; // 关键字id
  required int32 style_id = 2; // 风格id
}
message ACTDanceOutfitGenerationRspInfo {
}

message ACTDanceOutfitChangeReqInfo {
}
message ACTDanceOutfitChangeRspInfo {
}

message ACTDanceOutfitCancelReqInfo {
}
message ACTDanceOutfitCancelRspInfo {
}
message ACTDanceOutfitCollectReqInfo {
  required int64 index = 1;//时间戳
  required bool changeState = 2;// 修改状态
  required int32 collectIndex = 3;// 当前位置
}
message ACTDanceOutfitCollectRspInfo {
  optional int64 index = 1;//时间戳
  optional bool state = 2;// 状态
}



// 钓鱼名人堂-信息-req
message ActivityFishingHallOfFameReqInfo {
}

// 钓鱼名人堂-第1阶段数据
message ActivityFishingHallOfFameStage1Data {
  optional int32 layer = 1;         // 分区/层级 (见: N_农场钓鱼表.xlsx#鱼类配置#layer) (浮游层、中浮游层、下浮游层、阳光层、中阳光层、下阳光层、透光层)
  optional int32 fishId = 2;        // 鱼id (见: N_农场钓鱼表.xlsx#鱼类配置#id)
  optional int64 finalScore = 3;    // 最终分 (根据分数, 读配置表算出 鱼重量/鱼品质(S/D等))
  optional int64 uid = 4;           // 玩家id (客户端二次向服务器查询头像/昵称等信息)
}

// 钓鱼名人堂-第2阶段数据
message ActivityFishingHallOfFameStage2Data {
  optional int64 startTime = 1;     // 时段-开始时间戳
  optional int64 endTime = 2;       // 时段-结束时间戳
  optional int32 fishId = 3;        // 鱼id (见: N_农场钓鱼表.xlsx#鱼类配置#id)
  optional int64 finalScore = 5;    // 最终分 (根据分数, 读配置表算出 鱼重量/鱼品质(S/D等))
  optional int64 uid = 6;           // 玩家id (客户端二次向服务器查询头像/昵称等信息)
}

// 钓鱼名人堂-第1阶段
message ActivityFishingHallOfFameStage1 {
  optional int64 startTime = 1;     // 时段-开始时间戳
  optional int64 endTime = 2;       // 时段-结束时间戳
  optional int32 timeBucket = 3;    // 时段-当前时段
  repeated ActivityFishingHallOfFameStage1Data layerData = 4;    // 名人堂分区数据, 每个分区1个冠军
}

// 钓鱼名人堂-第2阶段
message ActivityFishingHallOfFameStage2 {
  optional int32 layer = 1;         // 分区/层级
  repeated ActivityFishingHallOfFameStage2Data layerData = 2;     // 第2阶段数据, 每个时段得冠军
}

// 钓鱼名人堂-信息-rsp
message ActivityFishingHallOfFameRspInfo {
  optional int32 fishingHallOfFameCurrentStage = 1;               // 当前阶段
  optional ActivityFishingHallOfFameStage1 stage1Data = 2;        // 第1阶段
  repeated ActivityFishingHallOfFameStage2 stage2Data = 3;        // 第2阶段
}

// 钓鱼名人堂-层数排行榜-req
message ActivityFishingFameReqLayer {
  optional int32 layer = 1;                                   // 分区/层级, 从首页进入时客户端请求 layer=1, 点击其他鱼层时再设置相应的鱼层
}

// 钓鱼名人堂-层数排行榜-rsp
message ActivityFishingFameRspLayer {
  optional int32 errorCode = 1;					              // 0:正常返回 非0:NKErrorCode错误码
  optional int32 layer = 2;
  optional int32 fishingHallOfFameCurrentStage = 3;           // 当前阶段
  optional ActivityFishingFameStage1 stage1Data = 4;          // 第1阶段
  optional ActivityFishingFameStage2 stage2Data = 5;          // 第2阶段
}

// 钓鱼名人堂-玩家信息
message ActivityFishingFamePlayerInfo {
  optional int32 fishId = 1;        // 鱼id (见: N_农场钓鱼表.xlsx#鱼类配置#id)
  optional int64 finalScore = 2;    // 最终分 (根据分数, 读配置表算出 鱼重量/鱼品质(S/D等))
  optional int64 uid = 3;           // 玩家id (客户端二次向服务器查询头像/昵称等信息)
  optional bool hasFollowed = 4;    // 是否点赞过对方
  optional int64 fansNum = 5;       // 点赞数量/实时粉丝数
}

// 钓鱼名人堂-第1阶段
message ActivityFishingFameStage1 {
  optional int64 startTime = 1;     // 时段-开始时间戳
  optional int64 endTime = 2;       // 时段-结束时间戳
  optional int32 timeBucket = 3;    // 时段-当前时段
  optional ActivityFishingFamePlayerInfo playerInfo = 4;        // 玩家信息
}

// 钓鱼名人堂-第2阶段-每个时段的数据
message ActivityFishingFameStage2Data {
  optional int64 startTime = 1;     // 时段-开始时间戳
  optional int64 endTime = 2;       // 时段-结束时间戳
  optional ActivityFishingFamePlayerInfo playerInfo = 3;        // 玩家信息
}

// 钓鱼名人堂-第2阶段
message ActivityFishingFameStage2 {
  repeated ActivityFishingFameStage2Data stage2Data = 2;        // 第2阶段数据, 每个时段的冠军
}

// 柯南预热活动-信息-req
message ActivityConanWarmupReqInfo {
}

// 柯南预热活动-信息-rsp
message ActivityConanWarmupRspInfo {
  optional int32 activityDays = 1;      // 当前属于活动第几天(从1开始)
  optional int32 answeredDays = 2;      // 已答题到第几天(也就是第几道题, 从1开始)
  repeated QuestionAnswerRecord answerRecords = 3; // 已经答过的题目答题记录
}

// 柯南预热活动-答题(签到)-req
message ActivityConanWarmupReqAnswer {
  optional int32 questionId = 1;        // 问题id
  optional int32 choseOptionId = 2;     // 选择的选项id
}

// 柯南预热活动-答题(签到)-rsp
message ActivityConanWarmupRspAnswer {
  optional int32 activityDays = 1;      // 当前属于活动第几天(从1开始)
  optional int32 answeredDays = 2;      // 已答题到第几天(也就是第几道题, 从1开始)
  repeated QuestionAnswerRecord answerRecords = 3; // 已经答过的题目答题记录
}

// 餐厅主题活动-上菜
message ActivityRestaurantThemedReqServing {
  // optional int32 npcId = 1;             // npcId
  // optional int32 foodId = 2;            // 菜品id
}

message ActivityRestaurantThemedRspServing {
}

// 餐厅主题活动-通过分享领取印章
message ActivityRestaurantThemedReqReceive {
  optional int32 foodId = 1;            // 菜品id
  optional int64 uid = 2;               // 分享者uid
}

message ActivityRestaurantThemedRspReceive {
}

// 元宵灯谜活动
message LanternRiddlesReqInfo {
  optional int32 questionId = 1;  // 问题id
  optional int32 choseOptionId = 2;  // 用户选项
}

message QingShuangTrialRewardReqInfo{
  optional int32 rewardId = 1;  // 奖励Id
}
message QingShuangTrialRewardRspInfo{
}




message MobaChallengeLevelReqInfo {
  optional int32 levelid = 1;  // levelid
}
message MobaChallengeLevelRspInfo {
  optional int32 code = 1;     // 结果  0成功  1失败
  optional int32 levelid = 2;  // levelid
}
message MobaChallengeReqInfo {
}

message MobaChallengeRsqInfo {
  repeated MobaChallengeModle mobaChallengeModle = 1;
}
message MobaChallengeModle {
  optional int32 level = 1;
  optional int32 status = 2;
}

message LanternRiddlesRspInfo {

}
// 农场答题
message FarmAnwserReqInfo {
  optional int32 questionId = 1;        // 问题id
  optional int32 choseOptionId = 2;     // 选择的选项id
}
message ActivityFarmAnwerRspAnswer {
  optional int32 activityDays = 1;      // 当前属于活动第几天(从1开始)
  optional int32 answeredDays = 2;      // 已答题到第几天(也就是第几道题, 从1开始)
  repeated QuestionAnswerRecord answerRecords = 3; // 已经答过的题目答题记录
}

// 狼人组队开宝箱抽奖
message LotteryGiftReqInfo {
  optional int32 type = 1;// 1消耗抽奖道具,2每日免费,3GM
}

message LotteryGiftRspInfo {
  optional int32 code = 1;
  repeated int32 configId = 2;
  repeated ItemInfo itemInfo = 3;
}
enum SummerRewardType {
  LVL_ALL = 0;				// 全部
  LVL_SINGLE_LEV = 1;	// 某一个等级
  LVL_BUFF = 2; 		// buff
}
message SummerVacationBPRewardReqInfo {
  optional SummerRewardType type = 1;//1=单个   2=全部
  optional int32 level = 2;
}
message SummerVacationBPRewardRspInfo {
  optional int32 code = 1;//0=成功
  optional int32 level = 2;
}
// 特色玩法内容整合模板-记录图片id
message FeatureIntegrationAddPicReqInfo {
  optional int32 picture = 1;
}

message FeatureIntegrationAddPicRspInfo {
  repeated int32 pictureList = 1;
}

// 柯南广场配套活跃活动-获得buff
message ConanPlazaActiveBuffReqInfo {
}

message ConanPlazaActiveBuffRspInfo {
  optional bool buff = 1;  // 是否获得buff,true表示获得buff
}


// 柯南广场配套活跃活动-挑战奖励
message ConanPlazaActiveChallengeAwardReqInfo {
  optional int32 choose = 1;      //选择卡牌奖励id
  optional bool buff = 2;       //true有buff
}

message ConanPlazaActiveChallengeAwardRspInfo {
  optional int32 rewardCount = 1;  // 当天获得奖励的次数
}

message ACTWishSelectReqInfo{
  repeated int64 giftIds = 1;             // 礼物ID
}

message ACTWishSelectRspInfo{
  repeated int64 giftIds = 1;             // 礼物ID
}

message ACTWishTaskDoneReqInfo{
  optional int64 taskId = 1;          // 任务ID
}

message ACTWishTaskDoneRspInfo{
  optional int64 taskId = 1;          // 任务ID
}

message ACTWishFriendsHelpReqInfo{
  optional int64 code = 1;            // 邀请码
  optional string helperName = 2;     // 助力人昵称
}

message ACTWishFriendsHelpRspInfo{
  optional int64 code = 1;            // 邀请码
  optional int64 helpUid = 2;         // 被助力人Uid
}

message ACTWishGetAwardReqInfo{
}

message ACTWishGetAwardRspInfo{
}

message ACTTrainingCampGetSportsmanDataReqInfo {
  optional int32 type = 1; // 0-全部 1-运动员数据 2-助力数据 3-被助力数据
}
message ACTTrainingCampGetSportsmanDataRspInfo {
  optional int32 type = 1; // 0-全部 1-运动员数据 2-助力数据 3-被助力数据
  optional TrainingCampSportsmanData data = 2; // 运动员数据
  optional TrainingCampAssistData assisted = 3; // 助力数据
  optional TrainingCampAssistData beAssisted = 4; // 被助力数据
}

message ACTTrainingCampTrainSportsmanReqInfo {
  optional int32 sportsId = 2;
}
message ACTTrainingCampTrainSportsmanRspInfo {
  optional TrainingCampSportsman sportsman = 1;
}

enum TrainingDataType {
  ALL = 0;				// 全部
  TRAINING_OBJECT = 1;	// 训练对象数据
  ASSIST_DATA = 2; 		// 助力数据
}

// 训练活动:获取数据请求
message ACTTrainingGetDataReqInfo {
  optional int32 type = 1; 						// by: TrainingDataType
}

// 训练活动:获取数据返回
message ACTTrainingGetDataRspInfo {
  optional int32 type = 1;							// by: TrainingDataType
  optional TrainingAllObjcetData object = 2; 		// 训练对象数据
  optional TrainingAssistData assisted = 3; 		// 助力数据
  optional TrainingAssistData beAssisted = 4; 		// 被助力数据
  repeated TrainingAssistDetail assistDetail = 5;	// 助力详细信息
  repeated TrainingAssistDetail beAssistDetail = 6;	// 被助力详细信息
}

// 训练活动:训练对象请求
message ACTTrainingTargetTrainingReqInfo {
  optional int32 objectId = 1;	//训练对象ID
}

// 训练活动:训练对象返回
message ACTTrainingTargetTrainingRspInfo {
  optional int32 objectId = 1;				// 训练对象ID
  optional TrainingObjcetData data = 2; 	// 训练对象当前数据
}

// 训练活动:领取训练目标奖励请求
message ACTTrainingReceiveAwardReqInfo {
  optional int32 objectId = 1;	// 训练对象ID
  optional int32 index = 2;	    // 领取奖励idx
}

// 训练活动:领取训练目标奖励返回
message ACTTrainingReceiveAwardRspInfo {
  optional int32 objectId = 1;     // 训练对象ID
  optional int32 index = 2;	       // 领取奖励idx
  repeated ItemInfo itemInfo = 3;  // 奖励内容
}

message CoinInfo {
  optional com.tencent.wea.xlsRes.CoinType coinType = 1;
  optional int64 coinNum = 2;
  optional string currencyItemId = 3;
}

message CoinArray {
  repeated CoinInfo coins = 1;
}
// 农场答题
message FarmAnwserReqAnswer {
  optional int32 questionId = 1;        // 问题id
  optional int32 choseOptionId = 2;     // 选择的选项id
}
message FarmAnwerRspAnswer {
  optional int32 activityDays = 1;      // 当前属于活动第几天(从1开始)
  optional int32 answeredDays = 2;      // 已答题到第几天(也就是第几道题, 从1开始)
  repeated QuestionAnswerRecord answerRecords = 3; // 已经答过的题目答题记录
}


// 服务器类型


// DS玩法类型
enum DsPlayType {
  DPT_Default = 0;
  DPT_Battle = 1; // 对局(从battlesvr拉起)
  DPT_FarmOrXiaoWo = 2; // 农场or家园(从farmsvr拉起)
  DPT_Lobby = 3; // 大厅(从lobbysvr拉起)
}

//idip 处理进程
enum IdipHandler {
  IH_None = 0;
  IH_GameSvr = 1;
  IH_IdipSvr = 2;
  IH_DirSvr = 3;
  IH_ChatSvr = 4;
  IH_BattleSvr = 5;
  IH_XiaowoSvr = 6;
  IH_TranslateSvr = 7;
  IH_ClubSvr = 8;
  IH_FarmSvr = 9;
  IH_ActivitySvr = 10;
  IH_SnsSvr = 11;
  IH_ArenaSvr = 12;
  IH_UgcDataStoreSvr = 13;
  IH_StarpSvr = 14;
  IH_StarpAccountSvr = 15;
}

enum RemoteServiceType {
  REMOTE_GAME_SERVICE = 1;
  REMOTE_ALLIANCE_SERVICE = 2;
  REMOTE_CHAT_SERVICE = 3;
  REMOTE_PROXY_SERVICE = 4;
  REMOTE_DIR_SERVICE = 5;
  REMOTE_RANK_SERVICE = 6;
}
enum RegionType {
  RegionInit = 100;
  RegionUgc = 101;
}

enum RankType {
  RT_Unknown = 0;
  RT_Friend = 1;
  RT_Global = 2;
  RT_Geo = 3;
}

// 通用性别枚举
enum Gender {
  G_UNKNOWN = 0;
  G_MALE = 1;
  G_FEMALE = 2;
}

message ShareDataStat {
  optional string name = 1;
  optional int64 uuid = 2;
  optional bool load = 3;
}

message ShareDataInfo {
  message ChangeInfo {
    optional int32 oldVersion = 1;
    optional int32 newVersion = 2;
    optional int64 changeBits = 3;
  }
  optional string name = 1;
  optional int64 uuid = 2;
  optional int32 version = 3; // 版本号
  optional ChangeInfo change = 4;
}

// 服务器类型
enum CompareMethodType {
  CMT_UNKNOWN = 0; // 非法
  CMT_EQ = 1; // 等于
  CMT_GT = 2; // 大于
  CMT_GE = 3; // 大于等于
  CMT_LT = 4; // 小于
  CMT_LE = 5; // 小于等于
}

// 服务器信息
message SvrInfo {
  optional string svrId = 1; //服务器id
  optional int32 zoneId = 7; //分区id
  optional string svrIp = 2; //gamesvr的ip
  optional string svrBackIp = 3;
  optional string svrPort = 4;
  optional string name = 5;
  optional DirsvrStateType state = 6;
}

enum DirsvrStateType {
  DirsvrState_Type_NOTOPEN = 0; // 未开服
  DirsvrState_Type_LOOSE = 1; // 流畅
  DirsvrState_Type_CROWD = 2; // 拥挤
  DirsvrState_Type_FULL = 3; // 爆满
  DirsvrState_Type_MAINTAIN = 4; // 维护状态
}

message TlogRequiredFields {
  optional int32 platID = 1;
  optional string vopenid = 2;
  optional int64 vRoleID = 3;
  optional int32 npc = 4;
  optional string appId = 5;
  optional int32 level = 6;
  optional string ServerIp = 7;
  optional string iSequence = 8;
  optional string telecomOper = 9;
  optional string network = 10;
  optional string clientIP = 11;
  optional string clientVersion = 12;
  optional string vClientIPV6 = 13;
  optional int32 clientPlat = 14;
  optional int32 seasonId = 15;
  optional string country = 16;
  optional string province = 17;
  optional string city = 18;
  optional string district = 19;
  repeated string ReservePara = 20; //只能填充10个
  optional string dsSessionId = 21; // 标记是来自哪个ds发来的协议，目前只有农场会使用！
}

//@noclient
message PlayerSimple {
  optional int64 uid = 1;
  optional int32 Platid = 2; //0ios 1android
  optional string Openid = 3;
  optional int32 Zoneid = 4;
  optional int64 lastLoginTime = 6;
  optional int64 lastLogoutTime = 7;
  optional int64 lastHeartBeatTime = 8;
  optional com.tencent.wea.xlsRes.TconndApiAccount accountType = 9;
}

message PlayerGameEventData {
  optional int64 playerUid = 1;
}

// ------------------------------- Mail START ----------------------------------------------
message MailAttachment {
  optional ItemInfo itemIfo = 1;
  optional string params = 3;       // reserved
}

message MailCommodityInfo {
  optional int32 commodityId = 1;   // 索要商品id
  optional int32 commodityNum = 2;  // 索要商品数量
}

// 仅展示用的附件
message MailShowOnlyAttachment {
  repeated ItemInfo itemInfo = 1;
}

message MailAttachmentList {
  repeated MailAttachment list = 1;
  optional MailCommodityInfo commodityInfo = 2;
  optional MailShowOnlyAttachment showOnlyAttachment = 3; // 仅仅用来展示的道具，比如好友使用心心糖果，请勿领取或使用！！！
}

message GlobalMailAttachmentList {
  repeated MailAttachmentList list = 1;
}

message MailContent {
  optional string Text = 1;
  optional string CdKey = 2;
}

enum MailSourceType {
  MT_Source_Default = 0;
  MT_Source_Global_Idip = 1;
  MT_Source_Global_Xls = 2;
  MT_Source_Person_Idip = 3;
  MT_Source_Template_Xls = 4;
}

message MailSource {
  optional int32 isOverSea = 1; //是否是国际化邮件
  optional int32 sourceType = 2; //邮件来源类型
  optional int32 cfgId = 3; // 来源配置id
  repeated string params = 4; // 邮件正文中的format参数
  optional com.tencent.wea.xlsRes.MailExtraType extraType = 5;
  optional bool isStarred = 6;
}

message MailIntimacyRecommendData {
  optional int64 friendUid = 1;
  optional int32 intimateId = 2;  // 推荐的亲密关系id
}

// 生日贺卡
message MailBirthdayCardInfo {
  // optional int64 itemUUID = 1;                     // 关联的道具UUID
  optional string birthdayCardUniqueId = 1;         // 关联的贺卡id. 贺卡可以删除的, 删除后邮件中查看不了贺卡信息
  optional BirthdayCardData birthdayCardData = 2;   // 贺卡数据
}

//外观类商品赠礼卡信息
message MallGiftCardInfo {
  optional int32 cardType = 1;    // 赠礼卡模板类型。无赠礼卡则填0
  optional int32 wordsId = 2;    // 赠礼卡默认祝福语id。自定义祝福语则填0
  optional string wordsContent = 3;    // 赠礼卡自定义祝福语内容
  optional bool isBirthday = 4;   // 是否是生日赠送
}

// 膨胀爆红包充值参数
message ActivityInflateRedPacketParam {
  optional int32 activityId = 1;  // 活动id
  optional int32 money = 2;       // 红包金额
}

// coc防守日志邮件结构化数据
message MailExtraCOCDefenseBattleData {
  optional PlayerColdData attackerProfile = 1; // 进攻方信息
  optional int64 battleTimeMs = 3;             // 被攻击时间(毫秒时间戳)
  optional bool defendSuccess = 4;             // 战斗结果-是否防守成功
  optional bool xingBaoCaptured = 5;           // 星宝是否被俘虏
  repeated CocSoldier battleSoldier = 6;       // 进攻方出战士兵
  optional int32 cupsScoreChanged = 7;         // 奖杯积分变更
  optional int32 overallDamageStar = 8;        // 破坏率星级
  optional double overallDamagePer = 9;        // 破坏率百分比
  map<int32, int64> plunderedResources = 10;   // 被掠夺的资源数量 resType -> resNum
  optional bool hashFoughtBack = 11;           // 是否已反击（这是个可变字段，不想影响到其他邮件，放在专用extra里）
}

// coc进攻日志邮件结构化数据
message MailExtraCOCAttackBattleData {
  optional PlayerColdData defenderProfile = 1;    // 防守方信息
  optional int64 battleTimeMs = 3;                // 进攻时间(毫秒时间戳)
  optional bool attackSuccess = 4;                // 攻击胜利
  repeated CocSoldier battleSoldier = 6;          // 进攻出战士兵
  optional int32 cupsScoreChanged = 7;            // 奖杯积分变更
  optional int32 overallDamageStar = 8;           // 破坏率星级
  optional double overallDamagePer = 9;           // 破坏率百分比
  map<int32, int64> plunderGainedResources = 10;  // 掠夺获取的资源数量 resType -> resNum
  optional bool capturedXingBao = 11;             // 是否抓获防守方星宝
}

message MailExtraData {
  optional MailIntimacyRecommendData intimacyRecommendData = 1;
  optional MallGiftCardInfo mallGiftCard = 2; // 外观类商品赠礼卡信息
  optional MailExtraCOCDefenseBattleData cocDefenseBattleData = 3;
  optional MailExtraCOCAttackBattleData cocAttackBattleData = 4;
  optional MailBirthdayCardInfo mailBirthday = 5; // 生日信息
  optional string lowestVersion = 6;              // 最低版本号(包含)
  optional string highestVersion = 7;             // 最低版本号(包含)
  repeated com.tencent.wea.xlsRes.MailBuyItem mailBuyItem = 8;           // 购买商品
  optional string mailContentImg = 9;             // 邮件内容图片
  optional MailUgcData mailUgcData = 10;          // 邮件 UGC 信息
}

// 邮件 UGC 信息
message MailUgcData {
  repeated MailUgcMapData mailUgcMapData = 1;   // ugc map
}

// 从 message PublishItem 摘取
message MailUgcMapData {
  optional int64 ugcId = 1;               // ugcId
  repeated UgcMapMetaInfo metaInfo = 2;   // metaInfo
  optional string bucket = 3;             // bucket
  optional string name = 4;               // 名字,地图名
  optional string tags = 5;               // tags,标签名,玩法名
}

message GlobalMailExtraData {
  repeated com.tencent.wea.xlsRes.MailBuyItem mailBuyItem = 1;           // 购买商品
  optional string mailContentImg = 2;             // 邮件内容图片
  optional MailUgcData mailUgcData = 3;           // 邮件 UGC 信息
}

enum GlobalMailAttachType {
  GMAT_Default = 0; // 默认, Template(GlobalMailConfData)附件
  GMAT_Idip = 1;    // idip 自定义附件
}

//--- 通用组队匹配结构体开始 ---

message RuleDimInfo {
  //维度id
  optional int32 id = 1;
  //维度值以,做分割组合不同维度值
  optional string value = 2;

}

message PlayerSideInfo {
  optional int32 chaseSideType = 1; //chase模式阵营信息 com.tencent.wea.xlsRes.ChaseSideType
  optional int32 hideAndSeekSideType = 2; //躲猫猫模式阵营信息 com.tencent.wea.xlsRes.HideAndSeekType
}

message WereWolfSideIdentityItemInfo {
  optional int32 itemID = 1; // 需要消耗的道具ID
  optional int32 cnt = 2; // 需要消耗的道具数量
}

message WereWolfSideIdentityInfo {
  repeated int32 mapID = 1; //地图id 0表示随机
  optional int32 sideID = 2; //阵营id 0表示随机
  optional int32 identityID = 3; //身份id 0表示随机
  repeated WereWolfSideIdentityItemInfo consumeItemInfo = 4; //消费的道具信息
}


message WolfKillGameInfo {
  repeated int32 unlockVocationIdList = 1; // 狼人杀可使用的职业列表
  optional int32 equipAttackAni = 2; // 装备的攻击动画
  optional int32 equipReportAni = 3; // 装备的报告动画
  repeated int32 equipEmoji = 4; //装备的表情
  optional int32 equipMvpAni = 5; // 装备的MVP动画
  repeated int32 allAttackAni = 6; //拥有的攻击动画，在随机时候需要使用
  repeated int32 allReportAni = 7; //拥有的攻击动画，在随机时候需要使用
  repeated int32 allMvpAni = 8; //拥有的mvp动画，在随机时候需要使用
  optional int32 equipAttackAniIsRandom = 9; // 是否随机
  optional int32 equipReportAniIsRandom = 10; // 是否随机
  optional int32 equipMvpAniIsRandom = 11; // 是否随机
  repeated int32 equipTreasure = 12; // 装备的珍宝系统
  optional int32 treasureLevel = 13; // 珍宝等级
  optional int32 hyperCoreScore = 14; // 超凡特攻分数
  optional int32 roleInfoPoints = 20; // 身份专精点数
  repeated int32 shieldVocationIdList = 21; // 狼人杀屏蔽的职业列表
  optional int32 brawlLastSelected = 22; // 狼人杀大乱斗上局所选项
  optional int32 monthCardLevel = 23; // 狼人杀月卡等级
  optional int64 monthCardEndTs = 24; // 狼人杀月卡过期时间
}


message WereWolfRoleInfoPointsInfo {
  optional int32 roleType = 1; // 职业
  optional int32 curPoints = 2; // 当前精炼点数
  optional int32 addPoints = 3; // 增加精炼点数
  optional int32 maxPoints = 4; // 最大精炼点数
  optional int32 isWin = 5; // 是否胜利
}

message WereWolfRepeatWinInfo {
  optional int32 totalRepeatWin = 1; // 个人连胜场次
  optional int32 campRepeatWin = 2; // 阵营连胜场次
}

message MatchRuleClientInfo {
  /* ugc玩法数据 */
  // 以下字段变动需要跟随matchTypeId,ugcId,someWhereIn其中一个或多个字段的变更
  optional int32 gameSource = 1;
  optional int32 canvasId = 2;
  optional int32 logicMapSource = 3;
  optional int32 lobbyType = 4;  // 带上来的大厅类型
  optional int64 lobbyMapId = 5;  // 带上来的大厅id
  optional string tabDesc = 6;
  optional string searchID = 7; // 搜索id
  optional string activityId = 8; // 活动id
  optional string subTabName = 9; // 子页签名称
  /* ugc玩法数据 */
}

message MatchRuleInfo {
  optional int32 matchTypeId = 1; // 玩法id
  // ugc匹配字段
  optional int64 ugcId = 10; // 地图id 用于ugc地图匹配
  // 赛事匹配字段
  optional int32 custom_comp_id = 13; // 定制赛事-比赛id
  // 玩法入口
  optional int32 someWhereIn = 15;  // 哪边进入的匹配 参考MatchSomeWhereIn
  // 客户端数据透传字段
  optional MatchRuleClientInfo clientInfo = 17;  // 客户端透传
  // ugc附加匹配字段
  optional int64 ugcCfgId = 18;  // 按钮合集配置id, 多图匹配或者单图匹配传递0即可

  // 服务器内部字段，客户端勿填写
  repeated RuleDimInfo dimension_list = 2;
  optional int32 roomInfoID = 3;
  optional RoomType roomType = 4; // 房间类型
  optional GameModeType gameModeType = 5; // 当前游戏模式
  optional string recid = 6; // 玩法推荐
  optional string expTag = 7; // 玩法推荐
  optional int32 ruleId = 9; // ruleid
  optional UgcMdList ugcMdList = 11;  // Ugc地图信息 用于地图匹配版本的传递
  optional PlayerSideInfo playerSideInfo = 12; //玩家阵营信息
  optional PlayerSideInfo playerSvrSideInfo = 14; //服务器玩家阵营信息
  optional WereWolfSideIdentityInfo wereWolfSideIdentityInfo = 16; //狼人杀阵营身份信息
  optional MainPlayModeMatchInfo mainPlayModeMatchInfo = 19; //主玩法触发特殊匹配信息
  optional MobaMatchInfo mobaMatchInfo = 20; //moba玩法触发特殊匹配信息
  // sp
  optional int64 starPWorldId = 30; // 啾灵世界id(啾灵玩法必填)
  optional int32 difficultyId = 31; // 啾灵副本难度id(啾灵玩法选填)
  optional int32 matchExpireTime   = 32; // 啾灵匹配超时时间
  optional int32 confirmExpireTime   = 33; // 啾灵确认超时时间
  optional int64 starPRoleId = 34; // starP角色Id-StarP角色跨世界转移(啾灵玩法必填)
  optional int64 publishTime = 35; // SP发布招募时间(ms)
  optional int64 requireSpLevel = 36; // SP招募加入等级要求
}

message MobaMatchInfo {
  optional bool isAiLeaderMatch = 1; //是否是AI作为队长发起的对局
}


message MainPlayModeMatchInfo {
  optional bool isThreeRoundTest = 1; //是否启用三轮制实验
  optional bool isOneRoundTest = 2; //是否启用一轮大乱斗实验
}

message UgcMatchRoomExtraInfo {
  optional bool ugcIsCompilations = 1;
  repeated int64 ugcIds = 2;  // 合集地图id
  optional string compName = 3;  // 合集名
}

message RoomLevelSetting {
  optional int32 index = 1; // 关卡序号 1开始
  repeated int32 levelIds = 2; // 关卡id列表
  optional RoomLevelSettingType settingType = 3; // 预留拓展
}

//--- 通用组队匹配结构体结束 ---

message PlatFriendList{
  repeated PlatFriendData data = 1;
}

message PlatFriendData{
  optional int64 uid = 1;
  optional string openid = 2;
  optional string platName = 3;  // 平台昵称
}

message RelationMsgExtraInfo {
  optional int32 intimateId = 1;
  optional int32 reason = 4;
  optional int32 subReason = 5;
  // 这个是申请列表中真正的添加原因(FriendAddMotivationType), 上面的是source,
  // 但改已有字段名太难了, 把这里命名成motivation
  optional int32 addMotivationEnumVal = 6;
  // 在收到好友申请时, 被申请者看到的推荐申请者的信息
  optional AlgoRecommendAddFriendReasonInfo recommendAddInfo = 7 [deprecated = true];
  optional RecommendAddFriendReasonDesc recommendAddDesc = 8;
}

enum FriendIntimacyChangeReason {
  RUR_UNKNOWN = 0;
  RUR_BATTLE_INTIMACY = 1;  // 完成对局
  RUR_SEND_GIFT = 2;  // 赠礼
  RUR_GM = 3; // gm命令
  RUR_IDIP = 4; // idip 接口修改亲密关系
  RUR_GOLD_COIN = 5;  // 赠送印章
  RUR_MALL_GIVE = 6;  // 商城赠送
  RUR_Chat = 7; // 聊天
  RUR_GIVE_INTER_SERVER_GIFT = 8; // 赠送一元幸启
  RUN_IDIP = 9; // idip 接口修改亲密度
  RUR_VISIT_FARM = 10;  // 访问农场
  RUR_VISIT_XIAOWO = 11;  // 访问小窝
  RUR_LUCKY_FRIEND_TASK = 12; // 幸运好友任务奖励
  RUR_FARM_GIFT = 13; // 农场送礼
  RUR_SEND_PRAYER_CARD = 14; // 赠送祈福牌
  RUR_SEND_BIRTHDAY_CARD = 15; // 生日贺卡赠送
  RUR_NR3E8_DONATE_CARD = 16; // 大富翁赠送卡片
}

// 关系链消息结构体
message RelationMsg {
  repeated RelationMsgInfo addRelationMsg = 2;
  repeated RelationMsgInfo agreeRelationMsg = 3;
  repeated RelationMsgInfo denyRelationMsg = 4;
  repeated RelationMsgInfo removeRelationMsg = 5;
}

enum PlayerNoticeMsgType {
  PNT_UNKNOWN = 0;                    // 缺省默认值
  PNT_DENY_INVITATION = 1;            // 拒绝邀请
  PNT_DENY_JOIN_REQ = 2;              // 拒绝入队申请
  PNT_CLIENT_VERSION_INVALID = 3;     // 客户端版本异常
  PNT_DENY_IN_ROOM = 4;               // 正在组队中, 拒绝
  PNT_DENY_IN_BATTLE = 5;             // 正在对战中, 拒绝
  PNT_KICK_BY_ROOM_LEADER = 6;        // 被队长踢出队伍
  PNT_MATCH_WAIT_CONFIRM_TIMEOUT = 7; // 匹配等待确认超时
  PNT_POSITION_EXCHANGE_DENY = 8;     // 座位交换申请拒绝
  PNT_CLIENT_VERSION_PLAY_MISMATCH = 9;// 版本玩法不兼容
  PNT_PARTNER_LEAVE_ROOM = 10;        // 成员离开房间
  PNT_PARTNER_REJECT_JOIN_ROOM = 11;  // 成员拒绝加入房间
  PNT_LEADER_EXIT_ROOM_WITH_TEAM = 12;// 队长带头退出房间
  PNT_ROOM_UGC_MAP_TAKEOFF = 13;      // ugc地图下架
  PNT_ROOM_KICK_WITH_TEAM = 14;       // 队伍一起被踢
  PNT_ROOM_MEMBER_LADDER_BAN = 15;    // 成员排位被ban
  PNT_ROOM_MEMBER_HAS_SIMULATOR = 16; // 队伍有模拟器玩家
  PNT_ROOM_MEMBER_SIMULATOR_LEAVE = 17; // 队伍模拟器玩家离开
  PNT_ERR_CODE_NTF = 18;              // 错误信息提示
  PNT_LOBBY_TRANSFER_NTF = 19;        // 大厅传送信息提示
  PNT_ROOM_PLAY_BLOCKED_BY_CLIENT_INFO_NTF = 20;     // 玩法屏蔽提示
  PNT_BIRTHDAY_CARD_SEND_CONVERT_TYPE = 21;     // 生日赠礼到期送转为立即送
  PNT_MATCH_WAIT_CONFIRM_ENTER_TIMEOUT = 22; // 确认进入对局超时
  PNT_ROOM_MEMBER_STARP_BAN = 23; // 成员啾灵副本被ban
  PNT_ROOM_PRE_START_CANCEL_NTF = 24; //房间自动开局被取消

  PNT_MIDAS_TEST_PAY_TIPS = 1001; // 模拟midas支付时提示Tips
  PNT_MIDAS_PAY_LOCK_TIPS = 1002; // midas支付锁提示Tips
  PNT_GM_DEBUG_TIPS = 1003; // gm服务器回包Tips
  PNT_ROOM_MATCH_TYPE_SWITCH = 1004; // 组队房间模式自动切换
  PNT_MIDAS_ERROR_TIPS = 1005; // midas失败提示Tips
  PNT_UGC_ACTOR_LOAD_ERR_TIPS = 1006; // ugc道具加载失败

  PNT_MIDAS_BALANCE_NOT_ENOUGH = 1007; // midas余额不足
  PNT_MIDAS_BUY_LOCK_ERROR = 1008; // midas购买锁失败
  PNT_MIDAS_INVALID_LOGIN_TYPE = 1009; // midas登陆态校验失败
  PNT_MIDAS_COMMON_ERROR = 1010; // midas通用失败提醒
  PNT_OUTLOOK_GROUP_CONFLICT_IN_MATCH_TYPE = 1011; // 外观组在该玩法中被屏蔽

  PNT_XIAOWO_VER_FAIL = 1012; // 小窝兼容检查失败
  PNT_TEST_ENV_DS_VER_FAIL = 1013; // 测试环境DS兼容组检查失败
  PNT_FARM_VER_FAIL = 1014; // 农场兼容性检查失败
  PNT_HOUSE_VER_FAIL = 1015; // 农场小屋兼容性检查失败
  PNT_DEBUG = 1016; // debug信息
}

enum PlayerClubMsgType {
  PCT_UNKNOWN = 0;                // 缺省默认值
  PCT_DISSOLVE = 1;               // 解散
  PCT_KICKOUT = 2;                // 踢出
  PCT_APPLY_RES = 3;              // 申请结果
}

enum RelationMsgType {
  ADD_RELATION_MSG = 1;
  REMOVE_RELATION_MSG = 2;
  CHANGE_RELATION_MSG = 3;
  TRANSLATE_GAME_FRIEND = 4;
  DENY_RELATION_MSG = 5;
}

message RelationMsgInfo {
  optional int64 uid = 1;
  optional int64 createTime = 2;
}

message PlayerHotData {
  required int64 uid = 1;
  optional bool is_online = 2;
  optional com.tencent.wea.xlsRes.RoomStatus status = 3;
  optional int64 scene_id = 4;
  optional int32 map_id = 5;
  optional int64 room_id = 6;
  optional bool room_is_full = 7;
}

enum ColdDataType {
  CDT_Unknown = 0;
  CDT_Relation = 1;
  CDT_Chat = 2;
  CDT_Xiaowo = 3;
  CDT_COC = 4;
}

//社区频道聊天室显示字段
message CommunityChannelColdData {
  optional int32 identityIconID = 1; // 身份 id
  optional int32 HeroIconID = 2; // 英雄 id
  optional bool last24HourArenaMVP = 3; // 是否过去24小时峡谷mvp
  optional bool last24HourWolfKillMVP = 4; // 是否过去24小时狼人杀mvp
  optional int32 farmLevel = 5;  // 农场等级
  optional bool farmMonthCard = 6;  // 是否开通农场月卡
}

// 玩家基础外显信息，服务于第三人称视角，如访客，聊天等场景
// PS: 不是所有字段都会赋值
message PlayerColdData {
  required int64 uid = 1;
  optional string nickname = 2;     //昵称
  optional int32 level = 3;        //等级
  optional string profile = 4;       //头像url (maybe)
  optional string friendNickName = 5;       //好友昵称
  optional int32 gender = 6;       //性别
  optional string openId = 8;
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo degreeInfo = 9; // 主玩法段位
  optional bool benefitCardEnable = 12;
  optional int32 vipLv = 13;
  optional HeadFrame headFrame = 14; // 头像框
  optional NamePlate namePlate = 15; // 铭牌
  optional CreatorAccountInfo creatorAccountInfo = 16;
  repeated int32 dressUpItems = 17; // 时装装扮信息
  optional bool returning = 18;   // 回归标识
  optional string xiaoWoName = 19; // 家园名称
  optional int64 returnExpiredSec = 20; // 回归标识过期时间戳
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo showDegreeInfo = 21; // 副玩法展示段位
  repeated int64 clubIds = 22; // 玩家加入的社团信息
  optional int32 cupsNum = 23;  // 奖杯数
  optional bool showCups = 24; // 展示奖杯
  optional CommunityChannelColdData communityChannelColdData = 25; ////社区频道聊天室显示字段
  optional int64 cocCupsScore = 26; // coc奖杯积分 type=ColdDataType.CDT_COC赋值
  optional bool hideEntertainmentQualifyInfo = 27; // 隐藏娱乐段位

  optional int32 cupsCycle = 28;  // 奖杯周目
}

message ChatModuleInfo {
  optional int32 moduleTypeVal = 1; // 模块类型，取值参考ChatModuleType
  optional ChatGroupInfo publicChatGroupInfo = 2; // 公共频道信息
  repeated ChatGroupInfoArray specificChatGroupInfo = 3; // 业务特有逻辑频道信息，二位数组
}

message ChatGroupDisplayInfo {
  optional string displayTitle = 1; // 外显标题
}

message ChatGroupInfo {
  optional ChatGroupKey key = 1; // 聊天key
  optional ChatGroupDisplayInfo displayInfo = 2; // 聊天频道外显信息
}

message ChatGroupInfoArray {
  repeated ChatGroupInfo groupInfo = 1; // 聊天频道列表，子列表存在折叠情况
  optional ChatGroupDisplayInfo displayInfo = 2; // 列表的统一外显信息
}

enum ChatType {
  CT_Unknown = 0;
  CT_Private = 1;         // 私聊
  CT_TeamGroup = 2;       // 房间内聊天
  CT_SideGroup = 3;       // 阵营内聊天
  CT_BattleChat = 4;      // 对局内聊天
  CT_BattleGroupChat = 5; // 弃用
  CT_Group = 6;           // 弃用
  CT_Zone = 7;            // 弃用
  CT_World = 8;           // 世界内聊天
  CT_Club = 9;            // 社团内聊天
  CT_Scene = 10;          // 弃用
  CT_TeamRecruit = 11;    // 弃用
  CT_Lobby = 12;          // 大厅聊天
  CT_LobbyFacility = 13;  // 大厅设施内聊天
  CT_NewStar = 14;        // 新人聊天
  CT_CustomRoom = 15;     // 多人房间内聊天
  CT_Xiaowo = 16;         // 小窝聊天
  CT_UgcCustomRoom = 17;  // ugc多人房间内聊天
  CT_CompetitionRoom = 18;// 赛事系统房间内聊天
  CT_AIChat = 19;         // AI聊天
  CT_Farm = 20;           //农场聊天
  CT_SuperCore = 21;      // 超核聊天 废弃
  CT_Stranger = 22;       // 陌生人聊天, 后台存储应定位到 CT_Private
  CT_FarmHouse = 23;           //农场小窝
  CT_FarmCommunityChannel = 24;     //农场社区频道
  CT_ArenaCommunityChannel = 25;     //峡谷社区频道
  CT_WolfKillCommunityChannel = 26;     //狼人杀社区频道
  CT_TradingCardChannel = 27;           //卡牌聊天频道
  CT_SpCommunityChannel = 28;           //sp兴趣频道
  CT_NR3E8Rich = 29;     //nr3e8Rich聊天
  CT_FarmCook = 30;           //农场餐厅

  CT_StarP = 31;          // 啾灵世界聊天
  CT_StarPGuild = 32;     // 啾灵公会聊天
  CT_StarPGroup = 33;     // 啾灵宗门聊天
  CT_ChatType_Max = 34;   // 参数上限
}
enum ChatMsgType {
  CMT_Unknown = 0;
  CMT_Text = 1;     // 普通文本
  CMT_Voice = 2;    // 语言信息
  CMT_Emoji = 3;    // 只带有普通表情
  CMT_PaidEmoji = 4;// 带有付费表情
  CMT_System = 5;      // 系统消息
  CMT_TeamRecruit = 6; // 组队招募信息
  CMT_SystemJoinTeam = 7; // 加入队伍系统通知
  CMT_SystemJoinScene = 8; // 加入场景系统通知
  CMT_PositionShare = 9; // 位置分享
  CMT_UgcMapShare = 10; // ugc地图分享
  CMT_TemplateText = 11; // 模板文字
  CMT_AIMessage = 12; // AI聊天
  CMT_SystemJoinClub = 13; // 加入社团系统通知
  CMT_ClubShare = 14; // 社团分享消息
  CMT_LobbyInteractionSystem = 15 ; // 大厅互动系统消息
  CMT_Top_LobbyInteractionSystem = 16 ; // 大厅互动置顶系统消息
  CMT_Spine_Emoji = 17;   // 聊天Spine动态表情消息
  CMT_RedPacketSend = 18; // 红包发送消息
  CMT_RedPacketReceive = 19; // 红包接收消息
  CMT_Share_BaseInfo = 20; // 个人信息
  CMT_Share_JoinTeam = 21; // 加入队伍
  CMT_Share_JoinRoom = 22; // 加入房间
  CMT_SystemClubModifyBrief = 23; // 系统消息：社团宣言修改
  CMT_UltramanInvite = 24; //奥特曼邀请
  CMT_SystemClubAddManager = 25; // 添加管理员
  CMT_SystemClubDelManager = 26; // 删除管理员
  CMT_SystemClubNewOwner = 27; // 更换团长
  CMT_SystemQuiteGame = 28;  // 系统消息 退出游戏
  CMT_UgcMapCollectionShare = 29; // ugc地图合集分享
  CMT_TeamBattleBroadcast = 30; // 队伍战斗播报
  CMT_SuperCore = 31; // 超核聊天
  CMT_ClubGroupInvite = 32; // 社团群聊邀请
  CMT_ActivityJoinTeam = 33; // 活动小队加入消息
  CMT_ClubNotifyManagerJoinRank = 34; // 提醒团长加入排行榜，改用CMT_JoinClubRankNotice
  CMT_PlayProposal = 35; // 玩法提议消息
  CMT_Screenshot = 36; // 截图消息
  CMT_AddPlayerHello = 37; // 初次加好友招呼信息
  CMT_IntimacyLevelNtf = 38; // 亲密度等级提示
  CMT_ClubWeekSettle = 39; // 社团周结算
  CMT_JoinClubRankNotice = 40; // 参加社团排行提醒
  CMT_SystemJoinClubRank = 41; // 加入社团排行系统消息
  CMT_JoinClubRank = 42; // 加入社团排行结构化消息
  CMT_IPEmoji = 43;   // 聊天动态表情消息
  CMT_GroupBuying = 44;   // 加入拼团消费活动队伍消息
  CMT_LuckyFriend = 45; // 亲密关系消息
  CMT_ReputationScore = 46; // 信誉分违规行为消息
  CMT_ReputationScoreTeammate = 47; // 信誉分违规行为队友消息
  CMT_SquadActivityInvitation = 48; // 小队活动邀请消息
  CMT_WolfKillSquadActivityInvitation = 49; // 狼人杀小队活动邀请消息
  CMT_ArenaWeeklyActivityInvitation = 50; //周五组队开黑邀请消息
  CMT_SurpassFriend = 51; //超越好友
  CMT_TradingCardTradeInfo = 52;//集卡交易的数据
  CMT_FarmBlessingTeamActivityInvitation = 53;//农场祈福小队邀请消息
  CMT_ShareGift = 54; //分享礼包
  CMT_BirthdayFriendRemind = 55;// 好友生日提醒结构化消息
  CMT_BirthdayCardSend = 56;// 好友生日贺卡赠送结构化消息
  CMT_DoubleTeamActivityInvitation = 57; //双人组队活动结构化消息
  CMT_ShareGiftMsg = 58; //双人组队活动结构化消息 -- 废弃，重复定义，请使用CMT_ShareGift
  CMT_FarmBuffWishSupport = 59; //农场回流助力增益
  CMT_PhotoAlbumRemind = 60; //个人相册提醒消息
  CMT_PositionShare_MusicConcert = 61; // 舞台位置分享
  CMT_WerewolfRecall = 62; // 狼人召回
  CMT_InflateRedPacket = 63; //膨胀爆红包
  CMT_RestaurantThemedSeal = 64; // 餐厅主题活动-印章信息
  CMT_SystemStarP = 65; // 啾灵房间系统消息
  CMT_SystemStarPGuild = 66; // 啾灵公会系统消息
  CMT_StarPCard = 67;    // sp卡片（房间卡片，宗门卡片等）
  CMT_AINPCImage = 68;  // AINPC作业帮图片
}

enum GroupChatModifyType {// 这个可以整合成一个
  GCMT_Uknown = 0;
  GCMT_ADD = 1;  // 增加人数
  GCMT_DEL = 2;  // 删除人数
  GCMT_Init = 3; // 群聊的初始化
}

enum QQSyncStatusType {
  SYNC_HIDDEN = 0;     // 不显示
  SYNC_NOT_SYNCED = 1; // 未同步
  SYNC_SYNCED = 2;     // 已同步
}

enum ChatP2PStatus {
  ST_NA = 0;            // 未定义
  ST_SENT = 1;          // 发送
  ST_RECV = 2;          // 收取
  ST_ESTABLISHED = 3;   // 建立
  ST_CLOSE = 4;         // 主动结束
  ST_CLOSED = 5;        // 被动结束
}

message ChatMsgData {
  required int64 from_id = 1;
  optional int64 seasion_id = 2; // 舍弃
  optional int32 side_id = 3; // 仅battle场景有效
  optional ChatMsgType msg_type = 4;
  optional string text = 5;
  optional string richText = 6; // 富文本
  optional ChatTeamRecruitMsg teamRecruit = 7; // 组队招募
  optional ChatTeamJoinMsg teamJoin = 8; // 加入/离开房间
  optional ChatSceneJoinMsg sceneJoin = 9; // 加入/离开场景
  optional KVArray kvArray = 100; // 通用kv结构 供客户端特殊消息类型传参使用
  optional int32 safetyCheckPassFlag = 101; // 安全检查通过标记
  optional int32 contentFiltered = 102; // 内容过滤标记
  optional int32 qqSyncStatus = 103; // QQ同步状态
  optional string qqSyncType = 104; // QQ同步类型：0_0, 0_1, 1_0, 1_1，分别表示自己和对方的消息同步开关状态
  repeated int64 remindUids = 201; // 被@的玩家，长度为1，且uid为0，就是所有人
  optional string textFormat = 10; // 文案格式
  repeated string parmas = 11; // 参数
  optional JoinLeaveClubChatMsg joinLeaveClubChatMsg = 12; // 加入/离开社团
  optional ChatClubGroupInviteMsg clubGroupInviteMsg = 13; // 邀请加入社团群聊
  optional TradingCardTradChatMsg tradingCardTradChatMsg = 14; // 集卡交易的数据
  optional int64 expireEpochSecs = 15; // 消息失效时间戳秒 (生日系统中:到生日当天)
  optional BirthdayCardChatData birthdayCardChatData = 16; // 生日贺卡数据
  optional ChatMessageShareGiftDisplayInfo shareGiftDisplayInfo = 17; // 分享礼包数据
  optional proto_StarPCardInfo starPCardInfo = 300; // SP卡片信息
}

message ChatMessageShareGiftDisplayInfo {
  optional int64 id = 1;                                          // 分享礼包唯一id
  optional int32 type = 2;                                        // 分享礼包类别 - 废弃
  optional int64 expireTimeMs = 3;                                // 过期时间
  optional int32 rewardLimit = 4;                                 // 领取上限
  optional int32 rewardedCnt = 5;                                 // 已领取计数
  optional int32 itemId = 6;                                      // 对应的道具id
  optional int32 status = 7;                                      // 状态 参考GiftShareStatusType的取值
}

enum BirthdayCardSendType {
  BirthdayCardSendTypeNow = 1;              // 立即送出
  BirthdayCardSendTypeThatDay = 2;          // 当天送出
}

// 生日贺卡聊天数据
message BirthdayCardChatData {
  optional int32 sendType = 1;                      // 参考 BirthdayCardSendType
  optional int64 senderUid = 2;                     // 赠送者 uid
  optional int64 sendeeUid = 3;                     // 被赠者 uid
  optional int64 mailId = 4;                        // 关联的邮件id
  optional int32 birthdayMonthDay = 5;              // 被赠送者生日(生日月*100+生日日)
  optional BirthdayCardData birthdayCardData = 6;   // 贺卡数据
  optional string birthdayCardUniqueId = 7;         // 贺卡id
}

// 生日贺卡赠送数据
message BirthdayCardData {
  optional int32 birthdayCardConfigId = 1;          // 贺卡配置表id(本期就一个贺卡主题)
  optional int32 blessingConfigId = 2;              // 祝福语配置表id
  optional string customContent = 3;                // 自定义祝福语
  optional int64 cardEpochMillis = 4;               // 贺卡实际时间戳毫秒("到期送"的贺卡, 会跟实际时间不一样)
}

message TradingCardTradChatMsg{
  optional int64 tradingCardTradeId = 1; //卡牌交易ID
  optional TradingCardTradeInfo tradingCardTradeInfo = 2; //卡牌交易的详细数据,这个数据只有在客户端拉取聊天数据的时候才会填充
}

message LaunchOrFinishGiftCard {
  optional int32 count = 1;                 //发起赠送或交换或索要的次数
  optional int32 TradeType = 2;             //交易类型 1.索要 2.赠送 3.交换
  optional int32 ShareChannel = 3;          // 1.私聊 2.卡牌频道 3.QQ 4.微信
}


message ChatGroupUser {
  required int64 uid = 1;
  required uint64 JoinTimeSeqId = 2;
  optional bool hasNormalMsg = 3; // 是否有正常消息
}

message ChatGroupUserList {
  repeated ChatGroupUser users = 1;
}

message ChatGroupKey {
  optional ChatType type = 1;
  optional int64 id = 2;
  optional int64 subId = 3;
}

message RoomChatGroupKey {
  optional ChatGroupKey groupKey = 1;
}

message BattleChatGroupKey {
  optional ChatGroupKey globalGroupKey = 1;
  repeated BattleSideChatGroupKey sideGroupKeys = 2;
}

message BattleSideChatGroupKey {
  optional int32 side = 1;
  optional ChatGroupKey sideGroupKey = 2;
}

message ChatNotReadInfo {
  optional ChatGroupKey groupKey = 1;
  optional GeneralChatMsgArray notReadMsgList = 2; // 可能只是最新的一条消息，未读技术用字段notReadCnt
  optional int32 notReadCnt = 3; // 当前未读的数量
}

message GeneralChatMsg {
  required uint64 seqId = 1;
  optional int64 senderUid = 2;
  optional uint64 sendTime = 3;
  optional ChatMsgData content = 4;
  optional PlayerColdData senderSnapshot = 5; // 玩家信息快照，用于外显
}

message GeneralChatMsgArray {
  repeated GeneralChatMsg chatMsgs = 1;
}

message ChatAidInfo {
  optional int32 isAll = 1; // 是否是@全部玩家
  repeated int64 remindUids = 2; // 被@的玩家
}

message ChatTeamRecruitMsg {
  optional int64 roomId = 1; // 房间Id
  optional int64 modeId = 2; // 模式Id
  optional string displayText = 3; // 外显文案，可能是富文本
  optional int32 curNumber = 4; // 当前房间内人数
  optional int32 targetNumber = 5; // 房间总人数
}

message ChatTeamJoinMsg {
  optional int64 roomId = 1; // 房间Id
  optional string text = 2; // 文本
  repeated PlayerColdData joinMember = 3; // 加入的成员
  optional string textFormat = 4; // 文案格式
  optional int32 ChatSystemMsgConfEnumVal = 5; // 系统消息类型
  repeated string parmas = 6; // 参数
}

message ChatSceneJoinMsg {
  optional int64 sceneId = 1; // 场景Id
  optional string text = 2; // 文本
  repeated PlayerColdData joinMember = 3; // 加入的成员
  optional string textFormat = 4; // 文案格式
  repeated string parmas = 5; // 参数
}

message JoinLeaveClubChatMsg {
  optional int64 clubId = 1;              // 社团ID
  optional string text = 2;               // 文本
  repeated PlayerColdData joinMember = 3; // 成员列表
  optional string textFormat = 4;         // 文案格式
  repeated string params = 5;             // 参数列表
}

message ChatClubGroupInviteMsg {
  optional int64 clubId = 1;              // 社团ID（客户端上报）
  optional string clubName = 2;           // 社团名称
  optional string msdkId = 3;             // 群组ID
  optional ClubMSDKGroupType groupType = 4;   // 群组类型
  optional string msdkAreaId = 5;         // MSDK
  optional string msdkZoneId = 6;         // MSDK
}

message SingleMsgSendTask {
  optional ChatMsgData msg = 1;
  optional int64 receiverUid = 2; // 私聊填玩家uid
  optional ChatGroupKey key = 3; // 频道填key
}

message AIInfo {
  // optional string name = 1;// 玩家当前的名字
  // optional string face = 2;// 头像

  // optional string skin = 3;// 皮肤描述
  // optional int32 modNumber = 4;// 比赛场次
  // optional int32 successNumber = 5;// 达标次数
  // optional int32 customNumber = 6;// 动物服数

  // optional string avatarInfo = 7;// avatar信息
  // optional int32 gender = 8;// 性别
  optional int32 difficulty = 9;  //机器人难度
  // optional int32 qualifyingIntegral = 10; //段位积分
  // optional int32 qualifying = 11; //段位
  // optional int32 degree = 12; //小段位
  // optional int32 wingId = 13;        //翅膀
  // optional int32 handLight = 14;        //手光
  optional int64 id = 15;     // ai信息配置表ID
  // repeated int32 dressUpItems = 16; // 穿戴时装信息
  // optional int32 fashionValue = 17; // 时尚分
  optional int32 source = 18; // 机器人来源，参考RobotSourceType的取值
}

enum RobotSourceType {
  RST_MatchFill = 0; // 匹配填充
  RST_PlayerAdd = 1; // 玩家添加
  RST_AutoInvitationTrigger = 2; // 自动邀请触发器
}

message IdValueInfo {
  required string id = 1;
  required string value = 2;
  repeated string values = 3;
}

message BaseBattleFrameRobotInfo {
  optional int32 robotLevel = 1;
  optional int32 baseRandScore = 2;
  optional int32 baseMinSocre = 3;
  optional int32 frameAveNodeNum = 4;
  optional int32 tagNodeNum = 5;
  optional int64 currentNodeNum = 6;
  optional int32 maxComboScore = 7;
  optional int32 missMaxNum = 8;
}

message BaseBattleFrameInfo {
  optional bytes lastBattleData = 1;
  optional int32 state = 2;
  optional int32 maxComboScore = 3;
  optional int32 missNum = 4;
  optional int32 reportCnt = 5;
  optional int32 toBreakRecord = 6;
  optional BaseBattleFrameRobotInfo robotInfo = 7;
  optional ExpressionInfo expInfo = 8;  // 表情信息
  optional int32 emojiCnt = 9; // 局内表情计数
}

message StreamStateInfo {
  optional bool isStreamOn = 1; // 是否开播
  optional string streamToken = 2; // 开播的token
  optional int32 streamPlatTypeVal = 3; // 开播平台枚举值 参考StreamPlatType的取值
}

message MatchIsolateInfo {
  optional int64 time = 1;
  optional int32 aiLevel = 2;
  optional int32 type = 3;
}

message PlayerGrayTagInfo {
  optional com.tencent.wea.xlsRes.PlayerGrayTagType tagType = 1; //tag类型
  optional string tagArg = 2; //tag类型对应的参数，比如匹配，可以填争对哪种排位做跨平台匹配
}

message GamePlayBrief {
  optional int32 featureType = 1;           // 玩法类型
  optional int64 pakVersion = 2;            // 玩法pak版本本
}

message LobbyMatchAnswerInfo {
  optional int32 id = 1;           // 玩法类型
  optional int32 answers = 2;            // 玩法pak版本本
}

message LobbyMatchInfo {
  repeated LobbyMatchAnswerInfo answers = 1;   // 答题答案
  optional int32 frequentlyModeID = 2;      // 常玩玩法ID
  optional int32 onlineTimePeriod = 3;      // 在线时间段
}

message AiLabWRBottomLineInfo {
  optional int32 difficulty = 1;
  optional int32 scriptId = 2;
  optional int32 difficultyPriority = 3;
  optional int32 scriptIdPriority = 4;
  optional int32 humanCnt = 5;
  optional int32 warm_rank = 6;   //透传信息
  optional int32 warm_rate = 7;   //透传信息
}

message CompetitionData {
//  repeated  int32 mapIDs = 1; //玩过的地图
//  repeated  int32 vocationIDs = 2; //玩过的职业
  optional  int32 round = 3; //场次
  optional  int32 score = 4; //积分
  optional  int32 rank = 5; // 排名
  map<int32, int32> levelIDMap = 6; // 玩过的
  map<int32, int32> vocationIDMap = 7; // 玩过的
  map<int32, int32> sideIDMap = 8; //玩过的
  repeated int32 vocationIDs = 9; //候选的3个身份
//  optional  int32 sideID = 10; // 确认的阵营
}

// MemberBaseInfo中 chase玩法特有的数据
message ChaseMemberBaseInfoData {
  map<int32, IntMapData> matchType2Side2BattleCnt = 1;    // 玩法模式id -> {阵营id -> 战斗次数}
}

message AiLabChaseWRBottomLineInfo {
  optional int32 campID = 1;
  optional AiLabWRBottomLineInfo aiLabWRBottomLineInfo = 2;
}

message MemberBaseInfo {// 战场中的不可变，房间中的可以更改 // 新增字段要同步到其他服的话, 得在 BattleInfo.getMemberBaseInfoList 中手动填充
  optional int64 uid = 1;
  optional bool isRobot = 2;
  optional int32 side = 3;//阵营id
  optional string face = 4;// 头像id
  // optional string skin = 5;// 皮肤描述，战场中一般不可变换，room中可以变换？
  optional string name = 6;// 玩家当前的名字
  optional int64 joinTime = 7;// 加入房间时的时间戳，加入战场时的时间戳
  optional int32 status = 8;// 房间中的状态
  optional int64 RoomID = 9;
  repeated bytes battleRecords = 10;

  optional AIInfo aiInfo = 11;  //AI个人信息
  // repeated KeyValueInfo msgList = 12;

  optional int32 gender = 13;// 性别
  // optional string avatarInfo = 14;// avatar信息
  // optional int32 privilegeSwitch = 15;   //特权开关  0-隐藏按钮，1-不响应点击，2-响应点击
  optional int32 privilegeLevel = 16;  //特权等级  红钻等级: 0-代表未开通，1-开通低档，2-开通中档，3-开通高档

  optional int64 curStateStartTime = 17;// 当前状态开始时间
  optional string openId = 18;
  optional int32 platId = 19;  //0ios 1android
  optional string token = 20;   // for ds auth

  // optional BaseBattleFrameInfo battleFrameInfo = 21;

  repeated RuleDimInfo ruleDimInfo = 22;
  optional int64 clientVersion = 23;
  // optional bool isTeamBattle = 24; // 组队比赛 房间人数大于1 为true
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifyingInfo = 25;
  // repeated EquipItemInfo equipItems = 26;
  optional int64 scene_id = 28; // 所在场景
  optional int32 map_id = 29; // 所在地图

  optional bool quitCurBattle = 30; // 是否已退出当前战斗
  optional int32 levelGuideGroupId = 31;  // 新手引导ABTest组id
  optional int32 levelGuideCount = 32;  // 新手引导次数
  // optional bool benefitCard = 33; // 权益卡

  repeated int32 unLockGameModeSet = 34; // 解锁的模式
  // optional int32 currentMatchType = 35;
  optional com.tencent.wea.xlsRes.PlayerStateType state = 36;

  optional SquadDailyTaskInfo dailyTaskInfo = 37; // 小队每日任务(废弃)
  optional SquadTaskGroupInfo achievementTaskInfo = 38; // 小队成就任务(废弃)
  repeated int32 dressUpItems = 39; // 时装装扮信息
  optional int32 fashionValue = 40; // 时尚分

  optional MMRScoresInfo mmrScoresInfo = 41;  // mmr数据
  optional WarmRoundInfo warmRoundInfo = 42;  // 温暖局数据

  optional int32 roomPosition = 43; // 1-32, 多人房间位置序号 (24.2.26新增: 也可能表示观战位置 需要看roomRoleType)
  optional int64 matchID = 44;
  optional int32 robotType = 45;   // 机器人类型 1 ai训练机器人 2 普通机器人
  optional int32 joinReason = 46; // 加入的原因，room->RoomJoinType

  optional HeadFrame headFrame = 47; // 头像框
  optional int32 battleRank = 48; // 对局结算显示用(废弃)
  optional int32 battleScore = 49; // 对局结算显示用(废弃)
  optional int32 battleIndex = 50; // 对局结算显示用(废弃)

  optional NamePlate namePlate = 51; // 铭牌
  optional int32 matchRuleId = 52;
  optional IDCNetworkInfo idcNetworkInfo = 53;      // 网络延迟信息
  optional int32 loginCountryCode = 54;  // 登陆的区域
  optional int32 aiType = 55; // 机器人生成类型: 0 普通机器人 1 队友机器人 2 超时机器人
  optional bool mapDownloaded = 56; // 地图下载完成与否
  optional int32 level = 57; // 等级
  optional com.tencent.wea.xlsRes.TconndApiAccount accountType = 58; // accountType
  optional MatchRuleInfo ruleInfo = 59; // 玩家的玩法详细信息
  optional int64 registerTimeMs = 60;     // 玩家注册时间 ai lab匹配需要
  optional int64 loginTimeMs = 61;        // 玩家本次登陆时间 ai lab匹配需要
  optional StreamStateInfo streamStateInfo = 62; // 玩家开播信息
  repeated LevelBattleEvent battleEventData = 63; // 玩家在对局中的事件 结算显示用

  optional bool dsReplayRecord = 64;    // 是否开启局内录像
  optional CreatorAccountInfo creatorAccountInfo = 65;

  optional string qqTeamTaskId = 66; // qq群应用任务id
  optional bool isLadderBan = 67; // 排位是否被禁
  optional MatchIsolateInfo matchIsolateInfo = 68;  //玩家匹配隔离信息
  optional string userIp = 69; //玩家Ip
  optional string userProfile = 70; //玩家头像
  optional int32 wolfKillReputationScore = 71; // 狼人杀荣誉分
  optional int32 deviceType = 72;       // 设备类型 0 手机 1 模拟器 参考LoginDeviceType

  repeated int32 recentLevelIds = 73;     // 近期游玩的关卡(废弃)
  optional int32 matchScore = 74;
  optional com.tencent.wea.xlsRes.MatchGrade matchGrade = 75;
  optional int32 battleResult = 76;
  optional int64 matchTimeMs = 77;      // 玩家开始匹配的时间
  optional int64 sourceTeamId = 78;  // 来源teamId，仅组队加入时会填充

  optional bool returningPrivilege = 79;  // 回归特权
  optional bool returning = 80;      // 回归标识
  repeated BattleRecentLevel recentRounds = 81; // 近期游玩的关卡
  optional int64 battleStartTime = 82; // 对局开始时间(塔防可以中途进入,不能用对局创建的时间)
  optional int32 roomRoleType = 83;              // 房间内角色信息 参见MemberBaseInfoRoomRoleType
  repeated  int64 followPlayerUuids = 84;  // 当为导播角色的时候，导播可以关注某一些房间内玩家，这样进入时候初始化就观战该玩家（多个则随机取其中之一）
  optional com.tencent.wea.xlsRes.DeviceLevel deviceLevel = 85;  // 机型评级
  // 副玩法段位信息 区别于QualifyingDegreeInfo
  // key(参见QualifyType) -> val(QualifyingDegreeInfo) 映射信息
  map<int32, com.tencent.wea.xlsRes.QualifyingDegreeInfo> secondaryGameplayQualifyingInfos = 86;
  optional PlayerABTestInfo abTestInfo = 87; // 玩家命中的ab实验数据
  repeated PlayerGrayTagInfo grayTags = 88; //灰度标签列表
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo showQualifyingInfo = 89; // 副玩法展示段位
  repeated int64 clubIds = 90; // 社团信息
  optional PlayerStatusDetails statusDetails = 91; // 玩家详细状态细节
  optional RoomMemberClientInfo memberClientInfo = 92; //纯客户端数据，服务器负责存储和同步
  optional bool isMidJoin = 93;  // 是否中途加入
  optional BattlePlayerSummary summary = 94;      // 简要信息
  optional int32 vocationId = 95;      // 职业，一些副玩法，比如狼人杀需要从服务端设置玩家的职业
  map<int32, int32> playerReputationScoreInfo = 96; // 信誉分分数id->玩家信誉分
  map<int32, proto_DressUpDetailInfo> dressUpItemDetails = 97; // 时装装扮(详细)信息
  optional WereWolfSideIdentityInfo wereWolfSideIdentityInfo = 98; //狼人杀阵营身份信息
  optional int32 midJoinType = 99;  // 中途加入的类型 1:匹配,2:邀请 MidJoinBattleType
  optional int32 midJoinState = 100;  // 中途加入状态
  optional int64 midJoinStart = 101;  // 中途加入检查起始时间
  map<string, bool> wolfkillAdminWhiteList = 102; // 狼人杀白名单位置
  repeated string wolfkillAdminWhiteModuleIds = 103; // 狼人杀白名单配置
  optional bool hideProfileToFriend = 104; // 对好友隐藏个人信息
  optional bool hideProfileToStranger = 105; // 对陌生人隐藏个人信息
  repeated string whitelistModuleId = 106;  // 白名单标签
  optional int32 randomSlotId = 107; // 随机装扮ID
  optional WolfKillGameInfo wolfKillGameInfo = 108; // 狼人的其他信息
  optional int32 mobileGearLevel = 109; //手机档位 low = 1;//低 middle = 2;//中 high = 3;//高 super = 4;//高档
  optional int32 profileTheme = 110; // 主页背景主题
  optional int32 lobbyModeTypeSetting = 111; // 大厅类型设置
  optional bool curPlayUnlocked = 112; // 当前所选玩法是否已解锁
  repeated int32 bannedMatchTypeList = 113; // 已被封禁的玩法列表（0表示封禁所有玩法）
  optional int32 arenaHeadFrame = 114; // arena头像框
  optional BattleHistoryData lastBattleData = 115; // 上次对局的数据
  repeated int32 unLockWolfKillGameModeSet = 116; // 解锁的狼人模式
  optional int32 addInBattleProcess = 117;  // 进入局内的进度信息
  optional int32 teamShowBackgroundTheme = 118; // 组队秀背景图
  optional int64 battleEndTime = 119; // 对局结束时间
  optional CommunityChannelColdData communityChannelColdData = 120; //社区频道聊天室显示字段
  repeated StreamStateInfo streamStateInfoList = 121; // 玩家开播信息列表，兼容同时多个平台开播
  repeated int64 blackListTopN = 122; // 玩家黑名单列表 最新的N个
  optional int32 wolfKillActionScore = 123; // 狼人杀行为分
  repeated int32 reputationBehaviorIds = 124; // 本局所有违规行为id
  optional int32 arenaHeadPic = 125; // arena头像
  repeated KeyValueInt32 abtest = 126; // abtest相关信息
  optional int32 sideInGame = 127;  // 玩家局内阵营id
  optional int32 heatPowerRank = 128; // 热力值排名
  optional bool hideEntertainmentQualifyInfo = 129; // 隐藏娱乐玩法段位
  // 129后禁止添加一级基础数据类型字段，根据模块特性或者玩法类型做数据结构封装，有疑问咨询nichtsun
  optional LobbyMatchInfo LobbyMatchInfo = 130; //万松书院大厅匹配
  repeated int32 fashionScore = 131; // 潮流度分数

  optional MemberBattleSceneInfo battleSceneInfo = 150;  // 场景信息
  optional MemberBattleSceneFlow battleSceneFlow = 151;  // 场景流水

  // 201-300 聊天透传数据

  // 301-400 各玩法异化数据
  optional MainPlayUserData mainPlayData = 301; // 主玩法数据
  optional MayDayUserData mayDayData = 302; // mayday玩法数据
  optional UgcUserData ugcUserData = 303; // ugc玩法数据
  optional ArenaData arenaData = 304;//arena相关的数据
  optional CompetitionData competitionData = 305; // 赛事数据
  optional ChaseMemberBaseInfoData chaseUserData = 306;     // 大王别抓我数据

  // 401-500 对局依赖数据
  map<int32, proto_DressUpDetailInfo> battlePreparationDressUpItems = 401; // 备战道具装扮(详细)信息
  repeated int32 matchPakTypes = 402; // 当前玩法的分包类型列表，计算所得
  optional AIInvitationData aiInvitationData = 403; // ai邀请数据
  optional int32 pakDetailID = 404;  // 分包组id用于随机关卡

  // 1000+ 玩家基础特性
  repeated GamePlayBrief playInfo = 1001;    // 玩法分包信息
  repeated proto_PakDownloadInfo pakDownloadInfoList = 1002; // 分包下载信息
  optional PlayerClientInfo clientInfo = 1003; // 客户端信息
  optional AiLabWRBottomLineInfo aiLabWRBottomLineInfo = 1004; // aiLab投放的玩家兜底信息 用于计算一场对局的难度等
  repeated AiLabChaseWRBottomLineInfo aiLabChaseWRBottomLineInfo = 1005; // aiLab投放的玩家兜底信息 用于计算一场对局的难度等
  repeated proto_PakDownloadInfo detailPakDownloadInfoList = 1006; // 细节分包（大小包）下载信息

  // SP
  optional StarPTeamUserInfo starPInfo = 1020; // 啾灵角色数据
  optional int64 starPBanTime = 1021; // 啾灵匹配惩罚过期时间(服务器填充)
  optional int32 confirmStatus = 1022; // SP 确认状态 0=未确认 1:已确认
  optional StarPPvpUserInfo starPPvpUserInfo = 1023; // 啾灵pvp数据
  optional int64 starPOccupyTime = 1024; // 啾灵招募预占位时间
  optional bool starPIsVisitor = 1025; // 啾灵是否为访客
}

message AIInvitationData {
  optional int64 updateTimestampMs = 1; // 更新时间戳
  optional int32 availableMatchCnt = 2; // 可游玩对局的次数
}

message MemberBattleSceneInfo {
  optional int32 sceneChangeStatus = 1;  // 场景切换状态
  optional int64 sceneId = 2;  // 当前场景
  optional int64 gameSessionId = 3;  // 当前GameSessionId
  optional int64 targetSceneId = 4;  // 目标场景
  optional int64 targetGameSessionId = 5;  // 目标GameSessionId
  optional KVArray kvArray = 6;  // 附加信息
}

message MemberBattleSceneFlow {
  map<int64, int64> scenePlayTime = 1;
  optional int64 lastSceneId = 2;
  optional int64 lastSceneJoinTime = 3;
}

message PlayerThroughGameSettings{
  repeated PlayerFashionScoreHideSetting fashionScoreHideSettings = 1;
}

message PlayerFashionScoreHideSetting{
  optional int32 matchType = 1;
  optional bool hide = 2;
}

message ArenaHeroStarData {
  optional int32 heroId = 1;  // 英雄ID，为0代表总勋章数
  optional int32 star = 2;    // 英雄勋章数据（英雄ID为0代表总勋章数）
  optional int32 level = 3;   // 英雄熟练等级（英雄ID为0时不适用）
}

message ArenaHeroInfo {
  optional int32 heroId = 1;
  optional proto_ArenaHeroCombatEffectivenessData ceData = 2; // 英雄战力数据
  map<int32, proto_ArenaHeroStatInfo> statData = 3;           // 英雄统计数据，matchType -> heroStat
}

// arena相关的数据
message ArenaData {
  optional bool isBigPackage = 1;//是否为大小包（3v3大小包的标记）  ture:大包
  optional bool userFightDirectFlag = 2;//是否防御塔攻击前几局弱引导 true:需要引导
  optional bool isMeetRandomEventCondition = 3;//是否满足开启随机事件条件
  repeated int32 randomEvent = 4;//触发的随机事件（战斗结束，battle 透传 到game 参数）
  optional string clientSetDSParam = 5;//客户端设置的参数（透传到DS），主要用于新皮肤局内的试用
  optional int32 newBeeTeamMatchFlag = 6;//新手组队温暖局的标记，0 非新手组队温暖局， 1 新手组队温暖局进入全真人局6101/6102， 2 新手组队温暖局进入5真人温暖局 66501/66502
  optional int32 newBeeSingleABTestFlag = 7;//单人新手优化，0 不做优化， 1 优化
  repeated ArenaHeroStarData arenaHeroStarData = 8; // 主目标系统数据
  optional int32 heroStarDisable = 9;               // 主目标系统是否禁用，0：开启 1：禁用
  map<int32, ArenaHeroInfo> heroInfo = 10;          // 英雄数据, heroId -> data
}

// 主玩法玩家数据
message MainPlayUserData {
  optional MainPlayBattlePreparationData preparationData = 1; // 备战数据
}

message MainPlayBattlePreparationData {

}

// mayday玩法数据
message MayDayUserData {
  optional MayDayEndlessModeSaveData endlessSaveData = 1; // 当前无尽模式存档数据
  optional int32 bestLevelRecord = 2; // 最高层数记录
}

// 无尽模式存档数据
message MayDayEndlessModeSaveData {
  //层数
  optional int32 level = 1;
  //团队资金
  optional int32 groupMoney = 2;
  //科技点数
  optional int32 techPoint = 3;
  //位置信息
  repeated MayDayEndlessModePosInfo posInfo = 4;
}

// 无尽模式角色位数据
message MayDayEndlessModePosInfo {
  //位置
  optional int32 pos = 1;
  //职业信息
  repeated MayDayEndlessModeCareerInfo IdentityInfo = 2;
  //技能书数量
  optional int32 bookCount = 3;
}

// 无尽模式职业信息
message MayDayEndlessModeCareerInfo {
  //职业ID
  optional int32 identityID = 1;
  //职业等级
  optional int32 level = 2;
}

// ugc玩法相关的数据
message UgcUserData {
  optional RoomMemberCoinBagInfo coinBagInfo = 1; // 聚会多轮匹配玩法的金币数据
  repeated RoomBattleSettlementInfo battleSettlementInfos = 2; // 聚会多轮对局结算信息
}

message RoomBattleSettlementInfo {
  optional int32 rankIndex = 1;                 // 名次，从1开始
  optional int32 score = 2;                     // 积分 -- 废弃
  optional MemberMultiRoundScoreSettlementInfo memberMultiRoundScoreSettlementInfo = 3; // 对局结算信息
}

message BattleHistoryData {
  optional int32 playId = 1; // 玩法id
  optional int64 playerUid = 2; // 玩家uid
  optional KVArray battleData = 3; // 对局数据，kv结构便于拓展
  optional int64 battleId = 4; // 对局id
}

message DressUpItemDetail {

}

message PlayerClientInfo {
  optional int32 loginPlat = 1; // App运行端平台：loginPlat 1模拟器、2QQ小游戏、3App、4微信小游戏、5先锋云、6QQVA、7PC平台
  optional int32 devicePlat = 2; // App包类型：devicePlatform 安卓1、IOS2、Windows3、Mac4
  optional int32 cloudGamePlat = 3; // 云游戏运行操作系统：cloudGamePlat 0IOS、1安卓、2Windows、3Mac
}

message PlayerStatusDetails {
  optional proto_PlayerStatusDetails statusDetails = 1; // 状态细节
  optional proto_PlayerPublicSceneData sceneData = 2; // 场景附加信息

}

message RoomMapVoteInfo {//房间选地图投票信息
  optional int64 uid = 1;     //玩家ID
  optional int64 mapId = 2;   //地图ID
}

message MapInfoForRoomMapVote {//投票用的地图信息
  optional int64 ugcId = 1;          //地图id
  optional int32 ugcType = 2;        // ugc类型，UGCMapType
  optional string name = 3;          //名字
  optional string tags = 4;          //tags
  repeated UgcMapMetaInfo metaInfo = 5;     //metaInfo列表，包含缩略图信息
  optional string editorName = 6;   //作者名字
  optional string editorProfile = 7; //作者头像
  optional string markString = 8;    //标志文本
  repeated PlayerDressItemInfo editorDressItemInfos = 9;
  optional string bucket = 10;        //bucket
  optional string region = 11;
}

message UgcMapShowInfo {//简要的地图显示信息，够用来展示就行
  optional int64 ugcId = 1;          //地图id
  optional int32 ugcType = 2;        // ugc类型，UGCMapType
  optional string name = 3;          //名字
  optional string tags = 4;          //tags
  repeated UgcMapMetaInfo metaInfo = 5;     //metaInfo列表，包含缩略图信息
  optional string editorName = 6;   //作者名字
  optional string editorProfile = 7; //作者头像
  optional string markString = 8;    //标志文本
  repeated PlayerDressItemInfo editorDressItemInfos = 9;
  optional string bucket = 10;        //bucket
  optional string region = 11;
  optional int64 likeCount = 12; //点赞数
  optional int64 playCount = 13; //游玩次数
}
message UgcRoomMapRecommendPlayerInfo {
  optional int64 uid = 1; //玩家UID
  optional int64 lastRecommendTime = 2; //推荐时间
}
message UgcRoomPlayerRecommendMapInfo {
  optional PublishItem ugcMapInfo = 1; //UGC地图信息
  map<int64, UgcRoomMapRecommendPlayerInfo> recommendPlayerInfo = 2; //玩家id到推荐玩家信息的map
  optional int64 lastRecommendTime = 3; //这个地图被玩家推荐的最新时间
  optional int64 lastRefreshTime = 4; //UGC地图信息上次刷新时间

}

message UgcRoomOfficalRecommendMapInfo {
  optional PublishItem ugcMapInfo = 1; //UGC地图信息
}
message UgcRoomOfficalRecommendMapIdInfo {
  optional int64 mapId = 1; //UGC地图信息
}
message UgcRoomOfficalRecommendTabInfo {
  optional string tab_id = 1; //子页签ID
  optional string tab_name = 2; //子页签名称
}
enum FashionScoreType {
  FST_None = 0;
  FST_WearFashionScore = 1;  // A×穿着分数
  FST_CurrentSeasonFashionScore = 2; // B×当前赛季时尚分
}

// 局内事件
message LevelBattleEvent {
  optional int32 level = 1; //关卡ID
  repeated KeyValueInt64 level_events = 2;
}

message HeadFrame {
  optional com.tencent.wea.xlsRes.ItemType dressUpType = 1;
  optional int64 itemUUID = 2;
  optional int32 itemId = 3;
}

message NamePlate {
  optional com.tencent.wea.xlsRes.ItemType dressUpType = 1;
  optional int64 itemUUID = 2;
  optional int32 itemId = 3;
}

message MMRScore {
  optional int32 id = 1;
  optional int32 score = 2;
}

message MMRScoresInfo {
  map<int32, MMRScore> scores = 1;
}

// 温暖回流信息
message WarmRoundReturningInfo {
  //回流天数
  optional int32 returningDays = 1;
  //当前温暖局总分数
  optional int32 warmRoundTimes = 2;
  // 实验分组id 用于判断不同玩法是否启用温暖局
  optional int32 abTestGrpId = 3;
}

message WarmRoundScore {
  optional int32 id = 1;
  optional int32 score = 2;
}

message WarmRoundInfo {
  map<int32, WarmRoundScore> scores = 1;
  optional WarmRoundReturningInfo returningInfo = 2;  // 温暖回流信息
}

// 单个延迟信息
message IDCNetworkRecord {
  optional int64 idcId = 1;       // idc id
  optional int32 rttMs = 2;       // 玩家探测的延迟rtt Ms
}

// 对各个IDC的延迟信息
message IDCNetworkInfo {
  repeated IDCNetworkRecord records = 1;
}

message IDCLoadStatus {
  optional int64 idcId = 1;                      // idc id
  optional int64 lobbyCnt = 2;                   // 大厅ds数量
  optional int64 battleCnt = 3;                  // 战场ds数量
  optional int64 lobbyHumanPlayerCnt = 4;        // 大厅玩家数量
  optional int64 battleHumanPlayerCnt = 5;       // 战场玩家数量
  optional int64 battleAiPlayerCnt = 6;          // 战场Ai玩家数量
}

// IDC Ping服务器信息
message IDCPingSvrInfo {
  optional int64 idcId = 1;
  optional string addr = 2;   // 地址
  optional int32 port = 3;    // 测速端口
}

message EquipItemInfo {
  optional com.tencent.wea.xlsRes.ItemType itemType = 1;
  repeated KeyValueInt32 items = 2; //pos => itemId
}

// 通用房间结构定义

message MemberInfoList {
  repeated MemberBaseInfo memberInfo = 1;       // 普通位置的玩家信息
}

// 房间历史对局数据
message RoomTeamBattleBroadcastInfo {
  optional int32 conWinCount = 1; // 连胜场数
  repeated int64 finishedBattle = 2; // 记录结算过的battle
  optional int32 conCount = 3; // 连续开黑
  // 对局轮次信息
  optional int32 roundIndex = 11; // 对局轮次，从0开始
}

enum InvitationType {
  IT_UnKnown = 0;
  IT_Accept = 1;      // 同意加入，game调用join
  IT_RefusedTo = 2;   // 拒绝加入, game调ss
  IT_WantBeInvited = 3; // 希望被邀请
}

enum ModifyType {
  MT_UnKnown = 0;
  MT_ADD = 1; // 增加
  MT_DEL = 2; // 减少
  MT_Init = 3;// 初始化
  MT_Update = 4;// 状态更新
  MT_LeaderChange = 5; // 队长切换
  MT_Disband = 6; // 队伍解散
  MT_Kick = 7; // 被踢
  MT_SystemKick = 8; // 系统逻辑踢出
}

enum TeamType {
  UnKnownRoom = 0;
  TeamRoom = 1;// 组队房间
  SingleRoom = 2;//单人房间
}

enum RoomType {
  DefaultRoom = 0;  // 默认组队房间，下属仅一个队伍
  CustomRoom = 1;   // 自定义大房间，下属有多个队伍
  VirtualRoom = 2;  // 单人虚拟房间
  UgcCustomRoom = 3;// ugc多人大房间，下属仅一个队伍
  CompPrelimRoom = 4; // 赛事-海选虚拟房间
  CompElimRoom = 5; // 赛事-淘汰赛/巅峰赛多人房间
  CompPointsRoom = 6;// 赛事-积分赛虚拟房间
  FeatureCompetitionRoom = 7; // 副玩法赛事房间

  // 匹配单元
  CoMatchSideUnit = 101; // 同阵营匹配单元
}


enum RoomRecommendListSource {
  RRLS_Unknown = 0;
  RRLS_UgcMapDetail = 1;  // UGC-地图详情页面-房间列表
  RRLS_UgcStarWorld = 2;  // UGC-星世界-一起来玩-房间列表
}

message RoomState {
  optional int32 stateVal = 1; // 状态数值，兼容多数枚举
  optional int64 startTs = 2;  // 状态开始时间，单位ms
}

message RoomPlayerStateInfo {
  optional int64 roomId = 1; //
  optional int64 playerUid = 2; //
  optional com.tencent.wea.xlsRes.RoomStateType state = 3; // 当前房间状态
  optional com.tencent.wea.xlsRes.PlayerStateType curPlayerState = 4; // 当前玩家状态
}

// 外显信息，标题，主题等
message RoomDisplayInfo {
  optional string title = 1; // 标题
  optional int32 topicId = 2; // 主题id
  optional int64 roomNo = 3; // 房间号
  optional int32 backgroundTheme = 4; // 背景主题-关联主页背景主题
}

// 多图匹配信息
message UgcMatchCompilations {
  optional bool ugcIsCompilations = 1;
  repeated UgcMatchLobbyMapWithWeight matchLobbyMaps = 2; // ugc匹配用地图集
  repeated int32 campInfo = 9;  // 阵营信息 如果是多阵营的这里便有阵营信息
  optional string compName = 3;  // 合集名
  optional int64 mapPoolId = 4;  // 地图池id
  optional int32 pointNumber = 5;
  optional bool openRound = 6;  // 是否开启轮次
  optional int32 roundTimes = 7;  // 轮次次数
  optional int32 minPlayer = 8;  // 最小人数
}

message RoomUgcMatchExtraInfo {
  optional int64 ugcId = 1;
  optional int64 ugcCfgId = 2;
  optional int32 matchTypeId = 3;
}

message RoomUgcInfo {
  // 当前房间地图数据
  optional int64 mapId = 1;
  optional int64 pass = 2;  // 密码 废弃
  optional bool hasPwd = 3; // 是否启用密码 废弃
  optional UgcBriefInfo ugcBriefInfo = 4; // 地图相关数据
  optional int32 mapSource = 5;
  repeated UgcMapTopic topics = 6; // 废弃
  optional string fromCollectionId = 12;
  optional int64 mapVersion = 14; // 地图版本

  // 玩法补充数据
  // -UGC结伴同游
  repeated MapInfoForRoomMapVote mapInfosForRoomMapVote = 7; // UGC结伴同游，投票用的地图信息
  repeated RoomMapVoteInfo voteInfos = 8; // UGC结伴同游，投票信息
  optional int64 firstUserVoteTime = 9; // UGC结伴同游，首次投票玩家的时间
  optional AlgoInfo mapRecommendAlgoInfo = 10; // UGC结伴同游，推荐地图的algoInfo
  repeated int64 historyPlayUgcList = 11; // UGC结伴同游，历史游玩过的地图
  optional UgcMatchCompilations ugcMatchCompilations = 13; // UGC结伴同游，多图匹配信息
  // -UGC匹配
  optional RoomUgcMatchExtraInfo ugcMatchExtraInfo = 15;
  // -UGC聚会玩法，匹配后多轮次对局
  repeated UgcBriefInfo multiRoundMaps = 16; // 多轮次地图列表
  optional int32 minPlayerLimit = 17; // 最小游玩人数
  optional int32 curRoundIndex = 18; // 当前轮次
  map<int64, UgcRoomPlayerRecommendMapInfo> ugcRoomPlayerRecommendMapInfos = 19; //UGC房间成员推荐的地图列表
}

// room相关流水字段
message RoomTFlowInfo {
  optional ArrayLong roomMemberUidArray = 1; // 当前成员玩家uid
  optional int32 offlineMemberCnt = 2; // 暂离玩家数量
}

message RoomBriefInfo {
  optional int64 roomID = 1; // 房间Id
  optional RoomType roomType = 2; // 房间类型
  optional int32 modeID = 3; // 模式Id
  optional int32 playID = 4; // 玩法Id
  optional string title = 5; // 房间标题
  optional int32 memberNumberLimit = 6; // 房间人数限制
  optional int32 curMemberNumber = 7; // 当前人数
  optional int32 topicID = 8; // 主题Id
  optional string topicDisplayContent = 9; // 主题外显内容
  optional int64 leaderUid = 10; // 房主Uid
  optional int64 roomNo = 11; // 房间短号
  optional int32 robotLevel = 12; // 机器人等级
  optional bool hasPass = 13; // 是否有密码
  optional int32 battleVersionGroup = 14; // 房间的对局兼容组，不同不允许加入队伍
  repeated RoomLevelSetting levelSetting = 15; // 关卡设置
  optional int32 targetIdcZoneId = 16; // 当前目标idcZoneId (不代表最终)
  optional MatchRuleInfo ruleInfo = 17; // 完整的玩法设置信息
  optional int32 curObserverNumber = 18;      // 当前观战人数
  optional int32 observerNumberLimit = 19;    // 观战人数上限
  optional int32 backgroundTheme = 20; // 背景主题-与玩家主页背景主题相同 -- 废弃
  repeated int32 aiTypes = 21; // 队伍中包含的ai类型列表，考虑可能同时存在多种ai逻辑
  optional MemberQualifyInfo roomQualifyInfo = 22; // 成员段位信息
}

message MemberQualifyInfo {
  optional int32 avgQualifyVal = 1; // 平均段位分
  optional int32 avgQualifyTypeInt = 2; // 平均大段位类型的值
}

message RoomPublicInfo {
  optional RoomPublicType publicType = 1; // 对外公开类型
  repeated int64 publicParams = 2; // 对应公开类型的附加参数
  optional RoomRecommendSourceType recType = 3; // 返回给玩家的推荐理由，供统计使用
  optional int64 publicTimestamp = 4; // 公开时间
  optional MemberBaseInfo leader = 5; // 房主
  optional bool isAnyMemberInReturnPrivilege = 6; // 是否有玩家处于回流状态
  optional bool isInBattle = 7;  // 是否局内
}

message RoomRecruitInfo {
  optional RoomPublicType publicType = 1; // 对外公开类型
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifyingInfoLowLimit = 2; // 最低的段位限制
  optional int64 publishTimestamp = 3; // 发布时间
  optional int32 modeId = 4; // 发布时选择modeId
  optional int32 playId = 5; // 发布时选择的playId
  optional MemberBaseInfo publisher = 6; // 发布者
  optional bool isAnyMemberInReturnPrivilege = 7; // 是否有玩家处于回流状态
  optional int32 recruitEntryType = 8; // 招募实体类型
  optional int32 modeType = 9; // 招募一级筛选类型
  optional int32 categoryType = 10; // 招募玩法类别
  optional int32 roomRecruitVersion = 11; // 招募信息版本
}

message RoomCoMatchInfo {
  optional int64 coMatchId = 1;
  repeated int64 coMatchPlayerList = 2;
  optional int64 timestampMs = 3;
}

message RoomCreateOptions {
  optional string title = 1; // 废弃
  optional int32 matchTypeId = 2; // 废弃
  optional int32 maxMemberLimit = 3; // 废弃
  optional string passwordStr = 4; // 废弃
  optional int32 robotLevel = 5;
  optional bool needRoomNo = 6;
  repeated RoomLevelSetting levelSettings = 7;
  optional RoomGeoInfo roomGeoInfo = 8; // 面对面短号信息
  optional RoomCoMatchInfo coMatchInfo = 9; // coMatch信息
  // ugc相关逻辑
  optional int64 mapId = 21; // 地图id
  optional int32 mapSource = 22; // ugc地图来自客户端的哪个标签
  optional int64 creatorId = 23; // 创建房间玩家的creatorId,只有ugc测试房间会设置
  optional int64 ugcLayerId = 24; // ugc图层Id，用来锁定图层，ugc测试房间需要设置这个值
  optional RoomSetting roomSetting = 30; // 整体的房间设置
  // 透传字段
  optional string sourceModule = 31;
  optional string activityId = 32;
  optional string themeName = 33;
  optional string fromCollectionId = 34;
  optional int32 logicMapSource = 35;
  optional string SubTabName = 36;
  // 操作类型
  optional bool preCreate = 101; // 预创建
  optional bool createWithMultiMember = 102; // 多成员创建

  // 赛事-淘汰赛房间信息
  optional CompetitionElimRoomInfo compElimRoomInfo = 201;
}

message RoomSetting {
  optional RoomRobotSetting robotSetting = 1; // 机器人设置
  optional RoomMapSetting mapSetting = 2; // 玩法地图设置
  repeated RoomSpecialSetting specialSettings = 3; // 特殊设置
  optional bool autoStartWhenFull = 4; //人满自动开始
  optional bool privateRoom = 5; //只有邀请才能加入房间
  optional bool singleRoom = 6;    // 单机开房间直接开局的房间
}

message RoomRobotSetting {
  optional bool hasRobot = 1; // 是否填充机器人
  optional int32 robotLevel = 2; // 机器人难度等级
  optional int32 number = 3; // 机器人数量
}

message RoomMapSetting {
  optional int32 mapType = 1;
  optional int64 mapId = 2;
  repeated RoomLevelSetting levelSettings = 3; // 关卡设置
}

message RoomSpecialSetting {
  optional int32 type = 1; // 设置类型 RoomSpecialSettingType
  optional KVArray kvArray = 2; // 设置kv项
}

// 公开对外的数据结构，兼容各种房间类型的通用结构
message UniversalRoom {
  optional RoomBriefInfo roomBriefInfo = 1;       // 房间摘要信息
  optional RoomUgcInfo roomUgcInfo = 2;           // 房间ugc信息
  optional RoomPublicInfo roomPublicInfo = 3;     // 房间对外公开的信息
  optional RoomRecruitInfo roomRecruitInfo = 4;   // 房间对外招募的信息
  optional CompetitionElimRoomInfo compElimRoomInfo = 5; // 赛事-淘汰赛房间信息
  optional RoomSetting roomSettingInfo = 6; // 设置相关信息
  optional RoomTFlowInfo roomTFlowInfo = 7; // 房间tlog信息
}

message UniversalRoomArray {
  repeated UniversalRoom rooms = 1;
}

message RoomRoundInfo {
  optional int32 roundIndex = 1; // 当前轮次序号
  optional int64 nextRoundStartTime = 2; // 当前轮次预计开始时间
  optional RoomRoundExtraInfo roomRoundExtraInfo = 3; // 当前轮次额外信息
}

message RoomRoundExtraInfo {
  optional MatchRuleInfo ruleInfo = 1; // 玩法信息
  oneof data {
    RoomUgcMultiRoundScoreInfo roomUgcMultiRoundScoreInfo = 2; // ugc多轮地图积分数据
  }
}

message RoomUgcMultiRoundScoreInfo {
  repeated MemberBaseInfo memberList = 1; // 玩家基础信息
  repeated RoomMemberCoinBagInfo memberCoinBagList = 2; // 玩家金币信息
  optional int64 curUgcId = 3; // 当前轮次地图id
}

message RoomMemberCoinBagInfo {
  optional int64 uid = 1; // 玩家uid
  optional int32 coinNum = 2; // 玩家金币数量
  optional int32 coinUsed = 3; // 玩家金币使用的数量
}

message RoomBroadcastInfo {
  optional RoomBroadcastInfoType type = 1;
  oneof data {
    RoomPlayerMapDownloadProcessInfo mapDataDownloadProcessInfo = 2; // 地图下载进度信息
    RoomMapDownloadRemindInfo mapDownloadRemindInfo = 3; // 地图下载提示信息
    RoomPlayerReadyRemindInfo playerReadyRemindInfo = 4; // 玩家准备提醒信息
    RoomMapStateSyncInfo mapStateSyncInfo = 5;  // 数据同步信息
    PlayerOperationStatusData playerOperationStatusData = 6; // 玩家当前操作状态广播数据
    RoomPlayerClientInfoList roomPlayerClientInfoList = 7; //房间部分玩家纯客户端维护的属性同步
    RoomMiniGameInfo roomMiniGameInfo = 8; // 房间内的小游戏邀请信息
    RoomCommonBroadcastInfo commonInfo = 9; // 通用广播信息
    RoomSPBroadcastData roomSPBroadcastData = 10; //SP队伍广播数据
    RoomDetailPakProcessData roomDetailPakProcessData = 11; // 细节分包（大小包）相关广播数据
  }
}

message RoomDetailPakProcessData {
  optional int32 type = 1; // 0:进度数据 1:提醒下载
  optional int32 playId = 2; // 当前玩法id，切换玩法导致队伍信息变更能够有数据做检查
  //repeated RoomPlayerMapDownloadProcessInfo processInfos = 3; // 下载进度数据，type=0时有效
  optional RoomPlayerMapDownloadProcessInfo processInfo = 4; // 下载进度数据，type=0时有效
}

// SP队伍 广播2级类型
enum RoomSPBroadcastSubType {
  RSPBST_Unknown = 0;
  RSPBST_ItemGet = 1; // SP 物品获得广播
}

message RoomSPBroadcastData {
  optional RoomSPBroadcastSubType type = 1;
  optional int64  uid = 2; // 发起广播玩家
  optional bool   broadcastSelf = 3; // 是否需要广播自己
  repeated string params = 4; // 动态数据,客户端自行根据type类型处理,服务器进进行广播
  optional RoomCommonBroadcastInfo commonInfo = 5; // 客户端可根据业务需求 使用4(params) or 5(commonInfo) 填充数据
  // oneof data {
  // }
}

message RoomPlayerClientInfoList {
  repeated RoomPlayerClientInfo infoList = 1;
}
message RoomPlayerMapDownloadProcessInfo {
  repeated PlayerMapDownloadProcessInfo playerProcessInfoList = 1; // 多个玩家的下载进度汇总数据
  optional int64 mapId = 2; // 当次下载内容的唯一id
}

message RoomMapDownloadRemindInfo {
  optional int64 mapId = 1;
}

message RoomPlayerReadyRemindInfo {
  optional int64 roomId = 1;
  optional int64 remindStartTimeMs = 2; // 提醒开始时间 单位ms
  optional RoomReadyReminderType type = 3; // 提醒类型
}

message PlayerMapStateSyncInfo {
  optional int64 playerUid = 1;
  optional int32 type = 2;
  optional int32 process = 3;
  optional int64 updateTimestamp = 4;
}

message PlayerOperationStatusData {
  optional int64 uid = 1;
  optional int32 status = 2;
}


message RoomMapStateSyncInfo {
  optional int64 mapId = 1;
  repeated PlayerMapStateSyncInfo playerMapStateSyncInfo = 2;
}

message PlayerMapDownloadProcessInfo {
  optional int64 playerUid = 1;
  optional ProcessInfo mapDataDownloadProcessInfo = 2;
}

message PlatMiniGameNoticeInfo {
  optional uint32 playId = 1;     // 玩法id
  optional string appid = 2;         // 小游戏appid
  optional string extData = 3;      // 邀请透传字段
}

message RoomMiniGameInfo {
  optional int64 proposal = 1; // 提出者
  optional PlatMiniGameNoticeInfo noticeInfo = 2; // 小游戏信息
}

message RoomCommonBroadcastInfo {
  optional int64 proposal = 1; // 提出者
  optional KVArray kvData = 2; // 附加信息
}

// 房间招募
message RoomRecruit {
  optional RoomBriefInfo roomBriefInfo = 1; // 房间Id
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifyingInfoLowLimit = 2; // 最低的段位限制
  optional PlayerColdData publisher = 3; // 发布者信息
  optional int64 publishTimestamp = 4; // 发布时间
  optional RoomUgcInfo roomUgcInfo = 5; // ugc信息
  optional bool isAnyMemberInReturnPrivilege = 6; // 是否有玩家处于回流状态
  optional int32 recruitEntryType = 7; // 招募实体类型
  optional RoomRecruitExtraInfo roomRecruitExtraInfo = 8; // 招募额外信息
}

message RoomRecruitExtraInfo {
  optional int32 modeType = 1; // 招募时的模式选择
  optional int32 categoryType = 2; // 招募时的玩法类型选择
}

message RoomRecruitArray {
  repeated RoomRecruit recruits = 1;
}

message UgcRoomInfo {
  optional RoomBriefInfo roomBriefInfo = 1;
  optional RoomUgcInfo roomUgcInfo = 2;
}

message UgcRoomArray {
  repeated UgcRoomInfo ugcRooms = 1;
}

message BattleResultInfo {
  required string key = 1; // key
  required string value = 2; // value
}

message MidJoinStatus {
  optional bool readyMidJoin = 1;  // 知否可以准备中途加入了
  optional int32 changeReason = 2;  // 改变状态的原因
}

message BattleSceneExtraInfo {
  optional int64 sceneId = 1;  // 场景的id
  optional int32 sceneType = 2;  // 场景类型
}

message BattleExtraInfo {
  optional string dsAddr = 1; // ds地址
  optional string dsLevelSeq = 2; //
  optional int64 dsSessionId = 12;  //ds实例id
  optional int64 dsaInstanceID = 3; // dsa实例id
  optional string dsRandomLevelGroupIds = 4; //
  optional int64 dsIdcId = 5; // 多idc时 ds所在idc id
  optional string dsLevelDuration = 6;
  optional string dsLevelSurvivalRate = 7;
  optional int64 dsUgcDuration = 8; // ugc对局生命周期
  optional int64 ugcId = 9;
  optional bool isGuideWarmRound = 10;
  optional CompetitionBattleData competitionData = 11; // 赛事数据
  optional int64 migrateBeginTime = 13; // 开始迁移时间
  optional int64 migrateSuccessTime = 14; // 成功迁移时间
  optional int32 migrateProcessStatus = 15; // 迁移状态
  optional RoomSetting roomSetting = 16; // 房间设置
  optional int64 totalPauseTimeMs = 17; // 累计暂停时间
  optional int32 battleCampSize = 18;  // 战斗阵营数量
  optional bool midJoinUnable = 19;  // 是否关闭了中途加入
  optional int32 pointNumber = 20;
  repeated BattleCamp battleCamps = 21;
  optional int32 guideWarmRoundIndex = 22; // 新手引导轮次
  optional int64 mapPoolId = 23;
  optional int32 ugcAchievementSize = 24;
  repeated LevelRoundABTestInfo levelRoundABTestInfos = 25;
  optional MatchDynamicConfigData dynamicConfigData = 26; //匹配动态配置
  repeated MatchTypeABTestInfo matchTypeABTestInfo = 27; //玩法模式ABtest信息
  optional CustomRoomInfo customRoomInfo = 28;  // 自定义房间信息
  optional int64 generalTime = 29;  // 通用的时间戳，单位秒，目前starpbattlesvr用于通知世界ds踢人的重试逻辑
  optional BattleSceneExtraInfo sceneExtraInfo = 30;  // 场景额外信息
  optional int64 battleId = 31;  // 记录的battleId
  optional BattleSceneBriefDB sceneBriefDB = 32;  // 场景的基础信息
}

message LevelRoundABTestInfo {
  optional int32 round = 1;
  repeated int32 enabledTestIds = 2;
}

message MatchTypeABTestInfo {
  optional int32 abTestId = 1;
  optional bool isExperimentGroup  = 2; //是否为实验组
  repeated int32 randEventRound = 3; //随机事件实验中启用随机事件轮次
  optional int32 groupId = 4; //groupId
  repeated int32 levelRound = 5; //实验触发轮次
}

message BattleFrameExtraInfo {
  optional int64 readyTime = 1;
  optional int64 seed = 2;
  optional string songID = 3;
  optional string desMod = 4;
  optional int32 songDiff = 5;
  optional int32 songTime = 6;
  optional bool hasCaculateModEnd = 7;
  optional bool firstPlayerReport = 8;
  optional int64 checkStartTime = 9;
  optional int32 currentFrame = 10;
  optional int64 lastFlushFrameTime = 11;
  optional bool firstPlayerReportEnd = 12;
}

message BattleLevelRoundInfo {
  optional int32 levelId = 1;
  optional int32 round = 2;
  optional int32 levelStatus = 3;
  optional int64 statusChangeTime = 4;
  optional int32 statusDurationTime = 5;
}

// 触发任务刷新的类型
enum TaskRefreshTriggerType {
  TRTT_Unknown = 0;
  TRTT_Normal = 1; // 正常刷新
  TRTT_Daily = 2; // 日刷新
  TRTT_Weekly = 3; // 周刷新
}

message BattleData {
  optional int64 player_id = 1;
  repeated string battle_data = 2; // 当前数据，音游是分数
  optional int32 score = 3;   //
  repeated KeyValueInfo KeyValueInfo = 4;
  optional int32 rank = 5;
}

message TeamBattleData {
  optional int64 side_id = 1;
  optional int32 total_score = 2;   //
  optional int32 rank = 3;
  repeated BattleData player_battle_data = 4;
}

message BattleFrameData {
  optional int32 frame = 1;
  repeated BattleData battle_data = 2; // 当前数据，音游是分数
  optional int32 modeID = 3;
  repeated TeamBattleData team_battle_data = 4; // 当前数据，音游是分数
}

message BattleEndData {
  optional int32 rank = 1;  // 结算时的排名
  optional int64 player_id = 2;
  repeated string battle_data = 3; // 当前数据，音游是分数
  optional int32 score = 4;   //
  repeated KeyValueInfo KeyValueInfo = 5;
  optional int32 star = 6; // 星数
}

message QualifyingBattleData {
  optional int64 battleId = 1;
  optional int32 matchType = 2;
  optional int32 result = 3;
  repeated int32 rankInfo = 4;
  repeated int32 teamRankInfo = 5;
  repeated int64 roomPlayer = 6;
  optional int64 battleEndTime = 7;
  optional int32 lastLevel = 8; // 0 未通过 1 通过
}

//公会信息
message ClubData {
  optional int64 id = 1; //公会id
  optional string clubName = 2; //公会名字
  optional int32 numbers = 3; //公会人数
  optional int32 countLimit = 4; //公会上限人数
  optional int32 clubLevel = 5; //公会等级
  optional string remark = 6; //公会宣言
  optional bool status = 7; //申请状态
  optional int32 curExp = 8; //当前经验
  optional int32 exp = 9; //升级经验
  optional int32 needAgree = 10; //是否需要审核
  optional int64 uid = 11;   //会长uid
  optional string name = 12; //会长昵称
  optional int64 chatSid = 13; //聊天sid
  optional ChatType type = 14; //聊天类型
}
//公会成员信息
message ClubMemberData {
  optional int64 uid = 1; //玩家uid
  optional string name = 2; //玩家昵称
  optional string avatar = 3;  //玩家头像
  optional int32 identity = 4; //玩家职务
  optional int32 level = 5; //玩家等级
  optional bool online = 6; //玩家状态
  optional int64 joinTime = 7; //加入时间
  optional int64 offOnlineTime = 8; //下线时间
  optional string openID = 9;
  optional int32 qualifying = 10; //段位
  optional int32 degree = 11; //小段位
}

message ClubId {
  repeated int64 cid = 1; //公会id
}

//公会成员msg
message ClubMemberListMsg {
  repeated ClubMemberMsg memMsg = 1;
}

message ClubApplyListMsg {
  repeated ClubApplyMsg applyMsg = 1;
}

message ClubMemberMsg {
  optional int64 uid = 1; //玩家uid
  optional int64 joinTime = 2; //公会：加入时间
  optional int32 identity = 3; //身份
  optional int64 offOnlineTime = 4; //离线时间
  optional int64 applyTime = 5; //申请时间
}

message ClubApplyMsg {
  optional int64 uid = 1; //玩家uid
  optional int64 applyTime = 2; //申请：申请时间
}

//idip结果
enum IdipHttpResult {
  IHR_SUCCESS = 0; // 成功
  IHR_SIGN_ERR = -2; // sign err
}

enum IdipUserOpMethod {
  BOM_ByUid = 0;
  BOM_ByZoneid = 1;
  BOM_ByPlatid = 2;
}

enum ActivateStatus {
  AS_Default = 0;
  AS_Activated = 1;
  AS_UnActivated = 2;
}

enum BanType {
  BT_BanLogin = 1;                // 封号(有角色)
  BT_BanRegister = 2;             // 禁止注册(无角色), 暂未使用
  BT_ShutUp = 3;                  // 禁言(有角色)
  BT_Signature = 4;               // 封禁个性签名, 暂未实现
  BT_Profile = 5;                 // 封禁玩家头像
  BT_ChangeName = 6;              // 封禁玩家改名
  BT_AiImage = 7;                 // 封禁AI参考图
  BT_Voice = 8;                   // 封禁玩家语音功能
  BT_AddFriend = 9;               // 封禁玩家添加好友
  BT_Search = 10;                 // 封禁玩家搜索
  BT_Rank = 11;                   // 封禁排行榜
  BT_CreateRoom = 12;             // 封禁玩家创建房间
  BT_CreateMap = 13;              // 封禁玩家创建地图
  BT_AiChangeColor = 14;          // 封禁使用AI配色
  BT_UseFireworks = 15;           // 封禁玩家放文字烟花
  BT_UgcTopic = 16;               // 封禁玩家发布UGC地图使用话题
  BT_GameMode = 17;               // 封禁玩家参与指定玩法
  BT_Device = 18;                 // 封禁设备
  BT_CreateOrModifyElement = 19;  // 封禁创建和修改元素标签
  BT_Club = 20;                   // 封禁玩家创建及编辑社团信息
  BT_AiAnicap = 21;               // 封禁AI视频动捕
  BT_ClubSearch = 22;             // 封禁搜索社团
  BT_PublishActivityMapWish = 23; // 封禁发布活动地图许愿
  BT_ModifyUgcCollectionName = 24;// 封禁修改ugc合集名称
  BT_ModifyUgcCollectionDesc = 25;// 封禁修改ugc合集简介
  BT_AiAnswer = 26;               // 封禁Npc智能对话
  BT_UgcRank = 27;                // 封禁ugc地图排行榜
  BT_CreateOrModifyUgcAchievement = 28;         // 封禁UGC自定义成就的编辑与创建
  BT_MallGiftCard = 29;           // 封禁商城赠送使用赠礼卡
  BT_AigcNpcPal = 30;             // 封禁AI伙伴
  BT_BirthdayCard = 31;           // 封禁赠送贺卡
  BT_FarmSignature = 32;          // 封禁农场个性签名
  BT_UgcCreatorMessage = 33;      // 封禁Ugc创作者留言
  BT_SP_GameMode = 34;            // 封禁啾灵SP匹配模式
  BT_SP_Login_IP = 35;            // 封禁登录,IP变更封禁,保密测试用
  BT_ItaBagEquip = 36;            // 禁止装饰痛包
}

enum AQUgcActionType {
  AQUAT_UNKNOWN = 0;
  AQUAT_YuanJianBiaoQian = 1;  // 元件标签
}

message IdipBanGameModeStatus {
  map<int32, BanStatus> banModeMap = 1; // matchTypeId => BanStatus
  optional bool banAll = 2; // 是否封禁所有玩法
  optional BanStatus banAllStatus = 3; // 封禁所有玩法信息
}

// 扩展的封禁状态信息
message ExtendedBanStatus {
  oneof data {
    IdipBanGameModeStatus banGameModeStatus = 1;
  }
}

message BanStatus {
  optional int64 BanStart = 1; //ban start time
  optional int64 BanBefore = 2; //cannot login before this time
  optional string BanReason = 3; //ban reason
}

message BanInfo {
  optional int32 platid = 1;
  optional int64 timeStamp = 2;
  map<int32, BanStatus> banStatusMap = 3; // banType => BanStatus
  map<int32, ExtendedBanStatus> extendedBanStatusMap = 4; // banType => ExtendedBanStatus
}

// 客户端资源文件md5信息
message ResourceFileInfo {
  required string fileName = 1; // 文件名
  optional string md5 = 2; // 客户端文件md5
}

message ExpressionInfo {
  optional int64 player_id = 1;
  optional int32 expID = 2;      //表情id
}

message LikeRecordInfo {
  repeated int64 uid = 1;
}

message ArrayString {
  repeated string arrayString = 1;
}

message ArrayLong {
  repeated int64 arrayLong = 1;
}

message QuickTextGroup {
  optional string name = 1; // 快捷文本类型文本
  repeated QuickText textList = 2; //文本列表
}

message QuickText {
  optional string text = 1;
  optional string audioUrl = 2; // 语音地址
}

//好友排行类型枚举，已废弃
enum FriendRankType {
  FRT_Unknown = 0;
}

enum ModGameSpecPlayerPublicInfoType {
  MGSPBIT_LetsGoWinTimes = 1;// 达标次数
  MGSPBIT_LetsGoContinuousWin = 2;// 连赢次数
  MGSPBIT_LetsGoWinTop3Times = 3;// 前三名次数
  MGSPBIT_LetsGoWinChampTimes = 4;// 夺冠次数
  MGSPBIT_LetsGoCrownPlayTimes = 5;// 参与抢皇冠次数
  MGSPBIT_LetsGoTeamPlayTimes = 6;// 组队开局次数
  MGSPBIT_LetsGoCrashTotalTimes = 7;// 累计碰撞次数
  MGSPBIT_LetsGoCountdownFinishTimes = 8;// 倒计时内达标次数
  //
  MGSPBIT_DancingKingStarCount = 10001;// 音游 总星数
  MGSPBIT_DancingKingFullCombo = 10002;// 音游 全连接
  MGSPBIT_DancingKingNumberOfMods = 10003;// 记录场次总数
  MGSPBIT_DancingKingNumberOfSinge = 10004;// 记录单人比赛总数
  MGSPBIT_DancingKingNumberOfDouble = 10005;// 记录组队比赛总数
  MGSPBIT_DancingKingNumberOfEmojis = 10006;// 发送表情总数
  MGSPBIT_DancingKingNumberOfTop = 10007;// 记录第一名次数
  MGSPBIT_DancingKingNumberOfDoubleWin = 10008;// 记录团战胜利次数
  MGSPBIT_DancingKingNumberOfTeam = 10009;// 房间人数大于一 匹配成功进入战场即为一次
}

// 聊天相关操作类型
enum ChatOperationType {
  COT_OnInput = 1; // 对方正在输入中
  COT_RetractMessage = 2; // 撤回指定seqId的消息
  COT_ClearUser = 3; // 清空指定玩家的消息
  COT_ClearAll = 4; // 清空所有消息
  COT_MemberJoinNtf = 5; // 玩家加入通知
  COT_MemberLeaveNtf = 6; // 玩家离开通知
}

message CustomData {
  optional bytes protodata = 1;  // 客户端自定义pb数据
  optional int32 prototype = 2;  // 客户端自定义pb类型
}

enum PvEStageSongDiff {
  PSSD_DEFAULT = 0;
  PSSD_SIMPLE = 1;
  PSSD_MIDDLE = 2;
  PSSD_HARD = 3;
}

enum PvERewardStatus {
  PRS_NotComplete = 0; // 未达成
  PRS_Completed = 1; // 已达成
  PRS_Acquired = 2; // 已领取
}

enum ReportReason {
  RR_Unknown = 0;
  RR_Revile = 101; // 辱骂谩骂
  RR_Vulgar = 103; // 色情低俗
  RR_PoliticallySensitive = 104; // 政治敏感
  RR_Other = 199;
  RR_NickIllegal = 901;
  RR_AvatarIllegal = 902;
}

enum ReportCategory {
  RC_Unknown = 0;
  RC_UGC = 1; // ugc内容
  RC_Profile = 9; // 个人信息
}

// mod历史战绩信息
message ModBattleRecordItem {
  optional int32 rank = 1; // 名次
  optional int32 matchDuration = 2; // 比赛时长秒为单位
  optional int32 endTime = 3; // 结束时间戳
  optional int32 isWin = 4; // 1赢了 0输了
}

// mod历史战绩信息
message ModBattleRecordInfo {
  repeated ModBattleRecordItem recordItem = 1; // 比赛时长秒为单位
  optional int32 totalNumberOfGamesNumber = 2; // 总对局数
  optional int32 numberOfWins = 3; // 胜场数
}

message ModRankRecordInfo {
  optional int32 numberOfChampions = 1; // 夺冠数
  optional int32 timeStamp = 2; // 最后夺冠时间点
}

message RewardItemInfo {
  optional int64 itemId = 1; // 奖励对应的道具id
  optional int64 rewardItemNum = 2; // 奖励对应的道具数量
}
message BaseSceneData {
  optional int64 countdownMs = 1; // 结束倒计时
  optional int64 endMsTime = 2;   // 结束时间点
  optional int32 sceneStatus = 3; // 状态
  optional int64 beginMsTime = 4; // 开始时间点（不包括准备时间）
}

enum TeamInvitationType {
  TIT_Default = 0;    // 邀请
  TIT_JumpToTeamScene = 1;      // 废弃
  TIT_AskForJoin = 2; // 请求加入
}

enum ScenePlayStatus {
  SPS_Invalid = 1;    // 无效状态
  SPS_Idle = 2;   // 过渡阶段
  SPS_Normal = 3; // 正常阶段
  SPS_End = 4; // 结束阶段
}

message SceneChatGroupKey {
  optional ChatGroupKey groupKey = 1;
}

message ScenePlayExtraInfo {
  optional int32 playId = 1;                  // 玩法id
  optional int64 curPlayStatusStartTime = 2;  // 玩法开始时间
  repeated int64 mapEntityIds = 3;            // 玩法相关MapEntity
  optional ScenePlayStatus playStatus = 4;    // 玩法状态
  optional int32 seedData = 5;                // 随机种子
}

message SceneAddrInfo {
  optional int64 scene_id = 1;
  optional string url = 2;
  optional int32 port = 3;
}

//房间排行类型枚举，已废弃
enum SceneRankType {
  SRT_Unknown = 0;
}

message MatchContentInfo {
  optional int32 is_join_after_match = 1;   // 是否匹配成功自动组队
  optional KVArray kvArray = 2; // 自定义kv项
}

message JoinMidwayFriendSvrInfo {
  optional int64 battleSvrId = 1;
  repeated int64 uidList = 2;
}

// 不同玩法类型额外的 需要自定义处理的匹配参数
message GameTypeSpecialMatchParams  {
  // 匹配确定Ai数量, 难度时 使用的房间配置id
  // 覆盖原有的房间, 实现匹配池在一起, 但是难度和ai数量可以随着队伍状态而变化
  optional int32 calRobotSideDiffUsingRoomInfoId = 1;
}

message TeamDataUgcBaseInfo {
  optional UgcMdList mdList = 1;  // 地图cos信息
}

message TeamData {
  required int64 roomID = 1;
  optional int64 leaderID = 2;
  optional int32 matchType = 3;
  map<int32, string> ruleDimInfos = 4;
  map<int64, MemberBaseInfo> memberInfos = 5;
  optional int64 matchTime = 6;
  optional int32 modeID = 7;
  required int32 ruleId = 8;
  required int32 roomInfoId = 9;
  optional bool isWarmRound = 10 [deprecated = true];             // 废弃 用warmRoundType
  optional bool isGuideWarmRound = 11;
  repeated int64 acceptableDsIDCIdList = 12;   // 匹配单元倾向的ds idc列表
  optional bool isNotFillTeammate = 13; //是否不用填充队友
  optional WarmRoundType warmRoundType = 14;  // // 是否触发温暖局及类型

  optional GameModeType gameModeType = 15; // 透传队伍的模式信息
  optional int32 guideWarmRoundIndex = 16;
  optional int64 ugcId = 17;
  optional int32 sideID = 18;   //选定的阵营
  // 匹配的各个玩法模式额外的匹配信息, 用于匹配时触发部分玩法的特殊匹配逻辑
  // 例如目前的moba玩法 可以根据不同的特殊局条件 动态并更房间的ai数量
  optional GameTypeSpecialMatchParams typeSpecMatchParams = 19;
  optional MetaAiWarmRoundV2TestingInfo metaAiWarmRoundV2TestingInfo = 20;  // ailab温暖局v2实验信息
  repeated BattleCamp battleCamps = 21;
  optional bool ugcIsCompilations = 22;  // ugc匹配时是否是组合图
  repeated int64 mapPoolList = 23;  // 地图图集id
  optional AiLabArenaWarmRoundInfo aiLabArenaWarmRoundInfo = 24; // ailab arena温暖局信息
  optional bool ugcIsMultiRound = 25;  // ugc匹配是否多轮次
  optional TeamDataUgcBaseInfo ugcBaseInfo = 26;  // 地图信息
  repeated MatchTypeABTestInfo matchTypeABTestInfo = 27;  // 玩法AB实验信息
  optional AiLabChaseWarmRoundInfo aiLabChaseWarmRoundInfo = 28; // ailab chase温暖局信息
  optional int32 difficultyId = 29;  // 啾灵组队pve玩法的关卡层id
}

// ailab温暖局v2实验信息
message MetaAiWarmRoundV2TestingInfo {
  repeated IntKVEntry aiRandomInfos = 1;      // ai难度信息 {难度: 权重}
  optional int32 scriptType = 2;              // 剧本id
  optional AiLabArenaWarmRoundType aiLabArenaWarmRoundType = 3; // ailab投放的arena温暖局类型
}

enum AiLabArenaWarmRoundType {
  ALAWRT_OneHuman = 1; // 单真人
  ALAWRT_OneTeamHuman = 3; // 单队真人
  ALAWRT_TwoTeamHuman = 6; // 两队真人
  ALAWRT_ThreeTeamHuman = 9; // 三队真人
  ALAWRT_Equivalent_Unknown = 10; // 排位对等真人局,未知人数
  ALAWRT_Equivalent_One = 11; // 排位对等真人局,每侧1个真人
  ALAWRT_Equivalent_Two = 12; // 排位对等真人局,每侧2个真人
  ALAWRT_Equivalent_Three = 13; // 排位对等真人局,每侧3个真人
  ALAWRT_Equivalent_Four = 14; // 排位对等真人局,每侧4个真人
  ALAWRT_DynamicHuman = 99; //动态数量真人
}

// ailab arena温暖局信息
message AiLabArenaWarmRoundInfo {
  optional AiLabArenaWarmRoundType aiLabArenaWarmRoundType = 1; // ailab投放的arena温暖局类型
  optional string aiLabMatchParam = 2;                          // ailab投放时携带的参数，用于上报ailab时回填
  optional int32 robotsCnt = 3;                                 // 温暖局下发的AI机器人数量 用来动态匹配使用
}

// ailab chase温暖局信息
message AiLabChaseWarmRoundInfo {
  optional string aiLabMatchParam = 1;                          // ailab投放时携带的参数，用于上报ailab时回填
  //机器人阵营信息
  repeated com.tencent.wea.xlsRes.MatchSideInfo teamRobotsSideInfo = 2;
}

// ailab干预ai强度表
message MetaAiInfoDifficulty {
  optional int32 levelId = 2;               // 关卡id
  repeated int64 expectedDuration = 3;      //目标时间
}

// ai lab缀合后 传过来的额外信息
message MetaAiGetMatchSuccData {
  optional int64 gameId = 1;    // 是否属于ai lab缀合 id 0 表示不是
  optional bool isUseAiInfo = 2;  // 是否使用aiLab返回的ai信息
  optional int32 scriptId = 3;    // 剧本id
  map<int32, MetaAiInfoDifficulty> infoDifficulties = 4;    // ai强度信息

  optional MetaAiWarmRoundV2TestingInfo warmRoundV2TestingInfo = 22;    // ailab温暖局v2实验信息 battle也需要
  optional AiLabArenaWarmRoundInfo aiLabArenaWarmRoundInfo = 23;        // ailab arena温暖局信息
  optional AiLabChaseWarmRoundInfo aiLabChaseWarmRoundInfo = 24;        // ailab chase温暖局信息
}

message MatchSuccData{
  optional int64 roomID = 1;//对应匹配规则表里的roomId，非业务模块的房间Id
  map<string, string> resultInfos = 2;//结果值信息
  map<int64, MatchTeamData> teamDatas = 3;//队伍信息
  optional int32 matchType = 4;//匹配类型(游戏id)
  optional int32 modeID = 5;//匹配规则
  optional bool isWarmRound = 6 [deprecated = true];        // 废弃 改用warmRoundType
  optional bool isGuideWarmRound = 7;
  optional int64 chosenDsIDCId = 8;     // 选定的创建ds的idc id
  optional int32 warmScriptID = 9; // 普通温暖局剧本ID
  optional int64 matchedTimeMs = 10;  // 匹配成功时的时间戳
  optional MetaAiGetMatchSuccData metaAiGetMatchData = 11;  // ai lab缀合局额外信息
  optional bool alreadyRandAIDifficulty = 12; // 是否已经随机过AI强度
  optional WarmRoundType warmRoundType = 13;  // 是否触发温暖局及类型
  optional int32 guideWarmRoundIndex = 14;  // 新手温暖局轮次
  optional CompetitionBattleData competitionData = 15; //赛事信息
  optional int32 ruleId = 18;
  optional int32 controlGroupID = 19;      // 对照组ID
  optional MatchFillBackData matchFillBackData = 20;  //回填信息带回
  optional GameTypeSpecialMatchParams typeSpecMatchParams = 21;  //  // 匹配的各个玩法模式额外的匹配信息, 用于匹配时触发部分玩法的特殊匹配逻辑
  repeated int32 mapIDList = 22;    //地图列表
  optional MatchDynamicConfigData dynamicConfigData = 23;  //匹配动态配置
  optional bool ugcIsMultiRound = 24;  // ugc匹配是否多轮次
  repeated MatchTypeABTestInfo matchTypeABTestInfo = 25;  // 玩法AB实验信息
  optional int32 difficultyId = 27;  // 啾灵组队pve玩法的关卡层id
}

message MatchTeamData {
  required int64 roomID = 1;
  optional int64 leaderID = 2;
  map<int64, MemberBaseInfo> memberInfos = 3;
  optional int32 side = 4; // 废弃，具体阵营在玩家MemberBaseInfo里定义，应对单房间多阵营的情况
  optional int64 matchTimeMs = 5;
  optional MatchRuleInfo ruleInfo = 6; // 队伍/房间的玩法详细信息
  optional MatchContentInfo matchContentInfo = 7; // 队伍/房间的其他匹配参数
  optional int32 reqWarmRoundType = 9;            // 队伍请求的温暖类型 见WarmRoundType
}

// 条件进度信息
message ConditionProgressInfo {
  optional int64 value = 1;
  optional int64 targetValue = 2;
}

enum TopRankBackendType {
  TRBT_Unknown = 0;
  TRBT_Apollo_Trank = 1;
  TRBT_Apollo_TopNext = 2;
  TRBT_UgcPlat = 3;
  TRBT_Redis_ZSet = 4;

  TRBT_Plat = 5;

  TRBT_Preserved_0 = 90;
  TRBT_Preserved_1 = 91;
  TRBT_Preserved_2 = 92;
  TRBT_Preserved_3 = 93;
  TRBT_Preserved_4 = 94;
}

enum MatchCancelType {
  MCT_None = 0;
  MCT_Ready = 1; // 取消准备
  MCT_Matching = 2; // 取消匹配
}

enum GeoLevel {
  GL_Unknown = 0;
  GL_Town = 1;
  GL_City = 2;
  GL_Province = 3;
  GL_Nation = 4;
}

message UgcWorkData {
  optional bytes workData = 1;
}

// 活动小队
message ActivitySquad {
  optional int64 squadId = 1; // 小队ID
  optional int32 activityNo = 2; // 活动期号
  repeated PlayerColdData playerList = 3; // 成员列表
  optional int32 dailyPoint = 4; // 当日分值
  optional int32 dailyPersonalTaskGroupId = 5; // 每日个人任务组
  optional int32 dailySquadTaskGroupId = 6; // 每日小队任务组
  optional int32 dailyPointRewardTaskGroupId = 7; // 每日羁绊累计奖励任务组
  optional int32 achievementTaskGroupId = 8; // 成就任务组
  optional SquadDailyTaskInfo dailyTaskInfo = 9; // 每日任务组详情
  optional SquadTaskGroupInfo achievementTaskInfo = 10; // 成就任务组详情
}

message PayInfo {
  optional string payToken = 1;
  optional string payPf = 2;
  optional string payPfKey = 3;
}

// 发货回调透传字段，注意：该字段有长度限制
message DeliverGoodsMetaData {
  optional int64 times = 1;
  optional DepositMidasMetaData deposit = 2;
  optional int64 costDiamonds = 3;
  optional string params = 4;
  optional int64 uid = 5;
  optional string busBillNo = 6;   // 业务订单号, 用于串联邮件、money、道具等流程
  optional int32 activityId = 7; // 活动id
  optional PermitPurchaseData permit = 8;
  optional int64 giveFriendUid = 9; // 赠送好友uid
  optional wea.xlsRes.ItemChangeReason reason = 10;
  optional string gopenid = 11;
  optional ScratchOffTicketMidasMetaData ticketMidasMetaData = 12;  // 刮刮乐
  optional UpgradeCheckInManualMidasMetaData checkInManualMidasMetaData = 13;  // 升级版打卡手册
  optional int64 mapId = 14; // ugc地图ID
  optional string billNo = 15;
  optional int64 creatorId = 16;
  optional string checkCode = 17;   // ugc内购的时候用于校验作弊的校验码
  optional BPMidasCBData bp = 18;
  optional int64 clientVersion = 19;
  optional MetaDataReason subReason = 20; // 存放subReason和其他
  optional WerewolfFullReduceActivityMidasMetaData werewolfFullReduceMidasMetaData = 21; //狼人满减
  optional MallGiftCardInfo mallGiftCard = 22; // 外观类商品赠礼卡信息
  optional string QL = 23;  // 运营透传流水数据QL:LoginPlat_PakType_Channel
  optional ActivityInflateRedPacketParam redPacket = 24; // 膨胀爆红包充值参数
  optional UpgradeFarmDailyMidasMetaData upgradeFarmDailyMidasMetaData = 25;  //农场天天领升级(购买权益卡)
  optional UpgradeWeekendGiftMidasMetaData upgradeWeekendGiftMidasMetaData = 26; //一元抽奖
}

message UpgradeWeekendGiftMidasMetaData {
  optional int32 activityId = 1;
  optional int32 rechargeId = 2;
  optional int32 commodityId = 3;
}

message MetaDataReason {
  optional int64 subReason = 1;
  repeated int64 changeReservedParams = 2;
}

message BillInfo {
  repeated DeliverProductInfo productInfoList = 1;
  optional DeliverGoodsMetaData appMeta = 2;
  optional string payChannel = 3; // 代币支付：uniacct 苹果直购：iap
  optional string outTradeNo = 4;
}

message DeliverProductInfo {
  optional string ProductId = 1;
  optional int32 Quantity = 2;
  optional string ZoneId = 3;
  optional string RoleId = 4;
  optional float OrigPrice = 5;
  optional float ProductPrice = 6;
}

enum BillStatus {
  BS_INIT = 0;
  BS_COST_COIN = 1;
  BS_SEND_ITEM = 2;
  BS_ERROR = 3;
  BS_SUCCESS = 4;
  BS_OFFLINE = 5;
  BS_DEL = 6;
}

enum BillType {
  BT_Unknown = 0;
  BT_DirectPurchase = 1;
}

message OpenIdAndUid {
  optional string openId = 1;
  optional int64 uid = 2;
}

message ProcessInfo {
  optional int32 curProcess = 1; // 当前进度
  optional int32 targetProcess = 2; // 目标进度
  optional int64 updateTimestamp = 3; // 更新时间
}

message TaskStateInfo {
  optional com.tencent.wea.xlsRes.TaskStatus status = 1; // 当前状态
  optional int64 updateTimestamp = 2; // 状态更新时间
}

message TaskMemberState {
  optional int64 playerId = 1; // 玩家
  optional ProcessInfo process = 2; // 进度
  optional TaskStateInfo state = 3; // 状态
}

message TaskMemberStateList {
  repeated TaskMemberState list = 1;
}

message SquadTaskInfo {
  optional int32 taskId = 1; // 任务ID
  optional TaskMemberStateList memberStateList = 2; // 成员的完成状态
  optional ProcessInfo totalProcessInfo = 3; // 总的完成进度
}

message SquadTaskInfoList {
  repeated SquadTaskInfo list = 1;
}

message SquadTaskGroupInfo {
  optional int32 groupId = 1; // 任务组Id
  optional SquadTaskInfoList taskList = 2; // 任务列表
}

message SquadDailyTaskInfo {
  optional SquadTaskGroupInfo personalTaskGroup = 1; // 每日个人任务
  optional SquadTaskGroupInfo squadTaskGroup = 2; // 每日团队任务
  optional SquadTaskGroupInfo pointTaskGroup = 3; // 每日羁绊值任务
  optional int64 lastRefreshTimestamp = 4; // 上一次任务刷新时间
}

enum MessageSlipOpType {
  MSOT_Unknown = 0;
  MSOT_Favour = 1;        // 点赞
  MSOT_DeleteFavour = 2;  // 取消点赞
  MSDT_Comment = 3;      // 评论
  MSDT_DeleteComment = 4; // 删除评论
}

enum MessageSlipType {
  MST_NewestSlip = 0;     // 最新留言
  MST_HotSlip = 1;        // 热门留言
  MST_SelfSlip = 2;       // 我的留言
}

enum ApplyType {
  AT_GET = 1;
  AT_PUT = 2;
}

enum ApplyReason {
  AR_CREATE = 1;  //废弃
  AR_COPY = 2;
  AR_SAVE = 3;
  AR_PUBLISH = 4;
  AR_OPEN = 5;//废弃 理应是废弃掉，因为get的密钥不走这里走
  AR_PUBLISH_REGAIN = 6;//发布列表恢复到草稿箱
  AR_COVER_UPLOAD = 7;//封面上传
  AR_GROUP_OBJECT = 8;//组合
  AR_GROUP_PUBLISH_OBJECT = 9;//组合发布
  AR_Report = 10;//举报
  AR_PUBLISH_UPDATE = 11;//发布更新

  AR_XIAOWO = 12;
  AR_PARTY = 13;
  AR_XIAOWO_MAP = 14;
  AR_XIAOWO_MAP_UPDATE = 15;

  AR_LAYOUT = 20; // 方案
  AR_MULTIPLE_PLAY = 21; //多人测试
  AR_PRE_AUDIT = 22;//编辑图片审核
  AR_Mul_Cover = 23;//多封面图上传
  AR_Publish_Review = 24; // 发布审核
  AR_Custom_Loading = 25;  // 自定义loading图
  AR_Custom_Lobby_Cover = 26;  // 乐园地图自定义封面
  AR_Video_Cover = 27;  // 视频封面上传
  AR_Fbx_File = 28;    //自制骨骼上传
  AR_3D_MODEL_SAVE = 29;    // 3D模型保存

  AR_StaticGet = 100;//not support client
  AR_AIGC_CREATE = 101;  // aigc上传(ai侧使用,不允许客户端直接使用)
  AR_AIGC_GET = 102;  // 下载aigc  增加缓存，所有aigc下载都用同一个reason
  AR_AIGC_COLOR_REQ = 103;  // 请求ai关卡换色
  AR_AIGC_COLOR_RES = 104;  // 获取ai关卡换色
  AR_AIGC_COLOR_CHANGE = 105;  // ai侧进行关卡换色(ai侧使用,不允许客户端直接使用)
  AR_AIGC_UPLOAD = 106; // 上传ai参考图历史信息
  AR_AIGC_VOICE_GEN = 107;  // 生成ai语音
  AR_AIGC_VOICE_GET = 108;  // 下载ai语音
  AR_AIGC_ANICAP_VIDEO_UPLOAD = 109; // ai动作生成，视频上传
  AR_AIGC_ANICAP_GEN = 110;  // ai侧动作生成
  AR_AIGC_ANICAP_GET = 111;  // 获取ai生成动作
  AR_AIGC_TTS_GEN = 112;   // 新tts平台生成ai语音
  AR_AIGC_TTS_GET = 113;   // 新tts平台下载ai语音
  AR_AIGC_MAGIC_PIC_UPLOAD = 114;   // aigc魔法图片上传
  AR_AIGC_ZUOYEBANG = 115;   // ainpc作业帮
  AR_AIGC_ZUOYEBANG_Copy = 116;   // ainpc作业帮复制

  AR_Upload_Video = 180;   // ds上传视频
  AR_SHARE_PUT = 200; // 上传分享图片     废弃
  AR_SHARE_GET = 201; // 下载分享图片     废弃
  AR_PHOTOALBUM_PUT = 202; // 上传相册
  AR_PHOTOALBUM_GET = 203; // 下载相册
  AR_COMMON_SHARE_PUT = 204; // 上传图片 通用逻辑（分享等）
  AR_COMMON_SHARE_GET = 205; // 下载图片 通用逻辑（分享等）
  AR_ServerStaticPut = 300; // 服务用上载
  AR_FARM_LAYOUT_UPLOAD = 307; // 农场图纸
  AR_FARM_COOKCOMMENT = 308; // 农场餐厅
  AR_ITA_BAG_BADGE_PUT = 309; // 痛包吧唧摆放图片上传
  AR_ITA_BAG_BADGE_GET = 310; // 痛包吧唧摆放图片下载



  //----------服务器内部使用不会被客户端调用--------------------//
  AR_UGC_GROUP_USE = 1000; // 组合使用
  AR_UGC_RES_USE = 1001; // 资产使用
  AR_UGC_GROUP_PUBLISH_USE = 1002; // 组合使用
  AR_UGC_RES_PUBLISH_USE = 1003; // 资产使用

}

message UgcKeyInfo {
  optional string key = 1;
  optional string id = 2;
  optional string token = 3;
  optional int64 startTime = 4;
  optional int64 endTime = 5;
  optional string km = 6;
  optional int64 serverTimestamp = 12; // 不会被修改时间影响的时间戳
}

message DepositMidasMetaData {
  optional int32 activityId = 1;
  optional int32 depositId = 2;
  optional int32 commodityId = 3;
}

message ScratchOffTicketMidasMetaData {
  optional int32 activityId = 1;
  optional int32 rechargeId = 2;
  optional int32 commodityId = 3;
}

message UpgradeCheckInManualMidasMetaData {
  optional int32 activityId = 1;
  optional int32 rechargeId = 2;
  optional int32 commodityId = 3;
}

message UpgradeFarmDailyMidasMetaData {
  optional int32 activityId = 1;
  optional int32 rechargeId = 2;
  optional int32 commodityId = 3;
  optional int32 buyNums = 4;
}

message WerewolfFullReduceActivityMidasMetaData {
  optional int32 couponId = 1;
  optional int64 sumPrice = 2;
  repeated int64 originalPrices = 3;                                 //已废弃
  repeated WerewolfFullReducedConfigs  werewolfFullReducedConf = 4;  //已废弃
  optional int32 activityId = 5;
  optional int32 bigAwardCouponId =6;
  repeated int32 commodityId = 7;
  repeated int32 buyNums = 8;
}

message WerewolfFullReducedConfigs{
  optional int32 couponID = 1;
  optional int32 limitNum = 2;
  optional int64 useTime = 3;
  optional int64 accumulatedAmount = 4;
  optional bool receiveFinalist = 5;
  optional int32 extraGiftNum = 6;
}



// ugc元数据消息类型
enum UgcMapMetaInfoMsgType {
  METAINFO_MSG_TYPE_PREVIEW_PIC = 1;  // 缩略图
  METAINFO_MSG_TYPE_LEVEL = 2;        // 关卡
  METAINFO_MSG_TYPE_EDIT = 3;         // 编辑
  METAINFO_MSG_TYPE_GROUP = 4;        // 组合
}

// ugc元数据处理类型
enum UgcMapMetaInfoProcessType {
  METAINFO_PROCESS_TYPE_NORMAL = 0; // 正常
  METAINFO_PROCESS_TYPE_BAN = 1;    // 封禁
}

message UgcMapMetaInfo {
  optional int32 size = 1;
  optional string msg = 2; //md5
  optional int32 msgType = 3;       // 消息类型, 具体值见枚举 MdType
  optional int32 processType = 4;   // 处理类型, 详情见枚举 UgcMapMetaInfoProcessType
  optional string version = 5;
  optional bool isCoverCheckPass = 6; // 是否通过了图片检查（目前仅组合使用）
  optional string preMsg = 7; //pre md5 加密前的md5，只有加密的文件才会有这个数据
  optional int64 layerId = 8;       //图层信息
}

message PlayerRankInfo {
  optional int64 uid = 1;
  optional int32 rank_no = 2;
  optional int32 score = 3;
  repeated int32 extraScores = 4;
  optional int32 up_or_down = 5;
}

message UgcMdList {
  repeated UgcMapMetaInfo info = 1;
  optional int64 curId = 2;        //草稿箱保存id
  optional int64 versionId = 3;     //版本id
}

message UgcTypeList {
  repeated int32 value = 1;
}

message UgcGroupIdList{
  repeated int64 groupId = 1;  // M7 废弃
  repeated UgcGroupActorList groupIdNum = 2;  // 组合id和数量
}

message UgcGroupActorList {
  optional int64 actorId = 1;
  optional int32 num = 2;
}

message UgcExtraInfo {
  optional bool isCloudEditor = 1;       // 是否云编辑器
  optional int32 groupOccupyValue = 2;   // 组合的占用值
  repeated UgcGroupActorList groupActorList = 3;  // 构成组合的组件列表
  repeated float groupAabbCenter = 4;    // 组合包围盒参数
  repeated float groupAabbExtent = 5;    // 组合包围盒参数
  repeated int32 groupModTagList = 6;    // 组合专用玩法
  repeated EUgcEditOption groupEditOption = 7; // 组合可编辑选项
  repeated int64 separateFactor = 8;     // 分包因子 (分包下载功能使用)
  optional string resVersion = 9;        // 资源版本号
  optional int32 subType = 10;            // 子类型
  optional ENewUgcEditOption editOption = 11;  // 可编辑器权限（新）0=无，1=可以编辑，2=不可编辑
  optional UgcMapSize ugcMapSize = 12;  // 地图长宽高
  optional bool canUseInPayMap = 13;    // 资源可否用于付费地图
  optional bool saveFlag = 14;          // 存储标识
  optional bool hasGroupForbidUseInPayMap = 15; // 地图是否使用了禁止用于付费地图的模组
  optional bool openSingleSaveDB = 16;          // 单机模式存储标识  实际上是单机给开ds然后还给存档的标识
  optional int32 codeUseNum = 17; // 积木使用数量
  optional bool isUsingLua = 18;  //是否编程元件地图
  optional bool haveScripts = 19;  //是否拥有编程元件
  optional bool haveUI = 20;  //是否拥有UI
  optional int32 textureStlyeType = 21; // 纹理风格, 详情见枚举UgcResTextureStlyeType所示
  optional int32 voxelStyleType = 22;   // 体素风格, 详情见枚举UgcResVoxelStyleType所示
  optional int32 converType = 23;  //封面类型 0-模组 1-界面
}

message SpawnPointInfo {
  optional int32 sideId = 1;              // 出生点side
  optional int32 spawnIndex = 2;          // 出生点编号
}

message UgcDownloadInfo{
  optional string regin = 1;
  optional string bucket = 2;
  optional string key = 3;
  optional string version = 4; // cos版本号
  optional string ugc_id = 5;
  optional string ugc_md5 = 6; // 加密后的md5
  optional string ugc_file_name = 7;
  optional UgcKeyInfo key_info = 8;
  optional string preMsg = 9; // 加密前的md5
  optional string mapKey = 10;
  optional int32 instanceType = 11;
  optional string dsaDownloadJsonInfo = 12;
  optional int32 msgType = 13;
  optional int32 size = 14;
  optional int64 layerId = 15;
  optional string dsaJsonData = 16; // 打包json数据
}

message PublishItem{
  optional int64 ugcId = 1;  //地图id
  optional string name = 2;  //名字
  optional string desc = 3;  //描述
  optional int64 createTime = 4;  //发布时间
  repeated UgcMapMetaInfo metaInfo = 5; //信息
  optional string editorName = 6;  //作者名字
  optional int32 type = 7;  //类型
  optional string bucket = 8;  //bucket
  optional string region = 9;
  optional int64 oldUgcId = 10; //下载id
  optional string tags = 11;  //tags
  optional int32 pos = 12; //推荐的pos
  optional int32 pointsNumber = 13;  //出生点人数
  optional int64 likeCount = 14; //点赞数
  optional int64 playCount = 15; //游玩次数
  optional bool isCollect = 16; //是否收藏
  optional int64 uid = 17; //uid
  optional string editorProfile = 18; //作者头像
  optional int64 collectTime = 19; //收藏时间
  optional string openId = 20; //作者openid
  optional UgcExtraInfo extraInfo = 21; // 额外信息
  optional bool isOfficial = 22; // 是否官方作品
  optional SafeStatus reportStatus = 23;  //审核状态
  optional int64 playerCount = 24; //游玩人数
  optional string ugcVersion = 25;
  optional string clientVersion = 26;
  optional string mapKey = 27;
  optional int64 creatorId = 28;
  optional int32 accountType = 29;   // 账号类型
  optional UgcGroupIdList groupIds = 30; // 地图中使用到的发布组合
  repeated EditorItemInfo creators = 31;
  optional int64 updateTime = 32; //更新时间
  repeated BattleCamp camps = 33;
  optional int32 loadingTemplateId = 34;
  optional com.tencent.wea.xlsRes.UgcAuthType authType = 35;
  optional bool isNew = 36;
  optional int32 templateId = 37;
  repeated PlayerDressItemInfo editorDressItemInfos = 38;// 作者装扮信息(铭牌、称号、头像框、道具头像等)
  repeated UgcMapTopic topics = 39;
  optional int32 needReview = 40;  //是否需要审核 0.已审核通过 1.未审核 (国际化)
  optional bool isShareHotBan = 41; // 是否处于过热分享禁止游玩状态
  optional string hotContent = 42; // 热度 或者文字
  optional UgcResType ugcResType = 43; // 资源类型
  optional int32 resCategory = 44;    // 资源大类别
  optional int32 resSubCategory = 45; // 资源小类别
  repeated int32 resLabels = 46;      // 资源标签
  optional bool isInResBag = 47; //是否加入资源库

  optional int32 goodMark = 48;       //优秀标志
  optional string markString = 49;    //标志文本
  optional int32 passMark = 50;       //游玩/通关
  optional int64 passCount = 51;      //通关次数

  optional bool isEditable = 52; //是否可编程 （单纯记录 策划需求不明确 不与tags一起存放）
  optional UgcMapPublishGoodsStatus publishGoodsStatus = 53; //发布商品状态
  optional UgcMapBuyGoodsStatus buyGoodsStatus = 54; //buy商品状态
  optional bool isOpenSave = 55; // 是否开启存档
  optional int64 collectCount = 56; // 收藏数
  optional bool isAiGen = 57; // 是否是AI生成的
  optional UgcRecommendMap recommendInfo = 58;
  optional string prizeId = 59;
  optional string prizeInfo = 60;
  repeated MapCoverInfo covers = 61;   //多封面缩略图
  optional int32 difficulty = 62;     // 地图评级

  optional int32 modelType = 63;  //游戏模式  竞速还是团队
  optional string confDesc = 64;  //配置的介绍文本
  optional int32 playType = 65;  //对局方式。0=单人；1=多人
  optional bool isLuaCoding = 66;    // 是否使用Lua编程字段
  optional int32 ugcAchievementSize = 67;  // 成就个数
  repeated UgcLayerInfo layerInfo = 68; // 多场景场景信息
  optional int32 omdLevel = 69; //兽人关卡难度 OMDLevelType
  optional int64 passRate = 70; //通关率
  optional double passLevelRate = 71;  //通关率 保留两位小数
  optional int32 playRuleOMD = 72; // 兽人通关模式 1表示无尽
  optional int32 dataStoreSaveType = 73; // 数据存储类型 ENUM DataStoreSaveType
  optional MapCoverVideoInfo videoCover = 74;  // 视频封面
  repeated UgcMapExtraConfigIndex extraConfigIndexList = 75;  // 额外配置列表
  optional MapLoadingInfo mapLoading = 76;  // 自定义加载
}

message CreatorAccountInfo {
  optional com.tencent.wea.xlsRes.UgcAuthType authType = 1;
  optional string authDesc = 2;
}

enum MdType {
  CoverPng = 1;   //缩略图
  LevelData = 2;  //关卡
  EditorData = 3; //编辑
  GroupData = 4; //组合
  CoverASTC = 5;  //缩略图(ASTC)
  GroupPng = 6;   // 组合缩略图
  GroupASTC = 7;  // 组合缩略图(ASTC)
  LayerData = 8;  // 图层
  CoverASTCLarge = 9; //缩略图(封面分辨率提升)
  LayerDataByZip = 10;  //图层数据zip格式
  LevelDataByZip = 11;  //关卡数据zip格式
  ResourceZip = 12; // 资源数据zip格式
  LevelResByZip = 13; // 地图资源数据zip格式
  ResourceResZip = 14;  //云资产数据
  //多场景扩展
  MapDescription = 15;  //地图描述
  EditorMapDescription = 16;  //编辑模式地图描述
  MapResByZip = 17;  //地图资源zip格式
  // 视频mp4格式
  CoverMP4 = 18;  // 封面mp4格式
  CodingJson = 19;	// 扣叮json文件
  CodingLua = 20;	// 扣叮lua文件
  ResourceModelResZip = 21; // 模型资产资源数据zip格式
}

enum SaveType {
  ManualSave = 1;   // 手动保存
  AutoSave = 2;   // 自动保存
  CreateSave = 3;   // 创建存档

}

enum DataStoreSaveType {
  NoSave = 0;   // 不存档
  DsSave = 1;   // 多人存档  实际上看 isOpenSave
  DsAndSingleSave = 2;   // 单人多人存档
}

message TopRankInfo {
  optional int64 uid = 1;
  optional int32 rank_no = 2;
  repeated int32 score = 3;
  optional int64 last_update_ts = 4;
  optional int64 creator_id = 5;          // ugc工匠值专用
  optional int32 up_or_down = 6;
}

message RankId {
  optional int32 id = 1;                // 参考RankingConf
  optional int32 apolloId = 2;          // 赛季切换机制相关
  optional int32 subId = 3;             // 地区相关
  optional int32 subType = 4;           // 地区相关
}

message UgcCreatorRankId {
  optional int32 rank_type = 1;       // 榜单类型，已废弃
  optional int32 map_type = 2;        // 地图类型，已废弃
  optional string creator_area = 3;   // 创作者地区
  optional int64 uid = 4;             // 玩家ID
  optional int32 season_id = 5;       // 赛季ID
  optional string env = 6;            // 请求游戏环境
  optional bool user_info_not_need = 7;
  optional uint32 top_count = 8;
  optional uint32 login_type = 10;
  optional int32 int32_rank_type = 11;          // 榜单类型
  optional int32 int32_map_type = 12;          // 地图类型
}

message UgcCreatorRankInfo {
  optional string id = 1;
  optional string nick_name = 2;
  optional string avatar_url = 3;
  optional uint64 growth_value = 4;
  optional uint32 rank = 5;
  optional string rank_logo_url = 6;
  optional string full_avatar_url = 7;
  optional string creator_uid = 8;
}

message UgcCreatorRankList {
  optional int32 rank_type = 1;
  repeated UgcCreatorRankInfo creator_rank_info_list = 2;
  optional UgcCreatorRankInfo self_rank_info = 3;
  optional int32 int32_rank_type = 4;
}

message RedisRankListKey {
  optional RankId rankId = 1;
  optional TopRankBackendType backend = 2;
  optional bool image = 3;
}

message UgcTaskInfo {
  optional int32 lvTaskId = 1;     // 主等级任务id
  optional int32 subLvTaskId = 2;  // 子等级任务id
  optional int32 finish = 3;       // 是否完成
  optional int32 received = 4;     // 是否领取
}

message UgcTasks {
  repeated UgcTaskInfo taskList = 1; // ugc 任务 , 未启用
  optional int32 peakPlayUv = 2;
  optional int32 totalPlayUv = 3;
}

enum UgcOpType {
  GiveALikeUgc = 1; //点赞
  CollectUgc = 2;   //收藏
  SubscribeUgc = 3; //订阅
  ShareUgc = 4;     //分享
}

// 历史原因，这里需要做 UgcMapScreenType -> UgcRecommendType 映射
enum UgcRecommendType {
  YM5 = 1;  //元梦五项
  EdenRecommendTotal = 2; //乐园综合推荐
  EdenRecommendNew = 3; //乐园最新推荐
  EdenHot = 4; //乐园热⻔榜
  EdenCollect = 5; //乐园收藏榜
  EdenTopic = 6; //乐园主题榜
  Eden7 = 7; //快速多排
  Eden8 = 8; //相似推荐
  EdenGiveLike = 9; // 组合点赞排序
  EdenOfficialGroup = 10; // 官方组合
  EdenGroupRecommendTotal = 11; // 组合综合推荐
  EdenSubscribePlayerMaps = 12; // 我的订阅
  EdenUgcRoomPopularity = 13;  // 房间大厅人气
  EdenUgcRoomCollect = 14;  // 房间大厅收藏
  EdenUgcBattleSettlementRecommend = 15;  // 结算地图推荐
  EdenUgcResRefSort = 16; // 资源引用最多排序
  EdenLastSeasonHot = 17; // 上赛季必玩
  EdenAllTimeHot = 18;  // 名图堂
  EdenAllTimeHotNew = 19;  // 新版名图堂
  EdenRecommendTheme = 20;  // 精选合集
  EdenWeeklyHot = 21;  // 每周必玩
  EdenMonthlyHot = 22;  // 每月必玩
}

enum UgcMapScreenType {
  Recommend = 1;    //推荐
  Latest = 2;       //最新
  Popularity = 3;   //人气
  collect = 4;      //收藏
  UgcMapScreenTopic = 5;        // 主题
  UgcMapScreenQuicksort = 6;    // 快速多排
  UgcMapScreenSimilar = 7;      // 相似推荐
  UgcMapScreenGiveLike = 8;     // 组合点赞排序
  UgcMapScreenOfficialGroup = 9;   // 官方组合
  UgcMapScreenGroupRecommand = 10; // 组合综合推荐
  UgcSubPlayerMaps = 11;  //  我的订阅
  UgcHotPlay = 12; // 大家都在玩, 只是作为切换标签枚举
  UgcBattleSettlementRecommend = 13; // 结算推荐
  UgcResRefSort = 14; // 资源引用最多排序
  LastSeasonHot = 15; // 上赛季必玩
  AllTimeHot = 16;  // 名图堂
  AllTimeHotNew = 17;  // 新版名图堂
  RecommendTheme = 18;  // 精选合集
  WeeklyHot = 19;  // 每周必玩
  MonthlyHot = 20;  // 每月必玩
}

enum UgcRoomLobbyMapType {
  URLMT_Popularity = 1;   //人气
  URLMT_Collect = 2;      //收藏
  URLMT_Recommend_Official = 3;  //官方推荐
  URLMT_Recommend_Members = 4; // 房间成员推荐
}

message UgcOpPlayerInfo {
  optional int64 uid = 1;        // uid
  optional string avatar = 2;    // 玩家图片
  optional string name = 3;      // 玩家名字
  optional int32 fansCount = 4; // 订阅粉丝数量
  optional int32 isTop = 5;     // 是否置顶
  optional int64 topTime = 6;   // 置顶时间
  optional bool  isMutual = 7;   // 是否互相订阅
  optional bool  isNew = 8;
  optional string openId = 9;
  optional int64 creatorId = 10;
  optional int32 accountType = 11;   // 账号类型
  repeated PlayerDressItemInfo dressItemInfos = 12;// 装扮信息(铭牌、称号、头像框、道具头像等)
  optional int32 gender = 13;
  optional int64 redDotTime = 14;
  optional int32 publishMapCount = 15;
  optional com.tencent.wea.xlsRes.UgcAuthType ugcAuthType = 16;
}

message MapMatchInfo {
  optional int32 roomInfoId = 1;  // -1为未开启匹配
  optional int64 openTime = 2;  // 开始时间
  optional int64 closeTime = 3;  // 结束时间
}

// ugc版本下通关数据
message UgcVersionPassData {
  optional int64 passRate = 1;			  // 通关率
  optional double passLevelRate = 2;	// 通关率, 保留两位小数
  optional int64 bestRecord = 3;		  // 最佳记录
  optional bool isVersionData = 4;	  // 是否是版本下数据
  optional string passName = 5;		    // 通关名称
  optional string passAvatar = 6;			// 通关时间
  repeated PlayerDressItemInfo passDressItemInfos = 7; // 通关装扮信息
  optional int64 playDuration = 8;    // 游玩时长
}

// 版本下通关记录数据
message UgcVersionPassRecordData {
  optional UgcBestRecord wxRecord = 1; 	          // 微信区最佳记录
  optional UgcBestRecord qqRecord = 2;        	  // qq区最佳记录
  optional UgcBestRecord ugcAppRecord = 3;        // 独立app最佳记录
  optional UgcPlayRecord wxPlayRecord = 4;        // 微信区游玩记录
  optional UgcPlayRecord qqPlayRecord = 5;        // qq区游玩记录
  optional UgcPlayRecord ugcAppPlayerRecord = 6;  // 独立app游玩记录
  optional int64 operateTime = 7;                 // 操作时间
  optional int64 resetVersion = 8;                // 数据重置版本
  optional int64 passTotalSec = 9;                // 通关总时长, 仅用于版本重置的时候存储历史数据
  optional int32 passTotalCount = 10;             // 通关总次数, 仅用于版本重置的时候存储历史数据
}

message MapDetails {
  optional int64 likeCount = 1; //点赞数
  optional int64 playCount = 2; //游玩次数
  optional string editorAvatar = 3; //作者头像
  optional string editorName = 4; //作者名称
  optional int64 bestRecord = 5; //最佳记录
  optional string passName = 6; //通关名字
  optional string passAvatar = 7; //通关头像
  optional int64 passRate = 8; //通关率
  optional int64 collectCount = 9; //收藏
  optional int64 subscribeCount = 10; //订阅人数
  optional bool isLike = 11; //是否点赞
  optional bool isCollect = 12; //是否收藏
  optional bool isSub = 13; //是否订阅
  optional int32 pointsNumber = 14;  //出生点人数
  optional double passLevelRate = 15;  //通关率 保留两位小数
  optional int64 playerCount = 16; //游玩人数
  optional int32 difficulty = 17;     // 地图评级
  optional int64 shareCount = 18;     // 分享数

  optional string mapName = 19;     // 名字
  optional string mapDesc = 20;     // 描述
  optional int32 templateId = 21;     // 模板id 读表获取地图类型
  optional string tags = 22;     // 标签
  optional int64 updateTime = 23;     //时间
  optional string mapCoverPng = 24;     //（废弃）
  repeated UgcMapMetaInfo metaInfo = 25; //信息
  repeated EditorItemInfo creators = 26;
  repeated int64 separateFactor = 27;   // 分包因子 (分包下载功能使用)
  repeated UgcMapTopic topics = 28;
  optional SafeStatus reportStatus = 29;  //审核状态
  repeated PlayerDressItemInfo editorDressItemInfos = 30;// 作者装扮信息(铭牌、称号、头像框、道具头像等)
  repeated PlayerDressItemInfo passDressItemInfos = 31;// 最佳记录者装扮信息(铭牌、称号、头像框、道具头像等)
  optional UgcGroupIdList groupIds = 32; // 地图中使用到的已发布组合
  optional com.tencent.wea.xlsRes.UgcAuthType authType = 33;
  optional int64 uid = 34;
  optional int64 creatorId = 35;
  optional int32 gender = 36; //作者性别  1-男 2-女 0-未知
  optional int32 loadingTemplateId = 37;
  optional string clientVersion = 38;
  optional bool isInResBag = 39;
  optional MapMatchInfo mapMatchInfo = 40;  // 地图匹配信息

  optional int64 createTime = 41;     //创建时间
  optional int64 playDuration = 42;   //游玩时长
  optional int32 goodMark = 43;       //优秀标志
  optional int32 passMark = 44;       //游玩/通关
  optional string markString = 45;       //标志文本
  optional bool isEditable = 46; //是否可编程 （单纯记录 策划需求不明确 不与tags一起存放）
  optional bool isOpenSave = 47; //是否开启存档
  optional UgcMapBuyGoodsStatus buyGoodsStatus = 48; // 购买星钻商品状态
  optional UgcMapSize danMuBlockCalcSize = 49;  // 弹幕区块计算
  repeated UgcRankInfo rankInfo = 50;      // 排行榜基本信息
  optional UgcMapLabelScoreDetail labelScoreInfo = 51;
  repeated MapCoverInfo covers = 52;
  optional bool saveFlag = 53;
  optional string bucket = 54;  //bucket
  optional string region = 55;
  optional bool openSingleSaveDB = 56;   // 单机模式存储标识  实际上是单机给开ds然后还给存档的标识
  optional string prizeId = 57;
  optional string prizeInfo = 58;
  optional int32 dataStoreSaveType = 59; // 数据存储类型  enum DataStoreSaveType
  optional int32 hasPublishGoodsRecord = 60;   // 0 没法过 1 发过

  optional int32 modelType = 61;  //游戏模式  竞速还是团队
  optional int32 playRuleOMD = 62;  // 兽人类型
  optional int64 lastEndBattleTime = 63;  // 上一次结算时间
  optional int32 achievementSize = 64;  // 成就个数
  optional int32 achievementDone = 65;  // 玩家已完成的成就个数
  optional OmdLevelType omdLevel = 66; //兽人关卡难度
  optional UgcVersionPassData versionPassData = 67;  // ugc版本下通关数据
  repeated UgcMapExtraConfigIndex extraConfigIndexList = 68;  // 额外配置列表
  repeated BattleCamp camps = 69;  // 阵营信息
  optional MapLoadingInfo mapLoading = 70;  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 71;  // 乐园封面
  optional string qqMusicInfo = 72; // qq音乐存储在服务器 废弃
  optional MapCoverVideoInfo videoCover = 73;  // 视频封面
  repeated UgcBGMInfo bgmInfo = 74; // ugc背景音乐设置，最大5个，数组索引表示顺序
  optional bool isAllowMidJoin = 75;  // 允许中途加入
}

message UgcMapLabelScoreDetail{
  repeated UgcMapDimLabelScoreDetail dimScoreDetail = 1;
  optional int64 scorePlayerCount = 2;
  optional string comScoreContent = 3;  // 综合评价标签
  optional string dimScoreContent = 4;  // 最高维度评价标签
  optional int32 comScoreLevel = 5; // 综合评价标签档位，值越大档位越高
}
message UgcMapDimLabelScoreDetail{
  optional int32 label = 1;
  optional float score = 2;
}

message BattleLevelEventDataForArena{
  optional int32 cardQualityChroma = 1; // 单局获得橙色卡牌的数量
  optional int32 totalDamage = 2; // 单局输出伤害
  optional int32 totalAssist = 3; // 单局助攻数
  optional int32 totalKill = 4; // 单局击杀数
  optional int32 isMvp = 5; // 是否Mvp
  optional int32 useItemTimes = 6; // 单局使用道具次数
  optional int32 destroyObjectTimes = 7; // 单局摧毁可破坏物次数
  optional int32 pentaKillTimes = 8; // 单局五杀次数
  optional int32 doubleKillInOneRoundTimes = 9; // 单局内单回合双杀次数
  optional int32 surviveInPoisonMaxSeconds = 10; // 玩家从进入毒圈到活着离开毒圈的最长持续秒数
  optional int32 sunceCarCarryOtherPlayersTimes = 11; // 单局孙策马车带过其他玩家次数
  optional int32 yaoAddShieldTimes = 12; // 单局瑶附身过对面玩家(给其他玩家加盾)次数
  optional int32 win1V2AfterAllyDeadTimes = 13; // 单局队友阵亡后1V2翻盘次数
  repeated int32 usedCards = 14;   // 单局使用的卡牌列表
  optional int32 tripleKillTimes = 15; // 单局三杀次数
  optional int32 pickHealthKitTimes = 16; // 拾取血瓶次数
  optional int32 totalRoundNumber = 17; // 总回合数
  repeated int32 roundResults = 18; // 每回合胜负结果
  optional int32 selfEliminatedTimes = 19; // 自己被击倒的次数
  optional int32 win1V3AfterAllyDeadTimes = 20; // 队友阵亡后1V3翻盘次数
  repeated int32 roundKillNumbers = 21; // 每回合的杀人数
  optional int32 remainScoreOfNo1Team = 22; // 第一名队伍剩余积分
  optional int32 lowPercentKillOtherPlayersTimes = 24; // 单局内在血量低于10%的情况下击杀别人的数量
  optional int32 isLoseMvp = 25; // 是否败方Mvp
  optional int32 legendaryTimes = 26; // 超神次数
  optional int32 rescueTeammateTimes = 27; //累计救助队友次数
  optional bool isWin = 28;     // 是否获胜
  optional int32 heroId = 29;   // 英雄ID
  optional bool isAfk = 30;   // 是否挂机
  optional bool isTeamMatch = 31;   // 是否组队 1是 0否
}

// 玩法事件上报
message GameplayEventData {
  optional int32 type = 1;
  repeated KeyValueInt64 value = 2; // 上报事件特征值 kv形式
  optional int64 num = 3;  // 上报数量
  optional string serialId = 4; // 序列号
  optional int64 timeMs = 5; // 时间戳
}

// 月卡信息
message MonthCardBriefInfo {
  optional string id = 1;           // 月卡id
  optional int64 expireTimeMs = 2;  // 月卡过期时间
  optional int32 cumDays = 3;       // 累计天数
}

message BattleLevelEventData {
  optional int32 levelId = 1;       // 关卡ID
  optional int32 levelType = 2;     // 关卡类型ID
  optional int32 atRound = 3;       // 轮次
  optional int32 matchType = 4;     // 玩法ID
  optional bool pass = 5;           // 本关通过
  optional int64 elapsedTime = 6;   // 本关耗时(ms)
  optional int32 outInTeam = 10;    // 个人队内第几个出局
  optional int32 survivalTime = 14; // 存活时间(ms)
  repeated KeyValueInt64 stat = 20; // 通用DS统计数据口径
  optional bool isSinglePlay = 21;   // 是否单人游玩模式(ugc分单人游玩和多人游玩)
  optional int32 competitionSeason = 22;// 赛事赛季ID 0则不是赛事
  optional int32 competitionGameType = 23;//赛事对局类型 CsCompetition.CompetitionGameType
  optional bool isGiveUp = 24; // 主动退出
  optional bool isTeamMatch = 25; // 是否组队
  optional int32 rank = 26; // 自己的名次
  optional int32 teamRank = 27; // 团队排名
  optional int32 rankInTeam = 28; // 玩家在自己队伍中排名
  optional int32 battlePlayersCnt = 29; // 总玩家数
  optional int32 jsSingleCircleTime = 30;   // 飞车本关最佳单圈耗时(ms)
  repeated int64 roomMember = 31;    // 组队玩家UID
  optional BattleLevelEventDataForArena arena_data = 32; // Arena关卡结算数据
  optional bool randEvent = 33; // 是否触发随机事件
}

message BattleEventData {
  optional int32 matchType = 1;     // 玩法ID
  repeated int32 levelIds = 2;      // 关卡ID
  optional bool isWin = 3;          // 是否胜利
  optional bool isMVP = 4;          // 是否MVP
  repeated KeyValueInt64 stat = 5;  // 通用DS统计数据口径
  repeated int64 roomMember = 6;    // 组队玩家UID
  optional int64 battleCreateTime = 7;
  repeated int64 roomFriend = 8;    // 组队好友UID
  optional bool isSinglePlay = 9;   // 是否单人游玩模式(ugc分单人游玩和多人游玩)
  optional int32 competitionSeason = 10;// 赛事赛季ID 0则不是赛事
  optional int32 competitionGameType = 11;//赛事对局类型 CsCompetition.CompetitionGameType
  optional int32 competitionRank = 12;//对局最终排名
  optional int32 competitionScore = 13;//赛事积分
  optional bool isGiveUp = 14; // 主动退出
  optional int64 battleCostTime = 15;
  optional int32 competitionType = 16;//赛事类型 CompetitionType
  optional bool ignoreFinishBattleEventTask = 17;
  repeated int32 reputationBehaviorTypeIds = 18;//玩法中触发的行为ID
  optional int64 ugcId = 19;
}


message ShopCommodityBuyEventData {
  optional int32 shopType = 1;       // 商城type
  optional int32 commodityId = 2;    // 商品ID
  optional int32 buyNum = 3; //购买数量
}


message NR3E8EventData{
  optional int32 buildingId = 1;
  optional int32 buildingType = 2;
  optional int32 dice1 = 3;
  optional int32 dice2 = 4;

}

// 抽奖完成事件结构
message DrawRaffleFinishData {
  optional int32 poolId = 1;        // 奖池ID
  optional int32 drawCount = 2;     // 抽奖次数
  optional ItemArray rewards = 3;  // 奖励信息
}

message LobbyActionEventData {
  optional int64 hugPlayerCnt = 1;  // 抱人次数
  optional int64 rideCarouselCnt = 2; // 旋转木马次数
  optional int64 stackPlayerCnt = 3;  // x人抱抱乐
  optional int64 playActionCnt = 4; // 单人播放动作次数
  optional int64 rideBalloonCnt = 5;  // 乘坐热气球次数
  optional int64 findLobbyEggCnt = 6; // 找到彩蛋次数
  optional int64 doubleInteractionCnt = 7; // 双人互动动作次数
  optional int64 playExpressionCnt = 8; // 播放表情次数
  optional int64 seesawWithOthers = 9; // 与他人乘坐跷跷板  同乘人是否是好友 1:好友 0:非好友
  optional int64 sitOnBench = 10; // 与他人共坐多人座椅 同坐人是否是好友 1:好友 0:非好友
  optional int64 onBalanceBallPlayerCnt = 11; // 平衡球乘坐人数
  optional int64 onBalanceDurationTime = 12; // 平衡球持续时间 秒
  optional int64 playPirateShipCnt = 13; // 乘坐海盗船次数
  optional int64 sitOnBenchPlayerCnt = 14; // 同坐多人座椅人数
  optional int64 getLuckyCnt = 15; //祈福抽签次数
  optional int64 beachChairCnt = 16; //泳池躺椅次数
  optional int64 swimPoolSlideCnt = 17; //泳池滑梯次数
  optional int64 waterBallCnt = 18; //泳池水球次数
  optional int64 useTeleportCnt = 19; //传送门次数
  optional int64 onBalanceBallCnt = 20; //使用平衡球次数
  optional int64 onBalanceBallWithOthers = 21; //与他人共乘平衡球 同乘人是否是好友 1:好友 0:非好友
  optional int64 findEggKindCnt = 22; //获得彩蛋种类数量
  optional int64 useFireworksCnt = 23; //放烟花次数
  optional int64 seesawCreateHeart = 24; //双人跷跷板触发爱心特效


  optional int64 npcTalkOneTimeCnt = 25; //大厅与NPC对话一次
  optional int64 npcTalkOneStepCnt = 26; //大厅与NPC对话一阶段
  optional int64 playBlockGrabCnt = 27; //大厅游玩积木
  optional int64 getXiaoXinCarCnt = 28; //大厅游玩小新三轮车
  optional int64 getBubbleGunCnt = 29; //大厅获得一次泡泡枪
  optional int64 playGunBubbleCnt = 30; //大厅进入一次梦奇泡泡

  optional int64 waterBallGoalCnt = 31; //大厅泳池水球进球一次
  optional int64 rideCarouselShare = 32; //与他人共乘旋转木马
  optional int64 basketballHitCnt = 33; // 篮球机连续投中篮球次数
  optional int64 archeryHitCnt = 34; // 射箭机连续射中4分次数

  optional int64 playBeachChairLongTime = 35; // 大厅长时间使用泳池躺椅
  optional int64 playNewYearCallCnt = 36;  // 玩家在大厅和对局内拜年次数
  optional int64 npcTalkDialogIdCnt = 37; // 大厅与NPC对话特定dialog一次
  optional int64 useGameplayPropId = 38; // 使用的玩法道具id
  optional int64 playFestiveLanternCnt = 39; // 参与一次花灯玩法
  optional int64 interactWithSpecificMapEntrance = 40; // 与指定地图入口id交互一次
  optional int64 ridingDragon = 41; // 广场骑龙一次
  optional int64 ultramanDamageMonster = 42; // 对芝顿造成伤害
  optional int64 ultramanUseItem = 43; // 使用赛罗眼镜道具一次
  optional int64 ultramanChat = 44; // 与胜利队员对话一次
  optional int64 getLuckyPrayDayCnt = 45; //祈福抽签天数
  optional int64 getSpringPrayCnt = 46; //春节财神祈福次数

  optional int64 miniGameConsoleSwitchOP = 47; //广场小游戏状态
  optional int64 useLobbyItemId = 48; // 使用广场道具id
  optional int64 interactionOtherPlayerUid = 49; //互动的其他玩家的uid
  optional int64 lobbyTransformation = 50; //广场三丽鸥区域参加变形玩法
  optional int64 lanternReachMaxLevel = 51; // 花灯达到最高等级

  optional int64 partnerActivityEnterDance = 52;  // 搭子活动进入舞蹈状态
  optional int64 partnerActivityCompleteDance = 53;  // 搭子活动进入完成舞蹈挑战

  optional int64 lobbyPutUpLucky = 54; //广场贴福字
  optional int64 lobbyHappyNewYear = 55; //广场拜年
  optional int64 lobbyIncensePrayed = 56; //钱王祠香炉祈愿


  optional int64 lobbyPutUpLuckyShow = 57; //广场贴福字表演
  optional int64 lobbyHappyNewYearShow = 58; //广场拜年表演
  optional int64 lobbyPlayFestiveLanternShow = 59; //花灯玩法表演

}

message ArenaCardEventData {
  optional uint32 heroId = 1;                      // 英雄ID
  optional int32 newCardId = 2;                    // 本次解锁的卡牌ID
  optional uint32 numberOfCardsUnlocked = 3;       // 已获取卡牌的数量
  optional bool hasUnlockedAllCards = 4;           // 是否获取了所有卡牌
  optional uint32 numberOfBlueCardsUnlocked = 5;   // 已获取蓝卡的数量
  optional bool hasUnlockedAllBlueCards = 6;       // 是否获取了所有蓝卡
  optional uint32 numberOfPurpleCardsUnlocked = 7; // 已获取紫卡的数量
  optional bool hasUnlockedAllPurpleCards = 8;     // 是否获取了所有紫卡
  optional uint32 numberOfChromaCardsUnlocked = 9; // 已获取橙卡的数量
  optional bool hasUnlockedAllChromaCards = 10;    // 是否获取了所有橙卡
}

message ArenaHeroEventData {
  optional int32 newHeroId = 1; // 本次解锁的英雄ID
}

message GameTimesStatEventData {
  optional com.tencent.wea.xlsRes.PlayerGameDataStatType type = 1; // 变更数据类型
  optional int64 oldValue = 2; // 变更前的数据值
  optional int64 newValue = 3; // 变更后的数据值
  optional int32 gameType = 4; // 玩法类型
}

message ArenaHeroStarLevelEventData {
  optional int32 heroId = 1;          // 英雄ID，0代表总进度等级
  optional int32 beforeLevel = 2;     // 原等级
  optional int32 afterLevel = 3;      // 最新等级
}

message ArenaHeroStarGeneralLevelEventData {
  optional int32 beforeLevel = 1;     // 原等级
  optional int32 afterLevel = 2;      // 最新等级
}

message ArenaHeroStarNumEventData {
  optional int32 heroId = 1;          // 英雄ID，0代表总奖章数
  optional int32 beforeNum = 2;       // 原奖章数
  optional int32 afterNum = 3;        // 最新奖章数
}

enum UgcOpenLevelType {
  Draft = 1;       //草稿
  Release = 2;     //发布
  Single = 3;      //单人
  MultiPlayers = 4;//多人游玩\
  DraftPublish = 5;//草稿箱已发布
}

enum UgcMapGenerateType {
  CreateMap = 1;       //生成
  CopyMap = 2;         //复制
  PublishMap = 3;      //发布
  PublishCopyMap = 4;  //发布复制
  SaveMap = 5;  //保存
  UgcMapGiveLike = 6;  //点赞
  UgcMapCollect = 7;  //收藏
  UgcMapSub = 8;  //收藏
  UgcMapCancelGiveLike = 9;  //取消点赞
  UgcMapCancelCollect = 10;  //取消收藏
  UgcMapCancelSub = 11;      //取消收藏
  TogetherCreateMap = 12;    //共创
  AutoSaveMap = 13;  //自动保存
}

enum UgcIdType {
  PublishId = 1;       //发布id
  CopyId = 2;          //copyId
  UniqueId = 3;        //uniqueId (举报)
  GroupId = 4;        //组合id
  CoverId = 5;        //封面图id
  LoadingId = 6;      // 自定义loading图
  LobbyCoverId = 7;   // 乐园地图自定义封面
}

enum UgcInstanceType {
  UnknowInstance = 0;
  CommonInstance = 1;       // 地图包含共创地图 后续策划需求升级为共创地图，任何地图都可以升级为共创地图
  CoCreateInstance = 2;     // 只有服务器使用
  GroupInstance = 3;        //组合
  HomeInstance = 4;         //小窝
  LayoutInstance = 5;       //方案
  NpcInstance = 6;          // NPC类型
  ResInstance = 7;          // 资源类型
}

message UgcEditorList{
  repeated UgcEditorInfo creator = 1;
}

message UgcEditorInfo{
  optional int64 creatorId = 1;
  optional UgcEditorType type = 2;
  optional int64 uid = 3;
  optional int32 publishEditorType = 4;   // 发布态草稿编辑权限类型，仅在type字段取值为2或者3时生效, 0值表示无权限, 非0值见枚举UgcEditorType所示
}

message UgcLayerList{
  map<int64, UgcLayerInfo> layer = 1;
}

message UgcLayerInfo{
  optional int64 layerId = 1;
  repeated int64 creator = 2;  //废弃
  optional string layerName = 3;
  optional int64 creatTime = 4;
  optional int64 updateTime = 5;
  optional string desc = 6;
  optional int32 pointNumber = 7;
  optional int64 templateId = 8;
  optional int32 layerType = 9;  //UgcLayerInfo
  repeated BattleCamp camps = 10;     // 地图阵营信息
}

message UgcAchievementInfo {
  optional int64 ugcId = 1;  // 当前成就的编辑索引
  optional int32 verId = 2;  // 版本id
  optional int32 type = 3;  // 类型 0:默认;1:已发布
}

message UgcAchievementMap {
  map<int32, UgcAchievementInfo> achCfgMap = 1;
  optional bool isBan = 2;  // 是否被封掉了
}

message UgcSceneBriefInfo {
  optional int64 ugcId = 1;  //地图id
  repeated UgcLayerInfo layerInfo = 2;
}

message UgcBriefInfo {
  optional int64 ugcId = 1;  //地图id
  optional string name = 2;  //名字
  optional string desc = 3;  //描述
  optional int64 createTime = 4;  //发布时间
  optional string editorName = 5;  //作者名字
  optional int32 templateId = 6;  //模板Id
  optional int64 oldUgcId = 7; //下载id
  optional string tags = 8;  //tags
  // 下载用数据
  optional int32 size = 9; // 废弃
  optional string md5 = 10; // 废弃
  optional UgcMapMetaInfo levelMetaInfo = 11; // 废弃
  optional UgcMapMetaInfo coverMetaInfo = 12; // 废弃
  //
  optional string editorAvatar = 13;  //作者头像
  repeated BattleCamp camps = 14; // 地图阵营分配设置
  // cos相关
  optional string bucket = 15; // 废弃
  optional string region = 16; // 废弃

  optional int32 ugcType = 17; // ugc类型，UGCMapType
  optional int32 mapSource = 18;

  repeated EditorItemInfo creators = 19;  // 共创作者信息
  repeated int64 separateFactor = 20;     // 分包因子

  optional UgcMdList mdList = 21;         //mdList
  optional CreatorAccountInfo creatorAccountInfo = 22;
  optional int32 loadingTemplateId = 23;
  repeated UgcMapTopic topics = 24;
  optional string clientVersion = 25;

  optional bool isCampsOpen = 26; //阵营是否开启
  optional UgcMapBuyGoodsStatus buyGoodsStatus = 27; // 购买星钻商品状态
  optional string fromCollectionId = 28;
  optional bool isOpenSave = 29; // 是否开启存档
  optional int32 logicMapSource = 30;
  optional bool openSingleSaveDB = 31;          // 单机模式存储标识  实际上是单机给开ds然后还给存档的标识
  optional bool isPrivateState = 32; // 是否私创状态
  optional string compName = 33;  // 匹配的图集名
  optional bool isMainTenance = 34;  // 是否维护中
  optional int32 markId = 35; //优秀markId
  optional string prizeId = 36;
  optional string prizeInfo = 37;
  optional string markString = 38;       //标志文本
  optional bool isAllowMidJoin = 39;  // 允许中途加入
  optional int64 editorCreatorId = 40;  //作者creatorId
  optional UgcMatchMapBriefFromRemote_Server_BirthInfo birthInfo = 41;
  optional int64 uid = 42;  // 创作者的uid
  optional string roundLoadingDesc = 43; // 轮次加载描述-多轮对局时使用
  optional MapLoadingInfo mapLoading = 44;  // 自定义loading信息
  repeated UgcBGMInfo bgmInfo = 45; // ugc背景音乐设置，最大5个，数组索引表示顺序
  optional UgcPublishInputParam publishParam = 46;  // 发布参数
  repeated UgcLayerInfo layerInfo = 47;

}

message UgcMapMidJoinSetting {
  optional int64 closeAfterBattleStart = 1;  // 开局后N秒关闭
  optional int64 closeBeforeBattleFinish = 2;  // 对局不足N秒前关闭
  optional bool newMidJoinVersion = 3;  // 新版本中途加入标记，新的中途加入的地图需要带上这个标记，不管是否设置时间
}

message TeamQuickJoinRoomUgcInfo {
  optional int64 ugcId = 1;
  optional string name = 2;
}

enum TeamJoinRoomExtraInfoType {
  TJREIT_UNKNOWN = 0;
  TJREIT_QuickJoinInfo = 1;  // 快熟加入
  TJREIT_DirectJoin = 2;  // 直接整队加入
}

message TeamQuickJoinRoomInfo {
  optional bool isQuickJoin = 1;  // 是否快速加入
  repeated int64 params = 2;  // 透传参数
  optional RoomType roomType = 3;  // 参考RoomType
  optional bool wentMidJoin = 4;
  optional TeamQuickJoinRoomUgcInfo ugcInfo = 5;
}

message TeamJoinRoomDirectInfo {
  optional bool allowMidJoin = 1;
}

message TeamJoinRoomExtraInfo {
  optional int32 type = 1;  // TeamJoinRoomExtraInfoType
  optional TeamQuickJoinRoomInfo quickJoinInfo = 2;  // 类型为 TJREIT_QuickJoinInfo
  optional TeamJoinRoomDirectInfo directInfo = 3;  // 类型为 TJREIT_DirectJoin
}

message UgcGoodsInfo{
  optional string goodsId = 1;     // 商品ID
  optional int32 uintPrice = 2;    // 价格
  map<string, int32> items = 5;    // 物品 id/num
}

enum UgcRankSortType {
  Ascend = 0; // 升序
  Descend = 1; // 降序
}

enum UgcRankResetType {
  NoReset = 0; // 不重置
  Daily = 1;  // 每日重置
  Weekly = 2; // 每周重置
  Monthly = 3; // 每月重置
}

enum UgcRankScoreType {
  Duration = 0; // 持续时长 单位为秒
  CountDown = 1;  // 倒计时
  NormalScore = 2; // 普通展示 表示次数 个数 等
}

message UgcRankInfo{
  optional int64 rankId = 1;     // rank ID
  optional string rankTitle = 2;    // 排行榜名字
  optional string scoreTitle = 3;    // 排行榜名字
  optional int32 sortType = 4; // enum -> UgcRankSortType
  optional int32 showNum = 5;    // 显示数量
  optional int32 resetType = 6;    // enum -> UgcRankResetType
  optional bool singlePlayRecord = 7; // 是否单人记录
  optional int32 scoreType = 8;     // 分数类型   enum UgcRankScoreType
}

message PageMapInfo {
  optional int64 mapId = 1;      //地图ID
  optional int32 pos = 2;      //位置
  optional int32 needReview = 3;  //是否需要审核 0.已审核通过 1.未审核 (国际化)
}

message HotPlayList {
  optional int32 type = 1;      // 0玩法大类  1玩法标签  2 蓝色话题 3 金色话题
  optional int32 typeId = 2;  //对应id
  optional string typeText = 3;  //文本or话题

}

message AlgoInfo {
  optional string recId = 1;
  optional string expTag = 2;
}

// ugc个人信息页用的这个结构.之前的可能会干掉了.
message UgcPlayerProfile {
  optional string nickname = 1; //昵称
  optional int32 gender = 2; //性别  1-男 2-女 0-未知
  optional string profile = 3; //头像url (maybe)
  optional uint64 uid = 4; //uid
  optional uint64 shortUid = 5; //短uid
  optional int32 ugcLv = 6; // 工匠值等级
  optional int32 fansCount = 8; //粉丝数量
  optional int32 subCount = 9; //订阅数量
  optional int32 beLikeCount = 10; //被点赞数量
  optional int32 playCount = 11; // 游玩数量
  optional int32 publishCount = 12; //发布数量
  optional int32 passCount = 13; //通关数量
  optional int32 totalUgcExp = 14; // 总工匠值
  optional string openId = 15; //
  optional int32 seasonUgcExp = 16; // 当前赛季工匠值
  repeated int32 dressId = 17; // 装扮id
  optional int32 loginType = 18;
  optional PlayerRankGeoInfo rankGeoInfo = 19;
  repeated PlayerDressItemInfo dressItemInfos = 20;
  optional PlayerGameSettings gameSettings = 21;
  optional int32 platId = 22;
  optional int64 ugcExp = 23;
  optional int32 qualifiedMapCount = 24;//达标地图数
  optional bool isSub = 25; // 是否已订阅
  optional int32 addSubNum = 26; // 新增订阅数量(废弃)
  optional int32 addFansNum = 27; // 新增粉丝数量
  optional int64 creatorId = 28;
  optional int32 collectCount = 29; //地图收藏数量
  optional com.tencent.wea.xlsRes.UgcAuthType authType = 30;
  optional string authDesc = 31;
  optional string mapKey = 32; // ugcplayerinfo 要有这个信息，方便查询
  optional string bucket = 33; // 地图/资源操作桶
  optional string commonBucket = 34;  // 通用桶
  optional int32 accountSource = 35;  // 账号来源, 详情见枚举UgcAccountSourceType所示
  optional int32 totalPlayUv = 36;  // 所有玩家自己发布地图总游玩人数(不包含共创)
}

message PlayerGameSettings {
  optional bool hidePersonalProfile = 1;
  optional bool isCanUpdateMap = 2;
}

message PlayerDressItemInfo {
  optional com.tencent.wea.xlsRes.ItemType dressUpType = 1;
  optional int64 itemUUID = 2;
  optional int32 itemId = 3;
}

message PlayerRankGeoInfo {
  //地区类型
  optional com.tencent.wea.xlsRes.RankGeoRegion region = 1;
  //省份
  optional int32 province = 2;
  //城市
  optional int32 city = 3;
  //县区
  optional int32 town = 4;
}

// 客户端资源文件md5信息
message ResourceFile {
  required string fileName = 1; // 文件名
  optional string md5 = 2; // 客户端文件md5
  optional int64 size = 3; // 文件大小
}

message ResourceFileContent {
  optional string fileName = 1; // 文件名
  optional int64 size = 2; // 文件大小，单位字节
  optional bytes data = 3; // 文件内容
  optional string md5 = 4; // md5值
}

message SpecificUserTopRankInfo {
  optional bool exists = 1;
  optional TopRankInfo info = 2;
  optional int32 sub_rank_type = 3;
  optional int32 sub_rank_id = 4;
  optional int32 total_size = 5;
}
enum SingleStageType {
  ST_Normal = 0; //普通的单人闯关
  ST_Daily = 1; //每日的单人闯关
  ST_StarWorld = 2; // 新版星海巡游
}

message UgcDailyStageStepInfo {
  optional int32 stepId = 1; // 阶段ID
  optional PublishItem mapInfo = 2; // 地图信息
  optional bool isPass = 3; // 是否通关
}

message UgcDailyStageResetInfo {
  optional int32 resetCount = 1; // 已经重置的次数
  optional int32 resetTotalCount = 2; // 每日的总重置的次数
}

message UgcStarWorldStepInfo {
  optional int32 difficulty = 1;  // 难度
  optional int32 itemId = 2;
  optional int32 itemNum = 3;
  optional int32 changeCount = 4; // 已修改地图次数
  optional int32 changeTotalCount = 5; // 修改地图总数
  optional int32 scoreItemId = 6;   // 积分道具ID
  optional int32 scoreItemNum = 7;   // 数量

}

message ChatRoomDbInfo {
  optional int32 chatRoomId = 1;
  optional ChatGroupKey chatRoomGroupKey = 2;
}

message RedPacketInfo {
  optional int64 packet_uuid = 1;  // 红包uuid
  optional int32 receive_count = 2;   // 红包已领取数量
  optional int32 total_count = 3;  // 红包总数量
  optional int64 createTimeMs = 6;    // 创建时间
  optional int32 packet_id = 7; // 红包配置id
  optional int64 sender_uid = 8;  // 发送者uid
  optional ObjectPosition pos = 9; // 坐标
  optional string sender_name = 10; // 发送者名字
}

message PlacedObjectInfo {
  repeated RedPacketInfo redPacketList = 1;       // 红包
}

// 从cdn下载ugc地图需要的信息
message UgcCdnDownloadInfo {
  optional string bucket = 1;                   // 桶信息
  optional int64 ugcId = 2;                     // ugc地图id
  optional string decryptKey = 3;               // 解密key
  repeated UgcMapMetaInfo metaInfo = 4;         // ugc的metaInfo
  optional string filename = 5;                 // 文件名
}
// ugc创作者信息
message LobbyUgcMapCreatorInfo {
  optional int64 creatorId = 1;                     // 创作者id
  optional int32 editorType = 2;                    // 编辑器类型
  optional string nickName = 3;                     // 昵称
  optional string avatar = 4;                       // 头像
  repeated proto_DressItemInfo dressItemInfos = 5;  // 装扮信息
}
// 广场ugc加载图
message LobbyUgcMapLoadingInfo {
  repeated LobbyUgcMapCreatorInfo creatorInfo = 1;           // 创作者信息
  optional int32 loadingTemplateId = 2;                 // 加载模板
}
// 广场ugc地图信息
message LobbyUgcMapInfo {
  repeated int32 gameCamParam = 8;                    // ugc镜头参数
  optional LobbyUgcMapLoadingInfo loadingInfo = 9;    // 加载图信息
  repeated int32 chunkGroupIdList = 10;               // 客户端分包id
  optional string clientVersion = 11;                 // 客户端版本号
  optional int32 templateId = 12;                     // 模板id
  optional UgcCdnDownloadInfo cdnDownloadInfo = 13;   // 从cdn下载地图需要的信息
  optional string ugcVersion = 14;                    // ugc版本，客户端校验用
  optional int32 bgmId = 15;                          // 背景音乐id
  optional bool isOpenSave = 16;                      //是否开启存档
  optional string difficulty = 17;                    // 地图难度
  optional MapLobbyCoverInfo lobbyCover = 18;  // 乐园封面
  optional MapLoadingInfo mapLoading = 19;     // 自定义loading
  optional int64 editorCreatorId = 20;         //作者creatorId
}


message MockLobbyPlayerInfo {
  optional int64 uuid = 1; // 玩家uuid
  optional int32 chat_room_id = 4;  // 所在私服聊天室
  optional com.tencent.wea.xlsRes.LobbyPlayerState status = 6; // 玩家状态
}

message MockDsLobbyInfo {
  repeated MockLobbyPlayerInfo playerList = 1;
}

message LobbyExtraInfo {
  repeated ChatRoomDbInfo chatRoomDbInfoList = 1;                  // 广场聊天室频道
  optional int32 svrId = 3;                                        // 当前所在服务器id
  optional int64 startTime = 4;                                    // 大厅拉起时间
  optional int32 state = 5;                                        // 大厅状态
  optional int64 curStateStartTime = 6;                            // 当前状态开始时间
  optional int64 lastMergeTime = 7;                                // 上次合并时间
  optional int64 idcId = 8;                                        // 多idc时 ds所在idc id
  optional string lobbyGroupId = 9;                                // 所属大厅组的id
  optional LobbyUgcMapInfo ugcMapInfo = 11;                        // ugc地图信息
  optional int64 lastDsHeartbeatTime = 12;                         //上次DS心跳时间
  optional MockDsLobbyInfo mockDsLobbyInfo = 13;                   //大厅玩家列表，目前只在lobbyMockDsEnable用
}

enum RoomJoinType {
  RJT_Default = 0; // 常规加入
  RJT_AcceptInvitation = 1; // 同意邀请
  RJT_JoinApplyApproved = 2; // 申请加入被同意
  RJT_Recruit = 3; // 招募
  RJT_RecruitQuickJoin = 4; // 招募随机加入
  RJT_CustomRoomList = 5; // 自定义房间列表
  RJT_CustomRoomQuickJoin = 6; // 自定义房间快速加入
  RJT_UgcCustomRoomList = 7; // ugc多人房间列表
  RJT_UgcCustomRoomQuickJoin = 8; // ugc多人房间快速加入
  RJT_ShortcutRoom = 9; // 一键组队分享
  RJT_CompElimRoomJoin = 10; // 赛事-淘汰赛房间进入
  RJT_QRCode = 11; // 通过二维码加入
  RJT_ByRoomNo = 12; // 通过房间短号查询
  RJT_UgcCoPlayMatch = 13; // 通过ugc同游匹配
  RJT_JoinApplyApprovedJumpToBattle = 14; // 同意邀请后加入battle
  RJT_JoinAfterMatch = 15;  // 匹配后加入同一个房间
  RJT_JoinByLbsPin = 16; // 面对面短号加入同一个房间
  RJT_DouyinStreamShare = 17; // 抖音直播分享
  RJT_AcceptAIInvitation = 18; // 接受AI邀请

  RJT_RecruitChatSide = 301; // 招募加入拓展-聊天侧边栏
  RJT_RecruitRoomLobby = 302; // 招募加入拓展-房间大厅
  RJT_RecruitPlayDetailPage = 303; // 招募加入拓展-玩法详情页

  RJT_CustomRoomListRoomLobby = 501; // 房间列表拓展-房间大厅
  RJT_CustomRoomListPlayDetailPage = 502; // 房间列表拓展-玩法详情页
}

enum UgcTLogOperate {
  CreateUgcMap = 1;
  CopyUgcMap = 2;
  DeleteUgcMap = 3;
  PublishUgcMap = 4;
  PlayUgcMap = 5;
  CollectUgcMap = 6;
}

enum InvitationSourceType {
  IST_Other = 0; // 其他
  IST_FriendInfoTipChatWorld = 1; // 信息面板（聊天-世界）
  IST_FriendInfoTipChatFriend = 2; // 信息面板（聊天-队伍）
  IST_FriendInfoTipChatLobby = 3; // 信息面板（聊天-岛屿）
  IST_FriendInfoTipChatPrivate = 4; // 信息面板（聊天-私密频道）
  IST_FriendInfoTipChatNewStar = 5; // 信息面板（聊天-新手频道）
  IST_FriendInfoTipFriendSideFriend = 6; // 信息面板（组队-好友）
  IST_FriendInfoTipFriendSideLobby = 7; // 信息面板（组队-岛屿）
  IST_FriendInfoTipFriendMainFriend = 8; // 信息面板（社交-好友）
  IST_FriendInfoTipFriendMainLobby = 9; // 信息面板（社交-岛屿）
  IST_FriendInfoTipFriendMainRecommend = 10; // 信息面板（社交-推荐）
  IST_FriendInfoTipLobby = 11; // 信息面板-大厅
  IST_FriendSideFriend = 12; // 组队侧边栏-好友
  IST_FriendSideLobby = 13; // 组队侧边栏-岛屿
  IST_ChatRecruit = 14; // 组队侧边栏-一键招募
  IST_FriendSideQuickRecruit = 15; // 组队侧边栏-一键招募
  IST_SystemTeam = 16; // 系统组队
  IST_FriendInfoTipFriendModeSelectFriend = 17; // 信息面板（模式选择-好友）
  IST_FriendInfoTipFriendModeSelectLobby = 18; // 信息面板（模式选择-大厅）
  IST_FriendModeSelectFriend = 19; // 模式选择好友侧边栏-好友
  IST_FriendModeSelectLobby = 20; // 模式选择好友侧边栏-岛屿
  IST_CustomRoomLobby = 21; // 房间-侧边栏-岛屿
  IST_FriendInfoTipCustomRoomFriend = 22; // 房间-侧边栏-一键招募
  IST_FriendInfoTipCustomRoomLobby = 23; // 房间-侧边栏-好友-信息面板
  IST_FriendInfoTipRank = 24; // 排行榜-信息面板-组队
  IST_UgcCustomRoomLobby = 25; // UGC房间-侧边栏-岛屿
  IST_FriendInfoTipUgcCustomRoomFriend = 26; // 房间-侧边栏-一键招募
  IST_FriendInfoTipUgcCustomRoomLobby = 27; // 房间-侧边栏-好友-信息面板
  IST_CustomRoomFriend = 28;//房间-侧边栏-好友
  IST_UgcCustomRoomFriend = 29;//UGC房间-侧边栏-好友
  IST_ClubMemberList = 30;//社团-成员列表
  IST_ClubTeamInvite = 31;//邀请组队-社团-成员列表
  IST_TeamSideRecentPlay = 32; //组队侧边栏-最近
  IST_TeamSideClub = 33; //组队侧边栏-社团
  IST_TeamSideNear = 34; //组队侧边栏-附近
  IST_CustomRoomRecentPlay = 35; //房间-侧边栏-最近
  IST_CustomRoomClub = 36; //房间-侧边栏-社团
  IST_CustomRoomNear = 37; //房间-侧边栏-附近
  IST_UGCRoomRencentPlay = 38; //UGC房间-侧边栏-最近
  IST_UGCRoomClub = 39; //UGC房间-侧边栏-社团
  IST_UGCRoomNear = 40; //UGC房间-侧边栏-附近
  IST_ModeSelectRecentPlay = 41; //模式选择好友侧边栏-最近
  IST_ModeSelectClub = 42; //模式选择好友侧边栏-社团
  IST_ModeSelectNear = 43; //模式选择好友侧边栏-附近
  IST_Reservation = 44; //预约玩家
  IST_UGCTravelTogether = 45; //活动结伴同游(星友滴滴)
  IST_PrivateChat = 46; //私聊界面的邀请
  IST_TeamSideSearch = 47; // 组队侧边栏-搜索
  IST_ModeSelectSearch = 48; // 模式选择好友侧边栏-搜索
  IST_CustomRoomSearch = 49; // 模式选择好友侧边栏-搜索
  IST_UGCRoomSearch = 50; // 模式选择好友侧边栏-搜索
  IST_FriendSideFriendInDs = 51; //ds内的好友列表
  IST_FriendRecommend = 52;  // 好友主页-推荐
  IST_UGCFriendInDs = 53;  // UGC-DS-好友(中途加入)
  IST_UGCClubInDs = 54;  // UGC-DS-社团(中途加入)
  IST_UGCNearInDS = 55;  // UGC-DS-附近(中途加入)
  IST_UGCRecentPlayInDs = 56;  // UGC-DS-最近(中途加入)
  IST_UGCPlayerSearchInDs = 57;// UGC-DS-搜索(中途加入)
  IST_TeamFriendMainGame = 58; // 组队-好友主页-游戏好友
  IST_TeamFriendMainPlatform = 59; // 组队-好友主页-平台好友
  IST_TeamFriendMainLobby = 60; // 组队-好友主页-大厅好友
  IST_TeamFriendMainRecommend = 61; // 组队-好友主页-推荐好友
  IST_TeamChatPrivate = 62; //组队-聊天-私聊
  IST_CustomRoomFriendMainGame = 63; // 自定义房间-好友主页-游戏好友
  IST_CustomRoomFriendMainPlatform = 64; // 自定义房间-好友主页-平台好友
  IST_CustomRoomFriendMainLobby = 65; // 自定义房间-好友主页-大厅好友
  IST_CustomRoomFriendMainRecommend = 66; // 自定义房间-好友主页-推荐好友
  IST_CustomRoomChatPrivate = 67; //自定义房间-聊天-私聊
  IST_UGCRoomFriendMainGame = 68; // UGC房间-好友主页-游戏好友
  IST_UGCRoomFriendMainPlatform = 69; // UGC房间-好友主页-平台好友
  IST_UGCRoomFriendMainLobby = 70; // UGC房间-好友主页-大厅好友
  IST_UGCRoomFriendMainRecommend = 71; // UGC房间-好友主页-推荐好友
  IST_UGCRoomChatPrivate = 72; //UGC房间-聊天-私聊
  IST_PrivateChatStranger = 73; //UGC房间-聊天-私聊
  IST_ReturnOnlineTip = 74; // 登录时回归好友弹窗
  IST_SP_FriendList = 75;  // SP-好友列表邀请
  IST_SP_WorldList = 76;  // SP-世界列表邀请
  IST_SP_RecentList = 77;  // SP-最近列表邀请
  IST_SP_UnionList = 78;  // SP-工会列表邀请
  IST_SP_SearchList = 79; // SP-搜索列表邀请
  IST_SP_Unknown = 80;  // SP-未知邀请
  IST_PLAY_AI = 81; // 玩法AI邀请组队 ****** 特殊逻辑，禁止修改
}

enum UgcStageChangeType {
  EUgcStageChangeType_Unkonwn = 0;
  EUgcStageChangeType_GroupPublishFail = 1;     // 已触发组合发布审核不通过弹窗(我的作品列表)
  EUgcStageChangeType_GroupCollectInvalid = 2;  // 已触发组合收藏失效弹窗(组合收藏夹)
  EUgcStageChangeType_GroupPublishBan = 3;      // 已触发组合被封禁确认弹窗(我的作品列表)
  EUgcStageChangeType_ResBagPublishBan = 4;     // 已触发组合被封禁确认弹窗(资源背包)
}

enum UGCItemTab {
  UGC_Construction = 1 ; //积木
  UGC_Stratagem = 2 ;//玩法
  UGC_Nature = 3 ;//自然
  UGC_Building = 4 ;//建筑
  UGC_Indoor = 5 ;//室内
  UGC_Decorate = 6 ;//装扮
  UGC_Group = 7 ;//模组
  UGC_Collect = 8 ;//收藏

  UGC_Construction_Basic = 9 ;//积木-基础
  UGC_Construction_Podetium = 10 ;//积木-柱体
  UGC_Construction_Cone = 11 ;//积木-台锥
  UGC_Construction_Arc = 12 ;//积木-弧形
  UGC_Construction_Sepcial = 13 ;//积木-特殊

  UGC_Stratagem_Flow = 14 ;//玩法-流程
  UGC_Stratagem_Machine = 15 ;//玩法-机关
  UGC_Stratagem_Logic = 16 ;//玩法-逻辑
  UGC_Stratagem_Function = 17 ;//玩法-功能
  UGC_Stratagem_Physics = 18 ;//玩法-物理

  UGC_Nature_Grass = 19 ;//自然-花草
  UGC_Nature_Tree = 20 ;//自然-树木
  UGC_Nature_Moutain = 21 ;//自然-山水
  UGC_Nature_Weather = 22 ;//自然-气象
  UGC_Nature_Animal = 23 ;//自然-动物

  UGC_Building_Facility = 24 ;//建筑-设施
  UGC_Building_Floor = 25 ;//建筑-道路
  UGC_Building_Handrail = 26 ;//建筑-围墙
  UGC_Building_Door = 27 ;//建筑-门窗
  UGC_Building_Roottop = 28 ;//建筑-屋顶

  UGC_Decorate_Decorobject = 29 ;//装扮-装饰
  UGC_Decorate_Effect = 30 ;//装扮-效果

  UGC_Indoor_Tree = 31 ;//室内-摇钱树
  UGC_Indoor_Seat = 32 ;//室内-床椅
  UGC_Indoor_Desk = 33 ;//室内-桌柜
  UGC_Indoor_Clean = 34 ;//室内-清洁
  UGC_Indoor_Cook = 35 ;//室内-烹饪
  UGC_Indoor_Entertainment = 36;//室内-娱乐
  UGC_Indoor_Light = 37 ;//室内-照明


  UGC_Group_Mine = 38;//模组-我的
  UGC_Group_Sample = 39 ;//模组-示例
  UGC_Group_Community = 40;//模组-社区

  UGC_Collect_All = 41 ;//收藏-全部
  UGC_Collect_Mine = 42 ;//收藏-本地
  UGC_Collect_Community = 43 ;//收藏-社区

  UGC_Search = 44 ;//搜索
  UGC_Search_All = 45 ;//搜索-全部
  UGC_Search_Mine = 46 ;//搜索-我的
  UGC_Search_Community = 47 ;//搜索-社区

  UGC_Decorate_Carpet = 48 ;//装扮-地毯
  UGC_Character = 49 ;//角色
  UGC_Character_Star = 50 ;//角色-星宝
  UGC_Stratagem_Steer = 51 ;//玩法-载具
  UGC_Indoor_Plant = 52 ;//自然-种植

  UGC_Collect_Actor = 53 ;//收藏-元件
  UGC_Search_Actor = 54 ;//搜索-元件

  UGC_Collect_Resource = 55 ;//收藏-资源
  UGC_Search_Resource = 56 ;//搜索-资源
  UGC_Resource_Group = 57 ;//资源-模组
  UGC_Resource_Character = 58 ;//资源-角色
  UGC_Resource_Steer = 59 ;//资源-载具
  UGC_Resource_Weapon = 60 ;//资源-武器
  UGC_Indoor_Fruit = 61 ;//自然-果实

  UGC_Custom = 62 ;//自定义
  UGC_Custom_Custom = 63 ;//自定义

  UGC_Stratagem_Camera = 64 ;//玩法-相机

  UGC_Constructio_Custom = 65 ;//积木-造型

  UGC_Indoor_Handheld = 66 ;//生活-手持

  UGC_Custom_Skeleton = 67 ;//自定义-骨骼
  UGC_Custom_Character = 68 ;//角色-自制
  UGC_Test = 69 ;//测试
  UGC_Test_Test = 70 ;//测试-测试

  UGC_Resource_Equipment = 71;//资源-装备
  UGC_Resource_Prop = 72;//资源-道具

  UGC_Test_Inner = 74 ;//测试-测试2
  UGC_Test_Test3 = 75 ;//测试-测试3
  UGC_Test_Test4 = 76 ;//测试-测试4
  UGC_Test_Test5 = 77 ;//测试-测试5

  UGC_Search_Community_OnlyAI = 79;//搜索-AI资源

  UGC_OMD = 81;//兽人塔防
  UGC_OMD_Sub = 82;//兽人塔防

  UGC_AIRecommend = 84;           // AI助手推荐
  UGC_AIRecommend_ALL = 85;       // AI助手推荐 - 全部
  UGC_AIRecommend_Actor = 86;     // AI助手推荐 - 元件
  UGC_AIRecommend_Community = 87; // AI助手推荐 - 社区
  UGC_Construction_AI = 88;       // 积木 - 智能

  UGC_Eden = 89;//乐园
  UGC_Programme = 90;//编程

  UGC_AIGCModel = 91;  		//模型
  UGC_AIGCModel_Mine = 92;	//我的模型
  UGC_AIGCModel_Base = 93;  //基础模型
  UGC_AIGCModel_Community = 94;  //模型社区
}

enum OpSource {
  OS_Player = 0; // 玩家行为
  OS_System = 1; // 系统行为
  OS_IDIP = 2; // IDIP
}

enum UgcMapBanType {
  UMBT_UNKNOWN = 0; // 按二进制位
  UMBT_BanCustomTags = 1;  // 屏蔽自定义标签
}

enum UgcMapPublishGoodsStatus {// ugc地图中星钻道具是否可以创建
  UGC_PUB_GOOD_CLOSE = 0; // 关闭中
  UGC_PUB_GOOD_OPEN = 1;  // 开启中
}

enum UgcMapBuyGoodsStatus {// ugc地图中星钻道具是否可以购买状态
  UGC_BUY_GOOD_CLOSE = 0; // 关闭中
  UGC_BUY_GOOD_OPEN = 1;  // 开启中
  UGC_BUY_GOOD_REVIEW = 2;  // 审核中
  UGC_BUY_GOOD_REVIEW_REJECT = 4;  // 审核不过
}

message UgcItem {
  optional int64 ugcId = 1;  //id
  optional string name = 2;  //名称
  optional int32 type = 3;   //类型
  optional string coverUrl = 4; //路径地址
  optional int64 createTime = 5; //创建时间
  optional int32 saveType = 6; //保存类型  1:草稿箱 2:发布 3:回收站
  optional int64 expireTime = 7; //过期时间
  repeated UgcMapMetaInfo metaInfo = 8;
  optional string bucket = 9;
  optional string region = 10;
  optional int32 templateId = 11;   //模板
  optional string desc = 12;   //描述
  optional string tags = 13;   //tag,tag,tag,tag
  optional SafeStatus reportStatus = 14;  //审核状态
  optional int64 rejectTime = 15;  //销毁时间 驳回后
  optional int64 oldUgcId = 16;
  optional int64 likeCount = 17; //点赞数
  optional int64 playCount = 18; //游玩人数
  optional bool isCollect = 19;  //是否收藏
  optional int64 collectTime = 20; //收藏时间
  optional UgcExtraInfo extraInfo = 21; // 额外信息
  optional UgcInstanceType instanceType = 22; //UgcInstanceType
  optional string ugcVersion = 23; //版本号
  optional string clientVersion = 24; //客户端版本号
  repeated UgcMapMetaMap metaMap = 25;
  optional int32 editorSec = 26;
  optional UgcGroupIdList groupIds = 27; // 地图中使用到的已发布组合
  repeated EditorItemInfo creators = 28;
  optional int64 updateTime = 29; //更新时间
  repeated UgcLayerInfo layerList = 30; //图层
  optional string fullBucket = 31; //bucket
  repeated UgcMapTopic topics = 32;
  optional int64 secFlags = 33;  // 安全标记
  optional int32 lockState = 34; // 锁定状态
  optional UgcResType ugcResType = 35; // 资源类型
  optional int32 resCategory = 36;    // 资源大类别
  optional int32 resSubCategory = 37; // 资源小类别
  repeated int32 resLabels = 38;      // 资源标签
  optional int32 disableMultiTest = 39; //是否允许多人测试
  optional int64 publishTime = 40;  //发布时间
  optional UgcMapPublishGoodsStatus publishGoodsStatus = 41; // 星钻道具是否可以创建
  optional UgcMapBuyGoodsStatus buyGoodsStatus = 42; // 星钻道具是否可以购买
  optional bool isOpenSave = 43; // 是否开启存档
  optional bool isResPubCosPath = 44; // 私有资源是否在pub路径
  repeated MapCoverInfo covers = 45;   //多封面缩略图
  optional bool isAiGen = 46; // 是否是AI生成的
  optional bool hasPublishGoodsRecord = 47; // 是否有发布过星钻内购商品
  optional bool isApplyTakeOff = 48;
  optional bool isWhiteMap = 49;  //是否白名单地图
  repeated UgcAchievementIndex achievementIndexList = 50;  // 成就列表
  optional int32 curLayerId = 51; // 当前所在场景id
  optional bool isLuaCoding = 52;    // 是否使用Lua编程字段
  optional int32 source = 53;  //地图来源
  optional int32 evaluationStatus = 54;  // 评估状态  UgcMapEvaluationStatusType
  optional string difficulty = 55;     // 地图评级
  repeated UgcMapExtraConfigIndex extraConfigIndexList = 56;  // 额外配置列表
  optional MapLoadingInfo mapLoading = 57;  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 58;  // 乐园封面
  optional CodingDataInfo codingData = 59;	// 扣叮数据
}

message UgcCreateInfo {
  optional int64 ugcId = 1;      //ugcId
  optional string name = 2;      //名称
  optional string desc = 3;      //描述
  optional string tags = 4;      //标签
  optional string bucket = 5;    //桶信息
  optional int32 templateId = 6; //模板id
  optional string openId = 7;    //openId
  optional int32 saveType = 8;    //保存位置
  optional UgcMdList mdList = 9; //md5信息
  optional UgcInstanceType instanceType = 10;  // 1、普通 2、共创 3、组合
  optional UgcExtraInfo extraInfo = 11; // 额外信息
  optional string ugcVersion = 12; //版本号
  optional string clientVersion = 13; //客户端版本号
  optional int32 difficulty = 14;     // 地图评级
  repeated UgcLayerInfo layers = 15; //图层
  optional UgcResParam ugcResParam = 16; // 资源参数
  optional UgcGroupIdList groupId = 17; //引用的组合
  optional bool isAiGen = 18; // 是否是AI生成的
  optional int32 editorSec = 19; // 编辑秒数
  repeated MapCoverInfo covers = 20;   //多封面缩略图
  optional bool isLuaCoding = 21;    // 是否使用Lua编程字段
  repeated UgcAchievementIndex achievementIndexList = 22;  // 成就列表
  repeated int64 resIds = 23;
  optional int32 instanceSource = 24; //创建来源
  repeated UgcMapExtraConfigIndex extraConfigIndexList = 25;  // 额外配置列表
  optional MapLoadingInfo mapLoading = 26;  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 27;  // 乐园封面
}

message UgcModifyInfo {
  optional int64 ugcId = 1;      //ugcId
  optional string desc = 2;      //描述
  optional string tags = 3;      //标签
  optional string name = 4;      //名字
  optional int32 saveCount = 5;  //保存次数
  optional int32 editorSec = 6;  //编辑秒数
  optional int32 saveType = 7;   //保存位置
  optional int64 expireTime = 8; //过期时间
  optional int32 isDelete = 9;   //是否删除
  repeated UgcMapMetaInfo metaInfos = 10; //md5信息
  optional UgcInstanceType instanceType = 11;  // 1、普通 2、共创 3、组合
  repeated int64 groupIdList = 12;
  optional UgcMapMetaSaveInfo saveInfo = 13;
  optional UgcGroupIdList groupIds = 14; // 地图中使用到的已发布组合
}

//谨慎添加，不要加新内容，mapkey现在这个信息太关键拉
//特别注意这里写blob的问题  updateWithUploadedProfile()
message UgcPlayerCommonInfo{
  optional string mapKey = 1;       // 地图密钥
  optional string bucket = 2;       // 地图/资源操作桶
  optional string commonBucket = 3; // 通用桶
}

// 管理端关卡数据信息 支持多关卡数据
message UgcMgrLevelDataInfo {
  optional int32 layerId = 1;
  optional string mapDataUrl = 2;
  optional string md5 = 3;
}

message UgcCollect{
  map<int64, UgcCollectStruct> info = 1;
  optional int64 lastRefreshSortDataTime = 2;
}

message UgcCollectStruct{
  optional int64 ugcId = 1;          //ugcId
  optional int32 type = 2;          //类型
  optional int64 createTime = 3;    //时间
  optional int32 subType = 4;       //子类型 UgcMapSubType
  optional UgcSortData sortData = 5;
  optional UgcResType resType = 6;  // 资源类型
}

message UgcSortData {
  optional int32 likeCount = 1;
  optional int32 playerCount = 2;
  optional int64 publishTime = 3;
}

message UgcSub{
  map<int64, UgcSubStruct> info = 1;
}

message UgcSubStruct{
  optional int64 creatorId = 1;         //creatorId
  optional int32 isTop = 2;          //是否置顶 1是
  optional int64 createTime = 3;     //描述
  optional int64 topTime = 4;     //置顶时间
  optional int64 redDotTime = 5;  // 红点消除时间
}

message UgcBestRecord{
  optional int64 creatorId = 1;      //creatorId
  optional int32 sec = 2;         //秒数
  optional int64 ugcId = 3;       //地图ugcId
  optional string avatar = 4;     //玩家头像
  optional string nickName = 5;   //玩家昵称
  optional int64 uid = 6;
  optional int64 createMs = 7;    // 达成时间
  optional int64 bestVal = 8;     // 最佳值：有可能是通关秒数、也有可能是兽人的最高得分数
  optional int64 sortVal = 9;     // 排序值
}

message UgcPlayRecord{
  optional int32 playerCount = 1; //游玩人数
  optional int32 passCount = 2; //通关数量
  optional int32 playCount = 3; //游玩数量
  optional int32 passerCount = 4; //通关人数
}

// 版本下通关数据
message VersionPlayData {
  optional int64 version = 1;                 // 通关数据重置版本
  optional int64 qq_play_num = 2;             // 版本下qq游玩次数
  optional int64 qq_pass_num = 3;             // 版本下qq通关次数
  optional int64 qq_best_record = 4;          // 版本下qq最佳记录
  optional int64 qq_best_record_user_uid = 5; // 版本下qq最佳记录玩家uid
  optional int64 wx_play_num = 6;             // 版本下微信游玩次数
  optional int64 wx_pass_num = 7;             // 版本下微信通关次数
  optional int64 wx_best_record = 8;          // 版本下微信最佳记录
  optional int64 wx_best_record_user_uid = 9; // 版本下微信最佳记录玩家uid
}

// 版本下通关数据列表
message VersionPlayDataList {
  repeated VersionPlayData version_play_data_list = 1;  // 版本下通关数据列表
}

message UgcPlayerMapData {
  optional int64 like_num = 1;
  optional int64 collect_num = 2;
  optional int64 visit_num = 3;
  optional int64 play_num = 4;
  optional int64 share_wechat_num = 5;
  optional int64 share_wechat_moments_num = 6;
  optional int64 share_qq_num = 7;
  optional int64 share_qzone_num = 8;
  optional int32 map_type = 9;
  optional int32 map_level = 10;
  optional int32 season_map_level = 11;
  optional int64 pass_num = 12;
  optional int64 pass_usernum = 13;
  optional int64 qq_play_usernum = 14;
  optional int64 qq_play_num = 15;
  optional int64 qq_pass_num = 16;
  optional int64 qq_pass_usernum = 17;
  optional int64 qq_best_record = 18;
  optional int64 qq_best_record_user_uid = 19;
  optional int64 wx_play_usernum = 20;
  optional int64 wx_play_num = 21;
  optional int64 wx_pass_num = 22;
  optional int64 wx_pass_usernum = 23;
  optional int64 wx_best_record = 24;
  optional int64 wx_best_record_user_uid = 25;
  optional int32 season_id = 28;
  optional int64 use_num = 29;
  optional int32 daily_map_level = 30;
  optional int32 weekly_map_level = 31;
  optional int64 publish_use_num = 32;
  optional int64 ref_num = 33;
  optional int32 play_source = 34;  // 游玩来源：0 默认,1 星海巡游,2 星选好图,3 广场-首页,4 广场-人气
  map<int64, int32> cancel_refer_map = 35;
  optional UgcReportLabelScoreData label_score_data = 36;
  optional UgcMapRankData ugc_map_rank_data = 37;
  optional VersionPlayDataList version_play_data = 38;    // 版本下通关数据
}

message UgcMapRankData{
  repeated UgcMapRankItem ugc_map_rank_item_list = 1;
}

message UgcMapRankItem{
  optional int64 rank_id = 1;
  optional int32 score = 2;
  optional int64 uid = 3;
}

message UgcReportLabelScoreData{
  repeated UgcReportLabelScoreItem label_score_item_list = 1;
}
message UgcReportLabelScoreItem {
  optional int32 label = 1;
  optional int64 score = 2;
  optional int64 count = 3;
  optional string name = 4;
  optional int64 warm_count = 5;
}

message UgcSeasonMapLevelData {
  optional int32 season_map_level = 1;
  optional int32 season_id = 2;
}

message UgcAigcHttpData {
  optional int32 req_type = 1;
  optional int32 game_type = 2;
  optional int64 req_id = 3;
  optional int64 uid = 4;
  optional int32 ret_code = 5;
  optional int32 seq_id = 6;
  optional int32 time = 7;
  optional int64 version = 8;
  optional string aiserver_content = 9;
}

message UgcAigcHttpContent {
  optional string Prompt = 1;
  repeated int32 StyleId = 2;
  repeated string ImagePaths = 3;
  optional UgcAigcCosParam CosParam = 4;
}

message UgcAigcCosParam {
  optional string secretId = 1;
  optional string secretKey = 2;
  optional string token = 3;
  optional string bucket = 4;
  optional string region = 5;
  optional string directory = 6;   // 文件所在目录
}

message UgcAiChangeColorHttpContent {
  repeated int32  Ids = 1;
  optional string Theme = 2;
  optional string Native = 3;
  optional UgcAigcCosParam CosParam = 4;
  optional string Board = 5;
}

message UgcAiGenModuleHttpContent {
  optional UgcAigcCosParam CosParam = 1;
  optional string BuildingParam = 2;
}

// ai魔法图片请求结构体
message UgcAiGenMagicPicHttpContent {
  optional UgcAigcCosParam CosParam = 1;
  optional string image = 2;
  repeated int32 bbox = 3;
  optional int32 method_type = 4;
  optional int32 detail_level = 5;
  optional int32 scale_level = 6;
  optional int32 stick_level = 7;
  optional bool segmentation = 8;
  optional bool collision = 9;
  optional int32 seed = 10;
  optional int32 TypeID = 11;
}

// ai编辑助手初始化http内容
message UgcAiEditAssistantInitHttpContent {
  optional int32 action = 1;
  optional string scene_id = 2;
  optional string scene_path = 3;
  optional UgcAigcCosParam cos_param = 4;
}

// ai编辑助手聊天http内容
message UgcAiEditAssistantChatHttpContent {
  optional int32 action = 1;
  optional int32 task_group_index = 2;
  optional string task_context = 3;
  optional string chat_context = 4;
  optional string operate_context = 5;
  optional string scene = 6;
  optional string session_id = 7;
}

// ai编辑助手关闭http内容
message UgcAiEditAssistantCloseHttpContent {
  optional int32 action = 1;
  optional string scene_id = 2;
  optional string session_id = 3;
}

message UgcAiChangeColorNativeInfo {
  optional string photo = 1;
  optional string index = 2;
}

message UgcAigcTTSQueryHttpContent {
  optional int32 spkid = 1;
  optional int32 emoid = 2;
}

message UgcAigcTTSParamHttpContent {
  optional UgcAigcTTSQueryHttpContent query = 1;
}

message UgcAiGenVoiceRequestHttpContent {
  optional int32 WorkFlow = 1;
  optional string QueryText = 2;
  optional UgcAigcTTSParamHttpContent TTSParam = 3;
  optional UgcAigcCosParam CosParam = 4;
}

message UgcAiGenVoiceResponseHttpContent {
  repeated string QueryText = 1;
  repeated string QueryTTS = 2;
}

message UgcAiGenAnicapRequestHttpContent {
  optional string RoleId = 1;
  optional int32 GenQuality = 2;
  optional string MoviePath = 3;
  optional UgcAiMovieParam MovieParam = 4;
  optional UgcAigcCosParam CosParam = 5;
  optional int32 IfDebug = 6;
}

message UgcAiGenAnicapResponseHttpContent {
  optional string RoleId = 1;
  optional string AnimPath = 2;
  optional string ResultPath = 3;
}

message AigcAnswerExtraInfo {

}

message UgcAiGenAnswerRequestHttpContent {
  optional int32 type = 1;
  optional string roleid = 2;
  optional string query = 3;
  optional AigcAnswerExtraInfo extra_info = 4;
}

message UgcAiGenAnswerResponseHttpContent {
  optional int32 type = 1;
  optional string answer = 2;
  optional AigcAnswerExtraInfo extra_info = 3;
}

message UgcGroupCommon {
  optional int64 id = 1;            // id
  optional int64 time = 2;          // 收藏时间
}

enum UgcMapSubType {
  UgcMapSubType_Publish = 0;     // 发布作品
  UgcMapSubType_Draft = 1;       // 草稿作品
  UgcMapSubType_LocalCommon = 2; // 客户端组件
  UgcMapSubType_GroupSelfPublish = 3; // 组合专用特殊类型，标识是自己的发布作品
}

message CSUgcTranslationData {
  optional int32 type = 1;      // 翻译类型 0.作品名 1.作品描述 2.局内文本
  optional int32 id = 2;        // 局内文本标识ID, 客户端定位到局内的文本位置
  optional string content = 3;    // 翻译后文本
  optional int32 index = 4;      // 客户端索引
}

// 账号状态
enum AccountState {
  ACCOUNT_STATE_NORMAL = 0;           // 正常账号
  ACCOUNT_STATE_CANCEL_APPLY = 1;     // 账号注销申请
  ACCOUNT_STATE_CANCEL_DELETE = 2;    // 账号注销删除
}

// 账号转区状态
enum AccountTransferState {
  ACCOUNT_STATE_TRANSFER_NONE = 0;     // 未转区
  ACCOUNT_STATE_TRANSFER_DOING = 1;    // 账号转区进行中
  ACCOUNT_STATE_TRANSFER_SUCCESS = 2;    // 账号转区成功
  ACCOUNT_STATE_TRANSFER_FAIL = 3;    // 账号转区失败
}

enum AccountTransferStage {
  TRANSFER_STAGE_KICK_PLAYER = 0;   //业务模块踢除玩家
  TRANSFER_STAGE_INIT_BACKUP = 1;   //初始化备份表结构
  TRANSFER_STAGE_USER_DATA = 2;     //用户基础数据修改
  TRANSFER_STAGE_RELATION_DATA = 3; //关系数据修改
  TRANSFER_STAGE_BUSINESS_DATA = 4; //业务模块数据修改
  TRANSFER_STAGE_OTHER_DATA = 5;    //其他数据修改
  TRANSFER_STAGE_MAIN_DATA = 6;     //转区生效数据修改
}

// 全局封禁数据
message GlobalBanData {
  optional uint64 beginTime = 1;                            // 封禁开始时间
  optional uint64 endTime = 2;                              // 封禁结束时间
  optional string reason = 3;                               // 封禁原因
}

// 全局封禁信息
message GlobalBanInfo {
  optional int64 timestamp = 1;                             // 更新时间戳
  map<int32, GlobalBanData> globalBanInfoMap = 2;           // 全局封禁消息列表, 封禁类型->全局封禁消息
}


// 多语言使用场景
enum MultiLanguageConfUseType {
  MULTI_LANGUAGE_USE_TYPE_COMMON = 0;                         // 通用
  MULTI_LANGUAGE_USE_TYPE_MAIL_SENDER = 1;                    // 邮件-发件人名称
  MULTI_LANGUAGE_USE_TYPE_MAIL_TITLE = 2;                     // 邮件-标题
  MULTI_LANGUAGE_USE_TYPE_MAIL_CONTENT = 3;                   // 邮件-内容
}

// 全局信息类型
enum GlobalInfoType {
  GLOBAL_INFO_TYPE_BAN = 1;                               // 全局封禁信息类型
  GLOBAL_INFO_TYPE_KICK = 2;                              // 全服踢人信息类型
  GLOBAL_INFO_TYPE_BAN_DEVICE = 3;                        // 封禁设备信息类型
}

// 全服踢人信息
message GlobalKickInfo {
  optional int64 timestamp = 1;                             // 更新时间戳
  optional GlobalKickData globalKickData = 2;               // 全服踢人数据
}

// 全服踢人数据
message GlobalKickData {
  optional uint64 closeTime = 1;                            // 停服开始时间戳
  optional uint64 openTime = 2;                             // 重新开启时间戳
  optional int32 regionId = 3;                              // 国家地区id
  repeated int32 accountChannels = 4;                       // 账号渠道类型，可填多个 ,不填则表示不限制
  optional int32 errCode = 5;                               // 下发给client 的错误码

}

// 全局封禁数据
message BanDeviceData {
  optional uint64 beginTime = 1;                            // 封禁开始时间
  optional uint64 endTime = 2;                              // 封禁结束时间
  optional string reason = 3;                               // 封禁原因
}

// 设备封禁数据
message BanDeviceInfo {
  optional int64 timestamp = 1;                             // 更新时间戳
  map<string, BanDeviceData> banDeviceInfoMap = 2;          // 封禁设备信息, 设备id->封禁信息
}

// 全局信息
message GlobalInfo {
  optional GlobalBanInfo globalBanMsg = 1;                  // 全局封禁信息
  optional GlobalKickInfo globalKickMsg = 2;                // 全服踢人信息
  optional BanDeviceInfo banDeviceMsg = 3;                  // 封禁设备信息
}

enum RoomBroadcastInfoType {
  RBIT_Unknown = 0;
  RBIT_MapDataDownloadProcess = 1;
  RBIT_MapDownloadReminder = 2;
  RBIT_PlayerReadyReminder = 3;
  RBIT_MapStateSync = 4;
  RBIT_PlayerOperationStatus = 5; // 玩家队伍中的操作状态
  RBIT_PlayerClientData = 6; // 玩家客户端维护的状态同步
  RBIT_MiniGameInvitation = 7; // 小游戏邀请信息
  RBIT_CommonInfo = 8; // 通用信息
  RBIT_SPBroadcastInfo = 9; // SP专用
  RBIT_DetailPakData = 10; // 细节分包（大小包）信息
}

enum DsStatComparisonOp {
  DSCO_Unknown = 0;
  DSCO_Equal = 1;
  DSCO_MoreOrEqual = 2;
  DSCO_More = 3;
  DSCO_LessOrEqual = 4;
  DSCO_Less = 5;
  DSCO_AnyOf = 6;
  DSCO_WithIn = 7;
}

// 小窝相关的 -----------start
message SceneItem {
  optional fixed64 objID = 1; // 场景物件的唯一ID
  optional fixed64 confID = 2; // 组件配表ID
}

enum DropType {
  Unknown = 0;
  PlayerItem = 1;
  SceneObject = 2;
}

enum XiaoWoHotType {// 这个可以整合成一个
  XWHT_Uknown = 0;
  XWHT_Action = 1;  // 角色操作
  XWHT_Chat = 2; // 聊天
  XWHT_Voice = 3;  // 语音
  XWHT_Like = 4; // 点赞
  XWHT_Star = 5; // 收藏
}

enum XiaowoVerifyResult {
  XVR_Uknown = 0;
  XVR_Running = 1; // 检测中
  XVR_Succ = 2; //成功
  XVR_MoneyTree = 3; // 摇钱树数量检测失败
  XVR_Aesthetic = 4; // 美观度校验失败
  XVR_Ocupy = 5; // 占用值校验失败
  XVR_ItemUnlock = 6; // 道具解锁校验失败
  XVR_ItemCount = 7; // 道具数量校验失败
  XVR_Max = 8;
}

enum XiaoWoKickReason {
  XWKR_Uknown = 0;
  XWKR_Block = 1;  // 屏蔽
  XWKR_PutDown = 2;  // 下架
  XWKR_Update = 3;  // 场景更新
  XWKR_BeBlack = 4;  // 被拉黑
  XWKR_Close = 5; // 小窝入口已关闭
  XWKR_VerUp = 6; // 小窝版本号升级，你被踢了
  XWKR_DsExit = 7; // 玩家的ds掉线了
  XWKR_VerifyFailed = 8; // 该小窝作弊校验失败了
  XWKR_VerDown = 9; // 小窝版本号下降，你被踢了
  XWKR_Exit = 10; // 正常退出
}

enum FarmKickReason {
  FKR_Uknown = 0;
  FKR_Exit = 1; // 正常退出
  FKR_DsExit = 2; // 玩家的ds掉线了
  FKR_Evict = 3; // 农场主人驱逐
  FKR_VerUp = 4; // 版本号升级，你被踢了
  FKR_VerDown = 5; // 版本号下降，你被踢了
  FKR_PutDown = 6;   // 下架
  FKR_BeBlack = 7; // 被拉黑
  FKR_PetEvict = 8; // 农场宠物驱逐
}

enum PickType {
  PT_UNKNOWN = 0;
  PT_WATER = 1; // 捡浇水
  PT_SHAKE = 2; // 捡摇树
  PT_WATEROWNER = 3; // 主人捡他人浇水
  PT_MAX = 4;
}
// 小窝相关的 -----------end
// UGC操作源
enum UGCOperateSource {
  UOS_Unknown = 0;
  UOS_Player = 1;  // 玩家操作
  UOS_IDIP = 2;     // IDIP操作
  UOS_Manager = 3;   // 管理端操作
}

// UGC操作原因
enum UGCOperateReason {
  UOR_Unknown = 0;
  UOR_ManagerReviewFail = 1; // 管理端审核不过
  UOR_ManagerBanTakeOff = 2; // 管理端封禁下架
}

message UgcMapMetaSaveInfo{
  optional UgcMapMetaMap handSave = 1;   //手动保存  (废弃)
  optional UgcMapMetaMap autoSave = 2;   //自动保存  (废弃)

  optional UgcMapMetaList activeRecord = 3;   //手动保存
  optional UgcMapMetaList autoRecord = 4;     //自动保存
  optional UgcMapMetaList createRecord = 5;   //手动创建
  //兼容之前数据
  repeated UgcMapMetaList activeRecords = 6;   //手动保存
  repeated UgcMapMetaList autoRecords = 7;     //自动保存
  repeated UgcMapMetaList createRecords = 8;    //手动创建
}

message UgcMapMetaList{
  repeated UgcMapMetaMap meta = 1;
  optional int32 layerId = 2;
}

message UgcMapMetaMap {
  optional int64 id = 1;
  optional SafeStatus status = 2;
  repeated UgcMapMetaInfo info = 3;
  optional int64 updateTime = 4;
  optional ugcMetaType metaType = 5;  //(废弃)
  optional int64 creatorId = 6;
  optional string SaveName = 7;
  optional SaveType saveType = 8;
  repeated int64 resIds = 9;
}

message UgcCommonMapInfo {
  optional string mapKey = 1;
  optional int32 useCountPrePublish = 2; // 废弃。组合被使用数量（地图发布后就统计，此时地图未过审）
}

// 理论上放在这里的数据是允许覆盖的,不考虑并发的问题
message UgcMapMgrInfo {
  optional int32 loadingTemplateId = 1;
  optional bool isShareHotBan = 2; // 是否处于过热分享禁止游玩状态
  optional bool isPrivateState = 3; // 私创状态
  optional bool isLockState = 4; // 锁定状态  // 废弃
  optional int32 markId = 5;//优秀markId
  optional int32 lockState = 6;  // 锁定状态
  map<int32, int64> shareReportVersion = 7;  // 记录各个阈值的上次上报版本
  optional UgcEditorList displayEditors = 8;    // 管理端修改的虚假的创作者信息
  optional int64 displayCreator = 9;            // 管理端修改的虚假的原作者信息
  optional string prize_id = 10;
  optional string prize_info = 11;
}

message UgcPublishDescInfo {
  repeated BattleCamp camps = 1;     // 地图阵营信息
  repeated uint32 goldTopics = 2;
  repeated string blueTopics = 3;
  optional int64 draftCreateTime = 4;   // 地图草稿创建时间
  optional bool isCampsOpen = 5; //阵营是否开启
  optional bool isEditable = 6; //是否可编程 （单纯记录 策划需求不明确 不与tags一起存放）
  optional bool isOpenHealth = 7; //是否打开生命值
  optional bool isOpenSave = 8; // 是否开启存档
  optional bool isDanMuUnable = 9;  // 是否允许发弹幕
  optional bool isAllowMidJoin = 10;  // 是否允许中途加入
  optional int32 modelType = 11;  //当前地图模式
  optional bool isAiGen = 12;     // 是否是AI生成的
  repeated SpawnPointInfo spawnPointInfos = 13; //出生点数据
  optional int32 dataStoreSaveType = 14; // 数据存储类型 ENUM DataStoreSaveType
  optional bool isContainDiyArms = 15;          // 是否包含自制装备
  optional int64 auditVersion = 16; // 当前审核版本
  optional string versionStr = 17;  //版本内容 是否读取新表
  optional int32 playRuleOMD = 18;
  optional int32 omdLevel = 19;
  optional bool isLuaCoding = 20;    // 是否使用Lua编程字段
  optional int32 source = 21; //等同于common中UgcInstanceSource
  optional bool isResetPassRecord = 22; // 是否重置通关记录, 针对竞速类地图/兽人ugc地图生效
  optional string traceStr = 23;    //转换统一模板  旧ugcId_templateId
  optional string qqMusicInfo = 24; // qq音乐存储在服务器 废弃
  optional int32 gameTime = 25;   //游戏时长
}

message UgcDailyReport {
  optional uint64 play_num = 1;
  optional uint64 fans_num = 2;
  optional uint64 like_num = 3;
  optional uint64 collect_num = 4;
  optional uint64 uid = 5;
  repeated RecommendedMap recommendedMaps = 6;
  repeated RecommendedMap hotPlayMaps = 7;
  message RecommendedMap {
    optional int64 mapId = 1;
    optional int32 mapType = 2;

    optional string bucket = 100;
    optional string mapName = 101;
    repeated UgcMapMetaInfo metaInfo = 112;
  }
}

message UgcCreatorInfo {
  optional int64 uid = 1;
  optional int64 creatorId = 2;
  optional int32 ugcExp = 3;
  optional int32 peakUgcExp = 4;
  optional com.tencent.wea.xlsRes.UgcAuthType authType = 5;
  optional string authDesc = 6;
  optional int32 ugcLv = 7;
  optional int32 draftCap = 8;
  optional int32 publishedCap = 9;
  optional UgcDailyReport dailyReport = 10;
  repeated ThirdPartyActivityParam taskInfos = 11;
  optional int32 peakPlayUv = 12;
  optional int32 totalPlayUv = 13;
  optional int32 fansCount = 14;
  optional int32 subCount = 15;
}

enum ugcMetaType {
  UgcMetaHand = 0;
  UgcMetaAuto = 1;
  UgcMetaUpdate = 2;
}

// pingSvr信息
message CSPingSvrInfo {
  optional int32 zone_id = 1;    // zone_id
  optional string ip = 2;        // IP
  optional int32 port = 3;    // 端口
}

// 翻译信息结果
message CSTranslateInfo {
  optional string ID = 1;             // ID
  optional string nameSpace = 2;      // 名字空间
  optional string path = 3;           // 客户端路径
  optional string sourceText = 4;     // 源语言文本
  optional string contentText = 5;    // 翻译内容文本
}

enum RoomPublicType {
  RPT_Unknown = 0;
  RPT_Recruit = 1;                    // 招募
  RPT_CustomLobby = 2;                // 房间大厅
  RPT_UgcCustomLobby = 3;             // ugc房间大厅

  // SP 招募
  RPT_SP_Free_Recruit     = 10;      // SP 自由招募
  RPT_SP_Tower_Recruit    = 11;      // SP 高塔招募
  RPT_SP_Dungeon_Recruit  = 12;      // SP 副本招募
  RPT_SP_ALL_Recruit      = 13;      // SP 所有招募
}

enum RoomRecommendSourceType {
  RRST_Unknown = 0;
  RRST_Friend = 1;                     // 好友置顶
  RRST_AboutToFull = 2;                // 快满的房间
  RRST_Local = 3;                      // 节点本地
  RRST_QueryByNo = 4;                  // 短号查询
}

enum EUgcEditOption {
  EUgcEditOption_ChangeBase = 1;       // 基础
  EUgcEditOption_ChangeAppearance = 2; // 外观
  EUgcEditOption_ChangeMove = 3;       // 运动
}

enum ENewUgcEditOption {
  ENewUgcEditOption_None = 0;          // 无
  ENewUgcEditOption_Open = 1;          // 可编辑
  ENewUgcEditOption_Close = 2;         // 不可编辑
}

// 平台特权类型
enum PlatPrivilegesType {
  NO_OPEN_PRIVILEGES = 0;       // 未开启特权
  QQ_PLAT_PRIVILEGES = 1;       // qq平台特权
  WX_PLAT_PRIVILEGES = 2;       // 微信平台特权
}

message UgcBaseList{
  map<int64, UgcBaseBrief> map = 1;
}

message UgcBaseBrief {
  optional int64 ugcId = 1;  //ugcId
  optional string name = 2;  //地图名称
  optional int32 templateId = 3; //模板id
  optional int32 saveType = 4; //保存类型
  optional int32 saveCount = 5; // 保存次数
  optional int32 isDelete = 6; // 1删 0有效
  optional int64 createTime = 7; //创建时间
  optional int64 expireTime = 8; //过期时间
  optional UgcMdList mdList = 9; //md5信息
  optional int64 oldUgcId = 10; //旧的ugcId
  optional string desc = 11; //描述
  optional string tags = 12; //tags
  optional int32 reportStatus = 13;  //审核状态
  optional int64 rejectTime = 14;  //销毁时间 驳回后
  optional int32 editorSec = 15;  //编辑时长
  optional int32 mapType = 16;  //地图类型 1:普通地图 2：共创 UgcMapType
  optional int32 isCollect = 17;  // 是否收藏
  optional int64 collectTime = 18; // 收藏时间
  optional UgcExtraInfo extraInfo = 19;   // 额外信息
  optional string bucket = 20; //bucket
  optional UgcGroupIdList ugcGroupIdList = 21;
  optional string ugcVersion = 22;         //地图版本号
  optional string clientVersion = 23;      //客户端版本号
  optional UgcMapMetaSaveInfo saveInfo = 24;
  optional UgcMapMetaList metaList = 25;   //更新自核列表
  optional string difficulty = 26;   //地图难度
  optional UgcCommonMapInfo commMap = 27;//地图通用信息

  optional UgcLayerList layers = 28;
  optional UgcEditorList editors = 29;
  optional int64 updateTime = 30;//更新时间
  optional int32 disableMultiTest = 31; //是否允许多人测试
}

message UgcCoCreateList{
  repeated int64 ugcId = 1;
}

message LayerInfo{
  optional int64 layerId = 1;  //图层id
  repeated int64 creatorId = 2; //图层归属者
}

enum UgcEditorType {
  Ugc_CoCreator = 1;        //作者
  Ugc_Team_Member = 2;      //队员
  Ugc_CoBrandedCreator = 3; //联名作者
}

message BattleCamp {
  optional uint32 campId = 1;
  optional uint32 maxPlayerCount = 2; // 人数上限
  optional string campName = 3;  // 阵营名
  optional int32 campColor = 4;  // 阵营颜色
}

enum DSCreateState {
  DSC_Unknown = 0;
  DSC_Created = 1; // 本次创建了新DS
  DSC_UseExist = 2; // 本次使用了已有的DS
  DSC_NoDS = 3; // 本次未使用DS
  DSC_CreateFail = 4; // 创建失败
  DSC_NoPakVerData = 5; // 没有找到pakversion的配置数据
}

message UgcMapTopic {
  optional uint32 id = 1;
  optional string name = 2;
  optional string desc = 3;
  optional string detail = 4;
  optional uint32 begin_time = 5;
  optional uint32 expire_time = 6;
  optional uint32 type = 7;         //话题类型 0-蓝色, 1-金色
  repeated uint32 tags = 8;         //话题标签, 1-推荐, 2-常用, 3.活动
  optional string label = 9;        //"热度上升快" 等
  optional string hot_degree = 10;  //"123.4万"等
}

message EditorItemInfo{
  optional int64 creatorId = 1;
  optional UgcEditorType type = 2;
  optional string nickName = 3; //昵称
  optional string avatar = 4; //头像
  optional bool isSub = 5; //是否订阅
  optional com.tencent.wea.xlsRes.UgcAuthType authType = 6;
  optional int32 gender = 7; //性别  1-男 2-女 0-未知
  optional int64 uid = 8; // uid
  optional int32 loginType = 9; // 1:wx 2:QQ
  optional bool isEditing = 10; //是否在编辑中
  optional int32 ugcLv = 11; // 工匠值等级
  optional int32 publishEditorType = 12;   // 发布态草稿编辑权限类型，仅在type字段取值为2或者3时生效, 0值表示无权限, 非0值见枚举UgcEditorType所示
  optional int32 editMode = 13;	// 编辑模式, 见枚举UgcMapEditMode所示
  optional bool isApplyOccupy = 14;	// 是否共创多人编辑申请占用
  repeated PlayerDressItemInfo dressItemInfos = 38;// 装扮信息(铭牌、称号、头像框、道具头像等)
}

enum GetMapsSortType {
  GMST_Default = 1; // 默认按发布时间(游玩历史默认是游玩时间,收藏默认是收藏时间)
  GMST_LikeCount = 2; // 按点赞数(支持收藏)
  GMST_PlayerCount = 3; // 按游玩人数(支持收藏)
  GMST_PlayTime = 4; // 按游玩时间(支持收藏)
  GMST_UpdateTime = 5; // 按更新时间(支持收藏)
}

enum RoomShareType {
  RST_Unknown = 0;
  RST_ShortcutRoom = 1; // 一键组队
  RST_QRCode = 2; // 二维码
}

enum BattleRewardAdditionType {
  BRAT_None = 0;
  BRAT_QQPLAT_PRIVILEGES = 1; //QQ平台特权
  BRAT_WXPLAT_PRIVILEGES = 2; //WX平台特权
  BRAT_EXP_ADDITION_ITEM = 3; //经验加成卡
  BRAT_ACTIVITYCOIN_ADDITION_ITEM = 4; //印章加成卡
  BRAT_RETURNING_PRIVILEGES = 5; //回归特权
  BRAT_FRIEND_RETURNING_PRIVILEGES = 6; //好友回归特权
  BRAT_ARENA_WEEK_ADDTION = 7; // arena周末加成
  BRAT_ARENA_TEAM_ADDTION = 8; // 组队加成
  BRAT_ARENA_FIRST_WIN_ADDTION = 9; // 首胜加成
  BRAT_ARENA_FIRST_COMPETION_ADDTION_DAILY = 10; // 每日首局加成
}

enum UgcHomePageRecommendScene{
  UHPRS_Default = 1;
  UHPRS_UgcHomePage = 2; // 每日精选
  UHPRS_LobbySet = 3; // 大厅合集
  UHPRS_Monster = 4; // 兽人omd合集
}

enum UgcHomePageRecommendType{
  UHPRT_Main = 1; // 主推荐位
  UHPRT_Hot = 2; // 热门
  UHPRT_Theme = 3; // 主题
  UHPRT_MonthlyHot = 4; // 每月必玩
  UHPRT_LikePlay = 5; // 玩家爱玩
  UHPRT_PlayRecord = 6; // 游玩记录
}

// ugc集合推荐类型
enum UgcRecommendTypeSet{
  UCPRT_OMD_Born_1 = 100; // 兽人推荐 出生点为1
  UCPRT_OMD_Born_2 = 101; // 兽人推荐 出生点为2
  UCPRT_OMD_Born_4 = 102; // 兽人推荐 出生点为4
}

message UgcHomePageRecommendData{
  optional UgcHomePageRecommendType type = 1;
  repeated PublishItem items = 2;
  optional string theme = 3;  // UHPRT_Theme
  optional string themeDesc = 4;  // UHPRT_Theme
  optional string themeId = 5;  // UHPRT_Theme
  optional AlgoInfo info = 6;
  optional int32 themeType = 7;  // UHPRT_Theme 合集类型：0=地图合集；1=创作者合集
  optional int64 creatorId = 8; // UHPRT_Theme  创作者id
  optional bool useItemHead = 9;  // UHPRT_Theme  是否使用物品头像
  optional UgcPlayerProfile creatorProfile = 10; // UHPRT_Theme,创作者合集的作者信息
}

message UgcHomePageRecommendModuleInfo {
  optional UgcHomePageRecommendType type = 1;
  optional string id = 2;
  optional string title = 3;     // 标题
  optional string subTitle = 4; // 副标题
}

message UgcRecommendDataSet{
  repeated PublishItem items = 1;
  optional AlgoInfo info = 2;
}

message RecommendInfo {
  optional int32 exposePos = 1;
  optional AlgoInfo info = 2;
  optional int32 canvasID = 3;
  optional string tabDesc = 4;
  optional string subTabName = 5;
  optional int32 mapSource = 6;
  optional string recommendCollectId = 7;
}

enum UgcRecommendSetSource{
  URSS_Unknown = 0;
  URSS_OMD = 1;  // omd recommend
}

message UVector {
  optional float x = 1;
  optional float y = 2;
  optional float z = 3;
}
enum MatchCancelReason {
  MCR_Unknown = 0;
  MCR_Click = 1;      // 点击取消
  MCR_Offline = 2;    // 玩家离线自动取消
  MCR_InCustomRoom = 3; //在多人房间
  MCR_InShop       = 4; //在商业化取消的
}

message MultiPlayerSquadDigRecord {
  optional int64 timestampMs = 1; // 发生时间ms
  optional int64 playerUid = 2; // 执行玩家
  optional int32 itemIndex = 3; // 宝藏序号 1开始
  optional int32 itemId = 4; // 宝藏id 配表中的值
}

enum PermitRewardStatus {
  PRS_UnReceivable = 0;
  PRS_Receivable = 1;
  PRS_Received = 2;
}

enum PermitType {
  PT_Common = 0;
  PT_High = 1;
  PT_Glory = 2;
}

enum PermitPurchaseType {
  PPT_Unknown = 0;
  PPT_CommonToHigh = 1;
  PPT_CommonToGlory = 2;
  PPT_HighToGlory = 3;
  PPT_IncreaseLevel = 4;
}

message PermitPurchaseData {
  optional PermitPurchaseType type = 1;
  optional int32 levelNum = 2;
}

message SnsInvitationParam {
  optional SnsInvitationActivityParam activity = 1;
  optional SnsInvitationRaffleParam raffle = 2;
}

message SnsInvitationActivityParam {
  optional int32 activityId = 1;
}

message SnsInvitationRaffleParam {
  optional int32 raffleId = 1;
  optional int32 poolId = 2;
}

message SnsInvitationData {
  optional int32 activity = 1;
  optional int32 raffleId = 2;
}

message UgcHomeRecommendOverviewInfo {
  optional UgcHomeRecommendDaily daily_recommend_list = 1;  // 地图每日推荐
  optional UgcHomeRecommendHot hot_recommend_list = 2;  // 地图热门推荐(每周必玩)
  repeated UgcHomeRecommendCollection collection_list = 3;  // 地图合集每日推荐
  optional UgcHomeRecommendHot month_recommend_list = 4;  // 本月必玩
  optional UgcHomeRecommendModule collection_module_info = 5;  // 合集模块信息
  // ==== 上面是s12版本之前的旧模块，数据格式不做改变 ====
  // ==== 下面是从s12开始的新模块，推荐内容数据都整理到item_list里面 ====
  optional UgcHomeRecommendModule guess_you_like_info = 6;  // 【猜你喜欢】模块信息
  optional UgcHomeRecommendModule play_record_info = 7;  // 【游玩记录】模块信息
}

message UgcHomeRecommendModule {
  optional string id = 1;
  optional string title = 2;     // 标题
  optional string sub_title = 3; // 副标题

  repeated UgcHomeRecommendItemInfo item_list = 101; // 推荐内容列表
  optional MgrAlgoInfo algo_info = 102; // 推荐算法信息
}

message UgcRecommendSetInfo {
  repeated UgcRecommendItemInfo itemInfo = 1;
  optional MgrAlgoInfo algo_info = 2; // 算法
  optional int64 cacheDuration = 3; // 缓存时长
}

message UgcHomeRecommendItemInfo {
  optional uint64 item_id = 1;
  optional uint32 item_type = 2; // 0-UGC地图 1-合集
  optional string id = 3; // 兼容字符串类型的ID
  optional UgcHomeRecommendCollection collection_info = 6; // 合集信息
  optional string recommend_tag = 101; // 推荐标签（如：近期热门）
}

message UgcRecommendItemInfo {
  optional uint64 item_id = 1;
  optional uint32 item_type = 2; // 0-UGC地图
}

message UgcHomeRecommendDaily {
  repeated UgcHomeRecommendItemInfo item_list = 1;
  optional MgrAlgoInfo algo_info = 2;
}

message UgcHomeRecommendHot {
  repeated UgcHomeRecommendItemInfo item_list = 1;
  optional MgrAlgoInfo algo_info = 2;
}

message UgcHomeRecommendCollection {
  optional string collection_id = 1; // 合集ID
  optional string collection_name = 2; // 合集名称
  optional string collection_desc = 3; // 合集描述
  repeated UgcHomeRecommendItemInfo item_list = 4;
  optional MgrAlgoInfo algo_info = 5;
  optional int32 collection_type = 101; // 合集类型。0=地图合集；1=创作者合集
  optional uint64 creator_id = 6; // 创作者ID（创作者合集用）
  optional bool use_item_head = 7; // 创作者头像是否使用物品头像
}

message UgcHomeRecommendHotTagsData {
  repeated uint32 tag_id_list = 1; // 热门标签id列表
}

message MgrAlgoInfo {
  optional string recid = 1;
  optional string exp_tag = 2;
}

message UgcSharedProfile {
  optional uint64 uid = 1;
  optional uint64 creatorId = 2;
  optional string nickname = 3;
  optional int32 gender = 4;
  optional string profile = 5;
  optional uint64 shortUid = 6;
  optional int32 ugcLv = 7;
}

// 温暖局的类型
enum WarmRoundType {
  WRT_NotWarmRound = 0;       // 非温暖局
  WRT_Normal = 1;             // 普通的 累计的
  WRT_SecondaryNewbie = 2;    // 副玩法新手局
  WRT_AiLab = 3;              // ailab投放
  WRT_ReturningUser = 4;      // 回流用户触发
  WRT_WRT_AiLabV2 = 5;        // ailab投放的第二组实验
  WRT_AiLabArena = 6;         // ailab投放的Arena温暖局
  WRT_AiLabChase = 7;         // ailab投放的Chase温暖局
  WRT_AiLabArena_Equivalent = 8; // ailab投放的Arena对等真人局
}

message IntVector3 {
  optional int32 x = 1;
  optional int32 y = 2;
  optional int32 z = 3;
}

enum TlogFlowType {// 废弃
  TFT_Invalid = 0;
  TFT_SEC_ROUND_START = 1;     // 安全流水：游戏对局开始
  TFT_SEC_ROUND_END = 2;       // 安全流水：游戏对局结束
  TFT_SEC_ROUND_DETAIL = 3;    // 安全流水：对局（轮）过程统计
  TFT_SEC_ROUND_ITEM_GET = 4;  // 安全流水：对局道具获得流水
  TFT_SEC_GAME_SAFE_DATA = 5;  // 安全流水：轻特征上报数据
}

message UgcHotPlayingStoryInfo {
  optional string story_id = 1;   // 动态ID
  optional int64 map_id = 2;     // 地图ID
  optional int64 friend_uid = 3; // 好友UID
  optional string text = 4;       // 文案
  optional PublishItem items = 5; // 地图信息
  optional int64 timestamp = 6;   // 时间戳
  optional string text_no_nick = 7;   // 文案(不包含昵称)
}

message UgcHotPlayingMapInfo {
  optional int64 map_id = 1;
  optional int32 play_cnt = 2;                 // xx人在玩
  repeated int64 friend_uid_list = 3; // 好友ID列表，空表示全局热门
  optional PublishItem items = 4;
  repeated string friend_url_list = 5; // 好友头像url列表
}

enum ModeIdType {
  MIT_Unknown = 0;
  MIT_Classical = 1;      // 经典模式
  MIT_Entertainment = 3;  // 娱乐模式
  MIT_Ugc = 4;            // ugc地图
  MIT_UgcRec = 7;         // ugc精选
}

enum GameModeType {
  GMT_Unknown = 0; // 未知模式
  GMT_Normal = 1; // 匹配模式 - 普通
  GMT_Rank = 2; // 匹配模式 - 排位
  GMT_Ugc = 3; // UGC房间 - 自定义模式 - 乐园
  GMT_Custom = 4; // 自定义房间 - 自定义模式 - 所有
  GMT_Competition = 5; // 赛事房间 - 赛事模式
  GMT_UgcMatch = 6; // 匹配模式 - ugc
  GMT_Special = 7; // 匹配模式 - 特色玩法（副玩法）
  GMT_UgcTest = 8; // UGC房间 多人测试模式
  GMT_UgcCoPlay = 9; // UGC房间 1v1 同游
  GMT_CompetitionMatch = 10; // 匹配模式 - 赛事
  GMT_UgcMultiRoundScore = 11; // ugc房间 - 多轮次积分模式
  GMT_StarP = 12; // 啾灵
}

enum GameTypeId {
  GTI_ROGUELIKE_GAME = 26;// 肉鸽模式
  GTI_KC_GAME = 37; // 赏金模式
  GTI_DF_GAME = 38; // 撤离模式
}

enum RoguelikeLevelDifficulty {
  RLD_NORMAL = 1;
  RLD_HARD = 2;
  RLD_DIFFICULTY = 3;
  RLD_ENDLESS = 4;
}

enum RoomExitType {
  RET_SelfExit = 0; // 自己退出
  RET_TeamExit = 1; // 组队一起退出
  RET_SystemLogicExit = 2; // 系统逻辑退出
  RET_IdleNotice = 3; // 闲置提醒退出
  RET_PartnerMatchingSuccExit = 4; // 搭子匹配成功退出
  RET_SucceedJoin = 5; // 有后续的加入
  RET_SP_LoginExit = 6; // SP 重登自动离队(特指gamesvr重登)
  RET_SP_LeaveSP   = 7; // SP 处于 非SP世界/非SP副本 自动离队
  RET_SP_SelfExit  = 8; // SP 自己手动离队
}

message ModifyExtraInfo {
  optional int32 exitType = 1; // 如果是退出，退出的原因枚举，参考RoomExitType
}

enum RoomReadyReminderType {
  RRRT_OwnerPropose = 0; // 房主发起
  RRRT_SelfIdle = 1; // 玩家闲置
}

enum XiaowoEventType {
  XEF_TotalVisitCountChange = 1;
  XEF_TotalLikeCountChange = 2;
  XEF_LevelUp = 3;
  XEF_PlantLevelUp = 4;
}
enum SceneInviteType {
  SIT_None = 0;
  SIT_Lobby = 1;   //进入大厅
  SIT_XiaoWo = 2;  //进入小窝
  SIT_Farm = 3;  //进入农场
  SIT_House = 4;  //进入农场小屋
  SIT_Cook = 5;  //进入农场小屋
}

enum RoomLevelSettingType {
  RLST_Specific = 0; // 指定
  RLST_Random = 1; // 未指定，随机
}

message BattleHistoryExtraInfo {
  optional int32 battleScore = 1;
  optional int32 matchGrade = 2;
}

message BattleSettlementData {
  optional int64 battleId = 1;    // 对局id
  optional int32 cupsNum = 2;     // 奖杯数量
  repeated int32 ruleId = 3;      // 加成规则id
}

message AlgoRecommendFriendItem {
  optional int64 uid = 1;
  optional int32 reason_id = 2; // FriendRecommendReason
  optional string reason_extra = 3; // 部分理由还需要补充参数, 如N个共同好友,喜欢玩的模式ID, 道具ID, 武器ID等
  optional string reason_text = 4; // 推荐理由文本
}
message AlgoRecommendFriendInfo {
  optional AlgoInfo algo_info = 1;
  repeated AlgoRecommendFriendItem rec_list = 2;
}

message AlgoRecommendAddFriendReasonInfo {
  optional AlgoInfo algo_info = 1;
  optional int32 reason_id = 2;    // FriendAddMotivationType
  optional string reason_extra = 3; // 部分理由还需要补充参数, 如N个共同好友,喜欢玩的模式ID, 道具ID, 武器ID等
  optional string reason_text = 4; // 推荐理由文本
}

message RecommendAddFriendReasonDesc {
  optional AlgoInfo algo_info = 1; // 可能没有
  optional int32 reason_id = 2;    // FriendAddMotivationType
  optional string reason_extra = 3; // 部分理由还需要补充参数, 如N个共同好友,喜欢玩的模式ID, 道具ID, 武器ID等
  optional int32 recommendReason = 4;
  optional string recommendReasonText = 5; // 推荐理由文本
}

enum RoomSpecialSettingType {
  RSST_Unknown = 0;
  RSST_LangRenSha = 1; // 狼人杀配置
  RSST_Ugc = 2; // ugc设置
  RSST_Pwd = 3; // 密码特殊逻辑
  RSST_MainGame = 4; // 主玩法设置
}

// 队友的战斗结算信息
message RoomMemberBattleSettlementInfo {
  optional int64 uid = 1;
  // 目前只有组队单排会有, 其它不一定有, 检查是否有效
  optional com.tencent.wea.xlsRes.MatchGrade matchGrade = 2;
}

// 结算附带的信息
message BattleSettlementExtraInfo {
  optional bool singleRoom = 1;  // 单人游玩
}

message ClientRechargeMetaData {
  optional int32 useMetaData = 1;
  optional int32 buyCommodity = 2;
  optional string param1 = 3;
  optional string param2 = 4;
  optional string param3 = 5;
  optional string param4 = 6;
  optional string param5 = 7;
  optional string param6 = 8;
  optional string param7 = 9;
  optional string param8 = 10;
  optional string param9 = 11;
  optional string param10 = 12;
}

message AiServerData {
  optional int32 aiUseRate = 1;
  optional int32 refreshTime = 2;
  optional int32 refreshInterval = 3;
}

//dsc统计信息
message DSCLoadData {
  optional string region = 1; // 区域名，即envname
  optional int64 timestamp_ms = 2; // 时间戳
  optional int32 total_running_ds_cnt = 3; // 实际所有机器上运行的DS进程实例总数
  optional int32 total_fixed_ds_cnt = 4; // 包含DSC预占位的DS实例总数
  optional int32 total_cpu_core_cnt = 5; // 所有机器的CPU总物理核数
  // free, busy, vary busy的阈值可通过DSA配置调节
  optional int32 free_machine_cnt = 6; // free机器数
  optional int32 busy_machine_cnt = 7; // busy机器数
  optional int32 very_busy_machine_cnt = 8; // very busy机器数
  optional int32 max_ds_cnt = 9; // 遍历本dsc上所有dsa，将每个dsa上的最大ds数累加返回
  optional int32 precheck_passed_dsa_cpu_core_cnt = 10; // 遍历通过预检查的DSA，将它们上报的CPU物理核数累加
  optional int32 precheck_passed_dsa_inuse_cpu = 11; // 遍历通过预检查的DSA，将它们上报的 (CPU使用率 * CPU核数)累加。CPU使用率的值范围为[0,100]
  optional int32 total_inuse_cpu = 12; // 遍历所有DSA，将它们上报的 (CPU使用率 * CPU核数)累加。CPU使用率的值范围为[0,100]
}

message BattleDSCLoadData {
  repeated DSCLoadData dSCLoadData = 2;    //dsc统计信息
}

//battlesvr温暖局、非温暖局数量
message BattleWarmNormalInfo {
  optional int64 timestamp_ms = 1; // 时间戳
  optional int32 warmGuideCnt = 2;  // 新手温暖局
  optional int32 normCnt = 3;  // 非新手温暖局
}

message CounterData {
  optional int64 timestamp = 1;
  optional int32 value = 2;
}

//dsc load battle recore info
message BattleDSCLoadUgcInfo {
  optional int32 singleDSCnt = 1;//ugc单人ds数
  repeated CounterData singleDSCountData = 2;  //ugc单人ds数量信息
}

//dsc统计粗略信息
message DSCLoadBriefData {
  optional int32 coreCnt = 1; //ds cpu核总数
}

//dsc load battle recore info
message BattleDSCLoadRecordInfo {
  optional BattleDSCLoadUgcInfo ugcInfo = 1;//ugc信息
  optional DSCLoadBriefData dscLoadBriefData = 2;//dsc统计粗略信息
}

// 激励玩法的标签内容
message UgcExcitationTags {
  optional string msg = 1;  // 置顶文字
  repeated int32 tags = 2;  // 激励标签
}

enum NoticeMsgParamType {
  NMPT_None = 0;
  NMPT_Text = 1; // 文本
  NMPT_Item = 2; // 道具
  NMPT_Task = 3; // 任务
  NMPT_Achievement = 4; // 成就
  NMPT_Timestamp = 5; // 时间戳(s)
  NMPT_LoadModecfg = 6;// 加载模式配置
}

enum NoticeMsgLoadModecfgType {
  NMLMT_None = 0;// 默认公告配置
  NMLMT_LoadStarPCfg = 1; // 加载starp配置
}

message ThirdPartyActivityParam {
  optional int64 uid = 1;
  optional int64 creator_id = 2;
  optional int32 activity_id = 3;
  optional int32 condition_id = 4;
  optional int64 progress = 5;
  optional int64 seq_id = 6;
}

enum ExitLastSceneReason {
  ELSR_None = 0;
  ELSR_EnterMatch = 1;    // 进入对局
  ELSR_EnterXiaowo = 2;   // 进入小窝
  ELSR_EnterLobby = 3;    // 进入广场
  ELSR_EnterFarm = 4;    // 进入农场
  ELSR_EnterStarPWorld = 5;    // 进入啾灵世界
}

message UgcAiTTSParam {
  optional int32 spkid = 1; // 音色id
  optional int32 emoid = 2; // 情感id
}

message UgcNewAiTTSParam {
  optional int32 voice_type = 1; // 音色
  optional int32 volume = 2;     // 音量[-10, 10]，不填默认0
  optional int32 speed = 3;      // 语速[50,200]，不填默认100
  optional string st = 4;        // 情感，peaceful、exciting、thrill、neutral、sad、angry、cute、fear、poetry、happy、regretful、exciting_strong、aojiao、sajiao、story、raido、call、jieshuo等等
  optional int32 sc = 5;         // 合成音频情感程度,[50,200],默认为100
  optional string codec = 6;     // 编码格式，支持pcm、wav、mp3，默认为pcm
  optional int32 od = 7;         // 由平台返回的参数, 文本的时长信息, 单位为ms
  optional int32 sampling = 8;   // 由平台返回的参数, 合成音频的采样率
}

message AigcHttpReqParam {
  optional int32 gameType = 1;
  optional int64 version = 2;
}

message AigcHttpResParam {
  optional int32 errorCode = 1;
  optional string errorMsg = 2;
}

message UgcAiMovieParam {
  optional int32 fps = 1;
  optional int32 size_x = 2;
  optional int32 size_y = 3;
  optional int32 duration = 4;
  optional int32 filesize = 5;
}

// 战斗创建原因
enum BattleCreateReason {
  BCR_Normal = 0; // 正常请求创建
  BCR_Migrate = 2; // DS 超时迁移创建
  BCR_SceneChange = 3;  // 场景切换
}

message SampleRoomRecord {
  optional int64 id = 1; // xiaowoid
  optional int32 visitorCount = 2;
  map<int64, int64> takePlaceUid = 3; // 占位的玩家 key是uid，v是时间。
}

message SampleRoomAllocator {
  repeated SampleRoomRecord SampleRooms = 1;
}

// 资源类型
enum UgcResType {
  EURT_Group = 0;               // 模组 (模组类 UgcResTypeGroup)
  EURT_Charactor = 1;           // 角色 (模组类 UgcResTypeGroup)
  EURT_Vehicle = 2;             // 载具 (模组类 UgcResTypeGroup)
  EURT_Weapon = 3;              // 武器 (模组类 UgcResTypeGroup)
  EURT_Animation = 4;           // 动画
  EURT_Camera = 5;              // 镜头
  EURT_Mesh = 6;                // 网格
  EURT_Texture = 7;             // 纹理
  EURT_Code = 8;                // 代码
  EURT_Voice = 9;               // 音频
  EURT_DialogueImage = 10;      // 剧情图片
  EURT_Task = 11;               // 任务
  EURT_CommonTexture = 12;      // 自定义图片
  EURT_Equipment = 13;          // 装备 (模组类 UgcResTypeGroup)
  EURT_Props = 14;              // 道具 (模组类 UgcResTypeGroup)
  EURT_CustomCharacter = 15;    // 角色-自制
  EURT_AIGenerateTexture = 16;  // AI生成贴图
  EURT_CustomIcon = 17;         // 通用图片
  EURT_Achievement = 18;        // 成就
  EURT_AchiCustomIcon = 19;     // 成就图标自定义图片
  EURT_CompressionResource = 20;// 压缩资源
  EURT_NavMesh = 21;            // 导航网格
  EURT_DynamicMesh = 22;        // 可造型元件加速数据
  EURT_UGCScript = 23;          // ugc脚本
  EURT_AssemblyPart = 24;       // 星宝衣架
  EURT_UGCPreset = 25;          // 预设
  EURT_Programme = 26;          // 编程 (模组类 UgcResTypeGroup)
  EURT_Interface = 27;          // 界面
  EURT_3DModel = 28;            // 混元3D模型
  EURT_Quadruped = 29;          // 四足
  EURT_Group_Common = 1000;     // 模组类 (模组类 UgcResTypeGroup)
}

// 模组类型的资源，有不同的逻辑，注意保持和UgcResType一样的枚举值(需跟策划确认某个资源类型是否属于模组类资源)
enum UgcResTypeGroup {
  EURTG_Group = 0;               // 模组
  EURTG_Charactor = 1;           // 角色
  EURTG_Vehicle = 2;             // 载具
  EURTG_Weapon = 3;              // 武器
  // 为兼容旧数据，请注意以下类型存在和 EURT_Group_Common 互转
  EURTG_Equipment = 13;          // 装备
  EURTG_Props = 14;              // 道具
  EURTG_Programme = 26;          // 编程
}

enum SearchType{
  UgcSearch_Map = 0;
  UgcSearch_Topic = 1;
  UgcSearch_All = 2;
  UgcSearch_Editor = 3;
  UgcSearch_Collection = 4;
}

enum TopicTyp{
  UgcTopicType_UnKnow = 0;  //未知话题
  UgcTopicType_Gold = 1;    //金色话题
  UgcTopicType_Blue = 2;    //蓝色话题
}

message RaffleBIQueryParam {
  optional string flowid = 1;
  optional string req_time = 2;
  optional string sceneid = 3;
  optional string userid = 4;
  optional string credid = 5;
  optional RaffleBIQueryParamData data = 6;
}

message RaffleBIQueryParamData {
  optional string partition = 1;
  optional string roleid = 2;
  optional string drawround = 3;
  optional string drawtype = 4;
  repeated RaffleBIQueryItem haveitems = 5;
  optional string ext1 = 6;
  optional string ext2 = 7;
  optional string ext3 = 8;
}

message RaffleBIQueryItem {
  optional string id = 1;
  optional string itemnum = 2;
}

message RaffleBIQueryResult {
  optional string credid = 1;
  optional string flowid = 2;
  optional string req_time = 3;
  optional string sceneid = 4;
  optional string userid = 5;
  optional string errcode = 6;
  optional string errdesc = 7;
  optional RaffleBIQueryResultData data = 8;
}

message RaffleBIQueryResultData {
  repeated RaffleBIQueryItem items = 1;
}

enum ClubStatusType {
  CST_Normal = 0;     // 正常
  CST_Auditing = 1;   // 审核中
  CST_Banned = 2;     // 已封禁
  CST_Dissolved = 3;  // 已解散
}

enum ClubMSDKGroupType {
  CMGT_None = 0;
  CMGT_QQ = 1;
  CMGT_WX = 2;
}

message ClubMSDKGroupInfo {
  optional string groupId = 2;
  optional ClubMSDKGroupType groupType = 3;
}

message ClubBasicInfo {
  optional int64 clubId = 1;        //社团Id
  optional string name = 2;         //社团名称
  optional int32 icon = 3;          //图标
  optional int64 heat = 4;          //社团热度
  optional int64 ownerUid = 5;      //当前团长
  optional int32 status = 6;        //社团状态，ClubStatusType
  optional string msdkId = 7;       //MSDK返回的ID，QQ/WX加群用到
  optional string title = 8;        //星团称号
  optional int32 memberCount = 9;   //成员数量
  optional int32 boyCount = 10;     //男性数量
  optional int32 girlCount = 11;    //女性数量
  optional int64 creatorId = 13;    //创建者
  optional int64 createTime = 14;   //创建时间
  optional string msdkZoneId = 15;  //MSDK
  optional string msdkAreaId = 16;  //MSDK
  optional string brief = 17;       //社团宣言
  repeated int32 labels = 18;       //社团标签
  optional bool requireApprove = 19;  // 加入需要审批
  optional bool ownerHomeCommon = 20; // 团长小窝作为公共家园
  optional string msdkGroupName = 21; // 外部群组Id
  optional ClubStatistic statistic = 22;  // 社团统计数据
  optional bool denyApply = 23;     //拒绝加入
  optional ClubMSDKGroupInfo msdkGroupInfo = 24; // MSDK社团信息(废弃）
  optional HotData hotData = 25;    //不存DB的一些实时数据
  optional int64 clubShortId = 26;  // 社团短ID
  optional bool geoEnabled = 27;    // 开启社团归属地，同时开启排行榜
  optional PlayerRankGeoInfo geoInfo = 28; // 社团归属地信息
  optional int64 lastWeekHeat = 29;  //上周社团热度
  optional ClubActiveScoreInfo scoreInfo = 30;  //社团活跃评分

  message HotData {
    optional string ownerProfile = 1; // 团长头像信息
    optional string ownerName = 2; // 团长游戏名称
  }
}

message ClubActiveScoreInfo {
  optional int32 score = 1;                 // 总分
  optional int32 ownerLoginDaysScore = 2;  // 团长活跃天数评分
  optional int32 ownerTotalRmbScore = 3;   // 团长消费金额评分
}

message ClubRankInfo {
  optional int32 rankType = 1;  // 全服/市/区排名
  optional int32 geo = 2;   // 地区
  optional int64 week = 3;      // 周开始时间（second)
  optional int32 rank = 4;      // 排名
}

message ClubHistoryRankInfo {
  repeated ClubRankInfo histories = 1;
}

message ClubExtraInfo {
  repeated int64 publicUgcMapIds = 1; //团队共享地图ID
  optional int64 commonXiaowoId = 2;  //团队小窝ID，团长设置分享后即为团长小窝ID，否则为0
  repeated int64 pinnedUgcMapIds = 3; //置顶团队共享地图ID
  optional int32 msdkGroupType = 4; // 当前使用的是什么群, ClubMSDKGroupType
  optional string groupIdQQ = 5;
  optional string groupIdWX = 6;
  optional int32 totalCupsNum = 8;       // 社团总奖杯数
  optional ClubWeekSettleInfo latestWeekSettle = 9;// 最新一次周结算
}

message ClubWeekSettleInfo {
  optional int64 week = 1; // 周开始时间（second)
  optional bool isSettled = 2; // 是否已结算
  optional bool isHonorClub = 3; // 是否是荣誉社团
  repeated ClubRankInfo rankInfo = 4; // 结算排名
  repeated ClubMemberDetail member = 6;// 结算活跃成员
  optional int64 heat = 7; // 社团热度
}

message ClubStatistic {
  optional int64 numChat = 1; // 社团频道发言次数
  optional int64 numVisitHome = 2; // 探访社团成员星家园次数
  optional int64 numMatch = 3; // 与社团成员组队游玩经典或娱乐模式
  optional int64 numStarWorld = 4; // 与社团成员组队游玩星世界
}

message ClubMemberActivity {
  optional string reserved = 1;
  optional int64 lastSaveXiaoWoMs = 2; // 最后更新小窝的时间
  optional int64 lastDegreeUpgradeMs = 3; // 最后升级时间
  optional int64 lastPublishUgcMapMs = 4; // 最后发布星图时间
  optional int64 lastChampionMs = 5; // 最近获得冠军时间
  optional int32 lastChampion = 6; // 最近获得几连冠
  optional int64 lastPlayUgcMapMs = 7; // 最近玩过星图时间
  optional int64 lastPlayModeMs = 8; // 最近玩过某游戏模式的时间
  optional int32 lastPlayModeId = 9; // 最近玩过的游戏模式

  optional com.tencent.wea.xlsRes.QualifyingDegreeType lastDegreeUpgradeType = 100;
  optional int32 lastDegreeUpgradeStar = 101;
}

message ClubMemberDetail {
  optional int64 uid = 1; //玩家UID
  optional int32 roleType = 2; //角色类型，0普通玩家，1团长，2管理员
  repeated int64 shareUgcMapId = 3; //玩家分享的收藏地图
  optional PlayerRankGeoInfo location = 4; // 玩家归属地
  optional int32 gender = 5; // 玩家性别
  optional int32 level = 6;
  optional string name = 7;
  optional com.tencent.wea.xlsRes.PlayerStateType playerState = 8;
  optional int32 atXiaowo = 9;
  optional int64 lastOnlineMs = 10;
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifyingInfo = 11;
  optional int64 applyTime = 12;
  optional int64 joinTime = 13;
  optional string openId = 14;
  repeated PlayerDressItemInfo dressItemInfo = 15;
  optional com.tencent.wea.xlsRes.UgcAuthType authType = 16;
  optional string profile = 17; // 头像
  optional int64 lastXiaowoSaveMs = 18; // 小窝最后保存时间
  optional bool hideRoomInvitation = 19; // 屏蔽组队邀请
  optional int64 xiaoWoId = 20; // 玩家小窝ID
  optional int64 ugcCreatorId = 21;
  optional int32 heat = 22;
  optional ClubStatistic statistic = 23;
  optional int64 heatDay = 24;
  optional bool returning = 25; // 回归玩家标志
  optional int64 returnExpiredSec = 26; // 回归玩家过期时间戳
  optional ClubMemberActivity recentActivity = 27; // 玩家最近活动
  optional int64 shortUid = 28; // 玩家短UID
  optional bool hideProfileToFriend = 29; // 对好友隐藏个人信息
  optional bool hideProfileToStranger = 30; // 对陌生人隐藏个人信息
  optional int32 cupsNum = 31;  // 奖杯数
  optional int32 curWeekHeat = 32;
  optional int32 lastWeekHeat = 33;
  optional bool hidePlayerStatus = 34; // 是否隐身
  optional bool isOneClickJoin = 35;  // 是否为一键加入
  optional int32 cupsCycle = 36;  // 奖杯周目
  optional int32 cupsTotal = 37;  // 所有周目奖杯
}


message ClubMemberInfo {
  repeated ClubMemberDetail members = 1; //玩家列表
}

message ClubInfo {
  optional int64 clubId = 1;
  optional int64 creatorId = 2;
  optional int64 createTime = 3;
  optional ClubBasicInfo basicInfo = 5;
  optional ClubMemberInfo memberInfo = 6;
  optional ClubExtraInfo extraInfo = 7;
}

message ClubLogRecord {
  optional int64 logTime = 1;
  optional int32 logType = 2;
  optional int64 logId = 3;
  optional ClubLogItem logItem = 4;
}

message ClubLogReqData {
  optional int32 logClass = 1;				// 日志类别
  optional int32 num = 2; 						// 请求日志条数：从logId开始（不包括），按时间逆序获取指定条数日志
  optional int64 logId = 3; 					// 起始日志ID
}

message ClubLogRspData {
  optional int32 logClass = 1;						// 日志类别
  optional int32 totalNum = 2; 						// 该类别日志总条数
  repeated ClubLogRecord recordData = 3;	// 日志信息
}

message ClubLogMemberInfo {
  optional int64 uid =  1;
  optional string name =  2;    // 日志记录时玩家的昵称
  optional int32 roleType = 3;  // 角色类型，0普通玩家，1团长，2管理员 (ClubType in cs_club.proto)
}

message ClubLogItem {
  oneof data {
    LogInfo logInfo = 1;                // 日志类型通用结构
    RankSettleInfo rankSettleInfo = 2;  // 历程-排行榜结算
  }
  message LogInfo {
    optional ClubLogMemberInfo member = 1;    // 日志主体
    optional ClubLogMemberInfo operator = 2;  // 操作人
  }
  message RankSettleInfo {
    optional ClubWeekSettleInfo settleInfo = 1;
  }
}

message UgcMatchLobbyMapLabel {
  optional int32 style = 1;
  optional string text = 2;
}

// 用于活动信息配置
message UgcMatchLobbyMapBriefExtra_Activity {
  optional int32 jumpId = 1;  // 活动跳转
  optional string activityDesc = 2;  // 活动说明
  optional int64 activityItem = 3;  // 活动道具-气泡展示
  optional string jumpParam = 4;  // 跳转参数
}

message UgcMatchLobbyMapBriefExtra_MultiMatch {
  optional PublishItem pubItem = 1;  // 组合的地图的封面地图
  optional bool isCompilations = 2;  // 是否是组合地图
  repeated PublishItem pubItems = 3;  // 所有的随机地图
  optional string name = 4;  // 名字
  optional UgcMatchLobbyMapLabel label = 5;  // 标签
  optional int32 pointNumber = 6;  // 出生点
  repeated int32 campInfos = 7;  // 阵营信息
  optional int64 configUgcId = 8;  // 地图集id，匹配需要传递这个值
  optional bool isMultiRound = 9;  // 是否开启多轮
  optional int32 roundTimes = 10;  // 轮次次数
  optional bool isShowCoverVideo = 11;  // 显示视频封面
  optional UgcMatchLobbyMapBriefExtra_Activity activityInfo = 12;  // 活动展示信息
}

message UgcMatchLobbyMapBriefExtra_LobbyV2_Button {
  optional string text = 1;  // 按钮文本
  optional PublishItem pubItem = 2;  // 地图详细信息 匹配id需要用这个填充
  optional int32 singlePlayType = 3;  // 参考UgcMatchSinglePlayType
  optional int32 sort = 4;  // 排序字段 越小越前
  optional int32 roomInfoId = 5;  // 服务填充
  optional UgcMatchMapBriefFromRemote_Server_BirthInfo birthInfo = 6;  // 按钮使用的出生信息
  optional bool miniGameDefault = 7;  // 小游戏默认按钮
}

message UgcMatchLobbyMapBriefExtra_LobbyV2 {
  optional int64 cfgId = 1;  // 如果是组合地图，匹配时需要传这个id上来
  optional PublishItem pubItem = 2;  // 封面地图
  optional bool isShowDetail = 3;  // 是否展示详情页入口
  repeated UgcMatchLobbyMapBriefExtra_LobbyV2_Button buttons = 4 ;  // 按钮配置
  optional UgcMatchMapBriefFromRemote_LobbyV2_Page extra = 5 ;  // 额外展示信息
  optional UgcMatchLobbyMapLabel label = 6;  // 标签信息
  optional int32 ugcLobbyJumpId = 7;  // ugc广场跳转id
  optional string ugcLobbyJumpDesc = 8;  // ugc广场跳转描述
  optional bool isShowInstruction = 9;  // 显示地图说明
  optional bool isShowCoverVideo = 10;  // 显示视频封面
  optional string recommendCoverCdn = 11;  // 上推荐后的封面cdn地址
}

message UgcMatchLobbyMapBriefExtra {
  optional UgcMatchLobbyMapBriefExtra_MultiMatch multiMatch = 1;
  optional UgcMatchLobbyMapBriefExtra_LobbyV2 lobbyV2 = 2;
  optional UgcMatchLobbyMapBriefExtra_MultiMatch multiLobby = 3;
  optional UgcMatchLobbyMapBriefExtra_MultiMatch multiCommon = 4;  // 合集匹配通用字段
}

message UgcMatchLobbyMapBrief {
  optional PublishItem pubItem = 1;  // 组合或其他则不使用
  optional int64 openTime = 2;
  optional int64 closeTime = 3;
  optional int32 roomInfoId = 4;  // 房间模式id
  optional bool isTop = 5;  // 废弃
  optional int32 priority = 6;  // 优先级
  optional string desc = 7;  // 描述
  optional int32 recommend = 8;  // 是否推荐
  //  optional bool isCompilations = 9;  // 是否是组合地图 废弃
  //  repeated PublishItem pubItems = 10;  // 所有的随机地图 废弃
  //  optional string name = 11;  // 名字 废弃
  optional UgcMatchLobbyMapLabel label = 12;  // 标签 单图
  //  optional int32 pointNumber = 13;  // 出生点 废弃
  //  repeated int32 campInfos = 14;  // 阵营信息 废弃
  //  optional int64 configUgcId = 15;  // 如果是组合地图，匹配时需要传这个id上来 废弃
  optional int32 singlePlayType = 16;  // 参考UgcMatchSinglePlayType
  optional int32 briefExtraType = 17;  // 参考UgcMatchLobbyMapBriefExtraType
  optional UgcMatchLobbyMapBriefExtra extraInfo = 18;  // 拆出来的非单图匹配的信息
  optional int32 modeTypeId = 19;  // 客户端接入用星世界排序玩法id
  optional UgcMatchMapBriefFromRemote_Server_BirthInfo birthInfo = 20;  // 单图使用的出生信息
  optional UgcMatchLobbyMapBriefExtra_Activity activityInfo = 21;  // 活动展示信息
  optional bool isReflowRecommend = 22;  // 是否回流推荐
}

message UgcMatchLobbyMapConfig {
  optional bool showUgcBp = 1;  // 是否显示ugcBP
}

message PublishStruct{
  repeated PublishItem maps = 1;
  optional AlgoInfo info = 2;
}

message UgcMapUrlInfo {
  optional int64 ugcId = 1;
  optional string url = 2;
}

message EditorInfoItem{
  optional string editorName = 1;
  optional string editorAvatar = 2; //头像
  optional int32 gender = 3; // 性别
  optional int32 ugcLv = 4; // ugc工匠等级
  optional int64 fansCount = 5;    //粉丝数量
  optional int64 pubCount = 6;     //发布数量
  optional bool isSub = 7;  //是否订阅
  optional string hotKeyWord = 8;  //热搜关键字
  optional int64 creatorId = 9;  //作者creatorId
  optional int64 uid = 10;  //作者uid
  optional com.tencent.wea.xlsRes.UgcAuthType ugcAuthType = 11;
  repeated PlayerDressItemInfo dressInfo = 12;
}

message TopicInfoItem{
  optional string topicName = 1;
  optional int64 giveLikeCount = 2; //点赞数 (话题总数)
  optional int64 playCount = 3;    //游玩数 (话题总数)
  optional int64 mapCount = 4;     //地图数 (话题总数)
  optional string hotKeyWord = 5;  //热搜关键字
  optional string jumpUrl = 6; //跳转链接
  optional TopicTyp topicType = 7; //话题类型
}

enum MatchIsolateType {
  MIT_NORMAL = 0; //普通玩家
  MIT_CHEAT = 1;  //作弊玩家
  MIT_ASSISTANT_SIMULATOR = 2;  //手游助手模拟器玩家
  MIT_THIRD_SIMULATOR = 3;  //第三方模拟器玩家
}

message UgcRemoteConfig {
  optional ManagementTagConf management_tag_conf = 1;
  optional CollectStarsConf collect_stars_conf = 2;
  optional XiaowoWhiteListConf xiaowo_whitelist_conf = 3;
  optional UgcGlobalEditUnlimit ugcGlobalEditUnlimit = 4;
  optional MapScoreLabelConf mapScoreLabelConf = 5;
  optional WhiteListStatusConfig whiteListStatusConfig = 6;
  optional UgcDatastoreAccessPermissionConfig ugcDatastoreAccessPermissionConfig = 7;

  // type=1 运营标签配置
  message ManagementTagConf {
    repeated ManagementTagItem list = 1;
    message ManagementTagItem {
      optional uint32 tag_id = 1;
      optional string tag_name = 2;
    }
  }
  // type=2 集星配置
  message CollectStarsConf {
    repeated int64 map_id_list = 1;
    optional int64 current_refresh_time = 2;
    optional int64 next_refresh_time = 3;
    repeated CollectStarsItem list = 4;
    message CollectStarsItem {
      repeated int64 map_id_list = 1;
      optional int64 current_refresh_time = 2;
      optional int64 next_refresh_time = 3;
    }
  }
  // type=3 小窝白名单配置
  message XiaowoWhiteListConf {
    repeated int64 uids = 1;
  }

  message UgcGlobalEditUnlimit{
    optional int64 edit_unlimit_start_time = 1;
    optional int64 edit_unlimit_end_time = 2;
  }

  // type=5 地图评分标签配置
  message MapScoreLabelConf{
    optional MapScoreShowThresholdConf score_show_threshold = 1;  //地图评分展示门槛配置
    optional MapScoreDimLabelShowConf score_dim_label_show_conf = 2;  //地图评分维度标签配置
    optional MapScoreComLabelShowConf score_com_label_show_conf = 3;  //地图评分综合标签配置
  }

  message MapScoreShowThresholdConf{
    optional int64 score_player_count = 1; // 评价人数
    optional float score = 2;   // 平均分数
    optional MapScoreShowThresholdMapLimit map_limit = 3;
    message MapScoreShowThresholdMapLimit{
      optional int64 player_count = 1;
      optional int64 like_count = 2;
      optional int64 collect_count = 3;
    }
  }

  message MapScoreDimLabelShowConf{
    repeated MapScoreDimLabelShowConfData conf_data = 1;
    message MapScoreDimLabelShowConfData{
      optional int32 label_id = 1;
      optional float left_score = 2;  // 左闭
      optional float right_score = 3; // 右开
      optional string content = 4;
      optional int32 dim_id = 5;
    }
  }

  message MapScoreComLabelShowConf{
    repeated MapScoreComLabelConfData conf_data = 1;
    message MapScoreComLabelConfData {
      optional int32 label_level = 1; // 标签档位，值越大档位越高
      optional string label_content = 2; // 综合标签文本
      optional int64 score_player_left_count = 3; // 评价人数
      optional int64 score_player_right_count = 4; // 评价人数
      optional float one_score_left_limit = 5;
      optional float one_score_right_limit = 6;
      optional float all_score_left_limit = 7;
      optional float all_score_right_limit = 8;
      optional int64 map_player_left_count = 9; // 地图游玩人数
      optional int64 map_player_right_count = 10; // 地图游玩人数
      optional int32 label_content_id = 11; // 综合标签id(跟label_content一一对应，不允许用0)
    }
  }

  message WhiteListStatusConfig {
    repeated WhiteListServiceConfig services = 1;
    repeated WhiteListTable tables = 2;
    repeated WhiteListTableMeta table_metas = 3;
  }

  message UgcDatastoreAccessPermissionConfig {
    repeated UgcDatastoreAccessPermissionConfigData config_data = 1;
    message UgcDatastoreAccessPermissionConfigData {
        optional int64 map_id = 1;
        optional int64 allowed_map_id = 2;
    }
  }
}

message UgcRecommendGameInfo{
  optional int64 map_id = 1; // 本关的地图ID
  optional bool is_pass = 2;  // 是否通关
  optional int64 battle_use_time = 3;  // 花费时间 ms
}

message UgcRecommendMap {
  optional uint64 map_id = 1; // 地图ID
  optional uint32 rcmd_pos = 2;
  optional int32 style_id = 3; // 样式ID, 0=无标签, 1=基本样式
  optional string tag = 4;  // 标签文本
}

// 资源社区推荐页信息
message UgcResHomePageInfo {
  optional bool hasBanner = 1;                  // 是否有活动横幅
  optional UgcResHomePagBannerInfo banner = 2;  // 横幅信息
  optional UgcResHomePageSet resSet = 3;        // 资源集合(合集)
  optional UgcKeyInfo keyInfo = 4;              // cos info
  optional AlgoInfo alogInfo = 5;                   // 推荐算法信息
}

// 资源社区推荐页活动横幅信息
message UgcResHomePagBannerInfo {
  repeated UgcResHomePagBannerDetail homeBanner = 1; // 首页横幅活动
  repeated UgcResHomePagBannerDetail moreBanner = 2; // 更多里的活动
}

// 资源社区推荐页活动横幅信息
message UgcResHomePagBannerDetail {
  optional string picUrl = 1;       // banner图地址
  optional string jumpUrl = 2;      // 跳转地址
  optional uint64 startTime = 3;    // 生效开始时间
  optional uint64 endTime = 4;      // 生效结束时间
}

// 资源社区推荐页合集信息
message UgcResHomePageSet {
  repeated UgcResHomePageSetDetail setDetail = 1;
}

// 资源社区推荐页合集信息
message UgcResHomePageSetDetail {
  optional int32 index = 1;      // 编号
  optional string name = 2;      // 合集名称
  optional string desc = 3;      // 合集描述
  repeated PublishItem items = 4; // 资源信息
  optional int32 showNum = 5;    // 合集展示数量
}

message UgcResBagList {
  repeated UgcResBagInfo res_data = 1;
}

message UgcResBagInfo {
  optional int64 ugcId = 1;      // id
  optional int64 createTime = 2; // 加入资源库时间
}

message UgcResParam {
  optional bool isAllResType = 1;   // 是否全部资源类型
  optional UgcResType resType = 2;   // 资源类型
  optional int32 category = 3;      // 资源大类别
  optional int32 subCategory = 4;   // 资源小类别
  repeated int32 labels = 5;        // 标签
  map<int64, int32> idNum = 6;  // id 数量
  optional bool isResPrivate = 7;    // 是否是私有资源
  optional bool isResPubCosPath = 8; // 私有资源是否在pub路径
  optional uint32 filterType = 9; // 0 -> 不筛选; 1 -> 只搜ai
}

message UgcResModifyPubParam {
  optional UgcResType resType = 1;       // 资源类型
  optional bool isModifyCategory = 2;    // 是否修改大类别
  optional int32 category = 3;           // 大类别
  optional bool isModifySubCategory = 4; // 是否修改小类别
  optional int32 subCategory = 5;        // 小类别
  optional bool isModifyLabels = 6;      // 是否修改标签
  repeated int32 labels = 7;             // 标签
  optional bool isModifyDesc = 8;        // 是否修改描述
  optional string desc = 9;               // 描述
}

message TopicInfo {
  optional int32 type = 1;      // 类型
  optional int32 typeId = 2;  //id
  optional string typeText = 3;  //文本
}

// 配置时间范围
message UgcMatchMapBriefFromRemote_TimeRange {
  optional int64 open_time = 2;  // 开启时间
  optional int64 close_time = 3;  // 结束时间
}

// 版本控制范围
message UgcMatchMapBriefFromRemote_VersionRange {
  optional string start_version = 1;  // 开启版本号
  optional string close_version = 2;  // 结束版本号
}

message UgcMatchMapBriefFromRemote_Label {
  optional int32 style = 1;  // 标签类型
  optional string text = 2;  // 标签文本
}

message UgcMatchMapBriefFromRemote_BirthInfo {
  optional int32 point_number = 1;  // 出生点
  repeated int32 camp_info = 2;  // 阵营信息 根据地图信息从大到小分配阵营id
}

// 服务填充带上分配的阵营id信息
message UgcMatchMapBriefFromRemote_Server_BirthInfo {
  optional int32 pointNumber = 1;  // 出生点
  repeated BattleCamp campInfo = 2;  // 阵营信息
  optional int64 campDimId = 3;  // 阵营维度id
}

message UgcMatchMapBriefFromRemote_Match {
  repeated UgcMatchMapBriefFromRemote_TimeRange time = 1;  // 时间范围
  optional int32 priority = 2;  // 优先级
  optional string desc = 3;  // 描述
  optional string name = 4;  // 地图名
  optional UgcMatchMapBriefFromRemote_Label label = 5;  // 标签
  optional int32 specified_room_info_id = 6;  // 指定模式id
  optional UgcMatchMapBriefFromRemote_BirthInfo birth_info = 7;  // 出生信息
}

message UgcMatchMapBriefFromRemote_ModeTypeInfo {
  optional int32 mode_type_id = 1;
  optional int32 sort = 2;
  optional bool is_show_mini_game = 3;  // 小游戏展示
}

message UgcMatchMapBriefFromRemote_Wright_Round {
  optional int32 weight = 1;  // 轮次随机权重
  optional int32 begin_round = 2;  // 可以开始随机的轮次
  optional int32 baodi_round = 3;  // 保底开始的轮次
  optional int32 max_times = 4;  // 最多可以随机到的次数
  optional string load_desc = 5;  // loading文案
  optional int32 bind_round = 6;  // 绑定轮次，只会在这个轮次随机，若存在绑定轮次，那么就不再处理非绑定配置
}

message UgcMatchMapBriefFromRemote_Weight {
  optional int64 ugc_id = 1;  // 地图id
  optional int32 weight = 2;  // 地图权重
  optional UgcMatchMapBriefFromRemote_Wright_Round round_info = 3;  // 轮次信息
}

message UgcMatchMapBriefFromRemote_MultiMatch {
  repeated UgcMatchMapBriefFromRemote_TimeRange time = 1;  // 时间范围
  optional int64 cover_ugc_id = 2;  // 封面地图
  optional int32 priority = 3;  // 优先级
  optional string desc = 4;  // 描述
  repeated UgcMatchMapBriefFromRemote_Weight ugc_infos = 5;  // 所有地图
  optional int32 point_number = 6;  // 出生点
  repeated int32 camp_info = 7;  // 阵营信息
  optional string name = 8;  // 合集名
  optional UgcMatchMapBriefFromRemote_Label label = 9;  // 标签
  optional UgcMatchMapBriefFromRemote_VersionRange version = 10; // 生效版本
  optional int32 specified_room_info_id = 11;  // 指定模式id
  optional UgcMatchMapBriefFromRemote_ModeTypeInfo match_type_info = 12;  // 星世界推荐通用配置
  optional int32 recommend = 13;  // 是否推荐
  repeated int32 cloud_game_type = 14;  // 允许的游戏平台，空表示允许所有，// 0:app, 1:先锋云游, 2:微信小游戏, 3:qq小游戏
  optional bool is_show_cover_video = 15;  // 显示封面视频
  optional UgcMatchMapBriefFromRemote_Activity activity_info = 16;  // 活动信息
  optional bool is_reflow_recommend = 17;  // 是否回流推荐
}

message UgcMatchMapBriefFromRemote_Plaza {
  repeated UgcMatchMapBriefFromRemote_TimeRange time = 1;  // 时间范围
  optional int32 specified_room_info_id = 6;  // 指定模式id
  optional UgcMatchMapBriefFromRemote_BirthInfo birth_info = 7;  // 出生信息
}

message UgcMatchMapBriefFromRemote_Lobby {
  repeated UgcMatchMapBriefFromRemote_TimeRange time = 1;  // 时间范围
  optional int32 priority = 2;  // 优先级
  optional string desc = 3;  // 描述
  optional int32 recommend = 4;  // 是否推荐
  optional int32 specified_room_info_id = 5;  // 指定模式id
  optional UgcMatchMapBriefFromRemote_ModeTypeInfo match_type_info = 6;  // 星世界推荐通用配置
  optional UgcMatchMapBriefFromRemote_BirthInfo birth_info = 7;  // 出生信息
}

message UgcMatchMapBriefFromRemote_LobbyV2_Button {
  optional string text = 1;  // 按钮文本
  optional int64 ugc_id = 2;  // 地图Id
  optional int32 single_play_type = 3;  // 参考UgcMatchSinglePlayType
  optional int32 sort = 4;  // 排序字段 越小越前
  optional int32 room_info_id = 5;  // 端内服务填充
  optional int32 specified_room_info_id = 6;  // 指定模式id
  optional bool mini_game_default = 7;  // 小游戏默认按钮
  optional UgcMatchMapBriefFromRemote_BirthInfo birth_info = 8;  // 出生信息
  optional UgcMatchMapBriefFromRemote_Server_BirthInfo server_birth_info = 9;  // 端内服务填充
}

message UgcMatchMapBriefFromRemote_LobbyV2_PageBase {
  optional string name = 1;  // 页签名, 排行榜不自定义
  optional bool show = 2;  // 是否展示
  optional bool show_first = 3;  // 是否是第一个页签展示
}

message UgcMatchMapBriefFromRemote_LobbyV2_PageRank {
  optional UgcMatchMapBriefFromRemote_LobbyV2_PageBase base = 1;  // 基础配置
  optional int64 rank_id = 2;  // 排行榜类型
}

message UgcMatchMapBriefFromRemote_LobbyV2_PageMoreMap_One {
  optional int64 ugc_id = 1;  // 地图id
  optional string icon_name = 2;  // icon名 废弃
  optional int32 icon_jump_id = 3;  // icon跳转id 废弃
  optional int32 sort = 4;  // 排序字段 越小越前面
}

message UgcMatchMapBriefFromRemote_LobbyV2_PageMoreMap {
  optional UgcMatchMapBriefFromRemote_LobbyV2_PageBase base = 1;  // 基础配置
  repeated UgcMatchMapBriefFromRemote_LobbyV2_PageMoreMap_One maps = 2;  // 所有的地图配置
  optional string icon_name = 3;  // icon名
  optional int32 icon_jump_id = 4;  // icon跳转id
}

message UgcMatchMapBriefFromRemote_LobbyV2_Achievement {
  optional UgcMatchMapBriefFromRemote_LobbyV2_PageBase base = 1;  // 基础配置
//  optional int64 ugc_id = 2;  // 直接用封面了
}

message UgcMatchMapBriefFromRemote_LobbyV2_Page {
  optional UgcMatchMapBriefFromRemote_LobbyV2_PageRank rank = 1;  // 排行榜配置
  optional UgcMatchMapBriefFromRemote_LobbyV2_PageMoreMap more_map = 2;  // 更多地图配置
  optional UgcMatchMapBriefFromRemote_LobbyV2_Achievement achievement = 3;  // 成就配置
}

message UgcMatchMapBriefFromRemote_LobbyV2 {
  repeated UgcMatchMapBriefFromRemote_TimeRange time = 1;  // 时间范围
  optional int32 priority = 2;  // 优先级
  optional string desc = 3;  // 描述
  optional int32 recommend = 4;  // 是否推荐
  optional UgcMatchMapBriefFromRemote_VersionRange version = 5; // 生效版本
  optional int64 cover_ugc_id = 6; // 封面地图id
  optional bool is_show_detail = 7;  // 是否展示详情页入口
  repeated UgcMatchMapBriefFromRemote_LobbyV2_Button buttons = 8 ;  // 按钮配置
  optional UgcMatchMapBriefFromRemote_LobbyV2_Page extra = 9 ;  // 额外展示信息
  optional UgcMatchMapBriefFromRemote_Label label = 10;  // 标签信息
  optional UgcMatchMapBriefFromRemote_ModeTypeInfo match_type_info = 11;  // 星世界推荐通用配置
  optional int32 ugc_lobby_jump_id = 12;  // ugc广场跳转id
  optional string ugc_lobby_jump_desc = 13;  // ugc广场跳转描述
  repeated int32 cloud_game_type = 14;    // 允许的游戏平台，空表示允许所有，// 0:app, 1:先锋云游, 2:微信小游戏, 3:qq小游戏
  optional bool is_show_instruction = 15;  // 是否展示地图说明
  optional bool is_show_cover_video = 16;  // 显示封面视频
  optional string recommend_cover_cdn = 17;  // 上推荐后的封面cdn
  optional UgcMatchMapBriefFromRemote_Activity activity_info = 18;  // 活动信息
  optional bool is_reflow_recommend = 19;  // 是否回流推荐
}

message UgcMatchMapBriefFromRemote_Panel {
  repeated UgcMatchMapBriefFromRemote_TimeRange time = 1;  // 时间范围
  optional int32 specified_room_info_id = 6;  // 指定模式id
  optional UgcMatchMapBriefFromRemote_BirthInfo birth_info = 7;  // 出生信息
}

message UgcMatchMapBriefFromRemote_MultiPanel {
  repeated UgcMatchMapBriefFromRemote_TimeRange time = 1;  // 时间范围
  repeated UgcMatchMapBriefFromRemote_Weight ugc_infos = 2;  // 所有地图
  optional int32 point_number = 3;  // 出生点
  repeated int32 camp_info = 4;  // 阵营信息
  optional string name = 5;  // 合集名
  optional UgcMatchMapBriefFromRemote_VersionRange version = 6; // 生效版本
  optional int32 specified_room_info_id = 7;  // 指定模式id
  optional int32 recommend = 8;  // 是否推荐
}

message UgcMatchMapBriefFromRemote_MultiRound {
  repeated UgcMatchMapBriefFromRemote_TimeRange time = 1;  // 时间范围
  repeated UgcMatchMapBriefFromRemote_Weight ugc_infos = 2;  // 所有地图
  optional int32 point_number = 3;  // 出生点
  repeated int32 camp_info = 4;  // 阵营信息
  optional string name = 5;  // 合集名
  optional UgcMatchMapBriefFromRemote_VersionRange version = 6; // 生效版本
  optional int32 specified_room_info_id = 7;  // 指定模式id
  optional int32 round_times = 8;  // 需要进行的轮次
  optional int32 min_player = 9;  // 最小的玩家数量
}

message UgcMatchMapBriefFromRemoteServerSide {
  // 单图情况下需要多处匹配，及match,plaza,lobby,panel,这里记录下配置统一的出生点信息，
  optional UgcMatchMapBriefFromRemote_Server_BirthInfo server_birth_info = 9;  // 端内服务填充
}

message UgcMatchMapBriefFromRemote_Activity {
  optional string start_version = 1;
  optional int32 jump_id = 2;
  optional string jump_param = 3;
  optional string activity_desc = 4;  // 活动描述
  optional int64 activity_item_id = 5;  // 道具id
  optional string end_version = 6;
}

// 玩法索引
enum UgcMatchMapBriefFromRemote_Reference_Type {
  UMMBFRRT_Unknown = 0;
  UMMBFRRT_MultiRound = 1;  // 多轮次玩法
}

message UgcMatchMapBriefFromRemote_Reference_Base {
  optional int32 reference_type = 1;  // 参考 UgcMatchMapBriefFromRemote_Reference_Type
  optional int32 jump_id = 2;  // 说明前往跳转id
  optional int64 config_id = 3;  //  配置id（地图库id)
  optional string activity_desc = 4;  // 活动描述
  optional int64 activity_item_id = 5;  // 道具id
  optional string jump_param = 6;  // 跳转参数
  optional string start_version = 7;
  optional string end_version = 8;
}

// 玩法选择索引
message UgcMatchMapBriefFromRemote_MultiMap_Reference {
  optional string name = 1;  // 合集名
  optional string desc = 2;  // 地图集合说明
  optional int32 priority = 3;  // 权重
  optional UgcMatchMapBriefFromRemote_VersionRange version = 4; // 生效版本
  repeated UgcMatchMapBriefFromRemote_TimeRange time = 5;  // 时间范围
  optional UgcMatchMapBriefFromRemote_Label label = 6;  // 标签
  optional int64 cover_ugc_id = 7;  // 封面地图
  optional UgcMatchMapBriefFromRemote_Reference_Base reference_base = 8;  // 索引信息
  optional UgcMatchMapBriefFromRemote_ModeTypeInfo match_type_info = 9;  // 星世界推荐通用配置
  repeated int32 cloud_game_type = 10;  // 允许的游戏平台，空表示允许所有，// 0:app, 1:先锋云游, 2:微信小游戏, 3:qq小游戏
}

message UgcMatchMapBriefFromRemote {
  optional int32 room_info_id = 1;  // 服务填充
  optional int64 ugc_id = 2;
  optional int32 single_play_type = 3;
  optional UgcMatchMapBriefFromRemote_Match match = 4;  // 一起来玩
  optional UgcMatchMapBriefFromRemote_MultiMatch multimatch = 5;  // 合集地图
  optional UgcMatchMapBriefFromRemote_Plaza plaza = 6;  // 广场匹配
  optional UgcMatchMapBriefFromRemote_Lobby lobby = 7;  // 星世界推荐
  optional UgcMatchMapBriefFromRemote_LobbyV2 lobby_v2 = 8;  // 星世界推荐-v2
  optional UgcMatchMapBriefFromRemote_MultiMatch multilobby = 9;  // 星世界推荐-合集地图
  optional UgcMatchMapBriefFromRemote_Panel panel = 10;  // 触发盒-单图匹配
  optional UgcMatchMapBriefFromRemote_MultiPanel multipanel = 11;  // 触发盒-合集地图
  optional UgcMatchMapBriefFromRemoteServerSide server_side = 12;  // 服务配置，服务填充
  optional UgcMatchMapBriefFromRemote_MultiRound multiround = 13;  // 多轮次-图集匹配
  optional UgcMatchMapBriefFromRemote_MultiMap_Reference multi_reference = 14;  // 合集匹配索引
}

message UgcMatchMapBriefFromRemoteConfig {
  repeated UgcMatchMapBriefFromRemote cfg = 1;
  optional int32 lobbyShowCnt = 2;
  optional int32 lobbyShowType = 3;
}

message UgcMatchMapBriefFromRemoteResultInfo {
  optional int64 ugc_id = 1;
  optional int32 room_info_id = 2;
  optional UgcMatchMapBriefFromRemote_Server_BirthInfo birth_info = 3;  // 指定的出生点信息
}

message UgcMatchMapBriefFromRemoteResultInfo_Button {
  optional int64 ugc_id = 1;
  optional int32 room_info_id = 2;
  optional int32 single_play_type = 3;
  optional UgcMatchMapBriefFromRemote_Server_BirthInfo birth_info = 4;  // 指定的出生点信息
}

message UgcMatchMapBriefFromRemoteResult {
  optional int64 ugc_id = 1;
  optional int32 single_play_type = 2;
  optional UgcMatchMapBriefFromRemoteResultInfo match = 3;  // 一起来玩
  optional UgcMatchMapBriefFromRemoteResultInfo multimatch = 4;  // 合集地图
  optional UgcMatchMapBriefFromRemoteResultInfo plaza = 5;  // 广场匹配
  optional UgcMatchMapBriefFromRemoteResultInfo lobby = 6;  // 星世界推荐
  repeated UgcMatchMapBriefFromRemoteResultInfo_Button lobby_v2 = 7;  // 星世界推荐-v2
  optional UgcMatchMapBriefFromRemoteResultInfo multilobby = 8;  // 星世界推荐-合集地图
  optional UgcMatchMapBriefFromRemoteResultInfo panel = 9;  // 触发盒-单图匹配
  optional UgcMatchMapBriefFromRemoteResultInfo multipanel = 10;  // 触发盒-多图匹配
  optional UgcMatchMapBriefFromRemoteResultInfo multiround = 11;  // 多轮次-图集匹配
  optional UgcMatchMapBriefFromRemoteResultInfo multireference = 12;  // 合集匹配索引
}

message UgcMatchMapBriefFromRemoteConfigResult {
  repeated UgcMatchMapBriefFromRemoteResult result = 1;
}

message UgcMatchLobbyMapBriefInfoTimeRange {
  optional int64 openTime = 2;
  optional int64 closeTime = 3;
}

message UgcMatchLobbyMapWithWeight {
  optional int64 ugcId = 1;
  optional int32 weight = 2;
  // 以下非配置字段
  optional int64 clientVersion = 3;
  optional int64 ugcVersion = 4;
  // 配置字段
  optional UgcMatchMapBriefFromRemote_Wright_Round roundInfo = 5;  // 轮次信息
}

message UgcMatchLobbyMapBriefInfoMatch {
  repeated UgcMatchLobbyMapBriefInfoTimeRange timeRange = 1;
  optional int32 priority = 2;
  optional string desc = 3;
  optional int32 recommend = 4;
  repeated UgcMatchLobbyMapWithWeight ugcInfos = 5;  // 所有组合地图id
  optional string name = 6;  // 名字
  optional UgcMatchLobbyMapLabel label = 7;
  optional int32 pointNumber = 8;  // 出生点
  repeated int32 campInfo = 9;  // 阵营信息
  optional int64 coverUgcId = 10;  // 封面地图id
}

message UgcMatchLobbyMapBriefInfoPlaza {
  repeated UgcMatchLobbyMapBriefInfoTimeRange timeRange = 1;
}

message UgcMatchLobbyMapBriefInfo {
  optional int64 ugcId = 1;
  //  optional int64 openTime = 2;  // 废弃
  //  optional int64 closeTime = 3;  // 废弃
  optional int32 roomInfoId = 4;  // 游戏模式id
  //  optional bool isTop = 5;  // 废弃
  optional UgcMatchLobbyMapBriefInfoMatch briefInfoMatch = 6;
  optional UgcMatchLobbyMapBriefInfoPlaza briefInfoPlaza = 7;
  optional UgcMatchLobbyMapBriefInfoMatch briefInfoLobby = 8;
  optional UgcMatchLobbyMapBriefInfoMatch briefInfoMultiMatch = 9;
  optional int32 singlePlayType = 10;  // 参考UgcMatchSinglePlayType
}

/* UGC匹配页面配置数据内容 */

// 基础配置
message UgcMatchConfig_Base {
  repeated UgcMatchMapBriefFromRemote_TimeRange time = 1;  // 时间范围
  optional UgcMatchMapBriefFromRemote_VersionRange version = 2; // 生效版本
  optional int32 priority = 3;  // 优先级
  optional string desc = 4;  // 描述
  optional bool recommend = 5;  // 是否推荐
  repeated int32 cloud_game_type = 6;    // 允许的游戏平台，空表示允许所有，// 0:app, 1:先锋云游, 2:微信小游戏, 3:qq小游戏
  optional int32 mode_type_id = 7;  // 槽位id
  optional int32 sort = 8;  // 槽位排序值
  optional bool is_show_mini_game = 9;  // 小游戏展示
}

message UgcMatchConfig_Extra_MiniGameArea {
  optional int32 pool_type = 1;  // 0:置顶池子(1号), 1:普通池子(2号)
  optional bool top = 2;  // 是否指定
  optional UgcMatchMapBriefFromRemote_TimeRange top_time_range = 3;  // 置顶时间段
  optional int32 top_priority = 4;  // 置顶优先级
}

// 多按钮配置
message UgcMatchConfig_MultiButton {
  optional UgcMatchConfig_Base base_info = 1;  // 基础信息
  optional int64 cover_ugc_id = 2; // 封面地图id
  optional bool is_show_detail = 3;  // 是否展示详情页入口
  optional bool is_show_instruction = 4;  // 是否展示地图说明
  repeated UgcMatchMapBriefFromRemote_LobbyV2_Button buttons = 5;  // 按钮配置
  optional UgcMatchMapBriefFromRemote_LobbyV2_Page extra = 6;  // 额外展示信息
  optional UgcMatchMapBriefFromRemote_Label label = 7;  // 标签信息
  optional UgcMatchConfig_Extra_MiniGameArea extra_mini_game = 8;  // 小游戏专区附加配置
  optional bool is_show_cover_video = 9;  // 显示封面视频
}

// 合集匹配
message UgcMatchConfig_MultiUgcMap {
  optional UgcMatchConfig_Base base_info = 1;  // 基础信息
  optional int64 cover_ugc_id = 2;  // 封面地图
  optional string name = 3;  // 合集名
  optional int32 point_number = 4;  // 出生点
  repeated int32 camp_info = 5;  // 阵营信息
  optional UgcMatchMapBriefFromRemote_Label label = 6;  // 标签
  optional int32 specified_room_info_id = 7;  // 指定模式id
  repeated UgcMatchMapBriefFromRemote_Weight ugc_infos = 8;  // 所有地图
  optional UgcMatchConfig_Extra_MiniGameArea extra_mini_game = 9;  // 小游戏专区附加配置
  optional bool is_show_cover_video = 10;  // 显示封面视频
}

message UgcMatchConfig_Server_BirthInfo {
  optional int32 point_number = 1;  // 出生点
  repeated BattleCamp camp_info_list = 2;  // 阵营信息
  optional int64 camp_dim_id = 3;  // 阵营维度id
}

// 配置页面类型定义
enum UgcMatchConfigPageType {
  UMCPT_UNKNOWN = 0;
  UMCPT_MINI_GAME_AREA = 1;  // 小游戏专区
  UMCPT_STAR_WORLD_RECOMMEND_CONFIG = 2;  // 星世界推荐配置
}

enum UgcMatchConfigType {
  UMCT_UNKNOWN = 0;
  UMCT_MULTI_BUTTON = 1;  // 多按钮
  UMCT_MULTI_UGC_MAP = 2;  // 多图匹配
}

// 配置页面内容
message UgcMatchConfigPageData {
  repeated int32 config_page_types = 1;  // 配置类型的列表 UgcMatchConfigPageType
  optional UgcMatchConfigPageData_MiniGameArea mini_game_area = 2;  // 小游戏专区配置
  optional UgcMatchConfigPageData_StarWorldRecommendConfig star_world_recommend_config = 3;  // 星世界推荐配置
}

// 每行配置类型
message UgcMatchConfigPageData_Detail {
  optional int32 config_type = 1;  // 配置类型 UgcMatchConfigType
  optional int64 config_id = 2;  // 配置id
  optional UgcMatchConfig_MultiButton multi_button = 3;  // 配置类型-多按钮配置
  optional UgcMatchConfig_MultiUgcMap multi_ugc_map = 4;  // 配置类型-多图匹配
}

// 小游戏专区页面配置内容
message UgcMatchConfigPageData_MiniGameArea {
  optional int32 show_count = 1;  // 显示的个数
  repeated UgcMatchConfigPageData_Detail config_list = 2;  // 所有配置行
}

// 星世界推荐配置内容
message UgcMatchConfigPageData_StarWorldRecommendConfig {
  optional bool show_ugc_bp = 1;  // 显示ugcbp
  optional UgcMatchMapBriefFromRemote_VersionRange version = 2; // 生效版本
}

// 基础结果
message UgcMatchConfigResult_OneItem {
  optional int64 ugc_id = 1;
  optional int32 room_info_id = 2;
  optional int32 single_play_type = 3;
  optional UgcMatchConfig_Server_BirthInfo birth_info = 4;  // 指定的出生点信息
}

message UgcMatchConfigResult_MultiItem {
  optional int64 ugc_id = 1;
  repeated UgcMatchConfigResult_OneItem items = 2;  // 多个结果
}

// 配置页面结果
message UgcMatchConfigPageResult {
  repeated int32 config_page_types = 1;  // 配置类型的列表 UgcMatchConfigPageType
  optional UgcMatchConfigPageResult_MiniGameArea mini_game_area = 2;  // 小游戏专区
  optional UgcMatchConfigPageResult_StarWorldRecommendConfig star_world_recommend_config = 3;  // 星世界推荐配置
}

message UgcMatchConfigPageResult_Detail {
  optional int32 config_type = 1;  // 配置类型 UgcMatchConfigType
  optional int64 config_id = 2;  // 配置id
  optional UgcMatchConfigResult_MultiItem multi_button = 3;  // 配置类型-多按钮配置
  optional UgcMatchConfigResult_OneItem multi_ugc_map = 4;  // 配置类型-多图匹配
}

// 小游戏专区配置页面结果
message UgcMatchConfigPageResult_MiniGameArea {
  repeated UgcMatchConfigPageResult_Detail result_list = 1;  // 结果列表
}

// 星世界推荐配置结果
message UgcMatchConfigPageResult_StarWorldRecommendConfig {
}

/* UGC匹配页面配置数据内容 */

/* UGC匹配页面配置数据内容服务存储 */

message UgcMatchConfigPageServer_MiniGameArea {
  optional UgcMatchConfigPageData_MiniGameArea config = 1;  // 所有配置
  optional UgcMatchConfigPageResult_MiniGameArea result = 2;  // 匹配用数据
}

// 星世界推荐配置内容
message UgcMatchConfigPageServer_StarWorldRecommendConfig {
  optional UgcMatchConfigPageData_StarWorldRecommendConfig config = 1;
  optional UgcMatchConfigPageResult_StarWorldRecommendConfig result = 2;
}

message UgcMatchConfigPageServer {
  optional int32 configPageType = 1;  // 配置类型 UgcMatchConfigPageType
  optional int64 configVersion = 2;  // 存储的配置版本
  optional UgcMatchConfigPageServer_MiniGameArea miniGameArea = 3;  // 小游戏
  optional UgcMatchConfigPageServer_StarWorldRecommendConfig starWorldRecommendConfig = 4;  // 星世界推荐
}

/* UGC匹配页面配置数据内容服务存储 */

message ObjectPosition {
  // 坐标
  optional int32 xPos = 1;
  optional int32 yPos = 2;
  optional int32 zPos = 3;
  // 朝向
  optional int32 xVec = 4;
  optional int32 yVec = 5;
  optional int32 zVec = 6;
  // 速度
  optional int32 xSpeed = 7;
  optional int32 ySpeed = 8;
  optional int32 zSpeed = 9;
}

message UgcMetaOperateRecord {
  optional string editorName = 1;   //编辑人
  optional string saveName = 2;     //存档名称
  optional int64 updateTime = 3;    //时间
  optional SaveType recordType = 4; //记录类型
  optional int64 curId = 5;         //当前id
  repeated UgcMapMetaInfo info = 6;
}

message CreateStruct{
  optional int64 useId = 1; //如果覆盖 被覆盖的id  否 0
  optional string createName = 2; //手动保存名称 （建档）
  optional int64 createTime = 3; //手动保存时间 （建档）
}

// AI剧本Ab实验分组
enum AiScriptAbTestGrp {
  ASATG_Invalid = 0;      // 无效
  ASATG_B1Grp = 1;        // b1组 对照组 大流量 主要
  ASATG_B2Grp = 2;        // b2组 实验
}

enum UgcWarmDateOptType {
  UWDOT_UNKNOWN = 0;
  UWDOT_Like = 1;       // 点赞
  UWDOT_Play = 2;       // 游玩
  UWDOT_Pass = 3;       // 通关
  UWDOT_LabelScore = 4; // 评分
  UWDOT_Collect = 5;    // 收藏
  UWDOT_Share = 6;      // 分享
}

enum VersionType {
  VersionM7 = 0;  //M7
  VersionM8 = 1;  //>M8
}

message ClubEventEntry {
  enum ClubEventType {
    CET_Invalid = 0;
    CET_MemberCount = 1;
    CET_WeeklyHeat = 2;
  }
  optional ClubEventType eventType = 1;
  optional MemberCountParam memberCountParam = 2;
  optional WeeklyHeatParam weeklyHeatParam = 3;

  message MemberCountParam {
    optional int32 count = 1;
  }
  message WeeklyHeatParam {
    optional int32 heat = 1;
    optional int64 weekBeginTime = 2;
  }
}

enum AigcType {
  AT_UNKNOWN = 0;
  AT_GEN_IMAGE = 1;
  AT_CHANGE_COLOR = 2;
  AT_GEN_VOICE = 3;
  AT_GEN_ANICAP = 4;
  AT_GEN_ANSWER = 5;
  AT_GEN_MODULE = 6;
}

message AigcHistoryRecord{
  map<string, AigcHistoryData> data = 1;
  repeated string use = 2;
  repeated string all = 3;
}

message AigcHistoryData{
  optional string file = 1;
  optional int64 createTime = 2;
  optional int64 useTime = 3;
  optional int32 typeId = 4;
}

message LBSPlayerInfo {
  optional int64 uid = 1;
  optional int32 latitudeE6 = 2;      // 纬度, 单位: 微度, 北纬为正，南纬为负
  optional int32 longitudeE6 = 3;     // 经度, 单位: 微度, 东经为正，西经为负
  optional int32 distance = 4;        // 距离，单位为米
}

message RedisRemoteConfig {
  map<int64, int64> ugcNewYearStep = 1;
}

enum FarmingWaterRarity {
  FWR_Normal = 0; // 普通浇水
  FWR_Silver = 1; // 白银浇水
  FWR_Gold = 2; // 黄金浇水
}

message FarmingWaterRecord {
  optional int64 timestamp = 1;
  optional FarmingWaterRarity rarity = 2;
  optional int64 operator = 3;
}

message FarmingWaterResult {
  map<int32, int32> rewardItemMap = 1;
  optional FarmingWaterRarity rarity = 2;
  optional int32 ripenTime = 3;
  optional int32 cropType = 4;
}

message FarmingHarvestResult {
  map<int32, int32> rewardItemMap = 1;
  optional int32 cropType = 2;
}

enum IntellectualActivityOperation {
  Not_Ready_Players_Three_Minutes = 1; // 开始三分钟前未候场玩家
  Not_Ready_Players_One_Minutes = 2; // 开始五分钟前未候场玩家
  In_Ready_Players = 3; // 所有已候场玩家
  Player_Achieve_New_Item = 4; // 玩家新增道具卡
  Player_Realtime_Number = 5; // 玩家实时人数
  Activity_End = 6; // 活动结束
}

enum UgcMapActionType{
  UgcMapDelete = 1;            //删除
  UgcMapRestore = 2;           //恢复
  UgcMapDisableMultiTest = 3;  // 编辑地图禁止多人测试
  UgcMapEnableMultiTest = 4;   // 编辑地图允许多人测试
}

// 家园发布类型
enum XiaowoPublishType {
  XPT_Editor = 0; // 编辑器发布
  XPT_OfficialLayout = 1; // 官方方案发布
  XPT_MyLayout = 2; // 我的方案发布
  XPT_HistoryRecord = 3; // 历史记录还原
  XPT_CreatorLayout = 4; // 创作家方案发布
}

message sHandbookInfo {
  optional uint32 Stage = 1; // 所属阶段
  optional bool Unlock = 2; // 是否解锁
}
message sLuckyStarInfo {
  optional uint32 Stage = 1; // 所属阶段
  optional uint32 StarId = 2; // 福星id
  optional uint32 Num = 3;    // 数量
}

message BattleRecentLevel {
  optional int64 endTime = 1;
  optional int32 mode = 2;
  repeated int32 level = 3;
}

message AMSItemParam {
  optional int32 amsId = 1;
  optional int64 uid = 2;
  optional string openId = 3;
  optional string billNo = 4;
  optional int32 reasonId = 5;
  optional int32 subReasonId = 6;

  optional int32 accountType = 7;
  optional int32 platId = 8;
  optional int64 asyncId = 10;
  optional AMSPackageParam amsPackageParam = 11;
}

message AMSPackageParam {
  optional int32 amsActivityId = 1;
  optional int32 moduleId = 2;
  optional int32 packageGroupId = 3;
  optional int32 accountType = 4;
}

message AMSItemResult {
  optional int32 result = 1;
  optional string serial = 2;

  optional int32 packageGroupId = 3;
  repeated KeyValueInt32 packageList = 4;

  optional int32 amsIRet = 5;
}

// ds物品变化
message DSItemsChange {
  optional int64 uid = 1;  // 玩家uid
  optional com.tencent.wea.xlsRes.ItemChangeReason reason = 2;//变化原因
  repeated DSItem changedItems = 3; // 变化的物品
  optional int32 subReason = 4;         //变化子原因
  repeated int64 params = 5;         //额外参数列表
}

message DSItem {
  optional int32 itemId = 1;         // 物品id
  optional int64 itemChangeNum = 2;  // 物品变化数量
  optional int64 newItemNum = 3;     // 物品最新数量
}

enum XiaoWoLiuYanMessageType {
  XLYMT_Unknown = 0;
  XLYMT_LiuYanMessage = 1;
  XLYMT_LiuYanReply = 2;
}

enum XiaoWoLiuYanMessageChoiceType {
  XLYMCT_Unknown = 0;
  XLYMCT_Choice = 1;
  XLYMCT_UnChoice = 2;
}

message XiaoWoLiuYanMessage {
  optional int64 id = 1;
  optional string content = 2;
  optional int64 sendTime = 3;
  optional bool isChosen = 4;// 是否精选
  optional int64 chosenTime = 5;// 精选时间
  repeated XiaoWoLiuYanMessageReply replys = 6;// 回复
  optional int64 uid = 7;// 玩家id
  optional string nickname = 8;     //昵称
  optional string profile = 9;       //头像url (maybe)
  optional HeadFrame headFrame = 10; // 头像框
}

message XiaoWoLiuYanMessageReply {
  optional int64 id = 1;
  optional string content = 2;
  optional int64 sendTime = 3;
  optional int64 uid = 4;// 玩家id
  optional string nickname = 5;     //昵称
  optional string profile = 6;       //头像url (maybe)
  optional HeadFrame headFrame = 7; // 头像框
}

enum XiaoWoLiuYanMessageNtfType {
  XLYMNT_Unknown = 0;
  XLYMNT_Ban = 1;
  XLYMNT_Number = 2;
  XLYMNT_LastPullId = 3;
}

enum XiaoWoType {
  XT_Home = 0;//家园
  XT_SampleRoom = 1;// 样本间
}

enum XiaoWoSampleRoomType {
  XSRT_UnKnown = 0;
  XSRT_Official = 1;
  XSRT_Creator = 2;
}

message UgcCollectionFace {
  optional int64 faceMapId = 1;
  optional UgcMapMetaInfo metaInfo = 2;
  optional string bucket = 3;

  optional string creatorName = 4;                // 作者昵称
  optional string creatorAvatar = 5;              // 作者头像
  optional com.tencent.wea.xlsRes.UgcAuthType authType = 6;
  repeated EquipItemInfo dressItemInfos = 7;      // 穿戴信息(铭牌、称号、头像框等)
  optional int32 safeStatus = 8;                  // 枚举 SafeStatus，地图状态

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message UgcCollectionMap {
  optional int64 mapId = 1;
  repeated int32 tags = 2;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message UgcCollectionMaps {
  repeated UgcCollectionMap items = 1;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message UgcCollectionInfo {
  optional int64 creator = 1;
  optional string name = 2;
  optional string desc = 3;
  optional com.tencent.wea.xlsRes.UgcCollectionType type = 4;
  optional int64 createMs = 8;
  optional int64 updateMs = 9;
  repeated int32 tags = 10;
  optional int64 uid = 11;
  optional int64 lastRefreshMs = 12;    // 上次刷新时间(用于定期刷新合集剔除非法地图)

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message UgcCollectionAdminData {
  optional int32 bg = 1;              // 背景 com.tencent.wea.xlsRes.UgcCollectionBg
  optional string recommendDesc = 2;  // 推荐描述
  repeated int32 adminTags = 3;       // 废弃
  optional string adminDesc = 4;      // 废弃
  repeated CollectionAdminTag adminTagsNew = 5;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message UgcCollectionData {
  optional int64 playTimesUv = 1;
  optional int64 playTimesPv = 2;
  optional int64 collectTimes = 3;

  optional int64 reserved1 = 1001;
  optional int64 reserved2 = 1002;
  optional int64 reserved3 = 1003;
}

message UgcCollectionPersonalData {
  optional bool collected = 1;

  optional int64 reserved1 = 1001;
  optional int64 reserved2 = 1002;
  optional int64 reserved3 = 1003;
}

message UgcCollectionRecommendData {
  repeated int64 relatedFriendsUid = 1;           // 合集推荐关系链uid
  optional string recommendText = 2;              // 关系链推荐文本
}

//合集简要信息
message UgcCollectionBrief {
  optional UgcCollectionFace face = 1;            // 合集封面数据
  optional UgcCollectionInfo info = 2;            // 合集描述信息
  optional UgcCollectionData data = 3;            // 合集动态信息(游玩数等)
  repeated UgcCollectionMap maps = 4;             // 合集内地图列表
  optional UgcCollectionAdminData adminData = 5;  // 管理端附加信息
  optional UgcCollectionPersonalData personalData = 6;
  optional string collectionId = 7;
  optional int32 mapCount = 8;
  optional UgcCollectionRecommendData recommendData = 9; //推荐数据
}

enum UgcCollectionStatus {
  UCS_INVALID = 0;
  UCS_NORMAL = 1;
  UCS_OFF = 2;
}

enum UgcCollectionOperateType {
  UCOT_INVALID = 0;     // 非法
  UCOT_DELETE = 1;      // 删除
  UCOT_COLLECT = 2;     // 收藏
  UCOT_UNCOLLECT = 3;   // 取消收藏
  UCOT_CREATE = 4;      // 创建
  UCOT_MODIFY = 5;      // 修改

  UCOT_RESERVED1 = 1001;
  UCOT_RESERVED2 = 1002;
  UCOT_RESERVED3 = 1003;
}

enum UgcCollectionListType {
  UCLT_INVALID = 0;   // 非法
  UCLT_CREATED = 1;   // 我创建的
  UCLT_COLLECTED = 2; // 我收藏的
}

// 房间成员角色信息
enum MemberBaseInfoRoomRoleType {
  MBIRRT_Normal = 0;     // 默认时普通成员(兼容旧逻辑)
  MBIRRT_Observer = 1;   // 观战ob
}

message RankSeasonInfo {
  optional int32 id = 1;          // 赛季ID
  optional int64 startSec = 2;    // 本赛季开始时间
  optional int64 endSec = 3;      // 本赛季结束时间
  optional int64 rewardSec = 4;   // 奖励发放时间
  optional int32 seqId = 5;       // 顺序ID
}

message UgcResourceInfo{
  optional int64 resId = 1;  //资产id
  repeated UgcMapMetaInfo metaInfo = 2;  //metaInfo信息
  optional string bucket = 3;
  optional string region = 4;
  optional bool isPrivate = 5;
  optional int64 creatorId = 6;
}

message UgcStarWorldNavPageCfgAbTestInfo {
  optional string abtestModuleKey = 1;
  optional int32 abtestExpId = 2;
  optional bool abtestDefault = 3;
}

message UgcStarWorldNavPageCfgBaseInfo {
  optional int32 whiteListType = 1;  // 白名单类型
  optional int64 startVersion = 6;  // 起始版本
  optional int64 endVersion = 7;  // 结束版本
}

// 新世界导航栏配置
message UgcStarWorldNavPageCfg {
  optional string id = 1;  // 导航栏id
  optional string name = 2;  // 导航栏名
  optional string json = 3;  // 当前导航栏配置串
  repeated UgcStarWorldNavPageCfg childList = 4;  // 子页签
}

message UgcStarWorldNavPageCfgForServer {
  optional UgcStarWorldNavPageCfg pageCfg = 1;
  optional UgcStarWorldNavPageCfgAbTestInfo abTestInfo = 2;
  repeated UgcStarWorldNavPageCfgForServer childList = 3;  // 子页签
  optional UgcStarWorldNavPageCfgBaseInfo baseInfo = 4; // 基础配置
}

// 编辑合集内容
message UgcCollectionModifyInfo {
  optional string name = 1;
  optional com.tencent.wea.xlsRes.UgcCollectionType type = 2;
  optional string desc = 3;
  repeated int64 mapsAfterEdit = 6;
  repeated int32 adminTags = 7;
  optional string adminDesc = 8;
  repeated int64 mapsDeleted = 9;
  repeated CollectionAdminTag adminTagsNew = 10;

  optional int64 playTimesUv = 1001;
  optional int64 playTimesPv = 1002;
  optional int64 collectTimes = 1003;
}

message UgcCoPlayRecommendUserInfo {
  optional int64 uid = 1;    //玩家Uid
  optional int32 device_level = 2; // 机型评级
  optional string client_version = 3; // 客户端版本号
  optional string open_id = 4; // openid
}
message UgcCoPlayUsedMap {
  optional int64 map_id = 1; //地图ID
}

message UgcCoPlayRecommendMap {
  optional int64 map_id = 1; //地图ID
}

message GetUgcCoPlayRecommendMapList {
  optional string session_id = 1;
  repeated UgcCoPlayRecommendMap rcmd_map_list = 2;
  optional MgrAlgoInfo algo_info = 3;
}


message UgcCoPlayUserInfo {
  optional int64  uid = 1;   // 玩家Uid
  optional string name = 2;   // 玩家名
  optional string profile = 3;   // 玩家头像
  optional int32 gender = 4;   // 玩家性别
  repeated EquipItemInfo dressItemInfos = 5;   //穿戴信息(铭牌、称号、头像框等)
}

message UgcCoPlayRecord {
  optional UgcCoPlayUserInfo userInfo = 1; //同游用户信息
  optional int64 playTime = 2;             //同游时间，毫秒unixtime
  optional int32 playTimes = 3;            //同游次数
}

message UgcCoPlayInfo {
  repeated UgcCoPlayRecord coPlayRecords = 1; //同游记录列表
}


message CollectionAdminTag {
  optional int32 id = 1;
  optional string desc = 2;
}

message CollectionReportData {
  optional string collection_id = 1;
  optional int64 creator_id = 2;
  optional string name = 3;
  optional string desc = 4;
  optional int32 type = 5;
  optional int64 create_time = 6;
  optional int64 update_time = 7;
  repeated int64 maps = 8;
  optional int64 face_map_id = 9;
  optional int64 status = 10;
  optional int64 uid = 11;
  repeated int32 tag_ids = 12;
  repeated int32 admin_tag_ids = 13;    // 废弃
  repeated CollectionAdminTag admin_tags = 14;

  optional int64 play_times_uv = 101;
  optional int64 play_times_pv = 102;
  optional int64 collection_times = 103;
}

message CollectionReport {
  optional uint64 event_time = 1;
  optional string event_type = 2; // ss_ugcplatsvr.proto::EventType
  optional string env = 3;
  optional string collection_id = 21;
  optional CollectionReportData ugc_collection_data = 109;
}

// UgcDataStore 存储类型
enum UgcDataStoreFuncType {
  UDSFT_Unknown = 0;
  UDSFT_Task = 1;         // 任务
  UDSFT_Bag = 2;         // 背包
  UDSFT_Attr = 3;        // 属性
  UDSFT_PaidItem = 4;    // 付费道具
  UDSFT_Customize = 5;   // 自定义KV
  UDSFT_Shop = 6;
}

enum GetTargetUgcPlayerFuncType {
  GTUPFT_Unknown = 0;
  GTUPFT_Diamonds = 1;
  GTUPFT_AttrData = 2;
}
// UgcDataStore 单玩家全量数据
message UgcPlayerDataStoreData {
  optional UgcDataStoreTaskData task = 1;
  optional UgcDataStoreBagData bag = 2;
  optional UgcDataStoreAttrData attr = 3;
  optional UgcDataStorePaidItemData paidItem = 4;
  optional UgcDataStoreCustomKvData customKv = 5;
  optional UgcDataStoreShopData shop = 6;
  optional int64 dataVersion = 7;
}

// UgcDataStore 任务数据
message UgcDataStoreTaskData {
  message UgcDataStoreTaskDataDetail {
    optional int64 taskId = 1;         // 任务Id
    optional uint32 taskStatus = 2;    // 任务状态  0=接取未完成 1=完成未领奖 2=完成已领奖
    optional int32 taskProcess = 3;   // 任务进度
    optional bytes extraData = 4;     // 预埋数据
  }
  repeated UgcDataStoreTaskDataDetail data = 1;
  optional bytes extraData = 2;     // 预埋数据
}

// UgcDataStore 背包数据
message UgcDataStoreBagData {
  message UgcDataStoreBagItemDetail {
    optional string itemId = 1;
    optional int32 num = 2;
    optional int32 slotId = 3;
    optional int32 slotIndex = 4;
    optional int32 instanceId = 5;
    optional int32 payNum = 6;
    optional int32 arg1 = 7;
    optional int32 arg2 = 8;
    optional string arg3 = 9;
    optional bytes extraData = 10;     // 预埋数据
  }
  message UgcDataStoreBagDataDetail {
    repeated UgcDataStoreBagItemDetail items = 1;
    optional bytes extraData = 2;     // 预埋数据
  }
  optional UgcDataStoreBagDataDetail data = 1;
  optional bytes extraData = 2;     // 预埋数据
}

// UgcDataStore 属性数据
message UgcDataStoreAttrData {
  message UgcDataStoreAttrDataDetail {
    repeated float pos = 1;
    repeated float rot = 2;
    repeated int32 archiveAreaIds = 3;
    optional bytes extraData = 4;   // 预埋数据
  }
  optional UgcDataStoreAttrDataDetail data = 1;
  optional bytes extraData = 2;     // 预埋数据
}

message UgcDataStoreShopData {
  optional string data = 1;
}

// UgcDataStore 付费道具
message UgcDataStorePaidItemData {
  message UgcDataStorePaidItemDetail {
    optional string itemId = 1;
    optional int32 num = 2;
    optional bytes extraData = 3;   // 预埋数据
  }
  repeated UgcDataStorePaidItemDetail data = 1;
  optional bytes extraData = 2;     // 预埋数据
}

// UgcDataStore 自定义kv数据
message UgcDataStoreCustomKvData {
  map<string, string> data = 1;
}

message UgcDataStorePayData {
  optional int64 totalCostDiamonds = 1;
}
// UgcDataStore 订单号
message UgcDataStoreBillNoData {
  message UgcDataStoreBillNoDataDetail {
    optional string billNo = 1;
  }
  repeated UgcDataStoreBillNoDataDetail data = 1;
}

message UgcPlayerPublicAttrsData {
  map<string, UgcPlayerPublicAttr> attrs = 1; //玩家的互动数据存档
}
message UgcPlayerPublicArrayAttrsData {
  map<string, UgcPlayerPublicArrayAttr> arrayAttrs = 1; //玩家的互动列表存档
}

message UgcPlayerPublicDsData {//玩家DS信息存档
  optional int64 gameSessionId = 1; //当前所在DS的GameSession
  optional int64 lastHeartbeatTime = 2; //DS心跳维持
  optional int64 DsaInstId = 3; //dsa实例id
}


enum UgcResStatus{
  Ugc_Res_Delete = 1;
  Ugc_Res_TakeOff = 2;
}

// 大厅组数据
message LobbyGroupData {
  optional int32 versionGroupId = 1;      // 广场ds兼容组
  optional int32 mapId = 2;               // 广场地图id
  optional string ugcClientVersion = 3;   // ugc客户端版本号
  optional int32 platId = 4;              // 平台id
}

enum DanMuType {
  DMT_UNKNOWN = 0;
  DMT_UGC = 1;
}

message DanMuBlockInfo_UGC {
  optional float x = 1;
  optional float y = 2;
  optional float z = 3;
}

message DanMuBlockInfo {
  oneof info {
    DanMuBlockInfo_UGC ugc = 1;
  }
}

message DanMuRecordInfo {
  optional int64 danMuId = 1;  // ugc使用地图id
  optional int64 blockId = 2;  // 弹幕区块id
  optional int64 recordId = 3;  // 弹幕的唯一id
}

message DanMuAttrInfo {
  optional int64 timestamp = 1;  // 时间戳 服务填充
  optional int64 uid = 2;  // 玩家uid 服务填充
  optional string content = 3;  // 弹幕内容 玩家填充
  optional int64 like = 4;  // 点赞数 服务填充
  optional DanMuBlockInfo blockInfo = 5;  // 存储区块信息 服务填充
}

message DanMuInfo {
  optional DanMuRecordInfo danMuRecordInfo = 1;
  optional DanMuAttrInfo danMuAttrInfo = 2;
}

message DanMuSort_UGC {
  optional int64 timestamp = 1;  // 初始化填充
  optional int32 playerLevel = 2;  // 初始化填充
  optional int32 contentLen = 3;  // 初始化填充
  optional int32 like = 4;  // 更新
}

message DanMuSort {
  oneof SortType {
    DanMuSort_UGC ugc = 1;
  }
}

enum DanMuOptType {
  DMOT_UNKNOWN = 0;
  DMOT_LIKE = 1;
}

message DanMuDBAttrInfo {
  optional string content = 1;  // 弹幕内容
  optional DanMuBlockInfo blockInfo = 2;  // 存储区块信息
}

message UgcMapSize {
  optional float length = 1;
  optional float width = 2;
  optional float height = 3;
}

enum UgcMapDanMuInfoUpdateOptType {
  UMDMIUOT_UNKNOWN = 0;
  UMDMIUOT_DANMU_INCREASE = 1;
  UMDMIUOT_DANMU_DECREASE = 2;
}

message UgcFastKey{
  optional int32 key = 1;            //快捷键
  optional string slotContent = 2;  // 快捷键对应内容
}

// 智能npc对话
message StreamNpcInfo {
  optional string render_id = 1;  // 角色npc id，必填
  optional int32 tts_voice_type = 2;  // 角色tts，必填
  optional string tts_st = 3;  // tts情绪
  optional int32 tts_sc = 4;  // tts情感程度, [50,200]
  optional int32 tts_speed = 5;  // tts语速
  optional bool direct_anim = 6;  // 是否直接通过文本对应的时间序列timestamps生成动画曲线
  optional int32 sampling = 7;  // 采样率,默认16000
  optional string anim_fade_mode = 8;   // 表情淡入淡出参数
  optional int32 output_coef_format = 9;   // 输出动画曲线驱动数据形式
  optional int32 tts_volume         = 10;  // tts音量大小，取值范围为[-10, 10],
                                          // -10表示音量相对默认值小10dB；不填写的话，默认为0，建议使用默认值；
  optional StreamNpcFaceTpInfo face_tp_info     = 20;  // 表情模板信息
  optional StreamNpcActionTpInfo action_tp_info = 21;  // 动作模板信息
}

// S2F 表情模板信息
message StreamNpcFaceTpInfo {
  optional string tp_id = 1;  // 模板ID
}

// S2A 动作模板信息
message StreamNpcActionTpInfo {
  optional string tp_id = 1;  // 模板ID
}

message LLMFilteredMsg {
  optional string type = 1;
}

message StreamNpcSingDance {
  optional string after_action_content = 1;
  optional string sing_resource_id = 2;
  optional string dance_resource_id = 3;
}

message StreamNpcPuzzle {
  optional string puzzle_id = 1;
  optional string puzzle_status = 2;
}

message StreamNpcInstruct {
  optional string instruct_id         = 1;  // TOD节点ID
  optional string after_execute_text  = 2;  // 执行指令后播报文本
  map<string, string> params = 3;  // 动态key-value参数
}

// TOD 节点信息，后续替换InstructMsg
message StreamNpcTodNodeInfoMsg {
  optional string type                                = 1; // 节点类型 tod_loop_task: 循环节点 / tod_instruct: TOD指令
  optional string description                         = 2; //目前只有循环任务节点有,给palyground展示用
  optional string name                                = 3; // 节点名称
  map<string, string> variables                       = 4; //循环节点用到的变量值
  repeated string tab_contents                        = 5; //实体选项卡，用户返回给用户做提示用
  optional string instruct_id                         = 6; // TOD指令 type=tod_instruct 指令ID
  optional string after_execute_text                  = 7; // 执行后播报文案
  map<string, string> params                          = 8; // TOD指令 type=tod_instruct 时使用的参数
  optional string task_status                         = 9; // success-成功，fail-失败，running-进行中
}

message AiNpcResponseEventAction {
  optional string hit_intent = 1;
}

message LLMBroadcastMsg {
  optional string llm_request_id = 1;   // 一次请求唯一标识
  optional string llm_ans = 2;   // llm回答
  optional string llm_summary = 3;   // llm回答摘要，忽略
  optional int32 round = 4;   // 请求回合
  optional int32 seq = 5;   // 该回合应答分句序号（拆句使用）
  optional bool end = 6;   // 请求回合是否结束
  optional bool filter = 7;   // 命中风控过滤需覆盖该轮次消息
  optional bool interrupt = 8;   // 是否命中打断，可打断上一轮，忽略
  optional bool llm_end = 9;   // 与end一样的含义
  optional int32 first_llm_seq_cost = 11;  // 首次llm分句结束耗时
  optional int32 llm_interrupt_cost = 12;  // llm打断请求耗时
  optional string debug_json = 13;  // llm debug信息
  optional string action_type = 14;  // 动作类型，
                                     // 闲聊=chat/唱歌=sing/跳舞=dance/聊天=chat/海龟汤=puzzle/TOD文本消息=tod_text/TOD指令=tod_instruct/TOD实体问询=tod_entity(逻辑同tod_text)/TOD节点=tod_nonode（tod没有配置输出节点,content为空）
                                     // 玩家邀请=tod_loop_task
  optional string sing_resource_id = 15;
  optional string dance_resource_id = 16;
  optional string after_action_content = 17;  // 唱跳后的文本，没有为空
  optional string feed_back_id = 18;
  optional LLMFilteredMsg filtered = 19;      // 安全过滤信息
  optional StreamNpcSingDance sing_and_dance = 20;   // 新唱跳结构 action_type=sing/dance
  optional StreamNpcPuzzle puzzle = 21;    // 海龟汤结构 action_type=puzzle
  optional StreamNpcInstruct instruct = 22;   // 指令消息，（后续废弃）TOD指令=tod_instruct
  optional StreamNpcTodNodeInfoMsg tod_node_info   = 28;  // TOD指令消息，TOD指令 action_type=tod_xxx
  repeated AiNpcResponseEventAction event_action = 29;    // 触发对应配置的事件
}

message BSBroadcastMsg {
  optional string bs_request_id = 1;   // 一次请求唯一标识
  optional string bs_json = 2;   // 数据json格式
  optional int32 round = 4;   // 请求回合
  optional int32 seq = 5;   // 该回合应答序号（拆句使用），该seq是否结束看bs_json数据
  optional int32 interrupt = 6;   // 可否打断上一轮，1不打断，2分句说完打断
  optional bool seq_final = 7;   // 标识该回合最后应答序号
  optional bool chunk_final = 8;   // 标识该seq最后一个bs回包
  optional int32 first_bs_cost = 11;  // 首包bs耗时
}

message ToneChatFiltered {
  optional string type = 1;  //business：业务安全过滤标识，ailab：lab自建过滤模型
  optional string category = 2;
}

message ToneChatExtraInfo {
  optional StreamNpcPuzzle puzzle = 1;
  optional StreamNpcSingDance sing_and_dance = 2;
}

message ToneChatResponseContent {
  optional string content = 1;
  optional string action_type = 2; //chat 默认大模型闲聊,sing ,dance, puzzle (海龟汤) ,tod_text,tod_instruct,tod_entity(实体问询，逻辑同tod_text)，tod_nonode（tod没有配置输出节点,content为空），tod_npc_profile(逻辑同tod_nonode),  tod_await_input(同tod_nonode),tod_loop_task(同tod_text)
  optional ToneChatFiltered filtered = 3;  //如果没命中安全过滤，则此dict为空
  optional StreamNpcSingDance t1_sing_and_dance = 4;
  optional string hit_intent = 5;
  optional StreamNpcTodNodeInfoMsg tod_node_info = 6;
  optional StreamNpcInstruct instruct = 7;
  optional ToneChatExtraInfo extra_info = 8;
  repeated AiNpcResponseEventAction event_action = 9;
  optional string topic_id = 10;
  optional string topic = 11;
}

message ArraySquadMember {
  repeated proto_SquadMember list = 1;
}

// 回填请求类型
enum MatchFillBackType {
  MFT_INVALID = 0;
  MFT_OMD_Mid_Join = 1; //omd中途加入到
  MFT_UgcMatch_Mid_Join = 2; //Ugc中途加入
  MFT_TYC_Mid_Join = 3; // tyc中途加入
  MFT_OMD_Mid_Join_Invite_Friend = 4; //omd邀请好友
  MFT_OMD_Mid_Join_Recruit = 5; //omd招募
  MFT_OMD_Mid_Join_Pwd = 6; //omd加入带密码校验

}

message MatchFillExtraInfo_Ugc {
  optional int32 side = 1;
}

message MatchFillExtraInfo {
  oneof FillExtraInfo {
    MatchFillExtraInfo_Ugc ugc = 1;
  }
}

message MatchFillBackSideInfo {
  optional int32 side = 1;
  optional int32 cnt = 2;
}

message MatchFillBackData {
  required int64 uuID = 1;    //唯一id例如roomID battleID
  required int64 uid = 2;     //触发的玩家uid
  optional int64 reqTime = 3;
  optional int32 fillBackCnt = 4;   //需要回填的人数(已废弃)改成使用sideInfo字段

  optional int32 matchType = 5;           //玩法id
  map<int32, string> ruleDimInfos = 6;    //维度信息
  required int32 ruleId = 7;            //规则id
  required int32 roomInfoId = 8;      //房间信息id
  required ServerType srcServerType = 9 [deprecated = true];   //来源服务类型
  optional MatchFillBackType fillBackType = 10; //回填类型
  optional MatchFillExtraInfo fillExtraInfo = 11; //额外信息
  repeated MatchFillBackSideInfo sideInfo = 12; //多阵营信息
  optional int32 srcServerTypeInt = 13;   //来源服务类型int
}

message MatchCancelFillBackData {
  required int64 uuID = 1;    //唯一id例如roomID battleID

  //服务器为了支持热更新不使用枚举改用int类型
  optional int32 srcServerTypeInt = 2;   //来源服务类型 对应枚举ServerType
  optional int32 fillBackTypeInt = 3; //回填类型 对应枚举MatchFillBackType
}

message MatchDynamicConfigData {
  //阵营信息
  repeated com.tencent.wea.xlsRes.MatchSideInfo sideInfo = 1;
  //重新分配阵营信息
  repeated int32 reCamp = 2; // 重新分配阵营数;
  optional int32 minMatchNum = 3; //最少开启人数
  //机器人阵营信息
  repeated com.tencent.wea.xlsRes.MatchSideInfo teamRobotsSideInfo = 4;
}

enum MatchOperateType {
  MOT_Normal = 0; //正常匹配
  MOT_FillBack = 1; //回填玩家
}

message PlayerABTestInfo {
  map<string, ABTestDetails> abTestInfo = 1; // <指标key，实验数据细节>
}

message RoomPlayerClientInfo {
  optional int64 uid = 1;
  optional RoomMemberClientInfo roomMemberClientInfo = 2;
}
message RoomMemberClientInfo {//纯客户端数据，玩家下线清除
  optional int32 voiceState = 1; //语音状态
  optional int32 voiceRoomTag = 2; //语音标签
  optional int64 chooseMapTime = 3;//选地图时间,用来客户端显示是否在选地图中
}

enum RoomMemberToMemberNtfType {
  RMTMNT_InviteOpenVoiceNtf = 1;   //邀请开启语音
  RMTMNT_InviteOpenSpeakerNtf = 2; //邀请开启喇叭
}

message BattleMemberClientInfo {
  optional KVArray extraData = 99; //预留字段方便做热更新
}
message BattlePlayerClientInfo {
  optional int64 uid = 1; //玩家uid
  optional RoomMemberClientInfo roomMemberClientInfo = 2; //房间带过来的客户端属性
  optional BattleMemberClientInfo battleMemberClientInfo = 3; //只在局内用到的客户端属性，走battlesvr做下转发，不能高频，否则battlesvr压力太大
}



message ABTestDetails {
  optional string key = 1; // 冗余一份指标key
  optional string expDim = 2; // 实验维度
  optional string expId = 3; // 实验id
  optional string value = 4; // 具体的指标值
  optional KVArray extra = 5; // 预留附加字段
}

enum XiaowoCoverType {
  XCT_Xiaowo = 0;
  XCT_Party = 1;
}

message UgcMapLabelScore {
  map<int32, UgcMapLabelScoreInfo>  info = 1;
  optional int32 dimLabelId = 2;
  optional int32 comLabelId = 3;
  optional int64 warmCount = 4;
  optional string battleId = 5;
}
message UgcMapLabelScoreInfo {
  optional int32 label = 1;
  optional int64 score = 2;
  optional int64 count = 3;
}

enum UgcDownloadInfoType {
  UDIT_Unknown = 0;
  UDIT_CosData = 1;  // 之内服务内请求
  UDIT_CdnData = 2;  // 客户端/服务器请求
}

enum UgcDownloadType {
  UDT_Draft = 1;
  UDT_Publish = 2;
}

message UgcDownloadCosData {
  optional string bucket = 1;
  optional string key = 2;
  optional string id = 3;
  optional string token = 4;
  optional string region = 5;
  optional int64 startTime = 6;
  optional int64 endTime = 7;
}

message UgcDownloadCdnData {
  optional string bucket = 1;
  optional string km = 2;
  optional string region = 3;
}

message UgcDownloadSvrCosData {
  optional UgcDownloadCosData cosData = 1;
  optional string km = 2;
}

message UgcDownloadData {
  oneof downloadData {
    UgcDownloadCosData cosData = 1;  // 客户端请求草稿（cos）
    UgcDownloadCdnData cdnData = 2;  // 客户端/服务端请求发布地图下载数据（cdn）
    UgcDownloadSvrCosData svrCosData = 3;  // 服务端请求发布地图下载数据（cos）
  }
}

message UgcDownloadBaseData {
  optional string ugcVersion = 1;
  optional string clientVersion = 2;
  optional int64 originCreatorId = 3;
  repeated UgcMapMetaInfo metaInfo = 4;
  optional int32 ugcLv = 5;  // 客户端请求会带上
  optional int64 lastSaveTime = 6;  // 客户端请求是会带上
}

message PublicFittingSlotInfo {
  optional int32 slotId = 1;
  repeated int64 dressItemIds = 2;
}

// 玩法模式 玩法类型ID 拷贝自客户端 EMoeGameType
enum MatchTypeEMoeGameType {
  MTEMGT_EcaRoundGame_EditorMode = -1;
  MTEMGT_SimpleGame = -2;
  MTEMGT_NewComerGame = -1004;
  MTEMGT_EcaArchiveGame = -999;
  MTEMGT_DisplayGame = -1001;
  MTEMGT_CommunityGame = -2000; //大厅game
  MTEMGT_RunningGame = 1; // 竞速关卡
  MTEMGT_SurvivalGame = 2; // 生存关卡
  MTEMGT_GhostCatchGame = 3; // 抓鬼玩法
  MTEMGT_PlayFootballGame = 6; // 积分关卡踢足球
  MTEMGT_ChasingLinghtGame = 7; // 积分关卡追光聚光灯
  MTEMGT_ColoringFloorGame = 9; // 积分关卡染色块
  MTEMGT_IndividualColoringGame = 10; // 生存关个人涂色
  MTEMGT_ScoreSingleGame = 14; // 积分关卡 个人
  MTEMGT_ScoreRegimentalGame = 15; // 积分关卡 组队
  MTEMGT_ColorFootballGame = 16; // 积分关卡 撞色足球
  MTEMGT_DollsRunGame = 18; // 谁是杀手
  MTEMGT_HideAndSeekGame = 19; // 躲猫猫
  MTEMGT_WerewolfGame = 20; // 狼人杀
  MTEMGT_DDPGame = 21; //大乱斗
  MTEMGT_OneLifeRunningGame = 23; // 一命竞速
  MTEMGT_GunGameGame = 24; // 军备竞赛
  MTEMGT_BioChaseGame = 25; // 生化追逐
  MTEMGT_RogueLikeGame = 26; // 蟹仔冒险玩法
  MTEMGT_DDPTutorial = 28; // DDP教程玩法
  MTEMGT_GunGameTDMGame = 29; // 枪战团队冲锋玩法
  MTEMGT_TYCGame = 30; // 大亨玩法
  MTEMGT_BrGame = 31; // Br模式玩法
  MTEMGT_GunGameBPMGame = 32; // 枪战团队爆破玩法
  MTEMGT_TDGame = 33; // 团队塔防
  MTEMGT_TDSmashGame = 35; // 竞技塔防
  MTEMGT_OMDGame = 36; //兽人塔防
  MTEMGT_GunGameKCGame = 37; // 枪战团队赏金玩法
  MTEMGT_DfGame = 38; // 逃离玩法
  MTEMGT_OMDGameArchive = 39; //兽人塔防RootGame
  MTEMGT_UGCRoundGame = 41; // ugc统一模板game
  MTEMGT_GameTypePreset02 = 42; // 预设占用值
  MTEMGT_GameTypePreset03 = 43; // 预设占用值
  MTEMGT_GameTypePreset04 = 44; // 预设占用值
  MTEMGT_GameTypePreset05 = 45; // 预设占用值
  MTEMGT_GameTypePreset06 = 46; // 预设占用值
  MTEMGT_GameTypePreset07 = 47; // 预设占用值
  MTEMGT_GameTypePreset08 = 48; // 预设占用值
  MTEMGT_GameTypePreset09 = 49; // 预设占用值
  MTEMGT_JSGame = 60; // JS滑板玩法
  MTEMGT_JSBikeGame = 61; //JS自行车玩法
  MTEMGT_JSKartGame = 62; //JS卡丁车玩法
  MTEMGT_MoeChaseGame = 100; // Chase 玩法

  MTEMGT_UGCEditorGame = 300; // UGC 编辑模式
  MTEMGT_UGCPlayGame = 301; // UGC 游玩模式
  MTEMGT_HomeGame = 400; // 家园模式
  MTEMGT_FarmGame = 410; //农场玩法
  MTEMGT_CookGame = 420; //餐厅玩法
  MTEMGT_HouseGame = 430; //小窝玩法

  // Warning! [450,490] is reserved for SP game mode
  MTEMGT_SPDungeonGame = 450;
  MTEMGT_SPPVP = 451;
  // end

  // Warning! [500;600] is reserved for DDP game mode
  MTEMGT_DDPGameModeBegin = 500;
  MTEMGT_DDPTeamCompetitionGame = 501; // 大乱斗团竞
  MTEMGT_DDPGameModeEnd = 600;
  // end
  MTEMGT_DDBGame = 700; // 躲避球

  MTEMGT_DNDGame = 701; // DND
  MTEMGT_ArenaGame = 702; // Arena
  MTEMGT_ArenaTutorialGame = 802; // Arena首局

  MTEMGT_HOKGame = 900; // hok
  MTEMGT_CocHomeGame = 950; // coc小岛
  MTEMGT_CocHouseGame = 951;  // coc收藏馆

  // 大厅
  MTEMGT_LobbyGame = 2000; // 新大厅

  MTEMGT_AITrainLoopGame = -5001; // AI训练循环模式
}

message UgcCosInfo {
  repeated MapCoverInfo info = 1;
}

message MapCoverInfo{
  optional string coverId = 1;  // 缩略图id
  optional int32 index = 2;    //下标
  optional UgcMapMetaInfo info = 3; //cos信息
  optional bool isReplace = 4;  //是否替换
  optional string replaceId = 5;  //被替换的coverId

}

message MapCoverVideoInfo {
  optional int64 videoId = 1;  // 视频id
  optional UgcMapMetaInfo info = 2;  // cos信息
  optional bool inUsed = 3;  // 是否启用
}

message MapCoverVideoInfoSaved {
  optional MapCoverVideoInfo coverVideoMgr = 1;  // 来自管理端的配置
}

// 乐园loading基础信息
message MapLoadingInfo {
  message MapLoadingData {
    optional UgcMapMetaInfo info = 1;  // 自定义封面的cos信息
    optional int64 loadingId = 2;  // 自定义loadingId
    optional bool useOfficial = 3;  // 是否使用官方
  }
  repeated MapLoadingData loadingList = 1;  // 自定义loading列表
}

// 乐园单封面，不用多存储
message MapLobbyCoverInfo {
  message MapLobbyCoverIcon {
    optional UgcMapMetaInfo info = 1;  // 非官方图标，这里会有自定义cos
    optional int64 iconId = 2;  // 自定义图标的id
    optional int32 officialIconId = 3;  // 官方图标的id
    optional bool useOfficial = 4;  // 是否使用官方
  }
  message MapLobbyCoverData {
    optional UgcMapMetaInfo info = 1;  // 自定义封面的cos信息
    optional int64 lobbyCoverId = 2;  // 自定义封面的id
    optional bool useOfficial = 3;  // 是否使用官方
    optional string officialCoverId = 4;  // 官方封面id
  }
  optional MapLobbyCoverData coverInfo = 1;  // 自定义封面的信息
  optional MapLobbyCoverIcon iconInfo = 2;  // 自定义图标的信息
}

message MidasZoneAndServerData{
  map<string, MidasZoneAndServerDetail> detail = 1;
}

message MidasZoneAndServerDetail{
  optional string zone_id = 1; // 分区id, 分区id请使用全英文/全数字, 不要出现下划线、横杠等字符
  optional string zone_name = 2; // 分区名称(创建时必填)
  optional int32 env_flag = 3; // 环境标志
  optional int64 status = 4; // 0-已删除, 1-有效
  optional string server_id = 5; // 回调地址id)
  optional string server_name = 6; // 服务名称
  optional string url = 7; // 	支持ip、l5和域名地址(例如http://*************/v1/xxx, http://1323423:1234, https://midas.oa.com/api)
}

// 方向
enum Direction {
  DIR_EAST = 0;
  DIR_SOUTH = 1;
  DIR_WEST = 2;
  DIR_NORTH = 3;
}

// 农场偷菜我的记录
message FarmStealingMyRecord {
  optional int64 timestamp = 1; // 这里是毫秒，防止冲突
  optional int64 friendUid = 2; // 偷取的农场好友uid
  optional int64 coinValue = 3; // 偷取物品的农场币价值
  optional int32 cropCategory = 4;
  optional int32 ripeQuality = 5; //地块成熟类型
  repeated FarmStealingItemInfo item = 6; // 偷取到的物品
  optional bool isStealFishFail = 7;  // 是否偷鱼失败
  optional bool isCookVisitant = 8; // 是否是餐厅贵宾
  optional int32 cookVisitantAvatarId = 9; // 餐厅贵宾avatarId
}

// 农场被偷记录
message FarmStealingCropRecord {
  optional int64 timestamp = 1; // 这里是毫秒，防止冲突
  repeated FarmStealingItemInfo item = 2; // 偷取到的物品
  optional bool fail = 3;
  optional int32 cropCategory = 4;
  optional int32 stealFishExp = 5; // 偷鱼补偿经验
  optional bool isCookVisitant = 6; // 是否是餐厅贵宾
  optional int32 cookVisitantAvatarId = 7; // 餐厅贵宾avatarId
}

message FarmStealingItemInfo {
  optional int32 itemId = 1;
  optional int32 itemNum = 2;
}

// 农场施肥记录
message FarmFertilizeRecord {
  optional int64 timestamp = 1; // 这里是毫秒，防止冲突
  repeated FarmFertilizeItemInfo item = 2; // 施肥效果
  optional bool isAquarium = 3;
  optional int32 withPetId = 4; // 追随的宠物的id
  repeated FarmFertilizeItemInfo withPetItem = 5; // 追随的宠物的祈愿效果
}

message FarmFertilizeItemInfo {
  optional int32 itemId = 1; // 施肥作物
  optional com.tencent.wea.xlsRes.FarmCropRipeQuality quality = 2; // 施肥效果
}
// 农场宠物驱逐记录
message FarmPetEvictRecord {
  optional int64 timestamp = 1; // 这里是秒
  optional int32 petId = 2; // 宠物id
}
// 宠物喂食记录
message FarmPetFeedRecord {
  optional int64 timestamp = 1; // 这里是秒
  optional int32 petId = 2; // 宠物id
}
// 税收剩余记录
message FarmCloudTaxRecord {
  optional int64 timestamp = 1; // 这里是秒
  optional int32 playerCount = 2; // 多少玩家来出售
  optional int64 priceTotal = 3; // 总售价
  optional int64 taxLeft = 4; // 剩余税收
  optional int32 evtSeries = 5; // 事件系列
}

message FarmStrangerItem {
  optional int64 uid = 1; // role id
  optional int32 reason_id = 2; // 标签ID
  optional string reason_extra = 3;
  optional string reason_text = 4; // 推荐理由文本, unicode编码，客户端显示用
  optional string rcmdInfo = 8; // 社交推荐参数信息
  optional string abTestInfo = 9; // 社交实验参数信息
}

message AlgoFarmStrangerData {
  optional AlgoInfo algo_info = 1; // 推荐理由。上报关注recid
  repeated FarmStrangerItem rec_list = 2; // 推荐数据
}

// 农场偷菜记录
message FarmFishPoolRecord {
  optional int64 timestamp = 1; // 这里是秒
  optional proto_FarmFishPoolInfo farmFishPoolInfo = 2;
}


enum TLogFarmReason {
  TFO_UNKNOWN = 0;
  TFO_PLANT = 1; // 种植
  TFO_CARE = 2; // 照料
  TFO_HARVEST = 3; // 收获
  TFO_DESTROY = 4; // 销毁
  TFO_RIPE = 5; // 成熟
  TFO_STEAL = 6; // 偷菜
  TFO_FERTILIZE = 7; // 施肥
  TFO_ENCOURAGE = 8; // 鼓励
  TFO_RAIN = 9; // 下雨
  TFO_VILE = 10; // 霸王花吞食
  TFO_MAGIC = 11; // 仙术-加速
  TFO_MAGIC_RECOVER = 12; // 仙术-恢复
  TFO_MAGIC_DELAY = 13; // 仙术-延迟
}

enum BattlePlayerSummaryType {
  BPST_None = 0;
  BPST_BattleNum = 1;
  BPST_HOKTeamGuide = 2;//HOK玩法组队新手指引信息
  BPST_AnimeDressOutline = 3;//二次元装扮描边 0/1
}

message BattlePlayerSummary {
  repeated KeyValueInt32 kvList = 1;      // BattlePlayerSummaryType
  repeated KeyValueInfo kvStrList = 2;
}
/**
 * 地图设置数据
 */
message UgcMapSetData {
  optional UgcMapModelType modelType = 1;
}


enum UgcMapModelType {
  UgcMap_Unknown = 0;
  UgcMap_Speed = 1; //竞速
  UgcMap_Score = 2; //积分
}

enum FarmCropExpSource {
  FCE_UNKNOWN = 0;
  FCE_HARVEST = 1;
  FCE_GM = 2;
  FCE_IDIP = 3;
  FCE_UPDATE = 4;
}

enum FarmExpReason {
  FES_UNKNOWN = 0;
  FES_HARVEST = 1; // 作物动物的收获
  FES_FISHING = 2; // 钓鱼
  FES_BUILDING = 3; // 建筑升级解锁
  FES_GRID = 4; // 地块升级解锁
  FES_IDIP = 5; // idip
  FES_GM = 6; // gm
  FES_PROC_ITEM = 7; // 加工物
  FES_HOUSE_EXTEND = 8; // 小屋扩建
  FES_TALENT = 9; // 天赋
  FES_AQUARIUM = 10; // 水族箱
  FES_TASK_REWARD = 11; // 任务奖励
  FES_FROM_FARMITEM = 12; // 经验道具转换
  FES_EVENTBASE = 13; // 事件基础奖励
  FES_VILLAGER_FAVOR = 15; // 村民好感度对话奖励
  FES_CLOUDNPC = 16; // 云游商人
  FES_HOTSPRING = 17; // 温泉
  FES_PRAY2GOD = 18;  // 神像许愿
  FES_KIRIN = 19; // 仙麒麟
  FES_COOK_ONLINE_SETTLEMENT = 20; // 餐厅菜品结算
  FES_COOK_OFFLINE_SETTLEMENT = 21; // 餐厅菜品结算
  FES_COOK_VISITANT_SETTLE = 22; // 餐厅贵宾结算
  FES_COOK_LEVEL_UP = 23;   // 餐厅升级
}

message CsUgcForwardHeader {
  optional string openId = 1;
  optional int32 platId = 2;
  optional int64 uid = 3;
  optional int64 creatorId = 4;
  optional int64 clientVersion = 5;
  optional int32 cloudGameType = 6;  // 小游戏类型
}

message UgcAdminForwardHeader {
  optional string openId = 1;
  optional int32 platId = 2;
  optional int64 uid = 3;
  optional int64 creatorId = 4;
}

// 单个活动数据
message ActivityOneData {
  optional int32 id = 1;                   // 活动ID
  optional bool redDotShow = 3;               // 是否显示红点
  optional bool isRead = 4;                 // 是否已读
  optional proto_ActivityDetail detailData = 5;      // 活动详情
  optional int64 createTimeMs = 6;                   // 活动创建时间
  repeated proto_ActivityRedDot clickRedDotInfo = 7; // 点击消失红点
}

// 单个玩家活动数据
message CsPlayerActivityInfo {
  optional com.tencent.wea.xlsRes.ActivityMainConfig activityLabel = 1; // 活动配置
  optional ActivityOneData activityData = 2;                            // 活动数据
}

// 传递给客户端活动数据通用结构
message CsAllPlayerActivityInfo {
  repeated CsPlayerActivityInfo allActivityInfo = 1; // 当前玩家可见的所有活动信息
}

// 单个玩家活动数据(DS)
message DsOneActivityData {
  optional int32 id = 1;                             // 活动ID
  optional int32 type = 2;                           // 活动类型
  optional proto_ActivityDetail detailData = 3;      // 活动详细数据
}

// 玩家活动数据(DS)
message DsAllActivityData {
  repeated DsOneActivityData activityData = 1;       // 活动数据
}

enum UgcPlayerAccountStatus {
  UgcPlayer_Normal = 0;   //正常
  UgcPlayer_SignOut = 1;  //销号
}

enum FarmLiuYanMessageNtfType {
  FLYMNT_Unknown = 0;
  FLYMNT_Ban = 1;
  FLYMNT_Number = 2;
  FLYMNT_LastPullId = 3;
}

message BanListInfo {
  optional int64 mapId = 1;
  repeated int64 banIdList = 2;
  optional int64 creatorId = 3;
}

// 玩家信誉分变化记录
message PlayerReputationScoreRecord {
  optional int64 dt = 1;          // 变化时间
  optional int32 deltaScore = 2;      // 变化分数
  optional int32 totalScore = 3;      // 变化后分数
  optional int32 reason = 4;        // 变化原因
  optional int64 battleId = 5;        // 对局id, 用于玩家举报核实信息使用
}

// 信誉分行为周期累计信息
message BehaviorCycleCumulativeInfo {
  optional int32 behaviorId = 1;      // 行为id
  optional int64 cycleTriggerTime = 2;    // 周期触发时间, 针对处罚行为生效
  optional int32 triggerTimes = 3;      // 周期触发次数, 针对处罚行为生效
  optional string currentDay = 4;      // 当天时间, 形如20240502, 针对奖励行为生效
  optional int32 cumulativeScore = 5;     // 累计分数, 针对奖励行为生效
}

// 信誉分相关举报信息
message ReputationScoreReportInfo {
  optional int64 dt = 1;          // 举报时间
  optional int64 reporterUid = 2;      // 举报者uid
}

// 信誉分相关举报信息数组
message ReputationScoreReportInfoArray {
  repeated ReputationScoreReportInfo reportInfos = 1;           // 信誉分相关举报信息
}

// 玩家玩法信誉分信息
message PlayerPlayModeReputationScoreInfo {
  optional int32 reputationScore = 1;              // 当前信誉分
  repeated PlayerReputationScoreRecord records = 2;        // 信誉分变化记录列表
  map<int32, BehaviorCycleCumulativeInfo> cumulativeInfos = 3;  // 信誉分行为id->信誉分行为周期累计信息
  map<int64, ReputationScoreReportInfoArray> reportInfos = 4;   // 对局id->信誉分相关举报信息
}

message RankImageInfo {
  optional RankId rankId = 1;
  optional int64 seqId = 2;                 // 镜像seqId, 0则表示由web console生成
  optional int32 seasonId = 3;
  optional int64 ts = 4;                    // ms
  repeated RankImageDetailInfo details = 5; // 镜像部署详情
}

message RankImageDetailInfo {
  optional string instanceId = 1;     // 实例ID
  optional int64 ts = 2;              // ms
  optional int32 errorCode = 3;       // 生成状态
}

message BlackMarketData {
  optional string openId = 1;
}

enum UgcTableType{
  UgcBriefType = 0;
  UgcPublishType = 1;
}

message HotContent{
  optional string text = 1; // 热搜文字
  optional bool isNew = 2; //是否为【新】
  optional bool isHot = 3; //是否为【热】
}

message FarmDynamicInfo {
  optional int64 farmId = 1;
  optional int32 farmLevel = 2;
  optional int64 canStealTime = 3;  // 可偷取时间
  map<uint64, proto_CanStealTime> canStealTimeInfo = 4;   // 服务器专用、客户端不关注
  map<uint64, proto_CanStealTime> cookCanStealTimeInfo = 5; // 服务器专用、客户端不关注
}

message F2F_Moncard {
  optional int64 lastBuyTime = 1; // 最后一次买月卡
  optional int64 endTime = 2; // 月卡到期时间
  optional int64 effectTime = 3; // 月卡总生效时间
}

// 透传给farm
message ForwardToFarmCtx {
  optional F2F_Moncard moncard = 4;
  optional proto_FarmEventSync event = 5;
  optional int32 npcStage = 7;
  optional int64 npcFarmID = 8;
}

// 透传给farmHouse
message ForwardToHouseCtx {
}

// 透传给farmCook
message ForwardToCookCtx {
}

message UgcUpdateRankInfo {
  optional int64 uid = 1;   // uid
  optional int64 rankId = 2;   // rankId
  optional int32 score = 3;    // 上报的分数
  repeated int32 extraScores = 4;   // 额外分数

  optional bool isInAiTakeOverProcess = 100;  // 是否处于ai接管流程中
}

// 独立app用户信息类型枚举
enum AppUserInfoType {
  TYPE_PLATOPENID_TO_APPCREATORID = 1;     // 平台openid->app账号创作者id映射
  TYOE_APPCREATORID_TO_PLATOPENID = 2;     // app账号创作者id->平台openid映射
  TYPE_APPCREATORID_TO_GAMECREATORID = 3;  // app账号创作者id->游戏账号创作者id映射
}

// 独立app用户信息
message AppUserInfo {
  optional int64 appCreatorId = 1;      // app账号创作者id
  optional string platOpenId = 2;       // 平台openid
  optional int64 gameCreatorId = 3;     // 游戏账号创作者id
  optional string openId = 4;           // 游戏账号gopenid
  optional int32 platId = 5;            // 游戏账号平台id
  optional int64 uid = 6;               // 游戏账号uid
}

// ugc账号来源类型枚举
enum UgcAccountSourceType {
  UGC_ACCOUNT_SOURCR_TYPE_GAME = 0;     // ugc账号来源类型-元梦之星游戏
  UGC_ACCOUNT_SOURCR_TYPE_APP = 1;      // ugc账号来源类型-独立app
}

enum ProgressType{
  Ugc_Success = 0;    //成功
  Ugc_NoStart = 1;    //未启动
  Ugc_Copying = 2;    //copy中
  Ugc_Copy_Text_Fail = 3;  //名字上限
  Ugc_Copy_Exceed_Limit_Fail = 4; //超过容量
}

message WhiteListServiceConfig {
  optional string service = 1;
  repeated uint32 group_id_list = 2;
  optional bool is_on = 3;             // 是否开启白名单
}
message WhiteListTableMeta {
  optional string name = 1;            // 表名 唯一的
  optional uint32 count = 2;           // 记录数
  optional uint64 last_modify_time = 3;// 最后一次修改时间，秒
  optional string etag = 4;            // etag, 不相同表示有变更
}
message WhiteListTableRow {
  optional uint32 group_id = 1;
  optional string open_id = 2;
}
message WhiteListTable {
  optional WhiteListTableMeta meta = 1;
  repeated WhiteListTableRow rows = 2;
}

enum IAAReason {
  IAAR_Unknown = 0;
  IAAR_SettlementSendItem = 1;            // 掉落礼包
  IAAR_SettlementChangeQualify = 2;       // 积分增长
  IAAR_RaffleFree = 3;                    // 抽奖免费抽数
  IAAR_GuideToApp = 4;                    // 云转端
  IAAR_UgcInLevel = 5;                    // UGC局内
}

// 信誉分关卡检测数据
message ReputationScoreLevelDetail {
  optional int32 levelId = 1;                             // 关卡id
  optional int64 data = 2;                                // 检测数据
}

// 玩家信誉分行为信息
message PlayerReputationScoreBehaviorInfo {
  optional int32 behaviorId = 1;                          // 中途退出、挂机等行为id
  optional int32 behaviorType = 2;                        // 行为类型
  optional int64 detectData = 3;                          // 检测数据
  repeated ReputationScoreLevelDetail levelDetectData = 4;// 关卡维度的检测数据
}

// 玩家信誉分行为上报信息
message PlayerReputationScoreBehaviorReportInfo {
  optional int64 uid = 1;                                 // 用户uid
  optional int32 modeId = 2;                              // 玩法id
  optional int32 behaviorId = 3;                          // 中途退出、挂机等行为id列表中优先级最高的行为id
  optional string matchId = 4;                            // 匹配id
  optional string battleId = 5;                           // 对局id
  optional int32 scoreId = 6;                             // 信誉分分数id
  repeated PlayerReputationScoreBehaviorInfo infos = 7;   // 信誉分行为数据
}

// 排行榜数据
message RankInfoPbItem {
  optional int32 key = 1;   // rankid,兼容
  optional int32 value = 2;   // 分数
  optional int32 seasonId = 3;
}

// 修改玩家排行榜
message FreshPlayerRanksInfo {
  repeated RankInfoPbItem playerRanks = 1;  // 玩家的排名
  optional int32 matchType = 2;
  optional int32 seasonId = 3;  // 赛季id
}

message UgcMatchRecordSummary {
  optional int64 playId = 1;
  optional int64 playTime = 2;
  optional string playName = 3;  // 配置的地图、合集名
  optional string playUgcName = 4;  // 实际游玩的地图名字
  optional int64 playUgcId = 5;  // 地图id
}

message MatchBattleRecordPlayer {
  optional int64 uid = 1;
  optional bool isRobot = 2;
  optional string profile = 3;// 头像id
  optional string name = 4;// 玩家当前的名字
  optional int32 gender = 5;// 性别
  repeated int32 dressUpItems = 6; // 时装装扮信息
}

message MatchBattlePlayerRecordDetail {
  map<int64, MatchBattleRecordPlayer> actors = 3;  // 参与者
}

message TaskExtraInfo {
  optional bool rewardLock = 1; // 奖励锁（新手任务使用）
  optional string burdenReduceDesc = 2; // 减负任务的新描述文案
}

message TaskStatusInfo {
  optional int32 id = 1;
  optional xlsRes.TaskStatus status = 2; // 任务状态
  repeated ConditionProgressInfo completeConditionList = 3; // 完成条件进度列表
  optional bool delete = 4; // 是否删除
  optional int64 completeTime = 5; // 任务完成时间
  optional TaskExtraInfo extraInfo = 6; // 额外信息
  repeated RewardConfInfo rewardConf = 7; // 奖励配置
  optional int32 TaskTagType = 8; // 任务标签类型 参考TaskTagType取值
  optional int32 repeatNum = 9; // 重复完成次数
  optional int32 maxRepeatNum = 10; // 最大可完成次数
  optional bool hide = 11; // 任务是否隐藏
  repeated xlsRes.TaskOptionalReward optionalReward = 12; // 任务可选奖励
  optional int32 rewardIndex = 13; // 当前选择的奖励 -1为未选择
}


message PlayerCurrentActivityInfo {
  optional int64 uid = 1;                   // 玩家uid
  optional int64 squadId = 2;               // 小队id
  optional int32 activityId = 3;            // 活动id
  optional int64 lastUpdateTimestampMs = 4; // 上次更新时间
  optional int32 currentMemberNumber = 5;   // 当前成员数
}

message FaceToFaceRoomType {
  optional int32 map_type = 1;        // 地图类型，业务层自定义
  optional int32 map_id = 2;          // 地图ID，业务层自定义
  optional int32 room_type = 3;       // 房间类型，业务层自定义
  optional int32 radius = 4;          // F2F房间半径
}

message GeoInfo {
  optional int32 nation = 1;          // 国家编码
  optional int32 province = 2;        // 省份编码
  optional int32 city = 3;            // 城市编码
  optional int32 town = 4;            // 区县编码
  optional string nationStr = 5;      // 国家
  optional string provinceStr = 6;    // 省份
  optional string cityStr = 7;        // 城市
  optional string townStr = 8;        // 区县
}

message RoomGeoInfo {
  optional int32 pin = 1;         // lbs短号
  optional int64 createTime = 2;  // 创建时间
  optional GeoInfo geoInfo = 3;   // 地理信息
  optional int32 latitudeE6 = 4;
  optional int32 longitudeE6 = 5;
  optional int64 creator = 6;     // 创建者
  optional string pinStr = 7;     // 短号字符串
}

message CupsBaseInfo {
  optional int32 conditionId = 1;     // 条件维度id
  optional int32 num = 2;             // 基础数量
}

message CupsAdditionInfo {
  optional com.tencent.wea.xlsRes.AdditionType additionType = 1;  // 加成类型
  optional int32 ruleId = 2;          // 加成规则id
  optional int32 percent = 3;         // 加成百分比
  optional int32 num = 4;             // 加成数量
}

// 增加奖杯详细信息
message AddCupsDetailInfo {
  optional int32 baseNum = 1;                 // 基础数量
  optional int32 additionNum = 2;             // 加成数量
  repeated CupsBaseInfo baseInfo = 3;         // 基础信息
  repeated CupsAdditionInfo additionInfo = 4; // 加成信息
  optional bool isOverflow = 5;               // 是否超过周上限
  optional int32 realAddMainProgress = 6;     // 实际增加的总进度
}

enum RelationOrderByField {
  field_intimacy = 1;
  field_togetherBattleCount = 2;
  field_recentInteractTs = 3;
  field_addTime = 4;
}

enum SnsInteractiveOp {
  op_set = 0;
  op_add = 1;
  op_sub = 2;
  op_sub_not_negative = 3;   // 不为负
}

message AppreciateMapRecord {
  optional bool played = 1;     // 是否游玩
  repeated int32 scores = 2;    // 各项评分
  repeated string tags = 3;     // 选择的标签
  optional string comment = 4;  // 评论
  optional int64 threadId = 5;  // 评论帖子的id
  optional string feedback = 6; // 问题反馈
}

message AppreciateMapInfo {
  repeated string tags = 1;     // 管理端配置的标签
}

message GamePlayInfo {
  optional string featureName = 1;      // 玩法名称
  optional string pakVersion = 2;       // 二进制版本号, pak version
  optional string confVersion = 3;      // 配置版本号
}

// 社团挑战星光值
message StarLightInfo{
  optional uint64 uid = 1;
  optional int32 starLight = 2;
}
message FriendUgcInfo{
  optional int64 uid = 1;
  optional int64 playTime = 2;
}

message BPMidasCBData {
  optional int32 bpType = 1;      // BP类型
  optional int32 seasonId = 2;    // 目标赛季
  optional int32 incLevel = 3;    // 增加等级
  optional int32 targetPay = 4;   // 目标付费等级
}

message TrainingCampSportsmanData {
  repeated TrainingCampSportsman sportsman = 1;
}

message TrainingCampSportsman {
  optional int32 id = 1;
  optional int32 score = 2;
  repeated KeyValueInt32 attr = 3;
}

message TrainingCampAssistData {
  optional int64 week = 1;
  repeated int64 weekUid = 2;
  repeated TrainingCampAssist assist = 3;
}

message TrainingCampAssist {
  optional int64 uid = 1;
  optional int32 sportsId = 2;
  optional int64 time = 3;
}

// 全部训练对象数据
message TrainingAllObjcetData {
  optional int32 curScore = 1;				// 当前可使用的积分数量
  repeated TrainingObjcetData data = 2;		// 训练对象数据
  repeated int32 globalAwardRecord = 3;		// 全局奖励的领奖记录
}

// 单个训练对象数据
message TrainingObjcetData {
  optional int32 id = 1;			// ID
  optional int32 score = 2;			// 训练进度
  repeated int32 awardRecord = 3;	// 单个对象的领奖记录
}

// 助力数据
message TrainingAssistData {
  optional int64 weekTime = 1;			// 此条数据产生的时间(周)
  repeated int64 uid = 2;   			// 数据产生关联的UID(被谁助力 or 助力了谁)
}

// 助力详细信息
message TrainingAssistDetail {
  optional int64 uid = 1;			// 玩家UID
  optional int32 id = 2;			// 助力对象ID
  optional int64 time = 3;			// 时间
  optional int32 type = 4;			// 数据类型 0.助力记录 1.被助力记录
}

// ugcdatastore 互动数据相关的定义
message UgcPlayerPublicAttrRange {//限定的上下限
  optional int64 min = 1; //下限
  optional int64 max = 2; //上限
}
message UgcPlayerPublicAttrAutoIncremental {//自增相关的属性
  optional int32 increaseTimes = 1; //自动增长次数
  optional int32 increaseFrequency = 2; //自动增长频率
  optional int32 increaseValue = 3; //自动增长值
  optional int64 lastIncreaseTime = 4; //上次增长的时间
}
enum UgcPlayerPublicAttrFieldType {
  UPAFT_None = 0;
  UPAFT_Attr_Value = 1; //改属性值
  UPAFT_Min = 2; //下限
  UPAFT_Max = 3; //上限
  UPAFT_Increase_Times = 4; //增长次数
  UPAFT_Increase_Frequency = 5; //增长频率
  UPAFT_Increase_Value = 6; //每次自增的值
}
message UgcPlayerPublicAttr {//互动数据
  optional int64 attrValue = 1; //互动数据的当前值
  optional int64 modifyTime = 2; //最后修改时间，毫秒
  optional UgcPlayerPublicAttrRange range = 3; //限定的上下限，0、0表示不限制
  optional UgcPlayerPublicAttrAutoIncremental autoIncremental = 4; //自动增长参数
}

message UgcPlayerPublicCacheInfo {//互动数值缓存
  optional int64 attrValue = 1; //互动数据的当前值
  optional UgcPlayerPublicAttrRange range = 3; //限定的上下限，0、0表示不限制
  optional UgcPlayerPublicAttrAutoIncremental autoIncremental = 4; //自动增长参数
}

enum UgcPlayerPublicAttrModifyLimitType {//修改限定
  UMLT_None = 0;
  UMLT_Limit_Old_Value = 1;   // 限定老的值，一般是用于要求老值不被修改的情况下，才做修改
}
message UgcPlayerPublicAttrModifyLimitOldValueArgs {
  optional UgcPlayerPublicAttr attr = 1; // 限定的老值
}
message UgcPlayerPublicAttrModifyLimit {//限定方式
  optional int32 limitType = 1; // 枚举类型UgcPlayerPublicAttrModifyLimitType
  oneof limitArgs {
    UgcPlayerPublicAttrModifyLimitOldValueArgs limitOldValueArgs = 2;
  }
}
enum UgcPlayerPublicAttrModifyType {
  UMT_Set_Attr_All_Fields = 1; // 设置全部属性
  UMT_Set_Attr_Field = 2; // 设置某一个属性值
  UMT_Modify_Attr_Value = 3; // 增量变更attrValue
}
message UgcPlayerPublicModifyCmdModifyAttrValueArgs {// 增量变更attrValue的参数
  optional int64 changeAttrValue = 1; // 变更的量，如果是直接改成目标值需要用UgcPlayerPublicModifyCmdSetAttrFieldArgs
}
message UgcPlayerPublicModifyCmdSetAttrFieldArgs {// 设置某一个属性值
  optional int32 fieldType = 1; // 枚举类型UgcPlayerPublicAttrFieldType
  optional int64 fieldValue = 2; // 变更的值或最终要改成的值，依赖isIncrementalChange的取值
}
message UgcPlayerPublicModifyCmdSetAttrAllFieldsArgs {// 设置全部属性的参数
  optional UgcPlayerPublicAttr attr = 1; // 设置全部属性
}

message UgcPlayerPublicAttrModifyCmd {
  optional string attrName = 1; // 修改的属性名，限定长度，20个字符
  optional int32 modifyType = 2; // 枚举类型UgcPlayerPublicAttrModifyType
  optional UgcPlayerPublicAttrModifyLimit limitInfo = 3; // 限定信息，如果用服务器默认限定就不用传了
  oneof cmdArgs {
    UgcPlayerPublicModifyCmdSetAttrAllFieldsArgs setAllFieldArgs = 10; //设置全部属性的参数
    UgcPlayerPublicModifyCmdSetAttrFieldArgs setFieldArgs = 11; //设置某一个属性值
    UgcPlayerPublicModifyCmdModifyAttrValueArgs modifyAttrValueArgs = 12; //增量变更attrValue的参数
  }
}

message UgcPlayerPublicAttrModifyResult {//每个cmd的结果
  optional int32 result = 1; //修改是否成功,0:成功，1：命令总数超过上限, 2：修改超过范围，3：校验老值限定失败，4:修改值不合法，5:异常
  optional int64 modifyValue = 2; //修改导致的变化量
}

message UgcPlayerPublicArrayElement {
  repeated int64 values = 1; //每个元素包含的值，最大数量为6,不保证有6个，客户端取值的时候需要处理成能取到0
}
message UgcPlayerPublicArrayAttr {
  repeated UgcPlayerPublicArrayElement elements = 1; //数组元素，最大数量为100
}

message UgcPlayerPublicAttrValueArray {
  optional int64 uid = 1; //玩家Id
  repeated int64 attrValues = 2; //属性值列表
}

message FriendHistoryInteractData {
  repeated proto_FriendInteractDetailAttr interactDetail = 1;
}

message FarmGiftCtx {
  optional string billNo = 1;
  optional string name = 2;
  optional HeadFrame headFrame = 3;
  optional string face = 4;
  optional string openId = 5;
  optional int64 giftID = 6;
  optional int32 skinID = 7;
  optional int32 critical = 8;
  optional float ratio = 9; // 倍数
}

message FarmGift {
  optional int32 itemID = 1;
  optional int32 count = 2;
  optional string pos = 3;
  optional string msg = 4;
  optional FarmGiftCtx ctx = 5; // 客户端不用管
}

// 奖励配置
message RewardConfInfo {
  optional int32 itemId = 1; // 道具id
  optional int32 num = 2; // 道具数量
  optional int32 validPeriod = 3; // 有效期(天)
  optional int64 expireTimestamps = 5; // 有效期(时间戳ms)
}

enum AiNpcFeedBackTotality {
  thumbs_up = 1;
  thumbs_down = 2;
}

enum LobbyModeType {
  LMT_Lobby = 0; // 广场大厅
  LMT_TeamShow = 1; // 组队秀页面
}

enum ClientSceneType {
  CST_Default = 0; // 默认值
  CST_TeamShow = 1; // 组队秀页面
  CST_HUD = 2;  // HUD页面
}

message ClubHeatTemplate {
  map<int32, int64> tmpWeek = 1;
  map<int32, int64> tmpWeekDelta = 2;
  map<int32, int64> curWeek = 3;
  map<int32, int64> curWeekDelta = 4;
  optional int64 tmpWeekTime = 5;
}

message ClubHeatDelta {
  optional double delta1 = 1;
  optional double delta7 = 2;
}

message UgcMapSetting {
  optional int32 dataStoreSaveType = 1; // 数据存储类型  enum DataStoreSaveType
  optional UgcDatastoreAccessedPermissionInfo datastoreAccessedPermissionInfo = 2; //可访问当前地图的UGC地图信息列表
}

enum RecruitEntryType {
  RET_Room = 0;
  RET_Battle = 1;
}

message PlayUgcMapEventData {
  optional int64 mapId = 1;
  optional int64 lobbyMapId = 2;
  optional int32 mapSource = 3;
  optional int64 spendTimeSec = 4;
}

message GameVersionBrief {
  optional string client = 1;   // app client version
  repeated KVEntry play = 2;    // app game play versions
}

message GameVersionSummary {
  repeated GameVersionBrief brief = 1;
}
message BattleRoomMidJoinShowInfo {
  optional int64 battleRoomNo =  1;  //房间短码
  optional string battleRoomPwd = 2;  //房间密码
  optional int64 lastHeartbeatTs = 3; //心跳
  optional int64 leaderId = 4; //队长
  optional string name = 5; // 房间名
  optional string param1 = 6; //备用
  optional string param2 = 7; //备用

}

message BattleMidJoinExtraInfo {
  optional string battleRoomPwd = 1;  //房间密码
}


message ActivityGroupingReturnReqInfo {
}

message ActivityGroupingReturnResInfo {
  optional int32 currentRewardStageId = 1; // 可领取奖励等级
  optional int32 RewardedStageId = 2; // 已领取奖励等级
  optional ArraySquadMember groupMemberInfo = 3;
  optional int64 leaderUid = 4; // 团长UID
  optional int64 tteamId = 5; // 小队ID
}

message ActivityGroupingReturnReqReward {
}

message ActivityGroupingReturnResReward {
  optional int32 rewardedStageId = 1; // 已领取奖励等级
}

message ActivityGroupingReturnReqJoin {
  optional int64 teamId = 1;
}

message ActivityGroupingReturnResJoin {
}

message ActivityGroupingReturnReqQuit {
}

message ActivityGroupingReturnResQuit {
}

message ActivityGroupingReturnReqKick {
  optional int64 opUid = 1;
}

message ActivityGroupingReturnResKick {
}

message ActivityGroupingReturnReqFriendList {
  repeated int64 friendUid = 1;
}

message ActivityGroupingReturnResFriendList {
  repeated KeyValueInt64 friendInfo = 1;
}

message ThemeAdventureOpenMysteryBoxReqInfo {
  optional int32 activityId = 1;
  optional int64 mysteryBoxId = 2;
}

message ThemeAdventureOpenMysteryBoxResInfo {

}

message EaseBurdenAppointmentReqInfo {
}

message EaseBurdenAppointmentResInfo {
}


// 独立app publish表通用信息
message UgcAppPublishCommonInfo {
  optional int32 likeCount = 1;       // 点赞数量
  optional int64 blackLikeCount = 2;  // 黑名单点赞数量
  optional UgcBestRecord bestRecord = 3; //最佳记录
  optional UgcPlayRecord playRecord = 4; //游玩记录
}
message UgcDatastoreAccessedPermissionConfig {
  optional int64 accessibleUgcId = 1; //可访问当前地图的ugc地图ID
  optional int64 time = 2; //设置时间
}
message UgcDatastoreAccessedPermissionInfo { //可访问当前地图的UGC地图信息列表
  repeated UgcDatastoreAccessedPermissionConfig configs = 1;
}
enum HouseKickReason {
  HKR_Uknown = 0;
  HKR_Exit = 1; // 正常退出
  HKR_DsExit = 2; // 玩家的ds掉线了
  HKR_Evict = 3; // 农场主人驱逐
  HKR_VerUp = 4; // 版本号升级，你被踢了
  HKR_VerDown = 5; // 版本号下降，你被踢了
  HKR_PutDown = 6;   // 下架
  HKR_BeBlack = 7; // 被拉黑
  HKR_Decorated = 8; // 小屋装修
  HKR_RemoveVillager = 9; // 请离村民
  HKR_SwitchRoom = 10; // 切换了房间
}

enum CookKickReason {
  CKR_Uknown = 0;
  CKR_Exit = 1; // 正常退出
  CKR_DsExit = 2; // 玩家的ds掉线了
  CKR_Evict = 3; // 农场主人驱逐
  CKR_VerUp = 4; // 版本号升级，你被踢了
  CKR_VerDown = 5; // 版本号下降，你被踢了
  CKR_PutDown = 6;   // 下架
  CKR_BeBlack = 7; // 被拉黑
  CKR_Decorated = 8; // 小屋装修
}

enum CookStateType {
  CST_Closing = 0; // 关店中
  CST_Opening = 1; // 正常营业中
}

enum FarmType {
  FT_Unknown = 0;
  FT_Player = 1; // 玩家农场
  FT_NPC = 2; // npc农场
  FT_Max = 3;
}

enum FarmNewbieNPCState {
  FNS_Init = 0;
  FNS_StealFinished = 1;
  FNS_Max = 2;
}

message ActivityCookie {
  optional int32 actId = 1; // 活动id
  optional int32 actType = 2; // 活动类型
  optional int32 code = 3; // 活动自定义识别码
  repeated int64 iParams = 4; // 活动参数列表
  repeated string sParams = 5; // 活动参数列表
}

message BookOfFriendsReportData {
  optional int32 type = 1;
  optional int32 times = 2;
  optional int64 credit = 3;
  optional int64 upTimeSec = 4;
}

message BookOfFriendsIdentity {
  optional string openId = 1;
  optional int32 platId = 2;
  optional int64 uid = 3;
  optional string accessToken = 4;
  optional int32 accountType = 5;
  optional int32 farmLevel = 6;     // 农场等级, 用于判断是否是回流用户
  optional int64 logoutTimeMs = 7;  // 上一次登出时间, 用于判断是否是回流用户
  optional int64 clientVersion = 8; // 客户端版本号, 用于判断用户是否有资格参与本次活动
}

enum TaskTagType {
  TTT_Default = 0;
  TTT_BurdenReduce = 1; // 减负
}

message FarmLiuYanMessage {
  optional int64 Uid = 1;           // 留言人uid
  optional string NickName = 2;     // 留言人昵称
  optional string Content = 3;      // 留言内容
  optional int64 SendTime = 4;      // 留言时间
}

message FarmCropStatusInfo {
  optional int64 cropDryTimeSec = 1; // 最近的干涸作物-时间
  optional int64 cropRipeTimeSec = 2; // 最近的成熟作物-时间
  optional int64 animalHungryTimeSec = 3; // 最近的饥饿动物-时间
  optional int64 animalRipeTimeSec = 4; // 最近的成熟动物-时间
  optional int64 animalCanEncourageTimeSec = 5; // 最近的可助产动物-时间
  optional int32 dryCropType = 6; // 最近的干涸作物-类型
  optional int32 ripeCropType = 7; // 最近的成熟作物-类型
  optional int32 hungryAnimalType = 8; // 最近的饥饿动物-类型
  optional int32 ripeAnimalType = 9; // 最近的成熟动物-类型
  optional int32 canEncourageAnimalType = 10; // 最近的可助产动物-类型
  optional int64 fishRipeTimeSec = 11; // 鱼塘可钓时间
  optional int64 machineRipeTimeSec = 12; // 加工器成熟时间
  optional int64 aquariumRipeTimeSec = 13; // 水族箱成熟时间
  optional int32 ripeMachineType = 14; // 加工器成熟养殖物类型
  optional int32 fishPoolLayer = 15; // 鱼塘深度
  optional int64 fishProtectTimeSec = 16; // 鱼塘保护到期时间
}

message FollowerInfo {
  optional int64 uid = 1;           // 点赞者uid
  optional int64 followTimeMs = 2;  // 点赞时间(毫秒)
  optional int32 source = 3;        // 点赞来源
  optional string text = 4;         // 点赞内容
  optional bool hasFollowed = 5;   // 是否点赞过对方
}

enum BattleBroadcastInfoType {
  BBIT_Unknown = 0;
  BBIT_UgcLoadingProcess = 1;
}

message PlayerUgcLoadingProcessInfo {
  optional int64 playerUid = 1;
  optional int32 process = 2;
  optional int64 updateTimestamp = 3;
  optional bool isOffline = 4;
  // 这个结构体中的isOffline表示在这次tick中发现玩家离线了，则标记一下，若isOffline为true时，若process为0说明该次tick中玩家未设置
  // 其他没有设置processInfo或者收到ifOffline是false的玩家则表示该玩家在线的
}

message UgcLoadingProcessInfo {
  repeated PlayerUgcLoadingProcessInfo processInfo = 1;
}

message BattleBroadcastInfo {
  optional int32 type = 1;  // 参考BattleBroadcastInfoType
  oneof data {
    UgcLoadingProcessInfo ugcLoadingProcessInfo = 2; // ugc对局loading进度同步
  }
}

message MQUgcDataBlobSaved {
  optional int64 ugcId = 1;
  optional int32 type = 2;
  optional string blob = 3;
  optional bool replace = 4;
}

enum AigcNpcActType {
  AigcNpcAct_Chat = 1;
  AigcNpcAct_Sing = 2;
  AigcNpcAct_Dance = 3;
  AigcNpcAct_Puzzle = 4;
  AigcNpcAct_Interact = 5;
  AigcNpcAct_PalCreate = 6;
  AigcNpcAct_MoodAdd = 7;
}

enum AigcNpcInteractType {
  AigcNpcInteract_Player = 1;  // 玩家邀请
  AigcNpcInteract_Npc = 2;   // npc邀请
}

message CoMatchInfo {
  optional int64 uniqueId = 1; // 唯一id，roomId或battleId
  repeated BattlePlayerCoMatchInfo players = 2; // 玩家列表
  optional int64 firstProposeEndTimeMs = 3; // 第一个有效提议结束时间ms
}

message BattlePlayerCoMatchInfo {
  optional int64 uid = 1; // 玩家uid
  optional int32 agreeFlag = 2; // 同意标记，0未投票，1同意，2拒绝
  // 联调时的假数据
  optional int64 teamId = 3; //
  optional bool isLeader = 4; //
}

enum StreamPlatType {
  SPT_Wechat = 0; // 微信视频号
  SPT_Douyin = 1; // 抖音
}

message UgcAchievementIndex {
  optional int64 ugcId = 1;
  optional int32 achId = 2;
  optional int32 verId = 3;
  optional int32 type = 4;  // 类型 0:默认;1:已发布
}

message UgcAchievementConf {
  optional string confData = 1;  // 配置数据
}

message UgcAchievementData {
  optional UgcAchievementIndex index = 1;
  optional UgcAchievementConf conf = 2;
  optional int64 progress = 3;
  optional int32 isFinish = 4;
  optional int64 finishTime = 5;
}

enum RaffleTabGroup {
  RTG_QiYuan = 1;   // 祈愿
  RTG_FaXian = 2;   // 发现
}

// 加入多人活动小队来源
enum ActivityMultiPlayerSquadJoinReason {
  // 未知
  AMPSJR_Unknown = 0;
  // 游戏内好友
  AMPSJR_InGameFriend = 1;
  // 游戏内社团
  AMPSJR_InGameClub = 2;
  // 游戏内大厅
  AMPSJR_InGameLobby = 3;
  // 游戏内世界
  AMPSJR_InGameWorld = 4;
  // sns好友
  AMPSJR_SnsFriend = 5;
  // idip指令 svrOnly
  AMPSJR_Idip = 6;
  // 默认初始化 svrOnly
  AMPSJR_Init = 7;
  // 狼人杀社区频道
  AMPSJR_WolfKillCommunityChannel = 8;
  // 万松书院双人舞
  AMPSJR_WansongAcademyDance = 9;
}

message UgcShowDataStoreAchievement {
  message UgcShowDataStoreAchievementDetail {
    optional int32 achId = 1;
    optional int32 achProcess = 2;
    optional bytes extraData = 3;
    optional int32 isFinish = 4;
    optional int64 finishTime = 5;
  }
  // 获取,更新,删除 可以指定某个id
  // 获取若不指定就全拉
  map<int32, UgcShowDataStoreAchievementDetail> data = 1;
}

message UgcShowDataStoreHead {
  optional int64 dataVersion = 1;  // 数据版本 修改的时候需要传递原有的 如果版本过低 需要重新拉一下再更新
  optional int64 dataUpdate = 2;  // 数据更新时间
  optional int32 dataType = 3;  // 数据类型 UgcShowDataStoreType
}

message UgcShowDataStoreBody {
  oneof store {
    UgcShowDataStoreAchievement achievement = 1;
  }
}

message UgcShowDataStoreWrapper {
  optional UgcShowDataStoreHead head = 1;
  optional UgcShowDataStoreBody body = 2;
}

message UgcShowDataStoreOperate {
  optional UgcShowDataStoreHead head = 1;
  optional UgcShowDataStoreBody set = 2;  // 设置的
  optional UgcShowDataStoreBody del = 3;  // 删除的
  optional bool clear = 4;  // 清空
}

message UgcPlayerShowDataStore {
  optional int64 uid = 1;
  repeated UgcShowDataStoreWrapper wrappers = 2;
}

enum CoMatchAgreeFlag {
  CMAF_Unhandled = 0; // 未处理
  CMAF_Agree = 1; // 同意
  CMAF_Reject = 2; // 拒绝
  CMAF_Leave = 3; // 离开
}

message MobaRankSnapshotItem {
  optional int32 rankId = 1;
  optional int32 global = 2;
  optional int32 province = 3;
  optional int32 city = 4;
}

message MobaRankSnapshotInfo {
  optional int64 uid = 1;
  optional int32 seasonId = 2;
  repeated MobaRankSnapshotItem items = 3;
}

message FarmGridEventData {
    map<int32, int32> gridType2Num = 1;
}

message HallOfFameMap{
  optional uint64 ugc_id = 1; // 地图ID
  optional string hof_desc = 2; // 介绍文本
  optional int32 play_type = 3; // 对局方式。0=单人；1=多人
}

message UgcLevelSettlementInfo {
  optional int64  ugcId = 1;
  optional AlgoInfo recommendMapAlgoInfo = 2;
  optional string fromCollectionId = 3;
  optional int32 mapSource = 4;
  optional int32 logicMapSource = 5;
  optional int64 mapPoolId = 6;
  optional BattleSettlementExtraInfo settlementExtraInfo = 7;
}

message WithGeoRankSnapshotBrief {
  optional int32 rankId = 1;
  optional int32 global_rank_no = 2;
  repeated KeyValueInt32 geo_rank_no = 3;
}

message TopRankJointInfo {
  optional int32 rankId = 1;
  repeated int32 scores = 6;

  optional int32 globalRankNo = 2;
  optional int32 globalSize = 4;

  repeated KeyValueInt32 geoRankNo = 3;
  repeated KeyValueInt32 geoSize = 5;
}

message UgcShowDataSnapshot {
  optional int32 achievementFinishProgress = 1;  // 成就完成进度
}

// ugc多轮地图积分对局结算数据
message UgcMultiRoundScoreSettlementInfo {
  optional int32 curRoundIndex = 1; // 当前轮次
  repeated MemberMultiRoundScoreSettlementInfo memberScoreInfos = 2; // 成员结算信息列表
  optional bool isEnd = 3; // 是否结束了
  optional int32 minPlayerLimit = 4; // 进行下一轮需要的最少玩家数量
}

message MemberMultiRoundScoreSettlementInfo {
  optional int64 uid = 1; // 玩家uid
  optional int32 curScoreNum = 2; // 当前积分总数
  optional int32 scoreIncrement = 3; // 积分增量
  optional int32 curCoinNum = 4; // 当前金币总数
  optional int32 coinIncrement = 5; // 金币增量
}

enum FarmSceneDropPlaceType {
  FSDPT_Unknown = 0;
  FSDPT_Farm = 1;
  FSDPT_House = 2;
  FSDPT_Cook = 3;
}

enum FarmBuildingTypeWithRoom {
  FBTWR_House = 1;// 小屋
  FBTWR_Cook = 9;// 餐厅
}

enum FarmPetFavorReason {
  FPFR_Unknown = 0;
  FPFR_Interact = 1; // 互动
  FPFR_Feed = 2; // 喂食
  FPFR_Hungry = 3; // 饥饿
}

enum FarmWeatherReason {
  FWR_Unknown = 0;
  FWR_Event = 1; // 事件
  FWR_GM = 2; // GM
}

enum ItemInfoFeatureType {
  IIFT_None = 0;
  IIFT_RaffleCombo = 1;   // 抽奖暴击
  IIFT_RaffleSpecialOffer = 2; // 抽奖特殊奖励
}

message UgcMultiLevelScene {
  optional int32 sceneId = 1;  // 场景id
  optional int32 timeCost = 2;  // 游玩时间 秒
}

message UgcBattleMultiLevelData {
  repeated UgcMultiLevelScene sceneChange = 1;  //
  optional int32 lastSceneId = 2;  //
}

message DsOngoingAddrInfo {
  optional string dsAddr = 1;       // 进行中的对战的ds地址
  optional string desModInfo = 2;   // 进行中的对战的ds关卡序列
  optional string dsAuthToken = 3;   // dsToken认证
  optional proto_ChatGroupKey chatGroupKey = 4; // 局内聊天key
  optional proto_ChatGroupKey sideChatGroupKey = 5; // 阵营聊天key
  optional UgcBriefInfo ugcBriefInfo = 6; // ugc对局的附加简要信息--废弃
  optional int32 matchType = 7; // 玩法id
  optional proto_CompetitionBasicInfo competitionBasicInfo = 8; // 赛事信息
  optional int64 battleId = 9;
  optional int32 errorCode = 10;
  optional ModOnGoingData modOnGoingData = 11;
  optional int64 sceneId = 12;  // 场景id
}

/********************** 集卡系统 ***********************/

// 交换信息
message TradingCardTradeInfo {
  optional int64 id = 1;                                          // 交换id
  optional com.tencent.wea.xlsRes.CardTradeType tradeType = 2;    // 交易类型：索要/赠送/交换
  repeated int32 cardIdList = 3;                                  // 卡牌列表 交换请求第一个为发起方卡牌，第二个为交换方卡牌
  optional int64 expireTimeMs = 5;                                // 过期时间
  optional int64 completePlayerUid = 6;                           // 完成玩家uid
  optional int64 originPlayerUid = 8;                             // 发起玩家uid
  optional string originPlayerName = 9;                           // 发起玩家名字
  optional int32 collectionId = 10;                               // 卡集id
  optional int64 createTimeMs = 11;                               // 创建时间
  optional com.tencent.wea.xlsRes.CardTradeShareChannel shareChannel = 12;               // 分享渠道
  optional int64 finishTimeMs = 13;                               // 完成时间
  repeated int64 targetPlayerUidList = 14;                        // 目标玩家列表
  repeated int32 requireCardIdList = 15;                        // 需要的卡牌列表 仅交换协议中使用
}

message CardAddInteraction {
  repeated proto_TradingCardInfo cardList = 1;                        // 增加的卡牌
  optional com.tencent.wea.xlsRes.CardAddSourceType sourceType = 2;   // 增加来源
  optional int64 sourceId = 3;                                        // 增加来源id
  optional int64 fromPlayerUid = 4;                                   // 赠送的玩家uid
  optional string fromPlayerName = 5;                                 // 赠送的玩家名字
}

message CardDeductInteraction {
  repeated proto_TradingCardInfo cardList = 1;                          // 扣除卡牌，来源为赠送/索要/交换时可以不填
  optional com.tencent.wea.xlsRes.CardDeductSourceType sourceType = 2;  // 扣除来源
  optional int64 sourceId = 3;                                          // 扣除来源id
}

message TradingCardInteractionData {
  required int64 srcUid = 1;                      // 发送方uid
  required int32 collectionId = 2;                // 卡集id
  optional CardAddInteraction addCard = 3;        // 增加卡牌
  optional CardDeductInteraction deductCard = 4;  // 扣除卡牌
  required int64 sendTime = 5;                    // 发送时间
  optional int64 addTradeRecordId = 6;            // 增加交易记录
}

enum TradingCardRedDotType {
  TRDT_Unknown = 0;
  TRDT_NEW_CARD = 1; //获得新卡牌的提示红点  此时红点ID为卡牌ID
  TRDT_NEW_TRADING_REQUEST = 2; //索要交易的提示红点 红点ID为交易ID
  TRDT_NEW_TRADING_GIVE = 3; //赠送交易的提示红点 红点ID为交易ID
  TRDT_NEW_TRADING_EXCHANGE = 4; //交换交易的提示红点 红点ID为交易ID
  TRDT_NEW_EXCHANGE_REWARD = 5; //【额外兑奖】的提示红点
  TRDT_WILE_CARD = 6;   //【万能卡牌】的提示红点 万能卡的实例ID
  TRDT_CYCLE_CUP_REWARD = 7;  //循环奖杯的红点, 循环奖杯的ID
  TRDT_EXTRA            = 8; //【额外】的提示红点 ID1 为主界面那个
  TRDT_ACTIVITY         = 9;  // 卡牌活动
  TRDT_BUBBLE           = 10; // 气泡
  TRDT_EXCHANGE_MALL     = 11; // 兑换商城的红点 ID为卡集ID
  TRDT_CARD_CHANGE = 12; //市集变动
}

// 气泡类型
enum TradingCardBubbleId {
  TCBT_Unknown = 0;
  TCBT_Activity = 1;        // 卡牌活动
  TCBT_WildCard = 2;        // 万能卡
  TCBT_ExtraExchange = 3;   // 额外兑换
  TCBT_BOUNDS = 4;        // 增量活动
  TCBT_NewCardCollection = 5;//新卡集
}

message PlayerRaffleEvtData {
  optional int32 poolId = 1;
  optional int32 raffleId = 2;
  optional int32 categoryId = 3;
  optional int32 count = 4;
}

enum OmdLevelType {
  OMD_Unknown = 0;
  OMD_Simple = 1;     // 简单
  OMD_Challenges = 2; // 挑战
  OMD_Oldsix = 3;     // 老六
  OMD_Trouble = 4;    // 极难
  OMD_Smart = 5;      // 烧脑
  OMD_Abreact = 6;    // 爽图
}

message UgcPublishInputParam {
  repeated UgcBGMInfo bgmInfo = 1; // ugc背景音乐设置，最大5个，数组索引表示顺序
  optional bool isAllowAiTakeOver = 2;	// 是否允许ai接管(设置界面参数)
  optional UgcMapMidJoinSetting mapMidJoinSetting = 3;  // 中途加入的参数设置
}


message UgcBGMInfo {
  optional string musicId = 1;
  optional string musicName = 2;
  optional int32 from = 3;    //音乐上传来源 ESongFrom,用于标识是否是罐头音乐
}

message PlayerGameMatrixExtraData {
  optional string openId = 1; // 云游h5登录态
  optional string openKey = 2; // 云游h5登录态
  optional int32 cloudGamePlat = 3; // 0 苹果, 1 安卓, 2 PC
  optional string cloudGameSystem = 4;
  optional string cloudGameBand = 5;
  optional string cloudGameModel = 6;
}

// 玩家个性状态类型
enum PersonalityStateType {
  PST_Default = 0;        // 原有的 每个人都有的
  PST_ItemGain = 1;       // 按道具获取
}

enum  LobbyMiniGameDataType{
    LMGDT_TRANSFER_CAKE_01 = 24; //送蛋糕
    LMGDT_TRANSFER_CAKE_02 = 25; //送蛋糕
    LMGDT_TRANSFER_CAKE_03 = 26; //送蛋糕
}

enum PlayerActionSceneType{
  PAST_LOBBY = 1; //主广场
  PAST_SUB_LOBBY = 2; //分广场
  PAST_XIAOWO = 3; //家园
  PAST_FARM = 4; //农场

}
enum FarmVillagerFavorReason {
  FVFR_Unknown = 0;
  FVFR_Interact = 1; // 互动
  FVFR_PresentGift = 2; // 送礼
  FVFR_AcceptGift = 3;  // 收礼
  FVFR_GM = 4; // GM指令
  FVFR_IDIP = 5; // idip指令
}

enum FarmHouseFurnitureBuyResult {
  FHFBR_Unknown = 0;
  FHFBR_FarmGoldCoinBuyOK = 1; // 农场币购买家具成功
  FHFBR_FarmGoldCoinNotEnough = 2; // 农场币数量不足
  FHFBR_FarmGoldCoinFurnitureEmpty = 3; // 农场币家具为空
}

enum FarmPetObtainReason {
  FPOR_InitPet = 0; // 初始宠物获得
  FPOR_ActiveItem = 1; // 激活道具获得
  FPOR_TameWildCat = 2; // 驯化野猫
  FPOR_Gm = 3; // GM指令
}

enum FarmPetClothingObtainReason {
  FPCOR_InitClothing = 0; // 初始服装获得
  FPCOR_ActiveItem = 1; // 激活道具获得
  FPCOR_LevelUnlock = 2; // 等级解锁获得
  FPCOR_ObtainCat = 3; // 猫获得
}

enum FarmVillagerOnDemandGiftType {
  FVDGT_Unknown = 0;
  FVDGT_Fish_C = 1; // C鱼
  FVDGT_Fish_A = 2; // A鱼
  FVDGT_Fish_S = 3; // S鱼
  FVDGT_Farm_Exp = 4; // 农场经验
  FVDGT_Farm_Coin = 5; // 农场币
}

enum FarmGiftType {
  FGT_Default = 0; // 默认
  FGT_Event = 100; // 事件掉落
}

enum FarmVillagerInOutType {
  FVIO_In = 0; // 入住
  FVIO_OutActive = 1; // 主动离开
  FVIO_OutPassive = 2; // 被动请离
}

enum CookWorkerJob {
  CWJ_Unknown = 0;
  CWJ_Chef = 1;
  CWJ_Waiter = 2;
}

enum CookWorkerState {
  CWS_Rest = 0;
  CWS_Working = 1;
}

message CommentImage {
  optional string commentImageURL = 1;
  optional proto_CosImage image = 2;
}

message FarmCookComment {
  optional uint64 id = 1;  // commentId timestamp
  optional string content = 2; // 点评内容(废弃 客户端生成）
  optional float score = 3; // 评分
  optional int64 sendTime = 4; // 发送时间
  optional com.tencent.wea.xlsRes.FarmCookCustomerType customerType = 5; // 顾客类型
  optional string nickname = 6; // 顾客名称
  optional com.tencent.wea.xlsRes.VillagerGenderType gender = 7; // 顾客性别(废弃)
  optional CommentImage commentImage = 8; // 点评照片
  optional bool isChosen = 9; // 是否精选(废弃)
  optional int64 chosenTime = 10; // 精选时间(废弃)
  repeated FarmCookCommentReply replies = 11; // 回复
  optional int64 uid = 12; // 玩家id(废弃)
  optional int64 customerUid = 13; // 在线点评顾客的uid(废弃)
  optional string chefName = 14; // 厨师名
  optional string waiterName = 15; // 服务员名
  optional string foodName = 16; // 菜品名
  optional int32 lineId = 17; // 台词id
  optional int32 foodId = 18; // 菜品id
  optional int64 chefId = 19; // 厨师id
  optional int64 waiterId = 20; // 服务员id
}

// 农场餐厅点评回复
message FarmCookCommentReply {
  optional uint64 id = 1; // replyId
  optional string content = 2; // 回复内容
  optional int64 sendTime = 3; // 回复时间(毫秒)
  optional int64 uid = 4;// 玩家的id
  optional string nickname = 5; // 昵称
  optional string profile = 6; // 头像url(废弃)
  optional HeadFrame headFrame = 7; // 头像框(废弃)
  optional int32 avatarId = 8; // 系统顾客的avatarId
  optional uint64 commentId = 9; // 回复所在的点评id
  optional int32 lineId = 10; // 系统顾客回复的台词id
  optional int32 replyIndex = 11; // 系统顾客回复的回复索引
}

enum FarmCookCommentType {
  FCCT_Unknown = 0;
  FCCT_Comment = 1; // 普通点评
  FCCT_CommentChosen = 2; // 精选点评
  FCCT_CommentReply = 3; // 回复
}
message FarmVillagerOnDemandGiftInfo {
  optional int32 itemId = 1;
  optional int64 itemNum = 2;
  optional int64 price = 3;
  optional int32 type = 4; // 参考FarmVillagerOnDemandGiftType
}

enum FarmVileExpReason {
  FVER_Unknown = 0;
  FVER_Eat = 1;
  FVER_Gm = 2;
}

enum UgcInstanceSource {
  UgcInstance_Unknown = 0;
  UgcInstance_App = 1;     //独立app
  UgcInstance_Game = 2;    //端内
  UgcInstance_Mapping = 3; //导入
}

message TravelingDogInfo {
  optional int32 id = 1;              //阶段id
  optional string name = 2;           //阶段名称
  optional int64 startTime = 3;      //开始时间
  optional int64 endTime = 4;        //结束时间
}

message TravelingDogStageData {
  repeated TravelingDogInfo stage = 1;
  optional int32 curStage = 2; //当前阶段
  map<int32,string> dateInfo = 3;
  optional int32 curStatus = 4; //当前状态
  optional int64 allCoinNum = 5; //一共获取的玫瑰数量
  optional int64 goHomeTime = 6; //狗狗回归时间
}

message TravelingDogRewardData {
  optional string name = 1;
  repeated ItemInfo item = 2;
}

message TravelingDogDogTravelRspInfo {
  optional int64 goHomeTime = 1; //回归时间戳
  repeated ItemInfo item = 2; //出行奖励
  optional int32 curStatus = 3; //当前状态
  optional int32 travelDate = 4; //出行日期
}

message TravelingDogDogGoHomeRspInfo {
  optional string name = 1; //画片奖励
  optional int32 curStatus = 2; //当前状态
  optional int32 travelDate = 3; //出行日期
}

enum TravelingDogStage {
  TravelingDog_Unknown = 0;
  TravelingDog_Preparation = 1;     //准备期(1期功能 2期已废弃)
  TravelingDog_TravelPeriod = 2;    //旅行期
  TravelingDog_Trimming = 3;        //休整期
}

message TreasureLevelUpTakeBoxRewardRspInfo {
  repeated ItemInfo boxReward = 1;  //宝箱奖励
  optional int32 boxStatus = 2;     //宝箱状态 0未领取 1已领取
}

message SummerNavigationBarRewardRspInfo {
  repeated ItemInfo reward = 1;     //奖励
  optional int32 status = 2;        //吧唧奖励状态 0未完成 1未领取 2已领取
}

enum UgcTableBatchGetSource {
  UTBGS_Unknow = 0;
  UTBGS_UgcPlayTable_UgcFriend = 10001;   // UGC局内好友
  UTBGS_UgcPlayTable_HomePage = 10002;  // 首页推荐
  UTBGS_UgcPlayTable_AllTimeHotNew = 10003; // 新版名图堂
  UTBGS_UgcPlayTable_CollectMapSort = 10004; // 地图收藏根据游玩时间排序
  UTBGS_GetUgcDailyStageInfo = 10005;
  UTBGS_UgcBatchPublishMap = 10006;
  UTBGS_GetHotPlaying = 10007;
  UTBGS_GetThemeRecommend = 10008;
  UTBGS_ClubShareMap = 10009;
  UTBGS_UgcCoPlayRecommendMap = 10010;
  UTBGS_AppUgcGroupBatchGetPublish = 10011;
  UTBGS_AppSingleStageStart = 10012; // 独立APP单人游玩开局
  UTBGS_GetHotRecommend = 10013;
  UTBGS_FillHomePageRecommend = 10014;
  UTBGS_GetRecommendSet = 10015;
  UTBGS_UgcGroupBatchGetPublish = 10016;
  UTBGS_GetOMDRecommendSet = 10017; // 兽人推荐 会拉去是否游玩字段
  UTBGS_PlayerGetPlayedMaps = 10018;
  UTBGS_IdipGetPlayedMaps = 10019;
  UTBGS_PlayerGetCollectMaps = 10020;
  UTBGS_OMDPlayedMapGetPublishMaps = 10021;  // 兽人最近游玩获取当前游玩地图信息
  UTBGS_GetCreatorHomePage = 10022;
  UTBGS_GetRecommendThemeDetail = 10023;
  UTBGS_GetRecommendHotDetail = 10024;
  UTBGS_GetBattleSettlementRecommend = 10025;
  UTBGS_PlayedMapSort = 10026;
  UTBGS_UgcRoomPlayerRecommendMap = 10027; //UGC房间成员推荐地图
  UTBGS_SendPersonalMail = 10029;                     // 个人-idip邮件
  UTBGS_SendPersonalMailWithAttachExpire = 10030;     // 个人-idip邮件
  UTBGS_SendPersonalMailWithAmsAttachExpire = 10031;  // 个人-idip邮件
  UTBGS_SendPersonalTextMail = 10032;                 // 个人-idip邮件
  UTBGS_SendGlobalMail = 10033;                       // 全服-idip邮件
  UTBGS_SendGlobalMailWithAttachExpire = 10034;       // 全服-idip邮件
  UTBGS_SendGlobalMailWithAmsAttachExpire = 10048;    // 全服-idip邮件
  UTBGS_SendPersonalTemplateMail = 10049;             // 个人-模板邮件
  UTBGS_SendGlobalTemplateMail = 10050;               // 全服-模板邮件
  UTBGS_DisplayBoardSave = 10051;                     // 推图展板保存
  UTBGS_DisplayBoardHistory = 10052;                  // 推图展板地图历史
}

enum FarmGiftCritical {
  FGC_Normal = 0;
  FGC_Little = 1;
  FGC_Big = 2;
}

message UgcAppUgcUpdatePlayer {
  optional int64 creator_id = 1;  // 作者。必填：是
  optional string name = 2;  // 昵称，必填：否
  optional string avatar = 3;  // 头像，必填：否
  optional int32 status = 4;  // 状态，0：离线，1：在线，必填：是
}

message UgcAppUgcJoinCocreate {
  optional int64 creator_id = 1;  // 作者
  optional int64 ugc_id = 2;  // 地图id
  optional int64 join_creator_id = 3;  // 加入作者id
  optional int64 join_game_creator_id = 4;  // 加入的作者的端内账号id
}

message RaffleRewardData {
  repeated KeyValueInt32 rewardTotalCounts = 1;
  repeated KeyValueInt32 rewardIncCounts = 2;
}

message RaffleEventData {
  optional int32 times = 2;
  optional int32 points = 3;
  repeated RafflePoolEventData pools = 4;
}

message RafflePoolEventData {
  optional int32 poolId = 1;
  optional int32 times = 2;
  repeated KeyValueInt32 rewardCounts = 3;
  repeated KeyValueInt32 rewardGroupCounts = 4;
  optional int32 grandCounts = 5;
}

message UgcMapEvaluationInfo {
  optional int64 map_id = 1;
  optional string map_name = 2;
  optional int64 map_update = 3;
  optional int32 map_type = 4;  // 地图类型
}

message ESBaseInfo {
  optional string moduleId = 1;
  optional string jsonSet = 2;
}

message ESOptResponse {
  optional bool ok = 1;
  optional int32 status = 2;
  optional string responseBody = 3;
}

message ESDocUgcMapEvaluationInfo {
  optional UgcMapEvaluationInfo doc = 1;
}


message FarmFoodFestivalReqInfo {
}

message FarmFoodFestivalRspInfo {
  repeated FoodElvesInfo elves = 1; //精灵信息
  optional int32 allCoinNum = 2;  //总收益金币
}

message FarmFoodFestivalReqSendGift {
  optional int32 id = 1;         //精灵id
  repeated FoodSendItem items = 2;
}

message FarmFoodFestivalRspSendGift {
  optional int32 result = 1;
}

message FarmFoodFestivalReqReceiveCoin {
  optional int32 id = 1;         //精灵id
  optional int32 good = 2;        //好感度
}

message FarmFoodFestivalRspReceiveCoin {
  optional int32 result = 1;
  optional int32 curCoinNum = 2;  //当前收益金币(需求修改 废弃)
  optional int32 allCoinNum = 3;  //总收益金币(需求修改 废弃)
  repeated ItemInfo item = 4;    //领取好感度奖励
}

message FarmFoodFestivalReqMadeItem {
  optional int32 id = 1;       //合成id
  optional int32 count = 2;    //合成次数
}

message FarmFoodFestivalRspMadeItem {
  optional int32 result = 1;
  optional FoodElvesInfo item = 2;
}


message FoodElvesInfo{
  optional int32 id = 1;         //精灵id
  optional string elvesName = 2;  //名字
  optional int32 curGood = 3;     //好感
  optional int32 goodLimit = 4;   //好感上限
  map<int32, int32> status = 5;  //好感度领取

}

message FoodSendItem {
  optional int32 itemId = 1; // 赠送的道具Id
  optional int32 cnt = 2; //  赠送的道具数量
}
enum FarmSpecialFishType {
  FSFT_Normal = 0;   // 常规鱼
  FSFT_Talent = 1;   // 神农宝典鱼
  FSFT_Activity = 2; // 活动鱼
}

enum HotSpringGrade {
  HSG_Normal = 0;
  HSG_Gold = 1;
  HSG_Color = 2;
}

enum VisitorListTagType {
  VLTT_Default = 0;
  VLTT_NotShow = 1; // 不展示
}
message CocSoldier {
  optional int32 id = 1;      // 士兵id
  optional int32 level = 2;   // 士兵等级
  optional int32 num = 3;     // 士兵数量
}

message UgcMapExtraConfigWrapper {
  oneof cfg {
    UgcMapExtraConfigDescription description = 1;  // 描述配置
  }
}

message UgcMapExtraConfigDescriptionItem {
  optional int32 index = 1;  // Item的序号
  optional int32 type = 2;   // Item的类型，1:居左标题 | 2:居中标题 | 3:居左正文 | 4:居中正文 | 5:图片
  optional string content = 3;  // Item的内容，当为图片时，记录图片的coverId
}

message UgcMapExtraConfigDescription {
  optional string text = 1;  // 描述文本
  repeated MapCoverInfo picture = 2;  // 描述插图，用于记录图片的详细信息，用于下载
  repeated UgcMapExtraConfigDescriptionItem items = 3;  // 每个Item的内容
}

message MapDescPicture {
  optional int32 index = 1;  // 图片索引
  optional UgcMapMetaInfo info = 2;  // cos信息
}

message UgcMapExtraConfigIndex {
  optional int32 cfgType = 1;  // 数据类型 UgcMapExtraConfigType
  optional int64 uniqueId = 2;  // 唯一id
  optional int32 cfgId = 3;  // 配置id
  optional int32 verId = 4;  // 生成的版本id
  optional int32 saveType = 5;  // 0: 默认, 1: 已发布
}

message UgcMapExtraConfigIndexType {
  map<int32, UgcMapExtraConfigIndex> cfgIdMap = 1;
  map<int32, bool> cfgIdBanMap = 2;  // 被封掉的id
}

message UgcMapExtraConfigIndexMap {
  map<int32, UgcMapExtraConfigIndexType> indexTypeMap = 1;
}

enum FoodFestivalAttrType{
    FOOD_Festival_UNKNOW = 0; //未知
    FOOD_Festival_CROPS = 1;  //农作属性
    FOOD_Festival_ANIMAL= 2;  //动物属性
    FOOD_Festival_FISH = 3;   //鱼类属性
}

enum FarmBuildingTypeWithFurniture {
  FBTWF_General = 0; // 通用
  FBTWF_House = 1; // 小屋
  FBTWF_Cook = 9; // 餐厅
  FBTWF_VillageHouse = 32; // 村民小屋
}

enum FarmHotSpringAchievement {
  FHSA_Buff = 0; // 获取buff
}

enum FarmHouseItemInteractType {
  FHIIT_House = 1; // 小屋
  FHIIT_Cook = 9; // 餐厅
  FHIIT_VillageHouseMin = 31;
  FHIIT_VillageHouseOne = 32; // 一号村民小屋
  FHIIT_VillageHouseTwo = 33; // 二号村民小屋
  FHIIT_VillageHouseThree = 34; // 三号村民小屋
  FHIIT_VillageHouseFour = 35; // 四号村民小屋
  FHIIT_VillageHouseFive = 36; // 五号村民小屋
  FHIIT_VillageHouseMax = 37;
}

enum FarmWaitingVillagerDisappearReason {
  FWVD_Default = 0;
  FWVD_Summon = 1; // 邀请入驻
  FWVD_DailyRefresh = 2; // 每日零点刷新
  FWVD_GM = 3; // GM操作
}


message SeasonData{
  optional SeasonReviewBasicData  seasonReviewBasicData = 1;
  optional proto_SeasonReview eventData = 2;
  optional SeasonTLogData seasonTLogData = 3;
}



message SeasonReviewBasicData {
  optional SeasonBrochure seasonBrochure = 1;
  optional SeasonFashionBrochure  seasonFashionBrochure = 2;
  optional  SeasonIntimacy seasonIntimacy =3;
}


// 原有   (注释原有)
// 赛季数据
message SeasonBrochure {
  // 1.夺冠次数
  optional int64  numberOfChampionships = 1;
  //2.游戏场数
  optional int64 numberOfGames = 2;
  //3.最强模式(所有玩法中段位最高的玩法)
  optional int64 mostPowerfulMode = 3;
  //4.最爱模式(玩家游玩场次最多的模式)
  optional int64 favoriteMode = 4;
  //5.当赛季组队次数
  optional int64 sumTeamedTimes = 5;
  //6.当赛季组队赢的次数
  optional int64 teamedWinTimes = 6;
  //7.总奖杯数
  optional int64 totalTrophies = 7;
  //8.终局次数
  optional int64 numberOfEndgames = 8;
  //9.个人信息点赞次数
  optional int64 numberOfPersonLike = 9;
  //10.局内点赞次数
  optional int64 numberOfInnerLike = 10;
  //11.当赛季游玩过的模式数量
  optional int64 numberOfModelPlay = 11;
}

// 原有
//时尚手册
message SeasonFashionBrochure {
  //1.赛季时尚分
  optional int32 fashionPoints = 1;
  //2.累计时尚分
  optional int32 sumFashionPoints = 2;
  //3.获得装扮数(时装数)
  optional int32 numberOfDressUpsAcquired = 3;
  //4.赛季祈愿次数
  optional int32 numberOfPrayers = 4;
  //5.最炫穿搭 :赛季时尚分最高的时装
  optional SeasonFashionEquipBookList seasonFashionEquipBook = 5;
  //6.赛季时尚分最高手持物
  optional int32 highestHandheldObject = 6;
  //7.赛季时尚分最高载具
  optional int32 topScoringVehicle = 7;
  //8.赛季时尚分最高星梭
  optional int32 highestScoringStaShuttle = 8;
}

message SeasonFashionEquipBookList {
  optional     com.tencent.wea.xlsRes.ItemType itemType =  1;         //装扮类型
  repeated     int32    itemIds   = 2  ;                        //获得的装扮道具ID列表
}

message SeasonIntimacy {
  optional int64 maxIntimacyFriend = 1;        //当赛季亲密度最高好友
}

//社交活跃  事件统计
message   SeasonSociallyActive {
  optional int32  numberOfFriends = 1;               // 当赛季添加的新好友数量
  optional int32  numberOfAddIntimacy = 2;           // 当赛季新增亲密关系数量
}

//星家园 事件统计
message SeasonStarHome {
  optional int32 numberOfVisited = 1;                //  当赛季星家园访问数
  optional int32 numberOfLiked = 2;                  //  当赛季星家园点赞数
  optional int32 numberOfParty = 3;                  //  当赛季星家园举办派对数
}


message SeasonTLogData {
  optional SeasonFarmyard seasonFarmyard = 1;
  optional SeasonStarWorld seasonStarWorld = 2;
  optional SeasonOtherData seasonOtherData = 3;
}



// TLog
//农场家园
message SeasonFarmyard {
  //1.农场等级
  optional int64 farmLevel = 1;
  //2.最大农场币记录
  optional int64 largestFarmCoinRecord  = 2;
  //3.当赛季访问好友农场次数
  optional int64 visitFriendFarm = 3;
  //4.当赛季在我的农场收获次数最多的作物
  optional int64 obtainMaxCrop = 4;
  //5.当赛季消耗的农场币数量
  optional int64  usedFarmCoins = 5;
  //6.当赛季获得的农场币数量
  optional int64  obtainedFarmCoins = 6;
  //7.当赛季农场进行收获的次数
  optional int64  obtainedTimes = 7;
  //8.当赛季农场收获时的丰收次数
  optional int64 harvests = 8;
  //9.当赛季农场收获时的大丰收次数
  optional int64 bigHarvests = 9;
  //10.当赛季玩家进行偷菜的次数
  optional int64  stolenVegetables = 10;
  //11.活跃天数
  optional int64 liveDays = 11;
}



// TLog
//星世界
message SeasonStarWorld {
  //1.造梦师等级
  optional int64 dreamMakerRank = 1;
  //2.星世界奖杯
  optional int64 starWorldTrophy = 2;
  //3.当赛季银奖杯数量
  optional int64 silverTrophies = 3;
  //4.当赛季铜奖杯数量
  optional int64 bronzeTrophies = 4;
  //5.星世界订阅数
  optional int64 subscriptions = 5;
  //6.星世界粉丝数
  optional int64 starWorldFans = 6;
  //7.星世界点赞数
  optional int64 starWorldLikes = 7;
  //8.赛季闯关挑战积分
  optional int64 challengePoints = 8;
  //9.当赛季我游玩的地图总数量
  optional int64 sumToursMap = 9;
  //10.当赛季我发布的地图总数量
  optional int64 sumReleasesMap = 10;
  //11.当赛季我通关的地图总数量
  optional int64 sumPasses = 11;
  //12.当赛季我发布的地图中玩家(游玩次数+点赞次数)最多的地图
  optional int64 visitSelfMap = 12;
  //13.当赛季所有地图中玩家(游玩次数+点赞)次数最多的地图
  optional int64 timesAndLikeMost = 13;
  //14.当赛季我游玩数量最多的地图
  optional int64 selfTimeMap = 14;
}

message SeasonOtherData {
  optional int64 visitStarHomeMostFriendUid  = 1;      // 当赛季访问星家园次数最多的好友
  optional int64 socialGiftSendCnt = 2;                // 当赛季玩家赠礼次数
  optional int64  teamMostFriendUid = 3;               // 当赛季组队最多玩家
}

message TopRankSnapshotInfo {
  optional RankId rankId = 1;
  optional int32 seasonId = 2;

  optional TopRankInfo self = 3;    // 玩家排名
  optional TopRankInfo entry = 4;   // 准入门槛
}


// 分享礼包信息
message ShareGiftBaseInfo {
  optional int64 id = 1;                                          // 分享礼包唯一id
  optional int32 type = 2;                                        // 分享礼包类别
  optional int64 playerId = 3;                                    // 发起玩家uid
  optional int32 itemId = 4;                                      // 对应的道具id
  optional int64 createTimeMs = 5;                                // 创建时间
  optional int64 expireTimeMs = 6;                                // 结束时间
  optional int64 finishTimeMs = 7;                                // 抢完时间
  optional int32 rewardLimit = 8;                                 // 领取上限
  repeated ShareGiftRewardPlayerInfo  shareGiftRewardPlayerInfo = 9;                          // 抢到的玩家信息
  optional int64 dbExpireTimeMs = 10;                                // db删除时间
}

message ShareGiftRewardPlayerInfo{
  optional int64 uid = 1;                                          // playerId
  optional ItemArray rewardItem = 2;                            //获取奖励道具
  optional int64 rewardTime = 3;                                //获取时间
}

enum GiftShareStatusType {// 礼包结构化消息状态
    GiftCantReward = 1; // 不能领取
    GiftExpired = 2; // 已过期
    GiftPlayerRewardLimit = 3; // 达到玩家领取上限
    GiftRewardCountDry = 4; // 已领完
    GiftRewarded = 5; // 已领取
    GiftCanReward = 6; // 可领取
}
message ActivityMobaRandomVoteReqInfo {
}

message ActivityMobaRandomVoteResInfo {
  optional int32 activityEventStatus = 1;//当前活动状态(投票期or展示期) enum ActivityMobaRandomVoteEventStatus
  optional int32 todayVoteEventId = 2;//玩家今日投票事件Id
  repeated  ActivityMobaRandomVoteEvent activityMobaRandomVoteEvent = 3;//所有阵营票数
  optional int32  totalVoteNum = 4;
}

message WolfReturnReceiveInvitationReqInfo{
  optional int64 sendUid=1;//邀请玩家id
  optional int32 receiveChannel=2; //接收平台,0-游戏内,1-QQ分享,2微信分享
}
message WolfReturnReceiveInvitationRspInfo{
  optional int32 res=1;//绑定结果
}


message WolfReturnRewardReqInfo{
  optional int32 id = 1;//回归奖励表id
}

message WolfReturnRewardRsqInfo{
}


message WolfReturnFriendListReqInfo{
}
message WolfReturnFriendListRsqInfo{
  repeated  WolfReturnRewardFriendMessage friends = 1;//好友列表
}

message WolfReturnGetStateReqInfo{
  optional int64 sendUid=1;//邀请玩家id
}
message WolfReturnGetStateRsqInfo{
  optional int32 activityState = 1;//0活动关闭 1活动开启  2正常活动开启
  optional int32 returnState = 2;//0不是回归用户 1已经接收了邀请  2可以接受邀请
}



message WolfReturnRewardFriendMessage{
  optional int64 uid = 1;//uid
  optional string openId = 2;//openId
  optional int32 friendType = 3;//0表示平台好友，1表示游戏好友,2同时是游戏和平台好友
  optional int32 user_type = 4;//0表示新玩家，1表示回流玩家
}



message ActivityMobaRandomVoteEvent {
  optional int32 eventId = 1; //投票阵营ID
  optional int64 totalVoteNum = 2; //该阵营总投票票数
}

enum ActivityMobaRandomVoteEventStatus {
  PeriodVote = 1; //投票期
  PeriodShow = 2; //展示期
}

message ActivityMobaRandomPlayerReqVote {
  optional int32 eventId = 1; //投票阵营ID
}

message ActivityMobaRandomPlayerResVote {
  optional int32  todayVoteNum = 1;
  optional int32  totalVoteNum = 2;
}

enum RecentPlayRecordType {
  RPRT_MatchType = 1;   // 玩法id
  RPRT_UgcSingleMap = 2;  // ugc单图
  RPRT_UgcMapPool = 3;    // 多图匹配
}

message RecentPlayRecordData {
  optional int32 type = 1;    // RecentPlayRecordType
  optional int32 matchType = 2; // 玩法id
  optional int64 mapId = 3;  // 地图id，单图为ugcId，多图为mapPoolId
  optional MapDetails mapDetails = 4;  // ugc单图
  optional UgcMatchLobbyMapBrief matchLobbyMapBrief = 5;  // 多图匹配
  optional int64 playTime = 6;
}

message SpecificUserTopRankInfoList {
  optional int32 rankId = 1;
  repeated SpecificUserTopRankInfo infos = 2;
}

// 发布态草稿共创地图操作类型
enum PublishCoCreateMapOperateType {
  PUBLISH_COCREATE_OPERATE_TYPE_ACTIVATE = 1;	// 激活权限(主创者给联创者激活权限)
  PUBLISH_COCREATE_OPERATE_TYPE_RECYCLE = 2;	// 回收权限(主创者回收联创者权限)
  PUBLISH_COCREATE_OPERATE_TYPE_QUIT = 3;		// 退出权限(联创者退出权限)
}

enum FarmActivityTimeEnum {
  FAT_Unknown = 0;
  FAT_Fish_Activity_1 = 1; // 钓鱼春节锦鲤活动
  FAT_RedPacket = 2;       // 红包活动
  FAT_Party = 3;           // 农场派对
}

enum FarmBuildingType {
  FBT_AnimalShop = 4; // 动物小铺
  FBT_Cook = 9; // 餐厅
}

enum FarmBuildingSellSubReason {
  FBSSR_Normal = 0;   // 正常售卖
  FBSSR_External = 1; // 外部小程序售卖
}

// Coc玩家基本信息，用于从gameSvr同步到cocSvr
message CocPlayerPublicGameInfo {
  optional int32 platId = 1;
  optional string openId = 2;
  optional int32 level = 3;
  optional com.tencent.wea.xlsRes.TconndApiAccount accountType = 4;
  optional int64 clientVersion64 = 5;
  optional string secReportData = 6;
  optional int32 arenaId = 7;
  optional bool isNPC = 8;
  optional string telecomOper = 9;
  optional string network = 10;
  optional string clientIP = 11;
  optional int32 clientPlat = 12;
  optional int32 seasonId = 13;
  optional string country = 14;
  optional string province = 15;
  optional string city = 16;
  optional string district = 17;
  optional com.tencent.wea.xlsRes.DeviceLevel deviceLevel = 18;
  optional string userAgent = 19;
  optional string language = 20;
  optional int64 shortUID = 21;
  optional bool isSimulate = 22;
  optional string jQCallInfo = 23;
  optional int32 isVA = 24;
  optional int32 channel = 25;
  optional int32 loginPlat = 26;
  optional string clientVersion = 27;
  optional string nickName = 28;
  repeated int32 completedGuideTask = 29; // 已完成的引导任务id列表
  optional string deeplink = 30;//用户启动微信小游戏的链路统计
}

// 农场派对发布的场景
enum FarmPartyScene {
  FPS_Farm = 0; // 农场
  FPS_House = 1; // 小屋
  FPS_Cook = 9; // 餐厅
  FPS_VillageHouseOne = 32; // 一号村民小屋
  FPS_VillageHouseTwo = 33; // 二号村民小屋
  FPS_VillageHouseThree = 34; // 三号村民小屋
  FPS_VillageHouseFour = 35; // 四号村民小屋
  FPS_VillageHouseFive = 36; // 五号村民小屋
}

// 农场派对结束原因
enum FarmPartyCloseReason {
  FPCR_Active = 1; // 主动结束
  FPCR_CountDown = 2; // 倒计时结束
  FPCR_Republish = 3; // 重新发布
  FPCR_ActivityClose = 4; // 活动结束
  FPCR_IDIPBan = 5; // 封禁
}

// 农场派对离开原因
enum FarmPartyLeaveReason {
  FPLR_PartyCloseActive = 101; // 派对主动结束
  FPLR_PartyCloseCountDown = 102; // 派对倒计时结束
  FPLR_PartyCloseRepublish = 103; // 派对重新发布结束
}

// 农场派对状态
enum FarmPartyStatus {
  FPS_HostInParty = 1; // 派对中(主人在)
  FPS_HostNotInParty = 2; // 暂离(主人不在)
  FPS_NotOpen = 3; // 未开始
}

// tlog的派对场景id
enum FarmPartySceneForTlog {
  FPSFT_Farm = 1; // 农场
  FPSFT_House = 2; // 小屋
  FPSFT_VillageHouse = 3; // 村民小屋
  FPSFT_Cook = 4; // 餐厅
}

// 农场派对发布tlog所需上下文
message FarmPartyReleaseTlogCtx {
  optional int32 isRepublish = 1; // 是否重新发布
  optional int32 partyScene = 2; // tlog派对场景
  optional int64 sceneId = 3; // tlog派对场景id
  optional int32 villagerId = 4; // 村民id
}

// 农场派对结束tlog所需上下文
message FarmPartyOverTlogCtx {
  optional string partyDesc = 1; // 派对描述
  optional int32 partyScene = 2; // 派对场景
  optional int64 sceneID = 3; // 派对场景ID
  optional int32 villagerID = 4; // 村民ID
  optional int64 releaseTime = 5; // 发布时间
  optional int32 partyUsers = 6; // 累计参与人数
  optional int32 partyPCU = 7; // 当场派对峰值人数
  optional int64 hotValue = 8; // 热度最大值
  optional int32 buildingId = 9; // 建筑id 非tlog派对场景id
}

// 农场派对参与tlog上下文
message FarmPartyEnterTlogCtx {
  optional int32 partyScene = 1; // tlog派对场景 1农场 2小屋 3村民小屋 4餐厅
  optional int64 sceneID = 2; // tlog派对场景ID 农场和餐厅记录uid 小屋和村民小屋记录buildingId
  optional int32 villagerID = 3; // 村民ID 如果是村民小屋则记录村民id
  optional string releaseTime = 4; // 发布时间
  optional int32 partyStatus = 5; // 派对状态 1派对中(主人在) 2暂离(主人不在)
}

// 农场派对离开tlog上下文
message FarmPartyLeaveTlogCtx {
  optional int32 source = 1; // 进入方式
  optional int32 partyScene = 2; // tlog派对场景 1农场 2小屋 3村民小屋 4餐厅
  optional int64 sceneID = 3; // tlog派对场景ID 农场和餐厅记录uid 小屋和村民小屋记录buildingId
  optional int32 villagerID = 4; // 村民ID 如果是村民小屋则记录村民id
  optional string releaseTime = 5; // 发布时间
  optional int32 partyStatus = 6; // 派对状态 1派对中(主人在) 2暂离(主人不在)
  optional int64 partyTime = 7; // 派对参与时长(毫秒)
}

enum FarmPartyEnterSource {
  FPES_DirectEnter = 22; // 派对现场直接进入
}

// 子玩法事件参数
message SubGameplayEventParam {
  oneof Data {
    int64 i64 = 1;     // i64 i32 bool
    uint64 u64 = 2;    // u64 u32
    string str = 3;
    double f64 = 4;    // f32 f64
    bytes byt = 5;
  }
}


message UgcCreatorBadgeInfo {
  map<int32, UgcCreatorBadgeDetailInfo> badgeInfos = 1;
}

message UgcCreatorBadgeDetailInfo {
  optional int32 badgeId = 1;
  optional int64 addTime = 2;
}

enum UgcCreatorHomePageOpType {
  UCHPOT_Unknown = 0;
  UCHPOT_Message = 1; // 留言
  UCHPOT_Tag = 2; // 擅长领域
  UCHPOT_PublishMap = 3; // 地图作品
  UCHPOT_Badge = 4; // 成就奖章
}

message UgcCreatorHomePageInfo {
  optional string creatorMessage = 1;
  repeated int32 tags = 2;
  repeated int64 publishMaps = 3;
  repeated UgcCreatorBadgeDetailInfo badges = 4;
  optional int32 useDefaultBadge = 5; // 0使用默认 1不使用
}

enum AnalyzeModule {
  PLAYER_RETURN = 1;
}

message PlayerInfoAnalyze{
  optional int64 uid = 1;
  optional int32 returnActiveLevel = 2;
  optional int32 active_level = 3;
}

enum UgcUploadFileType {
  UUFT_UNKNOWN = 0;
  UUFT_VideoCover = 1;  // 视频封面
}

// ugc ai接管用户信息
message UgcAiTakeOverPlayerInfo {
  optional int64 uid = 1;                   // 用户uid
  optional int64 time = 2;                  // 接管时间
}

// 对局ai接管用户信息
message BattleAiTakeOverPlayerInfo {
  optional int64 uid = 1;                   // 用户uid
  optional int64 time = 2;                  // 接管时间
}

// ai机器人信息
message AiBotInfo {
  optional int64 uid = 1;                   // 机器人uid
  optional int32 camp_id = 2;               // 机器人阵营id
  optional int32 ai_level = 3;              // 机器人难度等级
  optional int64 ai_degree = 4;             // 机器人段位等级 大段位*1000000+小段位*1000
}

enum GameModeReturnState{
  GMR_UNKNOWN = 0;
  GMR_Trigger = 1;
  GMR_Started = 2;
  GMR_Finish  = 3;
}

enum FarmCookVisitantSource {
  FCVS_Unknown = 0;
  FCVS_Prebook = 1; // 预约
  FCVS_Steal = 2;   // 偷取
}

// 农场餐厅点赞来源
enum FarmCookLikeSource {
  FCLS_Unknown = 0;
  FCLS_Online_Settlement = 1; // 在线结算
  FCLS_Offline_Settlement = 2; // 离线结算
  FCLS_GM = 3; // GM
  FCLS_Visitant_Settle = 4; // 贵宾结算
  FCLS_Form_FarmItem = 5; // 道具 或许是来着idip？
  FCLS_Cook_LevelUp = 6; // 餐厅升级
}

// 农场贵宾状态
enum FarmCookVisitantStateEnum {
  FCVSE_Waiting = 0; // 等待中
   FCVSE_Serving = 1; // 接待中
}

/*
农场贵宾变化原因
                        +--------------------------------------------------------------+
                        |                                                              |
                        |           +---------------+                                  |
                        |           |  SettleFail   | --------------------------+      |
                        |           +---------------+                           |      |
                        |             ^                                         |      |
                        |             |                                         |      |
                        v             |                                         v      |
+---------------+     +-------+     +---------------+     +-------------+     +-----+  |
| PrebookArrive | --> | Wait  | --> |     Serve     | --> | ServeExpire | --> | End |  |
+---------------+     +-------+     +---------------+     +-------------+     +-----+  |
                        |             |                                         ^      |
                        |             +----------------+                        |      |
                        v                              |                        |      |
                      +-------+     +---------------+  |                        |      |
                      | Steal | --> |  StealArrive  | -+------------------------+------+
                      +-------+     +---------------+  |                        |
                                                       |                        |
                                      +----------------+                        |
                                      v                                         |
                                    +---------------+                           |
                                    | SettleSuccess | --------------------------+
                                    +---------------+
*/
enum CookVisitantChangeReason {
  CVCR_Unknown = 0;
  CVCR_PrebookArrive = 1;  // (+)预约到达
  CVCR_StealArrive = 2;    // (+)偷取到达
  CVCR_Serve = 3;          // (=)邀请
  CVCR_SettleSuccess = 4;  // (-)结算成功(客户端决定)
  CVCR_SettleFail = 5;     // (-)结算失败(客户端决定)
  CVCR_Steal = 6;          // (-)被偷
  CVCR_ServeExpire = 7;    // (-)邀请后过期
  CVCR_GM = 8;             // (+/-)GM指令
}

enum CookPrebookChangeReason {
  CPCR_Unknown = 0;
  CPCR_Prebook = 1;       // 预约
  CPCR_Cancel = 2;        // 取消
  CPCR_Finish = 3;        // 完成
  CPCR_Arrive = 4;        // 到达
  CPCR_MagicSpeedUp = 5;  // 仙术时光跳跃
  CPCR_MagicDelay = 6;    // 仙术缓时咒
}

enum FarmCookEmployeeState {
  FCES_Rest = 0;
  FCES_Working = 1;
}

enum FarmBuildingTypeWithFurnitureReplace {
  FBTWFR_House = 1; // 小屋
  FBTWFR_Cook = 9; // 餐厅
}

enum FarmFurnitureSkinType {
  FFSST_Furniture = 0; // 家具皮肤
  FFSST_SimpleSkin = 1; // 纯皮肤
}

message RaffleCommonNtfInfo {

}

message RaffleCommonHint {
  optional int32 poolId = 1;
  optional int32 type = 2;

  optional string text = 3;                       // 提示文字
  optional int32 current_progress = 4;            // 当前进度
  optional int32 target_progress = 5;             // 目标进度
  optional int32 original_target_progress = 6;    // 原先目标进度
}

message RaffleCommonPurchase {
  optional int32 raffleId = 1;
  //
  //  optional int32 originalPrice = 2;
  //  optional int32 price = 3;
  //  optional string text = 4;
  //
  repeated RaffleCommonPurchaseItem items = 5;
  //
  // extra param for extension
  repeated KeyValueInfo extraParams = 11;
}

message RaffleCommonPurchaseItem {
  repeated KeyValueInt32 entrance = 1;    // poolId, drawCount
  repeated KeyValueInt64 costs = 2;
}

message RaffleCommonPurchaseReceipt {
  optional int32 raffleId = 1;

  repeated RaffleCommonPurchaseItem desiredItems = 2;
  repeated RaffleCommonPurchaseItem actualItems = 3;

  repeated int32 rewardIds = 4;
  repeated KeyValueInt32 reasonRewardIds = 5;   // (itemChangeReason, rewardId)

  // extra result for extension
  repeated KeyValueInfo extraResults = 11;
}

message RaffleItemInfo {
  optional int32 reason = 1;
  optional int32 subReason = 2;
  optional bool withNtf = 3;

  repeated RaffleGetItem rewardItems = 4;
}

message RaffleGetItem {
  optional ItemInfo srcItem = 1;

  // extra info for extension
  repeated KeyValueInfo extra = 12;
}

message CustomRoomInfo {
  optional bool isCustomRoom = 1;  // 是否是自定义房间
  optional int64 RoomId = 2; // 自定义房间id
}

enum CookRecruitmentMarketRefreshType {
  CRMRT_None = 0;
  CRMRT_Manual = 1;
  CRMRT_Daily = 2;
  CRMRT_Init = 3;
}

enum CookOpenReason {
  COR_Unknown = 0;
  COR_Normal = 1;
  COR_Continue = 2;
}

enum CookCloseReason {
  CCR_Unknown = 0;
  CCR_Normal = 1;
  CCR_Continue = 2;
  CCR_Expire = 3;
}

message FBXInfo {
  repeated FBXItem items = 1;
}

message FBXItem {
  optional int64 size = 1;
  optional string msg = 2; //md5
  optional string version = 3;
  optional string name = 4;
}


enum FBXStatus {
  FBX_Unknown = 0;
  FBX_Start = 1;
  FBX_Complete = 2;
}
// 玩家身上的啾灵索引
message PlayerStarPIndexInfo {
  repeated int64 ownedStarPId = 1; // 拥有所有权的房间id
  repeated int64 visitedStarPId = 2; // 拥有角色的房间id，是ownedRoomId的超集
  repeated int64 starStarPId = 3; // 收藏的房间id
}

enum StarPDsCommonDataType {
  SPDCDT_None = 0;
  SPDCDT_Player = 1;        // 玩家基础
  SPDCDT_Pet = 2;
  SPDCDT_Building_MainCenter = 3;   // 终端建筑
  SPDCDT_Building_StoreBox = 4;     // 存储箱子
  SPDCDT_Building_Stuff = 5;        // 建筑材料(组装用)
  SPDCDT_RoleAttr = 6;              // 玩家属性
  SPDCDT_RoleTech = 7;              // 玩家科技
  SPDCDT_PlayerTask = 8;            // 玩家任务
  SPDCDT_ClimbTower = 9;            // 爬塔副本
  SPDCDT_DropGuarantee = 10;        // 掉落保底
  SPDCDT_RoleBit = 11;              // 玩家标记位
  SPDCDT_POI_Monster = 12;      // POI
  SPDCDT_POI_Choppable = 13;    // POI
  SPDCDT_POI_Teleport = 14;      // POI
  SPDCDT_POI_Props = 15;      // POI
  SPDCDT_POI_EnemyCamp = 16;    // POI
  SPDCDT_POI_Choppable_Tree = 17;  // POI
  SPDCDT_POI_DungeonEntrance = 18;  // POI
  SPDCDT_POI_FireBead = 19;      // POI
  SPDCDT_POI_GodStatue = 20;    // POI
  SPDCDT_POI_StarEgg = 21;      // POI
  SPDCDT_PlayerTalent = 22;    // 玩家天赋
  SPDCDT_PlayerBasePve = 23;   // 玩家的基础pve副本数据
  SPDCDT_ShopInfo = 24;        // 商店信息
  SPDCDT_PlayerShopInfo = 25;  // 玩家的商店信息
  SPDCDT_PlayerSetting = 26;        // 局内设置
  SPDCDT_CommonBasePve = 27;  // 基础pve副本的公共数据
  SPDCDT_Props = 28;  // 世界掉落道具信息（非大世界POI天然刷新的收集物）
  SPDCDT_BuildingList = 29;   // 建筑列表
  SPDCDT_GuideSave = 30;            // 新手引导进度
  SPDCDT_Building = 31; // 建筑（通用）
  SPDCDT_PetDex = 32;  // 星兽图鉴
  SPDCDT_PetAmulet = 33;  // 星兽信物
  SPDCDT_PetAmuletCurrency = 34;  // 星兽信物货币
  SPDCDT_PetCatch = 35;  // 星兽捕捉进度
  SPDCDT_Guild = 36;                // 公会
  SPDCDT_POI_TreasureChest = 37; // POI
  SPDCDT_POI_NPC = 38;  // POI
  SPDCDT_AccountTeamPveData = 39; // 账号队伍副本数据
  SPDCDT_AccountPowerData = 40; // 账号体力数据
  SPDCDT_RecentTeammate = 41; // 最近队友数据
  SPDCDT_PlayerTeamPveData = 42; // 玩家队伍副本数据
  SPDCDT_PlayerTaskGroup = 43; // 任务组数据
  SPDCDT_PlayerTaskBaseInfo = 44; // 任务基本数据
  SPDCDT_POI_State = 45;          // poi状态数据
  SPDCDT_PlayerAchievement = 46;  //成就
  SPDCDT_ItemsUidIdx = 47;  // 道具的索引表(uid->item instId)，uid可以是player，也可以是建筑
  SPDCDT_MapInfo = 48; // 地图数据
  SPDCDT_Fog = 49; // 迷雾数据
  SPDCDT_PlayerMail = 50; // 角色邮件
  SPDCDT_PlayerForcePromptMail = 51; // 角色强提示邮件
  SPDCDT_WorldDSLastCatchPet = 52;   // 昨日房间内所有抓捕数量
  SPDCDT_CatchPetOrderLast = 53;     // 最后房间捕获
  SPDCDT_commonPoiState = 54;        // POI状态公共数据
  SPDCDT_TimeRefresh = 55;        // POI状态公共数据
  SPDCDT_PlayerBackpackInfo = 56; // 玩家的背包信息
  SPDCDT_Items = 57; // 道具数据
  SPDCDT_PlayerPve = 58;   // 玩家的pve副本数据，uid+starPId为数据key
  SPDCDT_WorldPve = 59;   // 啾灵世界级的pve副本数据，starPId为数据key
  SPDCDT_AccountData = 60;   // 玩家账号级的数据，uid为数据key
  SPDCDT_AccountAchievement = 61;  //账号成就数据
  SPDCDT_PlayerPetFeed = 62;
  SPDCDT_PlayerFightData = 63;    // 战斗数据
  SPDCDT_OfflineTaskData = 64;  //离线任务数据(临时使用)
  SPDCDT_WorldLevelBaseInfo = 65; //世界等级基础信息
  SPDCDT_WorldLevelUpRecord = 66; //世界等级升级记录
  SPDCDT_GuildNews = 67;  //公会动态
  SPDCDT_PlayerFunctionControl = 68;  //玩家功能控制状态数据存储
  SPDCDT_TutorialInfo = 69;           //教程信息
  SPDCDT_GodStatusData = 70;          //雕像强化属性信息
  SPDCDT_WorldRefreshTimeData = 71;    //世界时间刷新信息
  SPDCDT_StarPOfflineSOCData = 72;     //离线生产数据
  SPDCDT_PlayerOrderData = 73;  //玩家订单数据
  SPDCDT_VisitorBehaviorRecord = 74;  //访客行为记录数据
  SPDCDT_StarPField = 75;   //农家乐农田数据
  SPDCDT_StarPSeedBox = 76;   //农家乐种子箱数据
  SPDCDT_VisitorTempData = 77;  //访客临时数据

  SPDCDT_AccountBriefData = 78;        //账户级摘要数据 摘要
  SPDCDT_FriendSkillData = 79;         //伙伴技能
  SPDCDT_PlayerGrowthPathMission = 80; // 玩家级数据，成长之路任务数据
  SPDCDT_PlayerGrowthPathGroup = 81;   // 玩家级数据，成长之路任务组数据
  SPDCDT_POI_Pickables = 82;           // POI
  SPDCDT_GuildMember = 83;  // 公会成员数据
  SPDCDT_PlayerBriefData = 84;         // 玩家级数据
  SPDCDT_PlayerGrowthPathSignInReward = 85; // 成长之路七天登录数据
  SPDCDT_PlayerStoryLineCondition = 86;//玩家条件存储
  SPDCDT_PlayerBackCDStatusInfo = 87;//玩家回城技能CD状态
  SPDCDT_PlayerStoryLineInfo = 88;//玩家条件存储
  SPDCDT_StarPBreedEggsParentsHistoryInfo = 89;//玩家孵化历史
  SPDCDT_StarPTowerAbility = 90;        //宝石属性信息
  SPDCDT_SOCDormancyData = 91; // SOC的ai信息落DB
  SPDCDT_StarPLotteryInfo = 92; // 扭蛋机抽奖信息
  SPDCDT_AdventureBaseInfo = 93;        //冒险团一些基本信息-starpGameSvr使用
  SPDCDT_StarPTaskSeqInfo = 94; // 顺序批量执行的Task的Seq信息
  SPDCDT_StarPTaskSeq = 95; // 顺序批量执行的Task
  SPDCDT_StarPTaskSeqWhite = 96; // 顺序批量执行的Task的白名单
  SPDCDT_StarPPlayerShop = 97;    // 玩家商店数据
  SPDCDT_PlayerResourceControl = 98; // 玩家资源防刷控制数据
  SPDCDT_GuideCheckResources = 99; //引导仓检
  SPDCDT_WorldBoxSetting = 100; // 世界箱子类型设置信息
  SPDCDT_GuildResourceControl = 101; // 公会资源防刷控制数据
  SPDCDT_RoomResourceControl = 102; // 房间资源防刷控制数据
  SPDCDT_StorylineRewardData = 103; // 剧情赠送道具数据
  SPDCDT_HelperInfo = 104;  // 玩家的小帮手引导数据
  SPDCDT_StarPDSTimeOffset = 105; // DS时间偏移
  SPDCDT_PlayerResourceBalanceTable = 106; // 玩家资源余额表
  SPDCDT_GuildResourceBalanceTable = 107; // 公会资源对账表
  SPDCDT_RoomResourceBalanceTable = 108; // 房间资源对账表
  SPDCDT_SPAwardInfo = 109; //图鉴奖励数据
  SPDCDT_POI_Boss = 110; // POI
  SPDCDT_POI_Rescue = 111; // POI
  SPDCDT_PlayerEnergyInfo = 112; //玩家体力信息
  SPDCDT_WishStatue = 113; //许愿神像数据
  SPDCDT_MissionLevel = 114; //MissionLevel
  SPDCDT_POI_Encounter = 115; //POI Encounter
  SPDCDT_WorldLevel = 116; //世界ds迁移时需要保存的关卡数据
  SPDCDT_EquipUnlock = 117; //装备解锁信息
  SPDCDY_RecoveryWorldData = 118; //世界迁移信息
  SPDCDY_LastLevel = 119; //上一次点开的世界等级
  SPDCDT_StarPPlayerShopInfo = 120; // 玩家商店信息
  SPDCDT_StarPPlayerEquipPlan = 121; // 玩家商店信息
  SPDCDT_POI_SingleMiniGame = 122; //POI SingleMiniGame
  SPDCDT_VisitorData = 123; //玩家拜访相关玩法信息
  SPDCDT_StarPAccountBackpackInfo = 124; // 玩家账号级背包信息
  SPDCDT_StarPPlayerAssistOrderInfo = 125; // 玩家互助材料信息
  SPDCDT_PersonalBase = 126; // 个人据点信息
  SPDCDT_StarpGuildCopied = 127; // 房间记录部落数据副本信息
  SPDCDT_StarPPlayerContactRecordInfo = 128; // 玩家联系人记录信息
  SPDCDT_StarPMapIconInfo = 129;// 玩家地图掉落物信息
  SPDCDT_POI_Chase = 130; //POI Chase
  SPDCDT_POI_PuzzleMatrix = 131; //POI PuzzleMatrix
  SPDCDT_AccountTask = 132; // 玩家账号级任务数据
  SPDCDT_AccountTaskGroup = 133; // 玩家账号级任务组数据
  SPDCDT_AccountGroupData = 134; // 账号级宗门数据
  SPDCDT_AccountShopInfo = 135; // 账号级商店数据
  SPDCDT_AccountTerminalPetBackup = 136; // 账号级：终端内的啾灵切片
  SPDCDT_StarPPlayerFriendIntimacyInfo = 137; // 玩家亲密度数据
  SPDCDT_StarPCardInfo = 138; // 卡片信息数据，账号级数据
  SPDCDT_StarPFriendIntimacy = 139; // 玩家亲密度数据
  SPDCDT_StarPCardPlayerInfo = 140; // 玩家卡片系统数据，账号级数据
  SPDCDT_StarPHatchPetRecord = 141; // 每种元素类型的首次孵化记录数据
  SPDCDT_StarPSocInterActionRatio = 142; // 玩家建筑互动倍率数据
  SPDCDT_StarPAccountTimeRefresh = 143; // 玩家账号级时间刷新数据
  SPDCDT_PlayerSettingPC = 144;  // 局内设置PC
  SPDCDT_Max = 145;
}

enum StarPDsMapPosStoreType {
  SPDMPST_NoFloat = 0;      // 数据中没有float
  SPDMPST_PB = 1;           // 自定义的位置存储
}

enum StarPPetPosBoxType {
  // example================================

  // 终端内盒子：(SPPPBT_NormalBox, 1, 1, 1) 表示终端内第一个盒子的第一行第一列
  // 战斗中：(SPPPBT_Fight, 1, 0, 0) 表示第一只战斗中的
  // 据点内：(SPPBT_Home, 1, 1, 1) 表示1号据点的第一行第一列
  SPPPBT_None = 0;
  SPPPBT_NormalBox = 1; // 终端内盒子
  SPPPBT_Fight = 2; // 战斗
  SPPPBT_Home = 3; // 据点内
}

// 啾灵世界-成员身份
enum EnmStarPMemeberIdentity {
  ENM_NORMAL = 0;  // 普通成员
  ENM_VISITOR = 1;  // 访客成员
  ENM_INHERIT = 2;  // 继承成员
}

// 啾灵世界-成员状态
enum EnmStarPMemeberStatus {
  ENM_SPMEMBER_OFFLINE = 0;  // 下线状态
  ENM_SPMEMBER_WAIT_CLIENT_CONNECT = 1;  // 等待客户端连接
  ENM_SPMEMBER_ONLINE = 2;  // DS在线
  ENM_SPMEMBER_PRE_OFFLINE = 3;  // DS玩家请求预退出, 超时将自动转OFFLINE
}

// SP玩家状态
enum EnmStarPPlayerStatus {
  SPSTATUS_Offline = 0;  // 不在星灵世界，也不在星灵飞船中
  SPSTATUS_Ship = 1;  // 在星灵飞船中
  SPSTATUS_Idle = 2;  // 星灵世界，空闲中
  SPSTATUS_Underland = 3;  // 星灵世界，地下城
  SPSTATUS_Maze = 4;  // 星灵世界，密域
  SPSTATUS_Tower = 5;  // 星灵世界，高塔
  SPSTATUS_TeamPVE = 6;  // 星灵世界，组队pve
  SPSTATUS_RLTower = 7;  // 星灵世界，爬塔
  SPSTATUS_Cross_StarP = 8;  // 世界ds跨服中，目前只有偷菜
}

enum EnmStarPPlayerLoginFailReason {
  ENM_STARP_ACCOUNTSVR_NOT_FOUND_PLAYER = 0;  // accountsvr找不到玩家

  ENM_STARP_RPC_TO_STARPSVR_FAIL = 1;  // RPC到StarPSVR失败
  ENM_STARP_BATTLE_HAS_IN_MIGRATE = 2;  // 旧DS正在迁移中，不能登录
  ENM_STARP_PLAYER_EXIT_OLD_DS_FAIL = 3;  // 旧DS退出失败
  ENM_STARP_PLAYER_ENTER_DS_FAIL = 4;  // DS进入失败
  ENM_STARP_IDIP_ERROR = 5;  // 当前状态idip无法登录

  ENM_STARP_ROOM_NOT_FOUND = 6;  // 未找到房间
  ENM_STARP_ROOM_NOT_FOUND_ROLE = 7;  // 房间内未找到角色
  ENM_STARP_ROOM_PRIVATE_NOT_ALLOW = 8;  // 仅邀请房间仅允许房主+被邀请成员进入
  ENM_STARP_ROOM_BAN_ROLE = 9;  // 角色禁入房间
  ENM_STARP_ROOM_DEL_ROLE = 10; // 角色已被删除
}

enum EnmStarPPlayerLoginStatus {
  ENM_SPPLAYER_LOGIN_OFFLINE = 0;  // 下线状态
  ENM_SPPLAYER_LOGIN_WAIT_CLIENT_CONNECT = 1;  // 等待客户端连接
  ENM_SPPLAYER_LOGIN_ONLINE = 2;  // DS在线
  ENM_SPPLAYER_LOGIN_PRE_OFFLINE = 3;  // DS玩家请求预退出, 超时将自动转OFFLINE
  ENM_SPPLAYER_LOGIN_IDIP_OFFLINE = 4;  // 下线IDIP业务中

  ENM_SPPLAYER_LOGIN_STARP_WROLD_CREATE = 5;  // 创建ds
  ENM_SPPLAYER_LOGIN_STARP_WROLD_ENTER_DS = 6;  // 登录ds
  ENM_SPPLAYER_LOGIN_STARP_WROLD_ADD_PLAYER = 7;  // 让DS请求加载DB数据
  ENM_SPPLAYER_LOGIN_STARP_WROLD_INFO_NTF = 8;  // 通知DS地址信息
  ENM_SPPLAYER_LOGIN_STARP_WROLD_ADD_PLAYER_FINISH = 9;  // DS反馈已经加载数据完成
  ENM_SPPLAYER_LOGIN_STARP_WROLD_EXIT_DS = 10;  // 客户端退出DS

  ENM_SPPLAYER_LOGIN_STARP_WROLD_DS_DB_LOAD_FINISH = 11; // DS DB加载完成
  ENM_SPPLAYER_LOGIN_STARP_WROLD_DS_RECEIVE_PLAYER_READY_FOR_GAME = 12; // DS 接收玩家准备进入游戏
  ENM_SPPLAYER_LOGIN_STARP_WROLD_DS_RECEIVE_PLAYER_LOGIN_GAME = 13; // DS 接收玩家登录游戏
  ENM_SPPLAYER_LOGIN_STARP_WROLD_DS_LOGOUT = 14; // DS 退出游戏
  ENM_SPPLAYER_LOGIN_STARP_WROLD_DS_INVALID = 15; // DS 无效
  ENM_SPPLAYER_LOGIN_STARP_WROLD_INFO_NTF_FAILED = 16;  // 通知DS加载失败信息
}

// 啾灵-玩家数据加载原因
enum EnmStarPLoadReason {
  ENM_STARP_LOAD_NONE = 0;  // 未知原因
  ENM_STARP_LOAD_FROM_TCAPLUS_SUCC = 1;  // 从DB加载完成
  ENM_STARP_LOAD_LOGIN = 2;  // 啾灵玩家登录
  ENM_STARP_LOAD_ENTER = 3;  // 啾灵玩家进入啾灵世界
  ENM_STARP_LOAD_UPDATE = 4;  // 啾灵玩家更新数据
  ENM_STARP_LOAD_PASSIVE = 5;  // 啾灵user被动拉起
  ENM_STARP_LOAD_APPLY = 6;  // 啾灵玩家申请房间
  ENM_STARP_LOAD_INVITE = 7;  // 啾灵玩家邀请好友
  ENM_STARP_LOAD_OFFLINE = 8;  // 啾灵玩家ds下线时加载
  ENM_STARP_LOAD_CACHE_LOCK_PREEMPT = 9;  // 啾灵玩家分布式抢锁加载
  ENM_STARP_LOAD_IDIP = 10; // idip修改导致的加载
  ENM_STARP_LOAD_ENTER_CHAT_GROUP = 11; // 玩家加入聊天频道
  ENM_STARP_LOAD_EXIT_CHAT_GROUP = 12; // 玩家退出聊天频道
  ENM_STARP_LOAD_DS_COMMON_DB_INFO = 13; // DsCommonDbInfoTable只读数据加载
  ENM_STARP_LOAD_CREATE_DRESSUP = 14;  // 创角选择服装
  ENM_STARP_LOAD_SAVE_DRESSUP = 15;  // 飞船保存服装-设为SP玩法使用
  ENM_STARP_LOAD_GET_DRESSUP = 16;  // 获取服装信息
  ENM_STARP_LOAD_PLAYER_INHERT = 17; // 啾灵玩家账号继承
  ENM_STARP_LOAD_PLAYER_ROLE_STATUS = 18; // 啾灵玩家角色状态
  ENM_STARP_LOAD_GM = 19; // GM
  ENM_STARP_LOAD_PLAYER_FUNCTION_CONTROL = 20; // 功能控制
  ENM_STARP_LOAD_BLESS_STATUE = 21; //许愿神像
  ENM_STARP_LOAD_PET_DEX_DATA = 22; // 获取图鉴信息
  ENM_STARP_LOAD_ACHIEVEMENT_DATA = 23; // 获取成就信息
  ENM_STARP_LOAD_REFRESH_TICK = 24; // 重置框架tick
  ENM_STARP_LOAD_CREATE_CHOOSE_SUIT = 25;  //创角选择装备
  ENM_STARP_LOAD_MAX_TERMINAL_LEVEL = 26;  //终端最高等级（账号里所有房间内的）
  ENM_STARP_LOAD_CACHE_LOCK_RELEASE = 27;  // 啾灵玩家释放锁加载
  ENM_STARP_LOAD_SYNCHRONIZE = 28;  // 房间对账
}


// 啾灵-玩家退出原因
enum EnmStarPRemoveReason {
  ENM_STARP_REMOVE_NONE = 0;  // 未知原因
  ENM_STARP_REMOVE_LOGOUT = 1;  // 啾灵退出
  ENM_STARP_REMOVE_EXPIRE = 2;  // 缓存超时退出
  ENM_STARP_REMOVE_TCAPLUS_UPDATE_FAILED = 3;  // tcaplus更新失败退出
  ENM_STARP_REMOVE_CACHE_LOCK_PREEMPT = 4;  // 分布式抢锁
  ENM_STARP_REMORE_NOTIFY_RENEWAL_FAILED = 5;  // 续锁失败onNotifyRenewalFailed
}

// 啾灵-StopGame原因定义
enum EnmStarPStopGameReason {
  ENM_STARP_STOP_GAME_REQUEST = 1;  // SP侧主动请求StopGame
  ENM_STARP_STOP_GAME_TIMEOUT = 2;  // SP侧超时重试StopGame
  ENM_STARP_STOP_GAME_DS_ASK  = 3;  // DS侧主动请求StopGame
  ENM_STARP_MIGRATE_STOP_GAME_REQUEST = 4;  // SP侧主动请求StopGame
}

// 啾灵-数据更新操作
enum EnmStarPUpdateOP {
  ENM_STARP_UPDATE_USER = 1;  // 操作类型 1:更新账号房间数据
  ENM_STARP_INSERT_USER = 2;  // 操作类型 2:新建账号房间数据
  ENM_STARP_UPDATE_ROLE = 3;  // 操作类型 3:更新角色房间数据
}

enum EnmStarPUserCheck {
  ENM_STARP_CHECK_ADD_ROLE = 1;
  ENM_STARP_CHECK_ADD_ADMIN = 2;
}

enum EnmStarPUserAuthority {
  ENUM_STARP_USER_AUTHORITY_NORMAL = 0;
  ENUM_STARP_USER_AUTHORITY_ADMIN = 1;
}

// 啾灵-申请记录操作
enum EnmStarPApplyOP {
  ENM_STARP_APPLY_REFUSE = 0;  // 操作类型 0:拒绝 1:同意 2:屏蔽
  ENM_STARP_APPLY_AGREE = 1;  // 操作类型 0:拒绝 1:同意 2:屏蔽
  ENM_STARP_APPLY_BAN = 2;  // 操作类型 0:拒绝 1:同意 2:屏蔽
}

// 啾灵房间加载原因
enum EnmStarPWorldLoadReason {
  ENM_STARPWORLD_LOAD_NONE                 = 0;  // 未知原因
  ENM_STARPWORLD_LOAD_GAME_SESSION         = 1;  // 创建GameSession
  ENM_STARPWORLD_LOAD_APPLY                = 2;  // 审批
  ENM_STARPWORLD_LOAD_ADD_PLAYER_FINISH    = 3;  // addPlayer
  ENM_STARPWORLD_LOAD_STOP_GAME            = 4;  // stopGame
  ENM_STARPWORLD_LOAD_HEARTBEAT            = 5;  // heartBeat
  ENM_STARPWORLD_LOAD_DS_CALLBACK          = 6;  // dsCallBack
  ENM_STARPWORLD_LOAD_DS_OFFLINE_MSG       = 7;  // dsOFFLineMsg
  ENM_STARPWORLD_LOAD_DS_GUILD             = 8;  // dsGuild
  ENM_STARPWORLD_LOAD_DSR                  = 9;  // dsr
  ENM_STARPWORLD_LOAD_ENTER                =10;  // enter
  ENM_STARPWORLD_LOAD_EXIT                 =11;  // exit
  ENM_STARPWORLD_LOAD_PUBLISH              =12;  // publish
  ENM_STARPWORLD_LOAD_GET_ATTR             =13;  // getAttr
  ENM_STARPWORLD_LOAD_GET_PUBLIC_INFO      =14;  // getPublicInfo
  ENM_STARPWORLD_LOAD_VOTE_DEL_UESR        =15;  // voteDelUser
  ENM_STARPWORLD_LOAD_GET_TEAM_INFO        =16;  // getTeamInfo
  ENM_STARPWORLD_LOAD_INVITE               =17;  // invite
  ENM_STARPWORLD_LOAD_ONGOING_BATTLE       =18;  // onGoingBattle
  ENM_STARPWORLD_LOAD_CHECK_USER_ONLINE    =19;  // checkOnline
  ENM_STARPWORLD_LOAD_TEAM_CHANGE          =20;  // teamChange
  ENM_STARPWORLD_LOAD_FINI                 =21;  // fini
  ENM_STARPWORLD_LOAD_DS_MIGRATE           =22;  // migrateDS
  ENM_STARPWORLD_LOAD_DS_CREATE            =23;  // createDS
  ENM_STARPWORLD_LOAD_MODIFY               =24;  // modifyDS
  ENM_STARPWORLD_LOAD_BAN                  =25;  // ban
  ENM_STARPWORLD_LOAD_DEL_UESR             =26;  // delUser
  ENM_STARPWORLD_LOAD_TRANSFER             =27;  // transfer
  ENM_STARPWORLD_LOAD_DS_ENTER             =28;  // dsEnter
  ENM_STARPWORLD_LOAD_DS_PREPARE_OFFLINE   =29;  // dsPrepareOffline
  ENM_STARPWORLD_LOAD_DS_OFFLINE           =30;  // dsOffline
  ENM_STARPWORLD_LOAD_DS_RUN_OVER_TIME     =31;  // onDsRunOvertime
  ENM_STARPWORLD_LOAD_DS_HEART_BEAT        =32;  // getDsMaxHeartbeatIntervalSec
  ENM_STARPWORLD_LOAD_RELEASE_LOCK         =33;  // 释放分布式锁
  ENM_STARPWORLD_LOAD_IDIP_LOAD            =34;  // idip加载
  ENM_STARPWORLD_LOAD_GIVEUP_GAME          =35;  // 放弃进入上一次的世界ds副本
  ENM_STARPWORLD_LOAD_WORLD_LEVEL          =36;  // 世界等级操作
  ENM_STARPWORLD_LOAD_BATCH_BROADCAST      =37;  // 批量广播
  ENM_STARPWORLD_LOAD_VISITOR_ENTER        =38;  // 访客登录
  ENM_STARPWORLD_LOAD_GM                   =39;  // GM
  ENM_STARPWORLD_LOAD_REFRESH_TICK         =40;  // 刷新框架TICK
  ENM_STARPWORLD_LOAD_ES_BULK              =41;  // es bulk
  ENM_STARPWORLD_LOAD_MOE_MSG_CHANGE       =42;  // 元梦账号信息变更，需要更新es信息
  ENM_STARPWORLD_LOAD_VISITOR_OCCUPY       =43;  // 访客预占位
  ENM_STARPWORLD_LOAD_SYNCHRONIZE          =44;  // 对账
  ENM_STARPWORLD_LOAD_DEL_ROLE             =45;  // 删除角色
  ENM_STARPWORLD_LOAD_NTF_CONSUME_INTERACTION = 46; // 离线消息db类型
  ENM_STARPWORLD_LOAD_DS_UPDATE_GUILD_INFO =47;  // 更新部落信息
  ENM_STARPWORLD_LOAD_DS_NOTIFY_GUILD_MEMBER_APPLY = 48;  // 通知部落成员申请信息
}

enum EnmStarPWorldRemoveReason {
  ENM_STARPWORLD_REMOVE_NONE = 0;  // 未知原因
  ENM_STARPWORLD_REMOVE_LOCK_PREEMPT = 1;  // 续锁失败
  ENM_STARPWORLD_REMOVE_UPDATE_FAILED = 2;  // 更新db失败
  ENM_STARPWORLD_REMOVE_NOTIFY_ERNEWAL = 3;  // 释放本地锁
  ENM_STARPWORLD_REMOVE_CLEAN_CACHE = 4; //定时器清理缓存
}

// 啾灵-房间加入类型
enum StarPRoomState {
  SPRS_NONE = 0 ; // 无需申请直接进入
  SPRS_APPLY = 1 ; // 需要申请
  SPRS_PASSWD = 2 ; // 密码
  SPRS_INVITE = 3 ; // 仅邀请进入
}

enum GetStarPInfoListType {
  GSILT_SELF = 0; // 我的房间列表
  GSILT_FRIEND = 1; // 好友房间列表
  GSILT_WORLD = 2; // 世界房间列表
}

// 啾灵-角色账号类型
enum StarPRoleState {
  SP_ROLE_NORMAL = 0 ; // 正常
  SP_ROLE_BAN = 1 ; // 封禁
  SP_ROLE_DEL = 2 ; // 删除
  SP_ROLE_DEL_CD = 3 ; // 删除冷却期
  SP_ROLE_DEL_COMPLETELY = 4 ; // 彻底删除(无法继承)
}

// 啾灵-角色删除原因
enum StarPRoleDelReason {
  SP_ROLE_UNKNOWN = 0 ; //
  SP_ROLE_INHERITED = 1 ; // 已继承
  SP_ROLE_BY_SELF = 2 ; // 自己删
}

// 啾灵-角色账号子类型
enum StarPRoleSubState {
  SP_ROLE_SUB_NORMAL = 0 ; // 正常
  SP_ROLE_CAN_INHERIT = 1 ; // 能继承其他角色
  SP_ROLE_BEING_INHERITED = 2 ; // 被继承中
  SP_ROLE_INHERITING = 3 ; // 继承中
}

// 啾灵-离线消息类型
enum SPDataType {
  SPDT_NONE         = 0 ; // 无意义
  SPDT_CLIMB_TOWER  = 1 ; // 爬塔结算消息
  SPDT_TEAM_PVE_SETTLE  = 2 ; // 组队pve副本的结算消息
  SPDT_DEBUGDS          = 3 ; // Debugds
  SPDT_CROP_THEFT_SETTLE= 4 ; // 农家乐偷菜的结算消息
  SPDT_GUILD_DEL_USER   = 5 ; // 公会删除用户消息
  SPDT_WRITE_BACK_DATA = 6; // --数据带回大世界
  SPDT_PET_TRADE_RETURN_PET = 7 ; //  啾灵-啾灵交换订单取消或超时时返还啾灵
  SPDT_GUILD_CREATE = 8; // 部落创建
  SPDT_GUILD_MEMBER_JOIN = 9; // 部落成员加入
  SPDT_GUILD_MEMBER_LEAVE = 10; // 部落成员退出
  SPDT_GUILD_DISMISS = 11; // 部落解散
  SPDT_GUILD_CHANGE_NAME = 12; // 部落改名
  SPDT_GUILD_CHANGE_TITLE = 13; // 部落职务变更
  SPDT_ROOM_MEMBER_LEAVE = 14; // 玩家离开房间
  SPDT_GUILD_MEMBER_APPLY = 15; // 玩家申请加入公会
  SPDT_GUILD_MEMBER_INVITE = 16; // 邀请玩家加入公会
  SPDT_GUILD_EFFICIENCY_RATE_CHANGE = 17; // 建造效率变化
  SPDT_CONTACT_RECORD = 18; // 互动信息
  SPDT_CONTACT_TERMINAL_PET_BACK = 19;  // 将啾灵从终端踢回给归属者spuid
  SPDT_INTIMACY_LEVEL_CHANGE = 20; // 玩家亲密度等级变化
  SPDT_GROUP_INFO_UPDATE = 21; // 玩家宗门信息更新
}

// 啾灵世界被封禁时长
enum StarPBanTime {
  SP_BAN_TIME_NORMAL = 0 ; // 无意义
  SP_BAN_TIME_3h = 1 ; // 封禁3h
  SP_BAN_TIME_6h = 2 ; // 封禁6h
  SP_BAN_TIME_12h = 3 ; // 封禁12h
  SP_BAN_TIME_24h = 4 ; // 封禁24h
  SP_BAN_TIME_48h = 5 ; // 封禁48h
}

// 啾灵世界退出原因
enum StarPExitReason {
  SP_EXIT_REASON_NORMAL       = 0 ;   // 无意义
  SP_EXIT_REASON_BY_ACTIVE    = 1 ;   // 用户主动退出
  SP_EXIT_REASON_BY_KICK      = 2 ;   // 用户被踢退出
  SP_EXIT_REASON_BY_TIMEOUT   = 3 ;   // 用户超时退出
  SP_EXIT_REASON_BY_OFFLINE   = 4 ;   // 用户gamesvr下线
  SP_EXIT_REASON_BY_MATCH     = 5 ;   // 用户通过匹配进入对局ds
  SP_EXIT_REASON_BY_BAN       = 6 ;   // 用户通过BAN退出
  SP_EXIT_REASON_BY_DEL_USER  = 7 ;   // 用户通过删除退出
  SP_EXIT_REASON_BY_GIVEUP_GAME  = 8 ;   // 放弃进入上一次世界ds的副本
  SP_EXIT_REASON_BY_VISITOR   = 9 ;   // 离开本世界访问他服世界(偷菜玩法新增)
  SP_EXIT_REASON_BY_VISITOR_EXPIRES   = 10 ;   // 访客时间到期
  SP_EXIT_REASON_BY_RL                = 11 ;   // 进入爬塔对局DS
  SP_EXIT_REASON_BY_JOIN_RECRUIT_FAIL = 12 ;   // 进入招募队伍失败退出DS
}

// 进入啾灵世界来源
enum StarPEnterSource {
  SP_ENTER_NORMAL = 0 ;     // 正常从元梦大厅进入
  SP_ENTER_FROM_DS = 1 ;   // 用户从副本DS回到啾灵世界
  SP_ENTER_FROM_INHERIT = 2 ;   // 用户从继承界面进入
}

// 啾灵世界用户投票结果
enum StarPVoteRet {
  SP_VOTE_FOR_NORMAL = 0 ;   // 无意义
  SP_VOTE_FOR_ACCEPT = 1 ;   // 用户投票同意
  SP_VOTE_FOR_REFUSE = 2 ;   // 用户投票拒绝
  SP_VOTE_FOR_ABSTAIN = 3 ;   // 用户投票放弃
}

// 啾灵公会类型
enum EnmStarPGuildType {
  SP_GUILD_TYPE_PERSONAL_GUILD = 0;    //公会类型-个人公会
  SP_GUILD_TYPE_FORMAL_GUILD = 1;    //公会类型-正式工会
}

// 啾灵账号角色状态
message RoleStatus {
  optional int64 starPWorldId = 1; // 啾灵世界id
  optional int64 roleId = 2; // 啾灵世界id
  optional int32 status = 3; // 角色状态
  optional int32 subStatus = 4; // 子状态，参考 StarPRoleSubState
}

message InheritMsg {
  optional int64 srcStarPWorldId = 1; // 来源啾灵世界
}

// 啾灵-爬塔副本结算数据
message SPClimbTower {
  optional int32 towerId = 1;               //当前通关的副本id
  optional bool weekRewardStatus = 2;       //是否领取周奖励
  repeated int32 receiveTowerIds = 3;       //本次领取的奖励副本id列表
  repeated int32 buffIds = 4;               //当前的每章能力id列表(每章会清除，所以不能更新增量数据，最多10个)
  optional int32 buffResetTimes = 5;        //每章能力可重置次数
  repeated int32 chooseBuffIds = 6;         //通关后随机出来的可选择的能力id列表(三选一)
  optional int32 chooseBuffResetTimes = 7;  //通关后随机能力剩余刷新次数
}

// 啾灵-返还星灵数据
message SPReturnPet {
  optional int32 itemId = 1;
  optional proto_StarPPet userData = 2;
}

message SPTeamPveItem {
  optional int64 starPId = 1;                             // 世界ID
  optional int64 backPackId = 2;                          // 背包ID
  optional int32 id = 3;                                  // 物品ID
  optional int64 instId = 4;                              // 物品实例ID
  optional int64 lastUpdateTime = 5;                      // 上次创建或修改时间
  optional int32 itemType = 6;                            // 背包物品类型
  optional int32 backPackPos = 7;                         // 物品在背包里的格子位置
  optional int64 num = 8;                                 // 物品数量
  optional int32 period = 9;                              // 物品有效时间
  optional int64 obtainTime = 10;                         // 获得时间
  optional proto_StarPItemUserDataUnion itemData = 11;    // 自定义数据
  optional int32 isLock = 12;                             // 是否加锁
}

// 啾灵-组队pve副本的结算数据
message SPTeamPve {
  optional int32 pWorldId = 1;  // 对应SP副本模式_组队PVE表格  组队副本配置页签中的层id
  optional bool isAward = 2;    // 是否开启了宝箱奖励
  repeated int64 selectPetInstIds = 3;    // 选择出站的星兽实例id
  repeated SPTeamPveItem awardItems = 4;  // 挑战成功宝箱的掉落物品列表
  repeated SPTeamPveItem consumeItems = 5;  // 消耗的物品列表
  optional int64 addExp = 6;  // 增加的经验值
  optional int64 levelId = 7;  // SP副本配置表格 副本关卡跳转表页签的关卡id
  repeated int64 teamUids = 8;    // 该局匹配撮合的队友uid列表
  optional bool isSucc = 9;  // 是否挑战成功
  repeated SPTeamPveItem firstAwardItems = 10;  // 首通额外宝箱的掉落物品列表
  optional int64 costEnergy = 11;  // 花费的体力值，以进入副本那个时刻的配置为准
  optional int64 sendTime = 12;  // 发送结算消息的utc时间, 单位秒
  repeated proto_StarPBuff buffs = 13;  //结算buff
  optional int64 awardOpenTimeStamp = 14; // 打开宝箱的时间戳
}

// 啾灵-idip->DebugDs数据请求
message SPDebugdsModify {
  optional int64 starPId = 1;
  optional int64 uid = 2;
  optional int64 roleId = 5;
  optional string para = 3;
  optional int32 changeType = 4;
}

// 啾灵-公会删除玩家
message SPGuildDelUser {
  optional int64 starPId = 1;   // 啾灵世界ID
  optional int64 delUid  = 2;   // 被删除用户uid
  optional int64 delRoleId = 3; // 被删除用户角色id
  optional int64 optUid  = 4;   // 操作用户uid
  optional int64 optRoleId = 5; // 操作用户角色id
  optional int64 optTime = 6;   // 操作时间秒
}

// 啾灵-玩家离开房间（主动离开or被房主踢出）
message SPRoomMemberLeave {
  optional int64 starPId = 1;   // 啾灵世界ID
  optional int64 delUid  = 2;   // 被删除用户uid
  optional int64 delRoleId = 3; // 被删除用户角色id
  optional int64 optUid  = 4;   // 操作用户uid
  optional int64 optRoleId = 5; // 操作用户角色id
  optional int64 optTime = 6;   // 操作时间秒
}

// 啾灵-跨DS数据带回大世界(成就、任务)
message SPWriteBackCondData {
  optional int64 condType = 1;  // 条件类型
  optional int64 condPara = 2;  // 条件参数
  repeated int64 condParaArr = 3; // 条件参数
  repeated KeyValueInt32 condParaDict = 4; // 条件参数
  optional string condEvent = 5; // 条件事件
}

// 啾灵-跨DS数据带回大世界(成就、任务)
message SPWriteBackData {
  repeated SPWriteBackCondData condData = 1; // 所有带回的条件
}

// 啾灵-离线消息
message SPInteractionData {
  optional SPDataType type = 1; // 消息类型
  optional int64 id = 2; // id
  optional int64 deadTime = 3; // 消息截止处理时间(超过可直接丢弃不处理)
  oneof data {
    SPClimbTower climbTower = 4; // 爬塔副本结算数据
    SPReturnPet returnPet   = 5; // 返还星灵的数据
    SPTeamPve   teamPve     = 6;     // 组队pve副本的结算数据
    SPDebugdsModify debugdsModify = 7; // debugds数据更改请求数据
    SPCropTheft  cropTheft         = 8; // 农家乐偷菜的结算数据
    SPGuildDelUser guildDelUser = 9; // 公会需要删除的成员数据
    SPWriteBackData   writeBackData     = 10;     // 跨DS数据带回大世界(成就、任务)
    SPPetTradeReturnPet petTradeReturnPet = 11;
    SPGuildCreate guildCreate = 12; // 部落创建
    SPGuildMemberJoin guildMemberJoin = 13; // 部落成员加入
    SPGuildMemberLeave guildMemberLeave = 14; // 部落成员退出
    SPGuildDisMiss guildDismiss = 15; // 部落解散
    SPGuildChangeName guildChangeName = 16; // 部落改名
    SPGuildChangeTitle guildChangeTitle = 17; // 部落职务变更
    SPRoomMemberLeave roomMemberLeave = 18; // 玩家离开房间
    SPGuildMemberApply guildApply = 19; // 公会申请
    SPGuildMemberInvite guildInvite = 20; // 公会邀请
    SPGuildEfficiencyRateChange guildEfficiencyRate = 21; // 公会效率变化
    proto_StarPOneContactRecord contactRecord = 22; //互动信息
    SPTerminalPetBackList terminalPetBackList = 23; // 返还啾灵
    SPIntimacyLevelChange intimacyLevelChange = 24; // 亲密度等级
    SPPlayerGroupUpdateInfo playerGroupUpdateInfo = 25; // 宗门信息变更
  }
}

// StarPPlayerInteractionTable 离线db类型
enum StarPInteractionType{
  SPIT_NONE = 0 ; // 无意义
  SPIT_PLAYER = 1 ; // 玩家
  SPIT_UNION = 2 ; // 工会
  SPIT_DS = 3 ; // DS
  SPIT_RETURN_PET = 4 ; // 返还星灵
  SPIT_DEBUGDS = 5 ; // Debugds
  SPIT_GUILD = 6; // 部落
  SPIT_ACCOUNT = 7; //账号
  SPIT_INTIMACY_LEVEL = 8; // 亲密度
  SPIT_GROUP = 9; // 宗门
}

// 玩家亲密度等级变化
message SPIntimacyLevelChange {
  optional int64 targetUid       = 1; // 亲密度变化的对应玩家（非自身）
  optional int64 preLevel        = 2; // 之前的亲密度等级
  optional int32 postLevel       = 3; // 之后的亲密度等级
}


message StarPPetInfo {
  optional int32 petId = 1;   //星兽id
  optional int32 level = 2;   //星兽等级
}

message StarPEquipInfo {
  optional int32 equipId = 1;   //装备id
}

message StarPCommonUserInfo {
  optional int64 starPWorldId = 1;  // 啾灵世界id
  optional string starPWorldName = 2; // 啾灵世界名称
  optional int32 level = 3;   // 等级
  optional string name = 4;   // 昵称
  optional string avatar = 5;  // 头像
  optional int32 gender = 6; // 性别, 1-男 2-女 0-未知
  optional int32 status = 7;   // 当前状态
  optional int32 statusParam = 8;  // 当前状态的额外参数，status表示副本中则此字段表示哪个副本
  repeated StarPPetInfo petInfos = 9;  // 携带的星兽信息
  optional IntArray unlockedReplicaId = 10;  // 已解锁副本id
  optional int64 statusTime = 11;  // 状态变更的时间戳，方便在状态异常的时候将状态重置
  optional int64 starPRoleId = 12; // starP角色Id-StarP角色跨世界转移
  repeated int32 dressUpIds = 13;   // 装扮Id列表
  optional int32 professionId = 14; // 当前角色的职业
}
// SP-玩家最新的组队信息
message StarPTeamUserInfo {
  optional StarPCommonUserInfo commonInfo = 1;
  repeated StarPEquipInfo equipInfos = 2;   // 携带的装备信息
  optional int32 weaponId = 3;   // 当前持有的武器id
  optional SpUnleashedPetInfo spUnleashedPetInfo = 4;   //召唤星兽信息
  optional bool visitor   = 5;   // 是否访客
  optional StarPPvpInfo pvpInfo = 6;  // pvp信息
  repeated int32 spEquipInfoList = 7; // 装备列表
}

//召唤星兽信息
message SpUnleashedPetInfo {
  optional int32 petId = 1;  //星兽id
  optional int32 stats = 2;  //星兽状态
  optional int64 instanceid = 3;  //星兽uid
}

// pvp备战啾灵列表
message StarPPvpInfo {
  repeated StarPPvpPet pvpPetList = 1;  // 备战啾灵列表
}

message StarPPvpPet {
  optional int32 petId = 1;  //星兽id
  optional int64 instanceId = 2;  //星兽uid
}

// SP-啾灵PVP角色信息
message StarPPvpUserInfo {
  optional StarPCommonUserInfo commonInfo = 1;
}

// SP-玩家的部落简要数据
message StarPGuildInfo {
  optional int64 guildId = 1;     // 部落id
  optional string guildName = 2;  // 部落名称
  optional int64 starPId = 3;     // 账号级的部落所在世界
}

// SP-玩家的宗门简要数据
message StarPGroupInfo {
  optional int64 groupId = 1;     // 宗门id
  optional string groupName = 2;  // 宗门名称
}

// SP-玩家最新的公开信息
message StarPPublicUserInfo {
  optional StarPCommonUserInfo commonInfo = 1;
  optional int64 lastLoginOutTime = 3; // 登出的时间
  repeated PlayerDressItemInfo dressItemInfos = 5;// 装扮信息(铭牌、称号、头像框、道具头像等)
  optional bool teamPveActive = 6; // 组队pve玩法是否已解锁
  optional int64 mainTerminalUID = 7; // 可传送的主终端ID
  optional bool  isOpenBless     = 8; // 是否建造神像 [todo 即将删除，该字段已废弃]
  optional int32 adventureLevel = 9; // 冒险团等级 [todo 即将删除，改为使用accountLevelData.adventureLevel]
  optional StarPGuildInfo guildInfo = 10; // 部落信息 [todo 即将删除，改为使用accountLevelData.guildInfo]
  optional int64 globalGuildId = 11; // 账号级的部落Id [todo 即将删除, 改为使用accountLevelData.guildInfo.guildId]
  optional int64 guildStarPWorldId = 12; // 账号级的部落所在世界 [todo 即将删除, 改为使用accountLevelData.guildInfo.starPId]

  // 账号级数据, 后续账号级数据字段都加在这个message里
  message AccountLevelData {
    optional int32 adventureLevel = 1;    // 冒险团等级
    optional StarPGuildInfo guildInfo = 2; // 部落信息
  }
  optional AccountLevelData accountLevelData = 20;
}

// SP-玩家最新的公开信息, 仅java侧写入
message StarPPublicUserInfo2 {
  optional StarPGroupInfo groupInfo = 1; // 宗门信息
  optional StarPSocInteractionInfo SocInteractionInfo = 2;  //建筑互动信息
}

message StarPRoomPveInfo {
  optional int64 room_id = 1;
  optional int64 starPId = 2;
  optional int32 matchTypeId = 3; // 玩法id
  optional int32 pWorldId = 4;  // pve关卡id
}

// SP-玩家最新的角色杂项信息
message StarPMiscUserInfo{
  optional int32 starPCollectedPetCount = 1; // 已收集到的啾灵数量
}

message StarPChatGroupKey {
  optional ChatGroupKey chatGroupKey = 1;
  optional string starpWroldName = 2;
  optional string guildName = 3;
}

// 创建和预创建的参数
message StarPBaseGroupCreateContext {
  optional uint64 id      = 1;          // 组织唯一id，预创建时为空
  optional string name    = 2;          // 组织名
  optional uint64 uid     = 3;

  optional proto_StarPBaseGroupDataInfo businessData = 4;
  optional proto_StarPBaseGroupMemberDataInfo businessMemberData = 5;
}

message StarPBaseGroupJoinContext {
  optional uint64 id      = 1;          // 组织唯一id，预创建时为空
  optional uint64 uid     = 2;
  optional uint64 billno  = 3;
  optional proto_StarPBaseGroupMemberDataInfo businessMemberData = 4;
  optional proto_StarPBaseGroupApplicationDataUnion applicationData = 5;
  optional proto_StarPBaseGroupInvitationDataUnion invitationData = 6;
}

message StarPBaseGroupLeaveContext {
  optional uint64 id      = 1;          // 组织唯一id，预创建时为空
  optional uint64 uid     = 2;
  optional uint32 type    = 3;
}

message StarPBaseGroupDismissContext {
  optional uint64 id      = 1;          // 组织唯一id，预创建时为空
  optional uint64 uid     = 2;
  optional uint32 type    = 3;
  optional bool isNeedInherit = 4;
}

message StarPBaseGroupKickContext {
  optional uint64 id        = 1;          // 组织唯一id，预创建时为空
  optional uint64 uid       = 2;          // 组织首领uid or 房主uid
  optional uint64 memberUid = 3;          // 组织成员成员uid
  optional uint32 type      = 4;
  optional uint64 starpId   = 5;          // 房间ID
}

message StarPBaseGroupChangeNameContext {
  optional uint64 id        = 1;          // 组织唯一id，预创建时为空
  optional uint64 uid       = 2;          // 操作人UID
  optional string name      = 3;          // 新名字
}

message StarPBaseGroupChangeTitleContext {
  optional uint64 id        = 1;          // 组织唯一id，预创建时为空
  optional uint64 uid       = 2;          // 操作人UID
  optional uint64 memberUid = 3;          // 组织成员uid
  optional int32 title      = 4;          // 新职务
}

message StarPBaseGroupUid2GroupContext {
  optional int64 uid = 1;                                 // 玩家uid
  optional int32 baseGroupType = 2;                       // 组织类型
  optional int64 baseGroupId = 3;                         // 组织ID
  optional int32 status = 4;                              // 当前状态
  optional com.tencent.wea.protocol.proto_StarPBaseGroupSimpleInfo simpleInfo = 5; // 组织简明信息
}

// 宗门推荐列表协议用的宗门简要信息
message StarPGroupBriefInfo {
  optional int64 id = 1;  // 宗门id
  optional string name = 2;  // 宗门名称
  optional string desc = 3; // 宗门描述
  optional int32 iconId = 4;  // 宗门图标
  optional int32 joinType = 5;  // 加入类型
  optional int32 level = 6;  // 宗门等级
  optional int32 memberNum = 7;  // 当前成员数量
  optional bool isApply = 8;  // 是否已申请
}

// 宗门成员列表协议用的成员简要信息
message StarPGroupMemberBriefInfo {
  optional int64 uid = 1;  //
  optional string name = 2;  // 玩家名称
  optional string profile = 3;  // 元梦账号的头像
  optional com.tencent.wea.xlsRes.PlayerStateType playerState = 4; // 玩家状态
  optional int32 title = 5;  // 宗门职位
  optional int64 logoutTimeMs = 6;  //  上次登出时间戳
  optional bool hidePlayerStatus = 7; // 个人信息隐藏-隐身
  repeated proto_StarPAssistOrderSimpleInfo assistOrderMap = 8;  //玩家订单简要信息
  optional proto_StarPPetTradeWishInfo petTradeWishInfo = 9;  //啾灵交换心愿信息
  optional int32 level = 10;  // 最近登录sp角色的等级
  optional int32 professionId = 11;  // 最近登录sp角色的职业
}

message StarPGuildBriefInfo {
  optional int64 starPId = 1;
  optional int64 guildId = 2;
  optional int64 leaderUid = 3;
  optional string guildName = 4;
  optional string starpWorldName = 5;
  optional int64 efficiencyRate = 6;
  repeated int64 guildMemberUids = 7;
  optional bool isApprovalRequired = 8;
  optional int32 terminalLevel = 10;
}

//星灵的背包类型
enum StarPPetBackPackType{
  PET_BOX = 4;    //星灵盒子
  PET_EQUIP = 5;  //已装备背包
  PET_TEAM = 6;    //星灵队伍
  PET_TEMP_BOX = 11;   //临时盒子
  PET_HOME = 104;  //据点（终端）
}

//饰品背包类型
enum StarPOrnamentBackPackType{
  ORNAMENT_EQUIP = 5; // 装备背包
  ORNAMENT_BACKPACK = 8; // 饰品背包
}

//星灵的物品类型
enum StarPItemType {
  ITEM_ORNAMENT_TYPE = 14; // 饰品类型
  ITEM_PET_TYPE = 23; // 星灵类型
}

//世界箱子类型
enum StarPWorldBoxType{
  WORLD_BOX_STORAGE_BOX = 100;      //储物箱
  WORLD_BOX_FEED_BOX = 101;    //饲料箱
  WORLD_BOX_INCUBATOR_BOX = 102;//孵化箱
  WORLD_BOX_BREEDING_FRAMS = 103;//繁育场
  WORLD_BOX_TERMINAL = 104;//终端
  WORLD_BOX_REFRIGERATED_BOX = 105;//冷冻箱
  WORLD_BOX_FRIDGE_BOX = 106;//电冰箱
  WORLD_BOX_TURRET_AMMUNITION_BOX = 107;//炮塔弹药箱
}

//idip修改处理类型
enum IdipStarPModifyType{
  //啾灵相关每个模块预留50个属性, 如果清楚参数可以在这里查看 参数全部类型string，根据需要转换
  IDIP_STARP_PET_MODIFY_LEVEL = 1;          //修改星兽等级【 宠物uid(int64):宠物等级level(int32) 】
  IDIP_STARP_SUB_ITEM_NUM = 2;            //减少道具数量【 道具itemId(int32):道具实例化itemInstanId:减少数量num(int32) 】
  IDIP_STARP_PET_CHANGE_PASSIVESkill = 3;             //修改啾灵被动技能【 宠物uid(int64):技能index(int32):技能ID(int32) 】
  IDIP_STARP_PET_CHANGE_STARLEVEL = 4;                //修改啾灵星级【 宠物uid(int64):星级level(int32)】
  //账号相关
  IDIP_STARP_ACCOUNT_MODIFY_POWER = 50;        //修改账号体力

  //场景数据
  IDIP_STARP_ADD_ITEM_TO_WORLD_BOX = 100;        //给世界箱子添加道具(仅支持非实例道具)【 背包backpackId(int64):道具配置itemId(int32):增加数量num(int32) 】
  IDIP_STARP_SUB_ITEM_TO_WORLD_BOX = 101;        //给世界箱子扣除道具【 背包backpackId(int64):道具配置itemId(int32):道具实例itemInstanId(int64):扣除数量num(int32) 】

  //角色相关
  IDIP_STARP_OPER_EQUIPATTR = 150;                    //背包装备属性操作(修改， 删除， 增加)
  IDIP_STARP_USER_ADD_EXP = 151;            //给玩家添加经验【 增加数量num(int32) 】
  IDIP_STARP_USER_SUB_EXP = 152;            //给玩家减少经验【 减少数量num(int32) 】
}

// SP-组取消确认进入副本原因
enum StarPEnterCancelType{
  SP_ECT_CANCEL = 0;  // 玩家主动取消
  SP_ECT_TIMEOUT = 1;  // 超时取消
  SP_ECT_OTHER = 2;  // other
  SP_ECT_OOFFLINE = 3;  // 离线取消
}

// SP 错误源：由于不同的错误源的错误错误码可能重复
enum StarPMailErrSourceType {
  STARP_MAIL_ERR_SOURCE_SYSTEM = 1; // 系统错误
  STARP_MAIL_ERR_SOURCE_LOGIC = 2; // 业务逻辑错误
  STARP_MAIL_ERR_SOURCE_TCAPLUSDB = 3; // TcaplusDB报告的错误
}

// SP邮件来源
enum StarPMailSourceType {
  STARP_MAIL_SOURCE_TYPE_NORMAL = 1; // 一般
  STARP_MAIL_SOURCE_TYPE_SYSTEM = 2; // 系统
  STARP_MAIL_SOURCE_TYPE_IDIP = 3; // IDIP
  STARP_MAIL_SOURCE_TYPE_GM = 4; // GM
}

// SP邮件类型
enum StarPMailType {
  STARP_MAIL_TYPE_PLAYER_MAIL = 1; // 角色邮件
  STARP_MAIL_TYPE_FORCE_MAIL = 2; // 强提示邮件
}

// SP邮件状态
enum StarPMailStatus {
  STARP_MAIL_STATUS_UNREAD = 1; // 未读
  STARP_MAIL_STATUS_READ = 2; // 已读
}

// SP邮件范围
enum StarPMailScope {
  STARP_MAIL_SCOPE_UID = 1; // 账号范围内
  STARP_MAIL_SCOPE_ROLEID = 2; // 角色范围内
  STARP_MIAL_SCOPE_STARPID = 3; // 房间范围内
}

//SP离线任务类型
enum StarPOfflineTaskType {
  STARP_OFFLINE_TASK_MAIL = 1;    // 发送邮件类型
}

//SP无缓存数据类型
enum StarPCommonNoCacheType {
  STARP_COMMON_NO_CACHE_OFFLINE_TASK = 1;// 离线任务数据
}

// 星灵飞船的动作类型
enum StarPShipActionType {
  SP_Ship_Enter = 1 ;     // 进入星灵飞船
  SP_Ship_Leave = 2 ;   // 离开星灵飞船
  SP_Ship_Heart_Beat = 3 ; //在星灵飞船心跳(半小时请求一次)
}

// 星灵飞船的动作类型
enum StarPEnterReason {
  SP_Enter_Normal = 1 ;     // 正常进入
}

// 玩家行为
enum EnmStarPPlayerTLogStatus {
  ENM_SPPLAYER_CONNECTED = 0;  // 玩家连接
  ENM_SPPLAYER_RECONNECTED = 1;  // 玩家重连
  ENM_SPPLAYER_OFFLINE = 2;  // 玩家掉线
  ENM_SPPLAYER_QUIT = 3;  // 玩家退出
  ENM_SPPLAYER_KICK = 4;  // 玩家被踢
}

//农家乐-拜访获取道具的信息
message SPVisitItem {
    optional int64 itemId = 1;  //道具ID
    optional int32 itemCount = 2; //道具数量
}

//农家乐-拜访结算数据定义
message SPCropTheft {
    optional int32 sign = 1;  // 用于区分正常结算还是兜底机制触发的结算
    optional int64 recordId = 2; // 访客临时数据的唯一自增ID,通过该ID判断消息是否重复消费
    optional int64 starPId  = 3; //拜访的世界ID
    optional int64 visitedUid = 4; //拜访的好友uid
    repeated SPVisitItem cropItems = 5; //偷取的作物列表
    repeated SPVisitItem blessItems = 6; //祈福获得的道具列表
}

//SP商店宿主类型
enum SPShopHostType {

  SP_SHOP_HOST_TYPE_WORLD_NPC = 1; //NPC
  SP_SHOP_HOST_TYPE_TRADE_BUILDING = 2; //贸易站建筑
  SP_SHOP_HOST_TYPE_BASE_BUILDING = 3; //基地商店
}
// 任务失败原因
enum EnmStarPTaskAcceptFailedReason {
  ENM_SPTASK_UNKNOWN = 0;				// 未知错误
  ENM_SPTASK_CONF_NOT_FOUND = 1;  		// 任务配置不存在
}

// 副本行为
enum EnmStarPDungeonTLogStatus {
  ENM_SPDUNGEON_OPEN              		= 0;  // 副本创建
  ENM_SPDUNGEON_FINISH	    			= 1;  // 副本结束
  ENM_SPDUNGEON_UNLOAD             		= 2;  // 副本销毁
}

// 玩家副本行为
enum EnmStarPDungeonPlayerTLogStatus {
  ENM_SPDUNGEON_PLAYER_ENTER            		= 0;  // 玩家进入副本
  ENM_SPDUNGEON_PLAYER_QUIT	    			= 1;  // 玩家退出副本
}

// StarPWorld房间删除角色操作类型
enum EnmStarPRoomDelRoleOperation {
  ENM_SPROOM_DELROLE_ADMIN_FROCE            = 0;  // 管理员强制删除
  ENM_SPROOM_DELROLE_ADMIN	    			= 1;  // 管理员删除玩家
  ENM_SPROOM_DELROLE_BY_SELF	    		= 2;  // 玩家删除自己
  ENM_SPROOM_NON_DEL_OPERATION	    		= 3;  // 非删除操作（Tlog上报角色状态新增）
}

// StarPWorld房间管理员变更操作类型
enum EnmStarPRoomAdminOperation {
  ENM_SPROOM_ADMIN_TRANSFER           		= 0;  // 管理员给指定成员
  ENM_SPROOM_ADMIN_APPLY	    			= 1;  // 其他玩家申请成为管理员
}

// StarPWorld房间封禁用户操作类型
enum EnmStarPRoomBanOperation {
  ENM_SPROOM_BAN_ENTER           		= 0;  // 管理员封禁用户
  ENM_SPROOM_BAN_CANCEL	    			= 1;  // 管理员取消封禁
}

// 队伍模块更新starPInfo的类型
enum EnmStarPTeamUpdateType {
  ENM_STARP_TEAM_UPDATE_STATUS           		= 0;  // 更新啾灵队伍成员的状态
}

// 玩家登录监控状态的类型
enum EnmStarPUserLoginType {
  ENM_STARP_USER_LOGIN_GAMESVR_STARTPENTER                  = 1;      // gamesvr           第一步
  ENM_STARP_USER_LOGIN_STARPACCOUNTSVR_RPCSTARPUSERENTER    = 2;      // starpaccountsvr   第一步
  ENM_STARP_USER_LOGIN_STARPACCOUNTSVR_REQSTARPWORLDENTER   = 3;      // starpaccountsvr   第二步
  ENM_STARP_USER_LOGIN_STARPSVR_RPCSTARPWORLDENTER          = 4;      // starpsvr          第一步
  ENM_STARP_USER_LOGIN_STARPSVR_ENTER                       = 5;      // starpsvr          第二步
  ENM_STARP_USER_LOGIN_STARPSVR_ENTER_DS                    = 6;      // starpsvr          第三步
}

// 时间刷新模块
enum EnmTimeRefreshModule {
  ENM_STARP_WORLD_REFRESH = 0;  // starpsvr
  ENM_STARP_ACCOUNT_PLAYER_REFRESH = 1;  // starpaccountsvr-player
  ENM_STARP_GAME_PLAYER_REFRESH = 2;    //starpgamesvr
  ENM_STARP_GROUP_REFRESH = 3;    //starpgroupsvr
  ENM_STARP_GUILD_REFRESH = 4;    // starpguildsvr
}

// StarPWorld错误信息tlog类型
enum EnmStarPWorldErrorInfoTLogType {
  ENM_STARP_BATTLE_SVR_DS_CPU_MAX = 1;                        //battlesvr的ds的cpu资源不足
  ENM_STARP_ROOM_SVR_DB_VERSION_ERROR = 2;                    //组队pve的roomsvr写db发生乐观锁冲突
  ENM_STARP_BATTLE_SVR_DB_VERSION_ERROR = 3;                  //组队pve的battlesvr写db发生乐观锁冲突
  ENM_STARP_ROOM_SVR_DB_VERSION_ERROR_WITHOUT_RETRY = 4;      //组队pve的roomsvr乐观锁写db失败但没有重试
  ENM_STARP_BATTLE_SVR_DB_VERSION_ERROR_WITHOUT_RETRY = 5;    //组队pve的battlesvr乐观锁写db失败但没有重试
}

enum EnmStarPEsBulkReason {
  ENM_STARP_ES_BULK_BY_TICK = 1;
  ENM_STARP_ES_BULK_BY_STARP_RELEASE = 2;
}

enum EnmStarPEsWorldCustomOpen {
  ENM_STARP_ES_WORLD_CUSTOM_INVALID = 0;
  ENM_STARP_ES_WORLD_CUSTOM_OPEN = 1;
  ENM_STARP_ES_WORLD_CUSTOM_CLOSE = 2;
}

enum EnmStarPShopType {
  ENM_STARP_SHOP_TYPE_BEGIN = 0;                            // 商店类型起始值

  ENM_STARP_SHOP_ADVENTURE = 1;                             // 冒险团商店

  ENM_STARP_SHOP_TYPE_END = 2;                              // 商店类型结束值
}

enum StarPDsBaseInfoTimeoutSeasonType {
  SP_DS_BASEINFO_TINEOUT_WAIT = 1;                    // 在等待状态超时(基类)
  SP_DS_BASEINFO_TINEOUT_HEARTBEAT = 2;               // 心跳超时(基类)
  SP_DS_BASEINFO_TINEOUT_EXCEED_MAX_RUN_TIME = 3;     // 超过最大运行时间5分钟(基类)
  SP_DS_BASEINFO_TINEOUT_MIGRATE = 4;                 // 超过最大迁移时间(基类)
  SP_DS_SPBASEINFO_TINEOUT_HEARTBEAT = 5;             // 心跳超时(子类)
  SP_DS_SPBASEINFO_TINEOUT_EXCEED_MAX_RUN_TIME = 6;   // 超过最大迁移时间(子类)
}

enum StarPTerminateDSType {
  SP_TERMINATE_DS_MIGRATE = 1;                        // 即将迁移，终止旧的ds(基类)
  SP_TERMINATE_DS_FORCE_RECYCLE = 2;                  // 强制回收ds(基类)
  SP_TERMINATE_DS_SP_MIGRATE = 3;                     // 即将迁移，终止旧的ds(子类)
  SP_TERMINATE_DS_SP_FORCE_RECYCLE_OLD = 4;               // 强制回收旧ds(子类)
  SP_TERMINATE_DS_SP_FORCE_RECYCLE_NEW = 5;               // 强制回收新ds(子类)
  SP_TERMINATE_DS_SP_NORMAl_RECYCLE = 6;                  // 正常回收ds(子类)
}

// 宠物类型
enum StarPPetFormationType {
  STARP_PET_FORMATION_TYPE_COMBAT = 1; // 战斗
  STARP_PET_FORMATION_TYPE_RIDE = 2; // 乘骑
}

enum StarPSettleType {
  SP_SETTLE_TYPE_BASE_PVE_DUNGEON = 1; // 基础pve副本
  SP_SETTLE_TYPE_POI = 2; // POI
}

enum StarPAssistOrderStatus {
  SP_ASSIST_ORDER_STATUS_NONE = 0; // 无效状态
  SP_ASSIST_ORDER_STATUS_PROCESSED = 1; // 订单生效中
  SP_ASSIST_ORDER_STATUS_WAIT_FINISH = 2; // 订单待完成
  SP_ASSIST_ORDER_STATUS_FINISHED = 3; // 订单已完成
}

message StarPAssistOrderMaterialInfo {
  optional int64 orderId = 1; // 订单编号ID
  repeated proto_StarPAssistMaterialInfo materialInfo = 2; // 订单材料信息
  optional int64 ownerUid = 3; // 订单拥有者UID
}

message StarPFindAssistOrderCSMsg {
  optional int64 uid = 1;
  optional int64 orderId = 2;
}

message StarPAssistOrderCSInfo {
  optional int64 orderId = 1; // 订单编号
  repeated int64 assistLikedUids = 2; // 已点赞玩家
  optional int32 orderStatus = 3; // 订单状态
  optional int64 orderSubmitMs = 4; // 订单提交时间
  optional int32 materialId = 5; // 材料ID
  optional int32 materialNumLimit = 6; // 材料数量上限
  optional int32 materialNum = 7; // 已捐助材料数量
}

message StarPPlayerSingleAssistInfo {
  optional int64 assistMs = 1; // 捐助时间
  optional int64 uid = 2; // 捐助玩家uid
  optional int64 orderId = 3; // 订单ID
  optional int32 assistNum = 4; // 材料捐助数量
  optional int32 status = 5; // 领取状态（1：已领取）
}

message StarPAssistOrderStatusInfo {
  optional int64 orderId = 1; // 订单编号ID
  optional int32 orderStatus = 2; // 订单状态 StarPAssistOrderStatus
}


message StarPPlayerAssistOrderCSInfo {
  repeated StarPAssistOrderCSInfo orderInfo = 1; // 订单信息
  repeated StarPPlayerSingleAssistInfo assistInfo = 2; // 捐助信息
}


enum AddItemsWithBoxsType {
	MoveToBox_Success = 1;   //成功
	MoveToBox_NoBox = 2;    //没有箱子
	MoveToBox_BoxFull = 3;    //箱子满了
	MoveToBox_BagFull = 4;    //背包满了
	MoveToBox_TypeCannotMove = 5;    //道具不能放入储物箱
}

// StarP商店购买信息tlog类型
enum EnmStarPShopBuyInfoTLogType {
  ENM_STARP_PLAYER_SHOP_BUY_SUCC = 1;                        //购买成功
  ENM_STARP_PLAYER_SHOP_BUY_DECREASE_FAIL = 2;               //扣除资源失败
  ENM_STARP_BATTLE_UPDATE_BUY_TIMES_FAIL = 3;                //更新购买次数失败
  ENM_STARP_BATTLE_SEND_REWARD_MAIL_FAIL = 4;                //发送奖励邮件失败
}

// StarP啾灵交换订单枚举类型
enum EnmStarPPetOrderState {
  ENM_STARP_PET_ORDER_NONE = 1;                           // 无效状态
  ENM_STARP_PET_ORDER_NEED_AGREE = 2;                     // 待同意
  ENM_STARP_PET_ORDER_AGREE = 3;                          // 已同意
  ENM_STARP_PET_ORDER_FINISH = 4;                         // 已完成
  ENM_STARP_PET_ORDER_CANCEL = 5;                         // 已取消
  ENM_STARP_PET_ORDER_TIMEOUT = 6;                        // 已过期
}

// StarP GeneralData数据类型枚举
enum EnmStarPGeneralDataType {
  ENM_STARP_GENERAL_TYPE_NONE = 1;                           // 无效类型
  ENM_STARP_GENERAL_TYPE_PET_ORDER = 2;                      // 啾灵交换订单
  ENM_STARP_GENERAL_TYPE_MATERIAL_ASSIST_ORDER = 3;          // 材料捐助订单
  ENM_STARP_GENERAL_TYPE_PET_TRADE_INFO = 4;                 // 啾灵交换玩家数据
  ENM_STARP_GENERAL_TYPE_FRIEND_INTIMACY = 5;                 // 啾灵交换玩家数据
}

// StarP Group的Uid和GroupID映射DB记录的状态
enum EnmStarPGroupUidGroupIDDBRecordStatus {
  ENM_STARP_GROUP_UID_GROUPID_DB_RECORD_STATUS_PREPARE = 1; // 预占位状态
  ENM_STARP_GROUP_UID_GROUPID_DB_RECORD_STATUS_NORMAL =  2; // 正常加入状态
}

enum EnmStarPBaseGroupType {
  ENM_STARP_BASEGROUP_TYPE_GUILD = 1;                       // 组织类型部落
  ENM_STARP_BASEGROUP_TYPE_GROUP = 2;                       // 组织类型宗门
}

enum EnmStarPBaseGroupJoinApplyStatus {
  ENM_STARP_BASEGROUP_JOIN_APPLY_STATUS_APPLYING = 1; // 申请中
  ENM_STARP_BASEGROUP_JOIN_APPLY_STATUS_ACCEPT = 2; // 已同意
  ENM_STARP_BASEGROUP_JOIN_APPLY_STATUS_REJECT = 3; // 已拒绝
}

enum StarPGsIDType {
  GS_ID_TYPE_NONE       = 0;
  GS_ID_TYPE_UID        = 1;   	//账号ID
  GS_ID_TYPE_ROLE_ID    = 2;	  //角色ID
}

enum StarPGsCommonDbType {
  GS_COMMON_DB_TYPE_NONE                  = 0;
  GS_COMMON_DB_TYPE_ADVENTURE_BASE        = 1;
  GS_COMMON_DB_TYPE_ADVENTURE_SHOP        = 2;
  GS_COMMON_DB_TYPE_ADVENTURE_LOTTERYINFO = 3;
  GS_COMMON_DB_TYPE_SOCINTERACTIONRATIO   = 4;
  GS_COMMON_DB_TYPE_FRIEND_INTIMACY       = 5;
  GS_COMMON_DB_TYPE_PETTRADE_FOCUSINFO    = 6;
}

enum EnmStarPBaseGroupJoinInviteStatus {
  ENM_STARP_BASEGROUP_JOIN_INVITE_STATUS_INVITING = 1; // 邀请中
  ENM_STARP_BASEGROUP_JOIN_INVITE_STATUS_ACCEPT = 2; // 已同意
  ENM_STARP_BASEGROUP_JOIN_INVITE_STATUS_REJECT = 3; // 已拒绝
}

// StarP 组织成员被房主踢掉后的操作类
enum EnmStarPBaseGroupRoomKickOperationType {
  ENM_STARP_BASEGROUP_ROOM_KICK_OPERATION_UNKNOWN = 0;    // 未知
  ENM_STARP_BASEGROUP_ROOM_KICK_OPERATION_MEMBER = 1;     // 将普通成员踢出
  ENM_STARP_BASEGROUP_ROOM_KICK_OPERATION_CREATOR = 2;    // 踢出创建者，转移归属权
  ENM_STARP_BASEGROUP_ROOM_KICK_OPERATION_DISMISS = 3;    // 完全解散
}

// 啾灵-StopGame原因定义
enum EnmStarPGroupStopGameReason {
  ENM_STARP_GROUP_STOP_GAME_REQUEST = 1;  // SPGroup侧主动请求StopGame
  ENM_STARP_GROUP_STOP_GAME_TIMEOUT = 2;  // SPGroup侧超时重试StopGame
  ENM_STARP_GROUP_STOP_GAME_DS_ASK = 3;  // DS侧主动请求StopGame
  ENM_STARP_GROUP_MIGRATE_STOP_GAME_REQUEST = 4;  // SPGroup侧主动请求StopGame
}

// SP敏感词检测场景定义
enum EnmStarPTssCheckScene {
  SPTSS_DEFAULT = 0;  //
  SPTSS_GROUP = 1;  // 宗门
}

// SP宗门的加入类型
enum EnmStarPGroupJoinType {
  SPGROUP_JOIN_DEFAULT = 0;  // 需要审批
  SPGROUP_JOIN_FREE = 1;  // 自由加入，无需审批
  SPGROUP_JOIN_MAX = 2;  //
  SPGROUP_JOIN_ALL = 10;  // 包含所有类型
}

// SP宗门的发布类型
enum EnmStarPGroupPublishType {
  SPGROUP_PUBLISH_DEFAULT = 0;  // 公开，推荐列表可见
  SPGROUP_PUBLISH_PRIVATE = 1;  // 私密
  SPGROUP_PUBLISH_MAX = 2;  //
}

// SP宗门的职位类型
enum EnmStarPGroupTitleType {
  SPGROUP_TITLE_DEFAULT = 0;  // 普通成员
  SPGROUP_TITLE_LEADER = 1;  // 宗主
  SPGROUP_TITLE_ELDER = 2;  // 长老
}

// 啾灵-啾灵交换订单取消或超时时返还啾灵
message SPPetTradeReturnPet {
  optional int64 uid = 1;
  optional proto_StarPPetTradeInfo pet = 2;
}

// 部落创建离线消息结构
message SPGuildCreate{
  optional int64 guildId = 1; // 部落ID
  optional int64 starpId = 2; // 房间ID
  optional int64 uid = 3; // 创建玩家uid
  optional int64 optTime = 4; // 操作时间
}

// 部落成员加入离线消息结构
message SPGuildMemberJoin{
  optional int64 guildId = 1; // 部落ID
  optional int64 starpId = 2; // 房间ID
  optional int64 uid = 3; // 加入玩家uid
  optional int64 optTime = 4; // 操作时间
}

// 部落成员退出离线消息结构
message SPGuildMemberLeave{
  optional int64 guildId = 1; // 部落ID
  optional int64 starpId = 2; // 房间ID
  optional int64 uid = 3; // 退出玩家uid
  optional int64 optTime = 4; // 操作时间
  optional int64 reason = 5; // 退出原因
}

// 部落解散离线消息结构
message SPGuildDisMiss{
  optional int64 guildId = 1; // 部落ID
  optional int64 starpId = 2; // 房间ID
  optional int64 uid = 3; // 解散玩家uid
  optional int64 optTime = 4; // 操作时间
  optional bool isNeedInherit = 5; // true, 主动解散. false， 房间级别的离开(被踢等等)
}

// 部落改名离线消息结构
message SPGuildChangeName{
  optional int64 guildId = 1; // 部落ID
  optional int64 starpId = 2; // 房间ID
  optional int64 uid     = 3; // 操作玩家uid
  optional string name   = 4; // 新名字
  optional int64 optTime = 5; // 操作时间
}

// 部落成员职务变更离线消息结构
message SPGuildChangeTitle{
  optional int64 guildId = 1; // 部落ID
  optional int64 starpId = 2; // 房间ID
  optional int64 uid     = 3; // 操作玩家uid
  optional int64 memberUid = 4; // 被操作玩家uid
  optional int32 title = 5;      // 修改的职务
  optional int64 optTime = 6; // 操作时间
}

// 申请加入部落离线消息结构
message SPGuildMemberApply{
  optional int64 guildId = 1; // 部落ID
  optional int64 starpId = 2; // 房间ID
  optional proto_StarPGuildApplicationData data = 3;  // 申请信息
}

// 邀请加入部落离线消息结构
message SPGuildMemberInvite{
  optional int64 guildId    = 1; // 部落ID
  optional int64 starpId    = 2; // 房间ID
  optional proto_StarPGuildInvitationData data = 3; // 邀请信息
}

// 部落建造效率变化
message SPGuildEfficiencyRateChange{
  optional int64 guildId        = 1; // 部落ID
  optional int64 starpId        = 2; // 房间ID
  optional int32 efficiencyRate = 3; // 效率
}

// 将指定道具退还给玩家
message SPTerminalPetBack {
  optional int64 itemInstId = 1;    // 道具实例id
  optional int64 spuid = 2;         // 角色id
  optional int64 guildBillno = 3;   // 进入部落时的流水
  optional int64 guildId = 4;       // 部落id
}

// 将指定道具退还给玩家list
message SPTerminalPetBackList {
  repeated SPTerminalPetBack petList = 1; // 退还列表
}

// 玩家宗门信息变更
message SPPlayerGroupUpdateInfo {
  optional int64 uid            = 1; // 玩家id
  optional int64 enterGroupId   = 2; // 进入宗门的id
  optional int32 enterGroupType = 3; // 进入宗门的类型
  optional int64 exitGroupId    = 4; // 退出宗门的id
  optional int32 exitGroupType  = 5; // 退出宗门的类型
  optional string enterGroupName = 6; // 进入宗门的名称
  optional int64 roleId         = 7; // 角色id
  optional int32 title          = 8; // 职务
  optional int64 joinTime       = 9; // 加入时间
}

// SP宗门申请记录的查看者类型
enum EnmStarPGroupQueryType {
  SPGROUP_QUERY_NORMAL = 0;  // 散人
  SPGROUP_QUERY_MEMBER = 1;  // 成员
}

// SP DS世界类型
enum EnmStarPDsWorldType {
  STRAP_DS_WORLD_TYPE_UNKNOW = 0;       // 未知
  STRAP_DS_WORLD_TYPE_STARPROOM = 1;    // 啾灵房间
  STRAP_DS_WORLD_TYPE_STARPGROUP = 2;   // 宗门
}

enum StarPBuildingOwnerType {
  SP_BOT_PERSONAL = 1; // 个人
  SP_BOT_GUILD = 2; // 部落
}

enum FlashEggType {
  NotFlash = 1;  //不闪
  NormalFlash = 2;  //普通闪
  RedFlash = 3;  //红闪
  BlueFlash = 4;  //蓝闪
}

message StarPFriendIntimacyCSInfo {
  optional int64 friendUid          = 1; // 好友UID
  optional int64 intimacyExp        = 2; // 亲密度经验
  optional int64 recentInteractMs   = 3; // 最近交互时间（亲密度增长）
}

// 定义在SPFunctionControlUtil.lua::FuncID
enum StarPFunctionId {
  StarP_Function_StarPGroup = 125; // 宗门系统
  StarP_Function_StarPGuild = 126; // 部落系统
}

enum StarPGroupJoinSourceType {
  StarP_Group_Join_Source_None = 0; // 默认
  StarP_Group_Join_Source_Recommend_List = 1; // 推荐列表
  StarP_Group_Join_Source_Friend = 2; // 好友
  StarP_Group_Join_Source_Search = 3; // 名字搜索
}

message StarPGuildPublicInfo {
  optional int64 guildId = 1;
  optional proto_StarPGuildWishStatueData wishStatueData = 2;
}

enum EnumStarPCardType {
  STARP_CARD_TYPE_WORLD = 1;        // 房间
  STARP_CARD_TYPE_GROUP = 2;        // 宗门
  STARP_CARD_TYPE_GUILD = 3;        // 部落
  STARP_CARD_TYPE_PETTRADE = 4;     // 啾灵交换
}

enum StarPGuildLeaveSeason {
  STARP_GUILD_LEAVE_SELF = 1;     // 主动退出
  STARP_GUILD_LEAVE_KICK = 2;     // 部落会长踢出
  STARP_GUILD_LEAVE_DISMISS = 3;  // 部落会长解散部落
  STARP_GUILD_LEAVE_ROOM_KICK = 4;// 房间房主踢人
}

message StarPPlayerSocInteractionRatioInfo {
  optional int64 uid = 1;         // 好友uid
  optional int32 inactiveDays = 2;        // 不活跃天数
  optional int32 isSocInteract = 3;       //是否Soc互动，0：否，1：是
}

message StarPSocInteractionInfo {
  optional int64 uid = 1;
  optional int32 guildSocInteractSign = 2; // 部落互动标记，0：未互动，1：互动
  optional int32 personalSocInteractSign = 3; //个人互动标记，0：未互动，1：互动
}

// ugc共创多人编辑申请答复结果
enum CoCreateMultiEditReplyResult {
  REPLY_RESULT_REJECT = 1;			      // 答复结果-拒绝(给申请者推送答复)
  REPLY_RESULT_ACCEPT = 2;			      // 答复结果-接受(共创多人编辑申请玩家预占位)
  REPLY_RESULT_SELECT_MODE = 3;		    // 答复结果-选择编辑模式(给申请者推送答复)
}

// ugc地图编辑模式
enum UgcMapEditMode {
  EDIT_MODE_SINGLE = 0;	              // 编辑模式-单人
  EDIT_MODE_MULTI_SCENE = 1;	        // 编辑模式-多人模式中的场景模式
  EDIT_MODE_MULTI_CODING = 2;	        // 编辑模式-多人模式中的扣叮模式
}

// ugc共创多人编辑申请答复拒绝理由
enum CoCreateMultiEditRejectReason {
  REJECT_REASON_DIRECT_REJECT = 1;	  // 拒绝理由-直接拒绝
  REJECT_REASON_NO_SELECT_MODE = 2;	  // 拒绝理由-未选择编辑模式
  REJECT_REASON_EDIT_NUM_FULL = 3;    // 拒绝理由-编辑人数已满
}

// ugc共创多人编辑扣叮数据信息
message CodingDataInfo {
  repeated UgcMapMetaInfo metaInfos = 1;	// 数据信息列表
  optional int64 updateTime = 2;			    // 数据更新时间
}

message BattleSceneBaseInfo {
  optional int64 gameSessionId = 1;  // gameSessionId
  optional int64 sceneId = 2;  // 场景id
  optional int32 sceneType = 3;  // 场景类型
}

message BattleSceneBaseList {
  repeated BattleSceneBaseInfo battleList = 1;
  optional BattleSceneBaseInfo firstBattle = 2;  // 第一个对局
}

message ChangeBattleSceneResult {
  optional int64 uid = 1;
  optional int32 errCode = 2;  // 进入目标场景的错误码 参考BattleSceneChangeResultCode
  optional int64 targetSceneId = 3;  // 目标场景
}

message BattleSceneBriefDBUgc {
  optional UgcSceneBriefInfo ugcBriefInfo = 1;
}

message BattleSceneBriefDB {
  optional int32 type = 1;  // 记录数据类型
  optional BattleSceneBriefDBUgc ugcInfo = 2;  // ugc的信息

  optional MatchRuleInfo matchRuleInfo = 20;  // ruleInfo信息
}

// 推荐页枚举
enum ERecommendPageType {
  ERecommendPageType_Weekly = 0; // 每周必玩
  ERecommendPageType_Monthly = 1; // 每月必玩
  ERecommendPageType_Collection = 2;
  ERecommendPageType_HallOfFame = 3; // 名图堂
}

//DS场景类型
enum LayerType {
  UgcLayerType_Unknown = 0;  //未知
  UgcLayerType_Common = 1;   //普通图层  可以开多个 可以被销毁 不能中途加入
  UgcLayerType_Only = 2;     //唯一图层  不能开多个 可以被销毁 可以中途加入
  UgcLayerType_Stay = 3;     //保留图层  不能开多个 不能被销毁 可以中途加入
}

message BattleSceneStatusDB {
  optional int32 battleSceneStatus = 1;
  optional int64 battleSceneStatusStart = 2;
}

message UgcEntranceBubblesData{
  map<int32, UGCEntranceBubble> bubbles = 1; // key: UgcStarWorldEntranceBubbleType, value: 气泡
}

// 星世界入口气泡
message UGCEntranceBubble {
  optional int32 bubble_type = 1; // 气泡类型 UgcStarWorldEntranceBubbleType
  optional int32 bubble_id = 2; // 气泡ID
  optional int64 expire_time = 3; // 过期时间
  optional int32 tips_type = 4; // 样式
  optional string tips_text = 5; // 气泡文本
  optional string tips_icon = 6; // 气泡图标
  optional int32 tips_icon_type = 7; // 气泡图标类型
  optional string tips_background = 8; // 气泡背景底色
  optional string jump_id = 9; // 跳转ID
  optional string jump_params = 10; // 跳转参数
}

enum FarmReportType {
  FRT_None = 0;
  FRT_Stealing = 1;
}

enum CookRecruitmentMarketCandidateType {
  CRMCT_Normal = 0;
  CRMCT_High = 1;
}

// ugc资产纹理风格类型
enum UgcResTextureStlyeType {
  UGCTST_Universal = 0;         // 纹理风格-通用
  UGCTST_Cartoon = 1;           // 纹理风格-卡通
  UGCTST_ChineseStyle = 2;      // 纹理风格-中国风
  UGCTST_BlueWhitePottery = 3;  // 纹理风格-青花瓷
  UGCTST_StoneCarving = 4;      // 纹理风格-石雕
  UGCTST_Cyberpunk = 5;         // 纹理风格-赛博朋克
}

// ugc资产体素风格类型
enum UgcResVoxelStyleType {
  UGCVST_Default = 0;           // 体素风格-默认
  UGCVST_Voxel = 1;             // 体素风格-体素
  UGCVST_LowPolygon = 2;        // 体素风格-低多边形
}

// 修改匹配中队伍的信息字段类型
enum MatchTeamInfoModifyFieldType {
  MTIMFT_Unknown = 0;
  MTIMFT_SideId = 1;
}

// 修改匹配中队伍的信息内容
message MatchTeamInfoModificationField {
  oneof field_value {
    int32 side_id = 1;
  }
}

message UgcMatchLobbySummery_Config {
  optional int64 configId = 1;  // 配置id
  optional int64 openTime = 2;  // 开启时间
  optional int64 closeTime = 3;  // 结束时间
  optional int32 priority = 4;  // 优先级
  optional UgcMatchLobbyMapLabel label = 5;  // 标签
  optional int32 modeTypeId = 6;  // 客户端接入用星世界排序玩法id
  optional int32 pubItemsCount = 7;  // 个数数据
  optional int32 briefExtraType = 8;  // 参考UgcMatchLobbyMapBriefExtraType
}

message UgcMatchLobbySummery_UgcInfo {
  optional int64 ugcId = 1;
  optional string name = 2;
  repeated UgcMapMetaInfo metaInfo = 3;
  optional string bucket = 4;
  optional string region = 5;
  optional int64 creatorId = 6;
}

message UgcMatchLobbySummery {
  optional UgcMatchLobbySummery_Config config = 1;  // 配置信息
  optional UgcMatchLobbySummery_UgcInfo ugcInfo = 2;  // ugc基础信息
}

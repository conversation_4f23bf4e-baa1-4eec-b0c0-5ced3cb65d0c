<root>
  <entry id="1" name="ClubCacheReq" serverName="noserver"/>
  <entry id="2" name="PlayerPublicProfileReq" serverName="noserver"/>
  <entry id="3" name="FriendLuckyMoneyDataReq" serverName="noserver"/>
  <entry id="4" name="RankEntryListReq" serverName="noserver"/>
  <entry id="5" name="RankEntryReq" serverName="noserver"/>
  <entry id="6" name="UgcPlayerSubReq" serverName="noserver"/>
  <entry id="7" name="UgcPlayerFanReq" serverName="noserver"/>
  <entry id="8" name="UgcPlayerInfoReq" serverName="noserver"/>
  <entry id="9" name="UgcPassInfoReq" serverName="noserver"/>
  <entry id="10" name="UgcDataReq" serverName="noserver"/>
  <entry id="11" name="CacheUsedSeqReq" serverName="noserver"/>
  <entry id="12" name="PlayerBanInfoReq" serverName="noserver"/>
  <entry id="13" name="PlayerPublicReq" serverName="noserver"/>
  <entry id="14" name="UgcGroupListReq" serverName="noserver"/>
  <entry id="15" name="UgcPlayerOperateReq" serverName="noserver"/>
  <entry id="16" name="IDCLoadStatusListReq" serverName="noserver"/>
  <entry id="17" name="NicknameRecordReq" serverName="noserver"/>
  <entry id="18" name="UgcCreatorDailyReportReq" serverName="noserver"/>
  <entry id="19" name="UgcSharedProfileReq" serverName="noserver"/>
  <entry id="20" name="RecvFriendLuckyMoneyDataReq" serverName="noserver"/>
  <entry id="21" name="FriendApplyReq" serverName="noserver"/>
  <entry id="22" name="UserBindPackageCacheReq" serverName="noserver"/>
  <entry id="23" name="UgcThirdPartyTaskInfoReq" serverName="noserver"/>
  <entry id="24" name="UgcMatchLobbyMapBriefInfoReq" serverName="noserver"/>
  <entry id="25" name="UgcResHomePageCacheReq" serverName="noserver"/>
  <entry id="26" name="UgcResBriefKeyCacheReq" serverName="noserver"/>
  <entry id="27" name="UgcMatchLobbyMapBriefInfoAllReq" serverName="noserver"/>
  <entry id="28" name="FriendIntimacyUpdateNtf" serverName="noserver"/>
  <entry id="29" name="UgcMulTestSaveMetaReq" serverName="noserver"/>
  <entry id="30" name="IntellectualActivityReq" serverName="noserver"/>
  <entry id="31" name="IntellectualActivitiyIdsReq" serverName="noserver"/>
  <entry id="32" name="PlatHotResConfigReq" serverName="noserver"/>
  <entry id="33" name="FriendKungFuPandaHelpRacingDataReq" serverName="noserver"/>
  <entry id="34" name="CacheKungFuPandaRacingRankInfoReq" serverName="noserver"/>
  <entry id="35" name="ReceiveGiftNtf" serverName="noserver"/>
  <entry id="36" name="RankCoordinationProgressReq" serverName="noserver"/>
  <entry id="37" name="PlatHotResConfigSyncReq" serverName="noserver"/>
  <entry id="38" name="PlatHotResConfigSyncRes" serverName="noserver"/>
  <entry id="39" name="BlackMarketStoreReq" serverName="noserver"/>
  <entry id="40" name="RemovedFriendHistoryDataReq" serverName="noserver"/>
  <entry id="41" name="BattleDSCLoadRecordInfoReq" serverName="noserver"/>
  <entry id="42" name="PlayerFriendSortInfoListReq" serverName="noserver"/>
  <entry id="43" name="CacheSvrDataReq" serverName="noserver"/>
  <entry id="44" name="ActivityGroupingReturnInfoReq" serverName="noserver"/>
  <entry id="45" name="ActivityGroupingReturnInfoRes" serverName="noserver"/>
  <entry id="46" name="ActivityGroupingReturnRewardReq" serverName="noserver"/>
  <entry id="47" name="ActivityGroupingReturnRewardRes" serverName="noserver"/>
  <entry id="48" name="PlayerExtraInfoTableReq" serverName="noserver"/>
  <entry id="49" name="RankMobaListReq" serverName="noserver"/>
  <entry id="50" name="RankMobaReducedReq" serverName="noserver"/>
  <entry id="51" name="ACTDanceOutfitGenerationReq" serverName="noserver"/>
  <entry id="52" name="ACTDanceOutfitChangeReq" serverName="noserver"/>
  <entry id="53" name="ACTDanceOutfitCancelReq" serverName="noserver"/>
  <entry id="54" name="ACTDanceOutfitCollectReq" serverName="noserver"/>
  <entry id="55" name="UgcShowDataSnapshotReq" serverName="noserver"/>
  <entry id="56" name="ActivityFishingFameStage2WrapperReq" serverName="noserver"/>
  <entry id="57" name="BattleHistoryReq" serverName="noserver"/>
  <entry id="58" name="UgcMapEvaluationInfoReq" serverName="noserver"/>
  <entry id="59" name="SpringSlipAssistInfoReq" serverName="noserver"/>
  <entry id="60" name="SpringSlipTradeInfoReq" serverName="noserver"/>
  <entry id="61" name="ClubMigrateRankReq" serverName="noserver"/>
  <entry id="62" name="PasswordCodeInfoReq" serverName="noserver"/>
  <entry id="63" name="ActivityWolfReturnWrapperReq" serverName="noserver"/>
  <entry id="64" name="StarPGuildPublicReq" serverName="noserver"/>
  <entry id="65" name="StarPTipsNtf" serverName="noserver"/>
</root>

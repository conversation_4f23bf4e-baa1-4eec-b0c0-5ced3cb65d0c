syntax = "proto2";
option cc_generic_services = false;
option java_multiple_files = true;
package com.tencent.wea.protocol.idip;

import "common.proto";
import "ss_head.proto";
import "ResKeywords.proto";
import "attr_ActivitySquadData.proto";
import "google/protobuf/descriptor.proto";

extend google.protobuf.MessageOptions {
  optional IdipHandler handler = 110001;
  optional bool hacked = 110002;                  // 表明需要在目的svr目录下需要生成handler，但是需要事先在idip做预处理
  optional bool deprecated = 110003;              // 开发过但由于某些原因不再使用
  optional bool checkUserInfo = 110004;           // 表明该接口是否需要检查用户信息
  optional bool queryMsg = 110005;                // 表明该接口为查询接口
  optional bool priorityUseUid = 110006;          // 表明该接口优先使用uid参数作为该接口的账号id
  optional bool noCheckRoleTransferring = 110007; // 表明该接口在角色转区过程中不受约束
}
extend google.protobuf.FieldOptions {
  optional bool urlcode = 200010;             //如果可能是中文的string字段，需要加上该标签
  optional bool kickPlayer = 200011;          //修改玩家数据的接口，需要先将玩家踢线
}

// 基于zoneid路由的idip请求
message IdipRpcByZoneIdReq {
  optional int64 zone = 1 [(field_dest_zone) = true];
  optional IdipRequest req = 2;
}

// 基于zoneid路由的idip响应
message IdipRpcByZoneIdRes {
  optional IdipResponse res = 1;
}

// 基于uid路由的idip请求
message IdipRpcByUuidReq {
  option (define_meta_type) = MDT_Dynamic;
  optional int64 uid = 1 [(field_meta_uuid) = true];
  optional IdipRequest req = 2;
}

// 基于uid路由的idip响应
message IdipRpcByUuidRes {
  optional IdipResponse res = 1;
}

// 基于目标服务节点路由的idip请求
message IdipRpcReq {
  optional int64 destSvrId = 1 [(field_dest_serv) = true];
  optional IdipRequest req = 2;
}

// 基于目标服务节点路由的idip响应
message IdipRpcRes {
  optional IdipResponse res = 1;
}

// 基于hashkey路由的idip请求
message IdipRpcByHashKeyReq {
  optional int64 hashKey = 1 [(field_hash_key) = true];
  optional IdipRequest req = 2;
}

// 基于hashkey路由的idip响应
message IdipRpcByHashKeyRes {
  optional IdipResponse res = 1;
}

// 基于hashkey路由的idip请求(req和res均为string)
message IdipRpcMessageByHashKeyReq {
  optional int64 hashKey = 1 [(field_hash_key) = true];
  optional int32 cmdId = 2;
  optional string req = 3;
}

// 基于hashkey路由的idip请求(req和res均为string)
message IdipRpcMessageByHashKeyRes {
  optional string res = 1;
}

// 基于目标服务节点路由的idip响应(req和res均为string)
message IdipRpcMessageReq {
  optional int64 destSvrId = 1 [(field_dest_serv) = true];
  optional int32 cmdId = 2;
  optional string req = 3;
}

// 基于目标服务节点路由的idip响应(req和res均为string)
message IdipRpcMessageRes {
  optional string res = 1;
}

message IdipResMsg{
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;
}

//扣除玩家角色的背包道具
message DoSubUserBackpackItemReq {
    option (handler) = IH_IdipSvr;
	option (hacked) = true;
  option (checkUserInfo) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional string  OpenId = 4;                                // OpenId
	optional int64	 StarPId = 5;						  		// 啾灵世界Id
	optional int64   Uid = 6 [(field_meta_uuid) = true];        // 玩家uid
  optional int64   RoleId = 8;
	optional string  ReviseContent = 7;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//扣除玩家角色的背包道具返回
message DoSubUserBackpackItemRsp {
    optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//修改星兽等级
message DoPetModifyLevelReq {
    option (handler) = IH_IdipSvr;
	option (hacked) = true;
  option (checkUserInfo) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional string  OpenId = 4;                                // OpenId
	optional int64	 StarPId = 5;						  		// 啾灵世界Id
	optional int64   Uid = 6 [(field_meta_uuid) = true];        // 玩家uid
  optional int64   RoleId = 8;
	optional string  ReviseContent = 7;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//修改星兽等级返回
message DoPetModifyLevelRsp {
    optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//修改啾灵被动技能
message DoPetModifyPassiveSkillReq {
    option (handler) = IH_IdipSvr;
	option (hacked) = true;
  option (checkUserInfo) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional string  OpenId = 4;                                // OpenId
	optional int64	 StarPId = 5;						  		// 啾灵世界Id
	optional int64   Uid = 6 [(field_meta_uuid) = true];        // 玩家uid
  optional int64   RoleId = 8;
	optional string  ReviseContent = 7;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//修改啾灵被动技能返回
message DoPetModifyPassiveSkillRsp {
    optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//修改啾灵星级
message DoPetModifyStarLevelReq {
    option (handler) = IH_IdipSvr;
	option (hacked) = true;
  option (checkUserInfo) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional string  OpenId = 4;                                // OpenId
	optional int64	 StarPId = 5;						  		// 啾灵世界Id
	optional int64   Uid = 6 [(field_meta_uuid) = true];        // 玩家uid
  optional int64   RoleId = 8;
	optional string  ReviseContent = 7;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//修改啾灵星级返回
message DoPetModifyStarLevelRsp {
    optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//修改账户体力
message DoAccountModifyPowerReq {
    option (handler) = IH_IdipSvr;
	option (hacked) = true;
  option (checkUserInfo) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional string  OpenId = 4;                                // OpenId
	optional int64	 StarPId = 5;						  		// 啾灵世界Id
	optional int64   Uid = 6 [(field_meta_uuid) = true];        // 玩家uid
  optional int64   RoleId = 8;
	optional string  ReviseContent = 7;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//修改账户体力返回
message DoAccountModifyPowerRsp {
    optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//给世界箱子添加道具
message DoWorldAddItemReq {
    option (handler) = IH_IdipSvr;
	option (hacked) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional int64	 StarPId = 4;						  		// 啾灵世界Id
	optional string  ReviseContent = 5;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//给世界箱子添加道具返回
message DoWorldAddItemRsp {
    optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//给世界箱子扣除道具
message DoWorldSubItemReq {
    option (handler) = IH_IdipSvr;
	option (hacked) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional int64	 StarPId = 4;						  		// 啾灵世界Id
	optional string  ReviseContent = 5;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//给世界箱子扣除道具返回
message DoWorldSubItemRsp {
    optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//给玩家添加经验
message DoUserAddExpReq {
    option (handler) = IH_IdipSvr;
	option (hacked) = true;
  option (checkUserInfo) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional string  OpenId = 4;                                // OpenId
	optional int64	 StarPId = 5;						  		// 啾灵世界Id
	optional int64   Uid = 6 [(field_meta_uuid) = true];        // 玩家uid
  optional int64   RoleId = 8;
	optional string  ReviseContent = 7;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//给玩家添加经验返回
message DoUserAddExpRsp {
    optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//给玩家减少经验
message DoUserSubExpReq {
    option (handler) = IH_IdipSvr;
	option (hacked) = true;
  option (checkUserInfo) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional string  OpenId = 4;                                // OpenId
	optional int64	 StarPId = 5;						  		// 啾灵世界Id
	optional int64   Uid = 6 [(field_meta_uuid) = true];        // 玩家uid
  optional int64   RoleId = 8;
	optional string  ReviseContent = 7;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//给玩家减少经验返回
message DoUserSubExpRsp {
    optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//背包装备属性操作
message DoOperEquipAttrReq {
    option (handler) = IH_IdipSvr;
	option (hacked) = true;
  option (checkUserInfo) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional string  OpenId = 4;                                // OpenId
	optional int64	 StarPId = 5;						  		// 啾灵世界Id
	optional int64   Uid = 6 [(field_meta_uuid) = true];        // 玩家uid
  optional int64   RoleId = 8;
	optional string  ReviseContent = 7;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//背包装备属性操作返回
message DoOperEquipAttrRsp {
    optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

message IdipRequest {
  optional int32 cmdId = 1;
  optional QueryPersonalInfoReq QueryPersonalInfoReq = 14;

  optional SendPersonalMailReq sendPersonalMailReq = 15;
  optional DelPersonalMailReq delPersonalMailReq = 16;
  optional SendPersonalMailWithAmsAttachExpireReq sendPersonalMailWithAmsAttachExpireReq = 17;
  optional SendGlobalMailWithAmsAttachExpireReq sendGlobalMailWithAmsAttachExpireReq = 18;

  optional QueryPlayerItemListReq queryPlayerItemListReq = 20;
  optional DoModifyItemReq doModifyItemReq = 21;
  optional DoModifyTaskProgressReq doModifyTaskProgressReq = 22;
  optional DoModifyBpDataReq doModifyBpDataReq = 23;
  optional DoSecurityPunishReq doSecurityPunishReq = 24;
  optional DoModifyPlayerInfoReq doModifyPlayerInfoReq = 25;
  optional DoRemoveUgcMapReq doRemoveUgcMapReq = 26;
  optional SendGlobalMailReq sendGlobalMailReq = 27;
  optional AuditInfoCallbackReq auditInfoCallbackReq = 28;
  optional QueryPlayerStatInfoReq queryPlayerStatInfoReq = 29;
  optional DoSecurityPunishRemoveReq doSecurityPunishRemoveReq = 30;
  optional SendPersonalTextMailReq sendPersonalTextMailReq = 31;
  optional DoKickPlayerReq doKickPlayerReq = 32;
  optional DoModifyRoomInfoReq doModifyRoomInfoReq = 33;
  optional DoDisbandRoomReq doDisbandRoomReq = 34;
  optional DoModifyMapInfoReq doModifyMapInfoReq = 35;
  optional DoMapStageResetReq doMapStageResetReq = 36;
  optional DoGlobalBanProcessReq doGlobalBanProcessReq = 37;
  optional DoCleanUserChatReq doCleanUserChatReq = 38;
  optional DoModifyPlayerRegionReq doModifyPlayerRegionReq = 39;
  optional DoModifyFriendRelationReq doModifyFriendRelationReq = 40;
  optional DoModifyPlayerVipExpReq doModifyPlayerVipExpReq = 41;
  optional DoModifyPlayerMonthCardReq doModifyPlayerMonthCardReq = 42;
  optional DoSetUserLabelReq doSetUserLabelReq = 43;
  optional DoGuideTaskProcessReq doGuideTaskProcessReq = 44;
  optional AccountCancelCallbackReq accountCancelCallbackReq = 45;
  optional AccountStateChangeCallbackReq accountStateChangeCallbackReq = 46;
  optional DirAddIpBlackInfoReq dirAddIpBlackInfoReq = 47;
  optional DirRemoveIpBlackInfoReq dirRemoveIpBlackInfoReq = 48;
  optional DirGetIpBlackInfoReq dirGetIpBlackInfoReq = 49;
  optional DirGetIpBlackInfoListReq dirGetIpBlackInfoListReq = 50;
  optional QueryPersonalOpenidReq  queryPersonalOpenidReq = 51;
  optional SendGlobalTextMailReq sendGlobalTextMailReq = 52;
  optional DoGlobalSvrKickReq doGlobalSvrKickReq = 53;
  optional AddMultiLanguageConfReq addMultiLanguageConfReq = 54;

  optional AqDoXiaoWoBlockReq AqDoXiaoWoBlockReq = 61;
  optional AqDoXiaoWoBlockCancelReq AqDoXiaoWoBlockCancelReq = 62;
  optional AqDoXiaoWoPutDownReq AqDoXiaoWoPutDownReq = 63;
  optional AqDoXiaoWoSetHotReq AqDoXiaoWoSetHotReq = 64;
  optional AqDoXiaoWoClearInstructionAndImageReq AqDoXiaoWoClearInstructionAndImageReq = 65;
  optional AqDoXiaoWoReportSafeAuditResultReq AqDoXiaoWoReportSafeAuditResultReq = 66;
  optional SwitchPlayStatusSynchronizeReq switchPlayStatusSynchronizeReq = 67;
  optional QueryLiveHeartbeatReq queryLiveHeartbeatReq = 68;
  optional QueryRoomUserInfoReq queryRoomUserInfoReq = 69;
  optional GetUgcOutGameTranslateDataReq getUgcOutGameTranslateDataReq = 70;
  optional GetUgcInGameTranslateDataReq getUgcInGameTranslateDataReq = 71;
  optional ModifUgcOutGameTranslateDataReq modifUgcOutGameTranslateDataReq = 72;
  optional ModifUgcInGameTranslateDataReq modifUgcInGameTranslateDataReq = 73;
  optional QueryUgcOutGameOriginalTextReq queryUgcOutGameOriginalTextReq = 74;
  optional QueryUgcInGameOriginalTextReq queryUgcInGameOriginalTextReq = 75;
  optional QueryNewPlayerStatInfoReq queryNewPlayerStatInfoReq = 76;
  optional QueryPlayerBasicInfoReq queryPlayerBasicInfoReq = 77;
  optional SendPersonalMailWithAttachExpireReq sendPersonalMailWithAttachExpireReq = 78;
  optional QueryCommonPlayerDataByDatamoreReq queryCommonPlayerDataByDatamoreReq = 79;
  optional SendRollingNoticeInfoReq sendRollingNoticeInfoReq = 80;
  optional QueryRollingNoticeInfoListReq queryRollingNoticeInfoListReq = 81;
  optional OfflineRollingNoticeInfoReq offlineRollingNoticeInfoReq = 82;
  optional DoDeleteActivityMapWishReq doDeleteActivityMapWishReq = 83;
  optional DoCleanUserRegionRankReq doCleanUserRegionRankReq = 84;
  optional OffEndConsumeNotifyReq offEndConsumeNotifyReq = 85;
  optional SendItemDataIntoBagReq sendItemDataIntoBagReq = 86;
  optional SetUserAccountCancelStateReq setUserAccountCancelStateReq = 87;
  optional PlayerReputationScoreModifyReq playerReputationScoreModifyReq = 88;
  optional DoModifyCodingCommunityTemplateReq doModifyCodingCommunityTemplateReq = 89;
  optional SendGlobalMailWithAttachExpireReq sendGlobalMailWithAttachExpireReq = 90;
  optional DoModifyCodingCommunityTemplateStateReq doModifyCodingCommunityTemplateStateReq = 91;
  optional VerbalViolationReputationScorePunishReq verbalViolationReputationScorePunishReq = 92;
  optional VerbalViolationReputationScoreUnPunishReq verbalViolationReputationScoreUnPunishReq = 93;
  optional DoDeductOnlinePlayerItemReq doDeductOnlinePlayerItemReq = 94;
  optional QueryPlayerAndOtherPlayerRelationReq queryPlayerAndOtherPlayerRelationReq = 95;
  optional PlatformInterfaceForwardReq platformInterfaceForwardReq = 96;
  optional UgcDataStoreSvrTestReq ugcDataStoreSvrTestReq = 97;

  optional QueryPlayerFriendListReq queryPlayerFriendListReq = 100;
  optional QueryGameUserInfoReq queryGameUserInfoReq = 101;
  optional OpenSuperLuckyMoneyReq openSuperLuckyMoneyReq = 102;
  optional QuerySuperLuckyMoneyInfoReq querySuperLuckyMoneyInfoReq = 103;
  optional ClickActivityOuterLinkReq clickActivityOuterLinkReq = 104;
  optional SetPlayerDsRecordInfoReq setPlayerDsRecordInfoReq = 105;
  optional BanDeviceIdReq banDeviceIdReq = 106;
  optional UnbanDeviceIdReq unbanDeviceIdReq = 107;
  optional BanGameModeReq banGameModeReq = 108;
  optional UnbanGameModeReq unbanGameModeReq = 109;
  optional QueryInvitedFriendInfoReq queryInvitedFriendInfoReq = 110;
  optional CreatorTeamActivityReq creatorTeamActivityReq = 111;
  optional CreatorTeamActivityRankReq creatorTeamActivityRankReq = 112;
  optional SetBlackIndustryUserReq setBlackIndustryUserReq = 113;
  optional DoDeleteUgcMapTopicReq doDeleteUgcMapTopicReq = 114;
  optional AqDoDelYuanJianBiaoQianReq aqDoDelYuanJianBiaoQianReq = 115;
  optional AqQueryXiaoWoGetLayoutListReq AqQueryXiaoWoGetLayoutListReq = 116;
  optional AqDoXiaoWoDelLayoutReq AqDoXiaoWoDelLayoutReq = 117;
  optional GetResTableDataReq GetResTableDataReq = 118;
  optional ConfirmRecruiteOrderReq confirmRecruiteOrderReq = 119;
  optional MatchIsolateReq matchIsolateReq = 120;
  optional AqDoXiaoWoResetMapReq aqDoXiaoWoResetMapReq = 121;
  optional AqDoPlayerAddWarmRoundReq aqDoPlayerAddWarmRoundReq = 122;
  optional DoModifyPlayerRankScoreReq doModifyPlayerRankScoreReq = 123;
  optional DoModifyPlayerLevelScoreReq doModifyPlayerLevelScoreReq = 124;
  optional QueryRankInfoReq queryRankInfoReq = 125;
  optional ModifyClubNameReq modifyClubNameReq = 126;
  optional ModifyClubBriefReq modifyClubBriefReq = 127;
  optional DoUgcMapHandlerReq doUgcMapHandlerReq = 128;
  optional SpringH5GetClubInfoReq springH5GetClubInfoReq = 129;
  optional SpringH5GetMyClubInfoReq springH5GetMyClubInfoReq = 130;
  optional SpringH5BatchRandomClubInfoReq springH5BatchRandomClubInfoReq = 131;
  optional LuckyStarGetHandbookInfoReq luckyStarGetHandbookInfoReq = 132;
  optional LuckyStarGetGiveInfoReq luckyStarGetGiveInfoReq = 133;
  optional LuckyStarReceiveGiveStarReq luckyStarReceiveGiveStarReq = 134;
  optional AqDoXiaoWoWelcomeClearReq aqDoXiaoWoWelcomeClearReq = 135;
  optional AqDoXiaoWoWelcomeBanReq aqDoXiaoWoWelcomeBanReq = 136;
  optional AqDoXiaoWoBulletinDeleteReq aqDoXiaoWoBulletinDeleteReq = 137;
  optional AqDoXiaoWoBulletinBanReq aqDoXiaoWoBulletinBanReq = 138;
  optional AqDoXiaoWoWelcomeSetFreeReq aqDoXiaoWoWelcomeSetFreeReq = 139;
  optional AqDoXiaoWoBulletinSendFreeReq aqDoXiaoWoBulletinSendFreeReq = 140;
  optional QueryXiaoWoGetBasicinfoReq queryXiaoWoGetBasicinfoReq = 141;
  optional AqDoMapNameReq aqDoMapNameReq = 142;
  optional AqDoMapCollectionBlurbReq aqDoMapCollectionBlurbReq = 143;
  optional GetKungFuPandaHelpRacingInfoReq getKungFuPandaHelpRacingInfoReq = 144;
  optional HelpKungFuPandaRacingReq helpKungFuPandaRacingReq = 145;
  optional DisbandSquadReq disbandSquadReq = 146;
  optional AqDoFarmPutDownReq aqDoFarmPutDownReq = 147;
  optional AqDoClearFarmReq aqDoClearFarmReq = 148;
  optional QueryFarmGetItemListReq queryFarmGetItemListReq = 149;
  optional DoFarmAddItemReq doFarmAddItemReq = 150;
  optional DoFarmDelItemReq doFarmDelItemReq = 151;
  optional DoXiaoWoResetMapToVersionReq doXiaoWoResetMapToVersionReq = 152;
  optional QueryXiaoWoGetCreatorLayoutReq queryXiaoWoGetCreatorLayoutReq= 153;
  optional DoXiaoWoPubCreatorlayoutReq doXiaoWoPubCreatorlayoutReq= 154;
  optional DoXiaoWoDelCreatorlayoutReq doXiaoWoDelCreatorlayoutReq= 155;
  optional SyncGameTvRewardStatusReq syncGameTvRewardStatusReq = 156;
  optional DoFarmAdjustBuildingLevelReq doFarmAdjustBuildingLevelReq = 157;
  optional DoFarmAdjustMainExpReq doFarmAdjustMainExpReq = 158;
  optional DoFarmAddCropExpReq doFarmAddCropExpReq = 159;
  optional DoSetCommonUserLabelReq doSetCommonUserLabelReq = 160;
  optional DoModifyFriendIntimacyReq doModifyFriendIntimacyReq = 161;
  optional QueryFriendIntimacyReq queryFriendIntimacyReq = 162;
  optional AqDoFarmWelcomeClearReq aqDoFarmWelcomeClearReq = 163;
  optional AqDoFarmWelcomeBanReq aqDoFarmWelcomeBanReq = 164;
  optional AqDoFarmWelcomeSetFreeReq aqDoFarmWelcomeSetFreeReq = 165;
  optional AqDoFarmLiuYanMessageDeleteReq aqDoFarmLiuYanMessageDeleteReq = 166;
  optional AqDoFarmLiuYanMessageBanReq aqDoFarmLiuYanMessageBanReq = 167;
  optional AqDoFarmLiuYanMessageSendFreeReq aqDoFarmLiuYanMessageSendFreeReq = 168;
  optional DoModifyPlayerQualifyMaxDegreeReq doModifyPlayerQualifyMaxDegreeReq = 169;
  optional AqDoBanPlayerAlbumPicReq aqDoBanPlayerAlbumPicReq = 170;
  optional AqDoModifyRechargeLevelReq aqDoModifyRechargeLevelReq = 171;
  optional QueryPlayerHasItemReq queryPlayerHasItemReq = 172;
  optional AqDoModifyGameProtectionSettingsReq aqDoModifyGameProtectionSettingsReq = 173;
  optional AqQueryGameProtectionSettingsReq AqQueryGameProtectionSettingsReq = 174;
  optional DoFarmMonthCardReq doFarmMonthCardReq = 175;
  optional QueryPlayerCommodityBuyTimesReq queryPlayerCommodityBuyTimesReq = 176;
  optional DoUpdatePlayerCommodityBuyTimesReq doUpdatePlayerCommodityBuyTimesReq = 177;
  optional DoModifyQualifySeasonInfoReq doModifyQualifySeasonInfoReq = 178;
  optional QueryActivitySquadInfoReq queryActivitySquadInfoReq = 179;
  optional DoPlayerJoinSquadReq doPlayerJoinSquadReq = 180;
  optional DoClubSetOwnerReq doClubSetOwnerReq = 181;
  optional AqDoClubDissolveReq aqDoClubDissolveReq = 182;
  optional AqDoClubKickMemberReq aqDoClubKickMemberReq = 183;
  optional AddUgcPaidItemsReq addUgcPaidItemsReq = 184;
  optional DoWishActivityHelpBindReq doWishActivityHelpBindReq = 185;
  optional DoTrainingCampGetSportsmanDataReq doTrainingCampGetSportsmanDataReq = 186;
  optional DoTrainingCampTrainSportsmanReq doTrainingCampTrainSportsmanReq = 187;
  optional AnimalHandbookReceiveGiveAnimalReq animalHandbookReceiveGiveAnimalReq = 188;
  optional AqDoFarmForbidGiftSendingReq aqDoFarmForbidGiftSendingReq = 189;
  optional AqDoFarmBlockGiftMsgReq aqDoFarmBlockGiftMsgReq = 190;
  optional DoFarmAddFishExpReq doFarmAddFishExpReq = 191;
  optional QueryFarmGetBasicinfoReq queryFarmGetBasicinfoReq = 192;
  optional ModifyClubHeatReq modifyClubHeatReq = 193;
  optional ModifyClubLBSReq modifyClubLBSReq = 194;
  optional BanClubHeatRankReq banClubHeatRankReq = 195;
  optional QueryGetUgcPlayedMapsReq queryGetUgcPlayedMapsReq = 196;
  optional DoModifyOmdrankReq doModifyOmdrankReq = 197;
  optional ModifyCupsProgressReq modifyCupsProgressReq = 198;
  optional DoCleanUserBagInteractCombReq doCleanUserBagInteractCombReq = 199;
  optional QueryFarmLiuYanMessageReq queryFarmLiuYanMessageReq = 200;
  optional ModifyChargeProgressReq modifyChargeProgressReq = 201;
  optional AqDoFarmHousePutDownReq aqDoFarmHousePutDownReq = 202;
  optional AqDoFarmHousePickAllFurnitureReq aqDoFarmHousePickAllFurnitureReq = 203;
  optional DoUpdateSeasonFashionEquipBookReq doUpdateSeasonFashionEquipBookReq = 204;
  optional AqDoFarmResetPetNameReq aqDoFarmResetPetNameReq = 215;
  optional AqDoFarmBanChangePetNameReq aqDoFarmBanChangePetNameReq = 216;
  optional QueryWishActivityHelpInfoReq queryWishActivityHelpInfoReq = 205;
  optional DeliverMidasProductReq deliverMidasProductReq = 206;
  optional AqDoModifyUgcRankScoreReq aqDoModifyUgcRankScoreReq = 207;
  optional QueryPlayerSuperCoreRankActivityAuthReq queryPlayerSuperCoreRankActivityAuthReq = 208;
  optional DoSuperCoreRankActivityScoreModifyReq doSuperCoreRankActivityScoreModifyReq = 209;
  optional QueryPlayerSuperCoreRankActivityScoreReq queryPlayerSuperCoreRankActivityScoreReq = 210;
  optional DoTrainingGetOjbectDataReq  doTrainingGetOjbectDataReq = 211;
  optional DoTrainingTrainObjectReq  doTrainingTrainObjectReq = 212;
  optional DoFarmModifyVillagerReq doFarmModifyVillagerReq = 213;
  optional DoOffEndRechargeDiamondNotifyReq doOffEndRechargeDiamondNotifyReq = 214;
  optional AqDoMallGiftCardResetWordsReq aqDoMallGiftCardResetWordsReq = 217;
  optional QueryChargeMoenyReq queryChargeMoenyReq = 218;
  optional ModifyTradingCardReq modifyTradingCardReq = 219;
  optional QueryCardTradeInfoReq queryCardTradeInfoReq = 220;
  optional ReceiveTradingCardReq receiveTradingCardReq = 221;
  optional DoFarmDelBuildingSkinReq doFarmDelBuildingSkinReq = 224;
  optional AqDoBirthdayCardResetWordsReq aqDoBirthdayCardResetWordsReq = 225;
  optional QueryLuckStarReceiveReq queryLuckStarReceiveReq = 227;
  optional QueryLuckStarGiveReq queryLuckStarGiveReq = 228;
  optional DoLuckStarReceiveReq doLuckStarReceiveReq = 229;
  optional DoLuckStarGiveReq doLuckStarGiveReq = 230;
  optional NotifyStreamInteractionCmdReq notifyStreamInteractionCmdReq = 231;
  optional DoFarmMagicAddOrDelReq doFarmMagicAddOrDelReq = 232;
  optional DoFarmMagicAdjustMpReq doFarmMagicAdjustMpReq = 233;
  optional DoFarmLayoutOpReq doFarmLayoutOpReq = 234;
  optional AqDoModifyCreatorMessageReq aqDoModifyCreatorMessageReq = 235;
  optional DoModifyPlayerArenaHeroCeScoreReq doModifyPlayerArenaHeroCeScoreReq = 236;
  optional DoTransferUserPlatReq doTransferUserPlatReq = 237;
  optional QueryTransferAbilityReq queryTransferAbilityReq = 238;
  optional DoReleaseTransferLockReq doReleaseTransferLockReq = 239;
  optional QueryUserRealPlatReq queryUserRealPlatReq = 240;
  optional AqDoFarmPartyClearReq aqDoFarmPartyClearReq = 241;
  optional AqDoFarmPartyBanReq aqDoFarmPartyBanReq = 242;
  optional AqDoFarmPartySetFreeReq aqDoFarmPartySetFreeReq = 243;
  optional AqDoFarmCookScreenClearReq aqDoFarmCookScreenClearReq = 244;
  optional AqDoFarmCookScreenBanReq aqDoFarmCookScreenBanReq = 245;
  optional AqDoFarmCookScreenSetFreeReq aqDoFarmCookScreenSetFreeReq = 246;
  optional AqDoFarmCookPutDownReq aqDoFarmCookPutDownReq = 247;
  optional DoFarmCookAddEmployeeReq doFarmCookAddEmployeeReq = 248;
  optional DoSetRaffleDailyLimitTagWhiteListReq doSetRaffleDailyLimitTagWhiteListReq = 249;
  optional DoFarmCookUpdateExtendInfoReq doFarmCookUpdateExtendInfoReq = 250;
  optional QueryFarmCookQueryExtendInfoReq queryFarmCookQueryExtendInfoReq = 251;
  optional QueryAccountTransferPreConditionReq queryAccountTransferPreConditionReq = 252;

  // starp
  optional QueryStarPGuildInfoReq queryStarPGuildInfoReq = 300;
  optional QueryStarPTraderReq queryStarPTraderReq = 301;
  optional QueryStarPCommonPveDataReq queryStarPCommonPveDataReq = 302;
  optional QueryStarPPlayerDropItemsReq queryStarPPlayerDropItemsReq = 303;
  optional QueryStarPMapPosBuildingReq queryStarPMapPosBuildingReq = 304;
  optional QueryStarPPlayerItemsReq queryStarPPlayerItemsReq = 305;
  optional QueryStarPPlayerSimpleInfoReq queryStarPPlayerSimpleInfoReq = 306;
  optional QueryStarPPlayerRoleAttrReq queryStarPPlayerRoleAttrReq = 307;
  optional DoModifyStarPReq doModifyStarPReq = 308;
  optional QueryStarPItemInfosReq queryStarPItemInfosReq = 309;
  optional DoStarPAddMailReq doStarPAddMailReq = 310;

  optional AqDoModifyDebugDsReq aqDoModifyDebugDsReq = 311;

  optional DoSubUserBackpackItemReq doSubUserBackpackItemReq = 312;
  optional DoPetModifyLevelReq  doPetModifyLevelReq = 313;
  optional DoPetModifyPassiveSkillReq doPetModifyPassiveSkillReq  = 314;
  optional DoPetModifyStarLevelReq doPetModifyStarLevelReq  = 315;
  optional DoAccountModifyPowerReq doAccountModifyPowerReq  = 316;
  optional DoWorldAddItemReq doWorldAddItemReq  = 317;
  optional DoWorldSubItemReq doWorldSubItemReq  = 318;
  optional DoUserAddExpReq doUserAddExpReq  = 319;
  optional DoUserSubExpReq doUserSubExpReq  = 320;
  optional DoOperEquipAttrReq doOperEquipAttrReq  = 321;
  optional AqDoPlayerItaBagResetReq aqDoPlayerItaBagResetReq = 322;
}

message IdipResponse {
  optional QueryPersonalInfoRsp QueryPersonalInfoRsp = 13;

  optional SendPersonalMailRsp sendPersonalMailRsp = 15;
  optional DelPersonalMailRsp delPersonalMailRsp = 16;
  optional SendPersonalMailWithAmsAttachExpireRsp sendPersonalMailWithAmsAttachExpireRsp = 17;
  optional SendGlobalMailWithAmsAttachExpireRsp sendGlobalMailWithAmsAttachExpireRsp = 18;

  optional QueryPlayerItemListRsp queryPlayerItemListRsp = 20;
  optional DoModifyItemRsp doModifyItemRsp = 21;
  optional DoModifyTaskProgressRsp doModifyTaskProgressRsp = 22;
  optional DoModifyBpDataRsp doModifyBpDataRsp = 23;
  optional DoSecurityPunishRsp doSecurityPunishRsp = 24;
  optional DoModifyPlayerInfoRsp doModifyPlayerInfoRsp = 25;
  optional DoRemoveUgcMapRsp doRemoveUgcMapRsp = 26;
  optional SendGlobalMailRsp sendGlobalMailRsp = 27;
  optional AuditInfoCallbackRsp auditInfoCallbackRsp = 28;
  optional QueryPlayerStatInfoRsp queryPlayerStatInfoRsp = 29;
  optional DoSecurityPunishRemoveRsp doSecurityPunishRemoveRsp = 30;
  optional SendPersonalTextMailRsp sendPersonalTextMailRsp = 31;
  optional DoKickPlayerRsp doKickPlayerRsp = 32;
  optional DoModifyRoomInfoRsp doModifyRoomInfoRsp = 33;
  optional DoDisbandRoomRsp doDisbandRoomRsp = 34;
  optional DoModifyMapInfoRsp doModifyMapInfoRsp = 35;
  optional DoMapStageResetRsp doMapStageResetRsp = 36;
  optional DoGlobalBanProcessRsp doGlobalBanProcessRsp = 37;
  optional DoCleanUserChatRsp doCleanUserChatRsp = 38;
  optional DoModifyPlayerRegionRsp doModifyPlayerRegionRsp = 39;
  optional DoModifyFriendRelationRsp doModifyFriendRelationRsp = 40;
  optional DoModifyPlayerVipExpRsp doModifyPlayerVipExpRsp = 41;
  optional DoModifyPlayerMonthCardRsp doModifyPlayerMonthCardRsp = 42;
  optional DoSetUserLabelRsp doSetUserLabelRsp = 43;
  optional DoGuideTaskProcessRsp doGuideTaskProcessRsp = 44;
  optional AccountCancelCallbackRsp accountCancelCallbackRsp = 45;
  optional AccountStateChangeCallbackRsp accountStateChangeCallbackRsp = 46;
  optional DirAddIpBlackInfoRsp dirAddIpBlackInfoRsp = 47;
  optional DirRemoveIpBlackInfoRsp dirRemoveIpBlackInfoRsp = 48;
  optional DirGetIpBlackInfoRsp dirGetIpBlackInfoRsp = 49;
  optional DirGetIpBlackInfoListRsp dirGetIpBlackInfoListRsp = 50;
  optional QueryPersonalOpenidRsp  queryPersonalOpenidRsp = 51;
  optional SendGlobalTextMailRsp sendGlobalTextMailRsp = 52;
  optional DoGlobalSvrKickRsp doGlobalSvrKickRsp = 53;
  optional AddMultiLanguageConfRsp addMultiLanguageConfRsp = 54;

  optional AqDoXiaoWoBlockRsp AqDoXiaoWoBlockRsp = 61;
  optional AqDoXiaoWoBlockCancelRsp AqDoXiaoWoBlockCancelRsp = 62;
  optional AqDoXiaoWoPutDownRsp AqDoXiaoWoPutDownRsp = 63;
  optional AqDoXiaoWoSetHotRsp AqDoXiaoWoSetHotRsp = 64;
  optional AqDoXiaoWoClearInstructionAndImageRsp AqDoXiaoWoClearInstructionAndImageRsp = 65;
  optional AqDoXiaoWoReportSafeAuditResultRsp AqDoXiaoWoReportSafeAuditResultRsp = 66;
  optional SwitchPlayStatusSynchronizeRsp switchPlayStatusSynchronizeRsp = 67;
  optional QueryLiveHeartbeatRsp queryLiveHeartbeatRsp = 68;
  optional QueryRoomUserInfoRsp queryRoomUserInfoRsp = 69;
  optional GetUgcOutGameTranslateDataRsp getUgcOutGameTranslateDataRsp = 70;
  optional GetUgcInGameTranslateDataRsp getUgcInGameTranslateDataRsp = 71;
  optional ModifUgcOutGameTranslateDataRsp modifUgcOutGameTranslateDataRsp = 72;
  optional ModifUgcInGameTranslateDataRsp modifUgcInGameTranslateDataRsp = 73;
  optional QueryUgcOutGameOriginalTextRsp queryUgcOutGameOriginalTextRsp = 74;
  optional QueryUgcInGameOriginalTextRsp queryUgcInGameOriginalTextRsp = 75;
  optional QueryNewPlayerStatInfoRsp queryNewPlayerStatInfoRsp = 76;
  optional QueryPlayerBasicInfoRsp queryPlayerBasicInfoRsp = 77;
  optional SendPersonalMailWithAttachExpireRsp sendPersonalMailWithAttachExpireRsp = 78;
  optional QueryCommonPlayerDataByDatamoreRsp queryCommonPlayerDataByDatamoreRsp = 79;
  optional SendRollingNoticeInfoRsp sendRollingNoticeInfoRsp = 80;
  optional QueryRollingNoticeInfoListRsp queryRollingNoticeInfoListRsp = 81;
  optional OfflineRollingNoticeInfoRsp offlineRollingNoticeInfoRsp = 82;
  optional DoDeleteActivityMapWishRsp doDeleteActivityMapWishRsp = 83;
  optional DoCleanUserRegionRankRsp doCleanUserRegionRankRsp = 84;
  optional OffEndConsumeNotifyRsp offEndConsumeNotifyRsp = 85;
  optional SendItemDataIntoBagRsp sendItemDataIntoBagRsp = 86;
  optional SetUserAccountCancelStateRsp setUserAccountCancelStateRsp = 87;
  optional PlayerReputationScoreModifyRsp playerReputationScoreModifyRsp = 88;
  optional DoModifyCodingCommunityTemplateRsp doModifyCodingCommunityTemplateRsp = 89;
  optional SendGlobalMailWithAttachExpireRsp sendGlobalMailWithAttachExpireRsp = 90;
  optional DoModifyCodingCommunityTemplateStateRsp doModifyCodingCommunityTemplateStateRsp = 91;
  optional VerbalViolationReputationScorePunishRsp verbalViolationReputationScorePunishRsp = 92;
  optional VerbalViolationReputationScoreUnPunishRsp verbalViolationReputationScoreUnPunishRsp = 93;
  optional DoDeductOnlinePlayerItemRsp doDeductOnlinePlayerItemRsp = 94;
  optional QueryPlayerAndOtherPlayerRelationRsp queryPlayerAndOtherPlayerRelationRsp = 95;
  optional PlatformInterfaceForwardRsp platformInterfaceForwardRsp = 96;
  optional UgcDataStoreSvrTestRsp ugcDataStoreSvrTestRsp = 97;

  optional QueryPlayerFriendListRsp queryPlayerFriendListRsp = 100;
  optional QueryGameUserInfoRsp queryGameUserInfoRsp = 101;
  optional OpenSuperLuckyMoneyRsp openSuperLuckyMoneyRsp = 102;
  optional QuerySuperLuckyMoneyInfoRsp querySuperLuckyMoneyInfoRsp = 103;
  optional ClickActivityOuterLinkRsp clickActivityOuterLinkRsp = 104;
  optional SetPlayerDsRecordInfoRsp setPlayerDsRecordInfoRsp = 105;
  optional BanDeviceIdRsp banDeviceIdRsp = 106;
  optional UnbanDeviceIdRsp unbanDeviceIdRsp = 107;
  optional BanGameModeRsp banGameModeRsp = 108;
  optional UnbanGameModeRsp unbanGameModeRsp = 109;
  optional QueryInvitedFriendInfoRsp queryInvitedFriendInfoRsp = 110;
  optional CreatorTeamActivityRsp creatorTeamActivityRsp = 111;
  optional CreatorTeamActivityRankRsp creatorTeamActivityRankRsp = 112;
  optional SetBlackIndustryUserRsp setBlackIndustryUserRsp = 113;
  optional DoDeleteUgcMapTopicRsp doDeleteUgcMapTopicRsp = 114;
  optional AqDoDelYuanJianBiaoQianRsp aqDoDelYuanJianBiaoQianRsp = 115;
  optional AqQueryXiaoWoGetLayoutListRsp AqQueryXiaoWoGetLayoutListRsp = 116;
  optional AqDoXiaoWoDelLayoutRsp AqDoXiaoWoDelLayoutRsp = 117;
  optional GetResTableDataRsp GetResTableDataRsp = 118;
  optional ConfirmRecruiteOrderRsp confirmRecruiteOrderRsp = 119;
  optional MatchIsolateRsp matchIsolateRsp = 120;
  optional AqDoXiaoWoResetMapRsp aqDoXiaoWoResetMapRsp = 121;
  optional AqDoPlayerAddWarmRoundRsp aqDoPlayerAddWarmRoundRsp = 122;
  optional DoModifyPlayerRankScoreRsp doModifyPlayerRankScoreRsp = 123;
  optional DoModifyPlayerLevelScoreRsp doModifyPlayerLevelScoreRsp = 124;
  optional QueryRankInfoRsp queryRankInfoRsp = 125;
  optional ModifyClubNameRsp modifyClubNameRsp = 126;
  optional ModifyClubBriefRsp modifyClubBriefRsp = 127;
  optional DoUgcMapHandlerRsp doUgcMapHandlerRsp = 128;
  optional SpringH5GetClubInfoRsp springH5GetClubInfoRsp = 129;
  optional SpringH5GetMyClubInfoRsp springH5GetMyClubInfoRsp = 130;
  optional SpringH5BatchRandomClubInfoRsp springH5BatchRandomClubInfoRsp = 131;
  optional LuckyStarGetHandbookInfoRsp luckyStarGetHandbookInfoRsp = 132;
  optional LuckyStarGetGiveInfoRsp luckyStarGetGiveInfoRsp = 133;
  optional LuckyStarReceiveGiveStarRsp luckyStarReceiveGiveStarRsp = 134;
  optional AqDoXiaoWoWelcomeClearRsp aqDoXiaoWoWelcomeClearRsp = 135;
  optional AqDoXiaoWoWelcomeBanRsp aqDoXiaoWoWelcomeBanRsp = 136;
  optional AqDoXiaoWoBulletinDeleteRsp aqDoXiaoWoBulletinDeleteRsp = 137;
  optional AqDoXiaoWoBulletinBanRsp aqDoXiaoWoBulletinBanRsp = 138;
  optional AqDoXiaoWoWelcomeSetFreeRsp aqDoXiaoWoWelcomeSetFreeRsp = 139;
  optional AqDoXiaoWoBulletinSendFreeRsp aqDoXiaoWoBulletinSendFreeRsp = 140;
  optional QueryXiaoWoGetBasicinfoRsp queryXiaoWoGetBasicinfoRsp = 141;
  optional AqDoMapNameRsp aqDoMapNameRsp = 142;
  optional AqDoMapCollectionBlurbRsp aqDoMapCollectionBlurbRsp = 143;
  optional GetKungFuPandaHelpRacingInfoRsp getKungFuPandaHelpRacingInfoRsp = 144;
  optional HelpKungFuPandaRacingRsp helpKungFuPandaRacingRsp = 145;
  optional DisbandSquadRsp disbandSquadRsp = 146;
  optional  AqDoFarmPutDownRsp aqDoFarmPutDownRsp = 147;
  optional  AqDoClearFarmRsp aqDoClearFarmRsp = 148;
  optional  QueryFarmGetItemListRsp queryFarmGetItemListRsp = 149;
  optional  DoFarmAddItemRsp doFarmAddItemRsp = 150;
  optional  DoFarmDelItemRsp doFarmDelItemRsp = 151;
  optional DoXiaoWoResetMapToVersionRsp doXiaoWoResetMapToVersionRsp = 152;
  optional QueryXiaoWoGetCreatorLayoutRsp queryXiaoWoGetCreatorLayoutRsp= 153;
  optional DoXiaoWoPubCreatorlayoutRsp doXiaoWoPubCreatorlayoutRsp= 154;
  optional DoXiaoWoDelCreatorlayoutRsp doXiaoWoDelCreatorlayoutRsp= 155;
  optional SyncGameTvRewardStatusRsp syncGameTvRewardStatusRsp = 156;
  optional DoFarmAdjustBuildingLevelRsp doFarmAdjustBuildingLevelRsp = 157;
  optional DoFarmAdjustMainExpRsp doFarmAdjustMainExpRsp = 158;
  optional DoFarmAddCropExpRsp doFarmAddCropExpRsp = 159;
  optional DoSetCommonUserLabelRsp doSetCommonUserLabelRsp = 160;
  optional DoModifyFriendIntimacyRsp doModifyFriendIntimacyRsp = 161;
  optional QueryFriendIntimacyRsp queryFriendIntimacyRsp = 162;
  optional AqDoFarmWelcomeClearRsp aqDoFarmWelcomeClearRsp = 163;
  optional AqDoFarmWelcomeBanRsp aqDoFarmWelcomeBanRsp = 164;
  optional AqDoFarmWelcomeSetFreeRsp aqDoFarmWelcomeSetFreeRsp = 165;
  optional AqDoFarmLiuYanMessageDeleteRsp aqDoFarmLiuYanMessageDeleteRsp = 166;
  optional AqDoFarmLiuYanMessageBanRsp aqDoFarmLiuYanMessageBanRsp = 167;
  optional AqDoFarmLiuYanMessageSendFreeRsp aqDoFarmLiuYanMessageSendFreeRsp = 168;
  optional DoModifyPlayerQualifyMaxDegreeRsp doModifyPlayerQualifyMaxDegreeRsp = 169;
  optional AqDoBanPlayerAlbumPicRsp aqDoBanPlayerAlbumPicRsp = 170;
  optional AqDoModifyRechargeLevelRsp aqDoModifyRechargeLevelRsp = 171;
  optional QueryPlayerHasItemRsp queryPlayerHasItemRsp = 172;
  optional AqDoModifyGameProtectionSettingsRsp aqDoModifyGameProtectionSettingsRsp = 173;
  optional AqQueryGameProtectionSettingsRsp aqQueryGameProtectionSettingsRsp = 174;
  optional DoFarmMonthCardRsp doFarmMonthCardRsp = 175;
  optional QueryPlayerCommodityBuyTimesRsp queryPlayerCommodityBuyTimesRsp = 176;
  optional DoUpdatePlayerCommodityBuyTimesRsp doUpdatePlayerCommodityBuyTimesRsp = 177;
  optional DoModifyQualifySeasonInfoRsp doModifyQualifySeasonInfoRsp = 178;
  optional QueryActivitySquadInfoRsp queryActivitySquadInfoRsp = 179;
  optional DoPlayerJoinSquadRsp doPlayerJoinSquadRsp = 180;
  optional DoClubSetOwnerRsp doClubSetOwnerRsp = 181;
  optional AqDoClubDissolveRsp aqDoClubDissolveRsp = 182;
  optional AqDoClubKickMemberRsp aqDoClubKickMemberRsp = 183;
  optional AddUgcPaidItemsRsp addUgcPaidItemsRsp = 184;
  optional DoWishActivityHelpBindRsp doWishActivityHelpBindRsp = 185;
  optional DoTrainingCampGetSportsmanDataRsp doTrainingCampGetSportsmanDataRsp = 186;
  optional DoTrainingCampTrainSportsmanRsp doTrainingCampTrainSportsmanRsp = 187;
  optional AnimalHandbookReceiveGiveAnimalRsp animalHandbookReceiveGiveAnimalRsp = 188;
  optional AqDoFarmForbidGiftSendingRsp aqDoFarmForbidGiftSendingRsp = 189;
  optional AqDoFarmBlockGiftMsgRsp aqDoFarmBlockGiftMsgRsp = 190;
  optional DoFarmAddFishExpRsp doFarmAddFishExpRsp = 191; 
  optional QueryFarmGetBasicinfoRsp queryFarmGetBasicinfoRsp = 192;
  optional ModifyClubHeatRsp modifyClubHeatRsp = 193;
  optional ModifyClubLBSRsp modifyClubLBSRsp = 194;
  optional BanClubHeatRankRsp banClubHeatRankRsp = 195;
  optional QueryGetUgcPlayedMapsRsp queryGetUgcPlayedMapsRsp = 196;
  optional DoModifyOmdrankRsp doModifyOmdrankRsp = 197;
  optional ModifyCupsProgressRsp modifyCupsProgressRsp = 198;
  optional DoCleanUserBagInteractCombRsp doCleanUserBagInteractCombRsp = 199;
  optional QueryFarmLiuYanMessageRsp queryFarmLiuYanMessageRsp = 200;
  optional ModifyChargeProgressRsp modifyChargeProgressRsp = 201;
  optional AqDoFarmHousePutDownRsp aqDoFarmHousePutDownRsp = 202;
  optional AqDoFarmHousePickAllFurnitureRsp aqDoFarmHousePickAllFurnitureRsp = 203;
  optional DoUpdateSeasonFashionEquipBookRsp doUpdateSeasonFashionEquipBookRsp = 204;
  optional AqDoFarmResetPetNameRsp aqDoFarmResetPetNameRsp = 215;
  optional AqDoFarmBanChangePetNameRsp aqDoFarmBanChangePetNameRsp = 216;
  optional QueryWishActivityHelpInfoRsp queryWishActivityHelpInfoRsp = 205;
  optional DeliverMidasProductRsp deliverMidasProductRsp = 206;
  optional AqDoModifyUgcRankScoreRsp aqDoModifyUgcRankScoreRsp = 207;
  optional QueryPlayerSuperCoreRankActivityAuthRsp queryPlayerSuperCoreRankActivityAuthRsp = 208;
  optional DoSuperCoreRankActivityScoreModifyRsp doSuperCoreRankActivityScoreModifyRsp = 209;
  optional QueryPlayerSuperCoreRankActivityScoreRsp queryPlayerSuperCoreRankActivityScoreRsp = 210;
  optional DoTrainingGetOjbectDataRsp doTrainingGetOjbectDataRsp = 211;
  optional DoTrainingTrainObjectRsp  doTrainingTrainObjectRsp  = 212;
  optional DoFarmModifyVillagerRsp doFarmModifyVillagerRsp = 213;
  optional DoOffEndRechargeDiamondNotifyRsp doOffEndRechargeDiamondNotifyRsp = 214;
  optional AqDoMallGiftCardResetWordsRsp aqDoMallGiftCardResetWordsRsp = 217;
  optional QueryChargeMoenyRsp queryChargeMoenyRsp = 218;
  optional ModifyTradingCardRsp modifyTradingCardRsp = 219;
  optional QueryCardTradeInfoRsp queryCardTradeInfoRsp = 220;
  optional ReceiveTradingCardRsp receiveTradingCardRsp = 221;
  optional DoFarmDelBuildingSkinRsp doFarmDelBuildingSkinRsp = 224;
  optional AqDoBirthdayCardResetWordsRsp aqDoBirthdayCardResetWordsRsp = 225;
  optional QueryLuckStarReceiveRsp queryLuckStarReceiveRsp = 227;
  optional QueryLuckStarGiveRsp queryLuckStarGiveRsp = 228;
  optional DoLuckStarReceiveRsp doLuckStarReceiveRsp = 229;
  optional DoLuckStarGiveRsp doLuckStarGiveRsp = 230;
  optional NotifyStreamInteractionCmdRsp notifyStreamInteractionCmdRsp = 231;
  optional DoFarmMagicAddOrDelRsp doFarmMagicAddOrDelRsp = 232;
  optional DoFarmMagicAdjustMpRsp doFarmMagicAdjustMpRsp = 233;
  optional DoFarmLayoutOpRsp doFarmLayoutOpRsp = 234;
  optional AqDoModifyCreatorMessageRsp aqDoModifyCreatorMessageRsp = 235;
  optional DoModifyPlayerArenaHeroCeScoreRsp doModifyPlayerArenaHeroCeScoreRsp = 236;
  optional DoTransferUserPlatRsp doTransferUserPlatRsp  = 237;
  optional QueryTransferAbilityRsp queryTransferAbilityRsp  = 238;
  optional DoReleaseTransferLockRsp doReleaseTransferLockRsp = 239;
  optional QueryUserRealPlatRsp queryUserRealPlatRsp = 240;
    optional AqDoFarmPartyClearRsp aqDoFarmPartyClearRsp = 241;
  optional AqDoFarmPartyBanRsp aqDoFarmPartyBanRsp = 242;
  optional AqDoFarmPartySetFreeRsp aqDoFarmPartySetFreeRsp = 243;
  optional AqDoFarmCookScreenClearRsp aqDoFarmCookScreenClearRsp = 244;
  optional AqDoFarmCookScreenBanRsp aqDoFarmCookScreenBanRsp = 245;
  optional AqDoFarmCookScreenSetFreeRsp aqDoFarmCookScreenSetFreeRsp = 246;
  optional AqDoFarmCookPutDownRsp aqDoFarmCookPutDownRsp = 247;
  optional DoFarmCookAddEmployeeRsp doFarmCookAddEmployeeRsp = 248;
  optional DoSetRaffleDailyLimitTagWhiteListRsp doSetRaffleDailyLimitTagWhiteListRsp = 249;
  optional DoFarmCookUpdateExtendInfoRsp doFarmCookUpdateExtendInfoRsp = 250;
  optional QueryFarmCookQueryExtendInfoRsp queryFarmCookQueryExtendInfoRsp = 251;
  optional QueryAccountTransferPreConditionRsp queryAccountTransferPreConditionRsp = 252;

  optional QueryStarPGuildInfoRsp queryStarPGuildInfoRsp = 300;
  optional QueryStarPTraderRsp queryStarPTraderRsp = 301;
  optional QueryStarPCommonPveDataRsp queryStarPCommonPveDataRsp = 302;
  optional QueryStarPPlayerDropItemsRsp queryStarPPlayerDropItemsRsp = 303;
  optional QueryStarPMapPosBuildingRsp queryStarPMapPosBuildingRsp = 304;
  optional QueryStarPPlayerItemsRsp queryStarPPlayerItemsRsp = 305;
  optional QueryStarPPlayerSimpleInfoRsp queryStarPPlayerSimpleInfoRsp = 306;
  optional QueryStarPPlayerRoleAttrRsp queryStarPPlayerRoleAttrRsp = 307;
  optional DoModifyStarPRsp doModifyStarPRsp = 308;
  optional QueryStarPItemInfosRsp queryStarPItemInfosRsp = 309;
  optional DoStarPAddMailRsp doStarPAddMailRsp = 320;

  optional AqDoModifyDebugDsRsp aqDoModifyDebugDsRsp = 321;
  
  optional DoSubUserBackpackItemRsp doSubUserBackpackItemRsp = 322;
  optional DoPetModifyLevelRsp  doPetModifyLevelRsp = 323;
  optional DoPetModifyPassiveSkillRsp doPetModifyPassiveSkillRsp  = 324;
  optional DoPetModifyStarLevelRsp doPetModifyStarLevelRsp  = 325;
  optional DoAccountModifyPowerRsp doAccountModifyPowerRsp  = 326;
  optional DoWorldAddItemRsp doWorldAddItemRsp  = 327;
  optional DoWorldSubItemRsp doWorldSubItemRsp  = 328;
  optional DoUserAddExpRsp doUserAddExpRsp  = 329;
  optional DoUserSubExpRsp doUserSubExpRsp  = 330;
  optional DoOperEquipAttrRsp DoOperEquipAttrRsp  = 331;
  optional AqDoPlayerItaBagResetRsp aqDoPlayerItaBagResetRsp = 332;
}

//////////////////////////idip 请求///////////////////////////////////
message sBaoxiaoMailInfo {
  optional uint32 MailCfgId = 1;
  optional string MailTitle = 2 [(urlcode) = true];
  optional string MailContent = 3 [(urlcode) = true];
  optional string MailStatus = 4 [(urlcode) = true];
  optional uint32 MailUniqueId = 5;
}

// 查询玩家个人信息请求
message QueryPersonalInfoReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                           // 大区（2-手Q）
  optional uint32 PlatId = 2;                           // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                           // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];    // Uid
  optional string GameId = 5;                           // MSDK分配的游戏id
  optional uint32 IsQueryUgc = 6;                       // 查询ugc数据标志（0-不查询, 1-查询）
  optional uint32 Partition = 7;                        // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message sDressInfo {
  optional int32 DressId = 1;                           // 装扮ID
}

message sGameFeaturesInfo {
  optional int32 FeatureId = 1;                         // 特性ID
  optional int32 FeatureValue = 2;                      // 特性参数
}

message sStatGragh {
  optional int64 TimeKey = 1;         // 时间戳作为KEY 某一天的开始时间 in seconds
  optional int64 OnlineTimeMs = 2;    // 累计在线时长
}

// 查询玩家个人信息应答
message QueryPersonalInfoRsp {
  optional int32 Result = 1;                            // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                           // 返回消息
  optional string RoleName = 3[(urlcode) = true];       // 昵称
  optional int32 Gender = 4;                            // 性别
  optional string AvatarUrl = 5;                        // 头像url
  optional string Signature = 6;                        // 个性签名
  optional int32 Level = 7;                             // 等级
  optional int64 Exp = 8;                               // 经验
  optional string Region = 9;                           // 地区
  optional string Title = 10;                           // 称号
  optional string TeamName = 11[(urlcode) = true];      // 战队名
  optional string TeamIdentity = 12;                    // 战队身份
  optional int32 Attention = 13;                        // 关注数
  optional int32 Skins = 14;                            // 皮肤
  optional int32 GameNum = 15;                          // 游戏次数
  optional int32 PrivilegeSwitch = 16;                  // 特权开关
  optional int32 PrivilegeLevel = 17;                   // 特权等级
  repeated sDressInfo DressList = 18;                   // 装扮信息列表
  optional string AvatarInfo = 19;                      // avatar信息
  repeated sGameFeaturesInfo GameFeaturesList = 20;     // 游戏特性参数列表
  optional string AvatarUrl40 = 21;                     // 40寸头像url
  optional string AvatarUrl100 = 22;                    // 100寸头像url
  optional string OpenId = 23;                          // 玩家openId
  optional uint32 RefuseFriends = 24;                   // 拒绝好友申请
  optional int32 PlatId = 25;                           // 平台ID
  optional int32 TotalLoginDays = 26;                   // 累计登陆天数
  optional int64 TotalOnlineTime = 27;                  // 总的在线时间（s）
  optional int64 LastLoginTime = 28;                    // 最后登录时间
  optional int32 IsCreator = 29;                        // 是否是创作者
  optional int64 UgcExp = 30;                           // 工匠值
  optional int32 UgcLv = 31;                            // ugc等级
  optional int32 PublishMapNums = 32;                   // 发布地图数量
  optional int32 WinTimes = 33;                         // 胜利次数
  optional int64 RegisterTime = 34;                     // 注册时间
  optional int64 Uid = 35;                              // uid
  optional int32 VipLevel = 36;                         // vip等级
  optional int32 VipExp = 37;                           // vip经验
  optional int64 MonthCardExpireTime = 38;              // 月卡剩余天数
  optional int32 BpLevel = 39;                          // bp等级
  optional int32 IsOnline = 40;                         // 用户在线情况
  optional int64 LastOfflineTime = 41;                  // 上一次离线时间, 当用户离线时有意义
  optional uint32 SvrId = 42;                           // 服务器编号
  optional uint32 LostDays = 44;                        // 流失天数
  optional int32 ChannelId = 45;                        // 渠道号
  optional string ClientVersion = 46;                   // 客户端版本
  optional string SystemSoftware = 47;                  // 系统
  optional string Network = 48;                         // 网络
  optional int32 IsBanLogin = 49;                       // 封禁账号情况
  optional int32 IsBanShutUp = 50;                      // 禁言情况
  optional int32  RegionId = 51;                        // 玩家地区id
  optional uint32 RegisterAccountType = 52;             // 注册账号渠道类型 TconndApiAccount
  optional uint32 LastLoginAccountType = 53;            // 最近登陆账号渠道类型 TconndApiAccount
  optional uint64 CreatorId = 54;                       // ugc创作者id
  optional uint32 RankDegree = 55;                      // 段位
  optional string RegisterChannelId = 56;               // 注册渠道号
  optional int32 RankDegreeID = 57;                     // 小段位id
  optional int32 RankDegreeStar = 58;                   // 小段位星级
  repeated sStatGragh DailyGraph = 59;                  // 每日统计信息
  optional string PlatNickName = 60;                    // 平台昵称
}

// 查询玩家openid请求
message QueryPersonalOpenidReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                           // 大区（2-手Q）
  optional uint32 PlatId = 2;                           // 平台（1-安卓，0-ios）
  optional int64 Uid = 3 [(field_meta_uuid) = true];    // Uid
  optional uint32 Partition = 4;                        // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 查询玩家openid应答
message QueryPersonalOpenidRsp {
  optional int32 Result = 1;                            // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                           // 返回消息
  optional string OpenId = 3;                           // OpenId
  optional string RoleName = 4[(urlcode) = true];       // 昵称
}

// 发送个人邮件请求
message SendPersonalMailReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  option (noCheckRoleTransferring) = true;
  optional uint32 AreaId = 1;                             // 大区（2-手Q）
  optional uint32 PlatId = 2;                             // 平台（1-安卓，0-ios）
  optional string OpenId = 4;                             // OpenId
  optional string MailTitle = 5 [(urlcode) = true];       // 邮件标题
  optional string MailContent = 6 [(urlcode) = true];     // 邮件内容
  optional uint32 MailValidTime = 7;                      // 邮件有效期
  repeated sAttachInfo AttachList = 8;                    // 附件
  optional uint32 Source = 9;                             // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                            // 流水号
  optional int64 Uid = 11 [(field_meta_uuid) = true];     // Uid
  optional int64 MailId = 12;                             // 邮件id, 用于idipsvr往gamesvr通知使用
  optional string Sender = 13;                            // 发件人
  optional string Url = 14 [(urlcode) = true];            // 跳转链接
  optional uint32 OverseaCfgId = 15;                      // 0-正常邮件 ！0-国际化邮件多语言配置id
  optional string AmsSerial = 16;                         // ams平台发货流水号
  optional uint32 Partition = 17;                         // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 MailType = 18;                          // 邮件类型, 取值详情参考枚举MailType, 选填参数, 不设置该参数默认是系统邮件类型
  optional uint32 MailExtraType = 19;                     // 邮件附加类型, 取值详情参考枚举MailExtraType, 选填参数, 暂不使用
  optional string CopiableContent = 20;                   // 邮件内可复制内容
  optional int32 IsStarred = 23;                          // 是否星标邮件
  repeated sMailBuyItem MailBuyItemList = 24;             // 购买商品
  optional string MailContentImg = 25;                    // 邮件内容图片
  optional string LowestVersion = 27;                     // 最低版本号(包含)
  optional string HighestVersion = 28;                    // 最高版本号(包含)
  optional int32 MailAttachmentType = 29;                 // 邮件附件类型, 参见 MailAttachmentType
  optional string MailAttachmentTypeContent = 30;         // 邮件附件类型内容, 根据 MailAttachmentType 自己去解析. (mailAttachmentType==1:多个ugcId用分号分隔)
}

// 发送个人邮件响应
message SendPersonalMailRsp {
  optional int32 Result = 1;                              // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                             // 返回消息
  optional uint32 MailUniqueId = 3;                       // 邮件id
}

// UgcIdList
message sUgcIdList {
  // repeated int64 UgcIdList = 1;                           // UGC地图id // 这样不行, 不支持二级嵌套
  optional string UgcIdList = 1;                         // UGC地图id列表(用:分割)
}

// 邮件附件信息
message sAttachInfo {
  optional uint32 ItemId = 1;                            // 道具id
  optional uint32 ItemNum = 2;                           // 道具数量
  optional uint32 ItemExpire = 3;                        // 道具有效期(单位为天)
}

// 邮件附件信息
message sMailAttachInfo {
  optional uint32 ItemId = 1;                            // 道具id
  optional uint32 ItemNum = 2;                           // 道具数量
  optional uint32 ItemExpire = 3;                        // 道具有效期(单位为天)
  // optional int32 amsActivityId = 4;                      // AMS活动实例
  // optional int32 moduleId = 5;                           // AMS礼包单
  // optional int32 packageGroupId = 6;                     // AMS礼包组
  optional string AmsParam = 4;                          // Ams参数(格式: "amsActivityId;moduleId;packageGroupId")
}

message sMailBuyItem {
    optional int32 CommodityId = 1;
    optional int32 Num = 2;
}

// 邮件 UGC 信息
message sMailUgcInfo {
  repeated int64 UgcId = 1;   // ugcId
}

// 多语言配置
message sMultiLanguageConf {
  optional uint32 UseType = 1;                            // 使用类型 enum MultiLanguageConfUseType
  optional string LanguageConf = 2;                       // 多语言json结构[{"language":1, "content":"txt"},{"language":2, "content":"txt"}]
}

// 增加多语言配置 请求
message AddMultiLanguageConfReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                             // 大区
  optional int64 Id = 2 ;                                 // 配置唯一id
  repeated sMultiLanguageConf MultiLanguageConf = 3;      // 多语言配置
  optional uint32 Source = 4;                             // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 5;                             // 流水号
  optional uint32 Partition = 6;                          // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 增加多语言配置 响应
message AddMultiLanguageConfRsp {
  optional int32 Result = 1;                              // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                             // 返回消息
}

// 道具信息
message sItemInfo {
  optional uint32 ItemId = 1;                             // 道具id
  optional int32 ItemNum = 2;                             // 道具数量
  optional uint32 Type = 3;                               // 道具类型
  optional string Name = 4;                               // 道具名称
  optional uint64 ExpireMs = 5;                           // 道具过期时间
}

// 查询用户道具列表请求
message QueryPlayerItemListReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 Page = 6;                                 // 查询页码
  optional uint32 ItemId = 7;                               // 查询道具id, 若不填写则为查询道具列表
}

// 查询用户道具列表响应
message QueryPlayerItemListRsp {
  optional int32 Result = 1;                                // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                               // 返回消息
  repeated sItemInfo ItemList = 3;                          // 道具列表
  optional uint32 TotalNum = 4;                             // 道具种类总数量
  optional uint32 TotalPage = 5;                            // 道具列表翻页总页码
}

// 道具操作类型
enum ItemOperateType {
  EN_ITEM_OPERATE_TYPE_DEDUCT = 0;                         // 扣除道具
  EN_ITEM_OPERATE_TYPE_GIVE = 1;                           // 赠送道具
}

enum IdipItemNumCheckType {
  EN_ITEM_NUM_CHECK_TYPE_NO = 0;                            // 不检查道具数量
  EN_ITEM_NUM_CHECK_TYPE_YES = 1;                           // 检查道具数量
}

// 修改道具请求
message DoModifyItemReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];        // Uid
  optional uint32 OperateType = 6;                          // 操作类型, 见枚举ItemOperateType所示
  repeated sItemInfo ItemList = 7;                          // 道具列表
  optional uint32 CheckItemNumFlag = 8;                     // 检查道具数量标志, 见枚举IdipItemNumCheckType所示
  optional uint32 Source = 9;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                              // 流水号
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 修改道具响应
message DoModifyItemRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 修改任务进度请求
message DoModifyTaskProgressReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 TaskId = 5;                               // 任务id
  optional uint64 Progress = 6;                             // 任务进度
  optional uint32 Source = 7;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;                               // 流水号
  optional uint32 Partition = 9;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string TaskIdList = 10;                          // 任务id列表(分号分隔)
  optional string ProgressDiffList= 11;                     // 任务进度差值(分号分隔) (+:增加进度值 -:减少进度值(最少只能减到0))
  optional string ProgressTotalList = 12;                   // 任务进度总值(分号分隔)
}

// 修改任务进度响应
message DoModifyTaskProgressRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

enum BpDataType {
  EN_BP_DATA_TYPE_EXP = 0;                                  // bp经验
  EN_BP_DATA_TYPE_LEVEL = 1;                                // bp资格
}

enum BpLevelOperateType {
  EN_BP_LEVEL_OPERATE_TYPE_GRANT = 0;                       // bp资格发放
  EN_BP_LEVEL_OPERATE_TYPE_RECYCLE = 1;                     // bp资格回收
}

// 修改bp数据请求
message DoModifyBpDataReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 DataType = 5;                             // 数据类型, 见枚举BpDataType所示
  optional int32 BpExpModifyValue = 6;                      // bp经验修改值
  optional uint32 BpLevelOperateType = 7;                   // bp资格操作类型, 见枚举BpLevelOperateType所示
  optional uint32 BpLevelType = 8;                          // bp资格类型, 0: 普通; 1: 进阶; 2: 荣耀
  optional uint32 Source = 9;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                              // 流水号
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 修改bp数据响应
message DoModifyBpDataRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message DelPersonalMailReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 MailUniqueId = 5;                         // 邮件id
  optional uint32 Source = 6;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                               // 流水号
  optional uint32 Partition = 8;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DelPersonalMailRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 玩家安全处罚请求
message DoSecurityPunishReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 PunishType = 5;                           // 处罚类型, 见枚举BanType所示
  optional uint64 PunishLimitTime = 6;                      // 处罚时长
  optional string PunishReason = 7 [(urlcode) = true];      // 处罚原因
  optional uint32 Source = 8;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 9;                               // 流水号
  optional uint32 Partition = 10;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 玩家安全处罚响应
message DoSecurityPunishRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 玩家信息修改类型qu
enum IdipPlayerInfoModifyType {
  EN_PLAYER_INFO_MODIFY_TYPE_NICKNAME = 0;                  // 用户昵称
  EN_PLAYER_INFO_MODIFY_TYPE_PIC_URL = 1;                   // 用户头像
  EN_PLAYER_INFO_MODIFY_TYPE_SIGNATURE = 2;                 // 个性签名
  EN_PLAYER_INFO_MODIFY_TYPE_FARM_SIGNATURE = 3;            // 农场个性签名
}

// 修改玩家信息请求
message DoModifyPlayerInfoReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 ChangeType = 5;                           // 修改类型, 见枚举IdipPlayerInfoModifyType所示
  optional string ChangeContent = 6 [(urlcode) = true];     // 修改后内容
  optional string Reason = 7 [(urlcode) = true];            // 修改理由
  optional uint32 Source = 8;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 9;                               // 流水号
  optional uint32 Partition = 10;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 修改玩家信息响应
message DoModifyPlayerInfoRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 下架ugc地图请求
message DoRemoveUgcMapReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint64 MapId = 5;                                // 待下架的地图id
  optional string EvilStr = 6 [(urlcode) = true];           // 恶意描述
  optional string ReportStr = 7 [(urlcode) = true];         // 举报字符串
  optional uint32 Source = 8;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 9;                               // 流水号
  optional uint32 Partition = 10;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 下架ugc地图响应
message DoRemoveUgcMapRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 发送全服邮件请求
message SendGlobalMailReq {
  option (handler) = IH_IdipSvr;
  optional string MailTitle = 1 [(urlcode) = true];       // 邮件标题
  optional string MailContent = 2 [(urlcode) = true];     // 邮件内容
  optional string Sender = 3 [(urlcode) = true];          // 发件人
  repeated sAttachInfo AttachList = 4;                    // 附件
  optional uint64 BeginTime = 5;                          // 开始发送时间
  optional uint64 EndTime = 6;                            // 结束发送时间
  optional string Url = 7 [(urlcode) = true];             // 跳转链接
  optional int32 MailGroupId = 8;                         // 邮件分组id
  optional uint32 Source = 9;                             // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                            // 流水号
  repeated uint32 AccountChannels = 11;                   // 账号渠道限制(空表示不限制)
  optional uint32 IsOversea = 12;                         // 是否是国际化邮件（是的话，MailContent,MailTitle, Sender 为json 格式）
  optional uint32 MinLevel = 13;                          // 最低等级
  optional uint32 AreaId = 14;                            // 大区
  repeated uint32 RegisterChannels = 15;                  // 用户注册渠道限制(空表示不限制)
  optional uint32 Partition = 16;                         // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string LowestVersion = 17;                     // 最低版本号(包含)
  optional string HighestVersion = 18;                    // 最高版本号(包含)
  repeated uint32 LoginChannelIds = 19;                   // 用户登录渠道限制(空表示不限制)
  repeated uint32 PlatIds = 20;                           // 用户账号平台id限制(空表示不限制)
  repeated sMailBuyItem MailBuyItemList = 21;             // 购买商品
  optional string MailContentImg = 22;                    // 邮件内容图片
  optional int32 MailAttachmentType = 23;                 // 邮件附件类型, 参见 MailAttachmentType
  optional string MailAttachmentTypeContent = 24;         // 邮件附件类型内容, 根据 MailAttachmentType 自己去解析. (mailAttachmentType==1:多个ugcId用分号分隔)
}

// 发送全服邮件响应
message SendGlobalMailRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 发送全服纯文本邮件请求
message SendGlobalTextMailReq {
  option (handler) = IH_IdipSvr;
  optional uint32 AreaId = 1;                               // 大区
  optional string MailTitle = 2 [(urlcode) = true];         // 邮件标题
  optional string MailContent = 3 [(urlcode) = true];       // 邮件内容
  optional string Sender = 4 [(urlcode) = true];            // 发件人
  optional uint64 BeginTime = 5;                            // 开始发送时间
  optional uint64 EndTime = 6;                              // 结束发送时间
  optional string Url = 7 [(urlcode) = true];               // 跳转链接
  optional int32 MailGroupId = 8;                           // 邮件分组id
  repeated uint32 AccountChannels = 9;                      // 账号渠道限制(空表示不限制)
  optional uint32 IsOversea = 10;                           // 是否是国际化邮件(是的话，MailContent,MailTitle, Sender 为json 格式）
  optional uint32 MinLevel = 11;                            // 最低等级
  optional uint32 Source = 12;                              // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 13;                              // 流水号
  optional uint32 Partition = 14;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 发送全服纯文本邮件响应
message SendGlobalTextMailRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 全服踢人请求
message DoGlobalSvrKickReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                               // 大区
  optional uint64 CloseTime = 2;                            // 停服时间时间戳
  optional uint64 OpenTime = 3;                             // 重新开启时间戳
  optional int32 RegionId = 4;                              // 国家地区id
  repeated uint32 AccountChannels = 5;                      // 账号渠道类型(空表示不限制)
  optional int32 ErrCode = 6;                               // 给client 错误码
  optional uint32 Source = 7;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;                               // 流水号
  optional uint32 Partition = 9;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 全服踢人响应
message DoGlobalSvrKickRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 审核结果回调请求
message AuditInfoCallbackReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional uint32 WorldId = 3;                              // 小区号
  optional string OpenId = 4;                               // OpenId
  optional uint64 MapId = 5;                                // 地图id
  optional uint32 Type = 6;                                 // 审核类型
  optional uint32 ProcessResult = 7;                        // 审核结果
  optional string EvilStr = 8 [(urlcode) = true];           // 恶意描述
  optional string ReportStr = 9 [(urlcode) = true];         // 举报字符串
  optional int64 Uid = 10 [(field_meta_uuid) = true];       // Uid
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 审核结果回调响应
message AuditInfoCallbackRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 查询玩家统计信息请求
message QueryPlayerStatInfoReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint64 BeginTime = 5;                            // 开始时间
  optional uint64 EndTime = 6;                              // 结束时间
  optional uint32 Partition = 7;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 查询玩家统计信息响应
message QueryPlayerStatInfoRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
  optional uint32 GameTimes = 3;                            // 游戏次数
  optional uint32 RechargeTimes = 4;                        // 充值次数
  optional uint32 LoginDays = 5;                            // 登录天数
}

// 玩家安全处罚解除请求
message DoSecurityPunishRemoveReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint32 PunishRemoveType = 5;                       // 处罚解除类型, 见枚举BanType所示
  optional string PunishRemoveReason = 6 [(urlcode) = true];  // 处罚解原因
  optional uint32 Source = 7;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;                                 // 流水号
  optional uint32 Partition = 9;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 玩家安全处罚解除响应
message DoSecurityPunishRemoveRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 发送个人纯文本邮件请求
message SendPersonalTextMailReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  option (noCheckRoleTransferring) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 4;                               // OpenId
  optional string MailTitle = 5 [(urlcode) = true];         // 邮件标题
  optional string MailContent = 6 [(urlcode) = true];       // 邮件内容
  optional uint32 MailValidTime = 7;                        // 邮件有效期
  optional uint32 Source = 9;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                              // 流水号
  optional int64 Uid = 11 [(field_meta_uuid) = true];       // Uid
  optional int64 MailId = 12;                               // 邮件id, 用于idipsvr往gamesvr通知使用
  optional string Sender = 13;                              // 发件人
  optional string Url = 14 [(urlcode) = true];              // 跳转链接
  optional string GameId = 15;                              // MSDK分配的游戏id
  optional int32 ChannelId = 16;                            // gopenid所在的渠道id
  optional uint32 SendTime = 17;                            // 取消注销的时间，Unix时间戳
  optional uint32 OverseaCfgId = 18;                        // 0-正常邮件 ！0-国际化邮件多语言配置id
  optional uint32 Partition = 19;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 MailType = 20;                            // 邮件类型, 取值详情参考枚举MailType, 选填参数, 不设置该参数默认是系统邮件类型
  optional uint32 MailExtraType = 21;                       // 邮件附加类型, 取值详情参考枚举MailExtraType, 选填参数, 暂不使用
  optional string CopiableContent = 22;                     // 邮件内可复制内容
  optional int32 IsStarred = 23;                             // 是否星标邮件
  optional string LowestVersion = 24;                       // 最低版本号(包含)
  optional string HighestVersion = 25;                      // 最高版本号(包含)
  optional string MailContentImg = 26;                      // 邮件内容图片
  optional int32 MailAttachmentType = 27;                   // 邮件附件类型, 参见 MailAttachmentType
  optional string MailAttachmentTypeContent = 28;           // 邮件附件类型内容, 根据 MailAttachmentType 自己去解析. (mailAttachmentType==1:多个ugcId用分号分隔)
}

// 发送个人纯文本邮件响应
message SendPersonalTextMailRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
  optional uint32 MailUniqueId = 3;                         // 邮件id
}

// 踢人请求
message DoKickPlayerReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional int32 IsAll = 5;                                 // 用户是否全区服踢下线（0否 1是）
  optional uint32 Source = 6;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                               // 流水号
  optional uint32 Partition = 8;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 KickBattle = 9;                            // 0：默认，1：踢出对局
}

// 踢人响应
message DoKickPlayerRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// idip房间信息修改类型
enum IdipRoomInfoModifyType {
  EN_ROOM_INFO_MODIFY_TYPE_NAME = 0;                        // 房间名称修改
}

// 房间信息修改请求
message DoModifyRoomInfoReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional int64 RoomId = 5;                                // 房间id
  optional uint32 ModifyType = 6;                           // 修改类型, 见枚举IdipRoomInfoModifyType所示
  optional string ModifyContent = 7 [(urlcode) = true];     // 修改内容
  optional uint32 Source = 8;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 9;                               // 流水号
  optional uint32 Partition = 10;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 房间信息修改响应
message DoModifyRoomInfoRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 房间解散请求
message DoDisbandRoomReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional int64 RoomId = 5;                                // 房间id
  optional uint32 Source = 6;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                               // 流水号
  optional uint32 Partition = 8;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 房间解散响应
message DoDisbandRoomRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// idip地图信息修改类型
enum IdipMapInfoModifyType {
  EN_MAP_INFO_MODIFY_TYPE_NAME = 0;                         // 地图名称修改
  EN_MAP_INFO_MODIFY_TYPE_DESC = 1;                         // 地图描述修改
  EN_MAP_INFO_MODIFY_TYPE_PREVIEW_PIC = 2;                  // 地图预览图修改
  EN_MAP_INFO_MODIFY_TYPE_RE_PUBLISH = 3;                   // 重新上架地图
  EN_MAP_INFO_MODIFY_TYPE_CLEAR_ACHIEVEMENT = 4;            // 清空所有成就
  EN_MAP_INFO_MODIFY_TYPE_CLEAR_DESCRIPTION = 5;            // 清空地图说明
  EN_MAP_INFO_MODIFY_TYPE_MODIFY_DESCRIPTION = 6;           // 修改地图说明
  EN_MAP_INFO_MODIFY_TYPE_RESET_LOADING = 7;                // 重置地图的loading
}

// 地图信息修改请求
message DoModifyMapInfoReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional int64 MapId = 5;                                 // 地图id
  optional uint32 ModifyType = 6;                           // 修改类型, 见枚举IdipMapInfoModifyType所示
  optional string ModifyContent = 7 [(urlcode) = true];     // 修改内容
  optional string EvilStr = 8 [(urlcode) = true];           // 恶意描述
  optional string ReportStr = 9 [(urlcode) = true];         // 举报字符串
  optional uint32 Source = 10;                              // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 11;                              // 流水号
  optional uint32 Partition = 12;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 地图信息修改响应
message DoModifyMapInfoRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 地图阶段重置请求
message DoMapStageResetReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional int64 MapId = 5;                                 // 地图id
  optional string EvilStr = 6 [(urlcode) = true];           // 恶意描述
  optional string ReportStr = 7 [(urlcode) = true];         // 举报字符串
  optional uint32 Source = 8;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 9;                               // 流水号
  optional uint32 Partition = 10;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 地图阶段重置响应
message DoMapStageResetRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 全服封禁请求
message DoGlobalBanProcessReq {
  option (handler) = IH_IdipSvr;
  optional uint32 BanType = 1;                              // 封禁类型, 详情见枚举BanType所示
  optional uint64 BeginTime = 2;                            // 封禁开始时间
  optional uint64 EndTime = 3;                              // 封禁结束时间
  optional string Reason = 4 [(urlcode) = true];            // 封禁原因
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 AreaId = 8;                               // 大区（2-手Q）
}

// 全服封禁响应
message DoGlobalBanProcessRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 清理用户聊天请求
message DoCleanUserChatReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional int32 ChannelType = 5;                           // 频道类型
  optional int64 ChannelId = 6;                             // 频道id
  optional int64 ChannelSubId = 7;                          // 频道子id
  optional uint32 Source = 8;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 9;                               // 流水号
  optional uint32 Partition = 10;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 清理用户聊天响应
message DoCleanUserChatRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 好友亲密度修改请求
message DoModifyFriendRelationReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional string FriendOpenId = 5;                         // 好友Openid
  optional uint32 FriendPlatId = 6;                         // 好友平台（1-安卓，0-ios）
  optional int64 FriendUid = 7;                             // 好友Uid
  optional int32 Relation = 8;                              // 亲密度修改值（正增负减）
  optional uint32 Source = 9;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                              // 流水号
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 IsUnilateral = 12;                         // 是否单边补发(0:不是 1:是)
}

// 好友亲密度修改响应
message DoModifyFriendRelationRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// vip经验修改请求
message DoModifyPlayerVipExpReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional int32 VipExp = 5;                                // vip经验修改值（正增负减）
  optional uint32 Source = 6;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                               // 流水号
  optional uint32 Partition = 8;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// vip经验修改响应
message DoModifyPlayerVipExpRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 月卡有效期修改请求
message DoModifyPlayerMonthCardReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional string Id = 5;                                   // 月卡配置id
  optional int32 ExpireTime = 6;                            // 月卡剩余天数修改值（正增负减）
  optional uint32 Source = 7;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;                               // 流水号
  optional uint32 Partition = 9;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 月卡有效期修改响应
message DoModifyPlayerMonthCardRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 设置用户标签请求-用户问卷
message DoSetUserLabelReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional int32 UserLabel = 5;                             // 用户标签
  optional uint32 Source = 6;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                               // 流水号
  optional uint32 Partition = 8;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 设置用户标签响应-用户问卷
message DoSetUserLabelRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 新手引导任务处理请求
message DoGuideTaskProcessReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 GuideTaskId = 5;                          // 新手引导任务id
  optional uint32 Source = 6;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                               // 流水号
  optional uint32 Partition = 8;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 新手引导任务处理响应
message DoGuideTaskProcessRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// idip好友类型
enum IdipFriendType {
  EN_FRIEND_TYPE_GAME = 1;                                  // 好友类型-游戏好友
  EN_FRIEND_TYPE_PLAT = 2;                                  // 好友类型-平台好友
}

// idip好友搜索类型
enum IdipFriendSearchType {
  EN_FRIEND_SEARCH_TYPE_KEYWORD = 1;                        // 基于关键词搜索(昵称/短号)
  EN_FRIEND_SEARCH_TYPE_UID = 2;                            // 基于uid搜索
}

// idip好友排序类型
enum IdipFriendSortType {
  EN_FRIEND_SORT_TYPE_DEFAULT = 0;                        // 默认不排序
  EN_FRIEND_SORT_TYPE_ONLINE_AND_Relation = 1;            // 基于在线情况和亲密度排序
}

// 玩家好友信息查询请求
message QueryPlayerFriendListReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 FriendType = 6;                           // 好友类型, 见枚举IdipFriendType所示
  optional uint32 Page = 7;                                 // 当前页码
  optional uint32 SearchType = 8;                           // 查询数据类型, 见枚举IdipFriendSearchType所示
  optional string OriginalData = 9 [(urlcode) = true];      // 查询源数据
  optional uint32 SortType = 10;                            // 排序类型, 见枚举IdipFriendSortType所示
  optional uint32 PageNum = 11;                             // 每页显示条数
}

// 好友信息
message sIdipFriendInfo {
  optional string OpenId = 1;                               // OpenId
  optional int64 Uid = 2;                                   // Uid
  optional string Nickname = 3;                             // 昵称
  optional string Profile = 4;                              // 头像
  optional uint64 LastLoginTime = 5;                        // 上次登录时间
  optional uint64 Relation = 6;                             // 亲密度
  optional int32 IsOnline = 7;                              // 用户在线情况
  optional int32 Level = 8;                                 // 等级
  optional uint32 RankDegree = 9;                           // 段位
  optional int32 RankDegreeID = 10;                         // 小段位id
  optional int32 RankDegreeStar = 11;                       // 小段位星级
  optional int32 Area = 12;                                 // 区服信息
  optional uint64 LastOfflineTime = 13;                     // 上次离线时间
  optional uint64 ShortUid = 14;                            // 短号
  optional uint32 PlatId = 15;                              // 平台（1-安卓，0-ios）
  optional uint32 Gender = 16;                              // 性别（1-男 2-女 0-未知）
  optional string PlatNickName = 17;                        // 平台昵称
}

// 玩家好友信息查询响应
message QueryPlayerFriendListRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
  optional uint32 TotalNum = 3;                             // 好友总数
  optional uint32 TotalPage = 4;                            // 好友列表分页总页码
  repeated sIdipFriendInfo FriendList = 5;                  // 好友列表
}

// 修改玩家地区请求
message DoModifyPlayerRegionReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional int32 RevisedRegionId = 5;                       // 修改后的地区id (地区表里面对应的id)
  optional uint32 Source = 6;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                               // 流水号
  optional uint32 Partition = 8;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 修改玩家地区响应
message DoModifyPlayerRegionRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 账号注销回调请求
message AccountCancelCallbackReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional string GameId = 5;                               // MSDK分配的游戏id
  optional int32 ChannelId = 6;                             // 注销的渠道id
  optional uint32 DelTime = 7;                              // 实际注销的时间，Unix时间戳
  optional uint32 Partition = 8;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 账号注销回调响应
message AccountCancelCallbackRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 账号状态变更回调协议
message AccountStateChangeCallbackReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional string GameId = 5;                               // MSDK分配的游戏id
  optional int32 ChannelId = 6;                             // 注销的渠道
  optional uint32 State = 7;                                // 注销状态，1-处于注销冷静期中， 2-取消注销，注销流程终止 3-冷静期结束，已正常注销
  optional uint32 UpdateTime = 8;                           // 状态变更的时间，Unix时间戳
  optional uint32 Partition = 9;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 账号状态变更回调响应
message AccountStateChangeCallbackRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message DirAddIpBlackInfoReq {
  option (handler) = IH_DirSvr;
  option (hacked) = true;
  optional string Ip = 1 [(urlcode) = true];                // ip
  optional int32 Duration = 2;                              // 持续时间
  optional string Reason = 3 [(urlcode) = true];            // 原因
  optional uint32 Partition = 4;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DirAddIpBlackInfoRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message DirRemoveIpBlackInfoReq {
  option (handler) = IH_DirSvr;
  option (hacked) = true;
  optional string Ip = 1 [(urlcode) = true];                // ip
  optional uint32 Partition = 2;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DirRemoveIpBlackInfoRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message DirIpBlackInfo {
  optional string Ip = 1 [(urlcode) = true];                // ip
  optional string CreateTime = 2 [(urlcode) = true];        // 创建时间
  optional int32 Duration = 3;                              // 持续时间
  optional string EndTime = 4;                              // 结束时间
  optional string Reason = 5 [(urlcode) = true];            // 原因
}

message DirGetIpBlackInfoReq {
  option (handler) = IH_DirSvr;
  option (hacked) = true;
  optional string Ip = 1 [(urlcode) = true];                // ip
  optional uint32 Partition = 2;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DirGetIpBlackInfoRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
  repeated DirIpBlackInfo BlackInfo= 3;                     // 黑名单信息
}

message DirGetIpBlackInfoListReq {
  option (handler) = IH_DirSvr;
  option (hacked) = true;
  optional uint32 Partition = 1;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DirGetIpBlackInfoListRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
  repeated DirIpBlackInfo BlackInfoList = 3;                // 黑名单列表
}

// 游戏玩家信息查询请求
message QueryGameUserInfoReq {
  option (handler) = IH_IdipSvr;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                               // 大区（1-微信, 2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // 平台openid
  optional uint32 AccountType = 4;                          // 账号类
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 游戏玩家信息查询相应
message QueryGameUserInfoRsp {
  optional int32 Result = 1;                                // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                               // 返回消息
  optional string GopenId = 3;                              // MSDK组件分配的用户gopenid
  optional string NickName = 4;                             // 玩家游戏昵称
  optional string AvatarUrl = 5;                            // 玩家游戏头像
}

// IDip专用UGC作品原文数据结构
message IdipUgcOriginalTextData {
  optional int32 Type = 1;				                    // 翻译类型 0.作品名 1.作品描述 2.局内文本
  optional int32 ID = 2;					                // 局内文本标识ID, 客户端定位到局内的文本位置
  optional string Content = 3;			                    // 原文文本
}

message AqDoXiaoWoBlockReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int64 Duration = 8;
  optional string Evilstr = 9 [(urlcode) = true];
  optional string ReportStr = 10 [(urlcode) = true];
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoBlockRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoXiaoWoBlockCancelReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoBlockCancelRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoXiaoWoPutDownReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int64 Duration = 7;
  optional string Evilstr = 9[(urlcode) = true];
  optional string ReportStr = 10[(urlcode) = true];
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoPutDownRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoXiaoWoSetHotReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional float Ratio = 7;                                 // 权重比例
  optional int64 Duration = 8;
  optional uint32 Partition = 9;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoSetHotRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoXiaoWoClearInstructionAndImageReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional string Evilstr = 9[(urlcode) = true];
  optional string ReportStr = 10[(urlcode) = true];
  optional uint32 Partition = 11;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoClearInstructionAndImageRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoXiaoWoReportSafeAuditResultReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                     // Uid
  optional uint32 Source = 5;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                                 // 流水号
  optional uint32 Type = 7;
  optional uint32 Result = 8;
  optional string Evilstr = 9[(urlcode) = true];
  optional string ReportStr = 10[(urlcode) = true];
  optional uint32 Partition = 11;                             // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoReportSafeAuditResultRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoXiaoWoResetMapReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int32 Cancel = 7;                                // 取消打击
  optional string Evilstr = 9[(urlcode) = true];
  optional string ReportStr = 10[(urlcode) = true];
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoResetMapRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 打开超级红包请求
message OpenSuperLuckyMoneyReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 FArea = 1;                                  // 分享用户大区（2-手Q）
  optional uint32 FPlatid = 2;                                // 分享用户平台（1-安卓，0-ios）
  optional string FOpenid = 3;                                // 分享用户OpenId
  optional int64 FUid = 4;                                    // 分享用户uid
  optional uint32 Area = 5;                                   // 接受用户大区（2-手Q）
  optional uint32 Platid = 6;                                 // 接受用户平台（1-安卓，0-ios）
  optional string Openid = 7;                                 // 接受用户OpenId
  optional int64 Uid = 8 [(field_meta_uuid) = true];          // 接受用户uid
  optional int32 IActivityId = 9;                             // 活动id
  optional int64 Bagid = 10;                                  // 分享的红包id
  optional uint32 Partition = 11;                             // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 打开超级红包响应
message OpenSuperLuckyMoneyRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  repeated sItemInfo ItemList = 3;                            // 道具列表
}

// 查询超级红包信息请求
message QuerySuperLuckyMoneyInfoReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional int32 IActivityId = 5;                             // 活动id
  optional int64 Bagid = 6;                                   // 分享的红包id
  optional uint32 Partition = 7;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 红包信息
message sLuckyMoneyRewardInfo {
  optional string ReceivedOpenId = 1;                         // OpenId
  optional int64 ReceivedUid = 2;                             // Uid
  optional string ReceivedNickname = 3;                       // 昵称
  optional string ReceivedProfile = 4;                        // 头像
  optional uint32 ItemId1 = 5;                                // 道具1ID
  optional uint32 ItemNum1 = 6;                               // 道具1数量
  optional uint32 ItemId2 = 7;                                // 道具2ID
  optional uint32 ItemNum2 = 8;                               // 道具2数量
  optional uint32 ItemId3 = 9;                                // 道具3ID
  optional uint32 ItemNum3 = 10;                              // 道具3数量
  optional uint32 ItemId4 = 11;                               // 道具4ID
  optional uint32 ItemNum4 = 12;                              // 道具4数量
  optional uint32 ItemId5 = 13;                               // 道具5ID
  optional uint32 ItemNum5 = 14;                              // 道具5数量
  optional int64 ReceivedTimeMs = 15;                         // 领取红包时间
}

// 查询超级红包信息响应
message QuerySuperLuckyMoneyInfoRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  repeated sLuckyMoneyRewardInfo RewardList = 3;              // 红包信息
}

// 开关播状态同步请求
message SwitchPlayStatusSynchronizeReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional string IlinkToken = 4;                             // 标识一场直播
  optional uint32 OperateType = 5;                            // 操作类型（1-开启, 2-关闭）
  optional int64 Uid = 6 [(field_meta_uuid) = true];          // Uid
  optional uint32 Partition = 7;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 PlatTypeVal = 8;                             // 平台类型 参考StreamPlatType
  optional string PlatPlayInfoId = 9;                         // 平台配置的直播玩法id
}

// 开关播状态同步响应
message SwitchPlayStatusSynchronizeRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 直播心跳查询请求
message QueryLiveHeartbeatReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional string IlinkToken = 4;                             // 标识一场直播
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional uint32 Partition = 6;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 直播心跳查询响应
message QueryLiveHeartbeatRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional uint32 Status = 3;                                 // 开播状态（1-开播, 2-未开播）
  optional string IlinkToken = 4;                             // 标识一场直播
}

// 查询房间用户信息请求
message QueryRoomUserInfoReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional uint64 TeamId = 4;                                 // 房间id
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 Type = 6;                                    // 类型
  optional uint32 Partition = 7;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 队伍成员信息
message RoomMemberInfo {
  optional string NickName = 1;                               // 昵称
  optional string HeadUrl = 2;                                // 头像
  optional string RankName = 3;                               // 段位名称
  optional string RankUrl = 4;                                // 段位头像
  optional string Ext1 = 5;                                   // 扩展数据
}

// 查询房间用户信息响应
message QueryRoomUserInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 TeamStatus = 3;                              // 队伍/房间状态
  optional string TeamMode = 4;                               // 组队模式
  repeated RoomMemberInfo TeamMembersList = 5;                // 队伍成员信息
  optional string MasterNickName = 6;                         // 队长/房主的昵称
  optional string MasterHeadUrl = 7;                          // 队长/房主的头像
  optional string MasterRankName = 8;                         // 队长/房主的段位名称
  optional string MasterRankUrl = 9;                          // 队长/房主的段位图标
  optional int32 TeamNumLimit = 10;                           // 队伍人数最大值
  optional int32 IsInTeam = 11;                               // 当前玩家是否在队伍中
  optional string PlayName = 12;                              // 玩法名称
  optional string MapName = 13;                               // 地图名称
  optional string MapImg = 14;                                // 地图展示图片
  optional string MinRankName = 15;                           // 段位下限
  optional string MaxRankName = 16;                           // 段位上限
  optional string MasterOpenId = 17;                          // 房主gopenid
  optional uint64 MasterUid = 18;                             // 房主uid
  optional string VisitorOpenId = 19;                         // 访问者openid
  optional int32 KickOut = 20;                                // 是否被踢出房间
  optional int32 RoomType = 21;                               // 房间类型
  optional int32 GameModeType = 22;                           // 游戏模式类型
  optional string ArkTitle = 23;                              // ark标题
  optional string ArkHeader = 24;                             // ark封面图
  optional uint64 CreateTime = 25;                            // 创建时间戳, 单位是ms
  optional uint64 TeamId = 26;                                // 游戏房间id
}

// 点击活动分享外链
message ClickActivityOuterLinkReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 分享用户大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 分享用户平台（1-安卓，0-ios）
  optional string FOpenid = 3;                                // 分享用户OpenId
  optional int64 FUid = 4;                                    // 分享用户uid
  optional string MOpenid = 5;                                // 接受邀请用户的gopenid
  optional int32 IActivityId = 6;                             // 活动号
  optional int64 FInvitationId = 7;                           // 邀请ID
  optional int32 IActivityType = 8;                           // 活动类型，见ActivityType
  optional uint32 Partition = 9;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message ClickActivityOuterLinkRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 RetCode = 3;                                 // 业务处理返回的NKErrorCode
}

message QueryInvitedFriendInfoReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 邀请方的大区（2-手Q）
  optional uint32 Platid = 2 [json_name = "PlatId"];          // 邀请方的平台（1-安卓，0-ios）
  optional string Openid = 3;                                 // 邀请方的OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // 邀请方的uid
  optional int32 IActivityId = 5;                             // 活动号
  optional int32 IActivityType = 6;                           // 活动类型
  optional uint32 Partition = 7;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message QueryInvitedFriendInfoRsp {
  message InvitedFriendInfo {
    optional string MOpenid = 1;                              // 接受邀请玩家的Openid
    optional int64 MUid = 2;                                  // 接受邀请玩家的uid
    optional int64 RegisterTimeMs = 3;                        // 注册时间
    optional int32 Level = 4;                                 // 玩家等级
  }

  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 RetCode = 3;                                 // 业务处理返回的NKErrorCode
  repeated InvitedFriendInfo PlayerList = 4;                  // 邀请的玩家列表
}

// IDip专用UGC作品翻译数据结构
message IdipUgcTranslateData {
  optional int32 Type = 1;				                      // 翻译类型 0.作品名 1.作品描述 2.局内文本
  optional int32 ID = 2;					                  // 局内文本标识ID, 客户端定位到局内的文本位置
  optional int32 State = 3;				                      // 翻译状态 0.未翻译 1.已翻译 2.翻译中 3.翻译失败
  optional int32 TranslationTime = 4;		                  // 翻译时间
  optional int32 TssState = 5;			                      // 敏感词状态 0.未检测 1.已检测 2.检测未通过
  optional string Content = 6;			                      // 翻译后文本
}

// 查询UGC地图局外翻译文本请求
message GetUgcOutGameTranslateDataReq {
  option (handler) = IH_TranslateSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional int64 UgcID = 1;					                  // 作品ID
  optional int32 LangID = 2;				                  // 语言ID
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 查询UGC地图局外翻译文本应答
message GetUgcOutGameTranslateDataRsp {
  optional int32 Res = 1;					                  // 处理结果
  optional int64 UgcID = 2;					                  // 作品ID
  optional int32 LangID = 3;				                  // 语言ID
  repeated IdipUgcTranslateData Data  = 4;	                  // 翻译数据
}

// 查询UGC地图局内翻译文本请求
message GetUgcInGameTranslateDataReq {
  option (handler) = IH_TranslateSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional int64 UgcID = 1;					                  // 作品ID
  optional int32 LangID = 2;				                  // 语言ID
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 查询UGC地图局内翻译文本应答
message GetUgcInGameTranslateDataRsp {
  optional int32 Res = 1;					                  // 处理结果
  optional int64 UgcID = 2;					                  // 作品ID
  optional int32 LangID = 3;				                  // 语言ID
  repeated IdipUgcTranslateData TextData = 4;	              // 翻译数据
}

// 查询UGC局内原文信息请求
message QueryUgcInGameOriginalTextReq {
  option (handler) = IH_TranslateSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional int64 UgcID = 1;					                  // 作品ID
  optional uint32 Partition = 2;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 查询UGC局内原文信息应答
message QueryUgcInGameOriginalTextRsp {
  optional int32 Res = 1;						              // 处理结果
  optional int64 UgcID = 2;						              // 作品ID
  repeated IdipUgcOriginalTextData TextList  = 3;	          // 原文数据
}

// 查询UGC局外原文信息请求
message QueryUgcOutGameOriginalTextReq {
  option (handler) = IH_TranslateSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional int64 UgcID = 1;					                  // 作品ID
  optional uint32 Partition = 2;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 查询UGC局外原文信息应答
message QueryUgcOutGameOriginalTextRsp {
  optional int32 Res = 1;						              // 处理结果
  optional int64 UgcID = 2;						              // 作品ID
  repeated IdipUgcOriginalTextData TextList  = 3;	          // 原文数据
}

// 修改UGC地图局外翻译文本请求
message ModifUgcOutGameTranslateDataReq {
  option (handler) = IH_TranslateSvr;
  option (hacked) = true;
  optional int64 UgcID = 1;					                  // 作品ID
  optional int32 LangID = 2;				                  // 语言ID
  repeated IdipUgcTranslateData Data = 3;	                  // 翻译数据
  optional uint32 Partition = 4;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 修改UGC地图局外翻译文本应答
message ModifUgcOutGameTranslateDataRsp {
  optional int32 Res = 1;					                  // 处理结果
}

// 修改UGC地图局内翻译文本请求
message ModifUgcInGameTranslateDataReq {
  option (handler) = IH_TranslateSvr;
  option (hacked) = true;
  optional int64 UgcID = 1;					                  // 作品ID
  optional int32 LangID = 2;				                  // 语言ID
  repeated IdipUgcTranslateData Data = 4;	                  // 翻译数据
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 修改UGC地图局内翻译文本应答
message ModifUgcInGameTranslateDataRsp {
  optional int32 Res = 1;					                  // 处理结果
}

message SetPlayerDsRecordInfoReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional int32 RecordCount = 5;                             // 记录局数
  optional uint32 Partition = 6;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message SetPlayerDsRecordInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 封禁设备接口请求
message BanDeviceIdReq {
  option (handler) = IH_IdipSvr;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string DeviceId = 3;                               // 设备id
  optional uint64 Duration = 4;                               // 禁止时长（秒）
  optional string Reason = 5[(urlcode) = true];               // 禁止原因
  optional uint32 Source = 6;                                 // 渠道号，由前端生成，不需要填写
  optional string Serial = 7;                                 // 流水号，由前端生成，不需要填写
  optional uint32 Partition = 8;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 封禁设备接口应答
message BanDeviceIdRsp {
  optional int32 Result = 1;                                  // 结果：成功(0)，玩家不存在(1)，失败(其他)
  optional string RetMsg = 2;                                 // 返回消息
}

// 解除封禁设备接口请求
message UnbanDeviceIdReq {
  option (handler) = IH_IdipSvr;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string DeviceId = 3;                               // 设备id
  optional uint32 Source = 4;                                 // 渠道号，由前端生成，不需要填写
  optional string Serial = 5;                                 // 流水号，由前端生成，不需要填写
  optional uint32 Partition = 6;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 解除封禁设备接口应答
message UnbanDeviceIdRsp {
  optional int32 Result = 1;                                  // 结果：成功(0)，玩家不存在(1)，失败(其他)
  optional string RetMsg = 2;                                 // 返回消息
}

// 禁止玩法接口请求
message BanGameModeReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint32 Type = 5;                                   // 类型
  optional uint64 Duration = 6;                               // 禁止时长（秒）
  optional string Reason = 7[(urlcode) = true];               // 禁止原因
  optional uint32 Source = 8;                                 // 渠道号，由前端生成，不需要填写
  optional string Serial = 9;                                 // 流水号，由前端生成，不需要填写
  optional uint32 Partition = 10;                             // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 BanAll = 11;                                 // 如果为1封禁玩家所有玩法，否则根据Type封禁指定玩法分组（注意，全部封禁信息和分组封禁信息独立互不影响）
}

// 禁止玩法接口应答
message BanGameModeRsp {
  optional int32 Result = 1;                                  // 结果：成功(0)，玩家不存在(1)，失败(其他)
  optional string RetMsg = 2;                                 // 返回消息
}

// 解除禁止玩法接口请求
message UnbanGameModeReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint32 Type = 5;                                   // 类型
  optional uint32 Source = 6;                                 // 渠道号，由前端生成，不需要填写
  optional string Serial = 7;                                 // 流水号，由前端生成，不需要填写
  optional uint32 Partition = 8;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 UnbanAll = 9;                                // 如果为1解除所有玩法封禁，否则根据Type解封指定玩法分组（注意，全部封禁信息和分组封禁信息独立互不影响）
}

// 解除禁止玩法接口应答
message UnbanGameModeRsp {
  optional int32 Result = 1;                                  // 结果：成功(0)，玩家不存在(1)，失败(其他)
  optional string RetMsg = 2;                                 // 返回消息
}

// idip玩家统计信息类型
enum IdipPlayerStatInfoType {
  EN_PLAYER_STAT_INFO_TYPE_SINGLE_RECHARGE = 1;               // 统计类型-单次充值
  EN_PLAYER_STAT_INFO_TYPE_TOTAL_RECHARGE = 2;                // 统计类型-累计充值
  EN_PLAYER_STAT_INFO_TYPE_TOTAL_LOGIN_DAYS = 3;              // 统计类型-累计登录天数
  EN_PLAYER_STAT_INFI_TYPE_DAILY_GAME_TIMES = 4;              // 统计类型-每日参加游戏次数
}

// 查询玩家统计信息请求(走datamore接口查询)
message QueryNewPlayerStatInfoReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional int32 Type = 5;                                    // 查询数据类型, 见枚举IdipPlayerStatInfoType所示
  optional uint64 BeginTime = 6;                              // 开始时间, 单位是s, 仅在查询累计登录天数/每次参加游戏次数时生效
  optional uint64 EndTime = 7;                                // 结束时间, 单位是s, 仅在查询累计登录天数/每次参加游戏次数时生效
  optional uint32 Partition = 8;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 充值信息
message sRechargeInfo {
  optional uint32 RechargeAmount = 1;                         // 充值金额
  optional uint64 RechargeTime = 2;                           // 充值时间
}

// 查询玩家统计信息响应(走datamore接口查询)
message QueryNewPlayerStatInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  repeated sRechargeInfo RechargeInfos = 3;                   // 充值信息, 字段废弃
  optional uint32 SingleRechargeAmount = 4;                   // 单次充值金额
  optional uint32 TotalRechargeAmount = 5;                    // 累计充值金额
  optional uint32 TotalLoginDays = 6;                         // 累计登录天数
  optional uint32 DailyGameTimes = 7;                         // 每日参数游戏次数
}

// idip玩家基础信息查询类型
enum IdipPlayerBasicInfoQueryType {
  EN_PLAYER_INFO_QUERY_TYPE_SHORT_UID = 1;                    // 查询类型-短号
  EN_PLAYER_INFO_QUERY_TYPE_NICK_NAME = 2;                    // 查询类型-昵称
  EN_PLAYER_INFO_QUERY_TYPE_UID = 3;                          // 查询类型-uid
}

// 查询玩家基础信息请求
message QueryPlayerBasicInfoReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional int32 Type = 3;                                    // 查询数据类型, 见枚举IdipPlayerBasicInfoQueryType所示
  optional string OriginalData = 4 [(urlcode) = true];        // 查询源数据
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 查询玩家基础信息响应
message QueryPlayerBasicInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional string OpenId = 3;                                 // 用户openid
  optional uint32 PlatId = 4;                                 // 用户平台id
  optional int64 Uid = 5;                                     // 用户uid
  optional string NickName = 6;                               // 用户昵称
}

// 封禁玩家自定义标签接口
// 需要往gamesvr丢 ugc是大区的。。
message AqDoDelYuanJianBiaoQianReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 MapId = 4;                                   // 地图id
  optional uint32 Action = 5;                                 // 操作类型
  optional string Param = 6;                                  // 参数
  optional uint32 Source = 7;                                 // 渠道号，由前端生成，不需要填写
  optional string Serial = 8;                                 // 流水号，由前端生成，不需要填写
  optional int64 Uid = 9 [(field_meta_uuid) = true];          // Uid
  optional uint32 Partition = 10;                             // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 玩家安全处罚响应
message AqDoDelYuanJianBiaoQianRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 造梦星探查询地图信息和创作者信息
message CreatorTeamActivityReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = false;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // 用户OpenID
  optional uint32 PageNo = 4;                                 // 请求分页
  optional uint32 PageCount = 5;                              // 每页请求数量
  optional uint32 Partition = 6;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 Uid = 7;                                     // uid
}

message CreatorTeamActivityRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 RetCode = 3;                                 // 错误码
  message Map {
    optional int64 MapId = 1;                                 // 地图ID
    optional string MapName = 2 [(urlcode) = true];           // 地图名称
    optional string MapCover = 3 [(urlcode) = true];          // 地图封面
    optional string OpenId = 4;                               // 地图作者gopenid
    optional uint32 PlatId = 5;                               // 地图作者平台
    optional uint64 MapLevel = 6;                             // 地图造梦值
    optional uint64 PlayNum = 7;                              // 地图游玩次数
    optional uint64 PlayUserNum = 8;                          // 地图游玩人次
    optional int32 IsCoCreate = 9;                            // 是否共创地图
    optional string RuleTagList = 10 [(urlcode) = true];      // 规则标签
    optional string PlayTagList = 11 [(urlcode) = true];      // 玩法标签
  }
  message TeamMember {
    optional string OpenId = 1 [(urlcode) = true];            // gopenid
    optional uint32 PlatId = 2;                               // 0 IOS 1 安卓
    optional int32 IsLeader = 3;                              // 是否队长
    optional uint64 PlayNum = 4;                              // 所有地图的游玩次数
    optional uint64 PlayUserNum = 5;                          // 所有地图的游玩人数
    optional string Nick = 6 [(urlcode) = true];              // 昵称
    optional string HeadUrl = 7 [(urlcode) = true];           // 头像

  }
  optional string TeamId = 4 [(urlcode) = true];
  repeated TeamMember MemberList = 5;                         // 小队成员列表
  repeated Map MapList = 6;                                   // 地图列表
  optional uint64 TeamPlayNum = 7;                            // 小队总游玩次数
  optional uint64 TeamPlayUserNum = 8;                        // 小队总游玩人次
  optional uint32 MapTotalCount = 9;                          // 地图总数
  optional uint32 TotalPageNo = 10;                           // 地图总页数
  optional string Version = 11;                               // 地图数据版本号
}

// 造梦星探查询小队排行榜信息
message CreatorTeamActivityRankReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = false;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // 玩家gopenid
  optional uint32 ListCount = 4;                              // 返回排行榜列表个数
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 Uid = 6;                                     // uid
}

message CreatorTeamActivityRankRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 RetCode = 3;                                 // 错误码
  message TeamRankInfo {
    optional uint32 Rank = 1;                                 // 排名，从1开始
    optional string TeamId = 2 [(urlcode) = true];            // 队伍ID
    optional string Nick = 3 [(urlcode) = true];              // 队长昵称
    optional string HeadUrl = 4 [(urlcode) = true];           // 队长头像
    optional uint64 PlayUserNum = 5;                          // 所有地图的游玩人数
  }
  repeated TeamRankInfo TeamRankInfoList = 4;                 // 排行榜小队列表，个数由 list_count 指定
  repeated TeamRankInfo SelfTeamRankInfo = 5;                 // 自己所在小队的信息, 可能没有
}

// 标记黑产玩家
message SetBlackIndustryUserReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = false;
  optional uint32 PlatId = 1;                                 // 平台（0：ios/1：安卓）
  optional uint32 AreaId = 2;                                 // idip area
  optional uint32 LoginType = 3;                              // 微信（1）手Q（2）
  optional uint32 Partition = 4;                              // 小区ID
  optional string OpenId = 5;
  optional uint64 Uid = 6;
  optional uint32 Flag = 7;                                   // 黑产标签类型，0=正常，1=黑产
  optional uint32 Duration = 8;                               // 黑产标签时长（秒）
}

message SetBlackIndustryUserRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 RetCode = 3;                                 // 错误码
}

message DoDeleteUgcMapTopicReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // idip area
  optional uint32 PlatId = 2;                                 // 平台（0：ios/1：安卓）
  optional int64 MapId = 3[(field_meta_uuid) = true];         // 地图id
  optional string TopicTxt = 4;                               // 话题文本
  optional uint32 Source = 5;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                                 // 流水号
  optional uint32 Partition = 7;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DoDeleteUgcMapTopicRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  optional int64 MapId = 3;                                   // 地图id
  optional string TopicTxt = 4;                               // 话题文本
}

// 发送个人邮件请求(附件支持有效期设置)
message SendPersonalMailWithAttachExpireReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  option (noCheckRoleTransferring) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 4;                                 // OpenId
  optional string MailTitle = 5 [(urlcode) = true];           // 邮件标题
  optional string MailContent = 6 [(urlcode) = true];         // 邮件内容
  optional uint32 MailValidTime = 7;                          // 邮件有效期
  repeated sAttachInfo AttachList = 8;                        // 附件
  optional uint32 Source = 9;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                                // 流水号
  optional int64 Uid = 11 [(field_meta_uuid) = true];         // Uid
  optional int64 MailId = 12;                                 // 邮件id, 用于idipsvr往gamesvr通知使用
  optional string Sender = 13;                                // 发件人
  optional string Url = 14 [(urlcode) = true];                // 跳转链接
  optional uint32 OverseaCfgId = 15;                          // 0-正常邮件 ！0-国际化邮件多语言配置id
  optional string AmsSerial = 16;                             // ams平台发货流水号
  optional uint32 Partition = 17;                             // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 MailType = 18;                              // 邮件类型, 取值详情参考枚举MailType, 选填参数, 不设置该参数默认是系统邮件类型
  optional uint32 MailExtraType = 19;                         // 邮件附加类型, 取值详情参考枚举MailExtraType, 选填参数, 暂不使用
  optional string CopiableContent = 20;                       // 邮件内可复制内容
  optional int32 IsStarred = 21;                              // 是否星标邮件
  repeated sMailBuyItem MailBuyItemList = 22;                 // 购买商品
  optional string MailContentImg = 23;                        // 邮件内容图片
  optional string LowestVersion = 25;                         // 最低版本号(包含)
  optional string HighestVersion = 26;                        // 最高版本号(包含)
  optional int32 MailAttachmentType = 27;                     // 邮件附件类型, 参见 MailAttachmentType
  optional string MailAttachmentTypeContent = 28;             // 邮件附件类型内容, 根据 MailAttachmentType 自己去解析. (mailAttachmentType==1:多个ugcId用分号分隔)
}

// 发送个人邮件响应(附件支持有效期设置)
message SendPersonalMailWithAttachExpireRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional uint32 MailUniqueId = 3;                           // 邮件id
}

// 发送个人邮件请求(附件支持有效期设置, 区别于 SendPersonalMailWithAttachExpireReq, 本接口只供特定端外平台使用)   // 发送邮件
message SendPersonalMailWithAmsAttachExpireReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  option (noCheckRoleTransferring) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 4;                                 // OpenId
  optional string MailTitle = 5 [(urlcode) = true];           // 邮件标题
  optional string MailContent = 6 [(urlcode) = true];         // 邮件内容
  optional uint32 MailValidTime = 7;                          // 邮件有效期
  repeated sMailAttachInfo MailAttachList = 8;                // 附件
  optional uint32 Source = 9;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                                // 流水号
  optional int64 Uid = 11 [(field_meta_uuid) = true];         // Uid
  optional int64 MailId = 12;                                 // 邮件id, 用于idipsvr往gamesvr通知使用
  optional string Sender = 13;                                // 发件人
  optional string Url = 14 [(urlcode) = true];                // 跳转链接
  optional uint32 OverseaCfgId = 15;                          // 0-正常邮件 ！0-国际化邮件多语言配置id
  optional string AmsSerial = 16;                             // ams平台发货流水号
  optional uint32 Partition = 17;                             // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 MailType = 18;                              // 邮件类型, 取值详情参考枚举MailType, 选填参数, 不设置该参数默认是系统邮件类型
  optional uint32 MailExtraType = 19;                         // 邮件附加类型, 取值详情参考枚举MailExtraType, 选填参数, 暂不使用
  optional string CopiableContent = 20;                       // 邮件内可复制内容
  optional int32 IsStarred = 21;                              // 是否星标邮件
  repeated sMailBuyItem MailBuyItemList = 22;                 // 购买商品
  optional string MailContentImg = 23;                        // 邮件内容图片
  optional string LowestVersion = 25;                         // 最低版本号(包含)
  optional string HighestVersion = 26;                        // 最高版本号(包含)
  optional int32 MailAttachmentType = 27;                     // 邮件附件类型, 参见 MailAttachmentType
  optional string MailAttachmentTypeContent = 28;             // 邮件附件类型内容, 根据 MailAttachmentType 自己去解析. (mailAttachmentType==1:多个ugcId用分号分隔)
}

// 发送个人邮件响应(附件支持有效期设置)
message SendPersonalMailWithAmsAttachExpireRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional uint32 MailUniqueId = 3;                           // 邮件id
}

// 查询家园方案列表请求
message AqQueryXiaoWoGetLayoutListReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4[(field_meta_uuid) = true];           // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message sLayoutList {
  optional int64 LayoutId = 1;                                // 方案id
  optional string LayoutName = 2;                             // 方案名
}

// 查询家园方案列表应答
message AqQueryXiaoWoGetLayoutListRsp {
  optional int32 Result = 1;                                  // 结果：成功(0)，玩家不存在(1)，失败(其他)
  optional string RetMsg = 2;                                 // 返回消息
  repeated sLayoutList LayoutList = 3;	                      // 方案列表
}

// 删除家园方案请求
message AqDoXiaoWoDelLayoutReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4[(field_meta_uuid) = true];           // Uid
  optional int64 LayoutId = 5;                                // 方案ID
  optional uint32 Source = 6;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                                 // 流水号
  optional uint32 Partition = 8;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 删除家园方案应答
message AqDoXiaoWoDelLayoutRsp {
  optional int32 Result = 1;                                  // 结果：成功(0)，玩家不存在(1)，失败(其他)
  optional string RetMsg = 2;                                 // 返回消息
}

message GetResTableDataReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional string TableName = 1;
  optional string Fields = 2;
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message GetResTableDataRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional string Data = 3;
}

// 附件参数信息
message ExtParamInfo {
  optional string Key = 1;                                    // 参数key
  optional string Value = 2;                                  // 参数value
}

// 基于datamore通用查询用户信息请求
message QueryCommonPlayerDataByDatamoreReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string DmId = 6;                                   // datamore接口传参所需
  optional string RuleId = 7;                                 // datamore接口传参所需
  optional string RuleSecret = 8;                             // datamore接口传参所需
  optional string DmSourceId = 9;                             // datamore接口传参所需
  optional string BeginTime = 10;                             // 开始时间, 当需要基于时间段查询数据时设置, 参数格式形如20231226
  optional string EndTime = 11;                               // 结束时间, 当需要基于时间段查询数据时设置, 参数格式形如20231226
  optional uint32 SeasonId = 12;                              // 赛季id, 当需要基于赛季查询数据时设置
  repeated ExtParamInfo ExtParam = 13;                        // 附件参数列表, 用于以上参数无法满足需求时, 以key=value的形式传递附加参数
}

// 基于datamore通用查询用户信息响应
message QueryCommonPlayerDataByDatamoreRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional uint32 Data = 3;                                   // 查询数据返回
}

message ConfirmRecruiteOrderReq {//废弃
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (deprecated) = true;
  optional uint32 AreaId = 1;                                 // 招募用户大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 招募用户平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // 招募用户OpenId
  optional int64 Uid = 4;                                     // 招募用户uid
  optional string CallOpenid = 5;                             // 号召用户OpenId
  optional uint32 CallPlatId = 6;                             // 号召用户平台
  optional int64 CallUid = 7;                                 // 号召用户uid
  optional int32 ActivityId = 8;                              // 活动ID
  optional uint32 Partition = 9;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string ExtraData = 10[(urlcode) = true];           // 扩展信息，JSON格式，优先读JSON 参数
  optional uint32 Source = 11;                                // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 12;                                // 流水号
}

message ConfirmRecruiteOrderRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 RetCode = 3;                                 // 业务处理返回的NKErrorCode
}

message MatchIsolateReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4[(field_meta_uuid) = true];           // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 Time = 6;                                   //（秒）
  optional uint32 Ailevel = 7;                                //（ai局的ai难度等级）
  optional string Reason = 8;                                 //（提示语string）
  optional uint32 Type = 9;                                   //（1为黑作弊池，2为手游助手模拟器池，3为第三方模拟器池）
}

message MatchIsolateRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 RetCode = 3;                                 // 业务处理返回的NKErrorCode
}

// 新增一轮温暖局请求
message AqDoPlayerAddWarmRoundReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 MatchTypeId = 6;                             // 玩法类型
  optional uint32 Source = 7;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;                                 // 流水号
}

// 新增一轮温暖局响应
message AqDoPlayerAddWarmRoundRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 发送滚动公告信息请求
message SendRollingNoticeInfoReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 公告生效平台id, 0: IOS, 1: 安卓, 2: PC, 3: MAC, 4: ALL
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string Content = 4 [(urlcode) = true];             // 公告内容
  optional uint64 BeginTime = 5;                              // 公告开始时间
  optional uint64 EndTime = 6;                                // 公告结束时间
  optional uint32 TriggerTotalTimes = 7;                      // 触发总次数
  optional uint32 TemplateId = 8;                             // 公告模板id
  optional uint32 RepeatCnt = 9;                              // 公告轮播次数, 选填参数, 如果不设置以公告模板里的轮播次数为准
  optional uint32 Source = 10;                                // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 11;                                // 流水号
}

// 发送滚动公告信息响应
message SendRollingNoticeInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional uint32 NoticeId = 3;                               // 公告id
}

// 查询滚动公告信息列表请求
message QueryRollingNoticeInfoListReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 PageNo = 4;                                 // 页码
}

// 查询滚动公告信息列表响应
message QueryRollingNoticeInfoListRsp {
  // 滚动公告信息
  message RollingNoticeInfo {
    optional string Content = 1;                              // 公告内容
    optional uint64 BeginTime = 2;                            // 公告开始时间
    optional uint64 EndTime = 3;                              // 公告结束时间
    optional uint32 TriggerTotalTimes = 4;                    // 触发总次数
    optional uint32 TemplateId = 5;                           // 公告模板id
    optional uint32 RepeatCnt = 6;                            // 公告轮播次数, 选填参数, 如果不设置以公告模板里的轮播次数为准
    optional uint32 NoticeId = 7;                             // 公告id
    optional uint32 PlatId = 8;                               // 公告生效平台id, 0: IOS, 1: 安卓, 2: PC, 3: MAC, 4: ALL
  }

  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional uint32 TotalPageNo = 3;                            // 总页码
  optional uint32 TotalCount = 4;                             // 总数量
  repeated RollingNoticeInfo NoticeList = 5;                  // 滚动公告列表
}

// 下线滚动公告信息请求
message OfflineRollingNoticeInfoReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 NoticeId = 4;                               // 公告id
  optional uint32 Source = 5;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                                 // 流水号
}

// 下线滚动公告信息响应
message OfflineRollingNoticeInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 修改玩家段位分请求
message DoModifyOmdrankReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 RankId = 6;                                  // 排行榜id
  optional int32 Score = 7;                                   // 积分
}

// 修改玩家段位分响应
message DoModifyOmdrankRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 修改玩家段位分请求
message DoModifyPlayerRankScoreReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 Score = 6;                                   // 设置后的段位分
  optional int32 QualifyType = 7;                             // 段位类型
}

// 修改玩家段位分响应
message DoModifyPlayerRankScoreRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 BeforeScore = 3;                             // 修改前分数
  optional int32 AfterScore = 4;                              // 修改后分数
}

// 修改玩家某赛季某玩法历史最高段位信息请求
message DoModifyPlayerQualifyMaxDegreeReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 SeasonId = 6;                                // 赛季id
  optional int32 QualifyType = 7;                             // 排位玩法类型
  optional int32 MaxDedegreeType = 8;                         // 历史最高大段位
  optional int32 MaxDegreeID = 9;                             // 历史最高小段位
}

// 修改玩家某赛季某玩法历史最高段位信息响应
message DoModifyPlayerQualifyMaxDegreeRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// idip玩家排位玩法指定赛季数据修改类型
enum IdipQualifySeasonInfoModifyType {
  EN_IQSIMT_NONE = 0;       // 无效
  EN_IQSIMT_CUR_SCORE = 1;  // 当前积分
  EN_IQSIMT_MAX_SCORE = 2;  // 最高积分
  EN_IQSIMT_CUR_WIN = 3;    // 当前连胜数
  EN_IQSIMT_MAX_WIN = 4;    // 最高连胜数
}

// 修改玩家排位玩法指定赛季数据请求
message DoModifyQualifySeasonInfoReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 SeasonId = 6;                                // (排位配置里的)赛季id
  optional int32 QualifyType = 7;                             // (排位配置里的)排位类型
  optional int32 ModType = 8;   // 修改类型(enum IdipQualifySeasonInfoModifyType: 1-当前积分;2-最高积分;3-当前连胜数;4-最高连胜数)
  optional int32 Value = 9;     // 目标值
}

// 修改玩家排位玩法指定赛季数据响应
message DoModifyQualifySeasonInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 BeforeValue = 3;                             // 修改前数值
  optional int32 AfterValue = 4;                              // 修改后数值
}

// 修改玩家关卡分数或时长请求
message DoModifyPlayerLevelScoreReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 LevelId = 6;                                 // 关卡ID
  optional int32 Score = 7;                                   // 设置后的段位分
  optional int32 PlayMode = 8;                                // 单人双人四人
}

// 修改玩家关卡分数或时长响应
message DoModifyPlayerLevelScoreRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 BeforeScore = 3;                             // 修改前分数（或时长，毫秒）
  optional int32 AfterScore = 4;                              // 修改后分数（或时长，毫秒）
}

// 查询排行榜信息请求
message QueryRankInfoReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 RankId = 4;                                  // 榜单ID (enum RankIdDef)
  optional int32 RankType = 5;                                // 榜单类型（1: 好友; 2: 全服; 3: 地区）
  optional int32 RankSubType = 6;                             // 对于地区榜单：地区等级（1：区县，2：城市，3：省份）
  optional int32 RankSubId = 7;                               // 对于地区榜单：地区ID
  optional int32 IndexStart = 8;                              // 起始编号
  optional int32 Count = 9;                                   // 查询数量
  optional int32 ResRankingConfId = 10;                       // 当RankId为ResRankingConfKey时 设置为配表中的key
  optional int32 PlayMode = 11;                               // 单人双人四人
}

message sRankInfo {
  optional int32 Rank = 1;                                    // 排名
  optional int32 AreaId = 2;
  optional int32 PlatId = 3;
  optional string OpenId = 4;
  optional string RoleName = 5;                               // 角色昵称
  optional int64 UpdateTime = 6;                              // 上榜时间（毫秒）
  optional int32 Score = 7;                                   // 分数
  optional int32 PassTime = 8;                                // 通关时间（毫秒）
  optional int64 Uid = 9;                                     // UID
}

// 查询排行榜信息响应
message QueryRankInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  repeated sRankInfo RankInfoList = 3;                        // 修改前分数（或时长，毫秒）
}

// 修改UGC地图榜单分数（AQ）请求
message AqDoModifyUgcRankScoreReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];         // Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint64 RankId = 6;   // UGC地图榜单id
  optional int64 Score = 7;     // 要设置的排行榜分数/时长
}

// 修改UGC地图榜单分数（AQ）应答
message AqDoModifyUgcRankScoreRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int64 BeforeScore = 3;  // 设置前的排行榜分数/时长
  optional int64 AfterScore = 4;   // 设置后的排行榜分数/时长
}

// 重置赠礼卡祝福语接口（AQ）请求
message AqDoMallGiftCardResetWordsReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];         // 赠送方Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 GiftId = 6;      // 赠礼卡id
  optional int64 ReceiverUid = 7; // 赠礼卡接收方玩家uid
}

// 重置赠礼卡祝福语接口（AQ）应答
message AqDoMallGiftCardResetWordsRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 下架玩家相册图片
message AqDoBanPlayerAlbumPicReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string PicKey = 6;                                 // 相册图片key
  optional string PicUrl = 7[(urlcode) = true];               // 相册图片url(utf8+url编码)
  optional int32 OpType = 8;                                  // 操作类型(0下架；1恢复上架)
}

message AqDoBanPlayerAlbumPicRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 重置生日贺卡祝福语接口请求 (Anquan-安全)
message AqDoBirthdayCardResetWordsReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区(2-手Q)
  optional uint32 PlatId = 2;                                 // 平台(1-安卓, 0-ios)
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // 赠送方Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string BirthdayCardId = 6;                         // 生日贺卡id
  optional int64 ReceiverUid = 7;                             // 生日贺卡接收方玩家uid
}

// 重置生日贺卡祝福语接口应答
message AqDoBirthdayCardResetWordsRsp {
  optional int32 Result = 1;                                  // 结果(0-成功, 1-无角色, 其他-失败)
  optional string RetMsg = 2;                                 // 返回消息
}

// 修改社团名
message ModifyClubNameReq {
  option (handler) = IH_ClubSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional int64 ClubId = 3[(field_hash_key) = true];
  optional string ModifyContent = 4[(urlcode) = true];		  // 修改后的内容
}

message ModifyClubNameRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 修改社团宣言
message ModifyClubBriefReq {
  option (handler) = IH_ClubSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional int64 ClubId = 3[(field_hash_key) = true];
  optional string ModifyContent = 4[(urlcode) = true];		  // 修改后的内容
}

message ModifyClubBriefRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 删除活动地图许愿请求
message DoDeleteActivityMapWishReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string ActivityId = 4;                             // 活动id, 一个活动只会有一个许愿
  optional string OpenId = 5;                                 // OpenId
  optional int64 Uid = 6 [(field_meta_uuid) = true];          // Uid
  optional uint32 Source = 7;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;                                 // 流水号
}

// 删除活动地图许愿响应
message DoDeleteActivityMapWishRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 需要往gamesvr丢 ugc是大区的。。
message DoUgcMapHandlerReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 MapId = 4;                                   // 地图id
  optional UgcMapActionType Type = 5;                               // 操作类型
  optional string Param = 6;                                  // 参数
  optional int64 Uid = 7 [(field_meta_uuid) = true];          // Uid
  optional uint32 Partition = 8;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DoUgcMapHandlerRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message IdipClubBasicInfo {
  optional int32 Exist = 1;            // 社团是否存在
  optional int64 ClubId = 2;          // 社团ID
  optional string Name = 3;           // 社团名称
  optional int32 Avatar = 4;          // 社团头像
  optional int32 Heat = 5;            // 社团热度
  optional int32 MemberCount = 6;     // 成员数量
  optional int32 BoyCount = 7;        // 男生数量
  optional int32 GirlCount = 8;       // 女生数量
  optional string Brief = 9;          // 社团宣言
  repeated int32 Labels = 10;         // 社团标签
  optional int64 CreateTime = 11;     // 创建时间
  optional int32 DirectJoin = 12;      // 加入是否无需审批
  optional int32 DenyJoin = 13;        // 是否拒绝加入
}

// 增值平台：获取社团信息
message SpringH5GetClubInfoReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // 用户OpenID
  optional int64 Uid = 4;                                     // 用户UID
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  repeated int64 ClubIds = 6;                                 // 要查询的ClubId
}

message SpringH5GetClubInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  repeated IdipClubBasicInfo Clubs = 3;                       // 请求的社团信息返回
}

// 增值平台：获取我的社团信息
message SpringH5GetMyClubInfoReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // 用户OpenID
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // 用户UID
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message SpringH5GetMyClubInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  repeated IdipClubBasicInfo Clubs = 3;                       // 请求的社团信息返回
}

message SpringH5BatchRandomClubInfoReq {
  option (handler) = IH_ClubSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // 用户OpenID
  optional int64 Uid = 4 [(field_hash_key) = true];                                     // 用户UID
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 Count = 6;                                   // 获取数量
}

message SpringH5BatchRandomClubInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  repeated IdipClubBasicInfo Clubs = 3;                       // 返回随机一批社团
}


// 获取福星手账簿信息
message LuckyStarGetHandbookInfoReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional int64 Uid = 1 [(field_meta_uuid) = true];    // 玩家id
  optional uint32 ActivityId = 2;                       // 活动id
  optional uint32 Partition = 3;                        // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 PlatId = 4;                           // 平台（1-安卓，0-ios）
  optional string OpenId = 5;                           // 用户OpenID
}

message LuckyStarGetHandbookInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  repeated sHandbookInfo HandbookList = 3;                    // 手账簿信息
  repeated sLuckyStarInfo StarInfoList = 4;                   // 福星卡信息
}

// 查询赠送信息
message LuckyStarGetGiveInfoReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional int64 Uid = 1 [(field_meta_uuid) = true];    // 玩家id
  optional uint32 ActivityId = 2;                       // 活动id
  optional string GiveId = 3;                           // 赠送id
  optional uint32 Partition = 4;                        // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 PlatId = 5;                           // 平台（1-安卓，0-ios）
  optional string OpenId = 6;                           // 用户OpenID
}

message LuckyStarGetGiveInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional uint32 StarId = 3;                                 // 福星id
  optional string GiverName = 4;                              // 赠送人角色名
}

// 领取赠送的福星卡
message LuckyStarReceiveGiveStarReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional int64 Uid = 1 [(field_meta_uuid) = true];    // 玩家id
  optional uint32 ActivityId = 2;                       // 活动id
  optional string GiveId = 3;                           // 赠送id
  optional uint32 Partition = 4;                        // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 PlatId = 5;                           // 平台（1-安卓，0-ios）
  optional string OpenId = 6;                           // 用户OpenID
}

message LuckyStarReceiveGiveStarRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  repeated sLuckyStarInfo StarInfoList = 3;                   // 福星卡信息
}

// 清理玩家地区排行榜数据请求
message DoCleanUserRegionRankReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
}

// 清理玩家地区排行榜数据响应
message DoCleanUserRegionRankRsp {

}
message AqDoXiaoWoWelcomeClearReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                     // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoWelcomeClearRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoXiaoWoWelcomeBanReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                     // Uid
  optional uint64 Time = 5;                                   // 禁止的时间(秒)
  optional uint32 Partition = 6;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoWelcomeBanRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoXiaoWoBulletinDeleteReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                     // Uid
  optional uint64 BulletinId = 5;                              // 留言id
  optional uint64 BulletinReplyId = 6;                         // 回复id
  optional uint32 Partition = 7;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoBulletinDeleteRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoXiaoWoBulletinBanReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint64 Time = 5;                                   // 禁止的时间(秒)
  optional uint32 Partition = 6;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoBulletinBanRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoXiaoWoWelcomeSetFreeReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                     // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoWelcomeSetFreeRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoXiaoWoBulletinSendFreeReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoXiaoWoBulletinSendFreeRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 端外消费通知请求
message OffEndConsumeNotifyReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string OrderId = 6;                                // 订单id
  optional string GoodsId = 7;                                // h5侧直购id
  optional uint32 BuyNum = 8;                                 // 购买数量
  optional uint32 Source = 9;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                                // 流水号
}

// 端外消费通知响应
message OffEndConsumeNotifyRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 端外充值星钻通知请求
message DoOffEndRechargeDiamondNotifyReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string OrderId = 6;                                // 订单id
  optional string GoodsId = 7;                                // h5侧直购id
  optional uint32 BuyNum = 8;                                 // 购买数量
  optional uint32 Source = 9;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                                // 流水号
}

// 端外消费通知响应
message DoOffEndRechargeDiamondNotifyRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 查询家园基本信息请求
message QueryXiaoWoGetBasicinfoReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5 [(field_hash_key) = true];                                   // Uid
}

// 查询家园基本信息应答
message QueryXiaoWoGetBasicinfoRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  optional int64 XiaoWoID = 3;                                // 小窝ID
  optional string Name = 4;                                   // 名称
  optional int64 CreateTime = 5;                              // 创建时间
  optional int64 SaveTime = 6;                                // 更新时间
  optional string Version = 7;                                // 版本
  optional int32 Level = 8;                                   // 等级
  optional int32 Beauty = 9;                                  // 美观度
  optional int64 LikeCount = 10;                              // 点赞数
  optional int64 StarCount = 11;                              // 收藏数
  optional int32 CurState = 12;                               // 状态（0：正常、1:屏蔽、 2下架）
  optional string StateReason = 13;                           // 状态原因
}

// 修改合集名
message AqDoMapNameReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // idip area
  optional uint32 PlatId = 2;                                 // 平台（0：ios/1：安卓）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // uid
  optional string MapCollectionId = 5;                        // 合集Id
  optional string ModifyContent = 6;                          // 修改后内容
  optional uint32 Source = 7;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;                                 // 流水号
}

message AqDoMapNameRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
}

// 修改合集简介
message AqDoMapCollectionBlurbReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // idip area
  optional uint32 PlatId = 2;                                 // 平台（0：ios/1：安卓）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // uid
  optional string MapCollectionId = 5;                        // 合集Id
  optional string ModifyContent = 6;                          // 修改后内容
  optional uint32 Source = 7;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;                                 // 流水号
}

message AqDoMapCollectionBlurbRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
}

// 发送道具直接进背包请求
message SendItemDataIntoBagReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  repeated sAttachInfo SAttachList = 6;                        // 附件
  optional uint32 Source = 7;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;                                 // 流水号
  optional uint32 WithNtf = 9;                                // 0 不提示 1提示
}

// 发送道具直接进背包响应
message SendItemDataIntoBagRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
}

// 查询好友功夫熊猫助力信息请求
message GetKungFuPandaHelpRacingInfoReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 FArea = 1;                                  // 好友用户大区（2-手Q）
  optional uint32 FPlatId = 2;                                // 好友用户平台（1-安卓，0-ios）
  optional string FOpenId = 3;                                // 好友OpenId
  optional int64 FUid = 4;                                    // 好友用户uid
  optional uint32 Area = 5;                                   // 用户大区（2-手Q）
  optional uint32 PlatId = 6;                                 // 用户平台（1-安卓，0-ios）
  optional string OpenId = 7;                                 // 用户OpenId
  optional int64 Uid = 8 [(field_meta_uuid) = true];          // 用户uid
  optional int32 IActivityId = 9;                             // 活动id
  optional uint32 Partition = 11;                             // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 查询好友功夫熊猫助力信息响应
message GetKungFuPandaHelpRacingInfoRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 MaxHelp = 3;                                 // 助力最大次数
  optional int32 CurrentHelp = 4;                             // 当前助力次数
  optional int32 HelpMaxTimeMs = 5;                           // 最大助力时间
  optional int32 CurrentTotalHelpTimeMs = 6;                  // 当前总助力时间
}

// 好友助力功夫熊猫请求
message HelpKungFuPandaRacingReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 FArea = 1;                                  // 好友用户大区（2-手Q）
  optional uint32 FPlatId = 2;                                // 好友用户平台（1-安卓，0-ios）
  optional string FOpenId = 3;                                // 好友OpenId
  optional int64 FUid = 4 [(field_meta_uuid) = true];         // 好友用户uid
  optional uint32 Area = 5;                                   // 用户大区（2-手Q）
  optional uint32 PlatId = 6;                                 // 用户平台（1-安卓，0-ios）
  optional string OpenId = 7;                                 // 用户OpenId
  optional int64 Uid = 8;                                     // 用户uid
  optional int32 IActivityId = 9;                             // 活动id
  optional uint32 Partition = 11;                             // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 HelpTimeMs = 12;                              // 本次助力时间
}

// 好友助力功夫熊猫响应
message HelpKungFuPandaRacingRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 MaxHelp = 3;                                 // 助力最大次数
  optional int32 CurrentHelp = 4;                             // 当前助力次数
  optional int32 HelpMaxTimeMs = 5;                           // 最大助力时间
  optional int32 CurrentTotalHelpTimeMs = 6;                  // 当前总助力时间
}

// 心愿活动助力绑定请求
message DoWishActivityHelpBindReq {
  option (handler) = IH_ActivitySvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;                           // 使用uid 标识
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];         // Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 ActivityId = 6;                            // 活动id
  optional int64 InviterUid = 7;                            // 邀请者Uid（InviterUid发邀请给Uid，Uid打开页面操作要给InviterUid助力）
  // 服务器内部使用字段
  optional WishActivityHelpBindParams Params = 11;          // 额外信息
}

// 心愿活动助力绑定响应
message DoWishActivityHelpBindRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 心愿活动助力信息查询请求
message QueryWishActivityHelpInfoReq {
  option (handler) = IH_ActivitySvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;                           // 使用uid 标识
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];         // Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 ActivityId = 6;                            // 活动id
  optional int64 InviterUid = 7;                            // 邀请者Uid（InviterUid发邀请给Uid，Uid打开页面操作要给InviterUid助力）
}

// 心愿活动助力信息查询应答
message QueryWishActivityHelpInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 TodayLeftHelpCnt = 3;  // 今日剩余可助力次数
  optional int32 MaxDailyHelpCnt = 4;   // 每日可助力次数上限
  optional int32 HasHelpTarget = 5;     // 是否已经给目标玩家助力过
}

// 设置用户标签请求-通用
message DoSetCommonUserLabelReq {
  option (handler) = IH_IdipSvr;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 UserLabelId = 6;                           // 用户标签ID
  repeated string UserLabelVals = 7;                        // 用户标签值,废弃了,解析不了的神奇格式，XX****
  optional string UserLabelValFmtStr = 8;                   // 用户标签值,; 做分割
  optional int64 ExpiredTime = 101;                         // 默认为0，非0表示有效截止时间戳（秒）
  optional uint32 Source = 102;                             // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 103;                             // 流水号
}

// 设置用户标签响应-通用
message DoSetCommonUserLabelRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
  optional int32 RetCode = 3;                                 // 业务处理返回的NKErrorCode
}

message AqDoFarmPutDownReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int32 Cancel = 7;                               // 取消
  optional int64 Duraction = 8;
  optional string Evilstr = 9 [(urlcode) = true];
  optional string ReportStr = 10 [(urlcode) = true];
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 Duration = 12;
}

message AqDoFarmPutDownRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoFarmForbidGiftSendingReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];         // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int32 Cancel = 7;                               // 取消
  optional int64 Duration = 8;                              // 持续时间
  optional string Evilstr = 9 [(urlcode) = true];           // 恶意描述
  optional string ReportStr = 10 [(urlcode) = true];        // 举报字符串
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoFarmForbidGiftSendingRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoFarmBlockGiftMsgReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];         // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int32 Cancel = 7;                               // 取消
  optional string Evilstr = 8 [(urlcode) = true];           // 恶意描述
  optional string ReportStr = 9 [(urlcode) = true];         // 举报字符串
  optional uint32 Partition = 10;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 GiftID = 13;                               // 礼物ID
}

message AqDoFarmBlockGiftMsgRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoClearFarmReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional string Evilstr = 9 [(urlcode) = true];
  optional string ReportStr = 10 [(urlcode) = true];
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoClearFarmRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message QueryFarmGetItemListReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Partition = 5;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message FarmItemInfo {
  optional int64 ItemId = 1;                                // 道具ID
  optional int64 ItemNum = 2;                               // 道具数量
}

message QueryFarmGetItemListRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
  repeated FarmItemInfo ItemInfoList = 3;
}

message DoFarmAddItemReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int64 ItemId = 7;                                // 道具ID
  optional int64 ItemNum = 8;                               // 道具数量
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DoFarmAddItemRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message DoFarmMonthCardReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId                               
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 Mode = 8;                                // 1表示到期时间设置为time，2表示，如未过期，到期时间加上time（可以为负），如果已过期就相当于玩家购买了time时间
  optional int64 Time = 9;                               // 时间值
}

message DoFarmMonthCardRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}


message DoFarmDelItemReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int64 ItemId = 7;                                // 道具ID
  optional int64 ItemNum = 8;                               // 道具数量
  optional uint32 Partition = 9;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DoFarmDelItemRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message DoFarmAddFishExpReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int32 FishType = 7;                                // 鱼类型
  optional int32 Exp = 8;                               // 增加的经验
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DoFarmAddFishExpRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message DoXiaoWoResetMapToVersionReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int64 Version = 7;                               // 目标版本号
  optional uint32 Partition = 11;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message DoXiaoWoResetMapToVersionRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}
message QueryXiaoWoGetCreatorLayoutReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5;                                   // Uid
  optional int64 LayoutPubId = 6;                           // 创作家方案ID
  optional int64 CreatorId = 8;                             // 创作家ID
}

message LayoutInfo {
  optional int64 LayoutPubId = 1;        // 创作家方案ID
  optional int64 LayoutId = 2;           // 原始方案ID
  optional int64 CreatorId = 3;          // 创作家ID
  optional string Title = 4;             // 方案标题
  optional string Desc = 5;              // 方案描述
  optional int64 UpdateTime = 6;         // 更新时间
}

message QueryXiaoWoGetCreatorLayoutRsp {
  optional int32 Result = 1;                                // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                               // 返回消息
  repeated LayoutInfo LayoutList = 3;	                      // 方案列表
}

message DoXiaoWoPubCreatorlayoutReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5;                                   // Uid
  optional int64 LayoutPubId = 6;                           // 创作家方案ID
  optional int64 LayoutId = 7;                              // 原始方案ID
  optional int64 CreatorId = 8;                             // 创作家ID
  optional string Title = 9;                                // 方案标题
  optional string Desc = 10;                                // 方案描述
}

message DoXiaoWoPubCreatorlayoutRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
}

message DoXiaoWoDelCreatorlayoutReq {
  option (handler) = IH_XiaowoSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5;                                   // Uid
  optional int64 LayoutPubId = 6;                           // 创作家方案ID
}

message DoXiaoWoDelCreatorlayoutRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
}

message SyncGameTvRewardStatusReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 HasReward = 6;                               // 是否有奖励
  optional int64 RewardExpireTime = 7;                        // 奖励过期时间
}

message SyncGameTvRewardStatusRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 设置用户账号注销状态请求(修复问题s)
message SetUserAccountCancelStateReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

// 设置用户账号注销状态响应
message SetUserAccountCancelStateRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message DoFarmAdjustBuildingLevelReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 TypeId = 8;                                // 建筑类型
  optional int32 Value = 9;                                 // 调整值
}

message DoFarmAdjustBuildingLevelRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;
}

message DoFarmAdjustMainExpReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 Value = 8;                                 // 调整值
}

message DoFarmAdjustMainExpRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;
}

message DoFarmAddCropExpReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 CropType = 8;                             // 养殖物类型
  optional int64 AddExp = 9;                               // 增加经验值
}

message DoFarmAddCropExpRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;
}
message DisbandSquadReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 SquadId = 4;                                 // OpenId
  optional uint32 Source = 5;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                                 // 流水号
}

message DisbandSquadRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
}

// 批量修改好友亲密度
message DoModifyFriendIntimacyReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];        // Uid
  optional string FriendUidList = 6;                        // 好友UID列表，分号分隔
  optional int64 ChangeIntimacy = 7;                        // 修改值
}

message DoModifyFriendIntimacyRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  optional string SuccessUidList = 3;                         // 成功的好友UID列表
  optional string FailedUidList = 4;                          // 成功的好友UID列表
}

// 好像好友亲密度
message QueryFriendIntimacyReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];        // Uid
  optional int64 FriendUid = 6;                             // 好友UID
}

message QueryFriendIntimacyRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  optional int64 Intimacy = 3;                                // 亲密度值
}

message AqDoFarmWelcomeClearReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                     // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoFarmWelcomeClearRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoFarmWelcomeBanReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                     // Uid
  optional uint64 Time = 5;                                   // 禁止的时间(秒)
  optional uint32 Partition = 6;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoFarmWelcomeBanRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoFarmWelcomeSetFreeReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                     // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoFarmWelcomeSetFreeRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoFarmLiuYanMessageDeleteReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                     // Uid
  optional uint64 LiuYanMessageId = 5;                              // 留言id
  optional uint32 Partition = 7;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 Type = 8;                                    // 类型：0留言 1点评回复
  optional uint64 ReplyId = 9;                                // 点评回复id
}

message AqDoFarmLiuYanMessageDeleteRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoFarmLiuYanMessageBanReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint64 Time = 5;                                   // 禁止的时间(秒)
  optional uint32 Partition = 6;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoFarmLiuYanMessageBanRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoFarmLiuYanMessageSendFreeReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoFarmLiuYanMessageSendFreeRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoModifyRechargeLevelReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional uint32 OperateType = 6;                            // 0:更新氪条并删除已领奖状态到当前氪条进度  1:删除氪条已领奖状态 2:增加氪条已领奖状态
  repeated uint32 LevelIdList = 7;
}
message AqDoModifyRechargeLevelRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 玩家x
message PlayerReputationScoreModifyReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
  optional uint32 Partition = 5;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 ModeId = 6;                                  // 玩法id
  optional int32 ScoreId = 7;                                 // 分数组id
  optional int32 ModifyScore = 8;                             // 修改分数
  optional int64 BattleId = 9;                                // 对局id
  optional int32 BehaviorId = 10;                             // 行为id
  optional uint32 Source = 11;                                // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 12;                                // 流水号
}

message PlayerReputationScoreModifyRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 查询用户是否拥有道具请求
message QueryPlayerHasItemReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  repeated uint32 ItemIdList = 6;                           // 查询道具id列表
}

// 查询用户是否拥有道具响应
message QueryPlayerHasItemRsp {
  optional int32 Result = 1;                                // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                               // 返回消息
  repeated sItemInfo ItemList = 3;                          // 道具列表
}
message QueryPlayerCommodityBuyTimesReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 CommodityId = 6;                             // 商品ID
}

message QueryPlayerCommodityBuyTimesRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 CommodityId = 3;                             // 商品ID
  optional int32 BuyNum = 4;                                  // 购买数量(负数为回滚)
  optional int32 LimitType = 5;
  optional int64 ExpireTimeMs = 6;
  optional int32 LimitNum = 7;
}

message sCommodityInfo {
  optional int32 CommodityId = 1;                             // 商品ID
  optional int32 BuyNum = 2;                                  // 购买数量(负数为回滚)
  optional int32 LimitType = 3;
  optional int64 ExpireTimeMs = 4;
  optional int32 LimitNum = 5;
}

message DoUpdatePlayerCommodityBuyTimesReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 CommodityId = 6;                             // 商品ID
  optional int32 BuyNum = 7;                                  // 购买数量(负数为回滚)
  repeated sCommodityInfo CommodityInfo = 8;                  // 多商品
}

message DoUpdatePlayerCommodityBuyTimesRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 修改游戏防欺凌设置请求
message AqDoModifyGameProtectionSettingsReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 ProtectionType = 6;                          // 设置类型
  optional string Value = 7;                                  // 设置参数
  optional uint32 Source = 8;                                 // 渠道号，由前端生成，不需要填写
  optional string Serial = 9;                                 // 流水号，由前端生成，不需要填写
}

// 修改游戏防欺凌设置应答
message AqDoModifyGameProtectionSettingsRsp {
  optional int32 Result = 1;                                  // 结果：成功(0)，玩家不存在(1)，失败(其他)
  optional string RetMsg = 2;                                 // 返回消息
}

// 查询游戏防欺凌设置请求
message AqQueryGameProtectionSettingsReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 Partition = 2;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 3;                                 // OpenId
  repeated uint32 ProtectionTypeList = 4;                     // 查询道具id列表（最多100个）
  optional int32 QueryAll = 5;                                // 如果为1查询所有设置值，返回按type递增顺序最多100个
  optional uint32 PlatId = 6;                                 // 平台（1-安卓，0-ios）
  optional int64 Uid = 7 [(field_meta_uuid) = true];          // Uid
}

// 防欺凌设置信息
message sGameProtectionSetting {
  optional int32 ProtectionType = 1;                          // 防欺凌设置类型
  optional string Value = 2;                                  // 设置值
}

// 查询游戏防欺凌设置请求应答
message AqQueryGameProtectionSettingsRsp {
  optional int32 Result = 1;                                  // 结果：成功(0)，玩家不存在(1)，失败(其他)
  optional string RetMsg = 2;                                 // 返回消息
  optional uint32 PlatId = 3;                                 // 平台（1-安卓，0-ios）
  optional int64 Uid = 4;                                     // Uid
  repeated sGameProtectionSetting SettingList = 5;            // 设置列表（最多100个）
}

// 活动小队信息查询请求
message QueryActivitySquadInfoReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 SquadId = 4;                                 // OpenId
}

message sSquadMember {
  optional int64 Uid = 1;
  optional string Nickname = 2;                               //昵称
  optional int32 Gender = 3;                                  //性别  1-男 2-女 0-未知
  optional string Profile = 4;                                //头像url (maybe)
  optional int32 Level = 5;                                   //等级
  optional string Openid = 6;                                 //gopenid
  optional proto_ActivitySquadData MemberSquadData = 1001;
}

message QueryActivitySquadInfoRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
  optional int64 SquadId = 3;                               // 小队id
  repeated sSquadMember MemberInfoList = 4;                 // 成员列表
  optional int64 LeaderUid = 5;                             // 队长uid
  optional string Name = 6;                                 // 小队名称
  optional string GroupPhotoUrl = 7;                        // 合影地址
}

// 活动小队加入请求
message DoPlayerJoinSquadReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];        // Uid
  optional int64 SquadId = 6;                               // 小队id
  optional int32 ActivityId = 7;                            // 活动期数
}

message DoPlayerJoinSquadRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message DoClubSetOwnerReq {
  option (handler) = IH_ClubSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5;                                     // Uid
  optional int64 ClubId = 6[(field_hash_key) = true];         // ClubId
}

message DoClubSetOwnerRsp {
  optional int32 Result = 1;                                  // 结果：成功(0)，玩家不存在(1)，失败(其他)
  optional string RetMsg = 2;                                 // 返回消息
}

// 可视化编程模板修改类型
enum CodingCommunityTemplateModifyType {
  CODING_COMMUNITY_TEMPLATE_MODIFY_TYPE_NAME = 1;           // 修改名称
  CODING_COMMUNITY_TEMPLATE_MODIFY_TYPE_DESC = 2;           // 修改描述
}

// 修改可视化编程模板信息请求(路由到平台侧进行处理)
message DoModifyCodingCommunityTemplateReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];        // Uid
  optional string TemplateId = 6;                           // 模板id
  optional int32 ModifyType = 7;                            // 修改类型, 详情见枚举CodingCommunityTemplateModifyType所示
  optional string ModifyContent = 8;                        // 修改内容
}

// 修改可视化编程模板信息响应(路由到平台侧进行处理)
message DoModifyCodingCommunityTemplateRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 发送全服邮件请求(附件支持有效期设置)
message SendGlobalMailWithAttachExpireReq {
  option (handler) = IH_IdipSvr;
  optional string MailTitle = 1 [(urlcode) = true];       // 邮件标题
  optional string MailContent = 2 [(urlcode) = true];     // 邮件内容
  optional string Sender = 3 [(urlcode) = true];          // 发件人
  repeated sAttachInfo AttachList = 4;                    // 附件
  optional uint64 BeginTime = 5;                          // 开始发送时间
  optional uint64 EndTime = 6;                            // 结束发送时间
  optional string Url = 7 [(urlcode) = true];             // 跳转链接
  optional int32 MailGroupId = 8;                         // 邮件分组id
  optional uint32 Source = 9;                             // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                            // 流水号
  repeated uint32 AccountChannels = 11;                   // 账号渠道限制(空表示不限制)
  optional uint32 IsOversea = 12;                         // 是否是国际化邮件（是的话，MailContent,MailTitle, Sender 为json 格式）
  optional uint32 MinLevel = 13;                          // 最低等级
  optional uint32 AreaId = 14;                            // 大区
  repeated uint32 RegisterChannels = 15;                  // 用户注册渠道限制(空表示不限制)
  optional uint32 Partition = 16;                         // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string LowestVersion = 17;                     // 最低版本号(包含)
  optional string HighestVersion = 18;                    // 最高版本号(包含)
  repeated uint32 LoginChannelIds = 19;                   // 用户登录渠道限制(空表示不限制)
  repeated uint32 PlatIds = 20;                           // 用户账号平台id限制(空表示不限制)
  repeated sMailBuyItem MailBuyItemList = 21;             // 购买商品
  optional string MailContentImg = 22;                    // 邮件内容图片
  optional int32 MailAttachmentType = 23;                 // 邮件附件类型, 参见 MailAttachmentType
  optional string MailAttachmentTypeContent = 24;         // 邮件附件类型内容, 根据 MailAttachmentType 自己去解析. (mailAttachmentType==1:多个ugcId用分号分隔)
}

// 发送全服邮件响应(附件支持有效期设置)
message SendGlobalMailWithAttachExpireRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 发送全服邮件请求(附件支持有效期设置)
message SendGlobalMailWithAmsAttachExpireReq {
  option (handler) = IH_IdipSvr;
  optional string MailTitle = 1 [(urlcode) = true];       // 邮件标题
  optional string MailContent = 2 [(urlcode) = true];     // 邮件内容
  optional string Sender = 3 [(urlcode) = true];          // 发件人
  repeated sMailAttachInfo MailAttachList = 4;            // 附件
  optional uint64 BeginTime = 5;                          // 开始发送时间
  optional uint64 EndTime = 6;                            // 结束发送时间
  optional string Url = 7 [(urlcode) = true];             // 跳转链接
  optional int32 MailGroupId = 8;                         // 邮件分组id
  optional uint32 Source = 9;                             // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                            // 流水号
  repeated uint32 AccountChannels = 11;                   // 账号渠道限制(空表示不限制)
  optional uint32 IsOversea = 12;                         // 是否是国际化邮件（是的话，MailContent,MailTitle, Sender 为json 格式）
  optional uint32 MinLevel = 13;                          // 最低等级
  optional uint32 AreaId = 14;                            // 大区
  repeated uint32 RegisterChannels = 15;                  // 用户注册渠道限制(空表示不限制)
  optional uint32 Partition = 16;                         // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string LowestVersion = 17;                     // 最低版本号(包含)
  optional string HighestVersion = 18;                    // 最高版本号(包含)
  repeated uint32 LoginChannelIds = 19;                   // 用户登录渠道限制(空表示不限制)
  repeated uint32 PlatIds = 20;                           // 用户账号平台id限制(空表示不限制)
  repeated sMailBuyItem MailBuyItemList = 21;             // 购买商品
  optional string MailContentImg = 22;                    // 邮件内容图片
  optional int32 MailAttachmentType = 23;                 // 邮件附件类型, 参见 MailAttachmentType
  optional string MailAttachmentTypeContent = 24;         // 邮件附件类型内容, 根据 MailAttachmentType 自己去解析. (mailAttachmentType==1:多个ugcId用分号分隔)
}

// 发送全服邮件响应(附件支持有效期设置)
message SendGlobalMailWithAmsAttachExpireRsp {
  optional int32 Result = 1;                                // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 可视化编程模板操作类型
enum CodingCommunityTemplateOperateType {
    CODING_COMMUNITY_TEMPLATE_OPERATE_TYPE_REMOVE = 1;      // 下架操作
    CODING_COMMUNITY_TEMPLATE_OPERATE_TYPE_RECOVER = 2;     // 恢复操作
}

// 修改可视化编程模板状态请求(路由到平台侧进行处理)
message DoModifyCodingCommunityTemplateStateReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];        // Uid
  optional string TemplateId = 6;                           // 模板id
  optional int32 OperateType = 7;                           // 操作类型, 详情见枚举CodingCommunityTemplateOperateType所示
}

// 修改可视化编程模板状态响应(路由到平台侧进行处理)
message DoModifyCodingCommunityTemplateStateRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 解散指定社团
message AqDoClubDissolveReq {
  option (handler) = IH_ClubSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5;                                     // Uid
  optional int64 ClubId = 6[(field_hash_key) = true];         // ClubId
}

message AqDoClubDissolveRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}


// 踢出社团内指定玩家
message AqDoClubKickMemberReq {
  option (handler) = IH_ClubSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5;                                     // Uid
  optional int64 ClubId = 6[(field_hash_key) = true];         // ClubId
  optional string Reason = 7;                                 // 踢出原因
}

message AqDoClubKickMemberRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message PaidItem {
  optional string ItemId = 1;                                // 道具id
  optional uint32 ItemNum = 2;                               // 道具数量
}

// 补发 ugc星钻内购道具请求
message AddUgcPaidItemsReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];           // Uid
  optional int64 UgcId = 6;                                   // UgcId
  repeated PaidItem PaidItemList = 7;                         // 踢出原因
}

message AddUgcPaidItemsRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 特训营获取运动员数据
message DoTrainingCampGetSportsmanDataReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;                           // 使用uid 标识
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];         // Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 ActivityId = 6;                            // 活动id
  optional int32 SportsId = 7;                              // 运动员id
}

// 特训营获取运动员数据
message DoTrainingCampGetSportsmanDataRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 SportsId = 3;
  optional int32 Score = 4;
  repeated AttrInfo Attr = 5;
}

message AttrInfo {
  required int32 Key = 1; // key
  required int32 Value = 2; // value
}

// 特训营训练运动员
message DoTrainingCampTrainSportsmanReq {
  option (handler) = IH_ActivitySvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;                           // 使用uid 标识
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];         // Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 ActivityId = 6;                            // 活动id
  optional int32 SportsId = 7;                              // 运动员id
  optional int64 AssistUid = 8;                             // 助力人Uid
}

// 特训营训练运动员
message DoTrainingCampTrainSportsmanRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 SportsId = 3;
  optional int32 Score = 4;
  repeated AttrInfo Attr = 5;
}

// 训练活动获取训练对象数据请求
message DoTrainingGetOjbectDataReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;                           // 使用uid 标识
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];         // 查询数据对象的Uid
  optional int64 AssistUid = 5;                             // 助力人的Uid
  optional uint32 Partition = 6;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 ActivityId = 7;                            // 活动id
  optional int32 ObjectId = 8;                             	// 训练对象ID
}

// 训练活动获取训练对象数据返回
message DoTrainingGetOjbectDataRsp {
  optional int32 Result = 1;					// 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;					// 返回消息
  optional int32 ObjectId = 3;					// 训练对象ID
  optional int32 Score = 4;						// 训练兑现当前积分
  optional int32 CanTraining = 5;				// 是否有资格对该玩家助力 (0.可以 1.不可以)
}

// 训练活动对目标进行助力请求
message DoTrainingTrainObjectReq {
  option (handler) = IH_ActivitySvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;                           // 使用uid 标识
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];         // Uid
  optional int64 AssistUid = 5;                             // 助力人Uid
  optional uint32 Partition = 6;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 ActivityId = 7;                            // 活动id
  optional int32 ObjectId = 8;                              // 训练对象ID
}

// 训练活动对目标进行助力返回
message DoTrainingTrainObjectRsp {
  optional int32 Result = 1;			// 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;			// 返回消息
  optional int32 ObjectId = 3;			// 训练对象ID
  optional int32 Score = 4;				// 当前积分
}

// 动物图鉴领取赠送图鉴
message AnimalHandbookReceiveGiveAnimalReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                           // 大区（2-手Q）
  optional uint32 PlatId = 2;                           // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                           // 用户OpenID
  optional int64 Uid = 4 [(field_meta_uuid) = true];    // 玩家id
  optional uint32 Partition = 5;                        // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 ActivityId = 6;                       // 活动id
  optional string GiveId = 7;                           // 赠送id
}

message AnimalHandbookReceiveGiveAnimalRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}


message QueryFarmGetBasicinfoReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                               // OpenId
  optional int64 Uid = 5 [(field_hash_key) = true];         // Uid
}

message QueryFarmGetBasicinfoRsp {
  optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
  optional string RetMsg = 2;                                 // 返回消息
  optional int64 FarmID = 3;                                // 农场ID
  optional int32 FarmLevel = 5;                              // 农场等级
  optional int64 FarmExp = 6;                                // 农场经验
  optional int64 FarmCoin = 8;                                   // 农场币
}

// 修改社团活跃值
message ModifyClubHeatReq {
  option (handler) = IH_ClubSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional int64 ClubId = 3[(field_hash_key) = true];
  optional int32 Heat = 4;
}

message ModifyClubHeatRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 修改社团归属地
message ModifyClubLBSReq {
  option (handler) = IH_ClubSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional int64 ClubId = 3[(field_hash_key) = true];
  optional int32 Province = 4;
  optional int32 City = 5;
  optional int32 Town = 6;
}

message ModifyClubLBSRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 禁止社团参加榜单
message BanClubHeatRankReq {
  option (handler) = IH_ClubSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional int64 ClubId = 3[(field_hash_key) = true];
  optional int32 IsBan = 4;
}

message BanClubHeatRankRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message QueryGetUgcPlayedMapsReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional string OpenId = 1;                               // OpenId
  optional int64 Uid = 2;         // Uid
  optional int64 CreatorId = 3;
  optional int32 Page = 4;
}

message QueryGetUgcPlayedMapsRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  repeated int64 MapIdList = 3;
  optional int32 HasMorePage = 4;
}

message ModifyCupsProgressReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // 玩家id
  optional int32 Progress = 5;                              // 进度
  optional int32 Cycle = 6;                              // 周目 默认一周目
  optional uint32 Partition = 100;                          // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional uint32 Source = 101;                             // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 102;                             // 流水号

}

message ModifyCupsProgressRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}


// 清理玩家背包互动组合数据请求
message DoCleanUserBagInteractCombReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
}

// 清理玩家背包互动组合数据响应
message DoCleanUserBagInteractCombRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message QueryFarmLiuYanMessageReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_hash_key) = true];           // Uid
  optional int32 Page = 6;                                    // 页码
}


message QueryFarmLiuYanMessageRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional uint32 FarmLiuYanMessageCount = 3;                     // 留言数量
  repeated FarmLiuYanMessage FarmLiuYanMessageList = 4;           // 留言列表
  optional int32 Page = 5;                                    // 页码
  optional int32 TotalPageNo = 6;                             // 总页码
}

message ModifyChargeProgressReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int64 ChangeNum = 6;                               // 修改金额：分
  optional int64 TaskChangeNum = 7;                               // 累冲任务修改金额：分
  optional string TaskTime = 8;                                 // 累冲任务统计时间
}

message ModifyChargeProgressRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}
message AqDoFarmHousePutDownReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int32 Cancel = 7;                               // 取消
  optional string Evilstr = 8 [(urlcode) = true];
  optional string ReportStr = 9 [(urlcode) = true];
  optional uint32 Partition = 10;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 Duration = 11;
}
message AqDoFarmHousePutDownRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoFarmHousePickAllFurnitureReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_hash_key) = true];           // Uid
  optional uint32 Source = 6;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                               // 流水号
  optional int32 BuildingId = 8;                            // 建筑id
}

message AqDoFarmHousePickAllFurnitureRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message DoUpdateSeasonFashionEquipBookReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  repeated int32 ItemIds = 6;
  optional int32 SeasonId = 7;
}

message DoUpdateSeasonFashionEquipBookRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoFarmResetPetNameReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_hash_key) = true];           // UID
  optional int32 PetId = 6;                                   // 宠物ID
  optional uint32 Source = 7;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;                                 // 流水号
}

message AqDoFarmResetPetNameRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message AqDoFarmBanChangePetNameReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_hash_key) = true];           // UID
  optional int32 BanTime = 6;                                 // 处罚时间
  optional int32 IsCancel = 7;                                // 是否取消 1是 0否
  optional uint32 Source = 8;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 9;                                 // 流水号
}

message AqDoFarmBanChangePetNameRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message DoFarmModifyVillagerReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_hash_key) = true];           // UID
  optional int32 OpType = 6;                                   // 操作类型
  optional int32 VillagerId = 7;                               // 村民ID
  optional int64 Para1 = 8;                                    // 参数1
  optional int64 Para2 = 9;                                    // 参数2
  optional uint32 Source = 10;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 11;                                 // 流水号
}

message DoFarmModifyVillagerRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message DeliverMidasProductReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string ProductId = 6;                                // 商品ID
  optional uint32 ProductNum = 8;                                 // 数量
  optional int32 ActivityId = 9;
}

message DeliverMidasProductRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}


// 言语违规处罚信誉分处罚请求
message VerbalViolationReputationScorePunishReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string BattleViolationCallbackInfo = 6;            // 对局言语/语音违规回调信息
  optional int64 Time = 7;									                  // 发生时间
  optional int32 Label = 8;									                  // 违规标签
  optional int32 Type = 9;									                  // 违规类型, 1: 文字违规; 2: 语音违规
  optional uint32 Source = 11;                                // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 12;                                // 流水号
}

// 言语违规处罚信誉分处罚响应
message VerbalViolationReputationScorePunishRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 言语违规信誉分误处罚回撤并补分请求
message VerbalViolationReputationScoreUnPunishReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string BattleViolationCallbackInfo = 6;            // 对局言语/语音违规回调信息
  optional uint32 Score = 7;                                  // 补分数值
  optional uint32 Source = 11;                                // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 12;                                // 流水号
}

// 言语违规信誉分误处罚回撤并补分响应
message VerbalViolationReputationScoreUnPunishRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 超核活动玩家参与权限查询请求
message QueryPlayerSuperCoreRankActivityAuthReq {
  option (handler) = IH_GameSvr;
  option (queryMsg) = true;
  option (checkUserInfo) = true;
  option (hacked) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 ActivityId = 6;                              // 本次活动id
}

// 超核活动玩家参与权限查询响应
message QueryPlayerSuperCoreRankActivityAuthRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 IsAllowed = 3;                                 // 是否允许参与 (1-允许参与活动, other-不允许)
}

// 超核活动玩家排名分数修改请求
message DoSuperCoreRankActivityScoreModifyReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 ActivityId = 6;                              // 活动id
  optional int32 ModifyType = 7;                              // 修改的类型
  optional int32 InitTotalRmbYuan = 8;                        // 初始时充值yuan
  repeated sItemInfo CostItemsList = 9;                       // 消费的道具数量

  optional uint32 Source = 101;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 102;                               // 流水号
}

// 超核活动玩家排名分数修改响应
message DoSuperCoreRankActivityScoreModifyRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 超核活动玩家排名分数细节查询请求
message QueryPlayerSuperCoreRankActivityScoreReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];        // Uid
  optional uint32 Partition = 5;                            // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 ActivityId = 6;                            // 活动id
}

// 超核活动玩家排名分数细节查询响应
message QueryPlayerSuperCoreRankActivityScoreRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 RmbScore = 3;                                // 充值的分数
  optional int32 ActiveDaysScore = 4;                         // 活跃天数分数
  repeated sItemInfo ItemScoreList = 5;                       // 货币消费分数
}

message ModifyTradingCardReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 CardId = 6;                                  // 卡牌id
  optional int32 Num = 7;                                     // 数量
  optional int32 ModifyType = 8;                              // 0-增加 1-删除
}

message ModifyTradingCardRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 查询卡牌交易信息
message QueryCardTradeInfoReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string TradeId = 6;                                // 交易Id
}

message TradingCardIdipInfo {
  optional int32 CardId = 1;          // 卡牌id
  optional int32 Type = 2;            // 卡牌类型
  optional int32 Star = 3;            // 星级
  optional string Name = 4;           // 卡牌名
  optional string NormalBg = 5;       // 卡牌图标
  optional string Icon = 6;           // 卡牌图标
  optional string CardStarBg = 7;     // 卡牌星级底图
  optional string Desc = 8;           // 描述
  optional string TextDesBg = 9;      // 文案描述底图
}

message QueryCardTradeInfoRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 TradeType = 3;                               // 交易类型
  repeated TradingCardIdipInfo CardIdList = 4;                // 卡牌列表 交换请求第一个为发起方卡牌，第二个为交换方卡牌
  optional int64 OriginPlayerUid = 5;                         // 发起玩家uid
  optional string OriginPlayerName = 6;                       // 发起玩家名字
  optional int64 FinishTimeMs = 7;                            // 完成时间
  optional int64 ExpireTimeMs = 8;                            // 过期时间
}

// 领取赠送的卡牌
message ReceiveTradingCardReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string TradeId = 6;                                // 交易Id
}

message ReceiveTradingCardRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 ErrorCode = 3;                               // 错误码
}

// 扣除在线玩家道具请求
message DoDeductOnlinePlayerItemReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  repeated sItemInfo ItemList = 7;                            // 待处理的道具列表信息
  optional uint32 Source = 8;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 9;                                 // 流水号
  repeated ExtParamInfo ExtParams = 10;                       // 附加数据
}

// 扣除在线玩家道具响应
message DoDeductOnlinePlayerItemRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  repeated sItemInfo ItemList = 3;                            // 修改后的用户道具列表信息
}

// 查询玩家和另一个玩家间的亲密度请求
message QueryPlayerAndOtherPlayerRelationReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string OtherOpenId = 6;                            // 另一个玩家Openid
  optional uint32 OtherPlatId = 7;                            // 另一个玩家平台（1-安卓，0-ios）
  optional int64 OtherUid = 8;                                // 另一个玩家Uid
}

// 查询玩家和另一个玩家间的亲密度响应
message QueryPlayerAndOtherPlayerRelationRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int64 Relation = 3;                                // 亲密度
}

// 查询充值金额
message QueryChargeMoenyReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string BeginTime = 6;                                 // 累充统计开始时间(yyyyMMddHH)
  optional string EndTime = 7;                                 // 累充统计结束时间(yyyyMMddHH)
}

message QueryChargeMoenyRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int64 RechargeNum = 3;                             // 充值金额：角
}

message DoFarmDelBuildingSkinReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 SkinId = 8;                                // 皮肤ID
}

message DoFarmDelBuildingSkinRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;
}

// 查询福签是否可以领取
message QueryLuckStarReceiveReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string UniqueId = 6;                                 // 查询福签是否可以领取
  optional int32 IActivityId = 7;                             // 活动号
  option (priorityUseUid) = true;                            // 使用uid 标识
}

message QueryLuckStarReceiveRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 Receive = 3;                                  // 是否可以领取0-可以，1-不可以
  optional int64 SlipNum = 4;                             // 当前该福签拥有数量
}

// 查询福签是否可以赠与
message QueryLuckStarGiveReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string UniqueId = 6;                                 // 查询福签是否可以领取
  optional int32 IActivityId = 7;                             // 活动号
  option (priorityUseUid) = true;                            // 使用uid 标识
}

message QueryLuckStarGiveRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 Give = 3;                                  // 是否可以赠与0-可以，1-不可以
  optional int64 SlipNum = 4;                             // 当前该福签拥有数量
}

// 领取福签
message DoLuckStarReceiveReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string UniqueId = 6;                                 // 查询福签是否可以领取
  optional int32 IActivityId = 7;                             // 活动号
  optional uint32 Source = 8;                             // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 9;                            // 流水号
  option (priorityUseUid) = true;                        // 使用uid 标识
}

message DoLuckStarReceiveRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 赠与福签
message DoLuckStarGiveReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional string UniqueId = 6;                                 // 查询福签是否可以领取
  optional int32 IActivityId = 7;                             // 活动号
  optional uint32 Source = 8;                             // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 9;                            // 流水号
  option (priorityUseUid) = true;                        // 使用uid 标识
}

message DoLuckStarGiveRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message sStreamAudienceSimpleInfo {
  optional string AudienceHeadUrl = 1;                                // 观众url
  optional string AudienceNickName = 2;                               // 观众昵称
}

message NotifyStreamInteractionCmdReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 GiftId = 6;                                 // 特效id
  optional int32 Num = 7;                                      // 特效触发次数
  repeated sStreamAudienceSimpleInfo AudienceList = 8;         // 观众
}

message NotifyStreamInteractionCmdRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 平台接口中转请求类型(废弃)
enum PlatformInterfaceForwardReqType {
  FORWARD_REQ_TYPE_GET_USER_FARM_INFO = 1;                    // 获取用户农场信息操作
  FORWARD_REQ_TYPE_FARM_EXTERNAL_OPERATE = 2;                 // 农场收集操作
}

// 平台接口中转请求
message PlatformInterfaceForwardReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional int32 Type = 6;                                    // 请求类型, 详情见枚举PlatformInterfaceForwardReqType所示
  optional string ReqData = 7 [(urlcode) = true];             // 调用方将平台侧请求数据经由urlencode编码后设置
}

// 平台接口中转响应
message PlatformInterfaceForwardRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional string RspData = 3;                                // idipsvr将平台侧返回数据经由urlencode编码后设置
}
message DoFarmMagicAddOrDelReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 MagicId = 8;                               // 仙术ID
  optional int32 AddOrDel = 9;                              // 添加或删除（1添加 2 删除）
}

message DoFarmMagicAddOrDelRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;
}

message DoFarmMagicAdjustMpReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int32 MagicId = 8;                               // 仙术ID
  optional int64 Value = 9;                                 // 调整值
}

message DoFarmMagicAdjustMpRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;
}

message DoFarmLayoutOpReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_hash_key) = true];          // Uid
  optional uint32 OpType = 6;                                 // 操作类型（1 删除）
  optional int64 LayoutId = 7;                               // 图纸ID
  optional uint32 BuildingId = 8;                             // 建筑ID
  optional uint32 Source = 9;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 10;                               // 流水号
}

message DoFarmLayoutOpRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;
}

// 修改玩家峡谷英雄战力
message DoModifyPlayerArenaHeroCeScoreReq {
  option (handler) = IH_ArenaSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_hash_key) = true];           // Uid
  optional int32 HeroId = 6;                                  // 峡谷英雄ID
  optional int32 Group = 7;                                   // 峡谷玩法分组
  optional int32 BattleScoreChange = 8;                       // 该字段填写战力场次分变化量
  optional int32 PerfScoreChange = 9;                         // 该字段填写战力表现分变化量
}

message DoModifyPlayerArenaHeroCeScoreRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 BeforeBattleScore = 3;                       // 设置前战力场次分
  optional int32 AfterBattleScore = 4;                        // 设置后战力场次分
  optional int32 BeforePerfScore = 5;                         // 设置前战力表现分
  optional int32 AfterPerfScore = 6;                          // 设置后战力表现分
}

// 修改创作者留言（AQ）请求
message AqDoModifyCreatorMessageReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1; // 大区：微信（1），手Q（2）
  optional uint32 PlatId = 2; // 平台：IOS（0），安卓（1）
  optional uint32 Partition = 3; // 小区ID(取值定义为idip正式环境的area)，选填参数，非必要不设置此参数
  optional string OpenId = 4; // openid
  optional int64 Uid = 5 [(field_meta_uuid) = true];   // 用户id
  optional string ModifyContent = 6; // 修改后内容
  optional uint32 Source = 7; // 渠道号，由前端生成，不需要填写
  optional string Serial = 8; // 流水号，由前端生成，不需要填写
}

// 修改创作者留言（AQ）应答
message AqDoModifyCreatorMessageRsp {
  optional int32 Result = 1; // 结果：成功(0)，玩家不存在(1)，失败(其他)
  optional string RetMsg = 2; // 返回消息
}

// 转移角色区服请求
message DoTransferUserPlatReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
  optional uint32 Source = 6;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                               // 流水号
}

// 转移角色区服响应
message DoTransferUserPlatRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 TransferStatus = 3;                          // 转区状态（0-未转区，1-转区中，2-转区成功，3-转区失败）
}

// 查询用户转区资格请求
message QueryTransferAbilityReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
}

message UserTransferAbilityInfo {
  optional int64 Uid = 1;                                     // Uid
  optional string BanStatus = 2;                              // 转区禁用状态
  optional int64 DiamondNum = 3;                              // 星钻数量
  optional uint32 Level = 4;                                  // 等级
  optional uint64 RegisterTime = 5;                           // 注册时间
  optional int32 IsOnline = 6;                                // 在线状态(0-离线，1-在线)
}

// 查询用户转区资格响应
message QueryTransferAbilityRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 Ability = 3;                                 // 转区资格（0-有资格，其他-无资格）
  optional uint64 TransferInterval = 4;                       // 转区间隔时间
  repeated UserTransferAbilityInfo AbilityList = 5;           // 转区资格数据列表
}

// 解除转区登录锁请求
message DoReleaseTransferLockReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (noCheckRoleTransferring) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid
}

// 解除转区登录锁响应
message DoReleaseTransferLockRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

// 查询用户实际平台请求
message QueryUserRealPlatReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  option (queryMsg) = true;
  option (priorityUseUid) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
}

// 查询用户实际平台响应
message QueryUserRealPlatRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 TransferStatus = 3;                          // 转区状态（0-未转区，1-转区中，2-转区成功）
  optional int64 TransferTime = 4;                            // 转区成功时间
  optional uint32 PlatId = 5;                                 // 实际平台（1-安卓，0-ios）
}

message AqDoFarmCookPutDownReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional int32 Cancel = 7;                               // 取消
  optional string Evilstr = 8 [(urlcode) = true];         // 恶意描述
  optional string ReportStr = 9 [(urlcode) = true];         // 举报字符串
  optional uint32 Partition = 10;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional int64 Duration = 11;                           // 持续时间
}
message AqDoFarmCookPutDownRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message DoFarmCookAddEmployeeReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)

  optional int32 AvatarId = 8;                               // 模型id
  optional string Name = 9 [(urlcode) = true];            // 名字
  optional int32 Job = 10 ;                               // 工种
  optional int32 Level = 11;                              // 等级
  optional int32 Quality = 12;                            // 品质
  optional int32 Exclusive = 13;                          // 能力值
  optional int32 Speed = 14;                              // 速度值
  optional int32 Charm = 15;                              // 魅力值
}
message DoFarmCookAddEmployeeRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoFarmPartyClearReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)

  optional int32 Type = 8;                               // 1描述 2封面
}

message AqDoFarmPartyClearRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoFarmPartyBanReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)

  optional int64 Time = 8;                               // 秒
  optional string Reason = 9;                            // 提示语
}

message AqDoFarmPartyBanRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoFarmPartySetFreeReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}
message AqDoFarmPartySetFreeRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoFarmCookScreenClearReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}

message AqDoFarmCookScreenClearRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoFarmCookScreenBanReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)

  optional int64 Time = 8;                               // 秒
  optional string Reason = 9;                            // 提示语
}

message AqDoFarmCookScreenBanRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message AqDoFarmCookScreenSetFreeReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}
message AqDoFarmCookScreenSetFreeRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

// 修改玩家段位分请求
message DoSetRaffleDailyLimitTagWhiteListReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional uint32 Partition = 3;                              // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
  optional string OpenId = 4;                                 // OpenId
  optional int64 Uid = 5 [(field_meta_uuid) = true];          // Uid

  optional uint32 Source = 7;                             // 渠道号，由前端生成，不需要填写
  optional string Serial = 8;                             // 流水号，由前端生成，不需要填写
  optional int32 InWhite = 9;                             // 是否进入白名单
}

// 修改玩家段位分响应
message DoSetRaffleDailyLimitTagWhiteListRsp {
  optional int32 Result = 1;                                  // 结果（0-成功，1-无角色，其他-失败）
  optional string RetMsg = 2;                                 // 返回消息
}

message UgcDataStoreSvrTestReq {
  option (handler) = IH_UgcDataStoreSvr;
  optional uint32 AreaId = 1;
  optional uint32 PlatId = 2;
  optional string OpenId = 3;
  optional int64 Uid = 4;
}

message UgcDataStoreSvrTestRsp {
  optional int32 Result = 1;
  optional string RetMsg = 2;
}
//查询啾灵世界商人请求
message QueryStarPTraderReq{
	option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (queryMsg) = true;
	optional uint32 AreaId = 1;                           		// 大区（2-手Q）
	optional uint32 PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32 Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional int64	StarPId = 4;						  		// 啾灵世界Id
}

//查询啾灵世界商人响应
message QueryStarPTraderRsp{
	optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;                                 // 返回消息
	optional int32 Dump = 3;									// 占位数据
}

//查询啾灵世界地下城、密域、高塔副本的公共数据请求
message QueryStarPCommonPveDataReq{
	option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (queryMsg) = true;
	optional uint32 AreaId = 1;                           		// 大区（2-手Q）
	optional uint32 PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32 Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional int64	StarPId = 4;						  		// 啾灵世界Id
}

message rStarPOneCommonBasePve{
    optional int32 PWorldId = 1;								//副本id
    optional int32 CurPlayerNum = 2;							//当前挑战人数
    optional bool IsFinish = 3;									//关卡是否已挑战完成
    optional int64 LastRefreshTime = 4;							//上次刷新时间
}

//查询啾灵世界地下城、密域、高塔副本的公共数据响应
message QueryStarPCommonPveDataRsp{
	optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;                                 // 返回消息
	repeated rStarPOneCommonBasePve StarPOneCommonBasePve = 3; 	// 副本的公共数据
	
}

// 查询啾灵世界玩家丢弃的物品请求
message QueryStarPPlayerDropItemsReq{
	option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (queryMsg) = true;
	optional uint32 AreaId = 1;                           		// 大区（2-手Q）
	optional uint32 PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32 Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional int64	StarPId = 4;						  		// 啾灵世界Id
	optional int32 PosId = 5;                             		// 所在区域或地图id
	optional int64 CommonId = 6;                          		// commonId,不同数据类型有自己的commonId的规范
}

message rStarPMapPosDropItem {
    optional int32 ItemId = 1;									//物品ID
    optional int64 ItemInstId = 2;								//物品实例ID
    optional int32 ItemType = 3;								//背包物品类型
    optional int64 ItemNum = 4;									//物品数量
}

// 查询啾灵世界玩家丢弃的物品响应
message QueryStarPPlayerDropItemsRsp{
	optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;                                 // 返回消息
	optional rStarPMapPosDropItem StarPMapPosDropItem = 3;		// 道具
}

// 查询啾灵世界建筑信息请求
message QueryStarPMapPosBuildingReq{
	option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (queryMsg) = true;
	optional uint32 AreaId = 1;                           		// 大区（2-手Q）
	optional uint32 PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32 Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional int64	StarPId = 4;						  		// 啾灵世界Id
	optional int32 PosId = 5;                             		// 所在区域或地图id
	optional int64 CommonId = 6;                          		// commonId,不同数据类型有自己的commonId的规范
}

message rStarPMapPosBuilding{
	optional int64 Id = 1;										// 唯一id
	optional int32 ConfigId = 2;								// 配置id
	optional int64 PlacementPlayer = 4;							// 摆放者
	optional int64 GuildId = 5;									//所属的工会id
}

// 查询啾灵世界建筑信息响应
message QueryStarPMapPosBuildingRsp{
	optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;                                 // 返回消息
	optional rStarPMapPosBuilding StarPMapPosBuilding = 3;		// 建筑信息
}

//查询啾灵世界玩家道具请求
message QueryStarPPlayerItemsReq{
	option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (queryMsg) = true;
	optional uint32 AreaId = 1;                           		// 大区（2-手Q）
	optional uint32 PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32 Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional int64	StarPId = 4;						  		// 啾灵世界Id
	optional int64  Uid = 5;                                    // 玩家uid
}

message rStarPPlayerItem{
	optional int64 BackPackId = 1;                          	// 背包ID
	optional int32 Id = 2;                                  	// 物品ID
	optional int64 InstId = 3;                              	// 物品实例ID
	optional int32 ItemType = 4;                            	// 背包物品类型
	optional int32 BackPackPos = 5;                         	// 物品在背包里的格子位置
	optional int64 Num = 6;                                 	// 物品数量
}

//查询啾灵世界玩家道具响应
message QueryStarPPlayerItemsRsp{
	optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;                                 // 返回消息
	repeated rStarPPlayerItem StarPPlayerItemList = 3;			// 道具信息
}

//查询啾灵世界玩家简略数据请求
message QueryStarPPlayerSimpleInfoReq{
	option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (queryMsg) = true;
	optional uint32 AreaId = 1;                           		// 大区（2-手Q）
	optional uint32 PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32 Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional int64	StarPId = 4;						  		// 啾灵世界Id
	optional int64  Uid = 5;                                    // 玩家uid
}

//查询啾灵世界玩家简略数据响应
message QueryStarPPlayerSimpleInfoRsp{
	optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
    optional int64 RegistTime = 3;								//注册时间
    optional int64 GuildId = 4;									//公会Id
    optional int32 TerminalLevel = 5;							//终端等级
}
// 查询工会信息
message QueryStarPGuildInfoReq {
	option (handler) = IH_IdipSvr;
    option (hacked) = true;
    optional int64 StarPId = 1;                                 // 啾灵世界ID
    optional int64 GuildId = 2;                                 // 工会Id
}

//查询工会信息响应
message QueryStarPGuildInfoRsp {
    message sPDsGuildMemberDBUserData {
		optional int64 Uid = 1;									//玩家uid
		optional string Name = 2;								//玩家名称
		optional int32 Status = 3;								//玩家名称
		optional int64 Title = 4;								//公会职务
		optional int64 JoinTime = 5;							//记录玩家加入公会的时间
		optional int64 LastActiveTime = 6;						//记录玩家在啾灵玩法内的上次活跃时间
		optional int32 WeeklyApplyCount = 7;					//记录本周申请加入公会的次数
		optional int64 LastApplyTime = 8;						//记录上次申请加入公会的时间
    }

	message sPDsGuildTerminalKey {
		optional int64 buildingUId = 1;
	}
	
    message sPDsGuildTerminalData {
		repeated sPDsGuildTerminalKey Terminals = 1;
		repeated int64 Terminals_deleted = 2001;
		optional bool Terminals_is_cleared = 4001;
    }

	message sPDsGuildApplication {
		optional int64 ApplierUid = 1;							//申请玩家ID
		optional int64 ApplyTime = 2;							//申请时间
		optional int64 OperatorUid = 3;							//处理人
		optional int64 OperateTime = 4;							//处理时间
		optional int32 Result = 5;								//审批结果
	}

	message sPDsGuildApplicationData {
		repeated sPDsGuildApplication Applications = 1;
		repeated int64 Applications_deleted = 2001;
		optional bool Applications_is_cleared = 4001;
	}

	message sPDsGuildInvitation {
		optional int32 Id = 1;
		optional int64 InviteeUid = 2;							//收到邀请的玩家ID
		optional int64 InviterUid = 3;							//发出邀请的玩家ID
		optional int64 InviteTime = 4;							//邀请时间
	}

	message sPDsGuildInvitationData {
		repeated sPDsGuildInvitation Invitations = 1;
		repeated int32 Invitations_deleted = 2001;
		optional bool Invitations_is_cleared = 4001;
	}
	
	message starPGuildData {
		optional int64 GuildId = 1;								//公会ID
		optional string GuildName = 2; 							//公会名
		optional int32 GuildType = 3; 							//公会类型
		optional int32 GuildStatus = 4; 						//公会状态
		optional int64 CreateTime = 5; 							//公会创建时间
		repeated sPDsGuildMemberDBUserData Members = 6;			//成员列表
		repeated int64 Members_deleted = 2006; 					//删除者
		optional bool Members_is_cleared = 4006; 				//Members_is_cleared
		optional int32 TerminalLevel = 7;						//终端等级
		optional sPDsGuildTerminalData TerminalData = 8; 		//终端数据
	    optional sPDsGuildApplicationData ApplicationData = 9;	//申请数据
	    optional sPDsGuildInvitationData InvitationData = 10; 	//邀请数据
		repeated int64 Member = 98; 							//玩家uid列表
		optional bool Member_is_Cleared = 2098; 				//是否是创建者
		optional int32 StrongPointLevel = 99;					//终端等级
	}
	optional int32 Result = 1;                                  // 结果（0）成功 ， (1) 查找失败
	optional int64 StarPId = 2;                                 // 啾灵世界ID
    optional int64 GuildId = 3;                                 // 工会Id
    optional int64 LastUpdateTime = 4;                          // 更新时间 
	optional starPGuildData GuildData=5;                        // 工会数据
}


// 查询啾灵世界玩家属性请求
message QueryStarPPlayerRoleAttrReq{
	option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (queryMsg) = true;
	optional uint32 AreaId = 1;                           		// 大区（2-手Q）
	optional uint32 PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32 Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional int64	StarPId = 4;						  		// 啾灵世界Id
	optional int64  Uid = 5;                                    // 玩家uid
}

// 查询啾灵世界玩家属性响应
message QueryStarPPlayerRoleAttrRsp{
	message starPKeyValue{
		optional int32 AttrKey = 1;
		optional int64 AttrValue = 2;
	}
	optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
	repeated starPKeyValue StarPKeyValueList = 3;
}

//修改starp信息请求
message DoModifyStarPReq{
	option (handler) = IH_IdipSvr;
	option (hacked) = true;
	optional uint32  AreaId = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		// 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional string  OpenId = 4;                                // OpenId
	optional int64	 StarPId = 5;						  		// 啾灵世界Id
	optional int64   Uid = 6 [(field_meta_uuid) = true];        // 玩家uid
	optional int32   ChangeType = 7;							// 类型  IdipStarPModifyType 枚举类型
	optional string  ReviseContent = 8;      					// 修改后内容: 根据枚举类型 :号去分割参数
}

//修改starp信息响应
message DoModifyStarPRsp{
	optional int32 Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//添加starp邮件请求
message DoStarPAddMailReq{
	option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (checkUserInfo) = true;
	optional uint32  AreaId    = 1;                           		// 大区（2-手Q）
	optional uint32  PlatId    = 2;                           		// 平台（1-安卓，0-ios）
	optional uint32  Partition = 3;                        		    // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
	optional string  OpenId    = 4;                                 // OpenId
	optional int64	 StarPId   = 5;						  		    // 啾灵世界Id
	optional int64   Uid       = 6 [(field_meta_uuid) = true];      // 玩家uid
	optional int32	 MailType  = 7;								    // 邮件类型
	optional string  Sender    = 8;									// 发件人
	optional int32   ExpireDay = 9;									// 过期天数
	optional int32   ItemId    = 10;								// 道具配置id
	optional int32   ItemNum   = 11;								// 道具数量
	optional string  HintId    = 12;								// 幂等性检测(防止重复发送)
	optional string  Title     = 13;								// 标题
	optional string  SPContent   = 14;								// 内容
  optional int64   RoleId    = 15;                // 角色Id
  optional int32   MailScope = 16;                // 邮件范围：1 账号范围内； 2 角色范围内； 3 房间范围内
}

//添加starp邮件响应
message DoStarPAddMailRsp{
	optional int32  Result = 1;                                  // 结果（0）成功，（1）无角色，（其他）失败
	optional string RetMsg = 2;
}

//DebugDs修改数据请求
message QueryStarPItemInfosReq{
   	option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (queryMsg) = true;
    optional int64 Uid    = 1;                                    // 玩家uid
}

message AqDoModifyDebugDsReq{
  option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (queryMsg) = true;
  optional int64 Uid = 1;                                    // 玩家uid
  optional string DsSessionId = 2;
  optional int64 DsaInstanceId = 3;
  optional int64 StarpId = 4;
  optional string Para = 5;                                  // json串
}

message AqDoModifyDebugDsRsp{
  option (handler) = IH_IdipSvr;
	option (hacked) = true;
	option (queryMsg) = true;
  optional int64 Uid = 1;                                    // 玩家uid
  optional string RetMsg = 2;
}

message QueryStarPItemInfosRsp{
    message StarPItem {
        optional int64 BackpackId = 1;  
        optional int32 Grid = 2;                                     //物品在背包里的格子位置
        optional int64 ItemNum = 3;                                  //物品数量
        optional int64 ObtainTime = 4;                               //获得时间
        optional int32 ItemType = 5;                                 //背包物品类型
        optional int32 IsLock = 6;                                   //是否锁定
        optional int64 BindPlayerUid = 7;                            //绑定的玩家UI
        optional int64 ItemID        = 8;                            //物品ID
        optional int32 BackPackType  = 9;                            //背包类型
        optional int64 ItemInstID    = 10;                           //唯一ID
    }

    message StarPItems {
        optional  int32     ItemType     = 1;                           //物品类型
        repeated  StarPItem ItemsInfo    = 2;                           //物品信息  
    }
 
    message StarpWorld {
        optional int64 WroldID    = 1;                           // 世界ID
        repeated StarPItems Items  = 2;                          // 物品
    }

    optional int64 Uid = 1;                                      // 啾灵世界ID
    repeated StarpWorld Worlds = 2;                              // 世界信息
}

message DoFarmCookUpdateExtendInfoReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)

  optional int32 LastExtendLevel = 8;                               // 上次扩建等级
  optional int32 MaxExtendLevel = 9;                               // 最大扩建等级
}
message DoFarmCookUpdateExtendInfoRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
}

message QueryFarmCookQueryExtendInfoReq {
  option (handler) = IH_FarmSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)
}
message QueryFarmCookQueryExtendInfoRsp {
  optional int32 Result = 1;                                // 结果（0-成功，其他-失败）
  optional string RetMsg = 2;                               // 返回消息
  optional int32 ExtendCount = 3;                                  // 当前扩建次数
  optional int32 TmpExtendCount = 4;                               // 临时扩建次数
  optional int32 LastExtendLevel = 5;                              // 上次扩建等级
  optional int32 MaxExtendLevel = 6;                               // 最大扩建等级
}

// 查询转平台功能前置条件请求
message QueryAccountTransferPreConditionReq {
  option (handler) = IH_IdipSvr;
  option (hacked) = true;
  option (queryMsg) = true;
  optional uint32 AreaId = 1;                                 // 大区（2-手Q）
  optional uint32 PlatId = 2;                                 // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                                 // OpenId
  optional int64 Uid = 4 [(field_meta_uuid) = true];          // Uid
}

// 查询转平台功能前置条件响应
message QueryAccountTransferPreConditionRsp {
  optional int32 Result = 1;                                  // 结果
  optional string RetMsg = 2;                                 // 返回消息
  optional int32 Switch = 3;                                  // 总开关（1-开启，0-关闭）
  optional int32 TransferStatus = 4;                          // 转区状态（0-未转区，1-转区中，2-转区成功）
}

// 重置玩家痛包装饰请求(安全)
message AqDoPlayerItaBagResetReq {
  option (handler) = IH_GameSvr;
  option (hacked) = true;
  option (checkUserInfo) = true;
  optional uint32 AreaId = 1;                               // 大区（2-手Q）
  optional uint32 PlatId = 2;                               // 平台（1-安卓，0-ios）
  optional string OpenId = 3;                               // OpenId
  optional int64 Uid = 4 [(field_hash_key) = true];                                   // Uid
  optional uint32 Source = 5;                               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                               // 流水号
  optional uint32 Partition = 7;                           // 小区ID, 用于路由转发使用(取值定义为idip正式环境的area)

  optional int32 ItemId = 8;                               // 痛包道具ID
}

// 重置玩家痛包装饰响应
message AqDoPlayerItaBagResetRsp {
  optional int32 Result = 1;                                  // 结果
  optional string RetMsg = 2;                                 // 返回消息
}
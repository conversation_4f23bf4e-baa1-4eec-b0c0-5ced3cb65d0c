syntax = "proto2";

package com.tencent.wea.protocol;

option java_multiple_files = true;
option java_package = "com.tencent.wea.protocol.common";

//IDIP命令编码
enum NetCmdId {
  NCI_IDIP_INVALID_CMD = 0;
  NCI_IDIP_QUERY_PERSONAL_INFO_REQ = 4097;                      // 查询玩家个人信息请求
  NCI_IDIP_QUERY_PERSONAL_INFO_RSP = 4098;                      // 查询玩家个人信息响应
  NCI_IDIP_SEND_PERSONAL_MAIL_REQ = 4099;                       // 发送邮件请求
  NCI_IDIP_SEND_PERSONAL_MAIL_RSP = 4100;                       // 发送邮件响应
  NCI_IDIP_QUERY_PLAYER_ITEM_LIST_REQ = 4101;                   // 获取玩家道具列表请求
  NCI_IDIP_QUERY_PLAYER_ITEM_LIST_RSP = 4102;                   // 获取玩家道具列表响应
  NCI_IDIP_DO_MODIFY_ITEM_REQ = 4103;                           // 修改道具请求
  NCI_IDIP_DO_MODIFY_ITEM_RSP = 4104;                           // 修改道具响应
  NCI_IDIP_DO_MODIFY_TASK_PROGRESS_REQ = 4105;                  // 修改任务进度请求
  NCI_IDIP_DO_MODIFY_TASK_PROGRESS_RSP = 4106;                  // 修改任务进度响应
  NCI_IDIP_DO_MODIFY_BP_DATA_REQ = 4107;                        // 修改bp数据请求
  NCI_IDIP_DO_MODIFY_BP_DATA_RSP = 4108;                        // 修改bp数据响应
  NCI_IDIP_DEL_PERSONAL_MAIL_REQ = 4109;                        // 删除邮件请求
  NCI_IDIP_DEL_PERSONAL_MAIL_RSP = 4110;                        // 删除邮件响应
  NCI_IDIP_DO_SECURITY_PUNISH_REQ = 4111;                       // 安全处罚请求
  NCI_IDIP_DO_SECURITY_PUNISH_RSP = 4112;                       // 安全处罚响应
  NCI_IDIP_DO_MODIFY_PLAYER_INFO_REQ = 4113;                    // 修改玩家信息请求
  NCI_IDIP_DO_MODIFY_PLAYER_INFO_RSP = 4114;                    // 修改玩家信息响应
  NCI_IDIP_DO_REMOVE_UGC_MAP_REQ = 4115;                        // 下架ugc地图请求
  NCI_IDIP_DO_REMOVE_UGC_MAP_RSP = 4116;                        // 下架ugc地图响应
  NCI_IDIP_AUDIT_INFO_CALLBACK_REQ = 4117;                      // 审核结果回调请求
  NCI_IDIP_AUDIT_INFO_CALLBACK_RSP = 4118;                      // 审核结果回调响应
  NCI_IDIP_SEND_GLOBAL_MAIL_REQ = 4119;                         // 发送全服邮件请求
  NCI_IDIP_SEND_GLOBAL_MAIL_RSP = 4120;                         // 发送全服邮件响应
  NCI_IDIP_QUERY_PLAYER_STAT_INFO_REQ = 4121;                   // 查询玩家统计信息请求
  NCI_IDIP_QUERY_PLAYER_STAT_INFO_RSP = 4122;                   // 查询玩家统计信息响应
  NCI_IDIP_DO_SECURITY_PUNISH_REMOVE_REQ = 4123;                // 安全处罚解除请求
  NCI_IDIP_DO_SECURITY_PUNISH_REMOVE_RSP = 4124;                // 安全处罚解除响应
  NCI_IDIP_DO_MAP_STAGE_RESET_REQ = 4125;                       // 地图阶段重置请求
  NCI_IDIP_DO_MAP_STAGE_RESET_RSP = 4126;                       // 地图阶段重置响应
  NCI_IDIP_SEND_PERSONAL_TEXT_MAIL_REQ = 4127;                  // 发送纯文本邮件请求
  NCI_IDIP_SEND_PERSONAL_TEXT_MAIL_RSP = 4128;                  // 发送纯文本邮件响应
  NCI_IDIP_DO_MODIFY_PLAYER_REGION_REQ = 4129;                  // 修改玩家地区请求
  NCI_IDIP_DO_MODIFY_PLAYER_REGION_RSP = 4130;                  // 修改玩家地区响应
  NCI_IDIP_DO_KICK_PLAYER_REQ = 4131;                           // 踢人请求
  NCI_IDIP_DO_KICK_PLAYER_RSP = 4132;                           // 踢人响应
  NCI_IDIP_DO_MODIFY_ROOM_INFO_REQ = 4133;                      // 修改房间信息请求
  NCI_IDIP_DO_MODIFY_ROOM_INFO_RSP = 4134;                      // 修改房间信息响应
  NCI_IDIP_DO_DISBAND_ROOM_REQ = 4135;                          // 解散房间请求
  NCI_IDIP_DO_DISBAND_ROOM_RSP = 4136;                          // 解散房间响应
  NCI_IDIP_DO_MODIFY_MAP_INFO_REQ = 4137;                       // 修改地图信息请求
  NCI_IDIP_DO_MODIFY_MAP_INFO_RSP = 4138;                       // 修改地图信息响应
  NCI_IDIP_DO_GLOBAL_BAN_PROCESS_REQ = 4139;                    // 全服封禁请求(海外业务)
  NCI_IDIP_DO_GLOBAL_BAN_PROCESS_RSP = 4140;                    // 全服封禁响应(海外业务)
  NCI_IDIP_DO_CLEAN_USER_CHAT_REQ = 4141;                       // 清理用户聊天请求
  NCI_IDIP_DO_CLEAN_USER_CHAT_RSP = 4142;                       // 清理用户聊天响应
  NCI_IDIP_DO_MODIFY_FRIEND_RELATION_REQ = 4143;                // 修改好友亲密度请求
  NCI_IDIP_DO_MODIFY_FRIEND_RELATION_RSP = 4144;                // 修改好友亲密度响应
  NCI_IDIP_DO_MODIFY_PLAYER_VIP_EXP_REQ = 4145;                 // 修改玩家vip经验请求
  NCI_IDIP_DO_MODIFY_PLAYER_VIP_EXP_RSP = 4146;                 // 修改玩家vip经验响应
  NCI_IDIP_DO_MODIFY_PLAYER_MONTH_CARD_REQ = 4147;              // 修改玩家月卡请求
  NCI_IDIP_DO_MODIFY_PLAYER_MONTH_CARD_RSP = 4148;              // 修改玩家月卡响应
  NCI_IDIP_DO_SET_USER_LABEL_REQ = 4149;                        // 设置用户标签请求
  NCI_IDIP_DO_SET_USER_LABEL_RSP = 4150;                        // 设置用户标签响应
  NCI_IDIP_QUERY_PERSONAL_OPENID_REQ = 4151;                    // 根据UID查询openid请求
  NCI_IDIP_QUERY_PERSONAL_OPENID_RSP = 4152;                    // 根据UID查询openid响应
  NCI_IDIP_SEND_GLOBAL_TEXT_MAIL_REQ = 4153;                    // 发送全服纯文本邮件接口请求(海外业务)
  NCI_IDIP_SEND_GLOBAL_TEXT_MAIL_RSP = 4154;                    // 发送全服纯文本邮件接口响应(海外业务)
  NCI_IDIP_DO_GLOBAL_SVR_KICK_REQ = 4155;                       // 全服踢人下线请求(海外业务)
  NCI_IDIP_DO_GLOBAL_SVR_KICK_RSP = 4156;                       // 全服踢人下线响应(海外业务)
  NCI_IDIP_DO_GUIDE_TASK_PROCESS_REQ = 4157;                    // 新手引导任务处理请求
  NCI_IDIP_DO_GUIDE_TASK_PROCESS_RSP = 4158;                    // 新手引导任务处理响应
  NCI_IDIP_ACCOUNT_CANCEL_CALLBACK_REQ = 4159;                  // 账号注销回调请求
  NCI_IDIP_ACCOUNT_CANCEL_CALLBACK_RSP = 4160;                  // 账号注销回调响应
  NCI_IDIP_ACCOUNT_STATE_CHANGE_CALLBACK_REQ = 4161;            // 账号状态变化回调请求
  NCI_IDIP_ACCOUNT_STATE_CHANGE_CALLBACK_RSP = 4162;            // 账号状态变化回调响应
  NCI_IDIP_AQ_DO_XIAO_WO_REPORT_SAFE_AUDIT_RESULT_REQ = 4163;   // 审核信息回调请求(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_REPORT_SAFE_AUDIT_RESULT_RSP = 4164;   // 审核信息回调应答(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_SET_HOT_REQ = 4165;                    // 家园降权请求(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_SET_HOT_RSP = 4166;                    // 家园降权应答(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_BLOCK_REQ = 4167;                      // 屏蔽家园请求(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_BLOCK_RSP = 4168;                      // 屏蔽家园应答(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_BLOCK_CANCEL_REQ = 4169;               // 解除屏蔽家园请求(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_BLOCK_CANCEL_RSP = 4170;               // 解除屏蔽家园应答(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_CLEAR_INSTRUCTION_AND_IMAGE_REQ = 4171;// 重置家园描述和家园预览图请求(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_CLEAR_INSTRUCTION_AND_IMAGE_RSP = 4172;// 重置家园描述和家园预览图应答(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_PUT_DOWN_REQ = 4173;                   // 下架家园请求(小窝业务)
  NCI_IDIP_AQ_DO_XIAO_WO_PUT_DOWN_RSP = 4174;                   // 下架家园应答(小窝业务)
  NCI_IDIP_DIR_ADD_IP_BLACK_INFO_REQ = 4175;                    // Dir服上把ip加入黑名单请求
  NCI_IDIP_DIR_ADD_IP_BLACK_INFO_RSP = 4176;                    // Dir服上把ip加入黑名单响应
  NCI_IDIP_DIR_REMOVE_IP_BLACK_INFO_REQ = 4177;                 // Dir服上把ip从黑名单移除请求
  NCI_IDIP_DIR_REMOVE_IP_BLACK_INFO_RSP = 4178;                 // Dir服上把ip从黑名单移除响应
  NCI_IDIP_DIR_GET_IP_BLACK_INFO_REQ = 4179;                    // Dir服上获取ip黑名单请求
  NCI_IDIP_DIR_GET_IP_BLACK_INFO_RSP = 4180;                    // Dir服上获取ip黑名单响应
  NCI_IDIP_DIR_GET_IP_BLACK_INFO_LIST_REQ = 4181;               // Dir服上获取ip黑名单列表请求
  NCI_IDIP_DIR_GET_IP_BLACK_INFO_LIST_RSP = 4182;               // Dir服上获取ip黑名单列表响应
  NCI_IDIP_SWITCH_PLAY_STATUS_SYNCHRONIZE_REQ = 4183;           // 开关播状态同步请求
  NCI_IDIP_SWITCH_PLAY_STATUS_SYNCHRONIZE_RSP = 4184;           // 开关播状态同步响应
  NCI_IDIP_QUERY_LIVE_HEARTBEAT_REQ = 4185;                     // 直播心跳查询请求
  NCI_IDIP_QUERY_LIVE_HEARTBEAT_RSP = 4186;                     // 直播心跳查询响应
  NCI_IDIP_QUERY_ROOM_USER_INFO_REQ = 4187;                     // 查询队伍成员信息请求
  NCI_IDIP_QUERY_ROOM_USER_INFO_RSP = 4188;                     // 查询队伍成员信息响应
  NCI_IDIP_OPEN_SUPER_LUCKY_MONEY_REQ = 4189;                   // 打开超级红包请求
  NCI_IDIP_OPEN_SUPER_LUCKY_MONEY_RSP = 4190;                   // 打开超级红包响应
  NCI_IDIP_QUERY_SUPER_LUCKY_MONEY_INFO_REQ = 4191;             // 查询超级红包信息请求
  NCI_IDIP_QUERY_SUPER_LUCKY_MONEY_INFO_RSP = 4192;             // 查询超级红包信息响应
  NCI_IDIP_CLICK_ACTIVITY_OUTER_LINK_REQ = 4193;                // 点击活动分享外链请求
  NCI_IDIP_CLICK_ACTIVITY_OUTER_LINK_RSP = 4194;                // 点击活动分享外链响应
  NCI_IDIP_BAN_DEVICE_ID_REQ = 4195;                            // 封禁设备接口请求
  NCI_IDIP_BAN_DEVICE_ID_RSP = 4196;                            // 封禁设备接口应答
  NCI_IDIP_UNBAN_DEVICE_ID_REQ = 4197;                          // 解除封禁设备接口请求
  NCI_IDIP_UNBAN_DEVICE_ID_RSP = 4198;                          // 解除封禁设备接口应答
  NCI_IDIP_SET_PLAYER_DS_RECORD_INFO_REQ = 4199;                // 设置玩家开启局内录像请求
  NCI_IDIP_SET_PLAYER_DS_RECORD_INFO_RSP = 4200;                // 设置玩家开启局内录像响应
  NCI_IDIP_BAN_GAME_MODE_REQ = 4201;                            // 禁止玩法接口请求
  NCI_IDIP_BAN_GAME_MODE_RSP = 4202;                            // 禁止玩法接口应答
  NCI_IDIP_UNBAN_GAME_MODE_REQ = 4203;                          // 解除禁止玩法接口请求
  NCI_IDIP_UNBAN_GAME_MODE_RSP = 4204;                          // 解除禁止玩法接口应答
  NCI_IDIP_QUERY_NEW_PLAYER_STAT_INFO_REQ = 4205;               // 新查询玩家统计信息请求
  NCI_IDIP_QUERY_NEW_PLAYER_STAT_INFO_RSP = 4206;               // 新查询玩家统计信息应答
  NCI_IDIP_QUERY_PLAYER_BASIC_INFO_REQ = 4207;                  // 查询玩家基础信息请求
  NCI_IDIP_QUERY_PLAYER_BASIC_INFO_RSP = 4208;                  // 查询玩家基础信息响应
  NCI_IDIP_QUERY_INVITED_FRIEND_INFO_REQ = 4209;                // 查询邀请好友信息请求
  NCI_IDIP_QUERY_INVITED_FRIEND_INFO_RSP = 4210;                // 查询邀请好友信息响应
  NCI_IDIP_CREATOR_TEAM_ACTIVITY_REQ = 4211;                    // 造梦星探查询地图信息请求
  NCI_IDIP_CREATOR_TEAM_ACTIVITY_RSP = 4212;                    // 造梦星探查询地图信息响应
  NCI_IDIP_CREATOR_TEAM_ACTIVITY_RANK_REQ = 4213;               // 造梦星探查询小队信息请求
  NCI_IDIP_CREATOR_TEAM_ACTIVITY_RANK_RSP = 4214;               // 造梦星探查询小队信息响应
  NCI_IDIP_SET_BLACK_INDUSTRY_USER_REQ = 4215;                  // 标记黑产玩家请求
  NCI_IDIP_SET_BLACK_INDUSTRY_USER_RSP = 4216;                  // 标记黑产玩家响应
  NCI_IDIP_AQ_DO_DEL_YUAN_JIAN_BIAO_QIAN_REQ = 4217;
  NCI_IDIP_AQ_DO_DEL_YUAN_JIAN_BIAO_QIAN_RSP = 4218;
  NCI_IDIP_DO_DELETE_UGC_MAP_TOPIC_REQ = 4219;                  // 删除指定地图的话题请求
  NCI_IDIP_DO_DELETE_UGC_MAP_TOPIC_RSP = 4220;                  // 删除指定地图的话题应答
  NCI_IDIP_SEND_PERSONAL_MAIL_WITH_ATTACH_EXPIRE_REQ = 4221;    // 发送带有效期的附件邮件请求
  NCI_IDIP_SEND_PERSONAL_MAIL_WITH_ATTACH_EXPIRE_RSP = 4222;    // 发送带有效期的附件邮件响应
  NCI_IDIP_AQ_QUERY_XIAO_WO_GET_LAYOUT_LIST_REQ = 4223;         // 查询家园方案列表请求
  NCI_IDIP_AQ_QUERY_XIAO_WO_GET_LAYOUT_LIST_RSP = 4224;         // 查询家园方案列表应答
  NCI_IDIP_AQ_DO_XIAO_WO_DEL_LAYOUT_REQ = 4225;                 // 删除家园方案请求
  NCI_IDIP_AQ_DO_XIAO_WO_DEL_LAYOUT_RSP = 4226;                 // 删除家园方案应答
  NCI_IDIP_AQ_DO_XIAO_WO_RESET_MAP_REQ = 4227;                  // 重置家园地图请求
  NCI_IDIP_AQ_DO_XIAO_WO_RESET_MAP_RSP = 4228;                  // 重置家园地图响应
  NCI_IDIP_GET_RES_TABLE_DATA_REQ = 4229;                       // 配置表数据请求
  NCI_IDIP_GET_RES_TABLE_DATA_RSP = 4230;                       // 配置表数据响应
  NCI_IDIP_QUERY_COMMON_PLAYER_DATA_BY_DATAMORE_REQ = 4231;     // 基于datamore通用查询用户信息请求
  NCI_IDIP_QUERY_COMMON_PLAYER_DATA_BY_DATAMORE_RSP = 4232;     // 基于datamore通用查询用户信息响应
  NCI_IDIP_CONFIRM_RECRUITE_ORDER_REQ = 4233;                   // 回应召集令请求
  NCI_IDIP_CONFIRM_RECRUITE_ORDER_RSP = 4234;                   // 回应召集令响应
  NCI_IDIP_DO_MODIFY_PLAYER_RANK_SCORE_REQ = 4235;              // 修改玩家段位分（同时修改排行榜）请求
  NCI_IDIP_DO_MODIFY_PLAYER_RANK_SCORE_RSP = 4236;              // 修改玩家段位分（同时修改排行榜）响应
  NCI_IDIP_DO_MODIFY_PLAYER_LEVEL_SCORE_REQ = 4237;             // 修改玩家关卡分数或时长（同时修改排行榜）请求
  NCI_IDIP_DO_MODIFY_PLAYER_LEVEL_SCORE_RSP = 4238;             // 修改玩家关卡分数或时长（同时修改排行榜）响应
  NCI_IDIP_QUERY_RANK_INFO_REQ = 4239;                          // 查询排行榜信息请求
  NCI_IDIP_QUERY_RANK_INFO_RSP = 4240;                          // 查询排行榜信息响应
  NCI_IDIP_AQ_DO_PLAYER_ADD_WARM_ROUND_REQ = 4241;              // 增加一轮温暖局请求
  NCI_IDIP_AQ_DO_PLAYER_ADD_WARM_ROUND_RSP = 4242;              // 增加一轮温暖局响应
  NCI_IDIP_MATCH_ISOLATE_REQ = 4243;                            // 匹配隔离接口请求
  NCI_IDIP_MATCH_ISOLATE_RSP = 4244;                            // 匹配隔离接口响应
  NCI_IDIP_SEND_ROLLING_NOTICE_INFO_REQ = 4245;                 // 发送滚动公告信息请求
  NCI_IDIP_SEND_ROLLING_NOTICE_INFO_RSP = 4246;                 // 发送滚动公告信息响应
  NCI_IDIP_QUERY_ROLLING_NOTICE_INFO_LIST_REQ = 4247;           // 查询滚动公告信息列表请求
  NCI_IDIP_QUERY_ROLLING_NOTICE_INFO_LIST_RSP = 4248;           // 查询滚动公告信息列表响应
  NCI_IDIP_OFFLINE_ROLLING_NOTICE_INFO_REQ = 4249;              // 下线滚动公告信息请求
  NCI_IDIP_OFFLINE_ROLLING_NOTICE_INFO_RSP = 4250;              // 下线滚动公告信息响应
  NCI_IDIP_MODIFY_CLUB_NAME_REQ = 4251;                         // 修改社团名请求
  NCI_IDIP_MODIFY_CLUB_NAME_RSP = 4252;                         // 修改社团名响应
  NCI_IDIP_MODIFY_CLUB_BRIEF_REQ = 4253;                        // 修改社团宣言请求
  NCI_IDIP_MODIFY_CLUB_BRIEF_RSP = 4254;                        // 修改社团宣言响应
  NCI_IDIP_AQ_DO_XIAO_WO_WELCOME_CLEAR_REQ = 4255;              // 清空当前家园欢迎词请求
  NCI_IDIP_AQ_DO_XIAO_WO_WELCOME_CLEAR_RSP = 4256;              // 清空当前家园欢迎词应答
  NCI_IDIP_AQ_DO_XIAO_WO_WELCOME_BAN_REQ = 4257;                // 禁止设置家园欢迎词请求
  NCI_IDIP_AQ_DO_XIAO_WO_WELCOME_BAN_RSP = 4258;                // 禁止设置家园欢迎词应答
  NCI_IDIP_AQ_DO_XIAO_WO_BULLETIN_DELETE_REQ = 4259;            // 删除家园留言/回复请求
  NCI_IDIP_AQ_DO_XIAO_WO_BULLETIN_DELETE_RSP = 4260;            // 删除家园留言/回复应答
  NCI_IDIP_AQ_DO_XIAO_WO_BULLETIN_BAN_REQ = 4261;               // 禁止家园留言/回复请求
  NCI_IDIP_AQ_DO_XIAO_WO_BULLETIN_BAN_RSP = 4262;               // 禁止家园留言/回复应答
  NCI_IDIP_DO_DELETE_ACTIVITY_MAP_WISH_REQ = 4263;              // 删除活动地图许愿请求
  NCI_IDIP_DO_DELETE_ACTIVITY_MAP_WISH_RSP = 4264;              // 删除活动地图许愿响应
  NCI_IDIP_LUCKY_STAR_GET_HANDBOOK_INFO_REQ = 4265;             // 查询福星手账簿信息请求
  NCI_IDIP_LUCKY_STAR_GET_HANDBOOK_INFO_RSP = 4266;             // 查询福星手账簿信息应答
  NCI_IDIP_LUCKY_STAR_GET_GIVE_INFO_REQ = 4267;                 // 查询福星卡赠送信息请求
  NCI_IDIP_LUCKY_STAR_GET_GIVE_INFO_RSP = 4268;                 // 查询福星卡赠送信息应答
  NCI_IDIP_LUCKY_STAR_RECEIVE_GIVE_STAR_REQ = 4269;             // 领取赠送的福星卡请求
  NCI_IDIP_LUCKY_STAR_RECEIVE_GIVE_STAR_RSP = 4270;             // 领取赠送的福星卡应答
  NCI_IDIP_DO_UGC_MAP_HANDLER_REQ = 4271;                       // Ugc地图处理请求
  NCI_IDIP_DO_UGC_MAP_HANDLER_RSP = 4272;                       // Ugc地图处理响应
  NCI_IDIP_SPRING_H5_GET_CLUB_INFO_REQ = 4273;                  // 春节裂变H5获取指定社团信息请求
  NCI_IDIP_SPRING_H5_GET_CLUB_INFO_RSP = 4274;                  // 春节裂变H5获取指定社团信息响应
  NCI_IDIP_SPRING_H5_GET_MY_CLUB_INFO_REQ = 4275;               // 春节裂变H5获取玩家所在社团信息请求
  NCI_IDIP_SPRING_H5_GET_MY_CLUB_INFO_RSP = 4276;               // 春节裂变H5获取玩家所在社团信息响应
  NCI_IDIP_SPRING_H5_BATCH_RANDOM_CLUB_INFO_REQ = 4277;         // 春节裂变H5获取随机一批社团信息请求
  NCI_IDIP_SPRING_H5_BATCH_RANDOM_CLUB_INFO_RSP = 4278;         // 春节裂变H5获取随机一批社团信息响应
  NCI_IDIP_AQ_DO_XIAO_WO_WELCOME_SET_FREE_REQ = 4279;           // 解除禁止设置家园欢迎词请求
  NCI_IDIP_AQ_DO_XIAO_WO_WELCOME_SET_FREE_RSP = 4280;           // 解除禁止设置家园欢迎词应答
  NCI_IDIP_AQ_DO_XIAO_WO_BULLETIN_SEND_FREE_REQ = 4281;         // 解除禁止家园留言/回复应答
  NCI_IDIP_AQ_DO_XIAO_WO_BULLETIN_SEND_FREE_RSP = 4282;         // 解除禁止家园留言/回复应答
  NCI_IDIP_QUERY_XIAO_WO_GET_BASICINFO_REQ = 4283;              // 查询家园基本信息请求
  NCI_IDIP_QUERY_XIAO_WO_GET_BASICINFO_RSP = 4284;              // 查询家园基本信息响应
  NCI_IDIP_OFF_END_CONSUME_NOTIFY_REQ = 4285;                   // 端外消费通知请求
  NCI_IDIP_OFF_END_CONSUME_NOTIFY_RSP = 4286;                   // 端外消费通知响应
  NCI_IDIP_AQ_DO_MAP_NAME_REQ = 4287;                           // 修改地图合集名称请求
  NCI_IDIP_AQ_DO_MAP_NAME_RSP = 4288;                           // 修改地图合集名称响应
  NCI_IDIP_AQ_DO_MAP_COLLECTION_BLURB_REQ = 4289;               // 修改地图合集简介请求
  NCI_IDIP_AQ_DO_MAP_COLLECTION_BLURB_RSP = 4290;               // 修改地图合集简介响应
  NCI_IDIP_QUERY_PLAYER_FRIEND_LIST_REQ = 4291;                 // 查询玩家好友列表请求
  NCI_IDIP_QUERY_PLAYER_FRIEND_LIST_RSP = 4292;                 // 查询玩家好友列表响应
  NCI_IDIP_SEND_ITEM_DATA_INTO_BAG_REQ = 4293;                  // 发送道具直接进背包请求
  NCI_IDIP_SEND_ITEM_DATA_INTO_BAG_RSP = 4294;                  // 发送道具直接进背包响应
  NCI_IDIP_GET_KUNG_FU_PANDA_HELP_RACING_INFO_REQ = 4295;       // 查询好友功夫熊猫助力信息请求
  NCI_IDIP_GET_KUNG_FU_PANDA_HELP_RACING_INFO_RSP = 4296;       // 查询好友功夫熊猫助力信息响应
  NCI_IDIP_HELP_KUNG_FU_PANDA_RACING_REQ = 4297;                // 好友助力功夫熊猫请求
  NCI_IDIP_HELP_KUNG_FU_PANDA_RACING_RSP = 4298;                // 好友助力功夫熊猫响应
  NCI_IDIP_QUERY_XIAO_WO_GET_CREATOR_LAYOUT_REQ = 4299;         // 查询家园方案创作家列表请求
  NCI_IDIP_QUERY_XIAO_WO_GET_CREATOR_LAYOUT_RSP = 4300;         // 查询家园方案列表应答
  NCI_IDIP_DO_XIAO_WO_PUB_CREATORLAYOUT_REQ = 4301;             // 发布创作家方案请求
  NCI_IDIP_DO_XIAO_WO_PUB_CREATORLAYOUT_RSP = 4302;             // 发布创作家方案应答
  NCI_IDIP_DO_XIAO_WO_DEL_CREATORLAYOUT_REQ = 4303;             // 删除创作家方案请求
  NCI_IDIP_DO_XIAO_WO_DEL_CREATORLAYOUT_RSP = 4304;             // 删除创作家方案应答
  NCI_IDIP_QUERY_FARM_GET_ITEM_LIST_REQ = 4305;                 // 查询农场道具请求
  NCI_IDIP_QUERY_FARM_GET_ITEM_LIST_RSP = 4306;                 // 查询农场道具应答
  NCI_IDIP_DO_FARM_ADD_ITEM_REQ = 4307;                         // 增加农场道具请求
  NCI_IDIP_DO_FARM_ADD_ITEM_RSP = 4308;                         // 增加农场道具应答
  NCI_IDIP_DO_FARM_DEL_ITEM_REQ = 4309;                         // 删除农场道具请求
  NCI_IDIP_DO_FARM_DEL_ITEM_RSP = 4310;                         // 删除农场道具应答
  NCI_IDIP_DO_XIAO_WO_RESET_MAP_TO_VERSION_REQ = 4311;          // 重置家园到某个特定历史版本请求
  NCI_IDIP_DO_XIAO_WO_RESET_MAP_TO_VERSION_RSP = 4312;          // 重置家园到某个特定历史版本应答
  NCI_IDIP_AQ_DO_FARM_PUT_DOWN_REQ = 4313;                      // 下架农场（AQ）请求
  NCI_IDIP_AQ_DO_FARM_PUT_DOWN_RSP = 4314;                      // 下架农场（AQ）应答
  NCI_IDIP_AQ_DO_CLEAR_FARM_REQ = 4315;                         // 清理农场（AQ）请求
  NCI_IDIP_AQ_DO_CLEAR_FARM_RSP = 4316;                         // 清理农场（AQ）应答
  NCI_IDIP_DO_SET_COMMON_USER_LABEL_REQ = 4317;                 // 设置用户标签请求-通用
  NCI_IDIP_DO_SET_COMMON_USER_LABEL_RSP = 4318;                 // 设置用户标签响应-通用
  NCI_IDIP_SET_USER_ACCOUNT_CANCEL_STATE_REQ = 4319;            // 设置用户账号注销状态请求
  NCI_IDIP_SET_USER_ACCOUNT_CANCEL_STATE_RSP = 4320;            // 设置用户账号注销状态响应
  NCI_IDIP_SYNC_GAME_TV_REWARD_STATUS_REQ = 4321;               // 更新电视台奖励信息请求
  NCI_IDIP_SYNC_GAME_TV_REWARD_STATUS_RSP = 4322;               // 更新电视台奖励信息响应
  NCI_IDIP_DO_FARM_ADJUST_BUILDING_LEVEL_REQ = 4323;            // 增减农场建筑等级请求
  NCI_IDIP_DO_FARM_ADJUST_BUILDING_LEVEL_RSP = 4324;            // 增减农场建筑等级应答
  NCI_IDIP_DO_FARM_ADJUST_MAIN_EXP_REQ = 4325;                  // 增减农场经验请求
  NCI_IDIP_DO_FARM_ADJUST_MAIN_EXP_RSP = 4326;                  // 增减农场经验应答
  NCI_IDIP_DO_FARM_ADD_CROP_EXP_REQ = 4327;                     // 农场增加养殖物经验请求
  NCI_IDIP_DO_FARM_ADD_CROP_EXP_RSP = 4328;                     // 农场增加养殖物经验应答
  NCI_IDIP_DISBAND_SQUAD_REQ = 4329;                            // 解散星宝小队（AQ）请求
  NCI_IDIP_DISBAND_SQUAD_RSP = 4330;                            // 解散星宝小队（AQ）应答
  NCI_IDIP_DO_MODIFY_FRIEND_INTIMACY_REQ = 4331;                // 批量修改好友亲密度请求
  NCI_IDIP_DO_MODIFY_FRIEND_INTIMACY_RSP = 4332;                // 批量修改好友亲密度应答
  NCI_IDIP_QUERY_FRIEND_INTIMACY_REQ = 4333;                    // 查询好友亲密度请求
  NCI_IDIP_QUERY_FRIEND_INTIMACY_RSP = 4334;                    // 查询好友亲密度应答

  NCI_IDIP_AQ_DO_FARM_WELCOME_CLEAR_REQ = 4337;                     // 清空当前农场欢迎词请求
  NCI_IDIP_AQ_DO_FARM_WELCOME_CLEAR_RSP = 4338;                     // 清空当前农场欢迎词应答
  NCI_IDIP_AQ_DO_FARM_WELCOME_BAN_REQ = 4339;                       // 禁止设置农场欢迎词请求
  NCI_IDIP_AQ_DO_FARM_WELCOME_BAN_RSP = 4340;                       // 禁止设置农场欢迎词应答
  NCI_IDIP_AQ_DO_FARM_WELCOME_SET_FREE_REQ = 4341;                  // 解除禁止设置农场欢迎词请求
  NCI_IDIP_AQ_DO_FARM_WELCOME_SET_FREE_RSP = 4342;                  // 解除禁止设置农场欢迎词应答
  NCI_IDIP_AQ_DO_FARM_LIU_YAN_MESSAGE_DELETE_REQ = 4343;            // 删除农场留言请求
  NCI_IDIP_AQ_DO_FARM_LIU_YAN_MESSAGE_DELETE_RSP = 4344;            // 删除农场留言应答
  NCI_IDIP_AQ_DO_FARM_LIU_YAN_MESSAGE_BAN_REQ = 4345;               // 禁止农场留言请求
  NCI_IDIP_AQ_DO_FARM_LIU_YAN_MESSAGE_BAN_RSP = 4346;               // 禁止农场留言应答
  NCI_IDIP_AQ_DO_FARM_LIU_YAN_MESSAGE_SEND_FREE_REQ = 4347;         // 解除禁止农场留言应答
  NCI_IDIP_AQ_DO_FARM_LIU_YAN_MESSAGE_SEND_FREE_RSP = 4348;         // 解除禁止农场留言应答
  NCI_IDIP_DO_MODIFY_PLAYER_QUALIFY_MAX_DEGREE_REQ = 4349;          // 修改玩家某赛季某玩法历史最高段位信息请求
  NCI_IDIP_DO_MODIFY_PLAYER_QUALIFY_MAX_DEGREE_RSP = 4350;          // 修改玩家某赛季某玩法历史最高段位信息响应
  NCI_IDIP_AQ_DO_BAN_PLAYER_ALBUM_PIC_REQ = 4351;                   // 下架玩家相册图片请求
  NCI_IDIP_AQ_DO_BAN_PLAYER_ALBUM_PIC_RSP = 4352;                   // 下架玩家相册图片响应
  NCI_IDIP_AQ_DO_MODIFY_RECHARGE_LEVEL_REQ= 4353;                   // 修改玩家氪条请求
  NCI_IDIP_AQ_DO_MODIFY_RECHARGE_LEVEL_RSP= 4354;                   // 修改玩家氪条响应
  NCI_IDIP_DO_FARM_MONTH_CARD_REQ = 4355;                           // 改农场月卡时间（AQ）请求
  NCI_IDIP_DO_FARM_MONTH_CARD_RSP = 4356;                           // 改农场月卡时间（AQ）应答
  NCI_IDIP_QUERY_PLAYER_HAS_ITEM_REQ = 4357;                        // 查询用户是否拥有道具请求
  NCI_IDIP_QUERY_PLAYER_HAS_ITEM_RSP = 4358;                        // 查询用户是否拥有道具响应
  NCI_IDIP_QUERY_PLAYER_COMMODITY_BUY_TIMES_REQ = 4359;             // 获取商品购买次数
  NCI_IDIP_QUERY_PLAYER_COMMODITY_BUY_TIMES_RSP = 4360;             // 获取商品购买次数
  NCI_IDIP_DO_UPDATE_PLAYER_COMMODITY_BUY_TIMES_REQ = 4361;         // 更新商品购买次数
  NCI_IDIP_DO_UPDATE_PLAYER_COMMODITY_BUY_TIMES_RSP = 4362;         // 更新商品购买次数
  NCI_IDIP_PLAYER_REPUTATION_SCORE_MODIFY_REQ = 4363;               // 玩家信誉分修改请求
  NCI_IDIP_PLAYER_REPUTATION_SCORE_MODIFY_RSP = 4364;               // 玩家信誉分修改响应
  NCI_IDIP_QUERY_ACTIVITY_SQUAD_INFO_REQ = 4365;                    // 查询星宝小队信息请求
  NCI_IDIP_QUERY_ACTIVITY_SQUAD_INFO_RSP = 4366;                    // 查询星宝小队信息响应
  NCI_IDIP_DO_PLAYER_JOIN_SQUAD_REQ = 4367;                         // 执行玩家加入请求
  NCI_IDIP_DO_PLAYER_JOIN_SQUAD_RSP = 4368;                         // 执行玩家加入响应
  NCI_IDIP_DO_MODIFY_QUALIFY_SEASON_INFO_REQ = 4369;                // 修改玩家排位玩法指定赛季数据请求
  NCI_IDIP_DO_MODIFY_QUALIFY_SEASON_INFO_RSP = 4370;                // 修改玩家排位玩法指定赛季数据响应
  NCI_IDIP_AQ_DO_MODIFY_GAME_PROTECTION_SETTINGS_REQ = 4371;        // 修改游戏防欺凌设置请求
  NCI_IDIP_AQ_DO_MODIFY_GAME_PROTECTION_SETTINGS_RSP = 4372;        // 修改游戏防欺凌设置响应
  NCI_IDIP_AQ_QUERY_GAME_PROTECTION_SETTINGS_REQ = 4373;            // 查询游戏防欺凌设置请求
  NCI_IDIP_AQ_QUERY_GAME_PROTECTION_SETTINGS_RSP = 4374;            // 查询游戏防欺凌设置响应
  NCI_IDIP_DO_CLUB_SET_OWNER_REQ = 4375;                            // 设置社团团长UID请求
  NCI_IDIP_DO_CLUB_SET_OWNER_RSP = 4376;                            // 设置社团团长UID响应
  NCI_IDIP_DO_WISH_ACTIVITY_HELP_BIND_REQ = 4377;                   // 心愿活动助力绑定请求
  NCI_IDIP_DO_WISH_ACTIVITY_HELP_BIND_RSP = 4378;                   // 心愿活动助力绑定应答
  NCI_IDIP_DO_MODIFY_CODING_COMMUNITY_TEMPLATE_REQ = 4379;          // 修改可视化编程模板信息请求
  NCI_IDIP_DO_MODIFY_CODING_COMMUNITY_TEMPLATE_RSP = 4380;          // 修改可视化编程模板信息响应
  NCI_IDIP_DO_MODIFY_CODING_COMMUNITY_TEMPLATE_STATE_REQ = 4381;    // 修改可视化编程模板状态请求
  NCI_IDIP_DO_MODIFY_CODING_COMMUNITY_TEMPLATE_STATE_RSP = 4382;    // 修改可视化编程模板状态响应
  NCI_IDIP_SEND_GLOBAL_MAIL_WITH_ATTACH_EXPIRE_REQ = 4383;          // 发送全服邮件请求(附件支持有效期设置)
  NCI_IDIP_SEND_GLOBAL_MAIL_WITH_ATTACH_EXPIRE_RSP = 4384;          // 发送全服邮件响应(附件支持有效期设置)
  NCI_IDIP_AQ_DO_CLUB_DISSOLVE_REQ = 4385;                          // 解散制定社团请求
  NCI_IDIP_AQ_DO_CLUB_DISSOLVE_RSP = 4386;                          // 解散制定社团响应
  NCI_IDIP_AQ_DO_CLUB_KICK_MEMBER_REQ = 4387;                       // 社团踢出指定玩家请求
  NCI_IDIP_AQ_DO_CLUB_KICK_MEMBER_RSP = 4388;                       // 社团踢出指定玩家响应
  NCI_IDIP_ADD_UGC_PAID_ITEMS_REQ = 4389;                           // 补发星钻内购道具请求
  NCI_IDIP_ADD_UGC_PAID_ITEMS_RSP = 4390;                           // 补发星钻内购道具响应
  NCI_IDIP_AQ_DO_FARM_FORBID_GIFT_SENDING_REQ = 4391;               // 禁止送礼
  NCI_IDIP_AQ_DO_FARM_FORBID_GIFT_SENDING_RSP = 4392;               //
  NCI_IDIP_AQ_DO_FARM_BLOCK_GIFT_MSG_REQ = 4393;                    // 屏蔽留言
  NCI_IDIP_AQ_DO_FARM_BLOCK_GIFT_MSG_RSP = 4394;                    //
  NCI_IDIP_DO_FARM_ADD_FISH_EXP_REQ = 4395;                         // 农场鱼卡熟练度增加请求
  NCI_IDIP_DO_FARM_ADD_FISH_EXP_RSP = 4396;                         // 农场鱼卡熟练度增加应答
  NCI_IDIP_ANIMAL_HANDBOOK_RECEIVE_GIVE_ANIMAL_REQ = 4397;          // 动物图鉴领取赠送图鉴请求
  NCI_IDIP_ANIMAL_HANDBOOK_RECEIVE_GIVE_ANIMAL_RSP = 4398;          // 动物图鉴领取赠送图鉴响应
  NCI_IDIP_DO_TRAINING_CAMP_GET_SPORTSMAN_DATA_REQ  = 4399;         // 特训营获取运动员数据请求
  NCI_IDIP_DO_TRAINING_CAMP_GET_SPORTSMAN_DATA_RSP  = 4400;         // 特训营获取运动员数据响应
  NCI_IDIP_DO_TRAINING_CAMP_TRAIN_SPORTSMAN_REQ  = 4401;            // 特训营训练运动员请求
  NCI_IDIP_DO_TRAINING_CAMP_TRAIN_SPORTSMAN_RSP  = 4402;            // 特训营训练运动员响应
  NCI_IDIP_QUERY_FARM_GET_BASICINFO_REQ = 4403;                     // 查询农场基本信息请求
  NCI_IDIP_QUERY_FARM_GET_BASICINFO_RSP = 4404;                     // 查询农场基本信息响应
  NCI_IDIP_DO_MODIFY_OMDRANK_REQ = 4405;                            // OMD兽人修改排行榜
  NCI_IDIP_DO_MODIFY_OMDRANK_RSP = 4406;                            // OMD兽人修改排行榜响应
  NCI_IDIP_MODIFY_CLUB_HEAT_REQ = 4407;                             // 修改社团活跃值请求
  NCI_IDIP_MODIFY_CLUB_HEAT_RSP = 4408;                             // 修改社团活跃值响应
  NCI_IDIP_MODIFY_CLUB_L_B_S_REQ = 4409;                            // 修改社团归属地请求
  NCI_IDIP_MODIFY_CLUB_L_B_S_RSP = 4410;                            // 修改社团归属地响应
  NCI_IDIP_BAN_CLUB_HEAT_RANK_REQ = 4411;                           // 禁止社团参加榜单请求
  NCI_IDIP_BAN_CLUB_HEAT_RANK_RSP = 4412;                           // 禁止社团参加榜单响应
  NCI_IDIP_QUERY_GET_UGC_PLAYED_MAPS_REQ = 4413;                    // 获取UGC游玩列表请求
  NCI_IDIP_QUERY_GET_UGC_PLAYED_MAPS_RSP = 4414;                    // 获取UGC游玩列表响应
  NCI_IDIP_MODIFY_CUPS_PROGRESS_REQ = 4417;                         // 修改玩家奖杯征程进度请求
  NCI_IDIP_MODIFY_CUPS_PROGRESS_RSP = 4418;                         // 修改玩家奖杯征程进度响应
  NCI_IDIP_QUERY_FARM_LIU_YAN_MESSAGE_REQ = 4419;                   // 查询农场留言板的所有留言信息请求
  NCI_IDIP_QUERY_FARM_LIU_YAN_MESSAGE_RSP = 4420;                   // 查询农场留言板的所有留言信息应答
  NCI_IDIP_DO_MODIFY_STAR_P_REQ = 1;							// 修改starp信息请求（已废弃）
  NCI_IDIP_DO_MODIFY_STAR_P_RSP = 2;							// 修改starp信息响应（已废弃）
  NCI_IDIP_DO_STAR_P_ADD_MAIL_REQ = 4421;                  // 添加starp邮件请求
  NCI_IDIP_DO_STAR_P_ADD_MAIL_RSP = 4422;                  // 添加starp邮件响应

  NCI_IDIP_DO_CLEAN_USER_BAG_INTERACT_COMB_REQ = 4423;              // 清理玩家背包互动组合数据请求
  NCI_IDIP_DO_CLEAN_USER_BAG_INTERACT_COMB_RSP = 4424;              // 清理玩家背包互动组合数据响应
  NCI_IDIP_AQ_DO_FARM_HOUSE_PUT_DOWN_REQ = 4425;                    // 下架小屋（AQ）请求
  NCI_IDIP_AQ_DO_FARM_HOUSE_PUT_DOWN_RSP = 4426;                    // 下架小屋（AQ）应答
  NCI_IDIP_AQ_DO_FARM_HOUSE_PICK_ALL_FURNITURE_REQ = 4427;          // 收起农场小屋所有装饰家具（AQ）请求
  NCI_IDIP_AQ_DO_FARM_HOUSE_PICK_ALL_FURNITURE_RSP = 4428;          // 收起农场小屋所有装饰家具（AQ）应答
  NCI_IDIP_MODIFY_CHARGE_PROGRESS_REQ = 4429;                       // 修改氪条进度请求
  NCI_IDIP_MODIFY_CHARGE_PROGRESS_RSP = 4430;                       // 修改氪条进度响应
  NCI_IDIP_DO_UPDATE_SEASON_FASHION_EQUIP_BOOK_REQ = 4449;
  NCI_IDIP_DO_UPDATE_SEASON_FASHION_EQUIP_BOOK_RSP = 4450;
  NCI_IDIP_QUERY_WISH_ACTIVITY_HELP_INFO_REQ = 4451;                // 心愿活动助力信息查询请求
  NCI_IDIP_QUERY_WISH_ACTIVITY_HELP_INFO_RSP = 4452;                // 心愿活动助力信息查询应答
  NCI_IDIP_DELIVER_MIDAS_PRODUCT_REQ = 4453;                        // 米大师商品补发请求
  NCI_IDIP_DELIVER_MIDAS_PRODUCT_RSP = 4454;                        // 米大师商品补发响应
  NCI_IDIP_AQ_DO_MODIFY_UGC_RANK_SCORE_REQ = 4455;                  // 修改UGC地图榜单分数（AQ）请求
  NCI_IDIP_AQ_DO_MODIFY_UGC_RANK_SCORE_RSP = 4456;                  // 修改UGC地图榜单分数（AQ）应答
  NCI_IDIP_VERBAL_VIOLATION_REPUTATION_SCORE_PUNISH_REQ = 4457;     // 言语违规处罚信誉分处罚请求
  NCI_IDIP_VERBAL_VIOLATION_REPUTATION_SCORE_PUNISH_RSP = 4458;     // 言语违规处罚信誉分处罚响应
  NCI_IDIP_VERBAL_VIOLATION_REPUTATION_SCORE_UN_PUNISH_REQ = 4459;  // 言语违规信誉分误处罚回撤并补分请求
  NCI_IDIP_VERBAL_VIOLATION_REPUTATION_SCORE_UN_PUNISH_RSP = 4460;  // 言语违规信誉分误处罚回撤并补分响应
  NCI_IDIP_QUERY_PLAYER_SUPER_CORE_RANK_ACTIVITY_AUTH_REQ = 4461;   // 超核活动玩家参与权限查询请求
  NCI_IDIP_QUERY_PLAYER_SUPER_CORE_RANK_ACTIVITY_AUTH_RSP = 4462;   // 超核活动玩家参与权限查询响应
  NCI_IDIP_DO_SUPER_CORE_RANK_ACTIVITY_SCORE_MODIFY_REQ = 4463;     // 超核活动玩家排名分数修改请求
  NCI_IDIP_DO_SUPER_CORE_RANK_ACTIVITY_SCORE_MODIFY_RSP = 4464;     // 超核活动玩家排名分数修改响应
  NCI_IDIP_QUERY_PLAYER_SUPER_CORE_RANK_ACTIVITY_SCORE_REQ = 4465;  // 超核活动玩家排名分数细节查询请求
  NCI_IDIP_QUERY_PLAYER_SUPER_CORE_RANK_ACTIVITY_SCORE_RSP = 4466;  // 超核活动玩家排名分数细节查询响应
  NCI_IDIP_AQ_DO_FARM_RESET_PET_NAME_REQ = 4467;                    // （AQ）重置宠物名称请求
  NCI_IDIP_AQ_DO_FARM_RESET_PET_NAME_RSP = 4468;                    // （AQ）重置宠物名称应答
  NCI_IDIP_AQ_DO_FARM_BAN_CHANGE_PET_NAME_REQ = 4469;               // （AQ）禁止修改宠物名称请求
  NCI_IDIP_AQ_DO_FARM_BAN_CHANGE_PET_NAME_RSP = 4470;               // （AQ）禁止修改宠物名称应答
  NCI_IDIP_DO_DEDUCT_ONLINE_PLAYER_ITEM_REQ = 4471;                 // 扣除在线玩家道具请求
  NCI_IDIP_DO_DEDUCT_ONLINE_PLAYER_ITEM_RSP = 4472;                 // 扣除在线玩家道具响应
  NCI_IDIP_QUERY_PLAYER_AND_OTHER_PLAYER_RELATION_REQ = 4473;       // 查询玩家和另一个玩家间的亲密度请求
  NCI_IDIP_QUERY_PLAYER_AND_OTHER_PLAYER_RELATION_RSP = 4474;       // 查询玩家和另一个玩家间的亲密度响应
  NCI_IDIP_AQ_DO_MALL_GIFT_CARD_RESET_WORDS_REQ = 4475;             // 重置赠礼卡祝福语接口（AQ）请求
  NCI_IDIP_AQ_DO_MALL_GIFT_CARD_RESET_WORDS_RSP = 4476;             // 重置赠礼卡祝福语接口（AQ）应答
  NCI_IDIP_DO_TRAINING_GET_OJBECT_DATA_REQ = 4477;                  // 查询培养目标数据请求
  NCI_IDIP_DO_TRAINING_GET_OJBECT_DATA_RSP = 4478;                  // 查询培养目标数据响应
  NCI_IDIP_DO_TRAINING_TRAIN_OBJECT_REQ = 4479;                     // 对目标进行培养请求
  NCI_IDIP_DO_TRAINING_TRAIN_OBJECT_RSP = 4480;                     // 对目标进行培养响应
  NCI_IDIP_DO_FARM_MODIFY_VILLAGER_REQ = 4481;                      // 修改农场村民请求
  NCI_IDIP_DO_FARM_MODIFY_VILLAGER_RSP = 4482;                      // 修改农场村民应答
  NCI_IDIP_DO_OFF_END_RECHARGE_DIAMOND_NOTIFY_REQ = 4483;           // 端外充值星钻通知请求
  NCI_IDIP_DO_OFF_END_RECHARGE_DIAMOND_NOTIFY_RSP = 4484;           // 端外充值星钻通知应答
  NCI_IDIP_QUERY_CHARGE_MOENY_REQ = 4485;                           // 查询充值金额请求
  NCI_IDIP_QUERY_CHARGE_MOENY_RSP = 4486;                           // 查询充值金额应答
  NCI_IDIP_DO_FARM_DEL_BUILDING_SKIN_REQ = 4487;                    // 删除农场建筑装饰
  NCI_IDIP_DO_FARM_DEL_BUILDING_SKIN_RSP = 4488;                    // 删除农场建筑装饰
  NCI_IDIP_DO_SUB_USER_BACKPACK_ITEM_REQ = 4415;     // 扣除玩家角色的背包道具请求
  NCI_IDIP_DO_SUB_USER_BACKPACK_ITEM_RSP = 4416;     // 扣除玩家角色的背包道具响应
  NCI_IDIP_DO_PET_MODIFY_LEVEL_REQ = 4431;           // 修改星兽等级请求
  NCI_IDIP_DO_PET_MODIFY_LEVEL_RSP = 4432;           // 修改星兽等级响应
  NCI_IDIP_DO_PET_MODIFY_PASSIVE_SKILL_REQ = 4433;   // 修改啾灵被动技能请求
  NCI_IDIP_DO_PET_MODIFY_PASSIVE_SKILL_RSP = 4434;   // 修改啾灵被动技能响应
  NCI_IDIP_DO_PET_MODIFY_STAR_LEVEL_REQ = 4435;      // 修改啾灵星级请求
  NCI_IDIP_DO_PET_MODIFY_STAR_LEVEL_RSP = 4436;      // 修改啾灵星级响应
  NCI_IDIP_DO_ACCOUNT_MODIFY_POWER_REQ = 4437;       // 修改账户体力请求
  NCI_IDIP_DO_ACCOUNT_MODIFY_POWER_RSP = 4438;       // 修改账户体力响应
  NCI_IDIP_DO_WORLD_ADD_ITEM_REQ = 4439;             // 给世界箱子添加道具请求
  NCI_IDIP_DO_WORLD_ADD_ITEM_RSP = 4440;             // 给世界箱子添加道具响应
  NCI_IDIP_DO_WORLD_SUB_ITEM_REQ = 4441;             // 给世界箱子扣除道具请求
  NCI_IDIP_DO_WORLD_SUB_ITEM_RSP = 4442;             // 给世界箱子扣除道具响应
  NCI_IDIP_DO_USER_ADD_EXP_REQ = 4443;               // 给玩家添加经验请求
  NCI_IDIP_DO_USER_ADD_EXP_RSP = 4444;               // 给玩家添加经验响应
  NCI_IDIP_DO_USER_SUB_EXP_REQ = 4445;               // 玩家减少经验请求
  NCI_IDIP_DO_USER_SUB_EXP_RSP = 4446;               // 玩家减少经验响应
  NCI_IDIP_DO_OPER_EQUIP_ATTR_REQ = 4447;            // 背包装备属性操作请求
  NCI_IDIP_DO_OPER_EQUIP_ATTR_RSP = 4448;            // 背包装备属性操作响应

  NCI_IDIP_QUERY_CARD_TRADE_INFO_REQ = 4489;                        // 查询卡牌交易信息
  NCI_IDIP_QUERY_CARD_TRADE_INFO_RSP = 4490;                        // 查询卡牌交易信息
  NCI_IDIP_RECEIVE_TRADING_CARD_REQ = 4491;                         // 领取赠送的卡牌
  NCI_IDIP_RECEIVE_TRADING_CARD_RSP = 4492;                         // 领取赠送的卡牌
  NCI_IDIP_MODIFY_TRADING_CARD_REQ = 4493;                          // 获取卡牌
  NCI_IDIP_MODIFY_TRADING_CARD_RSP = 4494;                          // 获取卡牌

  NCI_IDIP_SEND_PERSONAL_MAIL_WITH_AMS_ATTACH_EXPIRE_REQ = 4590;    // 发送带有效期的附件邮件请求(AMS娱米积分)
  NCI_IDIP_SEND_PERSONAL_MAIL_WITH_AMS_ATTACH_EXPIRE_RSP = 4591;    // 发送带有效期的附件邮件请求(AMS娱米积分)
  NCI_IDIP_SEND_GLOBAL_MAIL_WITH_AMS_ATTACH_EXPIRE_REQ = 4592;      // 发送全服邮件请求(附件支持有效期设置, AMS娱米积分)
  NCI_IDIP_SEND_GLOBAL_MAIL_WITH_AMS_ATTACH_EXPIRE_RSP = 4593;      // 发送全服邮件响应(附件支持有效期设置, AMS娱米积分)

  // -----------------------------------------------------------------------------------------------------------
  // 海外业务使用idip接口
  NCI_IDIP_GET_UGC_OUT_GAME_TRANSLATE_DATA_REQ = 4500;              // 获取UGC局外翻译数据请求
  NCI_IDIP_GET_UGC_OUT_GAME_TRANSLATE_DATA_RSP = 4501;              // 获取UGC局外翻译数据响应
  NCI_IDIP_GET_UGC_IN_GAME_TRANSLATE_DATA_REQ = 4502;	              // 获取UGC局内翻译数据请求
  NCI_IDIP_GET_UGC_IN_GAME_TRANSLATE_DATA_RSP = 4503;	              // 获取UGC局内翻译数据响应
  NCI_IDIP_QUERY_UGC_IN_GAME_ORIGINAL_TEXT_REQ  = 4504;             // 获取UGC局内原文数据请求
  NCI_IDIP_QUERY_UGC_IN_GAME_ORIGINAL_TEXT_RSP  = 4505;             // 获取UGC局内原文数据响应
  NCI_IDIP_QUERY_UGC_OUT_GAME_ORIGINAL_TEXT_REQ  = 4506;            // 获取UGC局外原文数据请求
  NCI_IDIP_QUERY_UGC_OUT_GAME_ORIGINAL_TEXT_RSP  = 4507;            // 获取UGC局外原文数据响应
  NCI_IDIP_MODIF_UGC_OUT_GAME_TRANSLATE_DATA_REQ = 4508;            // 修改UGC局外翻译数据请求
  NCI_IDIP_MODIF_UGC_OUT_GAME_TRANSLATE_DATA_RSP = 4509;            // 修改UGC局外翻译数据响应
  NCI_IDIP_MODIF_UGC_IN_GAME_TRANSLATE_DATA_REQ  = 4510;            // 修改UGC局内翻译数据请求
  NCI_IDIP_MODIF_UGC_IN_GAME_TRANSLATE_DATA_RSP  = 4511;            // 修改UGC局内翻译数据响应
  // -----------------------------------------------------------------------------------------------------------

  NCI_IDIP_QUERY_LUCK_STAR_RECEIVE_REQ = 4530;                      // 查询福签是否可领取
  NCI_IDIP_QUERY_LUCK_STAR_RECEIVE_RSP = 4531;                      // 查询福签是否可领取
  NCI_IDIP_DO_LUCK_STAR_RECEIVE_REQ = 4532;                         // 领取福签
  NCI_IDIP_DO_LUCK_STAR_RECEIVE_RSP = 4533;                         // 领取福签
  NCI_IDIP_QUERY_LUCK_STAR_GIVE_REQ = 4534;                         // 查询福签是可以赠送
  NCI_IDIP_QUERY_LUCK_STAR_GIVE_RSP = 4535;                         // 查询福签是可以赠送
  NCI_IDIP_DO_LUCK_STAR_GIVE_REQ = 4536;                            // 赠送福签
  NCI_IDIP_DO_LUCK_STAR_GIVE_RSP = 4537;                            // 赠送福签
  NCI_IDIP_NOTIFY_STREAM_INTERACTION_CMD_REQ = 4538;                // 直播交互指令
  NCI_IDIP_NOTIFY_STREAM_INTERACTION_CMD_RSP = 4539;                // 直播交互指令
  NCI_IDIP_AQ_DO_BIRTHDAY_CARD_RESET_WORDS_REQ = 4540;              // 重置生日贺卡祝福语接口请求请求(AQ)
  NCI_IDIP_AQ_DO_BIRTHDAY_CARD_RESET_WORDS_RSP = 4541;              // 重置生日贺卡祝福语接口请求应答(AQ)
  NCI_IDIP_PLATFORM_INTERFACE_FORWARD_REQ = 4542;                   // 平台接口转发请求
  NCI_IDIP_PLATFORM_INTERFACE_FORWARD_RSP = 4543;                   // 平台接口转发响应
  NCI_IDIP_DO_FARM_MAGIC_ADD_OR_DEL_REQ = 4544;                     // 增删农场仙术请求
  NCI_IDIP_DO_FARM_MAGIC_ADD_OR_DEL_RSP = 4545;                     // 增删农场仙术应答
  NCI_IDIP_DO_FARM_MAGIC_ADJUST_MP_REQ = 4546;                      // 增减农场仙术蓝条请求
  NCI_IDIP_DO_FARM_MAGIC_ADJUST_MP_RSP = 4547;                      // 增减农场仙术蓝条应答
  NCI_IDIP_DO_FARM_LAYOUT_OP_REQ = 4548;                            // 处理农场图纸请求
  NCI_IDIP_DO_FARM_LAYOUT_OP_RSP = 4549;                            // 处理农场图纸应答
  NCI_IDIP_DO_TRANSFER_USER_PLAT_REQ = 4550;                        // 角色转移区服请求
  NCI_IDIP_DO_TRANSFER_USER_PLAT_RSP = 4551;                        // 角色转移区服响应
  NCI_IDIP_QUERY_TRANSFER_ABILITY_REQ = 4552;                       // 查询转区资格请求
  NCI_IDIP_QUERY_TRANSFER_ABILITY_RSP = 4553;                       // 查询转区资格响应
  NCI_IDIP_AQ_DO_FARM_COOK_PUT_DOWN_REQ = 4554;                     // 下架餐厅（AQ）请求
  NCI_IDIP_AQ_DO_FARM_COOK_PUT_DOWN_RSP = 4555;                     // 下架餐厅（AQ）应答
  NCI_IDIP_DO_FARM_COOK_ADD_EMPLOYEE_REQ = 4556;                    // 餐厅加一个员工请求
  NCI_IDIP_DO_FARM_COOK_ADD_EMPLOYEE_RSP = 4557;                    // 餐厅加一个员工应答
  NCI_IDIP_DO_RELEASE_TRANSFER_LOCK_REQ = 4558;                     // 解除转区登录锁请求
  NCI_IDIP_DO_RELEASE_TRANSFER_LOCK_RSP = 4559;                     // 解除转区登录锁响应

  NCI_IDIP_AQ_DO_MODIFY_CREATOR_MESSAGE_REQ = 4564;                 // 修改创建者信息请求
  NCI_IDIP_AQ_DO_MODIFY_CREATOR_MESSAGE_RSP = 4565;                 // 修改创建者信息应答
  NCI_IDIP_AQ_DO_FARM_PARTY_CLEAR_REQ = 4566;                       // 删除农场派对信息请求
  NCI_IDIP_AQ_DO_FARM_PARTY_CLEAR_RSP = 4567;                       // 删除农场派对信息应答
  NCI_IDIP_AQ_DO_FARM_PARTY_BAN_REQ = 4568;                         // 禁止创建与编辑农场派对信息请求
  NCI_IDIP_AQ_DO_FARM_PARTY_BAN_RSP = 4569;                         // 禁止创建与编辑农场派对信息应答
  NCI_IDIP_AQ_DO_FARM_PARTY_SET_FREE_REQ = 4570;                    // 解除禁止创建与编辑农场派对信息请求
  NCI_IDIP_AQ_DO_FARM_PARTY_SET_FREE_RSP = 4571;                    // 解除禁止创建与编辑农场派对信息应答
  NCI_IDIP_DO_MODIFY_PLAYER_ARENA_HERO_CE_SCORE_REQ = 4572;         // 修改玩家峡谷英雄战力请求
  NCI_IDIP_DO_MODIFY_PLAYER_ARENA_HERO_CE_SCORE_RSP = 4573;         // 修改玩家峡谷英雄战力应答
  NCI_IDIP_QUERY_USER_REAL_PLAT_REQ = 4574;                         // 查询实际平台请求
  NCI_IDIP_QUERY_USER_REAL_PLAT_RSP = 4575;                         // 查询实际平台响应
  NCI_IDIP_AQ_DO_FARM_COOK_SCREEN_CLEAR_REQ = 4576;                 // 修改餐厅显示屏文本请求
  NCI_IDIP_AQ_DO_FARM_COOK_SCREEN_CLEAR_RSP = 4577;                 // 修改餐厅显示屏文本应答
  NCI_IDIP_AQ_DO_FARM_COOK_SCREEN_BAN_REQ = 4578;                   // 禁止修改餐厅显示屏文本请求
  NCI_IDIP_AQ_DO_FARM_COOK_SCREEN_BAN_RSP = 4579;                   // 禁止修改餐厅显示屏文本应答
  NCI_IDIP_AQ_DO_FARM_COOK_SCREEN_SET_FREE_REQ = 4580;              // 解除禁止修改餐厅显示屏文本请求
  NCI_IDIP_AQ_DO_FARM_COOK_SCREEN_SET_FREE_RSP = 4581;              // 解除禁止修改餐厅显示屏文本应答
  NCI_IDIP_DO_SET_RAFFLE_DAILY_LIMIT_TAG_WHITE_LIST_REQ = 4582;            // 祈愿每日上限白名单请求
  NCI_IDIP_DO_SET_RAFFLE_DAILY_LIMIT_TAG_WHITE_LIST_RSP = 4583;            // 祈愿每日上限白名单应答
  NCI_IDIP_DO_FARM_COOK_UPDATE_EXTEND_INFO_REQ = 4584;            // 更新餐厅扩建信息请求
  NCI_IDIP_DO_FARM_COOK_UPDATE_EXTEND_INFO_RSP = 4585;            // 更新餐厅扩建信息应答
  NCI_IDIP_QUERY_FARM_COOK_QUERY_EXTEND_INFO_REQ = 4586;            // 查询餐厅扩建信息请求
  NCI_IDIP_QUERY_FARM_COOK_QUERY_EXTEND_INFO_RSP = 4587;            // 查询餐厅扩建信息应答
  NCI_IDIP_QUERY_ACCOUNT_TRANSFER_PRE_CONDITION_REQ = 4588;     // 查询转平台功能前置条件请求
  NCI_IDIP_QUERY_ACCOUNT_TRANSFER_PRE_CONDITION_RSP = 4589;     // 查询转平台功能前置条件响应

  NCI_IDIP_AQ_DO_PLAYER_ITA_BAG_RESET_REQ = 4594; // 重置玩家痛包装饰请求
  NCI_IDIP_AQ_DO_PLAYER_ITA_BAG_RESET_RSP = 4595; // 重置玩家痛包装饰响应



  // -----------------------------------------------------------------------------------------------------------
  // 以下cmdid为未上线接口
  NCI_IDIP_ADD_MULTI_LANGUAGE_CONF_REQ = 5003;                      // 增加多语言配置请求(海外业务)
  NCI_IDIP_ADD_MULTI_LANGUAGE_CONF_RSP = 5004;                      // 增加多语言配置响应(海外业务)
  NCI_IDIP_QUERY_GAME_USER_INFO_REQ = 5005;                         // 查询游戏玩家信息请求
  NCI_IDIP_QUERY_GAME_USER_INFO_RSP = 5006;                         // 查询游戏玩家信息请求
  NCI_IDIP_DO_CLEAN_USER_REGION_RANK_REQ = 5007;                    // 清理玩家地区排行榜数据请求
  NCI_IDIP_DO_CLEAN_USER_REGION_RANK_RSP = 5008;                    // 清理玩家地区排行榜数据响应
  NCI_IDIP_UGC_DATA_STORE_SVR_TEST_REQ = 5009;                      // ugcdatastoresvr路由测试请求
  NCI_IDIP_UGC_DATA_STORE_SVR_TEST_RSP = 5010;                      // ugcdatastoresvr路由测试响应
  // -----------------------------------------------------------------------------------------------------------


  NCI_IDIP_AQ_DO_MODIFY_DEBUG_DS_REQ  = 4512;        		    // 修改DebugDs请求
  NCI_IDIP_AQ_DO_MODIFY_DEBUG_DS_RSP  = 4513;        		    // 修改DebugDs响应


  NCI_IDIP_QUERY_STAR_P_GUILD_INFO_REQ = 5050;                  // 查询工会信息请求
  NCI_IDIP_QUERY_STAR_P_GUILD_INFO_RSP = 5051;                  // 查询工会信息响应
  NCI_IDIP_QUERY_STAR_P_TRADER_REQ = 5052;						// 查询啾灵世界商人请求
  NCI_IDIP_QUERY_STAR_P_TRADER_RSP = 5053;						// 查询啾灵世界商人响应
  NCI_IDIP_QUERY_STAR_P_COMMON_PVE_DATA_REQ = 5054;				// 查询啾灵世界地下城、密域、高塔副本的公共数据请求
  NCI_IDIP_QUERY_STAR_P_COMMON_PVE_DATA_RSP = 5055;				// 查询啾灵世界地下城、密域、高塔副本的公共数据响应
  NCI_IDIP_QUERY_STAR_P_PLAYER_DROP_ITEMS_REQ = 5056;			// 查询啾灵世界玩家丢弃的物品请求
  NCI_IDIP_QUERY_STAR_P_PLAYER_DROP_ITEMS_RSP = 5057;			// 查询啾灵世界玩家丢弃的物品响应
  NCI_IDIP_QUERY_STAR_P_MAP_POS_BUILDING_REQ = 5058;			// 查询啾灵世界建筑信息请求
  NCI_IDIP_QUERY_STAR_P_MAP_POS_BUILDING_RSP = 5059;			// 查询啾灵世界建筑信息响应
  NCI_IDIP_QUERY_STAR_P_PLAYER_ITEMS_REQ = 5060;				// 查询啾灵世界玩家道具请求
  NCI_IDIP_QUERY_STAR_P_PLAYER_ITEMS_RSP = 5061;				// 查询啾灵世界玩家道具响应
  NCI_IDIP_QUERY_STAR_P_PLAYER_SIMPLE_INFO_REQ = 5062;			// 查询啾灵世界玩家简略数据请求
  NCI_IDIP_QUERY_STAR_P_PLAYER_SIMPLE_INFO_RSP = 5063;			// 查询啾灵世界玩家简略数据响应
  NCI_IDIP_QUERY_STAR_P_PLAYER_ROLE_ATTR_REQ = 5064;			// 查询啾灵世界玩家属性请求
  NCI_IDIP_QUERY_STAR_P_PLAYER_ROLE_ATTR_RSP = 5065;			// 查询啾灵世界玩家属性响应
  
  NCI_IDIP_QUERY_STAR_P_ITEM_INFOS_REQ = 5066;                  // 查询啾灵世界物品信息请求
  NCI_IDIP_QUERY_STAR_P_ITEM_INFOS_RSP = 5067;                  // 查询啾灵世界物品信息响应
}

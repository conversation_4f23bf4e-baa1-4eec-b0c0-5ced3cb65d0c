syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "common.proto";
import "base_common.proto";
import "cs_head.proto";
import "ss_head.proto";
import "ss_common.proto";
import "ugcplatsvr/ss_ugcplatsvr.proto";


message TestUgcSvrReq {
  optional int32 testid = 1;
}

message TestUgcSvrRes {
  optional int32 result = 1;
}

enum MapExpType {
  MET_LIKE = 1; //点赞
  MET_COLLECT = 2; //收藏
  MET_PLAY = 4; //游玩
  MET_SUBSCRIBE_COUNT = 5;  // 订阅数量, 仅用于边界测试
  MET_COLLECT_COUNT = 6;    // 收藏数量, 仅用于边界测试
  MET_USE_COUNT = 7;
  MET_SPEND_TIME = 8;
  MET_CHANGE_STATUS = 9;    // 地图状态改变(上下架, 联名或除名)
  MET_SHARE_COUNT = 10;     // 分享数量, 仅用于边界测试
  MET_LABEL_SCORE_PLAYER_COUNT = 11;     // 评价人数
  MET_WARM_DATA = 12;  // 温暖数据
}

message RpcApplyUgcKeyReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional ApplyType type = 2;
  optional int32 reason = 3;
  repeated ApplyUgcKey allKey = 10;
}

message ApplyUgcKey {
  optional string bucket = 1;
  optional int64 ugcId = 2;
  optional int64 destId = 3; //目标id，例如发布就是pubId
  repeated int64 getList = 4;
  optional int64 creatorId = 5;
  optional int32 ugcResType = 6;
}

message RpcApplyUgcKeyRes {
  optional int32 result = 1;
  optional UgcKeyInfo info = 4;
}

message RpcApplyInfoReq {
  option (region_msg) = true;
  option (rpc_call_timeout_seconds) = 3;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string openID = 2;
  optional int32 platId = 3;
  optional bool isApplyBucket = 4;
  optional bool isApplyCreatorId = 5;
  optional int64 preGenerateId = 6; // gamesvr生成的 creatorId
  optional bool isApplyMapKey = 7;
  optional int32 worldId = 8;
  optional string preMapKey = 9; // gamesvr生成的 preMapKey
  optional string preBucket = 10; // gamesvr生成的 preBucket
}

message RpcApplyInfoRes {
  optional int32 result = 1;
  optional string bucket = 2;
  optional int64 creatorId = 3;
  optional string mapKey = 4;
}

message RpcMapPublishCheckReq {
  option (region_msg) = true;
  optional int64 ugcId = 1;
  optional bool isUpdate = 2;
  repeated UgcEditorInfo editors = 3; //作者
}

message RpcMapPublishCheckRes {
  optional int32 result = 1;
}

message RpcCheckGoodsValidReq {// 验证商品是否可以购买
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional string goodsId = 2;
  optional int32 uintPrice = 3;
  optional string itemId = 4;
  optional int32 itemNum = 5;
}

message RpcCheckGoodsValidRes {
  optional int32 result = 1;
  optional int64 creatorId = 2;
}

message RpcActivePublishGoodsReq {// 请求激活上架商品的能力
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional int64 creatorId = 2;
  optional int64 uid = 3;
}

message RpcActivePublishGoodsRes {
  optional int32 result = 1;
}

message RpcMapPublishReq {
  option (region_msg) = true;
  optional int64 creatorId = 1[(field_hash_key) = true];
  optional int64 ugcId = 2 ;
  optional string name = 3;
  optional string desc = 4;
  optional string tags = 5;
  optional UgcMdList mdList = 6; //mdList
  optional string bucket = 7;
  optional int32 templateId = 8;
  optional string editorName = 9;
  optional int64 oldUgcId = 10;
  optional string openId = 11;
  optional string avatar = 12;
  optional int32 pointsNumber = 13;
  optional UgcInstanceType ugcInstanceType = 14;
  optional int32 editorSec = 15;
  optional UgcGroupIdList groupIds = 16;  // 地图中使用到的发布组合
  optional int32 platId = 17;
  optional UgcExtraInfo extraInfo = 18;
  optional int32 worldId = 19 [(field_region_key) = true]; // 搬迁地图专用(ugcplatsvr -> ugcsvr)
  optional int32 idipArea = 20;
  optional string ugcVersion = 21;
  optional string clientVersion = 22;
  optional int32 difficulty = 23;     // 地图评级
  optional string mapKeys = 24; //地图key
  repeated uint32 goldTopics = 25;
  repeated string blueTopics = 26;
  repeated BattleCamp camps = 27;
  repeated UgcEditorInfo editors = 28;
  optional int64 updateTime = 29 ;
  optional int64 uid = 30;
  optional UgcMdList pubList = 31; //草稿箱已发布
  repeated UgcLayerInfo layerList = 32; //图层信息
  optional int64 draftCreateTime = 33 ;
  optional int32 occupancyValue = 34; //地图占用值
  optional int32 propertyScore = 35; //性能评分
  optional int32 actorCount = 36; //actor数量
  optional UgcResParam ugcResParam = 37; // 资源参数
  optional bool isCampsOpen = 38; //阵营是否开启
  optional bool isEditable = 39; //是否可编辑 （单纯记录 策划需求不明确 不与tags一起存放）
  optional bool isOpenHealth = 40; //是否打开生命值
  repeated UgcGoodsInfo ugcGoodsInfo = 41;    // 上架商品信息
  optional bool isOpenSave = 42; //是否开启存档
  repeated int64 resIdList = 43; //使用的资产id
  optional bool isDanMuUnable = 44;  // 是否打开弹幕
  optional bool isAllowMidJoin = 45;  // 是否中途加入
  repeated MapCoverInfo covers = 46;   //多封面缩略图
  repeated UgcRankInfo rankInfo = 47;      // 排行榜基本信息
  optional UgcMapSetData mapSet = 48;  //设置里面数据
  optional bool isAiGen = 49; // 是否是AI生成的
  optional bool toolGenFlag = 50;  // 工具发布的，用于绕过一些检查
  repeated SpawnPointInfo spawnPointInfos = 51; // 出生点信息
  optional int32 dataStoreSaveType = 52; // 数据存储类型
  optional bool isContainDiyArms = 53;  // 是否包含自制装备
  optional int32 playRuleOMD = 54;  // 兽人类型
  repeated UgcAchievementIndex achievementIndexList = 55;  // 成就列表
  optional int32 omdLevel = 56;  // 兽人关卡难度
  optional bool isLuaCoding = 57;    // 是否使用Lua编程字段
  optional int32 source = 58;  //地图来源
  repeated UgcMapExtraConfigIndex extraConfigIndexList = 59;
  optional MapLoadingInfo mapLoading = 60;  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 61;  // 乐园地图自定义封面
  optional string qqMusicInfo = 72; // qq音乐存储在服务器
  optional UgcPublishInputParam publishParam = 73; // ugc背景音乐设置，最大5个，数组索引表示顺序
  optional int32 gameTime = 74; //游戏时长
}

message RpcMapPublishRes {
  optional int32 result = 1;
  repeated int64 uid = 2;
  optional string editorName = 3;  //共创作者
  optional string mapName = 4;  //共创名字
}

message RpcMapPublishListReq {
  option (region_msg) = true;
  optional int32 page = 1;
  optional UgcInstanceType mapType = 2;  //地图类型 UgcInstanceType
  optional bool isLocalList = 3;
}

message RpcMapPublishListRes {
  optional int32 result = 1;
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
}

message RpcMapCheckMd5Req {
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional string bucket = 2;
  repeated UgcMapMetaInfo metaInfo = 3; //信息
  optional int64 creatorId = 4;
}

message RpcMapCheckMd5Res {
  optional int32 result = 1;
}


message RpcOperateMapReq {
  option (region_msg) = true;
  optional uint64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional uint64 ugcId = 2; // ugcId
  optional UgcOpType opType = 3; // 对应 common.proto 里面UgcOpType
  optional string name = 4; // name
  optional string avatar = 5; // avatar
  optional string openId = 6; // openId
  optional int32 platId = 7; //
  optional int32 LoginType = 8;
  optional int32 areaType = 9;     //大区
  optional int64 uid = 10;
  optional UgcInstanceType mapType = 11;   //地图类型
  optional bool isSelfMap = 12;   //是否自己的作品
  optional UgcMapSubType subType = 13; //子类型
  optional int32 worldId = 14[(field_region_key) = true];
  optional UgcResParam ugcResParam = 15;  // 资源参数
  optional int32 operateSvrSource = 16;   // 操作服务来源, 见枚举OperateSvrSourceType所示
}

message RpcOperateMapRes {
  optional int32 result = 1;
  optional int32 opCount = 2;
  optional int64 editorUid = 3;
  optional string editorOpenid = 4;
  optional int64 opTime = 5;
  repeated UgcMapMetaInfo metaInfo = 6; //关卡信息
}

message RpcGetLevelInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional ApplyType type = 2;
  optional int64 mapId = 3;
  optional bool needPublish = 4;
}

message RpcGetLevelInfoRes {
  optional int32 result = 1;
  optional UgcKeyInfo info = 2;
  optional string bucket = 3;
  optional string region = 4;
  optional string mapTags = 5;
  optional int64 creatorUid = 6;
  optional int32 templateId = 7;
  optional string ugcVersion = 8;
  optional string clientVersion = 9;
  optional UgcMapMetaInfo metaInfo = 10;
  repeated int64 layerId = 11;
  repeated UgcAchievementIndex achievementIndexList = 12;  // 成就列表
  optional int64 curLayerId = 13; //当前所在图层id
  repeated UgcMapMetaInfo metaInfos = 14;
  repeated UgcLayerInfo layerList = 15; //图层
  optional int32 difficulty = 16;     // 地图评级
  repeated UgcMapExtraConfigIndex extraConfigIndexList = 17;
  optional MapLoadingInfo mapLoading = 18;  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 19;  // 乐园自定义封面
}

message RpcGetUgcPlayerInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 self = 2;
}

message RpcGetUgcPlayerInfoRes {
  optional int32  result = 1;        // result
  optional uint64 ugcExp = 2;        // 工匠值
  optional int32  ugcLv = 3;        // 工匠等级
  optional UgcTasks ugcTasks = 4;    // 工匠任务
  optional int64  ugcSeasonExp = 5;  // 赛季工匠值
  optional int64  uid = 6;  // uid
  optional string  nickName = 7;  // 昵称
}

message RpcApplyPageListReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional UgcRecommendType type = 3;  //推荐类型
  optional int32 page = 4;
  optional int32 mapType = 5;         //地图类型 竞速
  optional string tag = 6;
  optional string lbs = 7;
  optional int32 isAdult = 8;
  optional UgcInstanceType instanceType = 9;
  optional int32 isGuide = 10; //是否是新手
  optional bool isLocalList = 11;
  repeated int32 multiTags = 12;  // 多标签，目前只有组合使用
  optional int32 loginType = 13 ;   //登录方式
  optional int32 areaType = 14;     //大区
  optional int64 uid = 15;
  optional string bucket = 16;
  optional int32 deviceLevel = 17;
  optional UgcRedDotInfo redDotInfo = 18;
  repeated int64 refreshMapId = 19;
  optional UgcRecommendGameInfo gameInfo = 20;  // UgcBattleSettlementRecommend结算推荐参数
  optional UgcResParam ugcResParam = 21;  // 资源参数
  optional TopicInfo topicInfo = 22; // 话题
  optional int32 sortType = 23; // 排序类型
}

message UgcRedDotInfo {
  optional bool needRedDot = 1;
  optional int64 lastGetSubPlayerMapsTime = 2;
  optional int64 getSubPlayerMapsTime = 3;
}

message RpcApplyPageListRes {
  optional int32 result = 1;
  repeated PublishItem items = 2;               // publish 作品
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 5;
  repeated UgcItem ugcItems = 6;                // brief作品 目前只有组合使用
  repeated UgcGroupCommon localCommonItems = 7; // 组件 目前只有组合使用
  repeated UgcMapScreenType redDotType = 8;
  repeated HotPlayList typeList = 9;
  optional int32 hasMorePage = 10; //1:表示还有下一页,0没有下一页
  repeated UgcHomePageRecommendData collectionData = 11;
  optional int32 subscribeStatus = 12; // 0=未订阅；1=已订阅
}

message RpcSearchMapReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int32 page = 3;
  optional string keyWord = 4;
  optional UgcInstanceType instanceType = 5;
  repeated int32 tags = 6; // 标签
  optional int32 loginType = 7 ;   //登录方式
  optional int32 areaType = 8;     //大区
  optional int64 uid = 9;
  optional UgcResParam ugcResParam = 10; // 资源参数

}

message RpcSearchMapRes {
  optional int32 result = 1;
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 4;
  optional PublishItem foundResItem = 5;   // 命中的资源id(废弃)
}

message RpcMapTakeOffReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64  ugcId = 2;
  optional string openId = 3;
  optional string reason = 4;
  optional string source = 5;
  optional int32 allPublishMapCount = 6;
  optional int32 platId = 7;
  optional UgcInstanceType instanceType = 8;
  optional int32 worldId = 9 [(field_region_key) = true];
  optional int64 uid = 10;
  optional UgcResParam UgcResParam = 11;   // 资源参数
  optional bool fromPlat = 12;  // 是否来自管理端的下架请求
  optional int32 reqFrom = 13;   //0:默认 1:管理端操作 2:安全审核
}

message RpcMapTakeOffRes {
  optional int32 result = 1;
}

message RpcMapTakeOffApplyReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64  ugcId = 2;
  optional int32 worldId = 3 [(field_region_key) = true];
  optional int64 uid = 4;
  optional string reason = 5;
}

message RpcMapTakeOffApplyRes {
  optional int32 result = 1;
}

// Ugc 发布界面详情
message RpcUgcPublishDetailsReq {
  option (region_msg) = true;
  optional int64 creatorId = 1  [(field_hash_key) = true];   //uid
  optional int64 ugcId = 2 ;   //uid
  optional int32 loginType = 3 ;   //登录方式
  optional int32 areaType = 4;     //大区
  optional bool auditVersion = 5; // 是否请求审核版本
}

message RpcUgcPublishDetailsRes {
  optional int32 result = 1;    //返回结果
  optional MapDetails details = 2; //详情
  optional PublishItem item = 3;  // 发布信息, 仅对UgcInstanceType=ResInstance有效
}

// Ugc 发布界面详情
message RpcBatchGetUgcPublishDetailsReq {
  option (region_msg) = true;
  optional int64 creatorId = 1  [(field_hash_key) = true];   //uid
  repeated int64 ugcIds = 2 ;
}

message RpcBatchGetUgcPublishDetailsRes {
  optional int32 result = 1;    //返回结果
  map<int64, MapDetails>  details = 2; //详情,  ugcId:MapDetails
}

message RpcApplyMapDataReq {
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
}

message RpcApplyMapDataRes {
  optional int32 result = 1;
  optional bytes mapData = 2;
  optional string md5 = 3;
  optional UgcKeyInfo keyInfo = 4;
}

message RpcUpdateUgcExpReq {
  // option (region_msg) = true; 这个是ugcsvr内部的消息, 就不能用这个option
  option (rpc_one_way) = true;
  optional int64 creatorId = 1;// 地图的拥有者的uid
  optional int64 ugcId = 2;
  optional int32 changeReason = 3; // 协议改造 枚举值切换至整数值 详情参考MapExpType
}

message RpcUpdateUgcExpRes {
  optional int32 result = 1;
  optional int64 ugcMapExp = 2;          //工匠值
}

message MapUgcExpInfo {
  optional int64 ugcId = 1;           //ugcid
  optional int64 ugcExp = 2;          //工匠值
  optional int32 isCurrentSeason = 3; //是否是当前赛季创建，0不是，1是
}

message RpcGetMapUgcExpReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  repeated int64 ugcIds = 2;
}

message RpcGetMapUgcExpRes {
  optional int32 result = 1;
  repeated MapUgcExpInfo mapInfoList = 2;
}

message RpcOperateCancelMapReq {
  option (region_msg) = true;
  optional uint64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional uint64 ugcId = 2; // ugcId
  optional UgcOpType opType = 3; // 对应 common.proto 里面UgcOpType
  optional string openId = 4; // openId
  optional int32 platId = 5;
  optional int32 loginType = 6;
  optional int32 areaType = 7;     //大区
  optional int64 uid = 8;
  optional UgcInstanceType mapType = 9;   //地图类型
  optional bool isSelfMap = 10;   //是否自己的作品
  optional UgcMapSubType subType = 11; //子类型
  optional int32 worldId = 12[(field_region_key) = true];
  optional UgcResParam ugcResParam = 13;  // 资源参数
  optional int32 operateSvrSource = 16;   // 操作服务来源, 见枚举OperateSvrSourceType所示
}

message RpcOperateCancelMapRes {
  optional int32 result = 1;
  optional int32 opCount = 2;
  optional int64 editorUid = 3;
  optional string editorOpenid = 4;
  optional int64 opTime = 5;
  repeated UgcMapMetaInfo metaInfo = 6; //关卡信息
}

message RpcOpSubTopReq {
  option (region_msg) = true;
  optional uint64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional uint64 topCreatorId = 2; // 被置顶uid
  optional int32 type = 3; //置顶uid
}

message RpcOpSubTopRes {
  optional int32 result = 1;
}

message RpcOpSubHandlerReq {
  option (region_msg) = true;
  optional uint64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional uint64 subCreatorId = 2; // 被订阅者id
  optional int32 type = 3; //0:订阅 1：取消订阅
  optional int32 worldId = 4[(field_region_key) = true];
}

message RpcOpSubHandlerRes {
  optional int32 result = 1;
  optional int32 opCount = 2;
  optional int64 editorUid = 3;
  optional string editorOpenid = 4;
  optional int64 opTime = 5;
}


message RpcMapUgcBestRecordReq {
  option (region_msg) = true;
  optional uint64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional int32 passSec = 2; // 通关秒数
  optional int64 ugcId = 3; // 通关地图id
  optional bool isPass = 4; // 是否通关
  optional string openid = 5;
  optional int32 platId = 6;
  optional int32 loginType = 7;
  optional string nickName = 8;   //昵称
  optional string avatar = 9;     //头像
  optional int32 areaType = 10;     //大区
  optional int64 uid = 11;      // 管理端需要
  optional int32 gameSource = 12;//游戏来源
  optional int32 logicMapSource = 13;
  optional string fromCollectionId = 14;
  optional bool isMidJoin = 15;  // 是否中途加入
  optional bool isUgcApp = 16;  // 独立app请求
  optional int64 lastEndBattleTime = 17;
  optional int64 passScore = 23; // 通关积分 如兽人的得分数
  optional int64 appGameCreatorId = 24; // 独立app时游戏测的creatorID
  optional string appGameOpenId = 25;// 独立app时游戏测的openID
  optional int32 multiRoundScore = 26;  // 多轮次地图积分
}

message RpcMapUgcBestRecordRes {
  optional int32 result = 1;
  optional int32 playedMapCount = 2; // 游玩地图数量
  optional string mapTags = 3;
  optional int64 creatorUid = 4;
  optional int32 templateId = 5;
  optional string mapName = 6;
  optional string mapBucket = 7;
  optional string mapDesc = 8;
  optional int64 roleId = 9;
  optional string openId = 10;
  optional int64 recodCreatorId = 11;
  optional string recordName = 12;
  optional int32 playCount = 13;
  optional int32 passCount = 14;
  optional int32 succRate = 15;
  optional int32 playerCount = 16;
  optional bool isFirstPlay = 17;
  optional bool isBestRecord = 18; // 是否打破这个地图的最佳记录
  optional string ugcVersion = 19; // ugcVersion
  optional int64 mapCreatorId = 20;
  optional bool appreciatePlay = 21;
  optional int32 appreciateTaskCount = 22;
  optional int32 modelType = 23;
}

message RpcMapUgcBaseInfoReq {
  option (region_msg) = true;
  optional uint64 ugcId = 1;
  optional bool isMulTest = 2; //弃用
  optional int32 playType = 3; // 多人测试 // 协议改造 枚举值切换至整数值 详情参考UgcPlayType
  optional int32 source = 4; // 调用来源// 协议改造 枚举值切换至整数值 详情参考UgcRcpSourceType
  optional int64 version = 5;
  optional bool auditVersion = 6; // 是否请求审核版本
  optional int64 creatorId = 7; // 创作者Id，用来拉取玩家的已发布列表里的地图
}

message RpcMapUgcBaseInfoRes {
  optional int32 result = 1;
  optional UgcBaseInfo info = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional string bucket = 4;
  optional string region = 5;
  optional string mapKey = 6;
  repeated BattleCamp camps = 7;
  optional string pubPath = 8; // 因多场景功能调整--》主关卡发布路径
  optional string fileName = 9; // 因多场景功能调整--》主关卡名字
  optional bool isCampsOpen = 10; //阵营是否开启
  optional string clientBucket = 11;
  optional int32 pointNumber = 12;  // 出生点人数
}

message RpcMapUgcBaseInfoBatchReq {
  option (region_msg) = true;
  message UgcMapInfo {
    optional uint64 ugcId = 1;
    optional int32 version = 2;
  }
  repeated UgcMapInfo ugcMapInfo = 1;
  optional int32 source = 2;
}

message RpcMapUgcBaseInfoBatchRes {
  optional int32 result = 1;
  repeated UgcBaseInfo info = 2;
}

message LobbyUgcMapShowInfo {
  optional int64 ugcId = 1;
  optional int32 templateId = 2;  //模板Id
  optional UgcMdList mdList = 3; //mdList
  repeated EditorItemInfo creators = 4;   // 作者信息
  optional int32 loadingTemplateId = 5;
  optional string clientVersion = 6; 
  optional bool isOpenSave = 7; // 是否开启存档
  optional string ugcVersion = 8; // ugcVersion
  optional string name = 9; //地图名
  optional string difficulty = 10; // 地图难度
  optional MapLobbyCoverInfo lobbyCover = 11;  // 乐园封面
  optional MapLoadingInfo mapLoading = 12;  // 自定义loading
  optional int64 editorCreatorId = 13;         //作者creatorId

  optional string mapKey = 21;
  optional string fileName = 22; // 因多场景功能调整--》主关卡名字
  optional string clientBucket = 23;
}

enum GetTestUgcListType {
  GTULT_PlayerTestUgc = 1; //获取玩家自己的自测UGC大厅地图
  GTULT_AllInspectorTestUgc = 2;  //获取所有的未评审通过的自测UGC大厅地图
}

message RpcGetTestUgcListInfoReq {
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 getType = 2; // 枚举 GetTestUgcListType
  optional int32 startIndex = 3; //从1开始
  optional int32 count = 4;
}

message RpcGetTestUgcListInfoRes {
  optional int32 result = 1;
  repeated LobbyUgcMapShowInfo maps = 2; 
}

message RpcFindTestUgcListInfoReq {
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 getType = 2; // 枚举 GetTestUgcListType
  optional string mapName = 3; //地图id
  optional int32 count = 4; //最大查询的数量
  optional int32 startIndex = 5; //需要查询的开始索引，1开始
}

message RpcFindTestUgcListInfoRes {
  optional int32 result = 1;
  repeated LobbyUgcMapShowInfo maps = 2; 
}

message UgcBaseInfo {
  optional int64 ugcId = 1;  //地图id
  optional string name = 2;  //名字
  optional string desc = 3;  //描述
  optional int64 createTime = 4;  //发布时间
  optional string editorName = 5;  //作者名字
  optional int32 templateId = 6;  //模板Id
  optional int64 oldUgcId = 7; //下载id
  optional string tags = 8;  //tags
  optional int32 pointsNumber = 9;  //出生点人数
  optional UgcMdList mdList = 10; //mdList
  optional string editorAvatar = 11;  //作者头像
  optional int32 ugcType = 12; // ugc类型，UGCMapType
  optional SafeStatus status = 13; // SafeStatus
  repeated int64 separateFactor = 14;   // 分包因子 (分包下载功能使用)
  repeated EditorItemInfo creators = 15;   // 作者信息
  optional CreatorAccountInfo creatorAccountInfo = 16;
  optional int32 loadingTemplateId = 17;
  repeated UgcMapTopic topics = 18;
  optional string clientVersion = 19;
  optional int32 disableMultiTest = 20; //封禁多人测试
  optional bool isOpenSave = 21; // 是否开启存档
  optional UgcMapBuyGoodsStatus buyGoodsStatus = 22; // 购买星钻商品状态
  optional string ugcVersion = 23; // ugcVersion
  optional int32 instanceType = 24; // 实例类型，地图、组合、小窝、资产等
  repeated int64 banResIdList = 25; // 封禁资产列表
  optional bool openSingleSaveDB = 26;  // 单机模式存储标识  实际上是单机给开ds然后还给存档的标识
  optional bool isPrivateState = 27; // 是否私创状态
  optional int32 markId = 28;//优秀markId
  optional string prizeId = 29;
  optional string prizeInfo = 30;
  optional string markString = 31;       //标志文本
  optional bool isAllowMidJoin = 32;  // 允许中途加入
  optional int64 editorCreatorId = 33;  //作者creatorId
  optional int32 ugcAchievementSize = 34;
  repeated UgcLayerInfo layerInfo = 35; // 多场景场景信息
  optional bool isMultiTest = 36; // 是否是多人测试地图
  optional int64 creatorUid = 37;  // 创作者的uid
  optional string difficulty = 38; // 地图难度
  optional MapLoadingInfo mapLoading = 39;  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 40;  // 乐园封面
  optional UgcPublishInputParam publishInputParam = 41;
}

// 获取玩家ugc的个人信息
message RpcGetUgcPlayerProfileReq {
  option (region_msg) = true;
  optional uint64 reqCreatorId = 1 [(field_hash_key) = true]; // reqUid
  repeated int64 creatorIdList = 2; // uidlist
  optional bool isSelf = 3;
}

message RpcGetUgcPlayerProfileRes {
  optional int32 result = 1;
  repeated UgcPlayerProfile ugcPlayerProfileList = 2; // 返回结果
}

// 获取订阅的人的地图们
message RpcGetSubscribePlayerMapsReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional int32 page = 2; //第几页
  optional int32 LoginType = 3;
  optional int32 areaType = 4;     //大区
  optional int64 reqCreatorId = 5;
}

message RpcGetSubscribePlayerMapsRes {
  optional int32 result = 1;
  optional int32 hasMorePage = 2; //1:表示还有下一页,0没有下一页
  repeated PublishItem ugcMapProfileList = 3; // 返回结果
  repeated uint64 collectMapIdList = 4; // 返回结果里面收藏的地图id列表
  optional UgcKeyInfo keyInfo = 5;
}

message RpcGetPlayedMapsReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional int32 page = 2; //第几页
  optional int32 LoginType = 3;
  optional int32 areaType = 4;     //大区
  optional int32 sceneType = 5; // 0-默认  1-idip
  optional int32 sortMapType = 6;  // 对应UGCMapType, 目前只支持:兽人(36)
  optional int32 pointsNumber = 7;  // 地图人数, 目前只支持:兽人(36)
  optional int32 source = 8;  // 请求来源，对应UgcTableBatchGetSource
  optional int32 mapFilterType = 9; // 生效类型:PlayedMaps   0全部 1存档 2通关
}

message RpcGetPlayedMapsRes {
  optional int32 result = 1;
  optional int32 hasMorePage = 2; //1:表示还有下一页,0没有下一页
  repeated PublishItem ugcMapProfileList = 3; // 返回结果
  repeated uint64 collectMapIdList = 4; // 地图id列表，sceneType为1时返回
  optional UgcKeyInfo keyInfo = 5;
}

message RpcGetUgcPublishMapReq {
  option (region_msg) = true;
  optional int64 creatorId = 1[(field_hash_key) = true];
  repeated int64 mapIdList = 2;
  optional bool needUgcKey = 3;
  optional bool isOpenLocalList = 4;
  optional string fromCollectionId = 5;
  optional int32 source = 6;  // 请求来源，对应UgcTableBatchGetSource
}

message RpcGetUgcPublishMapRes {
  optional int32 result = 1;
  repeated PublishItem mapInfos = 2;
  optional UgcKeyInfo keyInfo = 3;
  repeated UgcPlayerProfile ugcPlayerProfileList = 4;
}

message RpcGetPublishMapReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional int32 page = 2; //第几页
  optional int64 editorCreatorId = 3;
  optional GetMapsSortType sortType = 4;
  optional int32 sceneType = 5; // 0默认 1星世界订阅作者地图列表
  optional int32 pageCount = 6;
}

message RpcGetPublishMapRes {
  optional int32 result = 1;
  optional int32 hasMorePage = 2; //1:表示还有下一页,0没有下一页
  repeated PublishItem mapInfos = 3;
  optional UgcKeyInfo keyInfo = 4;
}

// 获取收藏的地图
message RpcGetCollectMapsReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional int32 page = 2; //第几页
  optional int32 collectType = 3; //UgcInstanceType
  optional GetMapsSortType sortType = 4;
  optional int32 sortMapType = 7;
  optional int32 pointsNumber = 8;
  optional int32 source = 9;  // 请求来源，对应UgcTableBatchGetSource
}

message RpcGetCollectMapsRes {
  optional int32 result = 1;
  optional int32 hasMorePage = 2; //1:表示还有下一页,0没有下一页
  repeated PublishItem ugcMapProfileList = 3; // 返回结果
  optional UgcKeyInfo keyInfo = 5;
  optional int32 collectMapsCount = 6;
}

enum UgcInfoType {
  PlayerLogin = 1;
  PlayerLogout = 2;
  PlayerChangeName = 3;
  PlayerChangeProfile = 4;
  PlayerChangeDress = 5;
}

message RpcUgcInfoNtfReq {
  option (region_msg) = true;
  option (rpc_one_way) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional UgcPlayerProfile playerProfile = 2;
  optional bool todayFirstLogin = 3;
  optional int32 type = 4;// 协议改造 枚举值切换至整数值 详情参考UgcInfoType
}

message RpcUgcCreateMapNtfReq {
  option (region_msg) = true;
  option (rpc_one_way) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 mapId = 2;
}


message RpcCosCopyReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 sourceUid = 2;
  optional string bucket = 3;
  repeated int64 ugcId = 4;
  optional bool isPublish = 5;
}

message RpcCosCopyRes {
  optional int32  result = 1;        // result
}

// 获取ugc图片cos url请求
message RpcGetUgcPicCosUrlReq {
  option (region_msg) = true;
  optional string key = 1;          // ugc图片key
  optional string bucket = 2;       // ugc图片bucket
}

// 获取ugc图片cos url响
message RpcGetUgcPicCosUrlRes {
  optional int32 result = 1;        // result
  optional string cosUrl = 2;       // ugc图片cos url
}

//--------------工坊协议------------------
//保存关卡信息
message RpcSaveLevelInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;     //地图id
  optional int32 editorSec = 3; //编辑时长
  optional UgcGroupIdList groupIds = 4;   //组合id
  optional int32 saveCount = 5;  //保存次数
  optional bool isFirstSave = 6;  //是否首次保存
  repeated UgcMapMetaInfo metaInfos = 7; //关卡信息
  optional UgcInstanceType instanceType = 8; //1、普通 2、共创 3、组合
  optional bool isAutoSave = 9; //是否自动保存
  optional string ugcVersion = 10; // 当前ugc版本号
  optional string clientVersion = 11;  // 是否自动保存
  optional int32 difficulty = 12;     // 地图评级
  optional int64 updateTime = 13;     // 更新时间
  optional UgcResParam ugcResParam = 14; // 资源参数
  optional SaveType saveType = 15;    //对应common中的 saveType
  optional CreateStruct createStruct = 16; // 建档、覆盖
  repeated int64 resIdList = 17; //使用的资产id
  optional UgcMapSetData mapSet = 18;  //设置里面数据
  optional int32 templateId = 19;  //templateId
  repeated UgcAchievementIndex achievementIndexList = 20;  // 成就列表
  optional int32 idipArea = 21;
  optional int32 curLayerId = 22;  //当前所在场景
  repeated UgcMapExtraConfigIndex extraConfigIndexList = 23;
  optional MapLoadingInfo mapLoading = 24;  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 25;  // 乐园自定义封面
  optional bool mapLoadingCanNotSave = 26;  // 是否可以保存自定义loading
  optional bool lobbyCoverCanNotSave = 27;  // 是否可以保存自定义loading
  optional UgcLayerList layers = 28;// 图层数据
  optional bool isResetCodingData = 29;	// 是否重置扣叮数据
  optional CodingDataInfo codingData = 30;	// 当前已更新扣叮数据
}

message RpcSaveLevelInfoRes {
  optional int32  result = 1;   //结果
  optional UgcInstanceType instanceType = 2; //1、普通 2、共创 3、组合
}

//创建地图
message RpcCreateUgcEntityReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional UgcCreateInfo info = 2;            // 创建信息
  optional int32 idipArea = 3;                // idip area
  optional bool isAiGen = 4;                  // 是否是AI生成的
  optional bool toolGenFlag = 5;              // 工具发布的，用于绕过一些检查
  optional string traceStr = 6;               // 转换统一模板  旧ugcId_templateId
  optional bool isApplyCosKey = 7;            // 是否申请cos key, 用于ugcPlatSvr接口调用
  optional int32 cosKeyApplyReason = 8;       // 申请pbin文件cos key原因, 用于ugcPlatSvr接口调用
  optional int32 coverCosKeyApplyReason = 9;  // 申请cover文件cos key原因, 用于ugcPlatSvr接口调用
}

message RpcCreateUgcEntityRes{
  optional int32 result = 1;            // 0:成功
  optional UgcKeyInfo keyInfo = 2;      // pbin文件cos key
  optional UgcKeyInfo coverKeyInfo = 3; // cover文件cos key
}


//复制地图
message RpcCopyUgcEntityReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional UgcCreateInfo info = 2;      //创建信息
  optional int64 oldUgcId = 3;      //源地图id
}

message RpcCopyUgcEntityRes{
  optional int32 result = 1; // 0:成功
}

//修改发布地图
message RpcModifyPublishReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;  // ugcId
  optional string name = 3;  //名字
  optional string desc = 4;  //描述
  optional string tags = 5;  //标签
  optional UgcInstanceType instanceType = 6;  // 1、普通 2、共创 3、组合
  optional string openId = 7;  //openId
  optional int64 uid = 8;
  optional UgcResModifyPubParam ugcResModifyPubParam = 9; // 资源修改参数
}

message RpcModifyPublishRes{
  optional int32 result = 1; // 0:成功
}

//修改地图信息
message RpcModifyUgcBaseInfoReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional UgcModifyInfo modifyInfo = 2;  // ugcId
}

message RpcModifyUgcBaseInfoRes{
  optional int32 result = 1; // 0:成功
}
//地图列表
message RpcUgcEntityListReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string bucket = 2 ;
  optional bool isLoginSource = 3;
  optional int64 uid = 4;
  optional UgcInstanceType instanceType = 5;
  optional bool isNeedResBrief = 6;
  optional int32 page = 7;
}

message RpcUgcEntityListRes{
  optional int32 result = 1; // 0:成功
  repeated UgcItem maps = 2; //ugc列表
  repeated UgcItem coCreateMaps = 3; //共创列表(废弃)
  optional int32 draftValue = 4; //草稿箱上限
  optional int32 coCreateDraftValue = 5; //联合共创上限
  optional int32 pubValue = 6; //发布上限
  optional int32 recycleValue = 7; //回收站上限

  optional int64 secStartTime = 8; //旧地图更新 开始时间
  optional int64 secEndTime = 9; //旧地图更新 结束时间
  optional int32 trustworthy = 10; //0:不是 1:是
  optional int32 allPage = 11;   //总页数
}

// 获取随机昵称
message RpcNickPromptReq {
  option (region_msg) = true;
  optional int64 creatorId = 1  [(field_hash_key) = true];
}
message RpcNickPromptRes {
  optional int32 ret = 1; // 0:成功
  optional string nick = 2;
}

// 设置昵称
message RpcUseNickReq {
  option (region_msg) = true;
  optional int64 creatorId = 1  [(field_hash_key) = true];
  optional string nick = 2;
  optional string oldNick = 3;
}
message RpcUseNickRes {
  optional int32 ret = 1; // 0:成功
  optional string nick = 2;
}

// 昵称可用性检测(重名, tss 等检测)
message RpcNickAvailableReq {
  option (region_msg) = true;
  optional int64 creatorId = 1  [(field_hash_key) = true];
  optional string nick = 2;
}
message RpcNickAvailableRes {
  optional int32 ret = 1; // 0:成功
}

message RpcGMSetMapInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 loginType = 2;
  optional int64 mapId = 3;
  optional int32 changeType = 4;
  optional int32 changeValue = 5;
}

message RpcGMSetMapInfoRes {
  optional int32 result = 1; // 0:成功
}

message RpcUpdatePlayerUgcExpAndUgcLvNtfReq {
  option (rpc_one_way) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional bool onlyMark = 2; // 这个值是true的时候只是mark一下, 不是真的更新计算
}

// idip修改地图信息请求
message RpcIdipModifyMapInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;             // ugcId
  optional string openId = 3;           // openId
  optional int32 platId = 4;            // platId
  optional uint32 modifyType = 5;       // 修改类型, 详情见idip_msg.proto文件中的枚举IdipMapInfoModifyType所示
  optional string modifyContent = 6;    // 修改内
  optional int64 uid = 7;
}

// idip修改地图信息响应
message RpcIdipModifyMapInfoRes {
  optional int32 result = 1; // 0:成功
}

message RpcChangeUgcPublishNameReq {
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional int32 changeType = 2;// 协议改造 枚举值切换至整数值 详情参考UgcPublishChangeType
  optional string name = 3;
  optional UgcInstanceType ugcType = 4;
  optional int32 worldId = 5 [(field_region_key) = true];
  optional int64 uid = 6;
  optional string desc = 7;
}

message RpcChangeUgcPublishNameRes {
  optional int32 result = 1; // 0:成功
}

message RpcGMClearPlayerUgcExpReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
}

message RpcGMClearPlayerUgcExpRes {
  optional int32 result = 1;
}

message RpcGMClearPlayerReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional bool isCreatorInfo = 2;
  optional string openId = 3;
  optional int32 platId = 4;
}

message RpcGMClearPlayerRes {
  optional int32 result = 1;
}

// 客户端弹窗等状态扭转
message RpcUgcStageChangeReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;                     // ugcId
  optional string openId = 3;                   // openId
  optional UgcStageChangeType changeType = 4;   // 变更类型
  optional UgcInstanceType mapType = 5;         // 地图类型
  optional UgcResType resType = 6;              // 资源类型
}

message RpcUgcStageChangeRes {
  optional int32 result = 1;       // 结果 0=success
}

message RpcDownLoadPublishKeyInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1[(field_hash_key) = true];
  optional int64 ugcId = 2;                     // ugcId
  optional bool isMulTest = 3; // 是否多人测试
  optional bool isGoodsAudit = 4; // 是否商审人员
}

message RpcDownLoadPublishKeyInfoRes {
  optional int32 result = 1;
  optional int64 ugcId = 2;   //地图id
  optional string bucket = 3;
  optional string region = 4;
  optional UgcKeyInfo keyInfo = 5;
  optional string km = 6;
}

// 获取 白名单，组合社区开关等信息 (gamesvr -> ugcsvr)
message RpcUgcGetGroupCommunityInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1  [(field_hash_key) = true];
  optional int64 uid = 2;
  optional string openId = 3;
}

message RpcUgcGetGroupCommunityInfoRes {
  optional bool isInWhiteList = 1;
  optional bool isGroupCommunityOpen = 2;
  optional int32 draftLimit = 3;
  optional int32 pubLimit = 4;
}

// 设置组合社区的开关 (ugcplatsvr->ugcsvr)
message RpcUgcSetGroupCommuntiyOpenReq {
  optional bool isOpen = 1;
}

message RpcUgcSetGroupCommuntiyOpenRes {
  optional int32 result = 1;
}

// 清理用户昵称请求
message RpcDeleteNickNameReq {
  option (region_msg) = true;
  optional int64 creatorId = 1  [(field_hash_key) = true];
  optional string nickName = 2;
}

// 清理用户昵称响应
message RpcDeleteNickNameRes {
  optional int32 result = 1;
}

//更新地图信息
message RpcUgcUpdatePublishMetaReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 metaId = 2;     //metaId
  optional int64 ugcId = 3;     //metaId
  repeated UgcMapMetaInfo info = 4;
  optional string desc = 5;
  optional string tags = 6;
  optional UgcInstanceType instanceType = 7;
  optional int64 uid = 8;
  optional string clientVersion = 9;
  repeated UgcMapMetaInfo briefInfo = 10;
  optional int32 pointNumber = 11;
  optional bool isEditable = 12; //是否可编程 （单纯记录 策划需求不明确 不与tags一起存放）
  optional bool IsOpenHealth = 13; //是否打开生命值
  repeated UgcGoodsInfo ugcGoodsInfo = 14;    // 上架商品信息
  optional bool isOpenSave = 15; // 是否开启存档
  repeated int64 resIdList = 43; //使用的资产id
  optional bool isDanMuUnable = 16;  // 是否打开弹幕
  optional bool isAllowMidJoin = 17;  // 是否允许中途加入
  optional UgcGroupIdList ugcGroupIdList = 18; // 地图中使用到的发布组合
  repeated MapCoverInfo covers = 19;   //多封面缩略图
  repeated UgcRankInfo rankInfo = 20;      // 排行榜基本信息
  optional UgcExtraInfo ugcExtraInfo = 21; // 额外信息
  optional UgcMapSetData mapSet = 22;  //地图设置数据
  repeated BattleCamp camps = 23;
  optional int32 editorSec = 24;
  optional bool isCampsOpen = 25; //阵营是否开启
  repeated SpawnPointInfo spawnPointInfos = 51; // 出生点信息
  optional int32 dataStoreSaveType = 52; // 存档类型
  optional bool isContainDiyArms = 53;  // 是否包含自制装备
  optional int32 occupancyValue = 54; //地图占用值
  optional int32 propertyScore = 55; //性能评分
  optional int32 actorCount = 56; //actor数量
  optional int32 playRuleOMD = 57;  // 兽人类型
  repeated UgcAchievementIndex achievementIndexList = 58;  // 成就列表
  optional int32 omdLevel = 59;  // 兽人关卡难度
  optional int32 templateId = 60;  // 修改模版id 只有家园功能在用！！
  optional bool isResetPassRecord = 61;  // 是否重置通关记录, 针对竞速类地图/兽人ugc地图生效
  repeated UgcMapExtraConfigIndex extraConfigIndexList = 62;
  repeated UgcEditorInfo editors = 63; // 共创作者
  optional MapLoadingInfo mapLoading = 64;  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 65;  // 乐园地图自定义封面
  optional string qqMusicInfo = 72; // qq音乐存储在服务器
  optional UgcPublishInputParam publishParam = 73; // 通用发布参数
  optional int32 gameTime = 74; //游戏时长
}

message RpcUgcUpdatePublishMetaRes{
  optional int32 result = 1; // 0:成功
}

// 获取订阅、粉丝
message RpcGetOpPlayerReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional int32 page = 2; //第几页
  optional int32 type = 3; //0 订阅 1粉丝
  optional int64 lastOpenViewTs = 4; // 上次打开界面的时间
  optional bool isRefreshNewFanNum = 5;
  optional int32 reqSceneType = 6;  // 0默认 1请求红点  2星世界订阅列表
}

message RpcGetOpPlayerRes {
  optional int32 result = 1;
  optional int32 hasMorePage = 2; //1:表示还有下一页,0没有下一页
  repeated UgcOpPlayerInfo infos = 3;
  optional int32 opPlayerCount = 4;
}

message RpcGmBatchUpdateMapDescReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // uid
}

message RpcGmBatchUpdateMapDescRes {
  optional int32 result = 1;
  repeated int64 sucList = 2;
  repeated int64 failList = 3;
}

message RpcGmUpdateUgcMapStatusReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional int64 ugcId = 2;  // 地图ugcId
  optional int32 status = 3; // 状态
  optional int64 uid = 4;
}

message RpcGmUpdateUgcMapStatusRes {
  optional int32 result = 1;
}

message RpcCmdClearMetaInfoReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional int64 ugcId = 2;  // 地图ugcId
}

message RpcCmdClearMetaInfoRes{
  optional int32 result = 1;
}

// ugc -> game 数据互通 - game请求ugc
message RpcGetCreatorInfoReq {
  option (region_msg) = true;
  option (rpc_call_timeout_seconds) = 3;
  optional int64 creatorId = 1  [(field_hash_key) = true];
  optional UgcCreatorInfo req = 2;
  optional DataUploadToUgcSvr uploadData = 3;
  optional QueryMapReq queryMapReq = 4;

  message DataUploadToUgcSvr {
    optional UgcPlayerProfile playerProfile = 1;
  }
  message QueryMapReq {
    repeated UgcInstanceType instanceTypes = 1;
    optional bool isCreator = 2;
    optional bool isHome = 3;
  }
}
message RpcGetCreatorInfoRes {
  optional UgcCreatorInfo res = 1;
  repeated UgcItem maps = 2; //ugc列表
}

//——————————共创——————————————————————
message RpcApplyLayerReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  optional int32 count = 3;
  optional UgcInstanceType instanceType = 4;
  optional VersionType version = 5; // VersionType
  optional string layerName = 6; // 场景名称
  optional string desc = 7;
  optional int32 pointNumber = 8;
  optional int64 templateId = 9;
}

message RpcApplyLayerRes{
  optional int32 result = 1; // 0:成功
  repeated int32 layerId = 2;
  optional UgcLayerList layers = 3;
}

message RpcAccreditLayerReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  optional int64 layerId = 3;
  optional int64 cid = 4;  //被授权的id
}

message RpcAccreditLayerRes{
  optional int32 result = 1; // 0:成功
}

message RpcGetCoCreateMapInfoReq{
  option (region_msg) = true;
  repeated int64 ugcId = 1;
  optional bool isGetApplyOccupy = 2;	// 是否获取共创多人编辑申请占用数据
}

message RpcGetCoCreateMapInfoRes{
  optional int32 result = 1; // 0:成功
  repeated UgcItem ugcItem = 2;
}

message RpcJoinCoCreateMapReq{
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional int64 creatorId = 2 ;
  optional int64 joinCreatorId = 3;
  optional int64 joinUid = 4;
}

message RpcJoinCoCreateMapRes{
  optional int32 result = 1; // 0:成功
  optional UgcEditorList member = 2; //共创者
  optional UgcItem ugcItem = 3;
}

message RpcRemoveCoCreatorReq{
  option (region_msg) = true;
  optional int64 reqCreator = 1;
  optional int64 ugcId = 2 [(field_hash_key) = true];
  optional int64 removeCreatorId = 3;
}
message RpcRemoveCoCreatorRes{
  optional int32 result = 1; // 0:成功
  optional UgcEditorList member = 2; //共创者
  optional string mapName = 3; //联合共创名字
  optional int64 editorUid = 4; //联合共创者uid
  optional int64 editorCreatorId = 5; //联合共创者creatorId
}

message RpcUpdateUgcDataWithCausalConsistencyReq {
  optional int64 routeKey = 1 [(field_hash_key) = true]; // creatorId or ugcId
  optional int32 type = 2;// 协议改造 枚举值切换至整数值 详情参考UgcUpdateType
  optional int32 tcaplusTableName = 3;// 协议改造 枚举值切换至整数值 详情参考UgcTcaplusTableName
  optional bytes data = 4;
}

message RpcUpdateUgcDataWithCausalConsistencyRes {
  optional int32 ret = 1;
}

// 管理端更新创作者信息
message RpcUpdateCreatorInfoReq {
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional UpdateCreatorInfoParam changeParam = 2;
}

message RpcUpdateCreatorInfoRes {
  optional int32 result = 1;   // 结果
  optional string retMsg = 2;  // 回包附带消息
}

// 管理端更新地图信息
message RpcUpdateUgcMapInfoReq {
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional UpdateUgcMapInfoParam changeParam = 2;
}

message RpcUpdateUgcMapInfoRes {
  optional int32 result = 1;   // 结果
  optional string retMsg = 2;  // 回包附带消息
}

// 从操作过的集合中获取地图
message RpcGetUserOperateMapReq {
  option (region_msg) = true;
  optional uint64 creatorId = 1 [(field_hash_key) = true];
  optional uint64 uid = 2;
  optional string openId = 3;
  optional UgcInstanceType mapType = 4;
  optional UgcOpType opType = 5;
  optional string keyword = 6;
  repeated int32 tags = 7;
  optional int32 page = 8;
  optional UgcResParam ugcResParam = 9;  // 资源参数
}

message RpcGetUserOperateMapRes {
  optional int32 result = 1;   // 结果
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional bool hasMore = 4;
}

// 编辑图层
message RpcCoCreateEditorReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;  //ugcId
  optional int64 layerId = 3;    //layerId
  optional int64 lockSeconds = 4; //锁定编辑地图图层的时长
  optional int64 uid = 5;		// 操作用户的uid
  optional int32 editMode = 6;		// 编辑模式, 见枚举UgcMapEditMode所示
}

message RpcCoCreateEditorRes {
  optional int32 result = 1;   // 结果
  optional int64 layerId = 2;  //layerId
  repeated int64 uid = 3;
  optional int64 ugcId = 4;
  repeated int64 creatorIds = 5;
  repeated EditorItemInfo itemInfos = 6;	// 共创地图当前编辑者列表
}

message RpcGetHomePageRecommendReq {
  option (region_msg) = true;
  optional int64 reqCreatorId = 1 [(field_hash_key) = true];
  optional HomePageRecommendPlatformParam platformParam = 2;
  optional bool isIgnoreCache = 3;
  optional UgcHomePageRecommendScene scene = 4;
}

message RpcGetHomePageRecommendRes {
  optional int32 result = 1;   // 结果
  repeated UgcHomePageRecommendData data = 2;
  optional UgcKeyInfo keyInfo = 3;
  repeated UgcHomePageRecommendModuleInfo moduleInfo = 4;
}

message HomePageRecommendPlatformParam {
  optional int64 uid = 2;
  optional string openId = 3;
  optional bool needGuide = 4;
  optional int32 abtestId = 5;
  optional int32 tabId = 6;
  optional int32 loginPlat = 7;  // 玩家登陆平台,对应PlayerLoginPlat
  optional string abTestInfo = 8;
  repeated int32 likedTagIdList = 9; // 玩家选择的标签id列表
}

message RpcGetHomePageThemeRecommendReq {
  option (region_msg) = true;
  optional int64 reqCreatorId = 1 [(field_hash_key) = true];
  optional string themeId = 2;
  optional UgcHomePageRecommendType type = 3;
  optional HomePageRecommendPlatformParam platformParam = 4;
  optional UgcHomePageRecommendScene scene = 5;
}
message RpcGetHomePageThemeRecommendRes {
  optional int32 result = 1;   // 结果
  optional UgcHomePageRecommendData data = 2;
  optional UgcKeyInfo keyInfo = 3;
}

// 删除brief地图
message RpcDeleteBriefReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;  //ugcId
  optional UgcInstanceType type = 3;  //类型
  optional UgcResParam ugcResParam = 4; // 资源类型
  optional int32 idipArea = 5;
}

message RpcDeleteBriefRes {
  optional int32 result = 1;   // 结果
  optional int64 ugcId = 2;  //ugcId
  optional string editorName = 3;  //共创作者
  optional string mapName = 4;  //共创名字
  optional bool isClear = 5;  //是否删除
  repeated int64 uid = 6;
}

enum ReportDataType {
  RDT_UNKNOWN = 0;
}
message RpcReportDataNtfReq {
  option (region_msg) = true;
  option (rpc_one_way) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 type = 2;// 协议改造 枚举值切换至整数值 详情参考ReportDataType
}

// layerId 是否被申请
message RpcLayerIdIsApplyReq {
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;    //ugcId
  optional int64 layerId = 3;  //layerId
}

message RpcLayerIdIsApplyRes {
  optional int32 result = 1;   // 结果
  optional bool isApply = 2;
}

message RpcUgcGetHotPlayingReq {
  option (region_msg) = true;
  optional int64 reqCreatorId = 1 [(field_hash_key) = true];
  optional int64 reqUid = 2;
  optional string reqOpenId = 3;
  optional int32 page = 4;
}

message RpcUgcGetHotPlayingRes {
  optional int32 result = 1;
  optional AlgoInfo algo_info = 2;
  repeated UgcHotPlayingStoryInfo story_list = 3; // 好友动态。page=0时才有
  repeated UgcHotPlayingMapInfo map_list = 4; // 好友在玩地图
  optional UgcKeyInfo keyInfo = 5;
}

message RpcUgcCoCreateEditorHeartReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;      //ugcId
  optional int64 layerId = 3;    //layerId
  optional int32 lockSeconds = 4; //锁定编辑地图图层的时长
  optional int32 editMode = 5;		// 编辑模式, 见枚举UgcMapEditMode所示
}

message RpcUgcCoCreateEditorHeartRes {
  optional int32 result = 1;    //结果
}

message RpcUgcCoCreateDingReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];  //叮的人身份
  optional int64 ugcId = 2;      //ugcId
  optional int64 layerId = 3;    //layerId
  optional int64 otherCreatorId = 4;  //被叮人creatorId
}

message RpcUgcCoCreateDingRes {
  optional int32 result = 1;        // result
  optional int64 uid = 2;  // uid
  optional string nickName = 3;  // 昵称
  optional UgcEditorType editorType = 4;  //身份
  repeated int64 uidList = 5; //成员uids
  optional int64 ugcId = 6;      //ugcId
  optional int64 layerId = 7;    //layerId
  repeated int64 creatorIds = 8; //成员creatorIds
  repeated EditorItemInfo itemInfos = 9;	// 共创地图当前编辑者列表
}

// 离开编辑图层
message RpcCoCreateExitEditorReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;  //ugcId
  optional int64 layerId = 3; //layerId
  optional int32 editMode = 4;		// 编辑模式, 见枚举UgcMapEditMode所示
}

message RpcCoCreateExitEditorRes {
  optional int32 result = 1;   // 结果
  repeated int64 uid = 2;
  repeated int64 creatorIds = 3;
  repeated EditorItemInfo itemInfos = 4;	// 共创地图当前编辑者列表
}

message RpcGmSetCreatorInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 type = 2;
  optional string value = 3;
}

message RpcGmSetCreatorInfoRes {
  optional int32 result = 1;   // 结果
}

message RpcUgcSvrTestReq {
  option (region_msg) = true;
  optional bytes content = 1;
  optional int32 type = 2;
}

message RpcUgcSvrTestRes {
  optional int32 result = 1;   // 结果
}

message RpcGmUpdateMapInfoReq {
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional string name = 2;
  optional string desc = 3;
  optional string tag = 4;
}

message RpcGmUpdateMapInfoRes {
  optional int32 result = 1;   // 结果
}

message RpcMapUgcStartBattleReq {
  option (region_msg) = true;
  optional uint64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional int64 ugcId = 2; // 通关地图id
  optional int32 gameSource = 3;//游戏来源
  optional int32 logicMapSource = 4;
  optional bool isUgcApp = 5;
}

message RpcMapUgcStartBattleRes {
  optional int32 result = 1;   // 结果
  optional bool isFirstPlay = 2;
  optional int32 playedMapCount = 3; // 游玩地图数量
  optional int64 creatorUid = 4;
  optional int32 playerCount = 5;
}

message RpcPublishSecretInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // uid
  optional int64 ugcId = 2;  // 地图ugcId
}

message RpcPublishSecretInfoRes {
  optional int32 result = 1;
  optional string km = 2;
}

message RpcGmUpdatePlayerInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 type = 2;
  optional int32 num = 3;
}

message RpcGmUpdatePlayerInfoRes {
  optional int32 result = 1;   // 结果
}


message RpcGetPublishPlayInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  repeated int64 ugcIds = 2;
}

message RpcGetPublishPlayInfoRes {
  optional int32 result = 1;   // 结果
  map<int64, int32> playMap = 2;
}

message RpcDoMapBanOptReq {
  option (region_msg) = true;
  optional int64 creatorId = 1;
  optional int64 mapId = 2 [(field_hash_key) = true];
  optional UgcMapBanType banType = 3;
  optional int32 opType = 4;  // 0设置 1清理
  optional string openId = 5;
  optional int32 platId = 6;
}

message RpcDoMapBanOptRes {
  optional int32 result = 1;   // 结果
  optional string retMsg = 2;
}

enum IdipOperateMapType {
  IOMT_INVALID = 0;
  IOMT_DEL_TOPIC = 1;
}

message DelTopic {
  optional string topic = 1;
}

// 使用ugcId作为路由key
message RpcIdipOperateMapReq {
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional int32 opType = 2;// 协议改造 枚举值切换至整数值 详情参考IdipOperateMapType
  optional DelTopic delTopic = 3;
}

// idip修改地图信息响应
message RpcIdipOperateMapRes {
  optional int32 result = 1; // 0:成功
}

message RpcUgcPlatSvrGmReq {
  // option (region_msg) = true; 这个是ugcsvr内部的消息, 就不能用这个option
  option (rpc_one_way) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];// 地图的拥有者的uid
  optional int32 cmd = 2;
}

message RpcUgcMatchLobbyDetailReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];  // 可能并没有creatorId直接用uid做hashkey 并五实际意义
  optional int64 creatorId = 2;
  optional int64 clientVersion = 3;
  optional int32 cloudGameType = 4;
}

message RpcUgcMatchLobbyDetailRes {
  optional int32 errCode = 1;
  repeated UgcMatchLobbyMapBrief pubBriefs = 2;
  optional UgcKeyInfo keyInfo = 3;
}

message RpcUgcMatchConfigPageReq {
  optional UgcMatchConfigPageData pageData = 1;  // 配置信息
}

message RpcUgcMatchConfigPageRes {
  optional int32 errCode = 1;
  optional UgcMatchConfigPageResult pageResult = 2;  // 配置结果
}

message UgcPlatMatchRecommendInfo {
  optional int64 ugcId = 1;
  optional int64 openTime = 2;
  optional int64 closeTime = 3;
  optional bool isTop = 4;
}

message RpcUgcPlatMatchRecommendListReq {
  optional int64 ugcId = 1 [(field_hash_key) = true];  // 只会发到一个ugcsvr去写redis，其他ugcsvr只读
  repeated UgcPlatMatchRecommendInfo recommendList = 2;  // 推荐列表 废弃
  map<int64, UgcMatchLobbyMapBriefInfo> briefInfos = 3;
  optional int32 lobbyShowCnt = 4;  // 显示个数
  optional int32 lobbyShowType = 5;  // 显示规则
}

message UgcPlatMatchRoomInfo {
  optional int64 ugcId = 1;
  optional int32 roomInfoId = 2;
  optional int32 singlePlayType = 3;
}

message UgcPlatMatchRecommendResInfo {
  optional int64 ugcId = 1;
  optional int32 roomInfoId = 2;
  optional int32 singlePlayType = 3;
  repeated UgcPlatMatchRoomInfo matchRoomInfo = 4;  // 所有的配置
  optional UgcMatchMapBriefFromRemoteResult packResult = 5;
}

message RpcUgcPlatMatchRecommendListRes {
  optional int32 errCode = 1;
  repeated UgcPlatMatchRecommendResInfo recommendRes = 2;
}

message RpcUgcMatchMapBriefFromRemoteConfigReq {
  optional int64 ugcId = 1 [(field_hash_key) = true];  // 只会发到一个ugcsvr去写redis，其他ugcsvr只读
  optional int32 lobbyShowCnt = 2;  // 显示个数
  optional int32 lobbyShowType = 3;  // 显示规则
  map<int64, UgcMatchMapBriefFromRemote> remoteCfg = 4;  // 原始配置
}

message RpcUgcMatchMapBriefFromRemoteConfigRes {
  optional int32 errCode = 1;
  repeated UgcPlatMatchRecommendResInfo recommendRes = 2;
}

message RpcUgcMatchUgcIdCheckReq {
  option (region_msg) = true;
  optional int64 ugcId = 1;
  optional int32 roomInfoId = 2;
  optional int64 ugcCfgId = 3;
}

message RpcUgcMatchUgcIdCheckRes {
  optional int32 errCode = 1;
  optional int32 roomInfoId = 2;
  optional int64 openTime = 3;
  optional int64 closeTime = 4;
  optional UgcMatchCompilations ugcMatchCompilations = 5;
  optional string compName = 6;  // 地图合集名
  // 服务携带出生点信息 动态匹配使用的 单图多图都用这个
  optional UgcMatchMapBriefFromRemote_Server_BirthInfo birthInfo = 7;
}

message RpcGetCollectStarActivityMapReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 uid = 2;
  optional string openId = 3;
}
message RpcGetCollectStarActivityMapRes{
  optional int32 result = 1;   // 结果
  repeated PublishItem mapList = 2;
  optional int64 currentRefreshMapTime = 3;
  optional int64 nextRefreshMapTime = 4;
}

message RpcUgcMapSearchFrontReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 uid = 2;
  optional string openId = 3;
  optional int32 deviceLevel = 4; // 机型评级 DeviceLevel
}

message RpcUgcMapSearchFrontRes {
  optional int32 result = 1;
  repeated string hotContent = 2;  //热搜内容（固定4条热搜）(废弃)
  optional PublishStruct hotMaps = 3; //热搜地图（固定4张地图）
  optional PublishStruct likeMaps = 4; //喜欢地图（固定4张地图）
  repeated UgcKeyInfo keyInfo = 5;
  optional string hotKeyWord = 6;     //默认热搜关键词(废弃)

  repeated HotContent contents = 7;    //热搜内容（固定4条热搜）
  optional HotContent keyWord = 8;    //默认热搜关键词
  repeated TopicInfoItem topics = 9;    //热搜话题列表
}

message RpcUgcSearchInstanceReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int64 uid = 3;
  optional int32 page = 4;
  optional string keyWord = 5;
  optional int32 searchType = 6;
  optional string sessionId = 7;  // 上下文
}

message RpcUgcSearchInstanceRes {
  optional int32 result = 1;
  optional PublishStruct item = 2;
  optional UgcKeyInfo keyInfo = 3;
  map<int32, int32>  countMap = 4;   //数量
  optional PublishStruct recommendMaps = 5;  //推荐地图 保底
  optional TopicInfoItem topicInfo = 6;  //精准话题
  optional EditorInfoItem editorInfo = 7; //精准作者
  repeated TopicInfoItem topicItem = 8; //话题列表
  repeated EditorInfoItem editorItem = 9; //作者列表
  optional string sessionId = 10;  // 上下文
  repeated UgcCollectionBrief collections = 11;
}

message RpcUgcTopicDetailReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int64 uid = 3;
  optional int32 page = 4;
  optional string topicName = 5;
  optional TopicTyp topicType = 6;
}

message RpcUgcTopicDetailRes {
  optional int32 result = 1;
  optional TopicInfoItem topicInfo = 2;  //话题名称
  optional PublishStruct topicMaps = 3; //话题地图
  optional UgcKeyInfo keyInfo = 4;
}

message RpcUgcRoomRecommendListReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 uid = 2;
  optional string openId = 3;
  optional int32 platId = 4;
  optional int32 loginType = 5;
  optional int32 areaType = 6;     //大区
  optional int32 recommendType = 7;  // UgcRecommendType
  optional uint32 tag = 8;  // tag_id
  optional int32 type = 9;  // type
  optional string lbs = 10;  // source_are
  optional int32 isAdult = 11;  // minor_flag
  optional int32 page = 12;
  optional int32 deviceLevel = 13;  // device_level
  repeated int64 refreshMapId = 14;  // 由客户端申请刷新的id
}

message RpcUgcRoomRecommendListRes {
  optional int32 errCode = 1;
  repeated PublishItem items = 2;               // publish 作品
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 4;
  repeated HotPlayList typeList = 5;
  optional int32 hasMorePage = 6; //1:表示还有下一页,0没有下一页
}

message RpcUgcRoomOfficalRecommendListReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string tab_id = 2;  // tab
  optional int32 page = 3;
  optional int64 uid = 4;
  optional string openId = 5;
}

message RpcUgcRoomOfficalRecommendListRes {
  optional int32 errCode = 1;
  repeated UgcRoomOfficalRecommendMapInfo recommendInfos = 2; //UGC房间地图列表
  repeated UgcRoomOfficalRecommendTabInfo tabInfos = 3; //页签信息，每次请求都返回
  optional AlgoInfo info = 4;
  optional int32 hasMorePage = 5; //1:表示还有下一页，0比没有下一页
}

// 测试增加消息是链路发送是否ok
message RpcTestAddMsgReq {
  optional int64 ugcId = 1 [(field_hash_key) = true];
  repeated UgcPlatMatchRecommendInfo recommendList = 2;
}


message RpcTestAddMsgRes {
  optional int32 errCode = 1;
}

// 资源社区首页推荐
message RpcResHomePageRecommendReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int64 uid = 3;
}

message RpcResHomePageRecommendRes {
  optional int32 result = 1;                    // 结果
  optional UgcResHomePageInfo homePageData = 2;  // 首页推荐信息
}

// 首页推荐中的集合的获取更多
message RpcResHomePageRecommedMoreSetReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 index = 2;                       // 集合index
  optional string openId = 3;                     // openId
  optional int64 uid = 4;                         // uid
}

message RpcResHomePageRecommedMoreSetRes {
  optional int32 result = 1;                    // 结果
  repeated PublishItem items = 2;               // 资源信息
  optional UgcKeyInfo keyInfo = 3;              // cos info
  optional AlgoInfo info = 4;                   // 推荐算法信息
}

// 资源背包——添加
message RpcUgcResBagAddReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  optional UgcResParam param = 3;     // 请求参数
}

message RpcUgcResBagAddRes {
  optional int32 result = 1;
}

// 资源背包——移除
message RpcUgcResBagDeleteReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  repeated int64 ugcId = 2;
  optional UgcResParam param = 3;     // 请求参数
}

message RpcUgcResBagDeleteRes {
  optional int32 result = 1;          // 结果
  repeated int64 succIds = 2;         // 成功的id
  repeated int64 failIds = 3;         // 失败的id
}

// 资源背包——拉取
message RpcUgcResBagGetReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 page = 2;            // 页号
  optional UgcResParam param = 3;     // 请求参数
  optional int64 uid = 4;                      // uid
  optional string openId = 5;                  // openid
}

message RpcUgcResBagGetRes {
  optional int32 result = 1;             // 结果
  repeated PublishItem items = 2;        // 列表
  optional UgcKeyInfo keyInfo = 3;       // cos key
  optional int32 count = 4;              // 该类型数量
  optional bool hasMore = 5;             // 是否还有下一页
  optional int32 capacity = 6;           // 该类资源背包上限
}

// 资源背包——搜索
message RpcUgcResBagSearchReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string name = 2;            // 搜索字符串
  optional int32 page = 3;             // 页号
  optional UgcResParam param = 4;      // 请求参数
  optional int64 uid = 5;                      // uid
  optional string openId = 6;                  // openid
}

message RpcUgcResBagSearchRes {
  optional int32 result = 1;             // 结果
  repeated PublishItem items = 2;        // 列表
  optional UgcKeyInfo keyInfo = 3;       // cos key
  optional bool hasMore = 4;             // 是否还有下一页
}

// 资源社区搜索
message RpcUgcResCommunitySearchReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 page = 2;
  optional UgcResParam ugcResParam = 3; // 资源参数
  optional string keyword = 4;
  optional int64 uid = 5;                      // uid
  optional string openId = 6;                  // openid
  optional string sessionId = 7;
}

message RpcUgcResCommunitySearchRes {
  optional int32 result = 1;
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 4;
  optional PublishItem foundResItem = 5;   // 命中的资源id
  optional string sessionId = 6;
}

// 测试增加消息是链路发送是否ok
message RpcTestAddMsgV2Req {
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  repeated UgcPlatMatchRecommendInfo recommendList = 2;
}

message RpcTestAddMsgV2Res {
  optional int32 errCode = 1;
}


message RpcGetSaveRecordReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  optional int32 layerId = 3;
}

message RpcGetSaveRecordRes {
  optional int32 result = 1;
  repeated  UgcMetaOperateRecord saveRecord = 2;
  repeated  UgcMetaOperateRecord updateRecord = 3;
}

message WarmDataOperateInfo {
  optional int32 opType = 1;  // 操作类型 UgcWarmDateOptType
  optional int32 accountType = 2;  // 游戏区 1:wx 2:qq
  optional int64 upValue = 3;  // 更新值
}

message RpcWarmDataOperateReq {
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional int32 opType = 2;  // 操作类型 UgcWarmDateOptType
  optional int32 accountType = 3;  // 游戏区 1:wx 2:qq
  optional int64 upValue = 4;  // 更新值
  optional bool isMulti = 5;  // 是否同步多个
  repeated WarmDataOperateInfo operateInfos = 6;
}

message RpcWarmDataOperateRes {
  optional int32 errCode = 1;
}

message RpcUGCSearchSuggestionReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int64 uid = 3;
  optional SearchType searchType = 4;
  optional string inputText = 5;
}

message RpcUGCSearchSuggestionRes {
  optional int32 result = 1;
  repeated string keyWord = 2;
  optional AlgoInfo info = 3;
}

message RpcUgcGetRecommendSubsReq {
  option (region_msg) = true;
  optional int64 uid = 1;
  optional string openId = 2;
  optional int64 creatorId = 7;
}

message RpcUgcGetRecommendSubsRes {
  optional int32 result = 1;
  repeated UgcOpPlayerInfo infos = 2; //订阅玩家列表
  optional AlgoInfo info = 3;
}

message RpcUgcMulTestSaveMetaReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional uint64 ugcId = 2; // 地图id
  repeated UgcMapMetaInfo info = 3; //cos
  optional uint32 pointNumber = 4; // 出生点人数
  repeated BattleCamp camps = 5;
  optional string mapKey = 6;
  optional UgcLayerList layers = 7;// 图层数据
}

message RpcUgcMulTestSaveMetaRes {
  optional int32 result = 1;
}

message RpcUgcMapGroupListReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional uint64 ugcId = 2; // 地图id
  optional int32 source = 3;  // 来源,UgcTableBatchGetSource
}

message RpcUgcMapGroupListRes {
  optional int32 result = 1;
  optional UgcGroupIdList groupIds = 2; // 地图中使用到的发布组合
}

enum UgcPlayType {
  UgcPlay = 0;
  UgcMulPlay = 1;  //多人测试
  UgcTestUgcLobby = 2; //ugc大厅自测或评审
}

message RpcUgcMapHandlerReq {
  option (region_msg) = true;
  optional int64 creatorId = 1;
  optional int64 mapId = 2 [(field_hash_key) = true];
  optional string openId = 4;
  optional int32 platId = 5;
  optional UgcMapActionType type = 6;
}

message RpcUgcMapHandlerRes {
  optional int32 result = 1;   // 结果
  optional string retMsg = 2;
}

enum UgcRcpSourceType {
  UgcSourceRoom = 0;     //房间
  UgcSourceMulPlay = 1;  //多人测试
  UgcSourceDs = 2;       //ds
  UgcSourceLobby = 3;    //大厅
  UgcSourceTestUgcLobby = 4; //ugc大厅自测或评审
}

message RpcUgcInstanceVersionReq {
  option (region_msg) = true;
  optional uint64 ugcId = 1;
}

message RpcUgcInstanceVersionRes {
  optional int32 result = 1;
  optional string clientVersion = 2; //客户端版本号
}

// 管理端操作合集更新
message RpcUgcCollectionUpdateReq {
  optional string collectionId = 1;
  optional AdminModifyCollectionParam modifyReq = 2;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message RpcUgcCollectionUpdateRes {
  optional int32 result = 1;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message RpcUgcModifyPublishConfigReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 startTime = 2;
  optional int64 endTime = 3;
}

message RpcUgcModifyPublishConfigRes {
  optional int32 result = 1;
}

message RpcUgcMatchLobbyDetailExReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];  // 可能并没有creatorId直接用uid做hashkey 并五实际意义
  optional int64 creatorId = 2;
  optional int32 type = 3;
  optional int64 clientVersion = 4;
  optional int32 cloudGameType = 5;
}

message RpcUgcMatchLobbyDetailExRes {
  optional int32 errCode = 1;
  optional int32 type = 2;
  repeated UgcMatchLobbyMapBrief pubBriefs = 3;
  optional UgcKeyInfo keyInfo = 4;
}

// 客户端操作合集
message RpcUgcCollectionOpReq {
  option (region_msg) = true;
  optional int64 operator = 1 [(field_hash_key) = true];
  optional int32 op = 2;
  optional string collectionId = 3;
  repeated string collectionIds = 4;
  optional UgcCollectionModifyInfo modified = 5;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}
message RpcUgcCollectionOpRes {
  optional int32 result = 1;
  optional UgcCollectionBrief out = 2;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message RpcWolfKillReportReq {
  option (region_msg) = true;
  optional int64 reporter = 1 [(field_hash_key) = true]; // 举报者uid
  optional int64 beReporter = 2; // 被举报者uid
  optional int64 battler = 3; // 对局id
  optional int64 reportId = 4; // 举报类型
}

message RpcWolfKillReportRes {
  optional int32 result = 1;
}

// 获取合集的信息
message RpcUgcCollectionGetBriefsReq {
  option (region_msg) = true;
  optional int64 operator = 1 [(field_hash_key) = true];
  optional int64 creatorId = 2;       // creatorId和collectionIds互斥
  repeated string collectionIds = 3;  // creatorId和collectionIds互斥
  optional bool focus = 4;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}
message RpcUgcCollectionGetBriefsRes {
  optional int32 result = 1;
  repeated UgcCollectionBrief briefs = 2;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message UgcResNeedDownReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  repeated int64 needResId = 3;
  optional UgcTableType tableType = 4;
  optional bool isCarryRule = 5;
  optional int64 uid = 6;
  optional string openId = 7;
}

message UgcResNeedDownRes{
  optional int32 result = 1;
  optional int64 ugcId = 2;
  repeated UgcResourceInfo info = 3;  //需要下载的资产
  repeated int64 ruleId = 4;  //违规或者下架的资产id
  optional UgcKeyInfo keyInfo = 5;
  optional string extraParam = 6;  //预留
}

message RpcUgcGetGoodsListReq {
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  repeated string goodsId = 2;
}
message RpcUgcGetGoodsListRes {
  optional int32 result = 1;
  repeated UgcGoodsInfo info = 2;
}

message RpcUgcDanMuSetInfoReq {
  option (region_msg) = true;
  optional int64 mapId = 1 [(field_hash_key) = true];
}

message RpcUgcDanMuSetInfoRes {
  optional int32 errCode = 1;
  optional bool isDanMuUnable = 2;  // 是否允许发弹幕
  optional UgcMapSize ugcMapSize = 3;  // 地图长宽高
}

message RpcUgcDanMuInfoUpdateReq {
  option (region_msg) = true;
  option (rpc_one_way) = true;
  optional int64 mapId = 1 [(field_hash_key) = true];
  optional int32 optType = 2;
  optional int32 optValue = 3;
}

// 合集官方精品推荐
message RpcUgcCollectionGovRecommendListReq {
  option (region_msg) = true;
  optional int64 operator = 1 [(field_hash_key) = true];
  optional int32 pageCap = 2;                   // 单页容量
  optional int32 pageNo = 3;                    // 页码, 从0开始
  optional string openId = 4;
  optional int64 uid = 5;
  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message RpcUgcCollectionGovRecommendListRes {
  optional int32 result = 1;
  repeated UgcCollectionBrief top = 2;                      // 置顶合集
  repeated UgcCollectionBrief other = 3;                    // 其他精品合集
}

// 合集个性化推荐
message RpcUgcCollectionRecommendListReq {
  option (region_msg) = true;
  optional int64 operator = 1 [(field_hash_key) = true];
  optional uint32 type = 2;                         // 推荐列表类型。0-合集广场
  repeated uint32 tagIds = 3;                       // 标签ID，传0则不筛选tag
  optional uint32 page = 4;                         // 页号，0表示第一页。
  optional string openId = 5;
  optional int64 uid = 6;
  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message RpcUgcCollectionRecommendListRes {
  optional int32 result = 1;
  repeated UgcCollectionBrief briefs = 2;
  optional RecommendAlgoInfo algo_info = 3;
  optional bool has_more = 4;

  message Collection {
    optional string collection_id = 1;              // 合集ID
    optional uint32 rcmd_pos = 2;                   // 推荐接口给的位置。回流参考
  }
}

message RpcUgcCollectionSearchListReq {
  option (region_msg) = true;
  optional int64 operator = 1 [(field_hash_key) = true];
  optional string key_word = 2;       // 搜索词
  optional uint32 page = 3;           // 页码。0表示第一页
  optional string session_id = 4;     // 上下文（预留，可能推荐缓存用）
}

message RpcUgcCollectionSearchListRes {
  optional int32 result = 1;
  repeated UgcCollectionBrief briefs = 2;
}

message RpcUgcUpdateMapLabelScoreReq {
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional int64 reqCreatorId = 2;
  repeated UgcMapLabelScoreInfo labelScore = 3;
  optional int64 uid = 4;
  optional string openid = 5;
  optional int32 platId = 6;
  optional int32 loginType = 7;
  optional bool isFirst = 8;
}
message RpcUgcUpdateMapLabelScoreRes {
  optional int32 result = 1;
}

message RpcUgcMapDownloadInfoReq {
  option (region_msg) = true;
  optional int64 ugcId = 1;
  optional int32 downloadInfoType = 2;  // 下载信息类型。UgcDownloadInfoType
}

message RpcUgcMapDownloadInfoRes {
  optional int32 result = 1;
  optional UgcDownloadBaseData baseData = 2;
  optional UgcDownloadData downloadData = 3;
}

// 私有资产数据兼容——M10: 私有资产数据挪到pub目录
message RpcUgcResPrivateAdaptReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  repeated UgcMapMetaInfo metaInfo = 3;  // metaInfo 信息
  optional UgcResParam ugcResParam = 4; // 资源参数
}

message RpcUgcResPrivateAdaptRes {
  optional int32 result = 1;        //返回结果
}

// 按类型拉取我的资产
message RpcUgcResMyListReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 uid = 2;
  repeated UgcResType ugcResType = 3;
  optional UgcInstanceType instanceType = 4;
  optional int32 page = 5;
}

message RpcUgcResMyListRes {
  optional int32 result = 1;  // 0:成功
  repeated UgcItem maps = 2;  // ugc列表
  optional int32 allCount = 3;
  optional bool hasMore = 4;  // 是否还有更多数据
}


message RpcUgcInfoSaveReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  repeated MapCoverInfo coverList = 3;
}

message RpcUgcInfoSaveRes {
  optional int32 result = 1; // 0:成功
}

message RpcUgcDeleteCoverReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  repeated string coverId = 3;
}

message RpcUgcDeleteCoverRes {
  optional int32 result = 1; // 0:成功
}

//设置封面图
message RpcUgcSetUpCoverReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  repeated MapCoverInfo covers = 3;
}

message RpcUgcSetUpCoverRes {
  optional int32 result = 1; // 0:成功
}

//封面图列表
message RpcUgcCoverListReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
}

message RpcUgcCoverListRes {
  optional int32 result = 1; // 0:成功
  repeated MapCoverInfo covers = 2;
  optional string bucket = 3;  //bucket
  optional string region = 4;
}



//验证是否允许单人模式上报排行榜
message RpcUgcCheckSingleRankUpdateReq {
  option (region_msg) = true;
  optional int64 mapId = 1 [(field_hash_key) = true];
  optional int64 rankId = 2;
}

message RpcUgcCheckSingleRankUpdateRes {
  optional int32 result = 1; // 0:成功
}

// 上报UGC排行榜数据
message RpcSetUgcMapRankReq {
    option (region_msg) = true;
    optional int64 mapId = 1;   //mapId
    optional int64 rankId = 2;   //rankId
    optional int64 uid = 3 [(field_hash_key) = true];
    optional int32 score = 4;    // 上报的分数
    repeated int32 extraScores = 5;   // 额外分数
}

message RpcSetUgcMapRankRes {
    optional int32 result = 1;    //返回结果
}

// 批量上报UGC排行榜数据
message RpcBatchSetUgcMapRankReq {
    option (region_msg) = true;
    optional int64 mapId = 1;
    repeated com.tencent.wea.protocol.UgcUpdateRankInfo updateRankItems = 2;
}

message RpcBatchSetUgcMapRankRes {
    optional int32 result = 1;    //返回结果
}

message RpcCsForwardReq {
  option (region_msg) = true;
  optional int64 hashKey = 1 [(field_hash_key) = true];
  optional CSHeader csHeader = 2;
  optional bytes csBody = 3;
  optional CsUgcForwardHeader forwardHeader  = 4;
}

message RpcCsForwardRes {
  optional int32 nkErrorCode = 1;
  optional string nkErrorMsg = 2;
  optional CSHeader csHeader = 3;
  optional bytes scRsp = 4;
}

message RpcGMUpdatePublishTimeReq {
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional int64 time = 2;
}

message RpcGMUpdatePublishTimeRes {
  optional int32 result = 1;
}

message AdminMsgTestReq {
  option (forward_admin_call) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string testParam = 2;
}
message AdminMsgTestRes {
  optional int32 code = 1;
  optional string msg = 2;
  optional string testParam = 3;
}

message RpcSignOutPlayerReq{
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional UgcPlayerAccountStatus status = 2;
}

message RpcSignOutPlayerRes {
  optional int32 result = 1;
}

message PlatformSynBanListReq {
  option (forward_admin_call) = true;
  repeated BanListInfo banListInfo = 2;
}

message PlatformSynBanListRes {
  optional int32 result = 1;
  optional string msg = 2;
}
// 独立app申请创作者id请求
message RpcAppApplyCreatorIdReq {
  option (region_msg) = true;
  optional string platOpenId = 1;                           // app账号平台openid
}

// 独立app申请创作者id响应
message RpcAppApplyCreatorIdRes {
  optional int32 result = 1;                                // 返回码
  optional int64 creatorId = 2;                             // app账号创作者id
}

// 独立app用户信息同步通知请求
message RpcAppUserInfoSyncNtfReq {
  option (region_msg) = true;
  optional int64 appCreatorId = 1 [(field_hash_key) = true];// app账号creatorId
  optional string platOpenId = 2;                           // app账号平台openid
  optional string nickName = 3;                             // app账号昵称
  optional int32 gender = 4;                                // app账号性别
  optional string profile = 5;                              // app账号头像
}

// 独立app用户信息同步通知响应
message RpcAppUserInfoSyncNtfRes {
  optional int32 result = 1;                                // 返回码
}

enum GetCosUrlReason {
  GCUR_MAPDATA = 0; //地图信息
}

message RpcGetCosUrlByUgcIdsReq {
  option (forward_admin_call) = true;
  repeated int64 ugcIds = 1;
  optional int32 reason = 2;  // 拉取原因 enum GetCosUrlReason
}

message UgcId2Url {
  optional int64 ugcId = 1;
  optional string url = 2;
}

message RpcGetCosUrlByUgcIdsRes {
  repeated UgcId2Url ugcId2Url = 1;
  optional int32 code = 2;     // 总的返回码 只要有成功的就会返回 0
  optional string msg = 3;     // 错误信息
  optional int32 result = 4;   // 0:成功
}

message RpcGetMapRankListReq{
  option (region_msg) = true;
  optional int64 mapId = 1 [(field_hash_key) = true];
}

message RpcGetMapRankListRes {
  optional int32 result = 1;
  repeated UgcRankInfo ugcRankInfo = 2;   // 排行榜信息
}

message RpcBatchGetPlayMapTimeReq{
  option (region_msg) = true;
  optional int64 mapId = 1;
  repeated int64 creatorId = 2;
  optional int32 source = 3;  // 请求来源，对应UgcTableBatchGetSource
}

message RpcBatchGetPlayMapTimeRes {
  optional int32 result = 1;
  repeated KeyValueInt64 playTimeList = 2; // key:creatorId, value:游玩时间
}

message RpcGetPlayerPlayMapsTimeReq{
  option (region_msg) = true;
  optional int64 creatorId = 1;
  repeated int64 mapIds = 2;
  optional int32 source = 3;  // 请求来源，对应UgcTableBatchGetSource
}

message RpcGetPlayerPlayMapsTimeRes {
  optional int32 result = 1;
  repeated KeyValueInt64 playTimeList = 2; // key:mapIds, value:游玩时间
}

//切换图层id
message RpcChangeUgcLayerIdReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];// creatorId
  optional int64 ugcId = 2;  //ugcId
  optional int32 layerId = 3;  // 切换场景id
  optional UgcInstanceType instanceType = 4; // 实例类型
  optional VersionType version = 5; // VersionType
}

message RpcChangeUgcLayerIdRes {
  optional int32 result = 1;        //结果
  optional int32 curLayerId = 2;    // 当前图层id
}

//删除图层
message RpcRemoveUgcLayerReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];// creatorId
  optional int64 ugcId = 2;  //ugcId
  optional int32 layerId = 3;  // 场景id
  optional UgcInstanceType instanceType = 4; // 实例类型
}

message RpcRemoveUgcLayerRes {
  optional int32 result = 1;      //结果
  optional int32 layerId = 2;     //删除图层id
  optional UgcLayerList layers = 3;
}

message RpcGetMapSettingReq{
  option (region_msg) = true;
  optional int64 mapId = 1 [(field_hash_key) = true];
}

message RpcGetMapSettingRes {
  optional int32 result = 1;
  optional UgcMapSetting ugcMapSetting = 2;   // 地图信息
}

// 获取玩法模式的排序信息
message RpcGetUgcMatchModeSortInfoReq {
  option (region_msg) = true;
  optional int64 clientVersion = 1;
  repeated int32 modeTypeIdList = 2;
}

message UgcMatchModeSortInfo {
  optional int64 startTime = 1;
  optional int64 closeTime = 2;
  optional int32 matchTypeId = 3;
  optional int32 sort = 4;
  optional bool isShowMiniGame = 5;
  optional int32 ugcLobbyJumpId = 6;  // ugc地图跳转id
  optional int64 configId = 7;  // 配置的id
}

message RpcGetUgcMatchModeSortInfoRes {
  optional int32 result = 1;  // 结果
  repeated UgcMatchModeSortInfo ugcMatchModeSortInfo = 2;  // 排序信息
}

// 拉取某个地图某个玩家的成就进度
message RpcUgcAchievementConfigEditReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;  // 当前地图id （如果修改的成就id不是当前地图的，会拷贝一封并更新当前地图存储）
  optional int32 achId = 3;  // 当前成就的id
  optional UgcAchievementConf conf = 4;  // 配置数据
  optional int64 uid = 5;
}

message RpcUgcAchievementConfigEditRes {
  optional int32 errCode = 1;
  optional UgcAchievementIndex ret = 2;
}

// 拉取配置信息
message RpcUgcAchievementConfigGetReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;  // 地图id
  optional int32 pageId = 3;  // 页id
  optional int64 uid = 4;
}

message RpcUgcAchievementConfigGetRes {
  optional int32 errCode = 1;
  optional int32 pageAll = 2;  // 总的页数
  repeated UgcAchievementData data = 3;  // 成就的数据
}

message GmUgcUpdatePlayerDataReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 pubCount = 2;  // 发布地图数量
}

message GmUgcUpdatePlayerDataRes {
  optional int32 result = 1;
}

message RpcUpdateUgcLayerNameReq {
  option (region_msg) = true;
  optional int64 creatorId = 1[(field_hash_key) = true];// creatorId
  optional int64 ugcId = 2;  //ugcId
  optional int32 layerId = 3;  // 切换场景id
  optional string layerName = 4; //图层名称
  optional string desc = 5;
  optional int32 pointNumber = 6;
  optional int64 templateId = 7;
  optional int32 layerType = 8;
  repeated UgcLayerInfo infos = 9; //兼容 如果layerId=0; 直接用infos结构
}

message RpcUpdateUgcLayerNameRes {
  optional int32 result = 1;        //结果
}

message QueryPlayerUgcExpReq {
  option (forward_admin_call) = true;
  optional int64 creatorId = 1;
}
message QueryPlayerUgcExpRes {
  optional int32 code = 1;     // 总的返回码 只要有成功的就会返回 0
  optional string msg = 2;     // 错误信息
  optional int64 ugcExp = 3;
}

message GmDeleteUgcRepeatedNameReq {
  option (region_msg) = true;
  optional int64 creatorId = 1[(field_hash_key) = true];
  optional string name = 2;
}
message GmDeleteUgcRepeatedNameRes {
  optional int32 result = 1;     // 总的返回码 只要有成功的就会返回 0
}

message RpcUgcCoverCheckLimitReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
}

message RpcUgcCoverCheckLimitRes {
  optional int32 result = 1; // 0:成功
}


message UploadCoverInfo {
  optional int32 msgType = 1;  //type=5 小图  type=9 大图
  optional string path = 2;
  optional string coverName = 3;
  optional int32 index = 4;
  optional string bucket = 5;
}


message RpcUgcUploadCoverInfoReq {
  option (forward_admin_call) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
}

message RpcUgcUploadCoverInfoRes {
  optional int32 result = 1; // 0:成功
  repeated UploadCoverInfo coverInfo = 2; // 上传封面信息数据
  optional UgcKeyInfo keyInfo = 3;
}

message UploadCoverNotifyInfo {
  optional string coverName = 1;
  optional UgcMapMetaInfo metaInfo = 2;
  optional int32 index = 3;
}

message RpcUgcUploadCoverNotifyReq {
  option (forward_admin_call) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  repeated UploadCoverNotifyInfo notifyInfo = 3;
}


message RpcUgcUploadCoverNotifyRes {
  optional int32 result = 1; // 0:成功
}

message UgcUploadFileInfoAsk {
  optional int32 upload_file_type = 1;  // 上传类型 UgcUploadFileType
  // 如果有附加消息 都是用message定义 不要直接写字段
}

message RpcUgcUploadFileInfoReq {
  option (forward_admin_call) = true;
  optional int64 creator_id = 1 [(field_hash_key) = true];
  optional int64 ugc_id = 2;
  optional UgcUploadFileInfoAsk file_info = 3;  // 需要上传的消息类型
}

message UgcUploadFileInfoAnswer_VideoCover {
  optional int32 msg_type = 1;  // CoverMP4
  optional string path = 2;  // 路径
  optional int64 video_id = 3;  // 视频id
  optional string bucket = 4;  // bucket
}

message UgcUploadFileInfoAnswer {
  optional int32 upload_file_type = 1;  // 上传类型
  optional UgcUploadFileInfoAnswer_VideoCover video_cover = 2;  // 视频封面
}

message RpcUgcUploadFileInfoRes {
  optional int32 result = 1; // 0:成功
  optional UgcUploadFileInfoAnswer answer = 2; // 上传信息
  optional UgcKeyInfo key_info = 3;
}

message UgcUploadFileNotify_VideoCover {
  optional int64 video_id = 1;  // 视频id
  optional UgcMapMetaInfo info = 2;  // cos信息
}

message UgcUploadFileNotify {
  optional int32 upload_file_type = 1;  // 上传类型
  optional UgcUploadFileNotify_VideoCover video_cover = 2;  // 视频封面
}

message RpcUgcUploadFileNotifyReq {
  option (forward_admin_call) = true;
  optional int64 creator_id = 1 [(field_hash_key) = true];
  optional int64 ugc_id = 2;
  optional UgcUploadFileNotify upload_info = 3;
}

message RpcUgcUploadFileNotifyRes {
  optional int32 result = 1; // 0:成功
}

// 发起地图评估
message RpcMapInitiateEvaluationReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  optional int64 uid = 3;
}

message RpcMapInitiateEvaluationRes {
  optional int32 errCode = 1;
}

// 地图额外配置
message RpcExtraConfigEditReq {
  option (region_msg) = true;
  optional int64 creatorId = 1;
  optional int64 ugcId = 2;  // 地图id
  optional int32 cfgType = 3;  // 数据类型 UgcMapExtraConfigType
  optional int32 cfgId = 4;  // 需要拉的配置id 默认0 如果没有多个配置
  optional UgcMapExtraConfigWrapper config = 5;  // 配置内容
}

message RpcExtraConfigEditRes {
  optional int32 errCode = 1;
  optional UgcMapExtraConfigIndex index = 2;  // 配置索引，用于草稿保存和发布
}

// 拉取额外配置
message RpcExtraConfigGetReq {
  option (region_msg) = true;
  optional int64 creatorId = 1;
  optional UgcMapExtraConfigIndex index = 2;  // 配置索引
}

message RpcExtraConfigGetRes {
  optional int32 errCode = 1;
  optional UgcMapExtraConfigIndex index = 2;  // 配置索引
  optional UgcMapExtraConfigWrapper config = 3;  // 配置内容
}

// 批量拉取多图匹配(只返回封面)
message RpcUgcMatchLobbyDetailGetAllReq {
  option (region_msg) = true;
  optional int64 creatorId = 1;
  repeated int64 cfgIds = 2;  // 地图的id
  optional int64 clientVersion = 3;
  optional int32 cloudGameType = 4;
}

message RpcUgcMatchLobbyDetailGetAllRes {
  optional int32 errCode = 1;
  map<int64, UgcMatchLobbyMapBrief> pubBriefMap = 2;
}

// 发布态草稿共创地图权限操作请求
message RpcOperatePublishCoCreateMapReq {
  option (region_msg) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];	// 地图id
  optional int64 creatorId = 2;							          // 操作者creatorId
  optional int64 targetUid = 3;							          // 目标用户uid
  optional int64 targetCreatorId = 4;				          // 目标用户creatorId
  optional int32 operateType = 5;                      // 操作类型, 详情见枚举PublishCoCreateMapOperateType所示
}

// 发布态草稿共创地图权限操作响应
message RpcOperatePublishCoCreateMapRes {
  optional int32 result = 1;                          // 返回码, 0表示成功, 非0表示失败
  optional UgcEditorList member = 2;                  // ugc共创编辑者信息
  optional UgcItem ugcItem = 3;                        // ugc地图数据
  optional int64 editorUid = 4;                        // 共创地图主创者uid
}


// 激活UGC创作者徽章
message RpcActiveUgcCreatorBadgeReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 badgeId = 2;
}
message RpcActiveUgcCreatorBadgeRes {
  optional int32 result = 1;                          // 返回码, 0表示成功, 非0表示失败
}

// 设置创作者主页
message RpcSetCreatorHomePageReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  repeated UgcCreatorHomePageOpType opType = 2;
  optional string creatorMessage = 3;
  repeated int32 tags = 4;
  repeated int64 publishMaps = 5;
  repeated int32 badges = 6;
  optional int32 reqFrom = 7;  // 0-默认客户端  1-idip
}
message RpcSetCreatorHomePageRes {
  optional int32 result = 1;                          // 返回码, 0表示成功, 非0表示失败
}

//工具修改封面图
message RpcChangeCoverToolReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  optional UgcMdList mdList = 3;
  optional UgcCosInfo cosInfo = 4;
}
message RpcChangeCoverToolRes {
  optional int32 result = 1;                          // 返回码, 0表示成功, 非0表示失败
}

// 设置创作者主页
message RpcIdipModifyCreatorHomePageMessageReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string creatorMessage = 2;
}
message RpcIdipModifyCreatorHomePageMessageRes {
  optional int32 result = 1;                          // 返回码, 0表示成功, 非0表示失败
}

// 角色转移-ugc玩家信息表修改请求
message RpcRoleTransferZoneUgcPlayerInfoModifyReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true]; // 创作者id
  optional string openId = 2;                         // openId
  optional int32 sourcePlatId = 3;                    // 源平台id
  optional int32 targetPlatId = 4;                    // 目标平台id
}

// 角色转移-ugc玩家信息表修改响应
message RpcRoleTransferZoneUgcPlayerInfoModifyRes {
  optional int32 result = 1;                          // 返回码, 0表示成功, 非0表示失败
}

// 角色转移-OpenIdToCreatorId表备份信息
message OpenIdToCreatorIdBackupInfo {
  optional string openId = 1;                         // openId
  optional int32 platId = 2;                          // 平台id
  optional int32 worldId = 3;                         // worldId
  optional int64 uid = 4;                             // uid
  optional int64 creatorId = 5;                       // 创作者id
}

// 角色转移-OpenIdToCreatorId表查询请求
message RpcRoleTransferZoneOpenIdToCreatorIdQueryReq {
  option (region_msg) = true;
  optional string openId = 1;                         // openId
  repeated int32 platIdList = 2;                      // 平台id列表
  optional int32 worldId = 3;                         // worldId
  map<int32, int64> platIdToUidMap = 4;               // 平台id到uid的映射
}

// 角色转移-OpenIdToCreatorId表查询响应
message RpcRoleTransferZoneOpenIdToCreatorIdQueryRes {
  optional int32 result = 1;                          // 返回码, 0表示成功, 非0表示失败
  repeated OpenIdToCreatorIdBackupInfo infoList = 2;  // OpenIdToCreatorId表备份信息列表
}

message RpcRoleTransferZoneOpenIdToCreatorIdModifyReq {
  option (region_msg) = true;

  message OpenIdToCreatorIdInfo {
    optional OpenIdToCreatorIdBackupInfo info = 1;    // OpenIdToCreatorId表备份信息
    optional int32 targetPlatId = 2;                  // 目标平台id
  }

  optional string openId = 1;                         // openId
  repeated OpenIdToCreatorIdInfo infoList = 2;        // OpenIdToCreatorId表信息列表
}

message RpcRoleTransferZoneOpenIdToCreatorIdModifyRes {
  optional int32 result = 1;                          // 返回码, 0表示成功, 非0表示失败
}

// 设置创作者主页
message RpcUgcFBXAnalyzeNotifyReq {
  option (region_msg) = true;
  optional int64 fbxId = 1 [(field_hash_key) = true];
  optional int64 creatorId = 2;
  optional string md5 = 3;
  optional string bucket = 4;
  optional int32 status = 5;
  repeated FBXItem items = 6;
  optional string param = 7;
}
message RpcUgcFBXAnalyzeNotifyRes {
  optional int32 result = 1;                          // 返回码, 0表示成功, 非0表示失败
  optional string bucket = 2;
  optional UgcKeyInfo keyInfo = 3;
}


message RpcAddUgcLayerReq {
  option (region_msg) = true;
  option (rpc_call_timeout_seconds) = 3;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  optional int32 layerId =3;
  optional string layerName = 4;
}

message RpcAddUgcLayerRes {
  optional int32 result = 1;
}

// ugc共创多人编辑扣叮数据更新请求
message RpcUgcCoCreateMultiEditCodingDataReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];// creatorId
  optional int64 ugcId = 2;           // 地图ugcId
  optional CodingDataInfo data = 3;   // 扣叮数据
}

// ugc共创多人编辑扣叮数据更新响应
message RpcUgcCoCreateMultiEditCodingDataRes {
  optional int32 result = 1;
}

// 共创地图多人编辑申请占用操作类型
enum MultiEditApplyOccupyOperateType {
  APPLY_OCCUPY_OPERATE_TYPE_SET = 1;		  // 申请占用操作类型-设置
  APPLY_OCCUPY_OPERATE_TYPE_CANCEL = 2;		// 申请占用操作类型-取消
}

// ugc共创多人编辑申请占用请求
message RpcUgcCoCreateMultiEditApplyOccupyReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];// creatorId
  optional int64 ugcId = 2;           // 地图ugcId
  optional int64 applyUid = 3;        // 申请者uid
  optional int64 applyCreatorId = 4;  // 申请者creatorId
  optional int32 operateType = 5;		  // 操作类型, 详情见枚举MultiEditApplyOccupyOperateType所示
}

// ugc共创多人编辑申请占用响应
message RpcUgcCoCreateMultiEditApplyOccupyRes {
  optional int32 result = 1;          // 返回码, 0表示成功, 非0表示失败
}

// ugc cos密钥申请请求
message RpcUgcApplyKeyInfoReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];// creatorId
  optional int64 ugcId = 2;         // 地图ugcId
  optional int32 instanceType = 3;  // map类型
  optional int32 ugcResType = 4;    // 资产类型
  optional string bucket = 5;       // bucket名称
  repeated int32 applyReason = 6;   // 申请理由
}

// ugc cos密钥申请响应
message RpcUgcApplyKeyInfoRes {

  message UgcApplyKeyInfo {
    optional int32 applyReason = 1;   // 申请理由
    optional UgcKeyInfo info = 2;     // cos密钥
  }

  optional int32 result = 1;          // 返回码, 0表示成功, 非0表示失败
  repeated UgcApplyKeyInfo infos = 2; // 申请密钥信息列表
}

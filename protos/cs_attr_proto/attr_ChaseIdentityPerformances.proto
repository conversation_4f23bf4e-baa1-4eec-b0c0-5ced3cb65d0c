syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_ChaseIdentityPerformance.proto";

message proto_ChaseIdentityPerformances {
    option (wea_attr_cls) = "com.tencent.wea.attr.ChaseIdentityPerformances";
    // 对局表现记录
    repeated proto_ChaseIdentityPerformance ChaseIdentityPerformance = 1;
    repeated int32 ChaseIdentityPerformance_deleted = 2001;
    optional bool ChaseIdentityPerformance_is_cleared = 4001;
}
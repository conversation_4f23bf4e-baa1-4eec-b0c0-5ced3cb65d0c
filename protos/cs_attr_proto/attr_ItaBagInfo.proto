syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_KvLL.proto";

message proto_ItaBagInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.ItaBagInfo";
    option (wea_attr_key) = "itemUUID";
    // 痛包道具UUID
    optional int64 itemUUID = 1;
    // cos存储信息
    optional string cosUrl = 2;
    // itemId => num
    repeated proto_KvLL badgeInfo = 3;
    repeated int64 badgeInfo_deleted = 2003;
    optional bool badgeInfo_is_cleared = 4003;
}
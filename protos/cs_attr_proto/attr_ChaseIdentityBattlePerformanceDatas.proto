syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_ChaseIdentityBattlePerformanceData.proto";

message proto_ChaseIdentityBattlePerformanceDatas {
    option (wea_attr_cls) = "com.tencent.wea.attr.ChaseIdentityBattlePerformanceDatas";
    // 身份对局详情
    repeated proto_ChaseIdentityBattlePerformanceData chaseIdentityBattlePerformanceData = 1;
    repeated int32 chaseIdentityBattlePerformanceData_deleted = 2001;
    optional bool chaseIdentityBattlePerformanceData_is_cleared = 4001;
}
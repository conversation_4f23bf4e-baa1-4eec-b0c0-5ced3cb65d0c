syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_MainMasterInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.MainMasterInfo";
    option (wea_attr_key) = "cycleId";
    // 周目id
    optional int32 cycleId = 1;
    // 当前进度
    optional int32 progress = 2;
    // 已经领取的奖励
    repeated int32 rewardedId = 3;
    repeated int32 rewardedId_deleted = 2003;
    optional bool rewardedId_is_cleared = 4003;
    // 当前进度*100
    optional int32 progressDb = 4;
}
syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_ChaseIdentityProficiencyData {
    option (wea_attr_cls) = "com.tencent.wea.attr.ChaseIdentityProficiencyData";
    option (wea_attr_key) = "identityId";
    // 身份ID
    optional int32 identityId = 1;
    // 熟练度
    optional int32 proficiency = 2;
    // 已解锁的角色小传
    repeated int32 unlockBiography = 3;
    repeated int32 unlockBiography_deleted = 2003;
    optional bool unlockBiography_is_cleared = 4003;
    // 是否解锁专精
    optional int32 unlockSpecialization = 4;
    // 已领取过的进度奖励
    repeated int32 claimedProgressReward = 5;
    repeated int32 claimedProgressReward_deleted = 2005;
    optional bool claimedProgressReward_is_cleared = 4005;
}
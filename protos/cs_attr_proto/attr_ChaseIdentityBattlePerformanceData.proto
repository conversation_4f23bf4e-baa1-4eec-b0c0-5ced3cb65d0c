syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_ChaseIdentityBattlePerformance.proto";

message proto_ChaseIdentityBattlePerformanceData {
    option (wea_attr_cls) = "com.tencent.wea.attr.ChaseIdentityBattlePerformanceData";
    option (wea_attr_key) = "identityId";
    // 身份ID
    optional int32 identityId = 1;
    // 对局数据记录
    repeated proto_ChaseIdentityBattlePerformance chaseIdentityBattlePerformance = 2;
    repeated int32 chaseIdentityBattlePerformance_deleted = 2002;
    optional bool chaseIdentityBattlePerformance_is_cleared = 4002;
}
syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_Bag.proto";

message proto_BagInfoDb {
    option (wea_attr_cls) = "com.tencent.wea.attr.BagInfoDb";
    // 背包
    repeated proto_Bag item = 1;
    repeated int32 item_deleted = 2001;
    optional bool item_is_cleared = 4001;
    repeated proto_Bag observingItem = 2;
    repeated int32 observingItem_deleted = 2002;
    optional bool observingItem_is_cleared = 4002;
    // 创建角色时已赠送的item, 杂项中的regItem
    repeated int32 createRoleItem = 3;
    repeated int32 createRoleItem_deleted = 2003;
    optional bool createRoleItem_is_cleared = 4003;
    // 道具设置
    optional bool actionSeting = 4;
}
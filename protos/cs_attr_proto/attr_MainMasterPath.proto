syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_ConditionGroupInfo.proto";
import "attr_MainMasterInfo.proto";

message proto_MainMasterPath {
    option (wea_attr_cls) = "com.tencent.wea.attr.MainMasterPath";
    option (wea_attr_key) = "masterEnumId";
    // 大师之路枚举id
    optional int32 masterEnumId = 1;
    // 大师之路周目进度数据
    repeated proto_MainMasterInfo masterPathInfo = 2;
    repeated int32 masterPathInfo_deleted = 2002;
    optional bool masterPathInfo_is_cleared = 4002;
    // 条件组信息
    repeated proto_ConditionGroupInfo unlockCycleCondition = 3;
    repeated int32 unlockCycleCondition_deleted = 2003;
    optional bool unlockCycleCondition_is_cleared = 4003;
}
syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_ChaseIdentityProficiencyData.proto";

message proto_ChaseIdentityProficiencyInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.ChaseIdentityProficiencyInfo";
    // 首次解锁专精是否成功
    optional int32 completeFirstCheckOpenFightPower = 1;
    // 大王熟练度
    repeated proto_ChaseIdentityProficiencyData chaseIdentityProficiencyDatas = 2;
    repeated int32 chaseIdentityProficiencyDatas_deleted = 2002;
    optional bool chaseIdentityProficiencyDatas_is_cleared = 4002;
}
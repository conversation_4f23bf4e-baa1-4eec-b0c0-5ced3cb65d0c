syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_MainMasterPath.proto";

message proto_MasterPatchData {
    option (wea_attr_cls) = "com.tencent.wea.attr.MasterPatchData";
    // 大师之路信息
    repeated proto_MainMasterPath mainMasterPathMap = 1;
    repeated int32 mainMasterPathMap_deleted = 2001;
    optional bool mainMasterPathMap_is_cleared = 4001;
}
--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_SPBuildTypeConfigData"] = {
        TableKey = "typeId",
        SubTableNames = {
			"BuildTypeConfigData",
        }
    },
    ["table_LevelInfoData"] = {
        TableKey = "Id",
        SubTableNames = {
			"LevelInfoData_SPGame",
        }
    },
    ["table_DeviceProfileCommandData"] = {
        TableKey = "Id",
        SubTableNames = {
			"PWDeviceFPSCommandAndroidStarp",
			"PWDeviceFPSCommandIOSStarp",
			"PWDeviceFPSCommandWindowsStarp",
			"PWDeviceProfileCommandAndroidStarp",
			"PWDeviceProfileCommandIOSStarp",
			"PWDeviceProfileCommandWindowsStarp",
			"PWDeviceScreenPercentageCommandAndroidStarp",
			"PWDeviceScreenPercentageCommandIOSStarp",
			"PWDeviceScreenPercentageCommandWindowsStarp",
        }
    },
    ["table_DeviceProfileInfoData"] = {
        TableKey = "Level",
        SubTableNames = {
			"PWDeviceFPSInfoAndroidStarp",
			"PWDeviceFPSInfoIOSStarp",
			"PWDeviceFPSInfoWindows",
			"PWDeviceProfileInfoAndroidStarp",
			"PWDeviceProfileInfoIOSStarp",
			"PWDeviceProfileInfoWindowsStarp",
			"PWDeviceScreenPercentageInfoAndroidStarp",
			"PWDeviceScreenPercentageInfoIOSStarp",
			"PWDeviceScreenPercentageInfoWindows",
        }
    },
    ["table_ProtectedScoreAdditionalData"] = {
        TableKey = "id",
        SubTableNames = {
			"ProtectedScoreAdditionalData_starp_pvp",
        }
    },
    ["table_DegreeTypeData"] = {
        TableKey = "id",
        SubTableNames = {
			"QDTDegreeTypeData_starp_pvp",
        }
    },
    ["table_EspecialIntegralData"] = {
        TableKey = "id",
        SubTableNames = {
			"QDTEspecialIntegralData_starp_pvp",
        }
    },
    ["table_SeasonCfgData"] = {
        TableKey = "id",
        SubTableNames = {
			"QDTSeasonCfgData_starp_pvp",
        }
    },
    ["table_QualifyingLevelDimensionConditionData"] = {
        TableKey = "id",
        SubTableNames = {
			"QualifyingLevelDimensionConditionData_starp_pvp",
        }
    },
    ["table_QualifyingLevelDimensionScoreData"] = {
        TableKey = "id",
        SubTableNames = {
			"QualifyingLevelDimensionScoreData_starp_pvp",
        }
    },
    ["table_RankKingDegreeData"] = {
        TableKey = "id",
        SubTableNames = {
			"RankKingDegreeData_starp_pvp",
        }
    },
    ["table_ResCaptureGuaranteed"] = {
        TableKey = "captureFrequency",
        SubTableNames = {
			"ResCaptureGuaranteed",
        }
    },
    ["table_ResCaptureLevelSuppress"] = {
        TableKey = "deltaLevel",
        SubTableNames = {
			"ResCaptureLevelSuppress",
        }
    },
    ["table_ResCaptureProbabilityGrowth"] = {
        TableKey = "passCaptureTestTimes",
        SubTableNames = {
			"ResCaptureProbabilityGrowth",
        }
    },
    ["table_ResCaptureProbabilityScaling"] = {
        TableKey = "realCaptureProbability",
        SubTableNames = {
			"ResCaptureProbabilityScaling",
        }
    },
    ["table_ResCharacterCapturePower"] = {
        TableKey = "captureLevel",
        SubTableNames = {
			"ResCharacterCapturePower",
        }
    },
    ["table_ResDamageLevelSuppress"] = {
        TableKey = "id",
        SubTableNames = {
			"ResDamageLevelSuppress",
        }
    },
    ["table_ResElementDamageEffectPVE"] = {
        TableKey = "elementType",
        SubTableNames = {
			"ResElementDamageEffectPVE",
        }
    },
    ["table_ResElementDamageEffectPVP"] = {
        TableKey = "elementType",
        SubTableNames = {
			"ResElementDamageEffectPVP",
        }
    },
    ["table_ResElementTenacityEffectPVE"] = {
        TableKey = "elementType",
        SubTableNames = {
			"ResElementTenacityEffectPVE",
        }
    },
    ["table_ResElementTenacityEffectPVP"] = {
        TableKey = "elementType",
        SubTableNames = {
			"ResElementTenacityEffectPVP",
        }
    },
    ["table_ResNewCaptureLevelSuppress"] = {
        TableKey = "monsterLevelHigherNum",
        SubTableNames = {
			"ResNewCaptureLevelSuppress",
        }
    },
    ["table_SPBusinessFuncSwitch"] = {
        TableKey = "businessType",
        SubTableNames = {
			"ResSPBusinessFuncSwitch",
        }
    },
    ["table_ResSPCaptureBallConfig"] = {
        TableKey = "captureBallId",
        SubTableNames = {
			"ResSPCaptureBallConfig",
        }
    },
    ["table_SPConstantData"] = {
        TableKey = "name",
        SubTableNames = {
			"ResSPConstantData",
        }
    },
    ["table_SPPvpBasicConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ResSPPvpBasicConf",
        }
    },
    ["table_SPResourceBalanceRule"] = {
        TableKey = "id",
        SubTableNames = {
			"ResSPResourceBalanceRule",
        }
    },
    ["table_ResSPTaskActiveReward"] = {
        TableKey = "id",
        SubTableNames = {
			"ResSPTaskActiveRewardConf",
        }
    },
    ["table_ResSPTaskConditionSkip"] = {
        TableKey = "id",
        SubTableNames = {
			"ResSPTaskConditionSkipConf",
        }
    },
    ["table_ResSPTaskGroup"] = {
        TableKey = "groupId",
        SubTableNames = {
			"ResSPTaskGroupConf",
        }
    },
    ["table_ResSPTaskGroupType"] = {
        TableKey = "groupTypeId",
        SubTableNames = {
			"ResSPTaskGroupTypeConf",
        }
    },
    ["table_ResSPTimeRefreshRule"] = {
        TableKey = "id",
        SubTableNames = {
			"ResSPTimeRefreshRule",
        }
    },
    ["table_SPTlogChecker"] = {
        TableKey = "stepId",
        SubTableNames = {
			"ResSPTlogChecker",
        }
    },
    ["table_SPAbilityActionData"] = {
        TableKey = "actionId",
        SubTableNames = {
			"SPAbilityActionData",
        }
    },
    ["table_SPAbilityActionParam"] = {
        TableKey = "LogicID",
        SubTableNames = {
			"SPAbilityActionParam",
        }
    },
    ["table_SPAbilityConditionData"] = {
        TableKey = "conditionId",
        SubTableNames = {
			"SPAbilityConditionData",
        }
    },
    ["table_SPAbilityConsumeData"] = {
        TableKey = "consumeId",
        SubTableNames = {
			"SPAbilityConsumeData",
        }
    },
    ["table_SPAbility"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAbilityData",
        }
    },
    ["table_SPAbilityEventData"] = {
        TableKey = "eventId",
        SubTableNames = {
			"SPAbilityEventData",
        }
    },
    ["table_SPAchievementConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAchievementConf",
        }
    },
    ["table_SPAdventureGroupSpeedUp"] = {
        TableKey = "level",
        SubTableNames = {
			"SPAdventureGroup",
        }
    },
    ["table_SPAdventureLevel"] = {
        TableKey = "level",
        SubTableNames = {
			"SPAdventureLevel",
        }
    },
    ["table_SPAdventureShop"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAdventureShop",
        }
    },
    ["table_SPAdventureShopSubPage"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAdventureShopSubPage",
        }
    },
    ["table_SPAffixData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAffixData",
        }
    },
    ["table_SPAfterLeavingCombatResetAttributeList"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAfterLeavingCombatResetAttributeList",
        }
    },
    ["table_SPAirWall"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAirWall",
        }
    },
    ["table_SPAssistQuickTextConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAssistQuickTextConfData",
        }
    },
    ["table_SPAvatarPartInGameConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAvatarPartInGameConfig",
        }
    },
    ["table_SPAvatarPartPreviewConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAvatarPartPreviewConfig",
        }
    },
    ["table_SPAvatarSuitInGameConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAvatarSuitInGameConfig",
        }
    },
    ["table_SPAvatarSuitPreviewConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPAvatarSuitPreviewConfig",
        }
    },
    ["table_ResSPBInteractUI"] = {
        TableKey = "id",
        SubTableNames = {
			"SPBInteractUI",
        }
    },
    ["table_SPBalanceDatabase"] = {
        TableKey = "attrBalanceIndex",
        SubTableNames = {
			"SPBalanceDatabase",
        }
    },
    ["table_SPBalanceIndexData"] = {
        TableKey = "balanceId",
        SubTableNames = {
			"SPBalanceIndexData",
        }
    },
    ["table_SPBattleTalent"] = {
        TableKey = "battleTalentId",
        SubTableNames = {
			"SPBattleTalent",
        }
    },
    ["table_SPBattleTalentElementEffect"] = {
        TableKey = "id",
        SubTableNames = {
			"SPBattleTalentElementEffect",
        }
    },
    ["table_SPBattleTalentElementWeapon"] = {
        TableKey = "id",
        SubTableNames = {
			"SPBattleTalentElementWeapon",
        }
    },
    ["table_SPBattleTalentEnchant"] = {
        TableKey = "enchantId",
        SubTableNames = {
			"SPBattleTalentEnchant",
        }
    },
    ["table_SPBattleTalentGroup"] = {
        TableKey = "talentGroupId",
        SubTableNames = {
			"SPBattleTalentGroup",
        }
    },
    ["table_SPBattleTalentPosition"] = {
        TableKey = "talentPosition",
        SubTableNames = {
			"SPBattleTalentPosition",
        }
    },
    ["table_SPBattleTalentWeaponInlay"] = {
        TableKey = "originId",
        SubTableNames = {
			"SPBattleTalentWeaponInlay",
        }
    },
    ["table_SPBreedAbilityInherit"] = {
        TableKey = "id",
        SubTableNames = {
			"SPBreedAbilityInherit",
        }
    },
    ["table_SPBreedAbilityMutation"] = {
        TableKey = "num",
        SubTableNames = {
			"SPBreedAbilityMutation",
        }
    },
    ["table_SPBreedAbilityNum"] = {
        TableKey = "sum",
        SubTableNames = {
			"SPBreedAbilityNum",
        }
    },
    ["table_SPBreedBuildItemConfig"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBreedBuildItemConfig",
        }
    },
    ["table_SPBreedCombination"] = {
        TableKey = "typeId",
        SubTableNames = {
			"SPBreedCombination",
        }
    },
    ["table_SPBroadcastData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPBroadcastData",
        }
    },
    ["table_SPBroadcastTemplateData"] = {
        TableKey = "templateID",
        SubTableNames = {
			"SPBroadcastTemplateData",
        }
    },
    ["table_SPBuffData"] = {
        TableKey = "buffId",
        SubTableNames = {
			"SPBuffData",
        }
    },
    ["table_SPBuffData_ABTest"] = {
        TableKey = "buffId",
        SubTableNames = {
			"SPBuffData_ABTest",
        }
    },
    ["table_SPBuffFlowExtraInfo"] = {
        TableKey = "buffId",
        SubTableNames = {
			"SPBuffFlowExtraInfo",
        }
    },
    ["table_SPBuildBaseView"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildBaseView",
        }
    },
    ["table_SPBuildBattle"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildBattle",
        }
    },
    ["table_SPBuildBattleBullet"] = {
        TableKey = "ammoID",
        SubTableNames = {
			"SPBuildBattleBullet",
        }
    },
    ["table_SPBuildConfigData"] = {
        TableKey = "typeId",
        SubTableNames = {
			"SPBuildConfigData",
        }
    },
    ["table_SPBuildElecGenerator"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildElecGenerator",
        }
    },
    ["table_SPBuildFarm"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildFarm",
        }
    },
    ["table_SPBuildFarmCrop"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPBuildFarmCrop",
        }
    },
    ["table_SPBuildFarmCropState"] = {
        TableKey = "CropChangeID",
        SubTableNames = {
			"SPBuildFarmCropState",
        }
    },
    ["table_SPBuildFarmVegeteal"] = {
        TableKey = "seedID",
        SubTableNames = {
			"SPBuildFarmVegeteal",
        }
    },
    ["table_SPBuildFormula"] = {
        TableKey = "formulaID",
        SubTableNames = {
			"SPBuildFormula",
        }
    },
    ["table_SPBuildGuildCheck"] = {
        TableKey = "closeUIName",
        SubTableNames = {
			"SPBuildGuildCheck",
        }
    },
    ["table_SPBuildLight"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildLight",
        }
    },
    ["table_SPBuildMake"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildMake",
        }
    },
    ["table_SPBuildMakeEquip"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildMakeEquip",
        }
    },
    ["table_SPBuildMakeFood"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildMakeFood",
        }
    },
    ["table_SPBuildMakeFoodProcess"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPBuildMakeFoodProcess",
        }
    },
    ["table_SPBuildPasture"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildPasture",
        }
    },
    ["table_SPBuildPastureDrop"] = {
        TableKey = "petID",
        SubTableNames = {
			"SPBuildPastureDrop",
        }
    },
    ["table_SPBuildPetRelax"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildPetRelax",
        }
    },
    ["table_SPBuildPetSleep"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildPetSleep",
        }
    },
    ["table_SPBuildPlanter"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildPlanter",
        }
    },
    ["table_SPBuildPlayerSleep"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildPlayerSleep",
        }
    },
    ["table_SPBuildProduct"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildProduct",
        }
    },
    ["table_SPBuildStorage"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildStorage",
        }
    },
    ["table_SPBuildTech"] = {
        TableKey = "changeID",
        SubTableNames = {
			"SPBuildTech",
        }
    },
    ["table_SPBuildTempControl"] = {
        TableKey = "buildID",
        SubTableNames = {
			"SPBuildTempControl",
        }
    },
    ["table_SPBuildTempControlEffect"] = {
        TableKey = "tempControlEffectID",
        SubTableNames = {
			"SPBuildTempControlEffect",
        }
    },
    ["table_SPBuildTerminalCond"] = {
        TableKey = "type",
        SubTableNames = {
			"SPBuildTerminalCond",
        }
    },
    ["table_SPBuildTerminalLevel"] = {
        TableKey = "level",
        SubTableNames = {
			"SPBuildTerminalLevel",
        }
    },
    ["table_SPCLogSettingConf"] = {
        TableKey = "categoryName",
        SubTableNames = {
			"SPCLogSettingConf",
        }
    },
    ["table_SPCaptureExp"] = {
        TableKey = "captureTimes",
        SubTableNames = {
			"SPCaptureExpCfg",
        }
    },
    ["table_SPCaptureExpGroup"] = {
        TableKey = "awardLevel",
        SubTableNames = {
			"SPCaptureExpGroup",
        }
    },
    ["table_SPCaptureTip"] = {
        TableKey = "id",
        SubTableNames = {
			"SPCaptureTip",
        }
    },
    ["table_SPCareerIdolStoryInfoData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPCareerIdolStoryInfoData",
        }
    },
    ["table_SPCharacterWork"] = {
        TableKey = "workTypeID",
        SubTableNames = {
			"SPCharacterWork",
        }
    },
    ["table_SPChoppableConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPChoppableConfigData",
        }
    },
    ["table_SPClimbSpeed"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPClimbSpeed",
        }
    },
    ["table_SPCombatAttribute"] = {
        TableKey = "attrId",
        SubTableNames = {
			"SPCombatAttribute",
        }
    },
    ["table_SPCombatEffectCalc"] = {
        TableKey = "id",
        SubTableNames = {
			"SPCombatEffectCalcConfig",
        }
    },
    ["table_SPCommonUITip"] = {
        TableKey = "id",
        SubTableNames = {
			"SPCommonUITip",
        }
    },
    ["table_SPConditionCombinationConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SPConditionCombinationConf",
        }
    },
    ["table_SPConfigUpdateLevelData"] = {
        TableKey = "ConfigID",
        SubTableNames = {
			"SPConfigUpdateLevelData",
        }
    },
    ["table_SPCreateTeamConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SPCreateTeamConf",
        }
    },
    ["table_SPDamageAttenuationConfig"] = {
        TableKey = "damageAttenuationId",
        SubTableNames = {
			"SPDamageAttenuationConfig",
        }
    },
    ["table_SPDamageConfig"] = {
        TableKey = "damageId",
        SubTableNames = {
			"SPDamageConfig",
        }
    },
    ["table_SPDamageConfig_ABTest"] = {
        TableKey = "damageId",
        SubTableNames = {
			"SPDamageConfig_ABTest",
        }
    },
    ["table_SPDamageDistanceAttenuation"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPDamageDistanceAttenuation",
        }
    },
    ["table_SPDamageTypeConfig"] = {
        TableKey = "damageTypeId",
        SubTableNames = {
			"SPDamageTypeConfig",
        }
    },
    ["table_SPDeviceCorrectCommandData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPDeviceCorrectCommandAndroid",
			"SPDeviceCorrectCommandIOS",
			"SPDeviceCorrectCommandWindows",
        }
    },
    ["table_SPDressUpItem"] = {
        TableKey = "id",
        SubTableNames = {
			"SPDressUpItem",
        }
    },
    ["table_SPDropBag"] = {
        TableKey = "rowId",
        SubTableNames = {
			"SPDropBag",
        }
    },
    ["table_SPDungeonData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPDungeonData",
        }
    },
    ["table_SPDungeonLinkPoint"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPDungeonLinkPoint",
        }
    },
    ["table_SPDungeonStepData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPDungeonStepData",
        }
    },
    ["table_SPElectricLevel"] = {
        TableKey = "ID",
        SubTableNames = {
			"SPElectricLevel",
        }
    },
    ["table_SPEncounterData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPEncounterData",
        }
    },
    ["table_SPEquipmentAttributeConfig"] = {
        TableKey = "AttrId",
        SubTableNames = {
			"SPEquipmentAttributeConfig",
        }
    },
    ["table_SPEquipmentFence"] = {
        TableKey = "FenceType",
        SubTableNames = {
			"SPEquipmentFence",
        }
    },
    ["table_SPEquipmentFormulaIdConfig"] = {
        TableKey = "FormulaId",
        SubTableNames = {
			"SPEquipmentFormulaIdConfig",
        }
    },
    ["table_SPEquipmentItemConfig"] = {
        TableKey = "EquipmentId",
        SubTableNames = {
			"SPEquipmentItemConfig",
        }
    },
    ["table_SPEquipmentPart"] = {
        TableKey = "PartId",
        SubTableNames = {
			"SPEquipmentPart",
        }
    },
    ["table_SPEquipmentSetConfig"] = {
        TableKey = "EquipmentSetId",
        SubTableNames = {
			"SPEquipmentSetConfig",
        }
    },
    ["table_SPEquipmentSetEffectConfig"] = {
        TableKey = "EquipmentSetId",
        SubTableNames = {
			"SPEquipmentSetEffectConfig",
        }
    },
    ["table_SPEquipmentSetGradeDefine"] = {
        TableKey = "GradeId",
        SubTableNames = {
			"SPEquipmentSetGradeDefine",
        }
    },
    ["table_SPEquipmentSetUnlockDefine"] = {
        TableKey = "UnlockId",
        SubTableNames = {
			"SPEquipmentSetUnlockDefine",
        }
    },
    ["table_SPEquipmentSlotConfig"] = {
        TableKey = "SlotId",
        SubTableNames = {
			"SPEquipmentSlotConfig",
        }
    },
    ["table_SPEquipmentSortConfig"] = {
        TableKey = "optionID",
        SubTableNames = {
			"SPEquipmentSortConfig",
        }
    },
    ["table_SPExperienceLimitConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPExperienceLimitConfig",
        }
    },
    ["table_SPExperienceLimitDefine"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPExperienceLimitDefine",
        }
    },
    ["table_SPExperienceWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPExperienceWhiteListConfig",
        }
    },
    ["table_SPFactionRelation"] = {
        TableKey = "factionId",
        SubTableNames = {
			"SPFactionRelation",
        }
    },
    ["table_SPFlyPetWorkSlot"] = {
        TableKey = "id",
        SubTableNames = {
			"SPFlyPetWorkSlot",
        }
    },
    ["table_SPFocusActorTypeConfigData"] = {
        TableKey = "FocusActorType",
        SubTableNames = {
			"SPFocusActorTypeConfigData",
        }
    },
    ["table_SPFriendIntimacyActionType"] = {
        TableKey = "id",
        SubTableNames = {
			"SPFriendIntimacyActionType",
        }
    },
    ["table_SPFriendIntimacyDeduction"] = {
        TableKey = "dayNum",
        SubTableNames = {
			"SPFriendIntimacyDeduction",
        }
    },
    ["table_SPFriendIntimacyLevel"] = {
        TableKey = "level",
        SubTableNames = {
			"SPFriendIntimacyLevel",
        }
    },
    ["table_SPFriendSkillData"] = {
        TableKey = "friendSkillId",
        SubTableNames = {
			"SPFriendSkillData",
        }
    },
    ["table_SPFriendSkillEnergy"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPFriendSkillEnergy",
        }
    },
    ["table_SPFriendSkillEnergyCost"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPFriendSkillEnergyCost",
        }
    },
    ["table_SPFriendSkillMapping"] = {
        TableKey = "friendSkillGroupId",
        SubTableNames = {
			"SPFriendSkillMapping",
        }
    },
    ["table_SPFunctionControl"] = {
        TableKey = "id",
        SubTableNames = {
			"SPFunctionControl",
        }
    },
    ["table_SPGPOCage"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPGPOCage",
        }
    },
    ["table_SPGPOInteractionTarget"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPGPOInteractionTarget",
        }
    },
    ["table_SPGPOItems"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPGPOItems",
        }
    },
    ["table_SPGPONPCTypeObjects"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPGPONPCTypeObjects",
        }
    },
    ["table_SPGPOTreasureChest"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPGPOTreasureChest",
        }
    },
    ["table_SPGameArmor"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPGameArmor",
        }
    },
    ["table_SPGameArmorAttribute"] = {
        TableKey = "keyID",
        SubTableNames = {
			"SPGameArmorAttribute",
        }
    },
    ["table_SPGameArmorSetEffect"] = {
        TableKey = "effectID",
        SubTableNames = {
			"SPGameArmorSetEffect",
        }
    },
    ["table_SPGameArmorSort"] = {
        TableKey = "optionID",
        SubTableNames = {
			"SPGameArmorSort",
        }
    },
    ["table_SPGameBackpack"] = {
        TableKey = "backpackType",
        SubTableNames = {
			"SPGameBackpack",
        }
    },
    ["table_SPGameBackpackFilterUILayout"] = {
        TableKey = "layoutId",
        SubTableNames = {
			"SPGameBackpackFilterUILayout",
        }
    },
    ["table_SPGameClothingItem"] = {
        TableKey = "itemId",
        SubTableNames = {
			"SPGameClothingItem",
        }
    },
    ["table_SPGameDropBagTreasure"] = {
        TableKey = "itemId",
        SubTableNames = {
			"SPGameDropBagTreasure",
        }
    },
    ["table_SPGameEquipFence"] = {
        TableKey = "fenceType",
        SubTableNames = {
			"SPGameEquipFence",
        }
    },
    ["table_SPGameEquipSlot"] = {
        TableKey = "slotID",
        SubTableNames = {
			"SPGameEquipSlot",
        }
    },
    ["table_SPGameFood"] = {
        TableKey = "itemId",
        SubTableNames = {
			"SPGameFood",
        }
    },
    ["table_SPGameGachaBuff"] = {
        TableKey = "buffID",
        SubTableNames = {
			"SPGameGachaBuff",
        }
    },
    ["table_SPGameGachaItem"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPGameGachaItem",
        }
    },
    ["table_SPGameGachaOnetimeReward"] = {
        TableKey = "dropBagItemID",
        SubTableNames = {
			"SPGameGachaOnetimeReward",
        }
    },
    ["table_SPGameGachaReward"] = {
        TableKey = "rewardLevel",
        SubTableNames = {
			"SPGameGachaReward",
        }
    },
    ["table_SPGameGachaSort"] = {
        TableKey = "optionID",
        SubTableNames = {
			"SPGameGachaSort",
        }
    },
    ["table_SPGameGlobal"] = {
        TableKey = "ID",
        SubTableNames = {
			"SPGameGlobal",
        }
    },
    ["table_SPGameGuideDebugInfo"] = {
        TableKey = "index",
        SubTableNames = {
			"SPGameGuideDebugInfo",
        }
    },
    ["table_SPGameHelper"] = {
        TableKey = "promptId",
        SubTableNames = {
			"SPGameHelper",
        }
    },
    ["table_SPGameImportantItem"] = {
        TableKey = "itemId",
        SubTableNames = {
			"SPGameImportantItem",
        }
    },
    ["table_SPGameItemSummary"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPGameItemSummary",
        }
    },
    ["table_SPGameItemType"] = {
        TableKey = "itemTypeId",
        SubTableNames = {
			"SPGameItemType",
        }
    },
    ["table_SPGameMapIcon"] = {
        TableKey = "ID",
        SubTableNames = {
			"SPGameMapIcon",
        }
    },
    ["table_SPGameMapIconTypeSummary"] = {
        TableKey = "ID",
        SubTableNames = {
			"SPGameMapIconTypeSummary",
        }
    },
    ["table_SPGameMedicine"] = {
        TableKey = "itemId",
        SubTableNames = {
			"SPGameMedicine",
        }
    },
    ["table_SPGameOrnament"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPGameOrnament",
        }
    },
    ["table_SPGameOrnamentAttribute"] = {
        TableKey = "keyID",
        SubTableNames = {
			"SPGameOrnamentAttribute",
        }
    },
    ["table_SPGameOrnamentDecompose"] = {
        TableKey = "keyID",
        SubTableNames = {
			"SPGameOrnamentDecompose",
        }
    },
    ["table_SPGameOrnamentEffect"] = {
        TableKey = "keyID",
        SubTableNames = {
			"SPGameOrnamentEffect",
        }
    },
    ["table_SPGameOrnamentPool"] = {
        TableKey = "keyID",
        SubTableNames = {
			"SPGameOrnamentPool",
        }
    },
    ["table_SPGameOrnamentPoolDetail"] = {
        TableKey = "keyID",
        SubTableNames = {
			"SPGameOrnamentPoolDetail",
        }
    },
    ["table_SPGameOrnamentSet"] = {
        TableKey = "suitID",
        SubTableNames = {
			"SPGameOrnamentSet",
        }
    },
    ["table_SPGameOrnamentSetEffect"] = {
        TableKey = "effectID",
        SubTableNames = {
			"SPGameOrnamentSetEffect",
        }
    },
    ["table_SPGameOrnamentSort"] = {
        TableKey = "optionID",
        SubTableNames = {
			"SPGameOrnamentSort",
        }
    },
    ["table_SPGameOrnamentType"] = {
        TableKey = "typeID",
        SubTableNames = {
			"SPGameOrnamentType",
        }
    },
    ["table_SPGamePet"] = {
        TableKey = "itemId",
        SubTableNames = {
			"SPGamePet",
        }
    },
    ["table_SPGameSkillFruit"] = {
        TableKey = "itemId",
        SubTableNames = {
			"SPGameSkillFruit",
        }
    },
    ["table_SPGameStaticIcon"] = {
        TableKey = "ID",
        SubTableNames = {
			"SPGameStaticIcon",
        }
    },
    ["table_SPGameStaticVisible"] = {
        TableKey = "ID",
        SubTableNames = {
			"SPGameStaticVisible",
        }
    },
    ["table_SPGameWeapon"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPGameWeapon",
        }
    },
    ["table_SPGameXingShouBall"] = {
        TableKey = "itemId",
        SubTableNames = {
			"SPGameXingShouBall",
        }
    },
    ["table_SPGameXingShouItem"] = {
        TableKey = "itemId",
        SubTableNames = {
			"SPGameXingShouItem",
        }
    },
    ["table_SPGiftPackageData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPGiftPackageConf",
        }
    },
    ["table_SPGlidingData"] = {
        TableKey = "ItemID",
        SubTableNames = {
			"SPGlidingData",
        }
    },
    ["table_SPGodStatueStoryInfoData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPGodStatueStoryInfoData",
        }
    },
    ["table_SPGroupActivityOutput"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGroupActivityOutput",
        }
    },
    ["table_SPGroupGachaConfig"] = {
        TableKey = "groupLevel",
        SubTableNames = {
			"SPGroupGachaConfig",
        }
    },
    ["table_SPGroupIcon"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGroupIcon",
        }
    },
    ["table_SPGroupLevel"] = {
        TableKey = "level",
        SubTableNames = {
			"SPGroupLevel",
        }
    },
    ["table_SPGrowthPathChapter"] = {
        TableKey = "chapterId",
        SubTableNames = {
			"SPGrowthPathChapterConf",
        }
    },
    ["table_SPGrowthPathChildMission"] = {
        TableKey = "childMissionId",
        SubTableNames = {
			"SPGrowthPathChildMissionConf",
        }
    },
    ["table_SPGrowthPathGeneral"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGrowthPathGeneralConf",
        }
    },
    ["table_SPGrowthPathMission"] = {
        TableKey = "missionId",
        SubTableNames = {
			"SPGrowthPathMissionConf",
        }
    },
    ["table_SPGuideBubbleConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGuideBubbleConfig",
        }
    },
    ["table_SPGuideConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGuideConfig",
        }
    },
    ["table_SPGuideWindowConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGuideWindowConfig",
        }
    },
    ["table_SPGuildEfficiencyConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGuildEfficiencyConfig",
        }
    },
    ["table_SPGuildHeadIconConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGuildHeadIconConfig",
        }
    },
    ["table_SPGuildIcon"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGuildIcon",
        }
    },
    ["table_SPGuildJoinCdConfig"] = {
        TableKey = "joinCount",
        SubTableNames = {
			"SPGuildJoinCdConfig",
        }
    },
    ["table_SPGuildMapIconConfig"] = {
        TableKey = "mapIconType",
        SubTableNames = {
			"SPGuildMapIconConfig",
        }
    },
    ["table_SPGuildMaterialConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGuildMaterialConfig",
        }
    },
    ["table_SPGuildNewsConfig"] = {
        TableKey = "type",
        SubTableNames = {
			"SPGuildNewsConfig",
        }
    },
    ["table_SPGuildParamConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGuildParamConfig",
        }
    },
    ["table_SPGuildSystemMessageConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGuildSystemMessageConfig",
        }
    },
    ["table_SPGuildTitleConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPGuildTitleConfig",
        }
    },
    ["table_SPHallGuideConf"] = {
        TableKey = "name",
        SubTableNames = {
			"SPHallGuideConf",
        }
    },
    ["table_SPHitConfig"] = {
        TableKey = "hitId",
        SubTableNames = {
			"SPHitConfig",
        }
    },
    ["table_SPImpactFoliageSetting"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPImpactFoliageSetting",
        }
    },
    ["table_ResSPInteract3DUI"] = {
        TableKey = "id",
        SubTableNames = {
			"SPInteract3DUI",
        }
    },
    ["table_ResSPInteractProgressUI"] = {
        TableKey = "id",
        SubTableNames = {
			"SPInteractProgressUI",
        }
    },
    ["table_SPInvasionLevelConfigData"] = {
        TableKey = "invasionLevel",
        SubTableNames = {
			"SPInvasionLevelConfigData",
        }
    },
    ["table_SPItemAcquisitionPathConf"] = {
        TableKey = "acquisitionPathId",
        SubTableNames = {
			"SPItemAcquisitionPathConf",
        }
    },
    ["table_SPItemGetPopupConfig"] = {
        TableKey = "itemTypeId",
        SubTableNames = {
			"SPItemGetPopupConfig",
        }
    },
    ["table_SPItemMonsterRangePositionConf"] = {
        TableKey = "traceMonsterId",
        SubTableNames = {
			"SPItemMonsterRangePositionConf",
        }
    },
    ["table_SPLearningDiagramLimitData"] = {
        TableKey = "ItemID",
        SubTableNames = {
			"SPLearningDiagramLimitData",
        }
    },
    ["table_SPLevel"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPLevel",
        }
    },
    ["table_SPLevelSequenceConfigData"] = {
        TableKey = "SequenceActorID",
        SubTableNames = {
			"SPLevelSequenceConfigData",
        }
    },
    ["table_SPLevelUpAddTechPoint"] = {
        TableKey = "levelId",
        SubTableNames = {
			"SPLevelUpAddTechPoint",
        }
    },
    ["table_SPLocationTrace"] = {
        TableKey = "jumpId",
        SubTableNames = {
			"SPLocationTrace",
        }
    },
    ["table_SPLogSettingConf"] = {
        TableKey = "categoryName",
        SubTableNames = {
			"SPLogSettingConf",
        }
    },
    ["table_SPLotteryPool"] = {
        TableKey = "id",
        SubTableNames = {
			"SPLotteryPool",
        }
    },
    ["table_SPLotteryReward"] = {
        TableKey = "rewardId",
        SubTableNames = {
			"SPLotteryReward",
        }
    },
    ["table_SPMailConfig"] = {
        TableKey = "confId",
        SubTableNames = {
			"SPMailConfig",
        }
    },
    ["table_SPMailTabConfig"] = {
        TableKey = "TabID",
        SubTableNames = {
			"SPMailTabConfig",
        }
    },
    ["table_SPMainProcessTlogGroupConfig"] = {
        TableKey = "groupId",
        SubTableNames = {
			"SPMainProcessTlog",
        }
    },
    ["table_SPMainProcessTlogDefine"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPMainProcessTlogDefine",
        }
    },
    ["table_SPMainUISequentialGroup"] = {
        TableKey = "id",
        SubTableNames = {
			"SPMainUISequentialGroup",
        }
    },
    ["table_SPMainUITip"] = {
        TableKey = "id",
        SubTableNames = {
			"SPMainUITip",
        }
    },
    ["table_SPMainUITips"] = {
        TableKey = "id",
        SubTableNames = {
			"SPMainUITips",
        }
    },
    ["table_SPMap"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPMap",
        }
    },
    ["table_SPMapPerfSetting"] = {
        TableKey = "Level",
        SubTableNames = {
			"SPMapPerfSettingData",
        }
    },
    ["table_SPMapQuality"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPMapQuality",
        }
    },
    ["table_SPMonsterBaseAttribute"] = {
        TableKey = "attrType",
        SubTableNames = {
			"SPMonsterBaseAttribute",
        }
    },
    ["table_SPMonsterDynamicAttribute"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPMonsterDynamicAttribute",
        }
    },
    ["table_SPMonsterDynamicAttributeFactor"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPMonsterDynamicAttributeFactor",
        }
    },
    ["table_SPMonsterPoolData"] = {
        TableKey = "monster_group_id",
        SubTableNames = {
			"SPMonsterPoolData",
        }
    },
    ["table_SPMonsterVoice"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPMonsterVoice",
        }
    },
    ["table_SPMoveAreaConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPMoveAreaConfig",
        }
    },
    ["table_SPNPCConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPNPCConfigData",
        }
    },
    ["table_SPOWMissionData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPOWMissionData",
        }
    },
    ["table_SPOWScenarioTriggerConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPOWScenarioTriggerConfigData",
        }
    },
    ["table_SPOfflineSOC"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPOfflineSOC",
        }
    },
    ["table_SPOfflineSOCPetStatus"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPOfflineSOCPetStatus",
        }
    },
    ["table_SPOfflineSOCSatiety"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPOfflineSOCSatiety",
        }
    },
    ["table_SPPCKeyActionNameData"] = {
        TableKey = "ActionName",
        SubTableNames = {
			"SPPCKeyActionNameData",
        }
    },
    ["table_SPPCKeyGroupData"] = {
        TableKey = "SlotIndex",
        SubTableNames = {
			"SPPCKeyGroupData",
        }
    },
    ["table_SPPCKeyIconData"] = {
        TableKey = "Key",
        SubTableNames = {
			"SPPCKeyIconData",
        }
    },
    ["table_SPPVPAutoChessMap"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPVPAutoChessMap",
        }
    },
    ["table_SPPVPGlobal"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPVPGlobal",
        }
    },
    ["table_SPPVPPet"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPVPPet",
        }
    },
    ["table_SPPVPPetBodyScale"] = {
        TableKey = "BodyType",
        SubTableNames = {
			"SPPVPPetBodyScale",
        }
    },
    ["table_SPPVPPetJob"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPVPPetJob",
        }
    },
    ["table_SPPVPPetLevelAttribute"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPVPPetLevelAttribute",
        }
    },
    ["table_SPPVPPetLevelAttribute_ABTest"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPVPPetLevelAttribute_ABTest",
        }
    },
    ["table_SPPVPPetMonsterLevelAttribute"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPVPPetMonsterLevelAttribute",
        }
    },
    ["table_SPPVPPetMonsterLevelAttribute_ABTest"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPVPPetMonsterLevelAttribute_ABTest",
        }
    },
    ["table_SPPVPPetPresets"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPVPPetPresets",
        }
    },
    ["table_SPPVPPetStarLevelAttribute"] = {
        TableKey = "starLevel",
        SubTableNames = {
			"SPPVPPetStarLevelAttribute",
        }
    },
    ["table_SPPVPPetType"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPVPPetType",
        }
    },
    ["table_SPPanel"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPanel",
        }
    },
    ["table_SPPassiveAbility"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPassiveAbilityData",
        }
    },
    ["table_SPPassiveBuffSwitches"] = {
        TableKey = "playId",
        SubTableNames = {
			"SPPassiveBuffSwitches",
        }
    },
    ["table_SPPassiveSkillsConf"] = {
        TableKey = "settingId",
        SubTableNames = {
			"SPPassiveSkillsConf",
        }
    },
    ["table_SPPerfSettingType"] = {
        TableKey = "Type",
        SubTableNames = {
			"SPPerfSettingTypeData",
        }
    },
    ["table_SPPet"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPet",
        }
    },
    ["table_SPPetAI"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetAI",
        }
    },
    ["table_SPPetActiveSkillPool"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetActiveSkillPool",
        }
    },
    ["table_SPPetAffinityTemplate"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetAffinityTemplate",
        }
    },
    ["table_SPPetAmulet"] = {
        TableKey = "petAmuletId",
        SubTableNames = {
			"SPPetAmulet",
        }
    },
    ["table_SPPetAmuletUpgrade"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetAmuletUpgrade",
        }
    },
    ["table_SPPetBT"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetBT",
        }
    },
    ["table_SPPetBaseAttribute"] = {
        TableKey = "attrType",
        SubTableNames = {
			"SPPetBaseAttribute",
        }
    },
    ["table_SPPetBodyType"] = {
        TableKey = "bodyType",
        SubTableNames = {
			"SPPetBodyType",
        }
    },
    ["table_SPPetClientNetConfig"] = {
        TableKey = "petClientNetConfigId",
        SubTableNames = {
			"SPPetClientNetConfig",
        }
    },
    ["table_SPPetElementDetail"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetElementDetail",
        }
    },
    ["table_SPPetForm"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetForm",
        }
    },
    ["table_SPPetHunger"] = {
        TableKey = "maxHunger",
        SubTableNames = {
			"SPPetHunger",
        }
    },
    ["table_SPPetIllustratedAward"] = {
        TableKey = "awardLevel",
        SubTableNames = {
			"SPPetIllustratedAward",
        }
    },
    ["table_SPPetInteractionConfig"] = {
        TableKey = "petInteractionConfigId",
        SubTableNames = {
			"SPPetInteractionConfig",
        }
    },
    ["table_SPPetInteractiveBehavior"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetInteractiveBehavior",
        }
    },
    ["table_SPPetLevelAttribute"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetLevelAttribute",
        }
    },
    ["table_SPPetLevelAttribute_ABTest"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetLevelAttribute_ABTest",
        }
    },
    ["table_SPPetMonsterLevelAttribute"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetMonsterLevelAttribute",
        }
    },
    ["table_SPPetMonsterLevelAttribute_ABTest"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetMonsterLevelAttribute_ABTest",
        }
    },
    ["table_SPPetPassiveSkillDetail"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetPassiveSkillDetail",
        }
    },
    ["table_SPPetPassiveSkillMutex"] = {
        TableKey = "PassliveSkilld",
        SubTableNames = {
			"SPPetPassiveSkillMutex",
        }
    },
    ["table_SPPetPassiveSkillPool"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetPassiveSkillPool",
        }
    },
    ["table_SPPetPotentialAttr"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetPotentialAttr",
        }
    },
    ["table_SPPetPotentialPool"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetPotentialPool",
        }
    },
    ["table_SPPetPrefeb"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetPrefeb",
        }
    },
    ["table_SPPetRarityEffect"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetRarityEffect",
        }
    },
    ["table_SPPetRarityPool"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetRarityPool",
        }
    },
    ["table_SPPetRideBasicInfo"] = {
        TableKey = "typeId",
        SubTableNames = {
			"SPPetRideBasicInfo",
        }
    },
    ["table_SPPetRideMove"] = {
        TableKey = "typeId",
        SubTableNames = {
			"SPPetRideMove",
        }
    },
    ["table_SPPetRidePose"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetRidePose",
        }
    },
    ["table_SPPetRideShape"] = {
        TableKey = "bodyType",
        SubTableNames = {
			"SPPetRideShape",
        }
    },
    ["table_SPPetRotationControlConfig"] = {
        TableKey = "petRotationControlConfigId",
        SubTableNames = {
			"SPPetRotationControlConfig",
        }
    },
    ["table_SPPetSOCBehavior"] = {
        TableKey = "type",
        SubTableNames = {
			"SPPetSOCBehavior",
        }
    },
    ["table_SPPetSOCConifg"] = {
        TableKey = "typeId",
        SubTableNames = {
			"SPPetSOCConifg",
        }
    },
    ["table_SPPetStarLevelAttribute"] = {
        TableKey = "starLevel",
        SubTableNames = {
			"SPPetStarLevelAttribute",
        }
    },
    ["table_SPPetStatus"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetStatus",
        }
    },
    ["table_SPPetStatusEffect"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetStatusEffect",
        }
    },
    ["table_SPPetStatusEffectCondition"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetStatusEffectCondition",
        }
    },
    ["table_SPPetTenacity"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPetTenacity",
        }
    },
    ["table_SPPetType"] = {
        TableKey = "typeId",
        SubTableNames = {
			"SPPetType",
        }
    },
    ["table_SPPetWorkSkillDetail"] = {
        TableKey = "type",
        SubTableNames = {
			"SPPetWorkSkillDetail",
        }
    },
    ["table_SPPetWorldJob"] = {
        TableKey = "worldJobID",
        SubTableNames = {
			"SPPetWorldJob",
        }
    },
    ["table_SPPetWorldJobType"] = {
        TableKey = "worldJobTypeID",
        SubTableNames = {
			"SPPetWorldJobType",
        }
    },
    ["table_SPPhysicsMatAttrData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPPhysicsMatAttrData",
        }
    },
    ["table_SPPlayerBaseAttribute"] = {
        TableKey = "attrType",
        SubTableNames = {
			"SPPlayerBaseAttribute",
        }
    },
    ["table_SPPlayerStatus"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPlayerStatus",
        }
    },
    ["table_SPPreloadPath"] = {
        TableKey = "id",
        SubTableNames = {
			"SPPreloadPath",
        }
    },
    ["table_SPProfession"] = {
        TableKey = "professionId",
        SubTableNames = {
			"SPProfessionConf_common",
        }
    },
    ["table_SPProfessionGenre"] = {
        TableKey = "professionGenreId",
        SubTableNames = {
			"SPProfessionGenreConf",
        }
    },
    ["table_SPProtectedScoreLevelDimensionData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPProtectedScoreLevelDimensionData_starp_pvp",
        }
    },
    ["table_SPPuzzleMatrixConfig"] = {
        TableKey = "itemID",
        SubTableNames = {
			"SPPuzzleMatrixConfig",
        }
    },
    ["table_SPQuickSearchConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPQuickSearchConfig",
        }
    },
    ["table_SPQuickTextConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPQuickTextConfDataLobby",
        }
    },
    ["table_SPResourceAICreator"] = {
        TableKey = "uid",
        SubTableNames = {
			"SPResourceAICreator",
        }
    },
    ["table_SPResourceAirwall"] = {
        TableKey = "uid",
        SubTableNames = {
			"SPResourceAirwall",
        }
    },
    ["table_SPResourceControlConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SPResourceControl",
        }
    },
    ["table_SPResourceGPO"] = {
        TableKey = "uid",
        SubTableNames = {
			"SPResourceGPO",
        }
    },
    ["table_SPResourcePatrol"] = {
        TableKey = "uid",
        SubTableNames = {
			"SPResourcePatrol",
        }
    },
    ["table_SPResourceSTP"] = {
        TableKey = "uid",
        SubTableNames = {
			"SPResourceSTP",
        }
    },
    ["table_SPResourceVirtualPoint"] = {
        TableKey = "uid",
        SubTableNames = {
			"SPResourceVirtualPoint",
        }
    },
    ["table_SPResourceZone"] = {
        TableKey = "uid",
        SubTableNames = {
			"SPResourceZone",
        }
    },
    ["table_SPRewardPoints"] = {
        TableKey = "id",
        SubTableNames = {
			"SPRewardPoints",
        }
    },
    ["table_SPRiverPolygonConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPRiverPolygonConfigData",
        }
    },
    ["table_SPRoleLevelBaseAttribute"] = {
        TableKey = "levelId",
        SubTableNames = {
			"SPRoleLevelBaseAttribute",
        }
    },
    ["table_SPSOCAIBuildTask"] = {
        TableKey = "id",
        SubTableNames = {
			"SPSOCAIBuildTask",
        }
    },
    ["table_SPSecondaryAttributeConvert"] = {
        TableKey = "id",
        SubTableNames = {
			"SPSecondaryAttributeConvert",
        }
    },
    ["table_SPSeedDSPreloadPath"] = {
        TableKey = "id",
        SubTableNames = {
			"SPSeedDSPreloadPath",
        }
    },
    ["table_SPSettingConf"] = {
        TableKey = "settingType",
        SubTableNames = {
			"SPSettingConfig",
        }
    },
    ["table_SPSettingPCConf"] = {
        TableKey = "settingType",
        SubTableNames = {
			"SPSettingPCConfig",
        }
    },
    ["table_SPShocksQualifyingRewardData"] = {
        TableKey = "qualifyType",
        SubTableNames = {
			"SPShocksQualifyingRewardData_starp_pvp",
        }
    },
    ["table_SPShopConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPShopConfig",
        }
    },
    ["table_SPShopDetailsConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPShopDetailsConfig",
        }
    },
    ["table_SPSimpleCondActionPair"] = {
        TableKey = "id",
        SubTableNames = {
			"SPSimpleCondActionPair",
        }
    },
    ["table_SPSkillComboData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPSkillComboData",
        }
    },
    ["table_SPSkillGroup"] = {
        TableKey = "id",
        SubTableNames = {
			"SPSkillGroup",
        }
    },
    ["table_SPSkillPosition"] = {
        TableKey = "skillPositionID",
        SubTableNames = {
			"SPSkillPositionData",
        }
    },
    ["table_SPSkinVoiceBankMapConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPSkinVoiceBankMapConfigData",
        }
    },
    ["table_SPSnapConfigData"] = {
        TableKey = "snapId",
        SubTableNames = {
			"SPSnapConfigData",
        }
    },
    ["table_SPSocInteractionRatioConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SPSocInteractionRatio",
        }
    },
    ["table_SPSoundVolumeConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPSoundVolumeConfigData",
        }
    },
    ["table_SPStaminaCost"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPStaminaCost",
        }
    },
    ["table_SPStaminaSum"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPStaminaSum",
        }
    },
    ["table_SPStarEggConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPStarEggConfigData",
        }
    },
    ["table_SPStarEggFirstIncubateData"] = {
        TableKey = "Index",
        SubTableNames = {
			"SPStarEggFirstIncubateData",
        }
    },
    ["table_SPStarEggFlashData"] = {
        TableKey = "EggID",
        SubTableNames = {
			"SPStarEggFlashData",
        }
    },
    ["table_SPStarEggGroupData"] = {
        TableKey = "Index",
        SubTableNames = {
			"SPStarEggGroupData",
        }
    },
    ["table_SPStarEggRandomPoolData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPStarEggRandomPoolData",
        }
    },
    ["table_SPStarEggRandomWeightData"] = {
        TableKey = "WorldLevel",
        SubTableNames = {
			"SPStarEggRandomWeightData",
        }
    },
    ["table_SPStarEggTemperatureData"] = {
        TableKey = "TemperatureState",
        SubTableNames = {
			"SPStarEggTemperatureData",
        }
    },
    ["table_SPStateToTag"] = {
        TableKey = "wantTagName",
        SubTableNames = {
			"SPStateToTag",
        }
    },
    ["table_SPStoryLineCondConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPStoryLineCondConfig",
        }
    },
    ["table_SPStoryLineConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPStoryLineConfig",
        }
    },
    ["table_SPStoryLineRandomGroupConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPStoryLineRandomGroupConfig",
        }
    },
    ["table_SPStoryLineSwitcherConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPStoryLineSwitcherConfig",
        }
    },
    ["table_SPSubPackageData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPSubPackageData",
        }
    },
    ["table_SPSummonConfig"] = {
        TableKey = "SummonId",
        SubTableNames = {
			"SPSummonConfig",
        }
    },
    ["table_SPTagName"] = {
        TableKey = "id",
        SubTableNames = {
			"SPTagName",
        }
    },
    ["table_SPTagToState"] = {
        TableKey = "wantStateName",
        SubTableNames = {
			"SPTagToState",
        }
    },
    ["table_SPTagToTag"] = {
        TableKey = "wantTagName",
        SubTableNames = {
			"SPTagToTag",
        }
    },
    ["table_SPTalentInfo"] = {
        TableKey = "talentID",
        SubTableNames = {
			"SPTalentInfo",
        }
    },
    ["table_SPTalentTree"] = {
        TableKey = "keyID",
        SubTableNames = {
			"SPTalentTree",
        }
    },
    ["table_SPTargetIndicator"] = {
        TableKey = "type",
        SubTableNames = {
			"SPTargetIndicator",
        }
    },
    ["table_SPTaskConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SPTaskConf_common",
        }
    },
    ["table_SPTechGroupConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPTechGroupConfigData",
        }
    },
    ["table_SPTechPointConfigData"] = {
        TableKey = "techPointType",
        SubTableNames = {
			"SPTechPointConfigData",
        }
    },
    ["table_SPTechTreeConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"SPTechTreeConfigData",
        }
    },
    ["table_SPTerminalWorkStatus"] = {
        TableKey = "id",
        SubTableNames = {
			"SPTerminalWorkStatus",
        }
    },
    ["table_SPTimedMailConfig"] = {
        TableKey = "checkId",
        SubTableNames = {
			"SPTimedMailConfig",
        }
    },
    ["table_SPTradingPostLevelData"] = {
        TableKey = "level",
        SubTableNames = {
			"SPTradingPostLevelData",
        }
    },
    ["table_SPTradingPostOrderData"] = {
        TableKey = "orderID",
        SubTableNames = {
			"SPTradingPostOrderData",
        }
    },
    ["table_SPTradingPostOrderRefreshCDData"] = {
        TableKey = "ID",
        SubTableNames = {
			"SPTradingPostOrderRefreshCDData",
        }
    },
    ["table_SPTradingPostOrderRefreshData"] = {
        TableKey = "ID",
        SubTableNames = {
			"SPTradingPostOrderRefreshData",
        }
    },
    ["table_SPTradingPostTagData"] = {
        TableKey = "tagID",
        SubTableNames = {
			"SPTradingPostTagData",
        }
    },
    ["table_SPTransportData"] = {
        TableKey = "TransportLevel",
        SubTableNames = {
			"SPTransportData",
        }
    },
    ["table_SPTreasureChestConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPTreasureChestConfigData",
        }
    },
    ["table_SPTreasureChestRandomPoolData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPTreasureChestRandomPoolData",
        }
    },
    ["table_SPTreasureChestRandomWeightData"] = {
        TableKey = "WorldLevel",
        SubTableNames = {
			"SPTreasureChestRandomWeightData",
        }
    },
    ["table_TreeStaticMeshScaleData"] = {
        TableKey = "TreeSMName",
        SubTableNames = {
			"SPTreeStaticMeshScaleData",
        }
    },
    ["table_SPTriggerTlogConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPTriggerTlogConfig",
        }
    },
    ["table_SPTrophyInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"SPTrophyInfo",
        }
    },
    ["table_SPTutorialCategoryConfig"] = {
        TableKey = "categoryId",
        SubTableNames = {
			"SPTutorialCategoryConfig",
        }
    },
    ["table_SPTutorialConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPTutorialConfig",
        }
    },
    ["table_SPTutorialDetailConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPTutorialDetailConfig",
        }
    },
    ["table_SPVideoConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SPVideoConfig",
        }
    },
    ["table_FPSWeaponClientConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SPWeaponClientConfDataStarP",
        }
    },
    ["table_WeaponEnergy"] = {
        TableKey = "id",
        SubTableNames = {
			"SPWeaponEnergyStarP",
        }
    },
    ["table_FPSWeaponPropertyConf"] = {
        TableKey = "blueprintName,propertyName",
        SubTableNames = {
			"SPWeaponPropertyConfDataStarP",
        }
    },
    ["table_FPSWeaponRenovationConf"] = {
        TableKey = "FormulaId",
        SubTableNames = {
			"SPWeaponRenovationConfDataStarP",
        }
    },
    ["table_FPSWeaponUnlockInfo"] = {
        TableKey = "itemId",
        SubTableNames = {
			"SPWeaponUnlockInfoStarP",
        }
    },
    ["table_SPWorldCareerIdolConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldCareerIdolConfigData",
        }
    },
    ["table_SPWorldCareerIdolPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldCareerIdolPointData",
        }
    },
    ["table_SPWorldChoppablePointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldChoppablePointData",
			"SPWorldChoppablePointData_PCG",
			"SPWorldChoppablePointData_Tree",
        }
    },
    ["table_SPWorldChoppableTreeSMData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldChoppableTreeSMData",
        }
    },
    ["table_SPWorldFireBeadPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldFireBeadPointData",
        }
    },
    ["table_SPWorldGodStatuePointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldGodStatuePointData",
        }
    },
    ["table_SPWorldIllegalBuildRegionPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldIllegalBuildRegionPointData",
        }
    },
    ["table_SPWorldLandTemplePointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldLandTemplePointData",
        }
    },
    ["table_SPWorldLeafEffectPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldLeafEffectPointData",
        }
    },
    ["table_SPWorldLevel"] = {
        TableKey = "level",
        SubTableNames = {
			"SPWorldLevel",
        }
    },
    ["table_SPWorldMazeConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldMazeConfigData",
        }
    },
    ["table_SPWorldMazeEntranceData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldMazeEntranceData",
        }
    },
    ["table_SPWorldMonsterLevelRangeData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldMonsterLevelRangeData",
        }
    },
    ["table_SPWorldMonsterPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldMonsterPointData",
        }
    },
    ["table_SPWorldMonsterTimeTypeData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldMonsterTimeTypeData",
        }
    },
    ["table_SPWorldNPCPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldNPCPointData",
        }
    },
    ["table_SPWorldPOIRegionPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldPOIRegionPointData",
        }
    },
    ["table_SPWorldPVPEntranceData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldPVPEntranceData",
        }
    },
    ["table_SPWorldPickablesPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldPickablesPointData",
			"SPWorldPickablesPointData_PCG",
        }
    },
    ["table_SPWorldPickablesPoolData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldPickablesPoolData",
        }
    },
    ["table_SPWorldPropsPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldPropsPointData",
			"SPWorldPropsPointData_PCG",
        }
    },
    ["table_SPWorldPropsPoolData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldPropsPoolData",
        }
    },
    ["table_SPWorldRLAbilityLibraryData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRLAbilityLibraryData",
        }
    },
    ["table_SPWorldRLAbilityRandomTemplateData"] = {
        TableKey = "Id,TriadID",
        SubTableNames = {
			"SPWorldRLAbilityRandomTemplateData",
        }
    },
    ["table_SPWorldRLAbilityTriadData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRLAbilityTriadData",
        }
    },
    ["table_SPWorldRLChapterCompleteBuffData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRLChapterCompleteBuffData",
        }
    },
    ["table_SPWorldRLChapterData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRLChapterData",
        }
    },
    ["table_SPWorldRLLinearLevelData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRLLinearLevelData",
        }
    },
    ["table_SPWorldRLPeriodAlternateData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRLPeriodAlternateData",
        }
    },
    ["table_SPWorldRLPeriodLevelData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRLPeriodLevelData",
        }
    },
    ["table_SPWorldRebornPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRebornPointData",
        }
    },
    ["table_SPWorldRegionData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRegionData",
        }
    },
    ["table_SPWorldRegionDefine"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRegionDefine",
        }
    },
    ["table_SPWorldRiverPolygonPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldRiverPolygonPointData",
        }
    },
    ["table_SPWorldSeaAudioData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldSeaAudioData",
        }
    },
    ["table_SPWorldSkillFruitConfigData"] = {
        TableKey = "RowId",
        SubTableNames = {
			"SPWorldSkillFruitConfigData",
        }
    },
    ["table_SPWorldSkillFruitTreePointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldSkillFruitTreePointData",
        }
    },
    ["table_SPWorldSoundVolumePointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldSoundVolumePointData",
        }
    },
    ["table_SPWorldStarDiaryPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldStarDiaryPointData",
        }
    },
    ["table_SPWorldStarEggPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldStarEggPointData",
			"SPWorldStarEggPointData_PCG",
        }
    },
    ["table_SPWorldStreamablePOIConfig"] = {
        TableKey = "SP_POIType",
        SubTableNames = {
			"SPWorldStreamablePOIConfig",
        }
    },
    ["table_SPWorldStreamablePOIPreloadAssetBundleConfig"] = {
        TableKey = "POIBundleId",
        SubTableNames = {
			"SPWorldStreamablePOIPreloadAssetBundleConfig",
        }
    },
    ["table_SPWorldStreamablePOIPreloadAssetPathConfig"] = {
        TableKey = "AssetId",
        SubTableNames = {
			"SPWorldStreamablePOIPreloadAssetPathConfig",
        }
    },
    ["table_SPWorldStreamablePOIPreloadData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldStreamablePOIPreloadData",
        }
    },
    ["table_SPWorldTeamPVEConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldTeamPVEConfigData",
        }
    },
    ["table_SPWorldTeamPVEEntranceData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldTeamPVEEntranceData",
        }
    },
    ["table_SPWorldTeleportPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldTeleportPointData",
        }
    },
    ["table_SPWorldTemperatureDefine"] = {
        TableKey = "Temperature",
        SubTableNames = {
			"SPWorldTemperatureDefine",
        }
    },
    ["table_SPWorldTowerConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldTowerConfigData",
        }
    },
    ["table_SPWorldTowerEntranceData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldTowerEntranceData",
        }
    },
    ["table_SPWorldTreasureChestPointData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldTreasureChestPointData",
			"SPWorldTreasureChestPointData_PCG",
        }
    },
    ["table_SPWorldUnderlandConfigData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldUnderlandConfigData",
        }
    },
    ["table_SPWorldUnderlandEntranceData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SPWorldUnderlandEntranceData",
        }
    },
    ["table_SPTalentAbilityInfo"] = {
        TableKey = "keyID",
        SubTableNames = {
			"SPalentAbilityInfo",
        }
    },
    ["table_ClientServerTextConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ServerTextConfData_starp",
        }
    },
    ["table_StarPCommonDbWhiteListConf"] = {
        TableKey = "dataClassDBValue",
        SubTableNames = {
			"StarPCommonDbWhiteListConf",
        }
    },
    ["table_StarPEnumConf"] = {
        TableKey = "labelId",
        SubTableNames = {
			"StarPEnumConf",
        }
    },
    ["table_StarPLoadingPicConf"] = {
        TableKey = "PicId",
        SubTableNames = {
			"StarPLoadingPicConf",
        }
    },
    ["table_MatchRuleRangeData"] = {
        TableKey = "id",
        SubTableNames = {
			"StarPMatchRuleRangeData_SPGame",
        }
    },
    ["table_MatchTypeData"] = {
        TableKey = "id",
        SubTableNames = {
			"StarPMatchTypeData_SPGame",
        }
    },
    ["table_StarPModeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"StarPModeConf",
        }
    },
    ["table_StarPPicConf"] = {
        TableKey = "picId",
        SubTableNames = {
			"StarPPicConf",
        }
    },
    ["table_StarPRoomConf"] = {
        TableKey = "name",
        SubTableNames = {
			"StarPRoomConf",
        }
    },
    ["table_StarPRoomNumConf"] = {
        TableKey = "roleLevel",
        SubTableNames = {
			"StarPRoomNumConf",
        }
    },
    ["table_StarPSysConf"] = {
        TableKey = "ID",
        SubTableNames = {
			"StarPSysConf",
        }
    },
    ["table_TextEntryData"] = {
        TableKey = "stringId",
        SubTableNames = {
			"TextEntryData_StarP",
        }
    },
    ["table_TlogAnalyseConf"] = {
        TableKey = "name",
        SubTableNames = {
			"TlogAnalyseConf_StarP",
        }
    },
}

return TableHelperTables
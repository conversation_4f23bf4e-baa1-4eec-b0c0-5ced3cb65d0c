com.tencent.wea.xlsRes.table_FarmRoomBuildingFunctionalFurnitureLimitConf
excel/xls/Farm/N_农场家具表.xlsx sheet:建筑功能家具数量上下限
rows {
  id: 1
  buildingTypeId: 9
  buildingLevel: 1
  limitStr: "FRFUT_MealSeat-0-1"
  limitStr: "FRFUT_CookTop-0-1"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-0"
}
rows {
  id: 2
  buildingTypeId: 9
  buildingLevel: 2
  limitStr: "FRFUT_MealSeat-0-2"
  limitStr: "FRFUT_CookTop-0-1"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-0"
}
rows {
  id: 3
  buildingTypeId: 9
  buildingLevel: 3
  limitStr: "FRFUT_MealSeat-0-3"
  limitStr: "FRFUT_CookTop-0-1"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-0"
}
rows {
  id: 4
  buildingTypeId: 9
  buildingLevel: 4
  limitStr: "FRFUT_MealSeat-0-4"
  limitStr: "FRFUT_CookTop-0-1"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-0"
}
rows {
  id: 5
  buildingTypeId: 9
  buildingLevel: 5
  limitStr: "FRFUT_MealSeat-0-4"
  limitStr: "FRFUT_CookTop-0-1"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-0"
}
rows {
  id: 6
  buildingTypeId: 9
  buildingLevel: 6
  limitStr: "FRFUT_MealSeat-0-5"
  limitStr: "FRFUT_CookTop-0-1"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-0"
}
rows {
  id: 7
  buildingTypeId: 9
  buildingLevel: 7
  limitStr: "FRFUT_MealSeat-0-5"
  limitStr: "FRFUT_CookTop-0-1"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 8
  buildingTypeId: 9
  buildingLevel: 8
  limitStr: "FRFUT_MealSeat-0-6"
  limitStr: "FRFUT_CookTop-0-1"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 9
  buildingTypeId: 9
  buildingLevel: 9
  limitStr: "FRFUT_MealSeat-0-7"
  limitStr: "FRFUT_CookTop-0-1"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 10
  buildingTypeId: 9
  buildingLevel: 10
  limitStr: "FRFUT_MealSeat-0-7"
  limitStr: "FRFUT_CookTop-0-1"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 11
  buildingTypeId: 9
  buildingLevel: 11
  limitStr: "FRFUT_MealSeat-0-7"
  limitStr: "FRFUT_CookTop-0-2"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 12
  buildingTypeId: 9
  buildingLevel: 12
  limitStr: "FRFUT_MealSeat-0-8"
  limitStr: "FRFUT_CookTop-0-2"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 13
  buildingTypeId: 9
  buildingLevel: 13
  limitStr: "FRFUT_MealSeat-0-8"
  limitStr: "FRFUT_CookTop-0-2"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 14
  buildingTypeId: 9
  buildingLevel: 14
  limitStr: "FRFUT_MealSeat-0-9"
  limitStr: "FRFUT_CookTop-0-2"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 15
  buildingTypeId: 9
  buildingLevel: 15
  limitStr: "FRFUT_MealSeat-0-10"
  limitStr: "FRFUT_CookTop-0-2"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 16
  buildingTypeId: 9
  buildingLevel: 16
  limitStr: "FRFUT_MealSeat-0-10"
  limitStr: "FRFUT_CookTop-0-2"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 17
  buildingTypeId: 9
  buildingLevel: 17
  limitStr: "FRFUT_MealSeat-0-10"
  limitStr: "FRFUT_CookTop-0-2"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 18
  buildingTypeId: 9
  buildingLevel: 18
  limitStr: "FRFUT_MealSeat-0-11"
  limitStr: "FRFUT_CookTop-0-2"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 19
  buildingTypeId: 9
  buildingLevel: 19
  limitStr: "FRFUT_MealSeat-0-11"
  limitStr: "FRFUT_CookTop-0-2"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 20
  buildingTypeId: 9
  buildingLevel: 20
  limitStr: "FRFUT_MealSeat-0-11"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 21
  buildingTypeId: 9
  buildingLevel: 21
  limitStr: "FRFUT_MealSeat-0-12"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 22
  buildingTypeId: 9
  buildingLevel: 22
  limitStr: "FRFUT_MealSeat-0-12"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 23
  buildingTypeId: 9
  buildingLevel: 23
  limitStr: "FRFUT_MealSeat-0-12"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 24
  buildingTypeId: 9
  buildingLevel: 24
  limitStr: "FRFUT_MealSeat-0-13"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 25
  buildingTypeId: 9
  buildingLevel: 25
  limitStr: "FRFUT_MealSeat-0-13"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 26
  buildingTypeId: 9
  buildingLevel: 26
  limitStr: "FRFUT_MealSeat-0-13"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 27
  buildingTypeId: 9
  buildingLevel: 27
  limitStr: "FRFUT_MealSeat-0-14"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 28
  buildingTypeId: 9
  buildingLevel: 28
  limitStr: "FRFUT_MealSeat-0-14"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 29
  buildingTypeId: 9
  buildingLevel: 29
  limitStr: "FRFUT_MealSeat-0-14"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 30
  buildingTypeId: 9
  buildingLevel: 30
  limitStr: "FRFUT_MealSeat-0-15"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 31
  buildingTypeId: 9
  buildingLevel: 31
  limitStr: "FRFUT_MealSeat-0-15"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 32
  buildingTypeId: 9
  buildingLevel: 32
  limitStr: "FRFUT_MealSeat-0-15"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 33
  buildingTypeId: 9
  buildingLevel: 33
  limitStr: "FRFUT_MealSeat-0-16"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 34
  buildingTypeId: 9
  buildingLevel: 34
  limitStr: "FRFUT_MealSeat-0-16"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 35
  buildingTypeId: 9
  buildingLevel: 35
  limitStr: "FRFUT_MealSeat-0-16"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 36
  buildingTypeId: 9
  buildingLevel: 36
  limitStr: "FRFUT_MealSeat-0-17"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 37
  buildingTypeId: 9
  buildingLevel: 37
  limitStr: "FRFUT_MealSeat-0-17"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 38
  buildingTypeId: 9
  buildingLevel: 38
  limitStr: "FRFUT_MealSeat-0-17"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 39
  buildingTypeId: 9
  buildingLevel: 39
  limitStr: "FRFUT_MealSeat-0-18"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 40
  buildingTypeId: 9
  buildingLevel: 40
  limitStr: "FRFUT_MealSeat-0-18"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 41
  buildingTypeId: 9
  buildingLevel: 41
  limitStr: "FRFUT_MealSeat-0-18"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 42
  buildingTypeId: 9
  buildingLevel: 42
  limitStr: "FRFUT_MealSeat-0-19"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 43
  buildingTypeId: 9
  buildingLevel: 43
  limitStr: "FRFUT_MealSeat-0-19"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 44
  buildingTypeId: 9
  buildingLevel: 44
  limitStr: "FRFUT_MealSeat-0-19"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 45
  buildingTypeId: 9
  buildingLevel: 45
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 46
  buildingTypeId: 9
  buildingLevel: 46
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 47
  buildingTypeId: 9
  buildingLevel: 47
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 48
  buildingTypeId: 9
  buildingLevel: 48
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 49
  buildingTypeId: 9
  buildingLevel: 49
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 50
  buildingTypeId: 9
  buildingLevel: 50
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 51
  buildingTypeId: 9
  buildingLevel: 51
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 52
  buildingTypeId: 9
  buildingLevel: 52
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 53
  buildingTypeId: 9
  buildingLevel: 53
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 54
  buildingTypeId: 9
  buildingLevel: 54
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 55
  buildingTypeId: 9
  buildingLevel: 55
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 56
  buildingTypeId: 9
  buildingLevel: 56
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 57
  buildingTypeId: 9
  buildingLevel: 57
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 58
  buildingTypeId: 9
  buildingLevel: 58
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 59
  buildingTypeId: 9
  buildingLevel: 59
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 60
  buildingTypeId: 9
  buildingLevel: 60
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 61
  buildingTypeId: 9
  buildingLevel: 61
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 62
  buildingTypeId: 9
  buildingLevel: 62
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 63
  buildingTypeId: 9
  buildingLevel: 63
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 64
  buildingTypeId: 9
  buildingLevel: 64
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 65
  buildingTypeId: 9
  buildingLevel: 65
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 66
  buildingTypeId: 9
  buildingLevel: 66
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 67
  buildingTypeId: 9
  buildingLevel: 67
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 68
  buildingTypeId: 9
  buildingLevel: 68
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 69
  buildingTypeId: 9
  buildingLevel: 69
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 70
  buildingTypeId: 9
  buildingLevel: 70
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 71
  buildingTypeId: 9
  buildingLevel: 71
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 72
  buildingTypeId: 9
  buildingLevel: 72
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 73
  buildingTypeId: 9
  buildingLevel: 73
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 74
  buildingTypeId: 9
  buildingLevel: 74
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 75
  buildingTypeId: 9
  buildingLevel: 75
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 76
  buildingTypeId: 9
  buildingLevel: 76
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 77
  buildingTypeId: 9
  buildingLevel: 77
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 78
  buildingTypeId: 9
  buildingLevel: 78
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 79
  buildingTypeId: 9
  buildingLevel: 79
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 80
  buildingTypeId: 9
  buildingLevel: 80
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 81
  buildingTypeId: 9
  buildingLevel: 81
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 82
  buildingTypeId: 9
  buildingLevel: 82
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 83
  buildingTypeId: 9
  buildingLevel: 83
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 84
  buildingTypeId: 9
  buildingLevel: 84
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 85
  buildingTypeId: 9
  buildingLevel: 85
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 86
  buildingTypeId: 9
  buildingLevel: 86
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 87
  buildingTypeId: 9
  buildingLevel: 87
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 88
  buildingTypeId: 9
  buildingLevel: 88
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 89
  buildingTypeId: 9
  buildingLevel: 89
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 90
  buildingTypeId: 9
  buildingLevel: 90
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 91
  buildingTypeId: 9
  buildingLevel: 91
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 92
  buildingTypeId: 9
  buildingLevel: 92
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 93
  buildingTypeId: 9
  buildingLevel: 93
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 94
  buildingTypeId: 9
  buildingLevel: 94
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 95
  buildingTypeId: 9
  buildingLevel: 95
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 96
  buildingTypeId: 9
  buildingLevel: 96
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 97
  buildingTypeId: 9
  buildingLevel: 97
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 98
  buildingTypeId: 9
  buildingLevel: 98
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 99
  buildingTypeId: 9
  buildingLevel: 99
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 100
  buildingTypeId: 9
  buildingLevel: 100
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-4"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 101
  buildingTypeId: 10
  buildingLevel: 101
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 102
  buildingTypeId: 11
  buildingLevel: 102
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 103
  buildingTypeId: 12
  buildingLevel: 103
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 104
  buildingTypeId: 13
  buildingLevel: 104
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 105
  buildingTypeId: 14
  buildingLevel: 105
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 106
  buildingTypeId: 15
  buildingLevel: 106
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 107
  buildingTypeId: 16
  buildingLevel: 107
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 108
  buildingTypeId: 17
  buildingLevel: 108
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 109
  buildingTypeId: 18
  buildingLevel: 109
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 110
  buildingTypeId: 19
  buildingLevel: 110
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 111
  buildingTypeId: 20
  buildingLevel: 111
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 112
  buildingTypeId: 21
  buildingLevel: 112
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 113
  buildingTypeId: 22
  buildingLevel: 113
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 114
  buildingTypeId: 23
  buildingLevel: 114
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 115
  buildingTypeId: 24
  buildingLevel: 115
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 116
  buildingTypeId: 25
  buildingLevel: 116
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 117
  buildingTypeId: 26
  buildingLevel: 117
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 118
  buildingTypeId: 27
  buildingLevel: 118
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 119
  buildingTypeId: 28
  buildingLevel: 119
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}
rows {
  id: 120
  buildingTypeId: 29
  buildingLevel: 120
  limitStr: "FRFUT_MealSeat-0-20"
  limitStr: "FRFUT_CookTop-0-3"
  limitStr: "FRFUT_CheckoutCounter-1-1"
  limitStr: "FRFUT_CookScreen-0-1"
}

com.tencent.wea.xlsRes.table_TradingCardExchangeConfig
excel/xls/K_卡牌.xlsx sheet:额外兑换
rows {
  collectionId: 102
  exchangeId: 1
  sort: 10
  exchangeCd: 86400
  maxCount: 0
  startTime {
    seconds: 1736697600
  }
  endTime {
    seconds: 1746115199
  }
  rewardItem {
    itemId: 290012
    itemNum: 1
  }
  needStar: 150
}
rows {
  collectionId: 102
  exchangeId: 2
  sort: 9
  exchangeCd: 86400
  maxCount: 0
  startTime {
    seconds: 1736697600
  }
  endTime {
    seconds: 1746115199
  }
  rewardItem {
    itemId: 290013
    itemNum: 1
  }
  needStar: 250
}
rows {
  collectionId: 102
  exchangeId: 3
  sort: 8
  exchangeCd: 86400
  maxCount: 0
  startTime {
    seconds: 1736697600
  }
  endTime {
    seconds: 1746115199
  }
  rewardItem {
    itemId: 290013
    itemNum: 1
  }
  rewardItem {
    itemId: 290014
    itemNum: 1
  }
  needStar: 500
}
rows {
  collectionId: 102
  exchangeId: 4
  sort: 7
  exchangeCd: 86400
  maxCount: 0
  startTime {
    seconds: 1736697600
  }
  endTime {
    seconds: 1746115199
  }
  rewardItem {
    itemId: 290014
    itemNum: 2
  }
  rewardItem {
    itemId: 290015
    itemNum: 1
  }
  needStar: 750
}
rows {
  collectionId: 102
  exchangeId: 5
  sort: 6
  exchangeCd: 86400
  maxCount: 0
  startTime {
    seconds: 1736697600
  }
  endTime {
    seconds: 1746115199
  }
  rewardItem {
    itemId: 290014
    itemNum: 2
  }
  rewardItem {
    itemId: 290015
    itemNum: 2
  }
  needStar: 1000
}
rows {
  collectionId: 102
  exchangeId: 6
  sort: 12
  exchangeCd: 0
  maxCount: 0
  startTime {
    seconds: 1736697600
  }
  endTime {
    seconds: 1746115199
  }
  rewardItem {
    itemId: 4
    itemNum: 100
  }
  needStar: 10
}
rows {
  collectionId: 102
  exchangeId: 7
  sort: 11
  exchangeCd: 0
  maxCount: 100
  startTime {
    seconds: 1736697600
  }
  endTime {
    seconds: 1746115199
  }
  rewardItem {
    itemId: 6
    itemNum: 5
  }
  needStar: 25
}
rows {
  collectionId: 103
  exchangeId: 1
  sort: 10
  exchangeCd: 86400
  maxCount: 0
  startTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1755791999
  }
  rewardItem {
    itemId: 290022
    itemNum: 1
  }
  needStar: 150
}
rows {
  collectionId: 103
  exchangeId: 2
  sort: 9
  exchangeCd: 86400
  maxCount: 0
  startTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1755791999
  }
  rewardItem {
    itemId: 290023
    itemNum: 1
  }
  needStar: 250
}
rows {
  collectionId: 103
  exchangeId: 3
  sort: 8
  exchangeCd: 86400
  maxCount: 0
  startTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1755791999
  }
  rewardItem {
    itemId: 290023
    itemNum: 1
  }
  rewardItem {
    itemId: 290024
    itemNum: 1
  }
  needStar: 500
}
rows {
  collectionId: 103
  exchangeId: 4
  sort: 7
  exchangeCd: 86400
  maxCount: 0
  startTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1755791999
  }
  rewardItem {
    itemId: 290024
    itemNum: 2
  }
  rewardItem {
    itemId: 290025
    itemNum: 1
  }
  needStar: 750
}
rows {
  collectionId: 103
  exchangeId: 5
  sort: 6
  exchangeCd: 86400
  maxCount: 0
  startTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1755791999
  }
  rewardItem {
    itemId: 290024
    itemNum: 2
  }
  rewardItem {
    itemId: 290025
    itemNum: 2
  }
  needStar: 1000
}
rows {
  collectionId: 104
  exchangeId: 1
  sort: 12
  exchangeCd: 0
  maxCount: 0
  startTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1755791999
  }
  rewardItem {
    itemId: 4
    itemNum: 100
  }
  needStar: 10
}
rows {
  collectionId: 104
  exchangeId: 2
  sort: 11
  exchangeCd: 0
  maxCount: 100
  startTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1755791999
  }
  rewardItem {
    itemId: 6
    itemNum: 5
  }
  needStar: 25
}

com.tencent.wea.xlsRes.table_ClientServerTextConfData
excel/xls/W_文本表_提示_Main.xlsm sheet:文本
rows {
  id: "99999998"
  zh_CN: "ErrorCodeEnd2"
}
rows {
  id: "10190001"
  zh_CN: "任务初始化失败"
}
rows {
  id: "10040972"
  zh_CN: "大奖已领取"
}
rows {
  id: "10040971"
  zh_CN: "生成步数出错"
}
rows {
  id: "10040970"
  zh_CN: "走格子开销不够"
}
rows {
  id: "0"
  zh_CN: "成功"
}
rows {
  id: "-1"
  zh_CN: "请尽量使用具体错误码, 尽可能少的使用UnknownError"
}
rows {
  id: "-2"
  zh_CN: "服务器繁忙"
  showType: ECST_ModelDialog
}
rows {
  id: "-3"
  zh_CN: "请求超时"
  showType: ECST_ModelDialog
}
rows {
  id: "-4"
  zh_CN: "服务器参数异常"
  showType: ECST_ModelDialog
}
rows {
  id: "-5"
  zh_CN: "-5"
}
rows {
  id: "-6"
  zh_CN: "-6"
}
rows {
  id: "-7"
  zh_CN: "锁失败"
}
rows {
  id: "-8"
  zh_CN: "单区创建uid过多"
}
rows {
  id: "-9"
  zh_CN: "try锁失败"
}
rows {
  id: "-10"
  zh_CN: "网络异常"
  showType: ECST_FlyTip
}
rows {
  id: "-11"
  zh_CN: "服务器异常(-11)"
  showType: ECST_ModelDialog
}
rows {
  id: "-12"
  zh_CN: "服务器异常(-12)"
  showType: ECST_ModelDialog
}
rows {
  id: "-13"
  zh_CN: "服务器维护中，点击确定退出"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-14"
  zh_CN: "服务器异常(-14)"
  showType: ECST_ModelDialog
}
rows {
  id: "-15"
  zh_CN: "服务器异常(-15)"
  showType: ECST_ModelDialog
}
rows {
  id: "-16"
  zh_CN: "您暂时无法参与本次测试"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-17"
  zh_CN: "服务器异常(-17)"
  showType: ECST_ModelDialog
}
rows {
  id: "-18"
  zh_CN: "-18"
}
rows {
  id: "-19"
  zh_CN: "-19"
}
rows {
  id: "-20"
  zh_CN: "客户端请求消息过多"
  showType: ECST_ModelDialog
}
rows {
  id: "-21"
  zh_CN: "服务器异常(-21)"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-22"
  zh_CN: "您暂时不在场景中"
  showType: ECST_FlyTip
}
rows {
  id: "-23"
  zh_CN: "服务器异常(-23)"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-24"
  zh_CN: "服务器异常(-24)"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-25"
  zh_CN: "服务器异常(-25)"
  showType: ECST_FlyTip
}
rows {
  id: "-26"
  zh_CN: "-26"
}
rows {
  id: "-27"
  zh_CN: "-27"
}
rows {
  id: "-28"
  zh_CN: "功能未解锁"
}
rows {
  id: "-29"
  zh_CN: "功能暂时无法进入"
  showType: ECST_FlyTip
}
rows {
  id: "-30"
  zh_CN: "LocalService调用失败"
}
rows {
  id: "-31"
  zh_CN: "不在白名单"
}
rows {
  id: "-32"
  zh_CN: "服务器异常(-32)"
  showType: ECST_FlyTip
}
rows {
  id: "-33"
  zh_CN: "服务器异常(-33)"
  showType: ECST_FlyTip
}
rows {
  id: "-34"
  zh_CN: "分配guid失败"
}
rows {
  id: "-35"
  zh_CN: "数据回滚失败"
}
rows {
  id: "-36"
  zh_CN: "用户数据初始化失败"
}
rows {
  id: "-37"
  zh_CN: "服务器异常(-37)"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-38"
  zh_CN: "-38"
}
rows {
  id: "-39"
  zh_CN: "-39"
}
rows {
  id: "-40"
  zh_CN: "服务器DB错误"
}
rows {
  id: "-41"
  zh_CN: "服务器异常(-41)"
  showType: ECST_ModelDialog
}
rows {
  id: "-42"
  zh_CN: "服务器异常(-42)"
  showType: ECST_FlyTip
}
rows {
  id: "-43"
  zh_CN: "服务器异常(-43)"
  showType: ECST_ModelDialog
}
rows {
  id: "-44"
  zh_CN: "服务器异常(-44)"
  showType: ECST_FlyTip
}
rows {
  id: "-45"
  zh_CN: "非法执行器"
}
rows {
  id: "-46"
  zh_CN: "没有获取到合法的LocalService"
}
rows {
  id: "-47"
  zh_CN: "您的版本较低，请重启游戏更新"
  showType: ECST_ModelDialog
  rpcType: 1
  zh_CN_cloud: "检测到小游戏更新，请重启。\n如暂时无法进入，请稍后再试。"
}
rows {
  id: "-48"
  zh_CN: "不支持的共享数据类型"
  showType: ECST_FlyTip
}
rows {
  id: "-49"
  zh_CN: "Component不存在"
}
rows {
  id: "-50"
  zh_CN: "StateHandle的状态不匹配"
}
rows {
  id: "-51"
  zh_CN: "服务器异常(-51)"
}
rows {
  id: "-52"
  zh_CN: "-52"
}
rows {
  id: "-53"
  zh_CN: "找不到配置"
}
rows {
  id: "-54"
  zh_CN: "Simulator客户端关闭连接失败"
}
rows {
  id: "-55"
  zh_CN: "Simulator客户端初始化失败"
}
rows {
  id: "-56"
  zh_CN: "Simulator客户端配置失败"
}
rows {
  id: "-57"
  zh_CN: "Simulator客户端连接失败"
}
rows {
  id: "-58"
  zh_CN: "Simulator客户端发送消息失败"
}
rows {
  id: "-59"
  zh_CN: "Simulator客户端未知错误"
}
rows {
  id: "-60"
  zh_CN: "无效的事件订阅者"
}
rows {
  id: "-61"
  zh_CN: "服务器异常(-61)"
  showType: ECST_FlyTip
}
rows {
  id: "-62"
  zh_CN: "服务器异常(-62)"
  showType: ECST_FlyTip
}
rows {
  id: "-63"
  zh_CN: "proxy无法直接处理元数据rpc"
}
rows {
  id: "-64"
  zh_CN: "-64"
}
rows {
  id: "-65"
  zh_CN: "Gm指令被其他选手锁定，需该选手解锁后才能使用"
}
rows {
  id: "-66"
  zh_CN: "请求zplan接口失败"
}
rows {
  id: "-67"
  zh_CN: "模块开启错误，模块已关闭"
}
rows {
  id: "-68"
  zh_CN: "模块指令不存在"
}
rows {
  id: "-69"
  zh_CN: "gm指令执行失败"
}
rows {
  id: "-70"
  zh_CN: "直播流数据获取失败"
}
rows {
  id: "-71"
  zh_CN: "Polaris服务发现异常"
}
rows {
  id: "-72"
  zh_CN: "庆典纪念币奖励配置异常"
}
rows {
  id: "-73"
  zh_CN: "今日庆典纪念币已达上限"
}
rows {
  id: "-74"
  zh_CN: "DB操作版本号错误"
}
rows {
  id: "-75"
  zh_CN: "DB操作版本号错误"
}
rows {
  id: "-78"
  zh_CN: "redis操作失败"
}
rows {
  id: "-79"
  zh_CN: "redis锁失败"
}
rows {
  id: "-80"
  zh_CN: "问卷签名错误"
}
rows {
  id: "-81"
  zh_CN: "问卷签名为空"
}
rows {
  id: "-82"
  zh_CN: "问卷回调异常"
}
rows {
  id: "-83"
  zh_CN: "问卷回调参数错误"
}
rows {
  id: "-84"
  zh_CN: "配置Key未找到"
}
rows {
  id: "-85"
  zh_CN: "登录异常"
}
rows {
  id: "-86"
  zh_CN: "超时登出"
}
rows {
  id: "-87"
  zh_CN: "连接中断"
}
rows {
  id: "-88"
  zh_CN: "超时登出"
}
rows {
  id: "-89"
  zh_CN: "无须控制DS日志级别"
}
rows {
  id: "-90"
  zh_CN: "请开启redis region配置"
}
rows {
  id: "-91"
  zh_CN: "-91"
  showType: ECST_FlyTip
}
rows {
  id: "-92"
  zh_CN: "-92"
  showType: ECST_ModelDialog
}
rows {
  id: "-93"
  zh_CN: "-93"
}
rows {
  id: "-94"
  zh_CN: "-94"
}
rows {
  id: "-95"
  zh_CN: "-95"
}
rows {
  id: "-96"
  zh_CN: "-96"
}
rows {
  id: "-97"
  zh_CN: "该功能已禁用"
}
rows {
  id: "-199"
  zh_CN: "QQ账号不能登微信区，微信账号不能等qq区"
}
rows {
  id: "-200"
  zh_CN: "不支持等待存盘"
}
rows {
  id: "-201"
  zh_CN: "踢线失败"
}
rows {
  id: "-202"
  zh_CN: "玩家UID分配失败"
}
rows {
  id: "-300"
  zh_CN: "定时器归属线程异常"
}
rows {
  id: "-301"
  zh_CN: "定时器参数错误"
}
rows {
  id: "-302"
  zh_CN: "定时器未注册"
}
rows {
  id: "-1000000"
  zh_CN: "-1000000"
}
rows {
  id: "-1000001"
  zh_CN: "IdipHandler不存在"
}
rows {
  id: "-1000002"
  zh_CN: "选手不存在"
}
rows {
  id: "-1000003"
  zh_CN: "rpc错误"
}
rows {
  id: "-1000004"
  zh_CN: "星宝您的网络异常了，请重新登录哟。"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-1000005"
  zh_CN: "openid uid不匹配"
}
rows {
  id: "-1000006"
  zh_CN: "md5校验失败"
}
rows {
  id: "-1000007"
  zh_CN: "md5校验失败"
}
rows {
  id: "-1000008"
  zh_CN: "不支持的请求，例如cmdid无效"
}
rows {
  id: "-1000193"
  zh_CN: "接收失败"
}
rows {
  id: "-1000194"
  zh_CN: "发送失败"
}
rows {
  id: "-1000195"
  zh_CN: "请求串小于最小长度"
}
rows {
  id: "-1000196"
  zh_CN: "请求串大于最大长度"
}
rows {
  id: "-1000197"
  zh_CN: "没有做系统初始化"
}
rows {
  id: "-1000198"
  zh_CN: "请求串格式非法"
}
rows {
  id: "-1000199"
  zh_CN: "请求队列满，一般为乐园端消息处理过慢，造成idip系统消息队列积压"
}
rows {
  id: "-1000885"
  zh_CN: "IDIP用户处于离线中"
}
rows {
  id: "-1000886"
  zh_CN: "IDIP小区转发目标错误"
}
rows {
  id: "-1000887"
  zh_CN: "IDIP小区转发白名单中没有该openid"
}
rows {
  id: "-1000888"
  zh_CN: "发送邮件重复"
}
rows {
  id: "-1000889"
  zh_CN: "http连接关闭"
}
rows {
  id: "-1000890"
  zh_CN: "目标服务不可用"
}
rows {
  id: "-1000891"
  zh_CN: "组访问率超限"
}
rows {
  id: "-1000892"
  zh_CN: "命令访问频率超限"
}
rows {
  id: "-1000893"
  zh_CN: "没有找到对应的乐园端配置项"
}
rows {
  id: "-1000894"
  zh_CN: "没有找到对应的乐园端配置表"
}
rows {
  id: "-1000895"
  zh_CN: "没有找到对应的组"
}
rows {
  id: "-1000896"
  zh_CN: "没有找到对应的路由表"
}
rows {
  id: "-1000897"
  zh_CN: "没找到对应的路由规则"
}
rows {
  id: "-1000898"
  zh_CN: "IP没有该命令的访问权限"
}
rows {
  id: "-1000899"
  zh_CN: "IP不在白名单当中，请联系IDIP增加白名单"
}
rows {
  id: "-1001197"
  zh_CN: "worker注册失败"
}
rows {
  id: "-1001198"
  zh_CN: "没有找到对应的worker"
}
rows {
  id: "-1001199"
  zh_CN: "没有找到对应的分组"
}
rows {
  id: "-1003976"
  zh_CN: "NPC智能对话功能已被封禁"
}
rows {
  id: "-1003977"
  zh_CN: "社团搜索功能已被封禁"
  showType: ECST_FlyTip
}
rows {
  id: "-1003978"
  zh_CN: "AI视频动捕功能已被封禁"
}
rows {
  id: "-1003979"
  zh_CN: "社团功能已被封禁"
  showType: ECST_FlyTip
}
rows {
  id: "-1003980"
  zh_CN: "创建或更新元件标签功能已被封禁"
}
rows {
  id: "-1003981"
  zh_CN: "玩法模式已被封禁"
}
rows {
  id: "-1003982"
  zh_CN: "AI配色功能已被封禁"
}
rows {
  id: "-1003983"
  zh_CN: "添加好友功能已被封禁"
}
rows {
  id: "-1003984"
  zh_CN: "搜索功能已被封禁"
}
rows {
  id: "-1003985"
  zh_CN: "创建房间功能已被封禁"
}
rows {
  id: "-1003986"
  zh_CN: "创建地图功能已被封禁"
}
rows {
  id: "-1003987"
  zh_CN: "排行榜功能已被封禁"
}
rows {
  id: "-1003988"
  zh_CN: "AI参考图功能已被封禁"
}
rows {
  id: "-1003989"
  zh_CN: "语音功能已被封禁"
}
rows {
  id: "-1003990"
  zh_CN: "头像已被封禁"
}
rows {
  id: "-1003991"
  zh_CN: "已完成创角选择昵称性别"
  showType: ECST_FlyTip
}
rows {
  id: "-1003992"
  zh_CN: "语音功能已被封禁"
}
rows {
  id: "-1003993"
  zh_CN: "改名卡不足"
}
rows {
  id: "-1003994"
  zh_CN: "不可填写该内容"
  showType: ECST_FlyTip
}
rows {
  id: "-1003995"
  zh_CN: "获取game api server失败"
}
rows {
  id: "-1003996"
  zh_CN: "输入参数不合法"
}
rows {
  id: "-1003997"
  zh_CN: "返回信息解码失败"
}
rows {
  id: "-1003998"
  zh_CN: "编码失败"
}
rows {
  id: "-1003999"
  zh_CN: "与game server之间的通讯失败"
}
rows {
  id: "-1004000"
  zh_CN: "game server api返回失败，具体错误看子错误码ERR_no和ERR_info字段"
}
rows {
  id: "-1004001"
  zh_CN: "新名字和旧名字相同"
  showType: ECST_FlyTip
}
rows {
  id: "-1004002"
  zh_CN: "新名字已存在"
  showType: ECST_FlyTip
}
rows {
  id: "-1004003"
  zh_CN: "更新名字出错"
  showType: ECST_FlyTip
}
rows {
  id: "-1004004"
  zh_CN: "性别非法"
  showType: ECST_FlyTip
}
rows {
  id: "-1004005"
  zh_CN: "昵称长度超过限制"
  showType: ECST_FlyTip
}
rows {
  id: "-1004006"
  zh_CN: "含有中文、数字、英文以外的内容"
  showType: ECST_FlyTip
}
rows {
  id: "-1004007"
  zh_CN: "性别修改冷却中"
  showType: ECST_FlyTip
}
rows {
  id: "-1004008"
  zh_CN: "请先解除情侣关系再修改性别"
  showType: ECST_FlyTip
}
rows {
  id: "-1004009"
  zh_CN: "idip 下发给客户端的错误码 兜底"
}
rows {
  id: "-1004010"
  zh_CN: "idip 下发给客户端的错误码 预留"
}
rows {
  id: "-1004011"
  zh_CN: "idip 下发给客户端的错误码 预留"
}
rows {
  id: "-1004012"
  zh_CN: "idip 下发给客户端的错误码 预留"
}
rows {
  id: "-1004013"
  zh_CN: "idip 下发给客户端的错误码 预留"
}
rows {
  id: "-1004014"
  zh_CN: "idip 下发给客户端的错误码 预留"
}
rows {
  id: "-1004015"
  zh_CN: "idip 下发给客户端的错误码 预留"
}
rows {
  id: "-1004016"
  zh_CN: "-1004016"
}
rows {
  id: "-1004017"
  zh_CN: "-1004017"
}
rows {
  id: "-1004018"
  zh_CN: "昵称已被预注册占用"
}
rows {
  id: "-1004019"
  zh_CN: "系统功能繁忙，请稍后再试"
}
rows {
  id: "-1060053"
  zh_CN: "不在创作者白名单"
}
rows {
  id: "-1060056"
  zh_CN: "地图类型不对"
}
rows {
  id: "-1060057"
  zh_CN: "生命值道具不存在"
}
rows {
  id: "-1060058"
  zh_CN: "生命值道具数量不够"
}
rows {
  id: "-1060224"
  zh_CN: "邀请测试玩家人数达到上限"
}
rows {
  id: "-1060226"
  zh_CN: "多人测试房间最大人数不够"
}
rows {
  id: "-1060227"
  zh_CN: "该地图被封禁进行多人测试"
  showType: ECST_Custom
}
rows {
  id: "-1060228"
  zh_CN: "地图当前处于审核中"
}
rows {
  id: "-1060229"
  zh_CN: "物品id申请上限"
}
rows {
  id: "-1060230"
  zh_CN: "找不到ugc data store service"
}
rows {
  id: "-1060231"
  zh_CN: "星钻购买状态异常"
}
rows {
  id: "-1060232"
  zh_CN: "获取商品信息失败"
}
rows {
  id: "-1060233"
  zh_CN: "资产下载Rpc异常"
}
rows {
  id: "-1060234"
  zh_CN: "发货失败"
}
rows {
  id: "-1060235"
  zh_CN: "星钻购买状态异常"
}
rows {
  id: "-1060237"
  zh_CN: "检测ugc商品价格不对应"
}
rows {
  id: "-1060238"
  zh_CN: "检测ugc商品中道具不对应"
}
rows {
  id: "-1060239"
  zh_CN: "检测ugc商品不存在"
}
rows {
  id: "-1060240"
  zh_CN: "内购暂未开启"
}
rows {
  id: "-1060241"
  zh_CN: "ugc地图快捷键参数错误"
}
rows {
  id: "-1060242"
  zh_CN: "地图已经评分过了"
}
rows {
  id: "-1060243"
  zh_CN: "地图评分更新失败"
}
rows {
  id: "-1060244"
  zh_CN: "请求的太频繁"
}
rows {
  id: "-1060245"
  zh_CN: "写入私有资源pub表失败"
}
rows {
  id: "-1060246"
  zh_CN: "禁止对非私有资源进行操作"
}
rows {
  id: "-1060247"
  zh_CN: "缩略图达到上限"
}
rows {
  id: "-1060248"
  zh_CN: "私有资源在publish表没有找到"
}
rows {
  id: "-1060249"
  zh_CN: "新旧排行榜配置不一致"
}
rows {
  id: "-1060250"
  zh_CN: "获取排行榜失败"
}
rows {
  id: "-1060251"
  zh_CN: "更新排行榜失败"
}
rows {
  id: "-1060252"
  zh_CN: "删除排行榜条目失败"
}
rows {
  id: "-1060253"
  zh_CN: "验证单人模式上报排行榜失败"
}
rows {
  id: "-1060254"
  zh_CN: "rank id申请上限"
}
rows {
  id: "-1060255"
  zh_CN: "设置apply uid redis数据报错"
}
rows {
  id: "-1060256"
  zh_CN: "设置apply uid redis数据报错"
}
rows {
  id: "-1060257"
  zh_CN: "Ugc Map 排行榜找不到"
}
rows {
  id: "-1060258"
  zh_CN: "Ugc Map 排行榜 不允许单人上报"
}
rows {
  id: "-1060259"
  zh_CN: "Ugc 批量拉playerpublic失败"
}
rows {
  id: "-1060260"
  zh_CN: "ugc地图发布检查排行榜报错"
}
rows {
  id: "-1060261"
  zh_CN: "ugc 地图排行榜操作 频率控制"
}
rows {
  id: "-1060262"
  zh_CN: "ugc玩家社区请求失败"
}
rows {
  id: "-1060263"
  zh_CN: "发表地图留言失败"
}
rows {
  id: "-1060264"
  zh_CN: "获取地图留言失败"
}
rows {
  id: "-1060265"
  zh_CN: "ugc地图单人存档白名单检查失败"
}
rows {
  id: "-1060266"
  zh_CN: "ugc地图单人上报排行榜对比uid检错误"
}
rows {
  id: "-1060267"
  zh_CN: "非法评论，未通过安全审核"
}
rows {
  id: "-1060268"
  zh_CN: "评论长度超出限制"
}
rows {
  id: "-1060269"
  zh_CN: "操作太频繁, 请稍后重试"
}
rows {
  id: "-2000040"
  zh_CN: "尝试进入场景服失败"
}
rows {
  id: "-10000001"
  zh_CN: "服务器异常(-10000001)"
}
rows {
  id: "-10000002"
  zh_CN: "服务器异常(-10000002)"
}
rows {
  id: "-10000003"
  zh_CN: "用户信息不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10000004"
  zh_CN: "服务器异常(-10000004)"
}
rows {
  id: "-10000005"
  zh_CN: "-10000005"
}
rows {
  id: "-10000006"
  zh_CN: "用户已离线"
  showType: ECST_FlyTip
}
rows {
  id: "-10000007"
  zh_CN: "-10000007"
}
rows {
  id: "-10000008"
  zh_CN: "-10000008"
}
rows {
  id: "-10000009"
  zh_CN: "-10000009"
}
rows {
  id: "-10000010"
  zh_CN: "服务器异常(-10000010)"
  showType: ECST_FlyTip
}
rows {
  id: "-10000011"
  zh_CN: "找不到用户"
}
rows {
  id: "-10000012"
  zh_CN: "初始化behaviour失败"
}
rows {
  id: "-10000013"
  zh_CN: "不支持行为"
}
rows {
  id: "-10000014"
  zh_CN: "组件不存在"
}
rows {
  id: "-10000015"
  zh_CN: "找不到目标"
}
rows {
  id: "-10000016"
  zh_CN: "没有冲突"
}
rows {
  id: "-10000017"
  zh_CN: "entity不包含某个component"
}
rows {
  id: "-10000018"
  zh_CN: "创建 map entity失败"
}
rows {
  id: "-10000019"
  zh_CN: "添加到entity mgr失败"
}
rows {
  id: "-10000020"
  zh_CN: "已经在地图上"
}
rows {
  id: "-10000021"
  zh_CN: "-10000021"
}
rows {
  id: "-10000022"
  zh_CN: "阻挡"
}
rows {
  id: "-10000023"
  zh_CN: "不在大地图"
}
rows {
  id: "-10000024"
  zh_CN: "任务条件配置错误"
}
rows {
  id: "-10000025"
  zh_CN: "消息发送过于频繁"
  showType: ECST_FlyTip
}
rows {
  id: "-10000026"
  zh_CN: "重复的聊天消息"
  showType: ECST_FlyTip
}
rows {
  id: "-10000027"
  zh_CN: "服务器异常(-10000027)"
  showType: ECST_FlyTip
}
rows {
  id: "-10000028"
  zh_CN: "Uic检测发生IO错误"
}
rows {
  id: "-10000029"
  zh_CN: "Uic检测发生json解析错误"
}
rows {
  id: "-10000030"
  zh_CN: "未通过审核，请修改后重试"
  showType: ECST_Custom
}
rows {
  id: "-10000031"
  zh_CN: "Uic检测初始化发生错误"
}
rows {
  id: "-10000032"
  zh_CN: "处于封禁状态, 不能修改个性签名"
}
rows {
  id: "-10000033"
  zh_CN: "不可填写, 请修改后重试"
  showType: ECST_FlyTip
}
rows {
  id: "-10000034"
  zh_CN: "使用过滤后的敏感词(服务器内部错误)"
}
rows {
  id: "-10000035"
  zh_CN: "发现敏感词, 不可公开"
}
rows {
  id: "-10000036"
  zh_CN: "你的QQ版本过低，无法查看，请更新"
  showType: ECST_ModelDialog
}
rows {
  id: "-10000037"
  zh_CN: "不能重复领取"
}
rows {
  id: "-10000038"
  zh_CN: "game service 错误"
}
rows {
  id: "-10000039"
  zh_CN: "分享奖励已经领取"
}
rows {
  id: "-10000040"
  zh_CN: "处于封禁状态, 不能修改昵称"
}
rows {
  id: "-10000041"
  zh_CN: "分享奖励未开放"
}
rows {
  id: "-10000042"
  zh_CN: "跨区查看资料功能即将开放，美好值得等待！"
}
rows {
  id: "-10000044"
  zh_CN: "平台请求的mapId不存在"
}
rows {
  id: "-10000046"
  zh_CN: "未搜索到任何用户"
}
rows {
  id: "-10000047"
  zh_CN: "json串非法"
}
rows {
  id: "-10000048"
  zh_CN: "平台请求参数错误"
}
rows {
  id: "-10000049"
  zh_CN: "平台请求的openid没有注册"
}
rows {
  id: "-10000050"
  zh_CN: "平台请求的uid没有注册"
}
rows {
  id: "-10000051"
  zh_CN: "-10000051"
}
rows {
  id: "-10000052"
  zh_CN: "-10000052"
}
rows {
  id: "-10000053"
  zh_CN: "-10000053"
}
rows {
  id: "-10000054"
  zh_CN: "-10000054"
}
rows {
  id: "-10000055"
  zh_CN: "请求msdk失败"
}
rows {
  id: "-10000056"
  zh_CN: "当日祈福次数上限"
}
rows {
  id: "-10000057"
  zh_CN: "玩家uid没有找到对应的creatorId"
}
rows {
  id: "-10000058"
  zh_CN: "-10000058"
}
rows {
  id: "-10000059"
  zh_CN: "-10000059"
}
rows {
  id: "-10000060"
  zh_CN: "玩家无权开播"
}
rows {
  id: "-10000061"
  zh_CN: "-10000061"
}
rows {
  id: "-10000062"
  zh_CN: "-10000062"
}
rows {
  id: "-10000063"
  zh_CN: "-10000063"
}
rows {
  id: "-10000064"
  zh_CN: "星宝暂时不能发言，稍后再试试吧"
}
rows {
  id: "-10000065"
  zh_CN: "无法查询到联名作者"
}
rows {
  id: "-10000066"
  zh_CN: "并非同一个玩家发布的"
}
rows {
  id: "-10000067"
  zh_CN: "-10000067"
}
rows {
  id: "-10000068"
  zh_CN: "-10000068"
}
rows {
  id: "-10000069"
  zh_CN: "-10000069"
}
rows {
  id: "-10000070"
  zh_CN: "货币不足"
}
rows {
  id: "-10000071"
  zh_CN: "错误文本服务器补丁测试1"
}
rows {
  id: "-10000079"
  zh_CN: "错误文本服务器补丁测试9"
}
rows {
  id: "-10000081"
  zh_CN: "当前地图无法匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-10000082"
  zh_CN: "当日祈福分享次数上限"
}
rows {
  id: "-10000083"
  zh_CN: "当日财神祈福活动次数上限"
}
rows {
  id: "-10000084"
  zh_CN: "祈福没有开放"
}
rows {
  id: "-10000085"
  zh_CN: "保存设置失败"
}
rows {
  id: "-10000086"
  zh_CN: "恢复设置失败"
}
rows {
  id: "-10000087"
  zh_CN: "云端功能未开放"
}
rows {
  id: "-10000088"
  zh_CN: "玩家关闭预约功能"
  showType: ECST_FlyTip
}
rows {
  id: "-10000089"
  zh_CN: "预约失败"
}
rows {
  id: "-10000090"
  zh_CN: "预约好友数已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10000091"
  zh_CN: "非改类型白名单成员"
}
rows {
  id: "-10000092"
  zh_CN: "配置不存在"
}
rows {
  id: "-10000093"
  zh_CN: "配置内容拉取限频"
}
rows {
  id: "-10000094"
  zh_CN: "获取免流信息异常"
}
rows {
  id: "-10000095"
  zh_CN: "签名含有非法字符"
  showType: ECST_FlyTip
}
rows {
  id: "-10000096"
  zh_CN: "签名长度超过限制"
  showType: ECST_FlyTip
}
rows {
  id: "-10000097"
  zh_CN: "处于封禁状态，禁止修改签名"
  showType: ECST_FlyTip
}
rows {
  id: "-10000098"
  zh_CN: "不可填写该内容"
  showType: ECST_FlyTip
}
rows {
  id: "-10000229"
  zh_CN: "不存在配置名"
}
rows {
  id: "-10000230"
  zh_CN: "前置检测不合法"
}
rows {
  id: "-10000501"
  zh_CN: "合服数据不支持编辑"
}
rows {
  id: "-10001001"
  zh_CN: "已在其他设备登录(-10001001)"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001002"
  zh_CN: "服务器异常(-10001002)"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001003"
  zh_CN: "-10001003"
}
rows {
  id: "-10001004"
  zh_CN: "-10001004"
}
rows {
  id: "-10001005"
  zh_CN: "无效的请求消息"
}
rows {
  id: "-10001006"
  zh_CN: "已在其他设备登录(-10001006)"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001007"
  zh_CN: "用户已被封号，禁止登录"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001008"
  zh_CN: "服务器维护中，具体维护时间及更新内容可留意更新公告"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001009"
  zh_CN: "当前时段注册人数已达上限，请稍后再试。"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001010"
  zh_CN: "您的设备暂不支持本次测试，敬请期待下次测试！"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001011"
  zh_CN: "您的设备暂不支持本次测试，敬请期待下次测试！"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001012"
  zh_CN: "您的账号并未获取本次测试资格，请核对账号。"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001013"
  zh_CN: "用户数据回滚失败"
}
rows {
  id: "-10001014"
  zh_CN: "暂时无法创建新账号"
  showType: ECST_FlyTip
}
rows {
  id: "-10001015"
  zh_CN: "服务器异常(-10001015)"
  showType: ECST_FlyTip
}
rows {
  id: "-10001016"
  zh_CN: "-10001016"
}
rows {
  id: "-10001017"
  zh_CN: "服务器已满"
  showType: ECST_FlyTip
}
rows {
  id: "-10001018"
  zh_CN: "登录过于频繁"
}
rows {
  id: "-10001019"
  zh_CN: "已登录过高版本，请更新至新版本。"
  showType: ECST_ModelDialog
  rpcType: 1
  zh_CN_cloud: "检测到小游戏更新，请重启。\n如暂时无法进入，请稍后再试。"
}
rows {
  id: "-10001020"
  zh_CN: "非法版本号"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001021"
  zh_CN: "服务器停服维护"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001022"
  zh_CN: "服务器内部错误(-10001022)"
}
rows {
  id: "-10001023"
  zh_CN: "不在白名单中，请检查白名单配置"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001024"
  zh_CN: "审核服重定向"
}
rows {
  id: "-10001025"
  zh_CN: "本次测试暂未开启，敬请期待！"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001026"
  zh_CN: "本次测试已结束，感谢您的支持，谢谢。"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001027"
  zh_CN: "客户端强更踢线"
}
rows {
  id: "-10001028"
  zh_CN: "服务器繁忙，请稍后登录（10001028）"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001029"
  zh_CN: "超过频率限制"
}
rows {
  id: "-10001030"
  zh_CN: "本次测试为保密测试，每个账号仅能登录一台终端设备"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001031"
  zh_CN: "ipDb 查询失败。"
}
rows {
  id: "-10001032"
  zh_CN: "ipdb地区登陆限制。"
}
rows {
  id: "-10001033"
  zh_CN: "账号解绑参数错误"
}
rows {
  id: "-10001034"
  zh_CN: "账号解绑禁止(绑定时间不够)"
}
rows {
  id: "-10001035"
  zh_CN: "账号解绑禁止(只有一个绑定账号)"
}
rows {
  id: "-10001036"
  zh_CN: "账号解绑禁止(解绑账号不能是当前登陆的)"
}
rows {
  id: "-10001037"
  zh_CN: "账号解绑失败"
}
rows {
  id: "-10001038"
  zh_CN: "服务器负载满"
}
rows {
  id: "-10001039"
  zh_CN: "玩家IP所在限制等级负载满"
}
rows {
  id: "-10001040"
  zh_CN: "IP访问过于频繁"
}
rows {
  id: "-10001041"
  zh_CN: "服务器网络负载满"
}
rows {
  id: "-10001042"
  zh_CN: "请求次数过多"
}
rows {
  id: "-10001043"
  zh_CN: "账号已经完成注销操作"
}
rows {
  id: "-10001044"
  zh_CN: "账号已经完成注销操作"
}
rows {
  id: "-10001045"
  zh_CN: "请重新登录"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-10001046"
  zh_CN: "设备被封禁"
}
rows {
  id: "-10001047"
  zh_CN: "没有合适的服务器"
}
rows {
  id: "-10001048"
  zh_CN: "已登录过高版本，请重启游戏拉取最新资源。"
  showType: ECST_ModelDialog
  rpcType: 1
  zh_CN_cloud: "检测到小游戏更新，请重启。\n如暂时无法进入，请稍后再试。"
}
rows {
  id: "-10001050"
  zh_CN: "很抱歉，您当前使用的设备机型暂不满足本次测试条件"
}
rows {
  id: "-10001051"
  zh_CN: "平台白名单数据异常"
}
rows {
  id: "-10001052"
  zh_CN: "平台白名单获取错误"
}
rows {
  id: "-10001053"
  zh_CN: "单gamesvr登陆qps控频，请稍后重试"
}
rows {
  id: "-10001054"
  zh_CN: "登录的gamesvr版本过低"
}
rows {
  id: "-10001055"
  zh_CN: "目前登录的gamesvr在下线中"
}
rows {
  id: "-10001056"
  zh_CN: "流式服务限流，请稍后再试"
}
rows {
  id: "-10001057"
  zh_CN: "当前登录的流式服务即将关闭"
}
rows {
  id: "-10010001"
  zh_CN: "-10010001"
}
rows {
  id: "-10010002"
  zh_CN: "好友申请信息不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10010003"
  zh_CN: "不能再添加更多好友啦"
  showType: ECST_FlyTip
}
rows {
  id: "-10010004"
  zh_CN: "对方的好友已满，无法添加好友"
  showType: ECST_FlyTip
}
rows {
  id: "-10010005"
  zh_CN: "不是好友关系"
  showType: ECST_FlyTip
}
rows {
  id: "-10010006"
  zh_CN: "请勿重复送礼"
  showType: ECST_FlyTip
}
rows {
  id: "-10010007"
  zh_CN: "礼物赠送异常"
  showType: ECST_FlyTip
}
rows {
  id: "-10010008"
  zh_CN: "申请已发送，再等等吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-10010009"
  zh_CN: "你们已经是好友啦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10010010"
  zh_CN: "送礼失败"
}
rows {
  id: "-10010011"
  zh_CN: "屏蔽好友异常"
  showType: ECST_FlyTip
}
rows {
  id: "-10010012"
  zh_CN: "添加好友异常"
  showType: ECST_FlyTip
}
rows {
  id: "-10010013"
  zh_CN: "不能添加自己为好友"
  showType: ECST_FlyTip
}
rows {
  id: "-10010014"
  zh_CN: "黑名单中，不能操作"
  showType: ECST_FlyTip
}
rows {
  id: "-10010015"
  zh_CN: "在对方黑名单中，不能操作"
}
rows {
  id: "-10010016"
  zh_CN: "跨区加好友功能即将开放，美好值得等待！"
}
rows {
  id: "-10010017"
  zh_CN: "添加好友队列到上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10010018"
  zh_CN: "消息列表已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10010019"
  zh_CN: "消息列表为空"
  showType: ECST_FlyTip
}
rows {
  id: "-10010020"
  zh_CN: "好友申请已发送"
  showType: ECST_FlyTip
}
rows {
  id: "-10010021"
  zh_CN: "此好友今日亲密度已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10010022"
  zh_CN: "今天已经送过啦"
}
rows {
  id: "-10010023"
  zh_CN: "今天赠送该好友星宝印章数量已达到上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10010024"
  zh_CN: "今天赠送星宝印章次数已达到上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10010025"
  zh_CN: "对方申请列表已满"
  showType: ECST_FlyTip
}
rows {
  id: "-10010026"
  zh_CN: "没有可推荐的玩家"
  showType: ECST_FlyTip
}
rows {
  id: "-10010027"
  zh_CN: "玩家uid没有找到对应的creatorId"
}
rows {
  id: "-10010028"
  zh_CN: "-10010028"
}
rows {
  id: "-10010029"
  zh_CN: "关系已存在"
}
rows {
  id: "-10010030"
  zh_CN: "条件不满足"
}
rows {
  id: "-10010031"
  zh_CN: "亲密关系配置不存在"
}
rows {
  id: "-10010032"
  zh_CN: "亲密关系数量已经到达上限啦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10010033"
  zh_CN: "已存在亲密关系"
}
rows {
  id: "-10010034"
  zh_CN: "亲密关系数量超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10010035"
  zh_CN: "无法发送更多亲密关系请求了，等等回应吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-10010036"
  zh_CN: "你们的亲密度还不够哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10010037"
  zh_CN: "你们刚刚解除了亲密关系，过段时间再试试吧"
  showType: ECST_FlyTip
}
rows {
  id: "-10010038"
  zh_CN: "只有异性才能成为情侣哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10010039"
  zh_CN: "已经发送申请啦"
  showType: ECST_FlyTip
}
rows {
  id: "-10010040"
  zh_CN: "对方亲密关系数量已经到达上限啦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10010041"
  zh_CN: "转换亲密关系对象不在亲密关系中"
}
rows {
  id: "-10010042"
  zh_CN: "亲密关系转换太频繁啦，过几天再试试吧"
  showType: ECST_FlyTip
}
rows {
  id: "-10010043"
  zh_CN: "申请人错误"
}
rows {
  id: "-10010044"
  zh_CN: "情侣性别必须为异性"
  showType: ECST_FlyTip
}
rows {
  id: "-10010045"
  zh_CN: "个性化推荐已关闭"
}
rows {
  id: "-10010046"
  zh_CN: "该好友今天已经接收了太多赠送了"
  showType: ECST_FlyTip
}
rows {
  id: "-10010047"
  zh_CN: "隐身次数已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10010048"
  zh_CN: "最多置顶5个好友"
  showType: ECST_FlyTip
}
rows {
  id: "-10010049"
  zh_CN: "不是游戏好友"
}
rows {
  id: "-10010050"
  zh_CN: "备注名太长了"
}
rows {
  id: "-10010051"
  zh_CN: "备注名不合法"
}
rows {
  id: "-10010052"
  zh_CN: "备注没有变化"
}
rows {
  id: "-10010053"
  zh_CN: "亲密关系等级不足"
  showType: ECST_FlyTip
}
rows {
  id: "-10010054"
  zh_CN: "没有时装借用卡"
  showType: ECST_FlyTip
}
rows {
  id: "-10010055"
  zh_CN: "已拥有该时装"
  showType: ECST_FlyTip
}
rows {
  id: "-10010056"
  zh_CN: "好友没有拥有该时装"
  showType: ECST_FlyTip
}
rows {
  id: "-10010057"
  zh_CN: "获取对方时装信息失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10010058"
  zh_CN: "时装道具配置未找到"
  showType: ECST_FlyTip
}
rows {
  id: "-10010059"
  zh_CN: "仅周六周日才能借用时装"
  showType: ECST_FlyTip
}
rows {
  id: "-10010060"
  zh_CN: "借用时装过期时间配置错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10010061"
  zh_CN: "只有永久时装允许借用"
  showType: ECST_FlyTip
}
rows {
  id: "-10010062"
  zh_CN: "好友被借用时装次数达到上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10010063"
  zh_CN: "操作太频繁，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-10020001"
  zh_CN: "服务器异常(-10020001)"
  showType: ECST_FlyTip
}
rows {
  id: "-10020002"
  zh_CN: "已经关注该用户"
  showType: ECST_FlyTip
}
rows {
  id: "-10020003"
  zh_CN: "已经是粉丝"
}
rows {
  id: "-10020004"
  zh_CN: "未关注该选手"
}
rows {
  id: "-10020005"
  zh_CN: "关注数超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10020006"
  zh_CN: "不能关注自己"
  showType: ECST_FlyTip
}
rows {
  id: "-10020007"
  zh_CN: "未关注任何用户"
  showType: ECST_FlyTip
}
rows {
  id: "-10020008"
  zh_CN: "暂无关注你的用户"
  showType: ECST_FlyTip
}
rows {
  id: "-10020009"
  zh_CN: "配置的粉丝列表数量超过限制"
}
rows {
  id: "-10020010"
  zh_CN: "配置的粉丝缓存条件数量过小"
}
rows {
  id: "-10020011"
  zh_CN: "关注数量超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10020012"
  zh_CN: "缓存数量过小"
  showType: ECST_FlyTip
}
rows {
  id: "-10040001"
  zh_CN: "活动不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10040002"
  zh_CN: "活动不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10040003"
  zh_CN: "缺少必须字段"
  showType: ECST_FlyTip
}
rows {
  id: "-10040004"
  zh_CN: "服务器异常(-10040004)"
  showType: ECST_FlyTip
}
rows {
  id: "-10040005"
  zh_CN: "-10040005"
}
rows {
  id: "-10040006"
  zh_CN: "活动不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10040007"
  zh_CN: "子活动与总控组ID不一致"
}
rows {
  id: "-10040008"
  zh_CN: "-10040008"
}
rows {
  id: "-10040009"
  zh_CN: "活动奖励已领取"
  showType: ECST_FlyTip
}
rows {
  id: "-10040010"
  zh_CN: "请重新进入活动尝试一下哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040011"
  zh_CN: "-10040011"
}
rows {
  id: "-10040012"
  zh_CN: "服务器异常(-10040012)"
  showType: ECST_FlyTip
}
rows {
  id: "-10040013"
  zh_CN: "活动类型不匹配"
}
rows {
  id: "-10040014"
  zh_CN: "活动重复"
  showType: ECST_FlyTip
}
rows {
  id: "-10040015"
  zh_CN: "奖励类型不匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-10040016"
  zh_CN: "红包未分享"
  showType: ECST_FlyTip
}
rows {
  id: "-10040017"
  zh_CN: "红包功能未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-10040018"
  zh_CN: "红包已打开"
  showType: ECST_FlyTip
}
rows {
  id: "-10040019"
  zh_CN: "分享红包已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10040020"
  zh_CN: "好友红包已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10040021"
  zh_CN: "无效的红包"
  showType: ECST_FlyTip
}
rows {
  id: "-10040022"
  zh_CN: "不允许领取自己的红包"
  showType: ECST_FlyTip
}
rows {
  id: "-10040023"
  zh_CN: "红包已领完"
  showType: ECST_FlyTip
}
rows {
  id: "-10040024"
  zh_CN: "领取红包奖励失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10040100"
  zh_CN: "补签次数已用完"
}
rows {
  id: "-10040101"
  zh_CN: "不是领取存款的时间"
}
rows {
  id: "-10040102"
  zh_CN: "存款不足"
}
rows {
  id: "-10040103"
  zh_CN: "已经签到过了哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040200"
  zh_CN: "活动缺少任务组设置"
}
rows {
  id: "-10040201"
  zh_CN: "活动任务时间配置错误"
}
rows {
  id: "-10040202"
  zh_CN: "打卡计划活动补签券不足"
}
rows {
  id: "-10040203"
  zh_CN: "打卡计划已打卡"
  showType: ECST_FlyTip
}
rows {
  id: "-10040204"
  zh_CN: "打卡计划配置不存在"
}
rows {
  id: "-10040205"
  zh_CN: "打卡计划补签次数不足"
  showType: ECST_FlyTip
}
rows {
  id: "-10040300"
  zh_CN: "购买次数超过限制"
  showType: ECST_FlyTip
}
rows {
  id: "-10040301"
  zh_CN: "全服礼包米大师配置错误"
}
rows {
  id: "-10040302"
  zh_CN: "全服礼包米大师支付失败"
}
rows {
  id: "-10040303"
  zh_CN: "全服礼包米奖励已领取"
}
rows {
  id: "-10040304"
  zh_CN: "全服礼包奖励不存在"
}
rows {
  id: "-10040305"
  zh_CN: "全服礼包奖励不满足领取条件"
}
rows {
  id: "-10040306"
  zh_CN: "已经助力过该玩家了哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040307"
  zh_CN: "该助力请求已经过期了哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040308"
  zh_CN: "您已经没有助力次数了哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040309"
  zh_CN: "挂机外卖助力奖励未满足条件"
}
rows {
  id: "-10040310"
  zh_CN: "您的助力奖励已经领取过了哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040311"
  zh_CN: "挂机外卖未找到解锁宝箱"
  showType: ECST_FlyTip
}
rows {
  id: "-10040312"
  zh_CN: "该助力宝箱还未解锁哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040313"
  zh_CN: "您还有未领取的助力奖励，请先领取哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040314"
  zh_CN: "您目前的宝箱解锁钥匙不足哦，快去做任务获得钥匙吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040315"
  zh_CN: "福袋雨活动未开放"
}
rows {
  id: "-10040316"
  zh_CN: "福袋雨轮次未开放"
}
rows {
  id: "-10040317"
  zh_CN: "福袋雨轮次数量上限"
}
rows {
  id: "-10040318"
  zh_CN: "福袋雨每次数量上限"
}
rows {
  id: "-10040319"
  zh_CN: "福袋雨活动总数量上限"
}
rows {
  id: "-10040320"
  zh_CN: "调用ams发货超时"
}
rows {
  id: "-10040321"
  zh_CN: "调用ams发货重试超过最大次数"
}
rows {
  id: "-10040322"
  zh_CN: "ams查询奖励错误"
}
rows {
  id: "-10040323"
  zh_CN: "福袋雨 红包都开了"
}
rows {
  id: "-10040324"
  zh_CN: "福袋雨 轮次不对"
}
rows {
  id: "-10040326"
  zh_CN: "ams ret < 0 系统级别异常"
}
rows {
  id: "-10040327"
  zh_CN: "ams 0 < ret < 100000 逻辑错误 不用补发"
}
rows {
  id: "-10040328"
  zh_CN: "ams >= 1000000 礼包领完 活动关闭"
}
rows {
  id: "-10040329"
  zh_CN: "调用ams发货遇到异常, 没有单号"
}
rows {
  id: "-10040330"
  zh_CN: "ams服务发现异常"
}
rows {
  id: "-10040331"
  zh_CN: "福袋雨特殊错误码 只是为了通知客户端刷新 非gm不出现"
}
rows {
  id: "-10040332"
  zh_CN: "打开红包太频繁"
}
rows {
  id: "-10040333"
  zh_CN: "发送邮件失败"
}
rows {
  id: "-10040334"
  zh_CN: "查询红包记录太频繁"
}
rows {
  id: "-10040335"
  zh_CN: "幸运气球无奖励可抽"
}
rows {
  id: "-10040336"
  zh_CN: "幸运气球无祝福可抽"
}
rows {
  id: "-10040337"
  zh_CN: "幸运气球货币不足"
}
rows {
  id: "-10040338"
  zh_CN: "幸运气球达到抽奖上限"
}
rows {
  id: "-10040340"
  zh_CN: "已被招募"
}
rows {
  id: "-10040341"
  zh_CN: "活动交互失败"
}
rows {
  id: "-10040342"
  zh_CN: "活动交互失败"
}
rows {
  id: "-10040343"
  zh_CN: "招募活动配置不存在"
}
rows {
  id: "-10040344"
  zh_CN: "招募玩家等级过低"
}
rows {
  id: "-10040345"
  zh_CN: "邀请玩家数量达到上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10040346"
  zh_CN: "招募暂未开启"
}
rows {
  id: "-10040347"
  zh_CN: "今日已助力过"
}
rows {
  id: "-10040348"
  zh_CN: "无空余好友位"
}
rows {
  id: "-10040349"
  zh_CN: "好友已助力过"
}
rows {
  id: "-10040350"
  zh_CN: "不允许招募自己"
  showType: ECST_FlyTip
}
rows {
  id: "-10040351"
  zh_CN: "刮刮乐已刮开,不开重复刮"
}
rows {
  id: "-10040352"
  zh_CN: "刮刮乐未解锁"
}
rows {
  id: "-10040353"
  zh_CN: "刮刮乐已刮开"
}
rows {
  id: "-10040354"
  zh_CN: "刮刮乐已升级"
}
rows {
  id: "-10040355"
  zh_CN: "无效的刮刮乐升级奖励配置"
}
rows {
  id: "-10040360"
  zh_CN: "今日助力已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10040361"
  zh_CN: "无空余好友位"
  showType: ECST_FlyTip
}
rows {
  id: "-10040362"
  zh_CN: "好友已助力过"
  showType: ECST_FlyTip
}
rows {
  id: "-10040363"
  zh_CN: "助力时间已过"
  showType: ECST_FlyTip
}
rows {
  id: "-10040364"
  zh_CN: "已领奖"
  showType: ECST_FlyTip
}
rows {
  id: "-10040365"
  zh_CN: "未达成"
  showType: ECST_FlyTip
}
rows {
  id: "-10040366"
  zh_CN: "不可以给自己助力"
  showType: ECST_FlyTip
}
rows {
  id: "-10040400"
  zh_CN: "红包被抢完啦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040401"
  zh_CN: "你已经开启过这个红包了哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040402"
  zh_CN: "星宝今天发送太多红包啦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040403"
  zh_CN: "星宝今天开启太多红包啦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040404"
  zh_CN: "红包被抢完啦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040405"
  zh_CN: "红包祝福太拥挤啦，稍后再试试吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040406"
  zh_CN: "这个红包只有特定的星宝才能开启哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040407"
  zh_CN: "活动期间才能给其他星宝发放道具红包哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040408"
  zh_CN: "销毁红包失败"
}
rows {
  id: "-10040409"
  zh_CN: "红包只能给星朋友分享哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10040500"
  zh_CN: "春季回馈活动时间配置错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10040501"
  zh_CN: "春季回馈活动配置未找到"
  showType: ECST_FlyTip
}
rows {
  id: "-10040502"
  zh_CN: "活动类型错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10040503"
  zh_CN: "奖励配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10040504"
  zh_CN: "签到数据不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10040505"
  zh_CN: "前面有未补签或领取的奖励"
  showType: ECST_FlyTip
}
rows {
  id: "-10040506"
  zh_CN: "该奖励已领取"
  showType: ECST_FlyTip
}
rows {
  id: "-10040507"
  zh_CN: "签到天数不够"
  showType: ECST_FlyTip
}
rows {
  id: "-10040508"
  zh_CN: "当前奖励不需要补签"
  showType: ECST_FlyTip
}
rows {
  id: "-10040509"
  zh_CN: "当前阶段未结束不能补签"
}
rows {
  id: "-10040510"
  zh_CN: "生成收集进度错误"
}
rows {
  id: "-10040511"
  zh_CN: "不满足领奖条件"
}
rows {
  id: "-10040512"
  zh_CN: "还不能领奖"
}
rows {
  id: "-10040513"
  zh_CN: "奖励已经领取"
}
rows {
  id: "-10040514"
  zh_CN: "抽取福星所需道具不足"
}
rows {
  id: "-10040516"
  zh_CN: "抽取福星所需道具不足"
}
rows {
  id: "-10040517"
  zh_CN: "领取合成奖励福星不足"
}
rows {
  id: "-10040518"
  zh_CN: "生成赠送请求失败"
}
rows {
  id: "-10040519"
  zh_CN: "生成索要请求失败"
}
rows {
  id: "-10040520"
  zh_CN: "领取赠送的福星卡失败"
}
rows {
  id: "-10040521"
  zh_CN: "超过最大领取上限"
}
rows {
  id: "-10040522"
  zh_CN: "福星卡不存在"
}
rows {
  id: "-10040523"
  zh_CN: "赠送索要的福星卡失败"
}
rows {
  id: "-10040524"
  zh_CN: "赠送和索要的福星卡不符"
}
rows {
  id: "-10040525"
  zh_CN: "赠送的福星卡已被领取"
}
rows {
  id: "-10040526"
  zh_CN: "索要的福星卡已赠送"
}
rows {
  id: "-10040527"
  zh_CN: "获取索要信息失败"
}
rows {
  id: "-10040528"
  zh_CN: "没有可赠送的福星卡"
}
rows {
  id: "-10040529"
  zh_CN: "福袋雨分享额外次数已用完"
}
rows {
  id: "-10040530"
  zh_CN: "福袋雨点击链接额外次数已用完"
  showType: ECST_FlyTip
}
rows {
  id: "-10040531"
  zh_CN: "福袋雨不能点击自己分享的链接"
  showType: ECST_FlyTip
}
rows {
  id: "-10040532"
  zh_CN: "第一张不可赠送"
}
rows {
  id: "-10040533"
  zh_CN: "收到的福星卡阶段未开启"
}
rows {
  id: "-10040534"
  zh_CN: "获取赠送信息失败"
}
rows {
  id: "-10040535"
  zh_CN: "赠送已被领取"
}
rows {
  id: "-10040536"
  zh_CN: "福袋雨抽奖系统繁忙, 请稍后再试"
}
rows {
  id: "-10040537"
  zh_CN: "获取福星手账簿信息失败"
}
rows {
  id: "-10040538"
  zh_CN: "获取福星手账簿信息不存在"
}
rows {
  id: "-10040539"
  zh_CN: "不能领取自己的福星卡"
}
rows {
  id: "-10040600"
  zh_CN: "春节集福活动配置错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10040601"
  zh_CN: "春节集福活动福卡配置错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10040602"
  zh_CN: "春节集福活动福卡奖励配置错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10040603"
  zh_CN: "春节集福活动任务奖励已领取"
  showType: ECST_FlyTip
}
rows {
  id: "-10040604"
  zh_CN: "春节集福活动奖励条件未达成"
  showType: ECST_FlyTip
}
rows {
  id: "-10040605"
  zh_CN: "今日该小摊获取次数已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10040606"
  zh_CN: "今日赠送福字次数已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10040607"
  zh_CN: "当前暂未获得该福卡"
  showType: ECST_FlyTip
}
rows {
  id: "-10040608"
  zh_CN: "分享次数已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10040609"
  zh_CN: "春节集福商铺配置未找到"
}
rows {
  id: "-10040800"
  zh_CN: "AMS队列满"
  showType: ECST_FlyTip
}
rows {
  id: "-10040801"
  zh_CN: "AMS重试队列未启用"
  showType: ECST_FlyTip
}
rows {
  id: "-10040802"
  zh_CN: "超过AMS重试最大次数"
  showType: ECST_FlyTip
}
rows {
  id: "-10040803"
  zh_CN: "AMS重试队列满"
  showType: ECST_FlyTip
}
rows {
  id: "-10040804"
  zh_CN: "AMS发货失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10040805"
  zh_CN: "创建队伍失败-玩家已经在队伍中"
}
rows {
  id: "-10040806"
  zh_CN: "创建队伍失败-请稍后重试"
}
rows {
  id: "-10040807"
  zh_CN: "创建队伍失败-请稍后重试"
}
rows {
  id: "-10040808"
  zh_CN: "创建队伍失败-请稍后重试"
}
rows {
  id: "-10040809"
  zh_CN: "退出队伍失败-玩家不在队伍中"
}
rows {
  id: "-10040810"
  zh_CN: "退出队伍失败-玩家入队还未满24小时"
}
rows {
  id: "-10040811"
  zh_CN: "退出队伍失败-请稍后再试"
}
rows {
  id: "-10040812"
  zh_CN: "踢出队伍失败-被踢玩家不在队伍中"
}
rows {
  id: "-10040813"
  zh_CN: "踢出队伍失败-玩家不是队长"
}
rows {
  id: "-10040814"
  zh_CN: "加入队伍失败-队伍人数达到上限"
}
rows {
  id: "-10040815"
  zh_CN: "加入队伍失败-玩家位于不同位面"
}
rows {
  id: "-10040816"
  zh_CN: "发送好友入队邀请失败-当前玩家不在队列中"
}
rows {
  id: "-10040817"
  zh_CN: "发送好友入队邀请失败-请稍后再试"
}
rows {
  id: "-10040818"
  zh_CN: "踢出队伍失败-请稍后重试"
}
rows {
  id: "-10040819"
  zh_CN: "踢出队伍失败-当前玩家不在队伍中"
}
rows {
  id: "-10040820"
  zh_CN: "踢出队伍失败-请稍后重试"
}
rows {
  id: "-10040901"
  zh_CN: "尚未打卡"
}
rows {
  id: "-10040902"
  zh_CN: "打卡手册已升级"
}
rows {
  id: "-10040903"
  zh_CN: "无效的打开手册升级配置"
}
rows {
  id: "-10040910"
  zh_CN: "抽奖消耗不足"
}
rows {
  id: "-10040911"
  zh_CN: "不放回抽奖无奖励可抽"
}
rows {
  id: "-10040921"
  zh_CN: "投喂数量已满"
}
rows {
  id: "-10040922"
  zh_CN: "无效的竞速时间"
}
rows {
  id: "-10040923"
  zh_CN: "投喂参数无效"
}
rows {
  id: "-10040924"
  zh_CN: "助力已经结束"
}
rows {
  id: "-10040925"
  zh_CN: "助力时间已满"
}
rows {
  id: "-10040926"
  zh_CN: "您的好友已购买"
  showType: ECST_FlyTip
}
rows {
  id: "-10040927"
  zh_CN: "每日竞速排行榜结算错误"
}
rows {
  id: "-10040928"
  zh_CN: "助力次数已满"
}
rows {
  id: "-10040929"
  zh_CN: "重复助力"
}
rows {
  id: "-10040930"
  zh_CN: "竞速排行榜邮件发送失败"
}
rows {
  id: "-10040931"
  zh_CN: "贴纸奖励重复获取"
}
rows {
  id: "-10040932"
  zh_CN: "贴纸奖励消耗道具不足"
}
rows {
  id: "-10040933"
  zh_CN: "贴纸奖励未连线"
}
rows {
  id: "-10040934"
  zh_CN: "贴纸奖励未全部揭开"
}
rows {
  id: "-10040950"
  zh_CN: "赛事热身活动阶段配置未找到"
}
rows {
  id: "-10040951"
  zh_CN: "赛事热身活动属性错误"
}
rows {
  id: "-10040952"
  zh_CN: "赛事热身活动游戏对局次数不足"
}
rows {
  id: "-10040953"
  zh_CN: "赛事热身活动对局次数奖励配置不存在"
}
rows {
  id: "-10040954"
  zh_CN: "赛事热身活动积分奖励配置错误"
}
rows {
  id: "-10040955"
  zh_CN: "赛事热身活动积分奖励条件未达到"
}
rows {
  id: "-10040956"
  zh_CN: "赛事热身活动积分奖励未到领取时间"
}
rows {
  id: "-10040957"
  zh_CN: "赛事热身活动对局次数奖励已领取"
}
rows {
  id: "-10040958"
  zh_CN: "赛事热身活动积分奖励已领取"
}
rows {
  id: "-10040960"
  zh_CN: "未找到指定的子活动id"
}
rows {
  id: "-10040961"
  zh_CN: "未找到对应打卡配置"
}
rows {
  id: "-10040962"
  zh_CN: "不符合领奖条件"
}
rows {
  id: "-10050000"
  zh_CN: "服务器内部错误(-10050000)"
  showType: ECST_FlyTip
}
rows {
  id: "-10050001"
  zh_CN: "-10050001"
}
rows {
  id: "-10050002"
  zh_CN: "-10050002"
}
rows {
  id: "-10050003"
  zh_CN: "-10050003"
}
rows {
  id: "-10050004"
  zh_CN: "-10050004"
}
rows {
  id: "-10051000"
  zh_CN: "服务器内部错误(-10051000)"
}
rows {
  id: "-10051100"
  zh_CN: "该用户隐藏了个人信息"
  showType: ECST_FlyTip
}
rows {
  id: "-10051101"
  zh_CN: "星宝暂时不接受邀请喔～"
  showType: ECST_FlyTip
}
rows {
  id: "-10052000"
  zh_CN: "邮件操作错误, 请稍后重试"
}
rows {
  id: "-10052001"
  zh_CN: "邮件操作失败, 请稍后重试"
}
rows {
  id: "-10052002"
  zh_CN: "邮件不存在"
}
rows {
  id: "-10052003"
  zh_CN: "该邮件没有可领取的附件"
  showType: ECST_FlyTip
}
rows {
  id: "-10052004"
  zh_CN: "没有可快速领取的附件"
}
rows {
  id: "-10052005"
  zh_CN: "邮件参数错误"
}
rows {
  id: "-10052006"
  zh_CN: "邮件已过期"
  showType: ECST_FlyTip
}
rows {
  id: "-10052007"
  zh_CN: "活动已过期"
  showType: ECST_FlyTip
}
rows {
  id: "-10052008"
  zh_CN: "邮件加载中，请稍候"
  showType: ECST_FlyTip
}
rows {
  id: "-10052009"
  zh_CN: "索取类邮件附件不可领取"
  showType: ECST_FlyTip
}
rows {
  id: "-10052010"
  zh_CN: "邮箱附件暂时不可领取，请稍候"
  showType: ECST_FlyTip
}
rows {
  id: "-10060001"
  zh_CN: "当前创建合集数量已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10060002"
  zh_CN: "合集参数错误"
}
rows {
  id: "-10060003"
  zh_CN: "合集包含敏感词"
  showType: ECST_Custom
}
rows {
  id: "-10060004"
  zh_CN: "合集错误, 请稍后重试"
}
rows {
  id: "-10060005"
  zh_CN: "合集id非法"
}
rows {
  id: "-10060006"
  zh_CN: "合集不存在"
}
rows {
  id: "-10060007"
  zh_CN: "合集名称修改被封禁"
}
rows {
  id: "-10060008"
  zh_CN: "合集简介修改被封禁"
}
rows {
  id: "-10060009"
  zh_CN: "收藏合集已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10060010"
  zh_CN: "合集名称不可填写该内容"
  showType: ECST_FlyTip
}
rows {
  id: "-10060011"
  zh_CN: "不是自己创建的合集, 无法进行此操作"
  showType: ECST_FlyTip
}
rows {
  id: "-10060012"
  zh_CN: "已经收藏过此合集"
  showType: ECST_FlyTip
}
rows {
  id: "-10060013"
  zh_CN: "不能收藏自己创建的合集"
  showType: ECST_FlyTip
}
rows {
  id: "-10060014"
  zh_CN: "尚未收藏此合集"
  showType: ECST_FlyTip
}
rows {
  id: "-10060015"
  zh_CN: "合集地图数量超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10070001"
  zh_CN: "CsUgc未知错误"
}
rows {
  id: "-10070002"
  zh_CN: "CsUgc转发错误"
}
rows {
  id: "-10070003"
  zh_CN: "CsUgc转发超时"
}
rows {
  id: "-10070004"
  zh_CN: "CsUgc协议解析错误"
}
rows {
  id: "-10070005"
  zh_CN: "CsUgc缺少对应的Handler"
}
rows {
  id: "-10070006"
  zh_CN: "CsUgc处理错误"
}
rows {
  id: "-10070007"
  zh_CN: "CsUgc处理超时"
}
rows {
  id: "-10080001"
  zh_CN: "-10080001"
}
rows {
  id: "-10080002"
  zh_CN: "-10080002"
}
rows {
  id: "-10080003"
  zh_CN: "-10080003"
}
rows {
  id: "-10080004"
  zh_CN: "-10080004"
}
rows {
  id: "-10080005"
  zh_CN: "-10080005"
}
rows {
  id: "-10080006"
  zh_CN: "-10080006"
}
rows {
  id: "-10080007"
  zh_CN: "-10080007"
}
rows {
  id: "-10081001"
  zh_CN: "-10081001"
}
rows {
  id: "-10081002"
  zh_CN: "-10081002"
}
rows {
  id: "-10081003"
  zh_CN: "-10081003"
}
rows {
  id: "-10081004"
  zh_CN: "-10081004"
}
rows {
  id: "-10081005"
  zh_CN: "-10081005"
}
rows {
  id: "-10081006"
  zh_CN: "-10081006"
}
rows {
  id: "-10081007"
  zh_CN: "-10081007"
}
rows {
  id: "-10081008"
  zh_CN: "-10081008"
}
rows {
  id: "-10081009"
  zh_CN: "-10081009"
}
rows {
  id: "-10100001"
  zh_CN: "检测背包空间不足"
}
rows {
  id: "-10100002"
  zh_CN: "更新道具数量失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10100003"
  zh_CN: "用户背包类型不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10100004"
  zh_CN: "道具不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10100005"
  zh_CN: "道具不能出售"
  showType: ECST_FlyTip
}
rows {
  id: "-10100006"
  zh_CN: "道具数量不足"
}
rows {
  id: "-10100007"
  zh_CN: "扣除背包道具失败"
}
rows {
  id: "-10100008"
  zh_CN: "道具使用数量错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10100009"
  zh_CN: "该道具不可使用"
  showType: ECST_FlyTip
}
rows {
  id: "-10100010"
  zh_CN: "道具使用类型错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10100011"
  zh_CN: "装备道具数量上限"
}
rows {
  id: "-10100012"
  zh_CN: "道具数量拥有达到上限"
}
rows {
  id: "-10100013"
  zh_CN: "道具已过期"
}
rows {
  id: "-10100014"
  zh_CN: "客户端版本不支持此道具"
}
rows {
  id: "-10100015"
  zh_CN: "QQ金币不足"
}
rows {
  id: "-10100016"
  zh_CN: "该装扮与性别不符，不能穿戴"
}
rows {
  id: "-10100017"
  zh_CN: "未获得基础角色"
}
rows {
  id: "-10100018"
  zh_CN: "礼包不存在"
}
rows {
  id: "-10100019"
  zh_CN: "AMS礼包配置不正确"
}
rows {
  id: "-10100020"
  zh_CN: "奖励发送失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10100021"
  zh_CN: "未查询到角色"
  showType: ECST_FlyTip
}
rows {
  id: "-10100022"
  zh_CN: "当前QQ未查询到王者荣耀角色"
  showType: ECST_FlyTip
}
rows {
  id: "-10100023"
  zh_CN: "礼包已过期"
}
rows {
  id: "-10100024"
  zh_CN: "礼包购买数量达到上限"
}
rows {
  id: "-10100025"
  zh_CN: "获取余额失败"
}
rows {
  id: "-10100026"
  zh_CN: "消费失败"
}
rows {
  id: "-10100027"
  zh_CN: "米大师接口请求失败"
}
rows {
  id: "-10100028"
  zh_CN: "米大师赠送失败"
}
rows {
  id: "-10100029"
  zh_CN: "米大师下单失败"
}
rows {
  id: "-10100030"
  zh_CN: "充值配置错误"
}
rows {
  id: "-10100031"
  zh_CN: "试衣间未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-10100032"
  zh_CN: "该渠道未开放充值"
}
rows {
  id: "-10100033"
  zh_CN: "钻石消耗途径错误"
}
rows {
  id: "-10100034"
  zh_CN: "米大师购买锁定失败"
}
rows {
  id: "-10100035"
  zh_CN: "米大师购买未解锁"
}
rows {
  id: "-10100036"
  zh_CN: "月卡过期"
}
rows {
  id: "-10100037"
  zh_CN: "今天月卡每日道具已领取"
}
rows {
  id: "-10100038"
  zh_CN: "未到续费时间"
}
rows {
  id: "-10100039"
  zh_CN: "月卡已下架"
}
rows {
  id: "-10100040"
  zh_CN: "初始装扮已赠送"
}
rows {
  id: "-10100041"
  zh_CN: "购买失败，余额不足"
}
rows {
  id: "-10100042"
  zh_CN: "服务器错误，请重新授权"
}
rows {
  id: "-10100043"
  zh_CN: "自选礼包该道具选取次数达到上限"
}
rows {
  id: "-10100044"
  zh_CN: "充值会员奖励未解锁"
}
rows {
  id: "-10100045"
  zh_CN: "充值会员奖励已领取"
}
rows {
  id: "-10100046"
  zh_CN: "米大师配置错误"
}
rows {
  id: "-10100047"
  zh_CN: "MidasCheckIdempotentERR"
}
rows {
  id: "-10100049"
  zh_CN: "默认装扮缺失, 无法操作"
}
rows {
  id: "-10100050"
  zh_CN: "联动装扮不可混合搭配"
}
rows {
  id: "-10100051"
  zh_CN: "应版权方要求，该装扮不可保存至备用装扮方案"
  showType: ECST_FlyTip
}
rows {
  id: "-10100052"
  zh_CN: "解析ams接口返回值错误"
}
rows {
  id: "-10100053"
  zh_CN: "ams http请求错误"
}
rows {
  id: "-10100054"
  zh_CN: "微信小游戏密钥更新失败"
}
rows {
  id: "-10100055"
  zh_CN: "操作太快了，请稍等"
  showType: ECST_FlyTip
}
rows {
  id: "-10100056"
  zh_CN: "脸部和时装参数错误"
}
rows {
  id: "-10100057"
  zh_CN: "无法获得道具，请升级客户端版本"
}
rows {
  id: "-10100058"
  zh_CN: "道具使用参数错误"
}
rows {
  id: "-10100059"
  zh_CN: "BP道具对应的通行证配置未找到"
}
rows {
  id: "-10100060"
  zh_CN: "BP道具对应的通行证类型错误"
}
rows {
  id: "-10100061"
  zh_CN: "米大师创建道具失败"
}
rows {
  id: "-10100062"
  zh_CN: "米大师发布道具失败"
}
rows {
  id: "-10100063"
  zh_CN: "米大师查询道具失败"
}
rows {
  id: "-10100064"
  zh_CN: "试衣间已解锁"
}
rows {
  id: "-10100065"
  zh_CN: "创作者入驻米大师失败"
}
rows {
  id: "-10100066"
  zh_CN: "获取创作者入驻信息失败"
}
rows {
  id: "-10100067"
  zh_CN: "此方案是当前穿戴的搭配方案，无法取消展示"
  showType: ECST_FlyTip
}
rows {
  id: "-10100068"
  zh_CN: "互动组合不存在"
}
rows {
  id: "-10100069"
  zh_CN: "互动组合序列不存在"
}
rows {
  id: "-10100070"
  zh_CN: "互动组合数量上限"
}
rows {
  id: "-10100071"
  zh_CN: "互动组合设置不符合规范"
}
rows {
  id: "-10100072"
  zh_CN: "该套装不能切换状态"
}
rows {
  id: "-10100073"
  zh_CN: "租借道具堆叠数量配置错误"
}
rows {
  id: "-10100074"
  zh_CN: "已存在该道具"
}
rows {
  id: "-10120001"
  zh_CN: "attr不存在该成就信息"
}
rows {
  id: "-10120002"
  zh_CN: "Res不存在该成就信息"
}
rows {
  id: "-10120003"
  zh_CN: "解锁失败，请重试"
  showType: ECST_FlyTip
}
rows {
  id: "-10120004"
  zh_CN: "成就奖励已领取"
}
rows {
  id: "-10120005"
  zh_CN: "成就不可领奖"
}
rows {
  id: "-10120006"
  zh_CN: "成就领奖失败"
}
rows {
  id: "-10130001"
  zh_CN: "资源不足无法购买"
  showType: ECST_FlyTip
}
rows {
  id: "-10130002"
  zh_CN: "背包空间不足"
  showType: ECST_FlyTip
}
rows {
  id: "-10130003"
  zh_CN: "商品达到上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10130004"
  zh_CN: "该商品已下架，请刷新"
  showType: ECST_FlyTip
}
rows {
  id: "-10130005"
  zh_CN: "商城售卖的一级货币购买的商品没有配置米大师商品"
}
rows {
  id: "-10130006"
  zh_CN: "商品价格已更新"
  showType: ECST_FlyTip
}
rows {
  id: "-10130007"
  zh_CN: "商品不支持批量购买"
}
rows {
  id: "-10130008"
  zh_CN: "商品购买条件不足"
  showType: ECST_FlyTip
}
rows {
  id: "-10130009"
  zh_CN: "该装扮为限时体验装扮，无法进行染色"
}
rows {
  id: "-10130010"
  zh_CN: "不能向非好友索要"
}
rows {
  id: "-10130011"
  zh_CN: "好友礼物索要列表满啦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10130012"
  zh_CN: "好友礼物箱满啦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10130013"
  zh_CN: "今天已经向很多星宝索要礼物啦，明天再试试吧"
  showType: ECST_FlyTip
}
rows {
  id: "-10130014"
  zh_CN: "今天已经送出很多礼物啦，明天再送吧"
  showType: ECST_FlyTip
}
rows {
  id: "-10130015"
  zh_CN: "商品购买数量异常"
}
rows {
  id: "-10130016"
  zh_CN: "赠送数量异常"
}
rows {
  id: "-10130017"
  zh_CN: "索要数量异常"
}
rows {
  id: "-10130018"
  zh_CN: "星宝达到5级才可以赠送礼物哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10130019"
  zh_CN: "该商品暂时不能购买"
}
rows {
  id: "-10130020"
  zh_CN: "已达到赠送次数上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10130021"
  zh_CN: "等级不足，无法发起索要"
  showType: ECST_FlyTip
}
rows {
  id: "-10130022"
  zh_CN: "亲密度不足，无法发起索要"
  showType: ECST_FlyTip
}
rows {
  id: "-10130023"
  zh_CN: "好友未开启索要功能"
  showType: ECST_FlyTip
}
rows {
  id: "-10130500"
  zh_CN: "生成商城赠送检查器失败"
}
rows {
  id: "-10130501"
  zh_CN: "获取商城赠送对象公共数据失败"
}
rows {
  id: "-10130502"
  zh_CN: "赠送失败，该物品已达好友上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10130503"
  zh_CN: "赠送失败，该物品已达好友上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10130504"
  zh_CN: "该好友已经拥有此物品，无法赠送"
  showType: ECST_FlyTip
}
rows {
  id: "-10130505"
  zh_CN: "赠送时发生异常，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-10130506"
  zh_CN: "对方版本不兼容无法赠送，请升级客户端版本后再尝试"
  showType: ECST_FlyTip
}
rows {
  id: "-10130507"
  zh_CN: "商品不支持离线购买"
}
rows {
  id: "-10130518"
  zh_CN: "本月索要次数已达上限，将于下个月1号刷新"
  showType: ECST_FlyTip
}
rows {
  id: "-10140001"
  zh_CN: "参数异常"
  showType: ECST_FlyTip
}
rows {
  id: "-10140002"
  zh_CN: "rank更新RPC失败"
}
rows {
  id: "-10140003"
  zh_CN: "排行榜后端未找到"
}
rows {
  id: "-10140004"
  zh_CN: "排行榜后端未响应"
}
rows {
  id: "-10140005"
  zh_CN: "排行榜删除数据失败"
}
rows {
  id: "-10140006"
  zh_CN: "排行榜ID编码失败"
}
rows {
  id: "-10140007"
  zh_CN: "数据刷新中，过段时间再来看看吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-10140008"
  zh_CN: "排行榜配置未找到"
}
rows {
  id: "-10140009"
  zh_CN: "排行榜地区信息修改达到上限"
}
rows {
  id: "-10140010"
  zh_CN: "排行榜后端错误码返回"
}
rows {
  id: "-10140011"
  zh_CN: "排行榜后端请求拥塞"
}
rows {
  id: "-10140012"
  zh_CN: "排行榜成绩编码失败"
}
rows {
  id: "-10140013"
  zh_CN: "排行榜成绩解码失败"
}
rows {
  id: "-10140014"
  zh_CN: "排行榜地区信息修改正在进行，请稍等"
}
rows {
  id: "-10140015"
  zh_CN: "排行榜信息已推送到Redis，请重试"
}
rows {
  id: "-10140016"
  zh_CN: "排行榜Redis操作错误码返回"
}
rows {
  id: "-10140017"
  zh_CN: "排行榜Ugc操作错误码返回"
}
rows {
  id: "-10140018"
  zh_CN: "排行榜更新时间戳超出最迟允许时间"
}
rows {
  id: "-10140019"
  zh_CN: "排行榜更新触发熔断"
}
rows {
  id: "-10140020"
  zh_CN: "排行榜LBS编码非法"
}
rows {
  id: "-10140021"
  zh_CN: "赛季数据结算中，稍后再试试吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-10140022"
  zh_CN: "地理排行榜未开启"
}
rows {
  id: "-10140023"
  zh_CN: "排行榜Proc任务数量达到上限"
}
rows {
  id: "-10140024"
  zh_CN: "排行榜Proc任务已关闭"
}
rows {
  id: "-10140025"
  zh_CN: "排行榜分发任务执行失败"
}
rows {
  id: "-10140026"
  zh_CN: "排行榜分发任务无法执行"
}
rows {
  id: "-10140027"
  zh_CN: "RankService未找到"
}
rows {
  id: "-10140028"
  zh_CN: "排行榜排名解码失败"
}
rows {
  id: "-10140029"
  zh_CN: "排行榜未到结算时刻"
}
rows {
  id: "-10140030"
  zh_CN: "排行榜正在结算"
}
rows {
  id: "-10140031"
  zh_CN: "排行榜分发任务进度不存在"
}
rows {
  id: "-10140032"
  zh_CN: "排行榜镜像生成中"
}
rows {
  id: "-10140033"
  zh_CN: "排行榜镜像生成失败"
}
rows {
  id: "-10140034"
  zh_CN: "排行榜镜像拒绝生成"
}
rows {
  id: "-10140035"
  zh_CN: "排行榜镜像未找到"
}
rows {
  id: "-10140036"
  zh_CN: "排行榜未找到对应编码的ID"
}
rows {
  id: "-10140037"
  zh_CN: "排行榜镜像状态未知"
}
rows {
  id: "-10140038"
  zh_CN: "排行榜结算已过期"
}
rows {
  id: "-10140039"
  zh_CN: "地区排行榜结算准备失败"
}
rows {
  id: "-10140040"
  zh_CN: "排行榜社团LBS信息未开启"
}
rows {
  id: "-10140041"
  zh_CN: "排行榜不支持更新"
}
rows {
  id: "-10140042"
  zh_CN: "排行榜个人信息拉取过多"
}
rows {
  id: "-10150001"
  zh_CN: "不能将自己拉入黑名单"
}
rows {
  id: "-10150002"
  zh_CN: "已是拉黑"
}
rows {
  id: "-10150003"
  zh_CN: "不在黑名单中"
  showType: ECST_FlyTip
}
rows {
  id: "-10150004"
  zh_CN: "黑名单数量超出限制"
  showType: ECST_FlyTip
}
rows {
  id: "-10150005"
  zh_CN: "你已将这名星宝拉黑"
  showType: ECST_FlyTip
}
rows {
  id: "-10150006"
  zh_CN: "这名星宝已将你拉黑"
  showType: ECST_FlyTip
}
rows {
  id: "-10170001"
  zh_CN: "tssmanager checkTextDirty失败"
}
rows {
  id: "-10170002"
  zh_CN: "暂时无法与该用户聊天"
  showType: ECST_FlyTip
}
rows {
  id: "-10170003"
  zh_CN: "不在目标聊天里"
}
rows {
  id: "-10170004"
  zh_CN: "聊天创建失败"
}
rows {
  id: "-10170005"
  zh_CN: "消息发送失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10170006"
  zh_CN: "聊天不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10170007"
  zh_CN: "稍等一下再发言吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-10170008"
  zh_CN: "聊天消息重复"
  showType: ECST_FlyTip
}
rows {
  id: "-10170009"
  zh_CN: "消息拉取失败"
}
rows {
  id: "-10170010"
  zh_CN: "招募信息"
}
rows {
  id: "-10170011"
  zh_CN: "当前聊天频道不在服务中，系统维护"
}
rows {
  id: "-10170012"
  zh_CN: "当前聊天频道不在版本支持范围内"
}
rows {
  id: "-10170013"
  zh_CN: "到达每日来聊天次数限制"
}
rows {
  id: "-10170014"
  zh_CN: "目前尚未达到世界聊天等级限制"
  showType: ECST_FlyTip
}
rows {
  id: "-10170015"
  zh_CN: "频道不支持当前消息类型"
}
rows {
  id: "-10170016"
  zh_CN: "玩家已不在新人频道"
}
rows {
  id: "-10170017"
  zh_CN: "系统繁忙，请稍后重试(1017)"
}
rows {
  id: "-10170018"
  zh_CN: "目前尚未达到大厅聊天等级限制"
}
rows {
  id: "-10170019"
  zh_CN: "由于您的腾讯游戏信用<350分，存在异常或不良游戏行为，无法发言。"
}
rows {
  id: "-10170020"
  zh_CN: "发送邀请过于频繁，请稍后再试"
}
rows {
  id: "-10170021"
  zh_CN: "您的分享太快啦！请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-10170022"
  zh_CN: "您已开启【禁用公共聊天】功能，可往【星宝防护设置】进行调整"
  showType: ECST_FlyTip
}
rows {
  id: "-10170023"
  zh_CN: "聊天系统升级中，暂时无法发送"
}
rows {
  id: "-10170031"
  zh_CN: "今天无法向更多人打招呼啦"
  showType: ECST_FlyTip
}
rows {
  id: "-10170032"
  zh_CN: "TA回复之前，不能发消息了哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10170033"
  zh_CN: "该功能已关闭"
  showType: ECST_FlyTip
}
rows {
  id: "-10170034"
  zh_CN: "该功能暂未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-10170035"
  zh_CN: "暂时无法向更多人打招呼"
  showType: ECST_FlyTip
}
rows {
  id: "-10170036"
  zh_CN: "暂时无法向更多人打招呼，请结束一些聊天"
  showType: ECST_FlyTip
}
rows {
  id: "-10170037"
  zh_CN: "这位星宝开启了【禁止陌生人私聊】权限哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10170038"
  zh_CN: "TA今天无法被更多人打招呼啦"
  showType: ECST_FlyTip
}
rows {
  id: "-10190001"
  zh_CN: "任务初始化失败"
}
rows {
  id: "-10190002"
  zh_CN: "奖励领取失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10190003"
  zh_CN: "任务不存在"
}
rows {
  id: "-10190004"
  zh_CN: "任务未完成"
  showType: ECST_FlyTip
}
rows {
  id: "-10190005"
  zh_CN: "任务已领奖"
  showType: ECST_FlyTip
}
rows {
  id: "-10190006"
  zh_CN: "任务已结束"
  showType: ECST_FlyTip
}
rows {
  id: "-10190007"
  zh_CN: "任务邮件模板不存在"
}
rows {
  id: "-10190008"
  zh_CN: "任务管理器不存在"
}
rows {
  id: "-10200001"
  zh_CN: "设置烟花文本或放烟花时活动没开"
}
rows {
  id: "-10200002"
  zh_CN: "设置烟花文本时名字非法"
}
rows {
  id: "-10200004"
  zh_CN: "放烟花时还没有设置文本"
}
rows {
  id: "-10200005"
  zh_CN: "放烟花时道具补足"
}
rows {
  id: "-10200006"
  zh_CN: "放烟花时道具错误"
}
rows {
  id: "-10200007"
  zh_CN: "不在大厅里"
}
rows {
  id: "-10200008"
  zh_CN: "不能使用烟花"
}
rows {
  id: "-10200009"
  zh_CN: "信用分检查不通过"
}
rows {
  id: "-10200010"
  zh_CN: "禁止修改烟花文本"
}
rows {
  id: "-10280001"
  zh_CN: "批量获取好友public数据失败"
}
rows {
  id: "-10280002"
  zh_CN: "批量获取场景内用户public数据失败"
}
rows {
  id: "-10300001"
  zh_CN: "pve入场券不足"
}
rows {
  id: "-10300002"
  zh_CN: "pve领取条件不满足"
}
rows {
  id: "-10300003"
  zh_CN: "pve奖励已领取"
}
rows {
  id: "-10300004"
  zh_CN: "pve数据加载失败"
}
rows {
  id: "-10400001"
  zh_CN: "已经加入社团了"
  showType: ECST_FlyTip
}
rows {
  id: "-10400002"
  zh_CN: "当前权限不足,不能操作"
  showType: ECST_FlyTip
}
rows {
  id: "-10400003"
  zh_CN: "该社团名称被占用了"
  showType: ECST_FlyTip
}
rows {
  id: "-10400004"
  zh_CN: "已经申请过该社团，请等待审批"
  showType: ECST_FlyTip
}
rows {
  id: "-10400005"
  zh_CN: "社团成员已满"
  showType: ECST_FlyTip
}
rows {
  id: "-10400006"
  zh_CN: "社团名称不能为空"
  showType: ECST_FlyTip
}
rows {
  id: "-10400007"
  zh_CN: "管理员数量已达到上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10400008"
  zh_CN: "社团申请人数已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10400009"
  zh_CN: "社团不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10400010"
  zh_CN: "创建公会插入db失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10400011"
  zh_CN: "创建公会插入db失败"
}
rows {
  id: "-10400012"
  zh_CN: "您已申请公会数量已达上限,请耐心等待审核噢"
  showType: ECST_FlyTip
}
rows {
  id: "-10400013"
  zh_CN: "成员不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10400014"
  zh_CN: "社团配置错误"
}
rows {
  id: "-10400015"
  zh_CN: "配置错误"
}
rows {
  id: "-10400016"
  zh_CN: "社团服务器内部错误"
}
rows {
  id: "-10400017"
  zh_CN: "货币数量不足"
  showType: ECST_FlyTip
}
rows {
  id: "-10400018"
  zh_CN: "社团名称过长"
  showType: ECST_FlyTip
}
rows {
  id: "-10400019"
  zh_CN: "社团名称不可用，换一个试试吧"
  showType: ECST_FlyTip
}
rows {
  id: "-10400020"
  zh_CN: "社团宣言不可用，换一个试试吧"
  showType: ECST_FlyTip
}
rows {
  id: "-10400021"
  zh_CN: "地图操作类型不存在"
}
rows {
  id: "-10400022"
  zh_CN: "地图无法操作"
}
rows {
  id: "-10400023"
  zh_CN: "已有社团，无法申请加入"
  showType: ECST_FlyTip
}
rows {
  id: "-10400024"
  zh_CN: "等级过低，10级后可创建社团"
  showType: ECST_FlyTip
}
rows {
  id: "-10400025"
  zh_CN: "社团标签太少"
  showType: ECST_FlyTip
}
rows {
  id: "-10400026"
  zh_CN: "社团标签太多"
  showType: ECST_FlyTip
}
rows {
  id: "-10400027"
  zh_CN: "未加入该社团"
  showType: ECST_FlyTip
}
rows {
  id: "-10400028"
  zh_CN: "加入社团的申请被拒绝"
  showType: ECST_FlyTip
}
rows {
  id: "-10400029"
  zh_CN: "该玩家已加入其他社团"
  showType: ECST_FlyTip
}
rows {
  id: "-10400030"
  zh_CN: "社团成员已满"
  showType: ECST_FlyTip
}
rows {
  id: "-10400031"
  zh_CN: "社团申请列表已满"
  showType: ECST_FlyTip
}
rows {
  id: "-10400032"
  zh_CN: "社团宣言过长"
  showType: ECST_FlyTip
}
rows {
  id: "-10400033"
  zh_CN: "有团员时无法解散社团"
  showType: ECST_FlyTip
}
rows {
  id: "-10400034"
  zh_CN: "创建群组信息失败"
}
rows {
  id: "-10400035"
  zh_CN: "分享地图数量过多"
}
rows {
  id: "-10400036"
  zh_CN: "社团当前关闭了申请"
  showType: ECST_FlyTip
}
rows {
  id: "-10400037"
  zh_CN: "玩家被禁止创建社团"
  showType: ECST_FlyTip
}
rows {
  id: "-10400038"
  zh_CN: "公会转发：已不存在"
}
rows {
  id: "-10400039"
  zh_CN: "无需转发"
}
rows {
  id: "-10400040"
  zh_CN: "社团名称过短"
  showType: ECST_FlyTip
}
rows {
  id: "-10400041"
  zh_CN: "社团当前总地图数量过多"
  showType: ECST_FlyTip
}
rows {
  id: "-10400042"
  zh_CN: "服务器已迁移"
}
rows {
  id: "-10400043"
  zh_CN: "您输入的内容不可用，换一个试试吧"
  showType: ECST_FlyTip
}
rows {
  id: "-10400044"
  zh_CN: "无效的订阅"
}
rows {
  id: "-10400045"
  zh_CN: "还有其它成员，无法解散社团"
  showType: ECST_FlyTip
}
rows {
  id: "-10400046"
  zh_CN: "已达今日修改次数上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10400047"
  zh_CN: "修改频率过快，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-10400048"
  zh_CN: "5级后即可申请加入社团"
  showType: ECST_FlyTip
}
rows {
  id: "-10400049"
  zh_CN: "置顶地图数量过多"
  showType: ECST_FlyTip
}
rows {
  id: "-10400050"
  zh_CN: "社团副团长数量已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10400051"
  zh_CN: "这名星宝已经是副团长啦"
  showType: ECST_FlyTip
}
rows {
  id: "-10400052"
  zh_CN: "申请的星宝过多，社团没有足够的人数空间"
  showType: ECST_FlyTip
}
rows {
  id: "-10400053"
  zh_CN: "该申请已被处理"
  showType: ECST_FlyTip
}
rows {
  id: "-10400054"
  zh_CN: "部分申请已被处理过"
  showType: ECST_FlyTip
}
rows {
  id: "-10400055"
  zh_CN: "本赛季已修改过社团定位"
  showType: ECST_FlyTip
}
rows {
  id: "-10400056"
  zh_CN: "今天已经提醒过团长啦"
  showType: ECST_FlyTip
}
rows {
  id: "-10470001"
  zh_CN: "玩家标签数量达到上限"
}
rows {
  id: "-10470002"
  zh_CN: "玩家标签未解锁"
}
rows {
  id: "-10500001"
  zh_CN: "已经领取奖励"
}
rows {
  id: "-10500002"
  zh_CN: "等级不足"
}
rows {
  id: "-10500003"
  zh_CN: "没有奖励领取"
}
rows {
  id: "-10500004"
  zh_CN: "活动尚未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-10500005"
  zh_CN: "已经领取奖励"
}
rows {
  id: "-10500006"
  zh_CN: "货币数量不足"
}
rows {
  id: "-10500007"
  zh_CN: "超过最大等级"
}
rows {
  id: "-10500008"
  zh_CN: "未解锁"
}
rows {
  id: "-10500009"
  zh_CN: "通行证已过期"
  showType: ECST_FlyTip
}
rows {
  id: "-10500010"
  zh_CN: "通行证已解锁"
}
rows {
  id: "-10520001"
  zh_CN: "派对获取失败（10520001）"
  showType: ECST_FlyTip
}
rows {
  id: "-10520002"
  zh_CN: "派对存储失败（10520002）"
  showType: ECST_FlyTip
}
rows {
  id: "-10520003"
  zh_CN: "派对发布失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10520004"
  zh_CN: "参数错误（10520004）"
  showType: ECST_FlyTip
}
rows {
  id: "-10520005"
  zh_CN: "派对获取失败（10520005）"
  showType: ECST_FlyTip
}
rows {
  id: "-10520006"
  zh_CN: "派对搜索失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10520007"
  zh_CN: "派对关闭失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10520008"
  zh_CN: "请重新打开派对界面"
  showType: ECST_FlyTip
}
rows {
  id: "-10520020"
  zh_CN: "超出推荐数量"
}
rows {
  id: "-10520021"
  zh_CN: "派对脚本生成错误"
}
rows {
  id: "-10600001"
  zh_CN: "地图不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10600002"
  zh_CN: "地图名称长度超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10600003"
  zh_CN: "发布地图数量已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10600004"
  zh_CN: "内部错误"
}
rows {
  id: "-10600005"
  zh_CN: "保存类型不存在"
}
rows {
  id: "-10600006"
  zh_CN: "上传地图不存在"
}
rows {
  id: "-10600007"
  zh_CN: "发布id 不匹配"
}
rows {
  id: "-10600008"
  zh_CN: "已经操作过"
}
rows {
  id: "-10600019"
  zh_CN: "不可填写该内容"
  showType: ECST_FlyTip
}
rows {
  id: "-10600020"
  zh_CN: "不可填写该内容"
  showType: ECST_FlyTip
}
rows {
  id: "-10600021"
  zh_CN: "地图操作类型不存在"
}
rows {
  id: "-10600022"
  zh_CN: "地图无法操作"
}
rows {
  id: "-10600023"
  zh_CN: "发布的地图数据不存在"
}
rows {
  id: "-10600024"
  zh_CN: "发布的地图md5数据不存在"
}
rows {
  id: "-10600026"
  zh_CN: "发布的地图page是错误的"
}
rows {
  id: "-10600027"
  zh_CN: "对象操作错误"
}
rows {
  id: "-10600028"
  zh_CN: "订阅作者数量达到上限，操作失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10600029"
  zh_CN: "不能订阅自己"
  showType: ECST_FlyTip
}
rows {
  id: "-10600030"
  zh_CN: "组对象超过了限制"
}
rows {
  id: "-10600031"
  zh_CN: "对象超过了限制"
}
rows {
  id: "-10600032"
  zh_CN: "没有此订阅者"
}
rows {
  id: "-10600033"
  zh_CN: "没有此粉丝"
}
rows {
  id: "-10600034"
  zh_CN: "对象已经包含到列表里面"
}
rows {
  id: "-10600035"
  zh_CN: "已经订阅"
}
rows {
  id: "-10600036"
  zh_CN: "已经成为粉丝"
}
rows {
  id: "-10600037"
  zh_CN: "对象不存在"
}
rows {
  id: "-10600038"
  zh_CN: "申请密钥出现错误"
}
rows {
  id: "-10600039"
  zh_CN: "申请密钥失败"
}
rows {
  id: "-10600040"
  zh_CN: "操作太过频繁"
  showType: ECST_FlyTip
}
rows {
  id: "-10600041"
  zh_CN: "申请密钥配置错误"
}
rows {
  id: "-10600042"
  zh_CN: "保存超过上限"
}
rows {
  id: "-10600043"
  zh_CN: "行为不存在"
}
rows {
  id: "-10600044"
  zh_CN: "其余人在操作"
}
rows {
  id: "-10600045"
  zh_CN: "地图缩略图不合法"
  showType: ECST_FlyTip
}
rows {
  id: "-10600046"
  zh_CN: "上传地图名称重复"
  showType: ECST_FlyTip
}
rows {
  id: "-10600047"
  zh_CN: "这个阶段没有开局"
}
rows {
  id: "-10600048"
  zh_CN: "这个阶段开局battleId错误"
}
rows {
  id: "-10600049"
  zh_CN: "这个阶段mapId错误"
}
rows {
  id: "-10600050"
  zh_CN: "这个阶段已经通关"
}
rows {
  id: "-10600051"
  zh_CN: "更换地图次数超过限制，请通关清除限制或明日再来"
  showType: ECST_FlyTip
}
rows {
  id: "-10600052"
  zh_CN: "重置次数超过限制"
}
rows {
  id: "-10600054"
  zh_CN: "模组社区已关闭"
  showType: ECST_FlyTip
}
rows {
  id: "-10600055"
  zh_CN: "收藏超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10600056"
  zh_CN: "地图类型不对"
}
rows {
  id: "-10600057"
  zh_CN: "生命值道具不存在"
}
rows {
  id: "-10600058"
  zh_CN: "生命值道具数量不够"
}
rows {
  id: "-10600059"
  zh_CN: "无法点赞自己的模组"
}
rows {
  id: "-10600060"
  zh_CN: "阶段ID不对"
}
rows {
  id: "-10600061"
  zh_CN: "从管理端获取step信息出错"
}
rows {
  id: "-10600062"
  zh_CN: "从管理端获取的mapId在DB里面没有"
}
rows {
  id: "-10600063"
  zh_CN: "请输入有效的房间名"
}
rows {
  id: "-10600064"
  zh_CN: "组队中，无法打开地图"
  showType: ECST_FlyTip
}
rows {
  id: "-10600065"
  zh_CN: "组队中，无法创建地图"
  showType: ECST_FlyTip
}
rows {
  id: "-10600066"
  zh_CN: "非空闲状态，无法打开地图"
  showType: ECST_FlyTip
}
rows {
  id: "-10600067"
  zh_CN: "非空闲状态，无法创建地图"
  showType: ECST_FlyTip
}
rows {
  id: "-10600068"
  zh_CN: "该地图已下架"
  showType: ECST_FlyTip
}
rows {
  id: "-10600069"
  zh_CN: "服务发现异常"
}
rows {
  id: "-10600070"
  zh_CN: "创建地图失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10600071"
  zh_CN: "发布地图失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10600073"
  zh_CN: "保存失败"
}
rows {
  id: "-10600074"
  zh_CN: "Db Brief请求失败"
}
rows {
  id: "-10600075"
  zh_CN: "复制失败"
}
rows {
  id: "-10600076"
  zh_CN: "修改信息失败"
}
rows {
  id: "-10600077"
  zh_CN: "发布列表失败"
}
rows {
  id: "-10600078"
  zh_CN: "地图已经存在"
}
rows {
  id: "-10600079"
  zh_CN: "点赞操作失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10600080"
  zh_CN: "收藏操作失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10600081"
  zh_CN: "地图游玩rpc失败"
}
rows {
  id: "-10600082"
  zh_CN: "ugc工匠值经验配置不存在"
}
rows {
  id: "-10600083"
  zh_CN: "订阅操作失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10600084"
  zh_CN: "下架操作失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10600085"
  zh_CN: "获取个人信息失败"
}
rows {
  id: "-10600086"
  zh_CN: "发布失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10600087"
  zh_CN: "收藏地图数达到上限，操作失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10600088"
  zh_CN: "订阅对象不存在"
}
rows {
  id: "-10600089"
  zh_CN: "参考图生成失败"
}
rows {
  id: "-10600090"
  zh_CN: "-10600090"
}
rows {
  id: "-10600091"
  zh_CN: "-10600091"
}
rows {
  id: "-10600092"
  zh_CN: "client connect error"
}
rows {
  id: "-10600093"
  zh_CN: "stub not exist"
}
rows {
  id: "-10600094"
  zh_CN: "-10600094"
}
rows {
  id: "-10600095"
  zh_CN: "-10600095"
}
rows {
  id: "-10600096"
  zh_CN: "-10600096"
}
rows {
  id: "-10600097"
  zh_CN: "操作太过频繁"
}
rows {
  id: "-10600098"
  zh_CN: "操作太过频繁"
  showType: ECST_FlyTip
}
rows {
  id: "-10600099"
  zh_CN: "不支持的UGC状态变更类型"
}
rows {
  id: "-10600100"
  zh_CN: "stub not exist"
}
rows {
  id: "-10600101"
  zh_CN: "下载地图获取信息失败"
}
rows {
  id: "-10600102"
  zh_CN: "-10600102"
}
rows {
  id: "-10600103"
  zh_CN: "通关消耗时间无效"
}
rows {
  id: "-10600104"
  zh_CN: "错误的地图保存状态"
}
rows {
  id: "-10600105"
  zh_CN: "更新地图失败"
}
rows {
  id: "-10600106"
  zh_CN: "没有meta信息"
}
rows {
  id: "-10600107"
  zh_CN: "置顶订阅失败"
}
rows {
  id: "-10600108"
  zh_CN: "置顶订阅失败"
}
rows {
  id: "-10600109"
  zh_CN: "通关客户端没有发送开始，就发送结束"
}
rows {
  id: "-10600110"
  zh_CN: "UgcExp配置未找到"
}
rows {
  id: "-10600111"
  zh_CN: "通关客户端BattleId错误，可能已经结算过了"
}
rows {
  id: "-10600112"
  zh_CN: "驳回"
}
rows {
  id: "-10600113"
  zh_CN: "下架"
}
rows {
  id: "-10600114"
  zh_CN: "申请密钥初始化"
}
rows {
  id: "-10600115"
  zh_CN: "申请key错误"
}
rows {
  id: "-10600116"
  zh_CN: "申请info错误"
}
rows {
  id: "-10600117"
  zh_CN: "操作频繁，请稍后再试"
}
rows {
  id: "-10600118"
  zh_CN: "分享操作失败"
}
rows {
  id: "-10600119"
  zh_CN: "共创地图不存在"
}
rows {
  id: "-10600120"
  zh_CN: "已经是共创者了"
}
rows {
  id: "-10600121"
  zh_CN: "未被邀请共创"
}
rows {
  id: "-10600122"
  zh_CN: "加入共创失败"
}
rows {
  id: "-10600123"
  zh_CN: "移除共创者失败"
}
rows {
  id: "-10600124"
  zh_CN: "该草稿的联合创作者人数已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10600125"
  zh_CN: "-10600125"
}
rows {
  id: "-10600126"
  zh_CN: "该联合创作草稿已被删除"
  showType: ECST_FlyTip
}
rows {
  id: "-10600127"
  zh_CN: "图层授权失败"
}
rows {
  id: "-10600128"
  zh_CN: "联合创作地图不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10600129"
  zh_CN: "不是联合创作地图作者"
  showType: ECST_FlyTip
}
rows {
  id: "-10600130"
  zh_CN: "联合创作地图不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10600131"
  zh_CN: "联合创作地图图层不存在"
}
rows {
  id: "-10600132"
  zh_CN: "无权编辑"
}
rows {
  id: "-10600133"
  zh_CN: "该草稿正在编辑中，请稍后再试试吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-10600134"
  zh_CN: "不在编辑中"
}
rows {
  id: "-10600135"
  zh_CN: "UGC通信失败"
}
rows {
  id: "-10600136"
  zh_CN: "创作者没有日报数据"
}
rows {
  id: "-10600137"
  zh_CN: "共创没有在白名单"
}
rows {
  id: "-10600138"
  zh_CN: "更新失败"
}
rows {
  id: "-10600139"
  zh_CN: "通关客户端没有发送开始，就发送结束"
}
rows {
  id: "-10600140"
  zh_CN: "通关客户端通关时间是负数"
}
rows {
  id: "-10600141"
  zh_CN: "待审核状态下地图无法创建房间"
  showType: ECST_FlyTip
}
rows {
  id: "-10600142"
  zh_CN: "删除失败"
}
rows {
  id: "-10600143"
  zh_CN: "加入共创修改数据失败"
}
rows {
  id: "-10600144"
  zh_CN: "共创基础数据不存在"
}
rows {
  id: "-10600145"
  zh_CN: "待审核状态下地图无法下载"
}
rows {
  id: "-10600146"
  zh_CN: "地图加密出现错误"
}
rows {
  id: "-10600147"
  zh_CN: "地图标签错误"
}
rows {
  id: "-10600148"
  zh_CN: "UGC发布地图话题功能被封禁"
}
rows {
  id: "-10600149"
  zh_CN: "该话题不可用，请更换另外的话题"
}
rows {
  id: "-10600150"
  zh_CN: "UGC发布地图话题安全检测超时"
}
rows {
  id: "-10600151"
  zh_CN: "UGC发布地图话题过多"
}
rows {
  id: "-10600152"
  zh_CN: "首页推荐主题不存在"
}
rows {
  id: "-10600153"
  zh_CN: "请勿重复邀请"
  showType: ECST_FlyTip
}
rows {
  id: "-10600154"
  zh_CN: "邀请频繁"
}
rows {
  id: "-10600155"
  zh_CN: "共创基础数据不存在"
}
rows {
  id: "-10600156"
  zh_CN: "联合创作图层上限"
}
rows {
  id: "-10600157"
  zh_CN: "联合创作创建图层失败"
}
rows {
  id: "-10600158"
  zh_CN: "邀请失败"
}
rows {
  id: "-10600159"
  zh_CN: "加密信息错误,需要重新发布地图"
}
rows {
  id: "-10600160"
  zh_CN: "联合创作地图数量已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10600161"
  zh_CN: "地图修改失败"
}
rows {
  id: "-10600162"
  zh_CN: "地图已下架"
  showType: ECST_FlyTip
}
rows {
  id: "-10600163"
  zh_CN: "发布的地图md5数据校验不过"
}
rows {
  id: "-10600164"
  zh_CN: "太热门啦，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-10600165"
  zh_CN: "对方正在对局中，无法邀请"
  showType: ECST_FlyTip
}
rows {
  id: "-10600166"
  zh_CN: "对方已离线，无法邀请"
  showType: ECST_FlyTip
}
rows {
  id: "-10600167"
  zh_CN: "已经有官方联名作者了"
}
rows {
  id: "-10600168"
  zh_CN: "发布的地图page是错误的"
}
rows {
  id: "-10600169"
  zh_CN: "AI生成参考图信用分检查不通过"
}
rows {
  id: "-10600170"
  zh_CN: "地图发布上限"
}
rows {
  id: "-10600171"
  zh_CN: "不是主创作者"
}
rows {
  id: "-10600172"
  zh_CN: "下架联名地图失败"
}
rows {
  id: "-10600173"
  zh_CN: "联合共创者发布已上线"
}
rows {
  id: "-10600174"
  zh_CN: "我的模组数量已到达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10600175"
  zh_CN: "可发布的社区模组数量已到达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10600176"
  zh_CN: "已经处于下架状态"
}
rows {
  id: "-10600177"
  zh_CN: "回收站存储数量超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10600178"
  zh_CN: "收藏失败，收藏夹已到达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10600179"
  zh_CN: "申请超频"
}
rows {
  id: "-10600180"
  zh_CN: "IDIP删除的话题不存在"
}
rows {
  id: "-10600181"
  zh_CN: "待审核状态下地图无法进行点赞"
  showType: ECST_FlyTip
}
rows {
  id: "-10600182"
  zh_CN: "待审核状态下地图无法进行收藏"
  showType: ECST_FlyTip
}
rows {
  id: "-10600183"
  zh_CN: "AI生成语音信用分检查不通过"
}
rows {
  id: "-10600184"
  zh_CN: "ai语音生成失败"
}
rows {
  id: "-10600185"
  zh_CN: "ai视频动捕生成失败"
}
rows {
  id: "-10600186"
  zh_CN: "AI生成视频动捕信用分检查不通过"
}
rows {
  id: "-10600187"
  zh_CN: "ai对话生成失败"
}
rows {
  id: "-10600188"
  zh_CN: "搜索前置异常"
}
rows {
  id: "-10600189"
  zh_CN: "ugc管理端配置错误"
}
rows {
  id: "-10600190"
  zh_CN: "管理端数据错误"
}
rows {
  id: "-10600191"
  zh_CN: "地图处于锁定状态,无法操作"
  showType: ECST_FlyTip
}
rows {
  id: "-10600192"
  zh_CN: "拉取Ugc管理端配置错误"
}
rows {
  id: "-10600193"
  zh_CN: "Ugc管理端配置不存在"
}
rows {
  id: "-10600194"
  zh_CN: "地图引用的模组数过多"
}
rows {
  id: "-10600195"
  zh_CN: "UgcSvr Rpc失败"
}
rows {
  id: "-10600196"
  zh_CN: "资源首页推荐获取失败"
}
rows {
  id: "-10600197"
  zh_CN: "资源类型错误"
}
rows {
  id: "-10600198"
  zh_CN: "资源不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10600199"
  zh_CN: "UgcResBrief 表操作失败"
}
rows {
  id: "-10600200"
  zh_CN: "获取资源列表失败"
}
rows {
  id: "-10600201"
  zh_CN: "资源状态异常"
}
rows {
  id: "-10600202"
  zh_CN: "获取资源详情失败"
}
rows {
  id: "-10600203"
  zh_CN: "资源UgcPublish 表操作失败"
}
rows {
  id: "-10600204"
  zh_CN: "资源错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10600205"
  zh_CN: "获取资源背包db失败"
}
rows {
  id: "-10600206"
  zh_CN: "拉取Ugc管理端配置错误"
}
rows {
  id: "-10600207"
  zh_CN: "UgcPlatSvr Rpc失败"
}
rows {
  id: "-10600208"
  zh_CN: "相关存档数据获取失败"
}
rows {
  id: "-10600209"
  zh_CN: "该资源类型的资源库已到达上限，无法继续添加资源"
  showType: ECST_FlyTip
}
rows {
  id: "-10600210"
  zh_CN: "删除失败，同时删除的资源数量过多"
  showType: ECST_FlyTip
}
rows {
  id: "-10600211"
  zh_CN: "资源重复添加，添加失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10600212"
  zh_CN: "AI视频动捕没有开放"
}
rows {
  id: "-10600213"
  zh_CN: "AI语音生成没有开放"
}
rows {
  id: "-10600214"
  zh_CN: "NPC对话生成没有开放"
}
rows {
  id: "-10600215"
  zh_CN: "不允许添加温暖数据"
}
rows {
  id: "-10600216"
  zh_CN: "为定义温暖数据操作"
}
rows {
  id: "-10600217"
  zh_CN: "模组列表Rpc异常"
}
rows {
  id: "-10600218"
  zh_CN: "地图保存Rpc异常"
}
rows {
  id: "-10600219"
  zh_CN: "组合功能版本限制"
}
rows {
  id: "-10600220"
  zh_CN: "资源已下架，无法加入资源库"
  showType: ECST_FlyTip
}
rows {
  id: "-10600221"
  zh_CN: "地图标签无效"
}
rows {
  id: "-10600222"
  zh_CN: "修改地图基础信息失败"
}
rows {
  id: "-10600223"
  zh_CN: "无结果！"
  showType: ECST_FlyTip
}
rows {
  id: "-10600225"
  zh_CN: "无结果！"
  showType: ECST_FlyTip
}
rows {
  id: "-10600255"
  zh_CN: "人物数据不存在"
}
rows {
  id: "-10602336"
  zh_CN: "检测ugc商品无效"
}
rows {
  id: "-10602340"
  zh_CN: "检测ugc商品id无效"
}
rows {
  id: "-10602341"
  zh_CN: "ugc地图快捷键参数错误"
}
rows {
  id: "-10602347"
  zh_CN: "缩略图达到上限"
}
rows {
  id: "-10700001"
  zh_CN: "没有找到星家园"
  showType: ECST_FlyTip
}
rows {
  id: "-10700002"
  zh_CN: "没有找到摇钱树"
  showType: ECST_FlyTip
}
rows {
  id: "-10700003"
  zh_CN: "发生未知错误（10700003）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700004"
  zh_CN: "没有摇树次数了呢"
  showType: ECST_FlyTip
}
rows {
  id: "-10700005"
  zh_CN: "没有浇水次数了呢"
  showType: ECST_FlyTip
}
rows {
  id: "-10700006"
  zh_CN: "发生未知错误（10700006）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700007"
  zh_CN: "这次摇树需要的花费不够了哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700008"
  zh_CN: "发生未知错误（10700008）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700009"
  zh_CN: "不能摇其他星宝放置的摇钱树哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700010"
  zh_CN: "请不要重复点赞哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700011"
  zh_CN: "您发布了超出拥有数量的家具"
  showType: ECST_FlyTip
}
rows {
  id: "-10700012"
  zh_CN: "发生未知错误（10700012）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700013"
  zh_CN: "您没有足够的梦之叶或乐之叶用于购买了哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700014"
  zh_CN: "星家园等级不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10700015"
  zh_CN: "美观度没有达到要求哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700016"
  zh_CN: "您摆出了超过数量上限的家具哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700017"
  zh_CN: "放置了超过数量限制的摇钱树哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700018"
  zh_CN: "这个元件还没有解锁哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700019"
  zh_CN: "星家园关闭中，当前无法进入哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700020"
  zh_CN: "星家园互访功能未开放，敬请期待"
  showType: ECST_FlyTip
}
rows {
  id: "-10700021"
  zh_CN: "摇钱树竟然什么都没有掉落！"
  showType: ECST_FlyTip
}
rows {
  id: "-10700022"
  zh_CN: "掉落不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10700023"
  zh_CN: "星家园被屏蔽"
  showType: ECST_FlyTip
}
rows {
  id: "-10700024"
  zh_CN: "星家园被下架"
  showType: ECST_FlyTip
}
rows {
  id: "-10700025"
  zh_CN: "这个星家园的访客已满，请稍后再试试看吧"
  showType: ECST_FlyTip
}
rows {
  id: "-10700026"
  zh_CN: "这里没有位子了哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700027"
  zh_CN: "星家园发布过于频繁，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-10700028"
  zh_CN: "发生未知错误（10700028）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700029"
  zh_CN: "错误的客户端版本号"
  showType: ECST_FlyTip
}
rows {
  id: "-10700030"
  zh_CN: "版本错误，无法进入星家园"
  showType: ECST_FlyTip
}
rows {
  id: "-10700031"
  zh_CN: "发生未知错误（10700031）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700032"
  zh_CN: "发生未知错误（10700032）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700033"
  zh_CN: "未找到访客"
  showType: ECST_FlyTip
}
rows {
  id: "-10700034"
  zh_CN: "您正处于匹配状态，暂时无法进入星家园"
  showType: ECST_FlyTip
}
rows {
  id: "-10700035"
  zh_CN: "查询玩家星家园失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10700036"
  zh_CN: "对方小窝版本不匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-10700037"
  zh_CN: "被邀请人版本不匹配，暂时无法邀请"
  showType: ECST_FlyTip
}
rows {
  id: "-10700038"
  zh_CN: "你被这位星宝屏蔽，无法进入他的家园哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700039"
  zh_CN: "你屏蔽了这位星宝，无法邀请他哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700040"
  zh_CN: "参数错误（10700040）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700041"
  zh_CN: "您的版本降低了，请升级后再尝试进入家园"
  showType: ECST_FlyTip
}
rows {
  id: "-10700042"
  zh_CN: "您的家园版本较低，请更新后再尝试访问"
  showType: ECST_FlyTip
}
rows {
  id: "-10700043"
  zh_CN: "发生错误，创建家园失败（10700043）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700044"
  zh_CN: "版本号不匹配，无法进入家园（10700044）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700045"
  zh_CN: "发生错误，创建家园失败（10700045）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700046"
  zh_CN: "发生错误，发布家园失败（10700046）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700047"
  zh_CN: "样板间分配失败"
}
rows {
  id: "-10700048"
  zh_CN: "发生错误，无法进入家园（10700048）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700049"
  zh_CN: "发生错误，无法进入家园（10700049）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700050"
  zh_CN: "请不要频繁操作家园方案哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700051"
  zh_CN: "下发元件失败，无法创建家园"
  showType: ECST_FlyTip
}
rows {
  id: "-10700052"
  zh_CN: "下发元件失败（10700052）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700053"
  zh_CN: "背包空间不足，下发元件失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10700054"
  zh_CN: "下发元件失败（10700054）"
  showType: ECST_FlyTip
}
rows {
  id: "-10700055"
  zh_CN: "您创建的家园方案数量太多啦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700056"
  zh_CN: "操作的小窝方案ID不存在"
}
rows {
  id: "-10700057"
  zh_CN: "这位星宝的家园版本较低，您暂时无法访问哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10700058"
  zh_CN: "作物不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10700059"
  zh_CN: "配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10700060"
  zh_CN: "作物未成熟"
  showType: ECST_FlyTip
}
rows {
  id: "-10700061"
  zh_CN: "作物未干涸"
  showType: ECST_FlyTip
}
rows {
  id: "-10700062"
  zh_CN: "作物已成熟"
  showType: ECST_FlyTip
}
rows {
  id: "-10700063"
  zh_CN: "不在家园里"
  showType: ECST_FlyTip
}
rows {
  id: "-10700064"
  zh_CN: "家园已存在，不能重复创建"
}
rows {
  id: "-10700065"
  zh_CN: "家园找不到红包"
}
rows {
  id: "-10700066"
  zh_CN: "家园创建红包失败"
}
rows {
  id: "-10700067"
  zh_CN: "家园打开红包失败"
}
rows {
  id: "-10700068"
  zh_CN: "农作物不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10700069"
  zh_CN: "种植配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-10700070"
  zh_CN: "农作物未成熟"
  showType: ECST_FlyTip
}
rows {
  id: "-10700071"
  zh_CN: "农作物未干涸"
  showType: ECST_FlyTip
}
rows {
  id: "-10700072"
  zh_CN: "农作物已成熟"
  showType: ECST_FlyTip
}
rows {
  id: "-10700073"
  zh_CN: "不在家园里"
  showType: ECST_FlyTip
}
rows {
  id: "-10700074"
  zh_CN: "达到农作物总数上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10700075"
  zh_CN: "图鉴任务未达到完成要求"
}
rows {
  id: "-10700076"
  zh_CN: "图鉴奖励已领取"
}
rows {
  id: "-10700077"
  zh_CN: "农作物数量不足"
  showType: ECST_FlyTip
}
rows {
  id: "-10700078"
  zh_CN: "农作物未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-10700079"
  zh_CN: "禁言导致不能留言"
}
rows {
  id: "-10700080"
  zh_CN: "玩家不在自己家"
}
rows {
  id: "-10700081"
  zh_CN: "家园欢迎词功能未开启"
}
rows {
  id: "-10700082"
  zh_CN: "达到欢迎词长度限制"
}
rows {
  id: "-10700083"
  zh_CN: "欢迎词禁止设置"
}
rows {
  id: "-10700084"
  zh_CN: "家园欢迎词功能未开启"
}
rows {
  id: "-10700085"
  zh_CN: "达到留言内容长度限制"
}
rows {
  id: "-10700086"
  zh_CN: "留言发送失败"
}
rows {
  id: "-10700087"
  zh_CN: "留言回复发送失败"
}
rows {
  id: "-10700088"
  zh_CN: "留言找不到"
}
rows {
  id: "-10700089"
  zh_CN: "达到留言每次发送上限"
}
rows {
  id: "-10700090"
  zh_CN: "留言回复找不到"
}
rows {
  id: "-10700091"
  zh_CN: "留言精选失败"
}
rows {
  id: "-10700092"
  zh_CN: "取消留言精选失败"
}
rows {
  id: "-10700093"
  zh_CN: "留言删除失败"
}
rows {
  id: "-10700094"
  zh_CN: "留言回复删除失败"
}
rows {
  id: "-10700095"
  zh_CN: "留言精选达到上限"
}
rows {
  id: "-10700096"
  zh_CN: "果实数量不足"
  showType: ECST_FlyTip
}
rows {
  id: "-10800001"
  zh_CN: "找不到样板间"
}
rows {
  id: "-10800002"
  zh_CN: "您和对方的版本不一致，无法前往"
  showType: ECST_FlyTip
}
rows {
  id: "-10800003"
  zh_CN: "正在房间组队中，不能进入家园图纸试玩"
  showType: ECST_FlyTip
}
rows {
  id: "-10800004"
  zh_CN: "这位星宝开启了【禁止陌生人拜访星家园】权限哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-11030001"
  zh_CN: "照片已存在"
}
rows {
  id: "-11030002"
  zh_CN: "照片数量已达上限"
}
rows {
  id: "-11030003"
  zh_CN: "照片不存在"
}
rows {
  id: "-20000001"
  zh_CN: "活动数据不存在"
}
rows {
  id: "-20000002"
  zh_CN: "活动配置不存在"
}
rows {
  id: "-20000003"
  zh_CN: "不满足两江条件"
}
rows {
  id: "-20000004"
  zh_CN: "活动事件类型未找到"
}
rows {
  id: "-20010001"
  zh_CN: "队伍出错"
  showType: ECST_FlyTip
}
rows {
  id: "-20010002"
  zh_CN: "只有队长才能进行该操作"
  showType: ECST_FlyTip
}
rows {
  id: "-20010003"
  zh_CN: "配置异常"
  showType: ECST_FlyTip
}
rows {
  id: "-20010004"
  zh_CN: "队伍不一致"
  showType: ECST_FlyTip
}
rows {
  id: "-20010005"
  zh_CN: "队伍创建失败"
}
rows {
  id: "-20010006"
  zh_CN: "队伍创建失败 因为已经存在"
}
rows {
  id: "-20010007"
  zh_CN: "加入房间失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010008"
  zh_CN: "队伍人数已满"
  showType: ECST_FlyTip
}
rows {
  id: "-20010009"
  zh_CN: "退出失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010010"
  zh_CN: "退出队伍失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010011"
  zh_CN: "退出队伍失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010012"
  zh_CN: "准备异常"
  showType: ECST_FlyTip
}
rows {
  id: "-20010013"
  zh_CN: "重复准备"
  showType: ECST_FlyTip
}
rows {
  id: "-20010014"
  zh_CN: "房间未准备"
  showType: ECST_FlyTip
}
rows {
  id: "-20010015"
  zh_CN: "队伍已解散"
  showType: ECST_FlyTip
}
rows {
  id: "-20010016"
  zh_CN: "队伍成员不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20010017"
  zh_CN: "邀请过期"
  showType: ECST_FlyTip
}
rows {
  id: "-20010018"
  zh_CN: "邀请太过频繁"
  showType: ECST_FlyTip
}
rows {
  id: "-20010019"
  zh_CN: "改变模式 条件不满足"
  showType: ECST_FlyTip
}
rows {
  id: "-20010020"
  zh_CN: "分配队伍失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010021"
  zh_CN: "接受邀请失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010022"
  zh_CN: "邀请失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010023"
  zh_CN: "成员离线失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010024"
  zh_CN: "成员上线失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010025"
  zh_CN: "更改模式失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010026"
  zh_CN: "邀请的人超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20010027"
  zh_CN: "超时队列添加失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20010028"
  zh_CN: "加入失败，邀请不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20010029"
  zh_CN: "db操作失败，版本号失效"
}
rows {
  id: "-20010030"
  zh_CN: "已加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20010031"
  zh_CN: "匹配中，不允许移除队员"
  showType: ECST_FlyTip
}
rows {
  id: "-20010032"
  zh_CN: "比赛中，不允许移除队员"
  showType: ECST_FlyTip
}
rows {
  id: "-20010033"
  zh_CN: "匹配中，无法退出"
  showType: ECST_FlyTip
}
rows {
  id: "-20010034"
  zh_CN: "比赛中，无法退出"
  showType: ECST_FlyTip
}
rows {
  id: "-20010035"
  zh_CN: "匹配中，无法加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20010036"
  zh_CN: "比赛中，无法加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20010037"
  zh_CN: "服务器异常(-20010037)"
  showType: ECST_FlyTip
}
rows {
  id: "-20010038"
  zh_CN: "服务器异常(-20010038)"
  showType: ECST_FlyTip
}
rows {
  id: "-20010039"
  zh_CN: "服务器异常(-20010039)"
  showType: ECST_FlyTip
}
rows {
  id: "-20010040"
  zh_CN: "玩家不在线"
  showType: ECST_FlyTip
}
rows {
  id: "-20010041"
  zh_CN: "维度信息列表为空"
}
rows {
  id: "-20010042"
  zh_CN: "用户离线超时"
  showType: ECST_FlyTip
}
rows {
  id: "-20010043"
  zh_CN: "匹配中无法改变状态"
  showType: ECST_FlyTip
}
rows {
  id: "-20010044"
  zh_CN: "比赛中无法改变状态"
  showType: ECST_FlyTip
}
rows {
  id: "-20010045"
  zh_CN: "当前状态不能开始匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-20010046"
  zh_CN: "还在求邀请冷却时间中，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-20010047"
  zh_CN: "创建房间数据库返回失败"
}
rows {
  id: "-20010048"
  zh_CN: "操作重试"
}
rows {
  id: "-20010049"
  zh_CN: "重试达到上限次数"
}
rows {
  id: "-20010050"
  zh_CN: "有队友在比赛中，不允许更改模式"
  showType: ECST_FlyTip
}
rows {
  id: "-20010051"
  zh_CN: "因状态不对导致的同意入队申请失败"
}
rows {
  id: "-20010052"
  zh_CN: "系统繁忙,请稍后再试"
}
rows {
  id: "-20010053"
  zh_CN: "操作失败, 请稍后重试"
}
rows {
  id: "-20010054"
  zh_CN: "room server锁定gamesvr的player失败, 服务器内部错误"
}
rows {
  id: "-20010055"
  zh_CN: "用户已经加入其他队伍"
  showType: ECST_FlyTip
}
rows {
  id: "-20010056"
  zh_CN: "没有当前难度的歌曲解锁"
}
rows {
  id: "-20010057"
  zh_CN: "该选手已经加入一个队伍"
}
rows {
  id: "-20010058"
  zh_CN: "该用户已经加入一个队伍"
}
rows {
  id: "-20010059"
  zh_CN: "-20010059"
}
rows {
  id: "-20010060"
  zh_CN: "匹配状态不允许更改模式"
  showType: ECST_FlyTip
}
rows {
  id: "-20010061"
  zh_CN: "队员还在比赛中，没法开始新的比赛噢"
  showType: ECST_FlyTip
}
rows {
  id: "-20010062"
  zh_CN: "客户端版本不一致，请更新"
  showType: ECST_ModelDialog
  rpcType: 1
}
rows {
  id: "-20010063"
  zh_CN: "客户端版本不一致，请更新"
  showType: ECST_ModelDialog
  rpcType: 2
}
rows {
  id: "-20010064"
  zh_CN: "队伍成员客户端版本不一致，请更新"
  showType: ECST_ModelDialog
}
rows {
  id: "-20010065"
  zh_CN: "请先退出当前队伍"
  showType: ECST_FlyTip
}
rows {
  id: "-20010066"
  zh_CN: "当前模式已关闭，请先切换模式"
  showType: ECST_FlyTip
}
rows {
  id: "-20010067"
  zh_CN: "通过聊天加入组队modeid已经改变"
}
rows {
  id: "-20010068"
  zh_CN: "通过聊天加入组队邀请人已不再是队长已经改变"
}
rows {
  id: "-20010069"
  zh_CN: "当前模式未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20010070"
  zh_CN: "当前模式已关闭，请先切换模式"
  showType: ECST_FlyTip
}
rows {
  id: "-20010071"
  zh_CN: "已经在队伍中, 无法加入其他队伍"
}
rows {
  id: "-20010072"
  zh_CN: "已经在对战中, 无法加入其他队伍"
}
rows {
  id: "-20010073"
  zh_CN: "邀请人已不再是队长"
}
rows {
  id: "-20010074"
  zh_CN: "被踢出队伍用户不允许再加入"
}
rows {
  id: "-20010075"
  zh_CN: "获取房间成员的场景id失败"
}
rows {
  id: "-20010076"
  zh_CN: "房间队长移交失败"
}
rows {
  id: "-20010077"
  zh_CN: "匹配已失效"
}
rows {
  id: "-20010078"
  zh_CN: "没有可用的招募供加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20010079"
  zh_CN: "该模式暂未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20010080"
  zh_CN: "队伍人数超过该模式最大组队人数"
  showType: ECST_FlyTip
}
rows {
  id: "-20010081"
  zh_CN: "当前人数大于该玩法，请选择其他玩法模式"
  showType: ECST_FlyTip
}
rows {
  id: "-20010082"
  zh_CN: "有玩家未解锁该模式"
  showType: ECST_FlyTip
}
rows {
  id: "-20010083"
  zh_CN: "等待队员确认时，不允许修改模式"
}
rows {
  id: "-20010084"
  zh_CN: "请检查所有成员都不能处于离线状态"
  showType: ECST_FlyTip
}
rows {
  id: "-20010085"
  zh_CN: "房间状态异常，无法执行操作"
}
rows {
  id: "-20010086"
  zh_CN: "无法退出当前队伍"
  showType: ECST_FlyTip
}
rows {
  id: "-20010087"
  zh_CN: "当前房间状态无法执行该操作"
}
rows {
  id: "-20010088"
  zh_CN: "房间密码错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20010089"
  zh_CN: "指定模式无法使用"
}
rows {
  id: "-20010090"
  zh_CN: "房间已过期"
}
rows {
  id: "-20010091"
  zh_CN: "邀请不存在"
}
rows {
  id: "-20010092"
  zh_CN: "玩家正在对局中"
}
rows {
  id: "-20010093"
  zh_CN: "玩家暂时无法接受邀请"
  showType: ECST_FlyTip
}
rows {
  id: "-20010094"
  zh_CN: "目标队伍已开启对局，无法加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20010095"
  zh_CN: "招募信息已过期"
  showType: ECST_FlyTip
}
rows {
  id: "-20010096"
  zh_CN: "该座位已有其他玩家"
  showType: ECST_FlyTip
}
rows {
  id: "-20010097"
  zh_CN: "座位不存在"
}
rows {
  id: "-20010098"
  zh_CN: "未达到该招募的段位限制"
  showType: ECST_FlyTip
}
rows {
  id: "-20010099"
  zh_CN: "客户端版本过低，请更新"
  showType: ECST_ModelDialog
  rpcType: 2
}
rows {
  id: "-20010100"
  zh_CN: "匹配开始失败, matchid不匹配"
}
rows {
  id: "-20010101"
  zh_CN: "等待队员确认匹配中，请勿移交队长"
  showType: ECST_FlyTip
}
rows {
  id: "-20010102"
  zh_CN: "匹配确认中，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-20010103"
  zh_CN: "正在匹配中，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-20010104"
  zh_CN: "仍有队员在对局中，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-20010105"
  zh_CN: "当前状态无法解散队伍"
  showType: ECST_FlyTip
}
rows {
  id: "-20010106"
  zh_CN: "有队友仍在对局中，无法解散队伍"
  showType: ECST_FlyTip
}
rows {
  id: "-20010107"
  zh_CN: "队员客户端版本过低，无法匹配"
  showType: ECST_ModelDialog
}
rows {
  id: "-20010108"
  zh_CN: "查询的房间不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20010109"
  zh_CN: "房间成员是机器人"
}
rows {
  id: "-20010110"
  zh_CN: "不能把队长移交给机器人"
}
rows {
  id: "-20010111"
  zh_CN: "房间有密码无法发布招募"
  showType: ECST_FlyTip
}
rows {
  id: "-20010112"
  zh_CN: "匹配确认中，稍后再转让队长吧"
  showType: ECST_FlyTip
}
rows {
  id: "-20010113"
  zh_CN: "匹配确认中，无法移除队员"
  showType: ECST_FlyTip
}
rows {
  id: "-20010114"
  zh_CN: "正在匹配中，无法移除队员"
  showType: ECST_FlyTip
}
rows {
  id: "-20010115"
  zh_CN: "请先取消匹配再解散"
}
rows {
  id: "-20010116"
  zh_CN: "人数已达招募模式的上限，无法招募"
  showType: ECST_FlyTip
}
rows {
  id: "-20010117"
  zh_CN: "当前玩法与选择模式不匹配，无法招募"
  showType: ECST_FlyTip
}
rows {
  id: "-20010118"
  zh_CN: "当前人数已超过目标玩法上限"
}
rows {
  id: "-20010119"
  zh_CN: "没有适合的房间供加入，请稍后重试"
  showType: ECST_FlyTip
}
rows {
  id: "-20010120"
  zh_CN: "已进入对局，无法退出"
  showType: ECST_FlyTip
}
rows {
  id: "-20010121"
  zh_CN: "无法以目标地图创建房间"
}
rows {
  id: "-20010122"
  zh_CN: "无法切换到目标地图"
}
rows {
  id: "-20010123"
  zh_CN: "当前人数已超过目标地图上限"
}
rows {
  id: "-20010124"
  zh_CN: "匹配时没有IDC延迟数据"
}
rows {
  id: "-20010125"
  zh_CN: "交换申请已失效"
  showType: ECST_FlyTip
}
rows {
  id: "-20010126"
  zh_CN: "有玩家未下载该模式"
  showType: ECST_FlyTip
}
rows {
  id: "-20010127"
  zh_CN: "已开局，请勿重复点击"
  showType: ECST_FlyTip
}
rows {
  id: "-20010128"
  zh_CN: "目标玩法版本不兼容，请升级客户端后体验"
  showType: ECST_FlyTip
}
rows {
  id: "-20010129"
  zh_CN: "当前房间不在开启匹配过程中"
}
rows {
  id: "-20010130"
  zh_CN: "信誉分过低，无法匹配"
}
rows {
  id: "-20010131"
  zh_CN: "道具不足不允许选择身份或者阵营"
}
rows {
  id: "-20010200"
  zh_CN: "roomsvr内部错误 20010200 - 20016999;"
}
rows {
  id: "-20010201"
  zh_CN: "房间DB操作失败"
}
rows {
  id: "-20010202"
  zh_CN: "已加入队伍"
  showType: ECST_FlyTip
}
rows {
  id: "-20010203"
  zh_CN: "目标房间空间不足，无法加入"
}
rows {
  id: "-20010204"
  zh_CN: "加入已取消"
}
rows {
  id: "-20010205"
  zh_CN: "等待其他玩家确认中，请稍候"
}
rows {
  id: "-20010206"
  zh_CN: "已确认，请勿重复操作"
}
rows {
  id: "-20010207"
  zh_CN: "目标房间不允许内部批量加入"
}
rows {
  id: "-20010208"
  zh_CN: "已在房间中"
  showType: ECST_FlyTip
}
rows {
  id: "-20010209"
  zh_CN: "无效地图"
}
rows {
  id: "-20010210"
  zh_CN: "地图已下架"
}
rows {
  id: "-20010211"
  zh_CN: "有玩家未确认"
}
rows {
  id: "-20010212"
  zh_CN: "有玩家未准备"
}
rows {
  id: "-20010213"
  zh_CN: "对方正在换位中，请稍候"
  showType: ECST_FlyTip
}
rows {
  id: "-20010214"
  zh_CN: "在房间中无法参与匹配"
}
rows {
  id: "-20010215"
}
rows {
  id: "-20010216"
  zh_CN: "有成员玩法模式已被封禁"
}
rows {
  id: "-20010217"
  zh_CN: "队伍退出但仅队长自己离开"
}
rows {
  id: "-20010218"
  zh_CN: "队伍只有一个人"
}
rows {
  id: "-20010219"
}
rows {
  id: "-20010220"
  zh_CN: "玩法异常无法开局"
}
rows {
  id: "-20010221"
  zh_CN: "机器人无法加入观战位"
  showType: ECST_FlyTip
}
rows {
  id: "-20010222"
  zh_CN: "房间角色类型不合法"
}
rows {
  id: "-20010223"
  zh_CN: "房间位置不合法"
}
rows {
  id: "-20010224"
  zh_CN: "房间ob功能关闭"
}
rows {
  id: "-20010225"
  zh_CN: "成员离线"
}
rows {
  id: "-20010226"
  zh_CN: "狼人杀玩家阵营或者身份组合不合法"
}
rows {
  id: "-20010227"
  zh_CN: "推荐最近对局好友"
}
rows {
  id: "-20017000"
  zh_CN: "房间模块对外展示的错误码 20017000 - 20019999;"
}
rows {
  id: "-20017001"
  zh_CN: "客户端组队数据不匹配"
}
rows {
  id: "-20017002"
  zh_CN: "目标玩家不在线"
  showType: ECST_FlyTip
}
rows {
  id: "-20017003"
  zh_CN: "当前队伍人数超目标玩法人数上限"
}
rows {
  id: "-20017004"
  zh_CN: "邀请的玩家已加入"
}
rows {
  id: "-20017005"
  zh_CN: "目标玩家已被封禁"
}
rows {
  id: "-20017006"
  zh_CN: "等待队员确认中，请稍候"
}
rows {
  id: "-20017007"
  zh_CN: "您已在一个房间内"
}
rows {
  id: "-20017008"
  zh_CN: "该房间与您当前登录账号所在平台不一致"
}
rows {
  id: "-20017009"
  zh_CN: "您已经在对局中，无法扫码加入"
}
rows {
  id: "-20017010"
  zh_CN: "您已经在匹配，无法扫码加入"
}
rows {
  id: "-20017011"
  zh_CN: "该队伍与您当前登录账号所在平台不一致"
}
rows {
  id: "-20017012"
  zh_CN: "星宝暂时不接受邀请喔～"
  showType: ECST_FlyTip
}
rows {
  id: "-20017013"
  zh_CN: "无法发布招募，请稍后重试"
  showType: ECST_FlyTip
}
rows {
  id: "-20017014"
  zh_CN: "您已被禁止组队"
  showType: ECST_FlyTip
}
rows {
  id: "-20017015"
  zh_CN: "目标玩家被禁止组队"
  showType: ECST_FlyTip
}
rows {
  id: "-20017016"
  zh_CN: "其他玩家仍在对局中，请稍候"
  showType: ECST_FlyTip
}
rows {
  id: "-20017018"
  zh_CN: "人数不足无法开局"
  showType: ECST_FlyTip
}
rows {
  id: "-20017019"
  zh_CN: "<Orange28>您的游戏版本较低</>，暂时无法进行该操作，请<Orange28>重启游戏并更新</>后重试"
  showType: ECST_ModelDialog
}
rows {
  id: "-20017020"
  zh_CN: "<Orange28>对方游戏版本较低</>，暂时无法进行该操作\n（可<Orange28>提醒对方更新版本</>后重试）"
  showType: ECST_ModelDialog
}
rows {
  id: "-20017021"
  zh_CN: "同游匹配失败，请稍后重试"
  showType: ECST_FlyTip
}
rows {
  id: "-20017022"
  zh_CN: "当前同游匹配仅支持单人，请退出组队后重试"
  showType: ECST_FlyTip
}
rows {
  id: "-20017023"
  zh_CN: "目标玩法暂时无法观战, 观战玩家请先调整位置"
}
rows {
  id: "-20017024"
  zh_CN: "由于您的腾讯游戏信用＜{350}分，存在异常或不良游戏行为，无法创建房间。"
}
rows {
  id: "-20017025"
  zh_CN: "玩法当前时段无法游玩"
  showType: ECST_FlyTip
}
rows {
  id: "-20017026"
  zh_CN: "即将开始对局，无法执行该操作"
}
rows {
  id: "-20017027"
  zh_CN: "您当前的版本过低，将无法获得积分，请更新客户端"
}
rows {
  id: "-20017028"
  zh_CN: "房间已解散"
  showType: ECST_FlyTip
}
rows {
  id: "-20017029"
  zh_CN: "请求已失效"
  showType: ECST_FlyTip
}
rows {
  id: "-20017030"
  zh_CN: "对方拒绝了你的加入请求"
  showType: ECST_FlyTip
}
rows {
  id: "-20017031"
  zh_CN: "人数已满"
  showType: ECST_FlyTip
}
rows {
  id: "-20017032"
  zh_CN: "暂时无法加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20017033"
  zh_CN: "对方房间不支持加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20017034"
  zh_CN: "对方处于样板间无法加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20017035"
  zh_CN: "对方处于农场无法加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20017036"
  zh_CN: "这位星宝开启了【禁止陌生人组队邀请】权限哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-20017037"
  zh_CN: "该房间仅房主邀请可进"
  showType: ECST_FlyTip
}
rows {
  id: "-20017038"
  zh_CN: "当前房间座位不足，对方无法加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20017039"
  zh_CN: "正在匹配中，请取消后再尝试"
}
rows {
  id: "-20017066"
  zh_CN: "只有房主才能进行该操作"
  showType: ECST_FlyTip
}
rows {
  id: "-20020001"
  zh_CN: "服务器异常(-20020001)"
  showType: ECST_FlyTip
}
rows {
  id: "-20020002"
  zh_CN: "服务器异常(-20020002)"
  showType: ECST_FlyTip
}
rows {
  id: "-20020003"
  zh_CN: "服务器异常(-20020003)"
  showType: ECST_FlyTip
}
rows {
  id: "-20020004"
  zh_CN: "服务器异常(-20020004)"
  showType: ECST_FlyTip
}
rows {
  id: "-20020005"
  zh_CN: "关卡创建失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20020006"
  zh_CN: "服务器异常(-20020006)"
  showType: ECST_FlyTip
}
rows {
  id: "-20020007"
  zh_CN: "配置未找到"
  showType: ECST_FlyTip
}
rows {
  id: "-20020008"
  zh_CN: "用户不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20020009"
  zh_CN: "重新在battle上线失败"
}
rows {
  id: "-20020010"
  zh_CN: "结束对战出错"
}
rows {
  id: "-20020200"
  zh_CN: "服务器异常(-20020200)"
  showType: ECST_FlyTip
}
rows {
  id: "-20020201"
  zh_CN: "服务器异常(-20020201)"
  showType: ECST_FlyTip
}
rows {
  id: "-20020202"
  zh_CN: "服务器异常(-20020202)"
  showType: ECST_FlyTip
}
rows {
  id: "-20020203"
  zh_CN: "服务器异常(-20020203)"
  showType: ECST_FlyTip
}
rows {
  id: "-20020204"
  zh_CN: "player因已失败或已完成, 不再允许进行此操作"
}
rows {
  id: "-20020205"
  zh_CN: "player因已失败或已完成, 不再允许进行此操作"
}
rows {
  id: "-20020301"
  zh_CN: "歌曲结算配置不存在"
}
rows {
  id: "-20020402"
  zh_CN: "服务器异常(-20020402)"
  showType: ECST_FlyTip
}
rows {
  id: "-20020403"
  zh_CN: "比赛结束"
  showType: ECST_FlyTip
}
rows {
  id: "-20020404"
  zh_CN: "比赛未结束"
  showType: ECST_FlyTip
}
rows {
  id: "-20020405"
  zh_CN: "局内引导错误"
}
rows {
  id: "-20020406"
  zh_CN: "比赛还未结束"
}
rows {
  id: "-20020407"
  zh_CN: "不是帧同步比赛"
}
rows {
  id: "-20020408"
  zh_CN: "操作重试"
}
rows {
  id: "-20020409"
  zh_CN: "重试达到上限次数"
}
rows {
  id: "-20020410"
  zh_CN: "confirmBattle result非法"
}
rows {
  id: "-20020411"
  zh_CN: "退出战场RPC通知错误"
}
rows {
  id: "-20020412"
  zh_CN: "上报结束失败，歌曲未结束"
}
rows {
  id: "-20020413"
  zh_CN: "删除失败，战场已经被删除"
}
rows {
  id: "-20020415"
  zh_CN: "用户对战信息不存在"
}
rows {
  id: "-20020416"
  zh_CN: "该功能不再有效时间内"
}
rows {
  id: "-20020417"
  zh_CN: "表情配置不存在"
}
rows {
  id: "-20020418"
  zh_CN: "战斗还在继续中"
}
rows {
  id: "-20020419"
  zh_CN: "未达成解锁条件"
}
rows {
  id: "-20020420"
  zh_CN: "未找到下一轮是第几轮"
}
rows {
  id: "-20020421"
  zh_CN: "未找到下一轮可选关卡"
}
rows {
  id: "-20020422"
  zh_CN: "由于游戏内容更新，您无法进入之前的游戏对局。<ReconnectTime>{0}秒</>后自动确定。"
  showType: ECST_FlyTip
}
rows {
  id: "-20020423"
  zh_CN: "玩家对局已结束(已结算)"
}
rows {
  id: "-20020424"
  zh_CN: "加入的战局失败"
}
rows {
  id: "-20020425"
  zh_CN: "加入的战局人数超标"
}
rows {
  id: "-20020426"
  zh_CN: "加入战局搜索失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20020427"
  zh_CN: "加入对局检查失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20020430"
  zh_CN: "战斗单局正在迁移中"
}
rows {
  id: "-20020431"
  zh_CN: "ds正在拉起过程中"
}
rows {
  id: "-20020433"
  zh_CN: "无效的对局兼容组"
}
rows {
  id: "-20020434"
  zh_CN: "不允许匹配内要人"
}
rows {
  id: "-20020435"
  zh_CN: "请求间隔太短"
}
rows {
  id: "-20020436"
  zh_CN: "游戏模式不匹配"
}
rows {
  id: "-20020437"
  zh_CN: "中途拉人事件错误"
}
rows {
  id: "-20020438"
  zh_CN: "在补位中，无法加入"
  showType: ECST_FlyTip
}
rows {
  id: "-20020439"
  zh_CN: "中途加入失败"
}
rows {
  id: "-20020440"
  zh_CN: "玩家对局中不存在"
}
rows {
  id: "-20020441"
  zh_CN: "当前玩法过于火爆，请稍后重试"
  showType: ECST_FlyTip
}
rows {
  id: "-20030001"
  zh_CN: "匹配出错"
  showType: ECST_FlyTip
}
rows {
  id: "-20030002"
  zh_CN: "匹配重复"
  showType: ECST_FlyTip
}
rows {
  id: "-20030003"
  zh_CN: "匹配取消失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20030004"
  zh_CN: "匹配结果出错"
  showType: ECST_FlyTip
}
rows {
  id: "-20030005"
  zh_CN: "匹配后创建场景出错"
}
rows {
  id: "-20030006"
  zh_CN: "匹配队友出错"
}
rows {
  id: "-20030007"
  zh_CN: "没有匹配到合适的星宝，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-20030008"
  zh_CN: "取消匹配失败"
}
rows {
  id: "-20030009"
  zh_CN: "匹配过于火爆啦, 请稍等一会再来"
}
rows {
  id: "-20030010"
  zh_CN: "匹配系统维护中"
  showType: ECST_ModelDialog
}
rows {
  id: "-20030011"
  zh_CN: "返回匹配撮合结果创建房间失败"
}
rows {
  id: "-20030012"
  zh_CN: "匹配回填初始化出错"
}
rows {
  id: "-20030013"
  zh_CN: "匹配回填返回结果出错"
}
rows {
  id: "-20030014"
  zh_CN: "匹配后加入战场失败"
}
rows {
  id: "-20040001"
  zh_CN: "已获得该装扮"
  showType: ECST_FlyTip
}
rows {
  id: "-20040002"
  zh_CN: "还没有获得该装扮"
  showType: ECST_FlyTip
}
rows {
  id: "-20040003"
  zh_CN: "已经穿上这件装扮"
  showType: ECST_FlyTip
}
rows {
  id: "-20040004"
  zh_CN: "未穿戴"
}
rows {
  id: "-20050001"
  zh_CN: "无法给自己点赞"
}
rows {
  id: "-20050002"
  zh_CN: "今日已给该用户点过赞了"
  showType: ECST_FlyTip
}
rows {
  id: "-20050003"
  zh_CN: "点赞失败, 请稍后重试"
}
rows {
  id: "-20060001"
  zh_CN: "战局赛季与用户段位赛季不一致，赛季已重置"
}
rows {
  id: "-20060002"
  zh_CN: "战局赛季不是当前赛季或上一个赛季"
}
rows {
  id: "-20060003"
  zh_CN: "领取参数错误"
}
rows {
  id: "-20060004"
  zh_CN: "领取参数错误"
}
rows {
  id: "-20060005"
  zh_CN: "未达成领取条件"
}
rows {
  id: "-20060006"
  zh_CN: "为达成打开条件"
}
rows {
  id: "-20060007"
  zh_CN: "重复打开"
}
rows {
  id: "-20060008"
  zh_CN: "-20060008"
}
rows {
  id: "-20060009"
  zh_CN: "当前排位赛处于休赛期，不改变分数"
  showType: ECST_FlyTip
}
rows {
  id: "-20060010"
  zh_CN: "游戏版本过低，无法结算积分，请更新版本"
  showType: ECST_FlyTip
}
rows {
  id: "-20060011"
  zh_CN: "对局还未结束，段位分稍后结算"
}
rows {
  id: "-20070001"
  zh_CN: "类型不匹配"
}
rows {
  id: "-20070002"
  zh_CN: "参数错误"
}
rows {
  id: "-20070003"
  zh_CN: "平台错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20070004"
  zh_CN: "转账失败"
}
rows {
  id: "-20080001"
  zh_CN: "场景服出错"
}
rows {
  id: "-20080002"
  zh_CN: "场景不存在"
}
rows {
  id: "-20080003"
  zh_CN: "场景物件创建失败"
}
rows {
  id: "-20080004"
  zh_CN: "场景物件重复"
}
rows {
  id: "-20080005"
  zh_CN: "查找场景失败"
}
rows {
  id: "-20080006"
  zh_CN: "场景用户不存在"
}
rows {
  id: "-20080007"
  zh_CN: "场景用户已销毁"
}
rows {
  id: "-20080008"
  zh_CN: "场景管理指令执行失败"
}
rows {
  id: "-20080009"
  zh_CN: "场景服务初始化失败"
}
rows {
  id: "-20080010"
  zh_CN: "场景服务初始化上报失败"
}
rows {
  id: "-20080011"
  zh_CN: "场景地图参数错误"
}
rows {
  id: "-20080012"
  zh_CN: "场景创建失败"
}
rows {
  id: "-20080013"
  zh_CN: "获取sceneService失败"
}
rows {
  id: "-20080014"
  zh_CN: "地图配置不存在"
}
rows {
  id: "-20080015"
  zh_CN: "无效的场景"
}
rows {
  id: "-20080016"
  zh_CN: "上报数据失败"
}
rows {
  id: "-20080017"
  zh_CN: "退出scene失败"
}
rows {
  id: "-20080018"
  zh_CN: "找不到session"
}
rows {
  id: "-20080019"
  zh_CN: "处理心跳失败"
}
rows {
  id: "-20080020"
  zh_CN: "scene没有准备好"
}
rows {
  id: "-20080021"
  zh_CN: "已进入演唱会场景"
}
rows {
  id: "-20080022"
  zh_CN: "互动目标状态非invite"
}
rows {
  id: "-20080023"
  zh_CN: "互动动作激活失败"
}
rows {
  id: "-20080024"
  zh_CN: "互动动作激活失败"
}
rows {
  id: "-20080025"
  zh_CN: "互动失败，对方当前状态无法进行互动"
}
rows {
  id: "-20080026"
  zh_CN: "同步用户数据失败"
}
rows {
  id: "-20080027"
  zh_CN: "取消失败"
}
rows {
  id: "-20080028"
  zh_CN: "场景不在运行状态"
}
rows {
  id: "-20080029"
  zh_CN: "结束对局出错"
}
rows {
  id: "-20080030"
  zh_CN: "对方已下线"
}
rows {
  id: "-20080031"
  zh_CN: "无效的类型"
}
rows {
  id: "-20080032"
  zh_CN: "锁失败"
}
rows {
  id: "-20080033"
  zh_CN: "庆典场景已满"
}
rows {
  id: "-20080034"
  zh_CN: "状态错误"
}
rows {
  id: "-20080035"
  zh_CN: "庆典纪念币RPC通知错误"
}
rows {
  id: "-20080036"
  zh_CN: "未发现该用户"
}
rows {
  id: "-20080037"
  zh_CN: "用户在其他场景中"
}
rows {
  id: "-20080038"
  zh_CN: "互动体验ID错误"
}
rows {
  id: "-20080039"
  zh_CN: "重连场景失败"
}
rows {
  id: "-20080040"
  zh_CN: "尝试进入场景服失败"
}
rows {
  id: "-20080041"
  zh_CN: "战斗中"
}
rows {
  id: "-20080042"
  zh_CN: "没有查询到玩家结算信息"
}
rows {
  id: "-20090001"
  zh_CN: "战斗中"
}
rows {
  id: "-20090002"
  zh_CN: "玩家不在广场中，稍后再试试吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-20090003"
  zh_CN: "大厅未就绪"
}
rows {
  id: "-20090004"
  zh_CN: "大厅查找失败"
}
rows {
  id: "-20090005"
  zh_CN: "重复进入大厅"
}
rows {
  id: "-20090006"
  zh_CN: "广场玩家不存在"
}
rows {
  id: "-20090007"
  zh_CN: "广场配置不存在"
}
rows {
  id: "-20090008"
  zh_CN: "广场玩家错误状态"
}
rows {
  id: "-20090009"
  zh_CN: "广场销毁"
}
rows {
  id: "-20090010"
  zh_CN: "广场创建失败"
}
rows {
  id: "-20090011"
  zh_CN: "广场服出错"
}
rows {
  id: "-20090012"
  zh_CN: "广场人数已满，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-20090013"
  zh_CN: "查看DS状态失败"
}
rows {
  id: "-20090014"
  zh_CN: "大厅DS结束"
}
rows {
  id: "-20090015"
  zh_CN: "玩家已经不在星梦广场啦"
  showType: ECST_FlyTip
}
rows {
  id: "-20090016"
  zh_CN: "同步ds玩家失败"
}
rows {
  id: "-20090017"
  zh_CN: "玩家不在大厅聊天室"
}
rows {
  id: "-20090018"
  zh_CN: "玩家加入大厅聊天室失败"
}
rows {
  id: "-20090019"
  zh_CN: "玩家不在广场上，无法前往"
  showType: ECST_FlyTip
}
rows {
  id: "-20090020"
  zh_CN: "玩家大厅邀请失败"
}
rows {
  id: "-20090021"
  zh_CN: "暂无可以更换的广场"
  showType: ECST_FlyTip
}
rows {
  id: "-20090022"
  zh_CN: "这个玩家有点忙，等等再试试吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-20090023"
  zh_CN: "游戏版本异常"
  showType: ECST_FlyTip
}
rows {
  id: "-20090024"
  zh_CN: "<Orange28>对方游戏版本较低</>，暂时无法进行该操作\n（可<Orange28>提醒对方更新版本</>后重试）"
  showType: ECST_ModelDialog
}
rows {
  id: "-20090025"
  zh_CN: "<Orange28>您的游戏版本较低</>，暂时无法进行该操作，请<Orange28>重启游戏并更新</>后重试"
  showType: ECST_ModelDialog
}
rows {
  id: "-20090026"
  zh_CN: "大厅服已满"
}
rows {
  id: "-20090027"
  zh_CN: "客户端版本过低，请更新"
  showType: ECST_ModelDialog
  rpcType: 2
}
rows {
  id: "-20090028"
  zh_CN: "版本过低，请更新"
  showType: ECST_ModelDialog
  rpcType: 2
}
rows {
  id: "-20090029"
  zh_CN: "没有可以进入的广场"
}
rows {
  id: "-20090030"
  zh_CN: "玩家在大厅中"
}
rows {
  id: "-20090031"
  zh_CN: "玩家拒绝邀请"
}
rows {
  id: "-20090032"
  zh_CN: "大厅不是运行状态"
}
rows {
  id: "-20090033"
  zh_CN: "大厅保存数据库失败"
}
rows {
  id: "-20090034"
  zh_CN: "大厅保存数据库失败"
}
rows {
  id: "-20090035"
  zh_CN: "广场创建红包失败"
}
rows {
  id: "-20090036"
  zh_CN: "广场打开红包失败"
}
rows {
  id: "-20090037"
  zh_CN: "广场里有太多红包啦"
  showType: ECST_FlyTip
}
rows {
  id: "-20090038"
  zh_CN: "乐园维护中，请稍后再试一下吧～"
}
rows {
  id: "-20090039"
  zh_CN: "乐园维护中，请稍后再试一下吧～"
}
rows {
  id: "-20090040"
  zh_CN: "游戏版本过低，请升级版本后再试"
}
rows {
  id: "-20090041"
  zh_CN: "游戏版本过低，请升级版本后再试"
}
rows {
  id: "-20090042"
  zh_CN: "游戏版本过低，请升级版本后再试"
}
rows {
  id: "-20090043"
  zh_CN: "游戏版本过低，请升级版本后再试"
}
rows {
  id: "-20090044"
  zh_CN: "缺少必要的资源，请先完成资源下载再试"
}
rows {
  id: "-20090045"
  zh_CN: "当前界面无法切换广场"
}
rows {
  id: "-20090046"
  zh_CN: "该地图还未到开启时间哦~"
}
rows {
  id: "-20090047"
  zh_CN: "该地图已关闭哦~"
}
rows {
  id: "-20090048"
  zh_CN: "没有对应的DS版本"
}
rows {
  id: "-20090049"
  zh_CN: "找不到dsa"
}
rows {
  id: "-20090050"
  zh_CN: "创建ds失败"
}
rows {
  id: "-20100001"
  zh_CN: "已获得保底大奖"
}
rows {
  id: "-20100002"
  zh_CN: "无法抽取奖励组"
}
rows {
  id: "-20100003"
  zh_CN: "无法抽取奖励"
}
rows {
  id: "-20100004"
  zh_CN: "配置未找到"
}
rows {
  id: "-20100005"
  zh_CN: "该祈愿奖池抽取次数错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20100006"
  zh_CN: "该祈愿奖池折扣显示错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20100007"
  zh_CN: "本次祈愿消耗货币异常"
  showType: ECST_FlyTip
}
rows {
  id: "-20100008"
  zh_CN: "本次祈愿货币类型错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20100009"
  zh_CN: "本次祈愿货币数量错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20100010"
  zh_CN: "该祈愿奖池尚未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20100011"
  zh_CN: "已达到祈愿上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20100012"
  zh_CN: "无法进行祈愿"
}
rows {
  id: "-20100013"
  zh_CN: "本次祈愿次数超过祈愿上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20100014"
  zh_CN: "祈愿次数已上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20100015"
  zh_CN: "已有抽数为满足对应宝箱的要求"
}
rows {
  id: "-20100016"
  zh_CN: "没有该抽数的宝箱"
}
rows {
  id: "-20100017"
  zh_CN: "重复获取该抽数宝箱"
}
rows {
  id: "-20100018"
  zh_CN: "抽奖卡位重复"
}
rows {
  id: "-20100019"
  zh_CN: "抽奖抵用券ID错误"
}
rows {
  id: "-20100020"
  zh_CN: "抽奖抵用券数量错误"
}
rows {
  id: "-20100021"
  zh_CN: "没有多余的免费抽取"
}
rows {
  id: "-20100022"
  zh_CN: "免费次数达到获取上限"
}
rows {
  id: "-20100023"
  zh_CN: "达到助力上限"
}
rows {
  id: "-20100024"
  zh_CN: "免费抽取达到助力上限"
}
rows {
  id: "-20100025"
  zh_CN: "抽奖未使用打折"
}
rows {
  id: "-20100026"
  zh_CN: "抽奖购买拒绝自动兑换"
}
rows {
  id: "-20100027"
  zh_CN: "抽奖购买拒绝自动兑换ID错误"
}
rows {
  id: "-20100028"
  zh_CN: "抽奖卡位参数错误"
}
rows {
  id: "-20100029"
  zh_CN: "抽奖BI Url失败"
}
rows {
  id: "-20100030"
  zh_CN: "抽奖BI参数编码失败"
}
rows {
  id: "-20100031"
  zh_CN: "抽奖BI结果解码失败"
}
rows {
  id: "-20100032"
  zh_CN: "抽奖BI返回错误码"
}
rows {
  id: "-20100033"
  zh_CN: "抽奖BI HttpPost失败"
}
rows {
  id: "-20100034"
  zh_CN: "抽奖BI结果非法"
}
rows {
  id: "-20100035"
  zh_CN: "活动未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20100036"
  zh_CN: "暂未满足活动参与条件"
  showType: ECST_FlyTip
}
rows {
  id: "-20100037"
  zh_CN: "活动未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20100038"
  zh_CN: "活动未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20100039"
  zh_CN: "客户端版本号不匹配，请更新游戏"
  showType: ECST_FlyTip
}
rows {
  id: "-20110000"
  zh_CN: "服务器忙，请稍后重试"
}
rows {
  id: "-20110001"
  zh_CN: "小队不存在，请换个队伍重新加入吧"
  showType: ECST_FlyTip
}
rows {
  id: "-20110002"
  zh_CN: "小队成员已存在"
}
rows {
  id: "-20110003"
  zh_CN: "活动不在有效期内"
  showType: ECST_FlyTip
}
rows {
  id: "-20110004"
  zh_CN: "活动配置不存在"
}
rows {
  id: "-20110005"
  zh_CN: "暂无相关活动"
}
rows {
  id: "-20110006"
  zh_CN: "不在该小队中"
}
rows {
  id: "-20110007"
  zh_CN: "不能退出当前小队"
}
rows {
  id: "-20110008"
  zh_CN: "活动暂时不可用"
}
rows {
  id: "-20110009"
  zh_CN: "获取退出锁失败"
}
rows {
  id: "-20110010"
  zh_CN: "获取加入锁失败"
}
rows {
  id: "-20110011"
  zh_CN: "请勿加入自己的小队"
}
rows {
  id: "-20110012"
  zh_CN: "今天已经挖过咯"
  showType: ECST_FlyTip
}
rows {
  id: "-20110013"
  zh_CN: "无宝藏可挖"
  showType: ECST_FlyTip
}
rows {
  id: "-20110014"
  zh_CN: "该宝藏已经被其他玩家挖过了"
  showType: ECST_FlyTip
}
rows {
  id: "-20110015"
  zh_CN: "已加入当前小队"
  showType: ECST_FlyTip
}
rows {
  id: "-20110016"
  zh_CN: "已成团，无法加入其他小队"
  showType: ECST_FlyTip
}
rows {
  id: "-20110017"
  zh_CN: "小队配置有误"
}
rows {
  id: "-20110018"
  zh_CN: "您加入的小队已满，换个队伍试试吧~"
  showType: ECST_FlyTip
}
rows {
  id: "-20110019"
  zh_CN: "小队配置有误"
}
rows {
  id: "-20110020"
  zh_CN: "您和队友挖到的宝藏冲突啦，已为您随机挖宝挖到新宝藏"
  showType: ECST_FlyTip
}
rows {
  id: "-20110021"
  zh_CN: "您已经加入其他小队了"
  showType: ECST_FlyTip
}
rows {
  id: "-20111000"
  zh_CN: "小队数据库操作失败"
}
rows {
  id: "-20120001"
  zh_CN: "当前帐面没有对应的礼包"
}
rows {
  id: "-20120002"
  zh_CN: "储蓄礼包购买次数达到上限"
}
rows {
  id: "-20120003"
  zh_CN: "储蓄礼包购买顺序错误"
}
rows {
  id: "-20120004"
  zh_CN: "储蓄礼包购买期限超时"
}
rows {
  id: "-20120005"
  zh_CN: "储蓄礼包购买价格错误"
}
rows {
  id: "-20120006"
  zh_CN: "储蓄礼包无购买价格"
}
rows {
  id: "-20120007"
  zh_CN: "储蓄活动不在日期内"
}
rows {
  id: "-20120008"
  zh_CN: "储蓄活动未找到对应的购买记录"
}
rows {
  id: "-20120009"
  zh_CN: "储蓄活动购买撤销"
}
rows {
  id: "-20130001"
  zh_CN: "代理出现错误"
}
rows {
  id: "-20140001"
  zh_CN: "留言已满"
}
rows {
  id: "-20140002"
  zh_CN: "留言DB操作失败, 请稍后重试"
}
rows {
  id: "-20140003"
  zh_CN: "无效的留言ID"
}
rows {
  id: "-20140004"
  zh_CN: "无效的页签"
}
rows {
  id: "-20140005"
  zh_CN: "评论显示上限"
}
rows {
  id: "-20140006"
  zh_CN: "点赞显示上限"
}
rows {
  id: "-20140007"
  zh_CN: "最新留言显示上限"
}
rows {
  id: "-20140008"
  zh_CN: "热门留言显示上限"
}
rows {
  id: "-20140009"
  zh_CN: "留言CD中"
}
rows {
  id: "-20140010"
  zh_CN: "已点赞"
}
rows {
  id: "-20140011"
  zh_CN: "未点赞"
}
rows {
  id: "-20150001"
  zh_CN: "平台访问出现异常错误"
}
rows {
  id: "-20150002"
  zh_CN: "平台访问返回错误码"
}
rows {
  id: "-20150003"
  zh_CN: "平台访问返回解码失败"
}
rows {
  id: "-20160001"
  zh_CN: "需要分配失败"
}
rows {
  id: "-20160002"
  zh_CN: "序号释放失败"
}
rows {
  id: "-20160003"
  zh_CN: "序号查询失败"
}
rows {
  id: "-20160004"
  zh_CN: "序号心跳失败"
}
rows {
  id: "-20170001"
  zh_CN: "ai lab请求队列满"
}
rows {
  id: "-20170002"
  zh_CN: "ai lab连接已关闭"
}
rows {
  id: "-20170003"
  zh_CN: "ai lab框架错误"
}
rows {
  id: "-20180001"
  zh_CN: "分享邀请不存在"
}
rows {
  id: "-20180002"
  zh_CN: "请重新点击助力"
  showType: ECST_FlyTip
}
rows {
  id: "-20180003"
  zh_CN: "分享邀请已拒绝"
}
rows {
  id: "-20180004"
  zh_CN: "分享邀请创建失败"
}
rows {
  id: "-20180005"
  zh_CN: "该玩家接受助力的次数达到上限了哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-20180006"
  zh_CN: "您已经没有助力次数了哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-20180007"
  zh_CN: "邀请码生成达到上限"
}
rows {
  id: "-20180008"
  zh_CN: "邀请创建达到上限"
}
rows {
  id: "-20180009"
  zh_CN: "邀请创建配置错误"
}
rows {
  id: "-20180010"
  zh_CN: "邀请已过期"
}
rows {
  id: "-20180011"
  zh_CN: "已经助力过该玩家了哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-20180012"
  zh_CN: "自我助力"
}
rows {
  id: "-20190001"
  zh_CN: "非法资源版本号，格式不对"
}
rows {
  id: "-20190002"
  zh_CN: "当前资源版本号太低了"
}
rows {
  id: "-20190003"
  zh_CN: "当前资源版本号太高了"
}
rows {
  id: "-20190004"
  zh_CN: "重复的资源版本号"
}
rows {
  id: "-20190005"
  zh_CN: "资源版本号对应的环境不一致，main.feature"
}
rows {
  id: "-20190006"
  zh_CN: "资源版本号太低，需要更新"
}
rows {
  id: "-20191001"
  zh_CN: "json 处理失败"
}
rows {
  id: "-20191002"
  zh_CN: "序列化失败"
}
rows {
  id: "-20191003"
  zh_CN: "从json反序列化失败"
}
rows {
  id: "-20191004"
  zh_CN: "分配失败"
}
rows {
  id: "-20191005"
  zh_CN: "配置错误"
}
rows {
  id: "-20191006"
  zh_CN: "配失败"
}
rows {
  id: "-20191007"
  zh_CN: "多环境版本冲突"
}
rows {
  id: "-20191008"
  zh_CN: "参数错误"
}
rows {
  id: "-20192001"
  zh_CN: "配置错误"
}
rows {
  id: "-20192002"
  zh_CN: "参数错误"
}
rows {
  id: "-20192003"
  zh_CN: "未初始化"
}
rows {
  id: "-20192004"
  zh_CN: "获取日常数据失败"
}
rows {
  id: "-20193001"
  zh_CN: "小游戏中，无法接受"
  showType: ECST_FlyTip
}
rows {
  id: "-20200001"
  zh_CN: "产出控制DB表不存在"
}
rows {
  id: "-20200002"
  zh_CN: "产出控制模块不存在"
}
rows {
  id: "-20200003"
  zh_CN: "产出控制检查不通过"
}
rows {
  id: "-20210001"
  zh_CN: "直播未初始化"
}
rows {
  id: "-20210002"
  zh_CN: "服务器内部错误"
}
rows {
  id: "-20210003"
  zh_CN: "服务器内部错误"
}
rows {
  id: "-20210004"
  zh_CN: "服务器内部错误"
}
rows {
  id: "-20210005"
  zh_CN: "服务器内部错误"
}
rows {
  id: "-20210006"
  zh_CN: "服务器内部错误"
}
rows {
  id: "-20210007"
  zh_CN: "服务器内部错误"
}
rows {
  id: "-20211001"
  zh_CN: "消息队列未初始化"
}
rows {
  id: "-20211002"
  zh_CN: "消息队列无效配置"
}
rows {
  id: "-20211003"
  zh_CN: "消息队列参数错误"
}
rows {
  id: "-20211004"
  zh_CN: "消息队列未知错误"
}
rows {
  id: "-20211005"
  zh_CN: "消息队列订阅者已存在"
}
rows {
  id: "-20211006"
  zh_CN: "消息队列发布失败"
}
rows {
  id: "-20211007"
  zh_CN: "消息队列功能关闭"
}
rows {
  id: "-20211008"
  zh_CN: "消息队列发送队列已满"
}
rows {
  id: "-20211009"
  zh_CN: "消息队列创建生产者不存在"
}
rows {
  id: "-20211101"
  zh_CN: "队列未初始化"
}
rows {
  id: "-20211102"
  zh_CN: "队列无效配置"
}
rows {
  id: "-20211103"
  zh_CN: "队列参数错误"
}
rows {
  id: "-20211104"
  zh_CN: "队列参数错误"
}
rows {
  id: "-20211105"
  zh_CN: "队列消费失败"
}
rows {
  id: "-20211106"
  zh_CN: "队列功能关闭"
}
rows {
  id: "-20211201"
  zh_CN: "找不到对应的标签配置"
}
rows {
  id: "-20211202"
  zh_CN: "不支持的标签数值类型"
}
rows {
  id: "-20211203"
  zh_CN: "设置标签交互失败"
}
rows {
  id: "-20211204"
  zh_CN: "标签数值不非法"
}
rows {
  id: "-20220001"
  zh_CN: "赛事访问出现异常错误"
}
rows {
  id: "-20220002"
  zh_CN: "赛事报名费用不足"
}
rows {
  id: "-20220003"
  zh_CN: "赛事发送邮件失败"
}
rows {
  id: "-20220004"
  zh_CN: "游戏版本低，更新后再试试吧"
}
rows {
  id: "-20221006"
  zh_CN: "检测到近期有作弊行为，禁止参赛！"
  showType: ECST_FlyTip
}
rows {
  id: "-20221007"
  zh_CN: "模拟器设备不允许参赛，请更换设备!"
  showType: ECST_FlyTip
}
rows {
  id: "-20221008"
  zh_CN: "破解设备不允许参赛，请更换设备!"
  showType: ECST_FlyTip
}
rows {
  id: "-20221009"
  zh_CN: "当前设备报名数量已达上限，禁止报名！"
  showType: ECST_FlyTip
}
rows {
  id: "-20221010"
  zh_CN: "检测到该设备有过作弊行为，禁止参赛！"
  showType: ECST_FlyTip
}
rows {
  id: "-20221011"
  zh_CN: "检测到该设备有过作弊行为，禁止参赛"
  showType: ECST_FlyTip
}
rows {
  id: "-20222001"
  zh_CN: "积分赛时间已结束"
  showType: ECST_FlyTip
}
rows {
  id: "-20222002"
  zh_CN: "积分赛所有比赛已完成"
  showType: ECST_FlyTip
}
rows {
  id: "-20222003"
  zh_CN: "积分赛匹配仅支持单人，请退出组队后重试"
  showType: ECST_FlyTip
}
rows {
  id: "-20223001"
  zh_CN: "范围伤害枪械不可更换准心类型"
}
rows {
  id: "-20230001"
  zh_CN: "LBS后端未找到"
}
rows {
  id: "-20230002"
  zh_CN: "LBS后端返回错误码"
}
rows {
  id: "-20230003"
  zh_CN: "LBS后端未响应"
}
rows {
  id: "-20230004"
  zh_CN: "LBS刷新过于频繁"
}
rows {
  id: "-20230005"
  zh_CN: "LBS坐标非法"
}
rows {
  id: "-20230006"
  zh_CN: "LBS未开启"
}
rows {
  id: "-20230007"
  zh_CN: "LBS请求过多"
}
rows {
  id: "-20240000"
  zh_CN: "活动已结束"
  showType: ECST_FlyTip
}
rows {
  id: "-20240001"
  zh_CN: "回归活动已激活"
}
rows {
  id: "-20240002"
  zh_CN: "回归活动未激活"
}
rows {
  id: "-20240003"
  zh_CN: "回归默认配置不存在"
}
rows {
  id: "-20240004"
  zh_CN: "回归付费签到活动不存在"
}
rows {
  id: "-20240005"
  zh_CN: "回归付费签到活动门票已购买"
}
rows {
  id: "-20240006"
  zh_CN: "回归配置不存在"
}
rows {
  id: "-20240007"
  zh_CN: "回归付费签到门票配置不存在"
}
rows {
  id: "-20240008"
  zh_CN: "回归付费签到门票重复"
}
rows {
  id: "-20250001"
  zh_CN: "AMS道具类型不支持"
}
rows {
  id: "-20250002"
  zh_CN: "AMS道具超时"
}
rows {
  id: "-20250003"
  zh_CN: "AMS道具告罄"
}
rows {
  id: "-20250004"
  zh_CN: "AMS道具处理拥塞"
}
rows {
  id: "-20250005"
  zh_CN: "AMS道具未准备该账号类型的配置"
}
rows {
  id: "-20250006"
  zh_CN: "AMS道具风控或未成年拦截"
}
rows {
  id: "-20250007"
  zh_CN: "AMS发货返回错误码"
}
rows {
  id: "-20250008"
  zh_CN: "AMS道具不存在"
}
rows {
  id: "-20250009"
  zh_CN: "AMS道具未发布"
}
rows {
  id: "-20260001"
  zh_CN: "自定义key不存在"
}
rows {
  id: "-20260002"
  zh_CN: "自定义kv encode失败"
}
rows {
  id: "-20260003"
  zh_CN: "自定义kv decode失败"
}
rows {
  id: "-20260004"
  zh_CN: "自定义kv 域解析失败"
}
rows {
  id: "-20260005"
  zh_CN: "自定义kv 数据超过上限"
}
rows {
  id: "-20260006"
  zh_CN: "自定义kv key超长"
}
rows {
  id: "-20260007"
  zh_CN: "自定义kv value超长"
}
rows {
  id: "-20260008"
  zh_CN: "频率过高"
}
rows {
  id: "-20260009"
  zh_CN: "未知类型"
}
rows {
  id: "-20260010"
  zh_CN: "付费道具不足"
}
rows {
  id: "-20260011"
  zh_CN: "批量操作kv数据过多"
}
rows {
  id: "-20260012"
  zh_CN: "重复发货"
}
rows {
  id: "-20260013"
  zh_CN: "操作db失败"
}
rows {
  id: "-20260014"
  zh_CN: "db获取失败"
}
rows {
  id: "-20260015"
  zh_CN: "rpc通信失败"
}
rows {
  id: "-20260016"
  zh_CN: "消息队列满"
}
rows {
  id: "-20260017"
  zh_CN: "job异常"
}
rows {
  id: "-20260018"
  zh_CN: "ds 上报排行榜参数错误"
}
rows {
  id: "-20260083"
  zh_CN: "种植所需农场币不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20268001"
  zh_CN: "相同的平台openid申请创作者id流程中"
}
rows {
  id: "-20268002"
  zh_CN: "平台openid已经申请过创作者id"
}
rows {
  id: "-20268003"
  zh_CN: "申请创作者id失败"
}
rows {
  id: "-20268004"
  zh_CN: "申请创作者id保存信息失败"
}
rows {
  id: "-20270001"
  zh_CN: "弹幕不存在"
}
rows {
  id: "-20270002"
  zh_CN: "弹幕发送CD检查失败"
}
rows {
  id: "-20270003"
  zh_CN: "弹幕点赞CD检查失败"
}
rows {
  id: "-20270004"
  zh_CN: "不允许发弹幕"
}
rows {
  id: "-20270005"
  zh_CN: "不是玩家发的弹幕"
}
rows {
  id: "-20270006"
  zh_CN: "已经点过赞了"
}
rows {
  id: "-20270007"
  zh_CN: "弹幕类型错误"
}
rows {
  id: "-20270008"
  zh_CN: "点赞失败了"
}
rows {
  id: "-20270009"
  zh_CN: "点赞失败了"
}
rows {
  id: "-20270010"
  zh_CN: "非法弹幕"
}
rows {
  id: "-20270011"
  zh_CN: "服务发现失败"
}
rows {
  id: "-20270012"
  zh_CN: "弹幕功能被关闭"
}
rows {
  id: "-20270013"
  zh_CN: "不在白名单中"
}
rows {
  id: "-20280001"
  zh_CN: "演唱会配置不存在"
}
rows {
  id: "-20280002"
  zh_CN: "不在演唱会期间"
}
rows {
  id: "-20280003"
  zh_CN: "该明星不可选择"
}
rows {
  id: "-20280004"
  zh_CN: "该应援表情不可用"
}
rows {
  id: "-20280005"
  zh_CN: "该应援牌不可用"
}
rows {
  id: "-20280006"
  zh_CN: "服务器繁忙，请稍后再试"
}
rows {
  id: "-20280007"
  zh_CN: "生成纪念票失败"
}
rows {
  id: "-20280008"
  zh_CN: "技能设置已达上限"
}
rows {
  id: "-20280009"
  zh_CN: "动作设置已达上限"
}
rows {
  id: "-20290001"
  zh_CN: "服务器繁忙，请稍后重试"
}
rows {
  id: "-20290002"
  zh_CN: "NPC对话参数错误"
}
rows {
  id: "-20290003"
  zh_CN: "NPC对话功能发生异常"
}
rows {
  id: "-20300001"
  zh_CN: "找不到目标农场"
  showType: ECST_FlyTip
}
rows {
  id: "-20300002"
  zh_CN: "这个农场的访客人数已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300003"
  zh_CN: "-20300003"
}
rows {
  id: "-20300004"
  zh_CN: "-20300004"
}
rows {
  id: "-20300005"
  zh_CN: "匹配中，不能进入星宝农场"
  showType: ECST_FlyTip
}
rows {
  id: "-20300006"
  zh_CN: "-20300006"
}
rows {
  id: "-20300007"
  zh_CN: "坐标非法"
  showType: ECST_FlyTip
}
rows {
  id: "-20300008"
  zh_CN: "该区域不可种植"
  showType: ECST_FlyTip
}
rows {
  id: "-20300009"
  zh_CN: "操作无效"
  showType: ECST_FlyTip
}
rows {
  id: "-20300010"
  zh_CN: "养殖物不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300011"
  zh_CN: "土地已经被占用"
  showType: ECST_FlyTip
}
rows {
  id: "-20300012"
  zh_CN: "土地已经被开垦过了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300013"
  zh_CN: "土地还没被开垦"
  showType: ECST_FlyTip
}
rows {
  id: "-20300014"
  zh_CN: "找不到这位访客"
  showType: ECST_FlyTip
}
rows {
  id: "-20300015"
  zh_CN: "找不到这个建筑类型"
  showType: ECST_FlyTip
}
rows {
  id: "-20300016"
  zh_CN: "当前目标不能频繁照料"
  showType: ECST_FlyTip
}
rows {
  id: "-20300017"
  zh_CN: "当前目标尚未产出农产品"
  showType: ECST_FlyTip
}
rows {
  id: "-20300018"
  zh_CN: "这块土地尚未开垦"
  showType: ECST_FlyTip
}
rows {
  id: "-20300019"
  zh_CN: "这块土地已被占用"
  showType: ECST_FlyTip
}
rows {
  id: "-20300020"
  zh_CN: "不需要照料"
  showType: ECST_FlyTip
}
rows {
  id: "-20300021"
  zh_CN: "操作养殖物数量超出限制"
  showType: ECST_FlyTip
}
rows {
  id: "-20300022"
  zh_CN: "养殖物配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300023"
  zh_CN: "建筑不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300024"
  zh_CN: "建筑已达最大等级"
  showType: ECST_FlyTip
}
rows {
  id: "-20300025"
  zh_CN: "建筑升级所需农场小窝等级不够"
  showType: ECST_FlyTip
}
rows {
  id: "-20300026"
  zh_CN: "升级所需农场经验不够"
  showType: ECST_FlyTip
}
rows {
  id: "-20300027"
  zh_CN: "建筑配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300028"
  zh_CN: "不是农场主人"
  showType: ECST_FlyTip
}
rows {
  id: "-20300029"
  zh_CN: "土地配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300030"
  zh_CN: "您尚未解锁农场小屋"
  showType: ECST_FlyTip
}
rows {
  id: "-20300031"
  zh_CN: "农场币不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20300032"
  zh_CN: "道具配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300033"
  zh_CN: "建筑售卖道具类型非法"
  showType: ECST_FlyTip
}
rows {
  id: "-20300034"
  zh_CN: "建筑用途非法"
  showType: ECST_FlyTip
}
rows {
  id: "-20300035"
  zh_CN: "养殖物未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-20300064"
  zh_CN: "当前时间段禁止拿取行为哦"
  showType: ECST_FlyTip
}
rows {
  id: "-20300065"
  zh_CN: "拿取对象不在当前所在农场"
  showType: ECST_FlyTip
}
rows {
  id: "-20300066"
  zh_CN: "您不能对自己农场出产的东西进行拿取操作"
  showType: ECST_FlyTip
}
rows {
  id: "-20300067"
  zh_CN: "拿取和祈福对象必须是自己的好友"
  showType: ECST_FlyTip
}
rows {
  id: "-20300068"
  zh_CN: "土地升级配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300069"
  zh_CN: "您在对方的黑名单中，无法进入该农场"
  showType: ECST_FlyTip
}
rows {
  id: "-20300070"
  zh_CN: "拿取次数已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300071"
  zh_CN: "给ds发送失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300072"
  zh_CN: "养殖物产出未计算"
  showType: ECST_FlyTip
}
rows {
  id: "-20300073"
  zh_CN: "对方不是您的好友"
  showType: ECST_FlyTip
}
rows {
  id: "-20300074"
  zh_CN: "当前目标已经被你拿取过了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300075"
  zh_CN: "ds创建失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300076"
  zh_CN: "您的农场版本较低，请更新后再尝试访问"
  showType: ECST_FlyTip
}
rows {
  id: "-20300077"
  zh_CN: "对方农场的版本过低，无法访问"
  showType: ECST_FlyTip
}
rows {
  id: "-20300078"
  zh_CN: "你不在你当前希望操作的农场"
  showType: ECST_FlyTip
}
rows {
  id: "-20300079"
  zh_CN: "建筑已经拥有"
  showType: ECST_FlyTip
}
rows {
  id: "-20300080"
  zh_CN: "当前目标已经不能再被拿取了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300081"
  zh_CN: "养殖物等级配置找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20300082"
  zh_CN: "您被农场主人请离，当前不能进入农场"
  showType: ECST_FlyTip
}
rows {
  id: "-20300083"
  zh_CN: "种植所需道具不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20300084"
  zh_CN: "农场下架"
  showType: ECST_FlyTip
}
rows {
  id: "-20300085"
  zh_CN: "对方在农场中，无法前往"
  showType: ECST_FlyTip
}
rows {
  id: "-20300086"
  zh_CN: "对方在农场中，无法邀请"
  showType: ECST_FlyTip
}
rows {
  id: "-20300087"
  zh_CN: "禁言导致不能留言"
  showType: ECST_FlyTip
}
rows {
  id: "-20300088"
  zh_CN: "玩家不在自己家"
  showType: ECST_FlyTip
}
rows {
  id: "-20300089"
  zh_CN: "家园欢迎词功能未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20300090"
  zh_CN: "达到欢迎词长度限制"
  showType: ECST_FlyTip
}
rows {
  id: "-20300091"
  zh_CN: "欢迎词禁止设置"
  showType: ECST_FlyTip
}
rows {
  id: "-20300092"
  zh_CN: "家园欢迎词功能未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20300093"
  zh_CN: "达到留言内容长度限制"
  showType: ECST_FlyTip
}
rows {
  id: "-20300094"
  zh_CN: "留言发送失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300095"
  zh_CN: "留言回复发送失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300096"
  zh_CN: "留言找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20300097"
  zh_CN: "达到留言每次发送上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300098"
  zh_CN: "留言回复找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20300099"
  zh_CN: "留言删除失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300100"
  zh_CN: "留言回复删除失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300101"
  zh_CN: "好友关系异常，无法进行拿取和祈福"
  showType: ECST_FlyTip
}
rows {
  id: "-20300102"
  zh_CN: "祈福次数已用尽，次日8点恢复"
  showType: ECST_FlyTip
}
rows {
  id: "-20300103"
  zh_CN: "祈福目标不是好友"
  showType: ECST_FlyTip
}
rows {
  id: "-20300104"
  zh_CN: "作物和地块不匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-20300105"
  zh_CN: "已经祈福过当前目标了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300106"
  zh_CN: "当前目标已经是大丰收了，不能祈福"
  showType: ECST_FlyTip
}
rows {
  id: "-20300107"
  zh_CN: "发生未知错误（10700008）"
  showType: ECST_FlyTip
}
rows {
  id: "-20300108"
  zh_CN: "星钻不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20300109"
  zh_CN: "米大师返回失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300110"
  zh_CN: "鼓励失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300111"
  zh_CN: "月卡功能尚未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20300112"
  zh_CN: "月卡开启时间配置错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20300113"
  zh_CN: "祈福功能尚未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20300114"
  zh_CN: "祈福开启时间配置错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20300115"
  zh_CN: "喂食数量不对"
  showType: ECST_FlyTip
}
rows {
  id: "-20300116"
  zh_CN: "农场好友置顶总数超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300117"
  zh_CN: "农场好友屏蔽总数超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300118"
  zh_CN: "只能对农场好友进行置顶和屏蔽"
  showType: ECST_FlyTip
}
rows {
  id: "-20300119"
  zh_CN: "你们的农场处于屏蔽状态，不能拜访哦"
  showType: ECST_FlyTip
}
rows {
  id: "-20300120"
  zh_CN: "你们的农场处于屏蔽状态，不能拜访哦"
  showType: ECST_FlyTip
}
rows {
  id: "-20300121"
  zh_CN: "你们的农场处于屏蔽状态，不能拜访哦"
  showType: ECST_FlyTip
}
rows {
  id: "-20300122"
  zh_CN: "邀请被屏蔽的人"
  showType: ECST_FlyTip
}
rows {
  id: "-20300123"
  zh_CN: "邀请人农场版本不匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-20300124"
  zh_CN: "畜牧未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20310001"
  zh_CN: "订阅QQ机器人失败"
}
rows {
  id: "-20320000"
  zh_CN: "无法解析的协议id"
}
rows {
  id: "-20320001"
  zh_CN: "协议id无对应处理"
}
rows {
  id: "-20320002"
  zh_CN: "协议id处理未实现"
}
rows {
  id: "-20320003"
  zh_CN: "绑定的游戏角色被禁止创建地图，无法同步地图"
}
rows {
  id: "-20330000"
  zh_CN: "当前登录方式不支持IAA"
}
rows {
  id: "-20330001"
  zh_CN: "IAA未开启"
}
rows {
  id: "-20330002"
  zh_CN: "IAA版本号不匹配"
}
rows {
  id: "-20330003"
  zh_CN: "IAA不在开放时间"
}
rows {
  id: "-20330004"
  zh_CN: "IAA达到总上限"
}
rows {
  id: "-20330005"
  zh_CN: "IAA达到每日上限"
}
rows {
  id: "-30010001"
  zh_CN: "DSC分配失败"
}
rows {
  id: "-40010001"
  zh_CN: "DS无权修改此物品"
}
rows {
  id: "-40010002"
  zh_CN: "此次修改版本号不能修改DsDB"
}
rows {
  id: "-101400010"
  zh_CN: "排行榜后端错误码返回"
}
rows {
  id: "-101400011"
  zh_CN: "排行榜后端请求拥塞"
}
rows {
  id: "-102000003"
  zh_CN: "设置烟花文本时长度非法"
}
rows {
  id: "-106000025"
  zh_CN: "没有点赞 收藏 订阅"
}
rows {
  id: "-200204312"
  zh_CN: "BattleResMgr不可访问该配置表"
}
rows {
  id: "-10000099"
  zh_CN: "客户端版本比地图版本低"
  showType: ECST_FlyTip
}
rows {
  id: "-10140043"
  zh_CN: "排行榜未到发奖时刻"
}
rows {
  id: "-1060270"
  zh_CN: "ugc 申请UGC商品ID 频率控制"
}
rows {
  id: "-1060271"
  zh_CN: "ugc 申请UGC rank ID 频率控制"
}
rows {
  id: "-10600256"
  zh_CN: "对局数据不存在"
}
rows {
  id: "-20000005"
  zh_CN: "创建事件错误"
}
rows {
  id: "-20300125"
  zh_CN: "该农场现在不能进入"
  showType: ECST_FlyTip
}
rows {
  id: "-10040964"
  zh_CN: "已经打过卡"
}
rows {
  id: "-10040965"
  zh_CN: "补签天数错误"
}
rows {
  id: "-10040966"
  zh_CN: "补签次数不足"
}
rows {
  id: "-10040967"
  zh_CN: "已经领过该奖励"
}
rows {
  id: "-10040968"
  zh_CN: "不符合领奖条件"
}
rows {
  id: "-10040969"
  zh_CN: "没有对应配置奖励"
}
rows {
  id: "-10400057"
  zh_CN: "社团名称功能升级中，暂时无法修改"
  showType: ECST_FlyTip
}
rows {
  id: "-10400058"
  zh_CN: "社团宣言功能升级中，暂时无法修改"
  showType: ECST_FlyTip
}
rows {
  id: "-10400059"
  zh_CN: "社团头像功能升级中，暂时无法修改"
  showType: ECST_FlyTip
}
rows {
  id: "-10040980"
  zh_CN: "暂无手账本"
}
rows {
  id: "-10040981"
  zh_CN: "手账未找到"
}
rows {
  id: "-10040982"
  zh_CN: "手账未完成"
}
rows {
  id: "-10040983"
  zh_CN: "完成手账失败"
}
rows {
  id: "-10040984"
  zh_CN: "还未领取奖励，不能重置"
}
rows {
  id: "-10040985"
  zh_CN: "今日重置次数已用完"
}
rows {
  id: "-10040986"
  zh_CN: "任务信息未找到"
}
rows {
  id: "-10040987"
  zh_CN: "奖励暂时不能领取"
}
rows {
  id: "-10040988"
  zh_CN: "奖励已领取"
}
rows {
  id: "-10040989"
  zh_CN: "奖励领取失败"
}
rows {
  id: "-10040990"
  zh_CN: "贴纸未找到"
}
rows {
  id: "-10040991"
  zh_CN: "贴纸交换失败"
}
rows {
  id: "-10600257"
  zh_CN: "拉取地图排行榜信息失败"
}
rows {
  id: "-10800005"
  zh_CN: "家园留言板权限检查未通过"
  showType: ECST_FlyTip
}
rows {
  id: "-20017040"
  zh_CN: "房间对局中，请稍后重试"
}
rows {
  id: "-20100040"
  zh_CN: "不允许分享产生抽奖折扣"
}
rows {
  id: "-20100041"
  zh_CN: "不允许看广告产生抽奖折扣"
}
rows {
  id: "-20100042"
  zh_CN: "分享产生抽奖折扣达到上限"
}
rows {
  id: "-20100043"
  zh_CN: "看广告产生抽奖折扣达到上限"
}
rows {
  id: "-20260019"
  zh_CN: "ds 上报排行榜rankid不合法"
}
rows {
  id: "-20300127"
  zh_CN: "当前农场被祈福次数已达上限，每日8点重置"
  showType: ECST_FlyTip
}
rows {
  id: "-20300128"
  zh_CN: "农场等级未达到祈福解锁限制"
  showType: ECST_FlyTip
}
rows {
  id: "-20017041"
  zh_CN: "加入失败，是否重试"
  showType: ECST_FlyTip
}
rows {
  id: "-20017042"
  zh_CN: "地理信息失效"
  showType: ECST_FlyTip
}
rows {
  id: "-10471001"
  zh_CN: "二级密码保护已开启"
  showType: ECST_FlyTip
}
rows {
  id: "-10471002"
  zh_CN: "二级密码保护未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-10471003"
  zh_CN: "二级密码保护已关闭"
  showType: ECST_FlyTip
}
rows {
  id: "-10471004"
  zh_CN: "密码错误，请检查后重新输入"
  showType: ECST_FlyTip
}
rows {
  id: "-10471005"
  zh_CN: "密码为空，请重新输入"
  showType: ECST_FlyTip
}
rows {
  id: "-10471006"
  zh_CN: "密码不符合规则，请重新输入"
  showType: ECST_FlyTip
}
rows {
  id: "-10471007"
  zh_CN: "两次密码不相同，请重新输入"
  showType: ECST_FlyTip
}
rows {
  id: "-10471008"
  zh_CN: "请先验证二级密码"
  showType: ECST_FlyTip
}
rows {
  id: "-1004020"
  zh_CN: "小队链接已失效"
}
rows {
  id: "-1004021"
  zh_CN: "系统繁忙，请重试"
}
rows {
  id: "-1004022"
  zh_CN: "您已在其他小队，无法重复加入"
}
rows {
  id: "-1004023"
  zh_CN: "您已加入该小队"
}
rows {
  id: "-1004024"
  zh_CN: "小队人数已满，加入失败"
}
rows {
  id: "-10100075"
  zh_CN: "备用搭配方案不可随机"
}
rows {
  id: "-10140044"
  zh_CN: "排行榜不支持KV键值对类型的分数"
}
rows {
  id: "-10140045"
  zh_CN: "排行榜KV键值对类型的Key非法"
}
rows {
  id: "-10900001"
  zh_CN: "社交服代理配置错误"
}
rows {
  id: "-10900002"
  zh_CN: "加载RelationCache失败"
}
rows {
  id: "-10900003"
  zh_CN: "缓存可迁移"
}
rows {
  id: "-10900004"
  zh_CN: "加载SnsCache失败"
}
rows {
  id: "-10900005"
  zh_CN: "社交服内部错误"
}
rows {
  id: "-20017043"
  zh_CN: "数字不符合规范，请重新输入"
  showType: ECST_FlyTip
}
rows {
  id: "-20017044"
  zh_CN: "数字过于简单，请重新输入"
  showType: ECST_FlyTip
}
rows {
  id: "-20017045"
  zh_CN: "房间已满，请更换房间码"
  showType: ECST_FlyTip
}
rows {
  id: "-20017046"
  zh_CN: "未解锁该玩法"
}
rows {
  id: "-20017047"
  zh_CN: "有成员未解锁该玩法"
}
rows {
  id: "-20211301"
  zh_CN: "非法配置"
}
rows {
  id: "-20230008"
  zh_CN: "面对面房间搜索失败"
}
rows {
  id: "-20230009"
  zh_CN: "面对面房间更新失败"
}
rows {
  id: "-20230010"
  zh_CN: "面对面房间删除失败"
}
rows {
  id: "-20230011"
  zh_CN: "面对面房间非创建者"
}
rows {
  id: "-20230012"
  zh_CN: "面对面房间信息解析失败"
}
rows {
  id: "-20230013"
  zh_CN: "面对面房间ID不匹配"
}
rows {
  id: "-20230014"
  zh_CN: "面对面房间尚未完成初始化"
}
rows {
  id: "-20230015"
  zh_CN: "面对面房间不存在"
}
rows {
  id: "-20260020"
  zh_CN: "cs 转发 Ugcdatastore 错误"
}
rows {
  id: "-20260021"
  zh_CN: "cs 转发 Ugcdatastore 错误"
}
rows {
  id: "-20260022"
  zh_CN: "cs 转发 Ugcdatastore 找不到handle"
}
rows {
  id: "-20260023"
  zh_CN: "cs 转发 Ugcdatastore 错误 处理超时"
}
rows {
  id: "-20260024"
  zh_CN: "cs 转发 Ugcdatastore 处理错误"
}
rows {
  id: "-20300146"
  zh_CN: "只有好友才可以取消本次提醒"
  showType: ECST_FlyTip
}
rows {
  id: "-20330006"
  zh_CN: "IAA处理失败"
}
rows {
  id: "-20330007"
  zh_CN: "IAA抽奖奖池不匹配"
}
rows {
  id: "-20330008"
  zh_CN: "IAA操作尚不支持"
}
rows {
  id: "-20340000"
  zh_CN: "总进度节点奖励已领取"
}
rows {
  id: "-20340001"
  zh_CN: "总进度节点不存在"
}
rows {
  id: "-20340002"
  zh_CN: "总进度节点领奖失败"
}
rows {
  id: "10040973"
  zh_CN: "圈奖励条件不满"
}
rows {
  id: "10040974"
  zh_CN: "圈奖励已领取"
}
rows {
  id: "10041000"
  zh_CN: "动物图鉴活动聚集地配置不存在"
}
rows {
  id: "10041001"
  zh_CN: "动物图鉴活动探查道具不足"
}
rows {
  id: "10041002"
  zh_CN: "动物图鉴活动聚集地不能开始诱捕"
}
rows {
  id: "10041003"
  zh_CN: "动物图鉴活动聚集地开始诱捕失败"
}
rows {
  id: "10041004"
  zh_CN: "动物图鉴活动物种配置不存在"
}
rows {
  id: "10041005"
  zh_CN: "动物图鉴活动动物配置不存在"
}
rows {
  id: "10041006"
  zh_CN: "动物图鉴活动聚集地不能捕获"
}
rows {
  id: "10041007"
  zh_CN: "动物图鉴活动聚集地捕获失败"
}
rows {
  id: "10041008"
  zh_CN: "动物图鉴活动不能领取物种图鉴集齐奖励"
}
rows {
  id: "10041009"
  zh_CN: "动物图鉴活动领取物种图鉴集齐奖励失败"
}
rows {
  id: "10041010"
  zh_CN: "动物图鉴活动不能领取终极大奖"
}
rows {
  id: "10041011"
  zh_CN: "动物图鉴活动领取终极大奖失败"
}
rows {
  id: "10041012"
  zh_CN: "动物图鉴活动奖励已领取"
}
rows {
  id: "-10060016"
  zh_CN: "未获得鉴赏资格"
}
rows {
  id: "-10060017"
  zh_CN: "已经评论过了"
}
rows {
  id: "-10060018"
  zh_CN: "已经打过标签了"
}
rows {
  id: "-10060019"
  zh_CN: "已经打过分了"
}
rows {
  id: "-10060020"
  zh_CN: "数据库错误"
}
rows {
  id: "-10600258"
  zh_CN: "获取玩家游玩地图时间失败"
}
rows {
  id: "-10950001"
  zh_CN: "缓存可迁移"
}
rows {
  id: "-10950002"
  zh_CN: "缓存管理器内部错误"
}
rows {
  id: "-20220005"
  zh_CN: "赛事检查文本不合法"
}
rows {
  id: "-20340003"
  zh_CN: "总进度节点领奖奖杯不足"
}
rows {
  id: "-20017048"
  zh_CN: "有成员未解锁该玩法"
}
rows {
  id: "-10010064"
  zh_CN: "达到亲密好友的交互动作数量限制"
}
rows {
  id: "-10010065"
  zh_CN: "亲密好友的交互动作配置错误"
}
rows {
  id: "-10010066"
  zh_CN: "亲密好友的交互动作未拥有"
}
rows {
  id: "-10010067"
  zh_CN: "不是亲密好友"
}
rows {
  id: "-1004091000"
  zh_CN: "社团挑战活动未开启"
}
rows {
  id: "-1004091001"
  zh_CN: "社团挑战活动不在进行期"
}
rows {
  id: "-1004091002"
  zh_CN: "该玩法未开启挑战"
}
rows {
  id: "-1004091003"
  zh_CN: "该档位奖励已经领取"
}
rows {
  id: "-1004091004"
  zh_CN: "您在该社团没有贡献过星光值"
}
rows {
  id: "-1004091005"
  zh_CN: "社团总贡献度不够，无法领取该档位奖励"
}
rows {
  id: "-1004091006"
  zh_CN: "名次不达标"
}
rows {
  id: "10041020"
  zh_CN: "动物图鉴活动聚集地配置不存在"
}
rows {
  id: "10041021"
  zh_CN: "动物图鉴活动探查道具不足"
}
rows {
  id: "10041022"
  zh_CN: "动物图鉴活动聚集地不能开始诱捕"
}
rows {
  id: "10041023"
  zh_CN: "动物图鉴活动聚集地开始诱捕失败"
}
rows {
  id: "10041024"
  zh_CN: "动物图鉴活动物种配置不存在"
}
rows {
  id: "10041025"
  zh_CN: "动物图鉴活动动物配置不存在"
}
rows {
  id: "10041026"
  zh_CN: "动物图鉴活动聚集地不能捕获"
}
rows {
  id: "10041027"
  zh_CN: "动物图鉴活动聚集地捕获失败"
}
rows {
  id: "10041028"
  zh_CN: "动物图鉴活动不能领取物种图鉴集齐奖励"
}
rows {
  id: "10041029"
  zh_CN: "动物图鉴活动领取物种图鉴集齐奖励失败"
}
rows {
  id: "10041030"
  zh_CN: "动物图鉴活动不能领取终极大奖"
}
rows {
  id: "10041031"
  zh_CN: "动物图鉴活动领取终极大奖失败"
}
rows {
  id: "10041032"
  zh_CN: "动物图鉴活动奖励已领取"
}
rows {
  id: "10041033"
  zh_CN: "动物图鉴活动生成赠送信息失败"
}
rows {
  id: "10041034"
  zh_CN: "动物图鉴活动不能赠送每种动物第一个获得的图鉴"
}
rows {
  id: "10041035"
  zh_CN: "动物图鉴活动达到每周赠送图鉴次数上限"
}
rows {
  id: "10041036"
  zh_CN: "动物图鉴活动领取赠送失败"
}
rows {
  id: "10041037"
  zh_CN: "动物图鉴活动不能领取自己赠送的图鉴"
}
rows {
  id: "-10170039"
  zh_CN: "聊天消息已超时"
}
rows {
  id: "-20000006"
  zh_CN: "未找到ActivityUnit数据"
}
rows {
  id: "-20000007"
  zh_CN: "未找到BaseActivity实例"
}
rows {
  id: "-20000008"
  zh_CN: "未找到活动对应的配置"
}
rows {
  id: "-20000009"
  zh_CN: "活动自定义处理异常"
}
rows {
  id: "-20000010"
  zh_CN: "活动数据加载失败"
}
rows {
  id: "-20000011"
  zh_CN: "活动服务RPC处理异常"
}
rows {
  id: "-20000012"
  zh_CN: "活动服务RPC消息队列已满"
}
rows {
  id: "-20000013"
  zh_CN: "活动服务RPC消息队列添加数据失败"
}
rows {
  id: "-20000014"
  zh_CN: "活动通用协议type解析失败"
}
rows {
  id: "-20000015"
  zh_CN: "活动通用协议处理回调未注册"
}
rows {
  id: "-20000101"
  zh_CN: "已经助力过别人"
}
rows {
  id: "-20000102"
  zh_CN: "不能助力自己"
}
rows {
  id: "-20000103"
  zh_CN: "选择的奖励数量异常"
}
rows {
  id: "-20000104"
  zh_CN: "已经选择过心愿礼物"
}
rows {
  id: "-20000105"
  zh_CN: "选择的心愿礼物有重复"
}
rows {
  id: "-20000106"
  zh_CN: "玩家还未选择礼物,无法领奖"
}
rows {
  id: "-20000107"
  zh_CN: "心愿值不满足要求,无法领奖"
}
rows {
  id: "-20000108"
  zh_CN: "已领取过奖励"
}
rows {
  id: "-20000109"
  zh_CN: "短ID转换失败(RPC)"
}
rows {
  id: "-20000110"
  zh_CN: "短ID转换失败(Type)"
}
rows {
  id: "-20000111"
  zh_CN: "邀请码无效"
}
rows {
  id: "-20000112"
  zh_CN: "助力值配置异常"
}
rows {
  id: "-20000113"
  zh_CN: "已经助力过该好友"
}
rows {
  id: "-20000114"
  zh_CN: "奖励推导异常,请检查配置"
}
rows {
  id: "-20350001"
  zh_CN: "BP版本号不匹配"
}
rows {
  id: "-20350002"
  zh_CN: "BP未开启"
}
rows {
  id: "-20350003"
  zh_CN: "BP等级未达到"
}
rows {
  id: "-20350004"
  zh_CN: "BP付费类型不满足"
}
rows {
  id: "-20350006"
  zh_CN: "BP奖励重复领取"
}
rows {
  id: "-20350007"
  zh_CN: "BP等级参数非法"
}
rows {
  id: "-20350008"
  zh_CN: "BP类型不支持"
}
rows {
  id: "-20350009"
  zh_CN: "BP赛季已过期"
}
rows {
  id: "-20350010"
  zh_CN: "BP奖励未开放"
}
rows {
  id: "-20350011"
  zh_CN: "BP等级购买超出了当前上限"
}
rows {
  id: "-20350012"
  zh_CN: "BP等级购买不满足购买条件"
}
rows {
  id: "-20350013"
  zh_CN: "BP购买资金不足"
}
rows {
  id: "-20350014"
  zh_CN: "BP付费已生效"
}
rows {
  id: "-1000884"
  zh_CN: "IDIP用户处于安全惩罚处理中"
}
rows {
  id: "-10040701"
  zh_CN: "特训营数据不存在"
}
rows {
  id: "-10040702"
  zh_CN: "运动员数据不存在"
}
rows {
  id: "-10040703"
  zh_CN: "运动员配置不存在"
}
rows {
  id: "-10040704"
  zh_CN: "运动员已满级"
}
rows {
  id: "-10040705"
  zh_CN: "运动员体力不足"
}
rows {
  id: "-10040706"
  zh_CN: "运动员训练失败"
}
rows {
  id: "-10040707"
  zh_CN: "本周助力达到上限"
}
rows {
  id: "-10040708"
  zh_CN: "本周已助力过"
}
rows {
  id: "-10040709"
  zh_CN: "本周被助力达到上限"
}
rows {
  id: "-10040710"
  zh_CN: "本周已被助力过"
}
rows {
  id: "-10040711"
  zh_CN: "不能助力自己"
}
rows {
  id: "10041038"
  zh_CN: "动物图鉴活动赠送的动物图鉴已经被领取"
}
rows {
  id: "-10060021"
  zh_CN: "种草社区暂未开启"
}
rows {
  id: "-10060022"
  zh_CN: "未找到待鉴赏地图"
}
rows {
  id: "-10600259"
  zh_CN: "ugc切换场景失败"
}
rows {
  id: "-10600260"
  zh_CN: "ugc删除场景失败"
}
rows {
  id: "-10600261"
  zh_CN: "ugc当前场景不存在"
}
rows {
  id: "-10600262"
  zh_CN: "正在当前场景"
}
rows {
  id: "-10800006"
  zh_CN: "对方礼物已摆满"
  showType: ECST_FlyTip
}
rows {
  id: "-10800007"
  zh_CN: "对方的礼物已超今日上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10800008"
  zh_CN: "你今天送太多礼物了"
  showType: ECST_FlyTip
}
rows {
  id: "-10800009"
  zh_CN: "你给这个人送太多礼物了"
  showType: ECST_FlyTip
}
rows {
  id: "-10800010"
  zh_CN: "礼物留言字数超上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10800011"
  zh_CN: "你已被禁止向他人送礼"
  showType: ECST_FlyTip
}
rows {
  id: "-20000016"
  zh_CN: "删除道具列表为空 (协议)"
}
rows {
  id: "-20000017"
  zh_CN: "删除道具列表为空 (玩家背包)"
}
rows {
  id: "-20100044"
  zh_CN: "BI响应为空"
}
rows {
  id: "-20100045"
  zh_CN: "当前环境BI配置缺失"
}
rows {
  id: "-20100046"
  zh_CN: "BI页签排序被忽略"
}
rows {
  id: "-20100047"
  zh_CN: "BI页签排序未开启"
}
rows {
  id: "-20211302"
  zh_CN: "重复配置"
}
rows {
  id: "-20300129"
  zh_CN: "农场鱼塘未清空"
  showType: ECST_FlyTip
}
rows {
  id: "-20300130"
  zh_CN: "农场鱼塘里已经没有鱼了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300131"
  zh_CN: "鱼已经钓上来了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300132"
  zh_CN: "太久没有收杆了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300133"
  zh_CN: "鱼还不能收获哦"
  showType: ECST_FlyTip
}
rows {
  id: "-20300134"
  zh_CN: "鱼已经可以收获，不能取消了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300135"
  zh_CN: "鱼塘状态错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20300136"
  zh_CN: "还在保护期，不可以捕鱼哦"
  showType: ECST_FlyTip
}
rows {
  id: "-20300137"
  zh_CN: "给主人留点吧……"
  showType: ECST_FlyTip
}
rows {
  id: "-20300138"
  zh_CN: "您在这个鱼塘的拿取次数已达上限，下次再来吧"
  showType: ECST_FlyTip
}
rows {
  id: "-20300139"
  zh_CN: "鱼缸未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-20300140"
  zh_CN: "鱼缸大小不匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-20300141"
  zh_CN: "兑换米大师不存在，参考摇树米大师，一模一样配置一份"
  showType: ECST_FlyTip
}
rows {
  id: "-20300142"
  zh_CN: "鱼饵跟鱼塘深度不匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-20300143"
  zh_CN: "图鉴奖励条件不满足"
  showType: ECST_FlyTip
}
rows {
  id: "-20300144"
  zh_CN: "图鉴奖励已经领取"
  showType: ECST_FlyTip
}
rows {
  id: "-20300145"
  zh_CN: "没有这个鱼的价格"
  showType: ECST_FlyTip
}
rows {
  id: "-20300147"
  zh_CN: "找不到建筑皮肤配置"
  showType: ECST_FlyTip
}
rows {
  id: "-20300148"
  zh_CN: "建筑皮肤类型不支持"
  showType: ECST_FlyTip
}
rows {
  id: "-20300149"
  zh_CN: "未拥有该建筑皮肤"
  showType: ECST_FlyTip
}
rows {
  id: "-20300150"
  zh_CN: "建筑皮肤已经拥有"
  showType: ECST_FlyTip
}
rows {
  id: "-20300151"
  zh_CN: "只能给好友送礼"
  showType: ECST_FlyTip
}
rows {
  id: "-20300152"
  zh_CN: "活动已过期，无法领取"
  showType: ECST_FlyTip
}
rows {
  id: "-20300153"
  zh_CN: "鱼缸已经解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-20300154"
  zh_CN: "buff配置表不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300155"
  zh_CN: "没找到buff"
  showType: ECST_FlyTip
}
rows {
  id: "-20300156"
  zh_CN: "钓鱼未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20300157"
  zh_CN: "不能送鱼"
  showType: ECST_FlyTip
}
rows {
  id: "-20300158"
  zh_CN: "请先进入自己的星宝农场"
  showType: ECST_FlyTip
}
rows {
  id: "10041039"
  zh_CN: "动物图鉴活动获取赠送图鉴错误"
}
rows {
  id: "10041050"
  zh_CN: "奖励未领取"
}
rows {
  id: "10041051"
  zh_CN: "奖励已领取"
}
rows {
  id: "10041052"
  zh_CN: "点数非法"
}
rows {
  id: "10041053"
  zh_CN: "无奖励配置"
}
rows {
  id: "10041054"
  zh_CN: "奖励ID非法"
}
rows {
  id: "10041055"
  zh_CN: "无有效奖励"
}
rows {
  id: "10041056"
  zh_CN: "圈数不够"
}
rows {
  id: "10041057"
  zh_CN: "步数不够"
}
rows {
  id: "10041058"
  zh_CN: "步数生成出错"
}
rows {
  id: "10041059"
  zh_CN: "骰子不够"
}
rows {
  id: "-10600263"
  zh_CN: "已经申请下架了"
}
rows {
  id: "-20000115"
  zh_CN: "已经助力过别人(重复帮助此人)"
}
rows {
  id: "-20000116"
  zh_CN: "已经助力过别人(openid重复)"
}
rows {
  id: "-20100048"
  zh_CN: "奖励拒绝被选中"
}
rows {
  id: "-20100049"
  zh_CN: "同组奖励已被选中"
}
rows {
  id: "-20100050"
  zh_CN: "请先完成心愿单的选择"
  showType: ECST_FlyTip
}
rows {
  id: "-20200004"
  zh_CN: "产出控制检查必须调用到checkOutput"
}
rows {
  id: "-10400060"
  zh_CN: "分享过于频繁，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-10010068"
  zh_CN: "当前阶段不能选任务"
}
rows {
  id: "-10010069"
  zh_CN: "已经选了任务"
}
rows {
  id: "-10010070"
  zh_CN: "所选任务不存在"
}
rows {
  id: "-10010071"
  zh_CN: "已邀请好友"
}
rows {
  id: "-10010072"
  zh_CN: "没有邀请该好友"
}
rows {
  id: "-10010073"
  zh_CN: "操作失败"
}
rows {
  id: "-10010074"
  zh_CN: "该好友的申请未找到活已失效"
}
rows {
  id: "-10010075"
  zh_CN: "已有配对的好友"
}
rows {
  id: "-10010076"
  zh_CN: "本阶段任务次数到达上限"
}
rows {
  id: "-10010077"
  zh_CN: "任务状态错误"
}
rows {
  id: "-10010078"
  zh_CN: "不是邀请目标"
}
rows {
  id: "-10010079"
  zh_CN: "邀请信息不匹配"
}
rows {
  id: "-10010080"
  zh_CN: "解锁配置未找到或已达最大数量"
}
rows {
  id: "-10010081"
  zh_CN: "没有可以提醒的内容"
}
rows {
  id: "-10040370"
  zh_CN: "目标不存在"
}
rows {
  id: "-10040371"
  zh_CN: "奖励参数错误"
}
rows {
  id: "-10040372"
  zh_CN: "奖励已领取"
  showType: ECST_FlyTip
}
rows {
  id: "-10040373"
  zh_CN: "不满足领奖条件"
  showType: ECST_FlyTip
}
rows {
  id: "-10041020"
  zh_CN: "动物图鉴活动聚集地配置不存在"
}
rows {
  id: "-10041021"
  zh_CN: "动物图鉴活动探查道具不足"
}
rows {
  id: "-10041022"
  zh_CN: "动物图鉴活动聚集地不能开始诱捕"
}
rows {
  id: "-10041023"
  zh_CN: "动物图鉴活动聚集地开始诱捕失败"
}
rows {
  id: "-10041024"
  zh_CN: "动物图鉴活动物种配置不存在"
}
rows {
  id: "-10041025"
  zh_CN: "动物图鉴活动动物配置不存在"
}
rows {
  id: "-10041026"
  zh_CN: "动物图鉴活动聚集地不能捕获"
}
rows {
  id: "-10041027"
  zh_CN: "动物图鉴活动聚集地捕获失败"
}
rows {
  id: "-10041028"
  zh_CN: "动物图鉴活动不能领取物种图鉴集齐奖励"
}
rows {
  id: "-10041029"
  zh_CN: "动物图鉴活动领取物种图鉴集齐奖励失败"
}
rows {
  id: "-10041030"
  zh_CN: "动物图鉴活动不能领取终极大奖"
}
rows {
  id: "-10041031"
  zh_CN: "动物图鉴活动领取终极大奖失败"
}
rows {
  id: "-10041032"
  zh_CN: "动物图鉴活动奖励已领取"
}
rows {
  id: "-10041033"
  zh_CN: "动物图鉴活动生成赠送信息失败"
}
rows {
  id: "-10041034"
  zh_CN: "动物图鉴活动不能赠送每种动物第一个获得的图鉴"
}
rows {
  id: "-10041035"
  zh_CN: "动物图鉴活动达到每周赠送图鉴次数上限"
}
rows {
  id: "-10041036"
  zh_CN: "动物图鉴活动领取赠送失败"
}
rows {
  id: "-10041037"
  zh_CN: "动物图鉴活动不能领取自己赠送的图鉴"
}
rows {
  id: "-10041038"
  zh_CN: "动物图鉴活动赠送的动物图鉴已经被领取"
}
rows {
  id: "-10041039"
  zh_CN: "动物图鉴活动获取赠送图鉴错误"
}
rows {
  id: "-10060023"
  zh_CN: "已经游玩过了"
}
rows {
  id: "-10060024"
  zh_CN: "评分数量错误"
}
rows {
  id: "-10060025"
  zh_CN: "评分越界"
}
rows {
  id: "-10100076"
  zh_CN: "米大师发货处理前检查失败"
}
rows {
  id: "-10100077"
  zh_CN: "背景主题未持有"
}
rows {
  id: "-10130508"
  zh_CN: "赠送记录不存在"
}
rows {
  id: "-10130509"
  zh_CN: "赠送记录已被删除"
}
rows {
  id: "-10600264"
  zh_CN: "存单的时候version没有检验过"
}
rows {
  id: "-10600265"
  zh_CN: "下架申请失败"
}
rows {
  id: "-10600266"
  zh_CN: "获取地图配置失败"
}
rows {
  id: "-10600267"
  zh_CN: "获取玩家信息失败"
}
rows {
  id: "-10600268"
  zh_CN: "获取社区未读消息数量失败"
}
rows {
  id: "-20000018"
  zh_CN: "红掉消除失败"
}
rows {
  id: "-20000117"
  zh_CN: "今日已经被太多人助力过,请明天再试"
}
rows {
  id: "-20017049"
  zh_CN: "选择指定阵营不合法"
}
rows {
  id: "-20100051"
  zh_CN: "不可切换选中的奖励"
}
rows {
  id: "-20240011"
  zh_CN: "日历奖励已获得"
}
rows {
  id: "-20260025"
  zh_CN: "ugcdatastore 获取cache失败"
}
rows {
  id: "-20260026"
  zh_CN: "ugcdatastore 限频"
}
rows {
  id: "-20260027"
  zh_CN: "ugcdatastore 提示不是单机存档地图"
}
rows {
  id: "-20260028"
  zh_CN: "自定义kv key 为空"
}
rows {
  id: "-20260029"
  zh_CN: "自定义kv value为空"
}
rows {
  id: "-20260030"
  zh_CN: "自定义kv value 存储超限制了"
}
rows {
  id: "-20260031"
  zh_CN: "datastore 存盘时候 任务太多了"
}
rows {
  id: "-20260032"
  zh_CN: "datastore 存盘时候 道具太多了"
}
rows {
  id: "-20300159"
  zh_CN: "送礼开启时间配置格式错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20300160"
  zh_CN: "送礼尚未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20400001"
  zh_CN: "Arena配表错误"
}
rows {
  id: "-20400002"
  zh_CN: "Arena服务不可用"
}
rows {
  id: "-20400003"
  zh_CN: "卡包条件检查不存在"
}
rows {
  id: "-20400004"
  zh_CN: "Arena服务内部错误"
}
rows {
  id: "-20400005"
  zh_CN: "Arena DB异常"
}
rows {
  id: "20400007"
  zh_CN: "没有找到玩家的arena数据"
}
rows {
  id: "20400008"
  zh_CN: "重复添加英雄"
}
rows {
  id: "20400009"
  zh_CN: "没有找到英雄数据"
}
rows {
  id: "20400010"
  zh_CN: "增加英雄失败"
}
rows {
  id: "20400011"
  zh_CN: "重复添加武器"
}
rows {
  id: "20400012"
  zh_CN: "添加武器失败"
}
rows {
  id: "20400013"
  zh_CN: "解锁表中没有该英雄配置"
}
rows {
  id: "20400014"
  zh_CN: "解锁英雄货币不足"
}
rows {
  id: "20400015"
  zh_CN: "解锁英雄条件配置错误"
}
rows {
  id: "20400016"
  zh_CN: "不满足解锁条件"
}
rows {
  id: "20400017"
  zh_CN: "已有该英雄，无需解锁"
}
rows {
  id: "20400018"
  zh_CN: "英雄解锁条件错误"
}
rows {
  id: "20400019"
  zh_CN: "选择的卡牌尚未解锁"
}
rows {
  id: "20400020"
  zh_CN: "卡牌超过可设置上线"
}
rows {
  id: "20400021"
  zh_CN: "没有该卡牌配置"
}
rows {
  id: "20400022"
  zh_CN: "该卡牌尚未解锁"
}
rows {
  id: "20400023"
  zh_CN: "尚未拥有的道具"
}
rows {
  id: "20400024"
  zh_CN: "解锁英雄道具不足"
}
rows {
  id: "20400025"
  zh_CN: "卡包抽卡失败"
}
rows {
  id: "20400026"
  zh_CN: "不在英雄开放时间"
}
rows {
  id: "20400027"
  zh_CN: "解锁英雄游戏次数不够"
}
rows {
  id: "-10010082"
  zh_CN: "邀请好友失败"
}
rows {
  id: "-10130510"
  zh_CN: "与购买商品的版本号不匹配"
}
rows {
  id: "-10400061"
  zh_CN: "社团日志功能未开放"
}
rows {
  id: "-20360001"
  zh_CN: "缓存不存在"
}
rows {
  id: "-20360002"
  zh_CN: "缓存不是master"
}
rows {
  id: "-20360003"
  zh_CN: "缓存不是slave"
}
rows {
  id: "-20360004"
  zh_CN: "Master信息错误"
}
rows {
  id: "-20360005"
  zh_CN: "SlaveMeta不存在"
}
rows {
  id: "-20360006"
  zh_CN: "Slave已下线"
}
rows {
  id: "-20360007"
  zh_CN: "SlaveMeta心跳超时"
}
rows {
  id: "-20360008"
  zh_CN: "同步版本错误"
}
rows {
  id: "-10040380"
  zh_CN: "助力错误"
}
rows {
  id: "-10040381"
  zh_CN: "已签约"
}
rows {
  id: "-10040382"
  zh_CN: "配置错误"
}
rows {
  id: "-10040383"
  zh_CN: "每日助力次数限制"
}
rows {
  id: "-10040384"
  zh_CN: "同玩家助力次数限制"
}
rows {
  id: "-10040385"
  zh_CN: "不允许助力自己"
}
rows {
  id: "-10040386"
  zh_CN: "助力已满"
}
rows {
  id: "-10040387"
  zh_CN: "没有可用的奖励"
}
rows {
  id: "-10040388"
  zh_CN: "奖励已领取"
}
rows {
  id: "-10040389"
  zh_CN: "非法的奖励索引"
}
rows {
  id: "-10040390"
  zh_CN: "奖励标准未达标"
}
rows {
  id: "-10040391"
  zh_CN: "活动关闭"
}
rows {
  id: "-10040392"
  zh_CN: "契约数量过多，服务器内部限制"
}
rows {
  id: "10041077"
  zh_CN: "星界奇遇任务属性错误"
}
rows {
  id: "10041078"
  zh_CN: "任务奖励条件未达标"
}
rows {
  id: "10041079"
  zh_CN: "还有未开启的盲盒"
}
rows {
  id: "10041080"
  zh_CN: "盲盒未找到"
}
rows {
  id: "10041081"
  zh_CN: "盲盒已开"
}
rows {
  id: "10041082"
  zh_CN: "该奖励已领取"
}
rows {
  id: "-10100078"
  zh_CN: "互动组合名字不合法"
}
rows {
  id: "-10600269"
  zh_CN: "创建房间失败，版本过低"
}
rows {
  id: "-10600270"
  zh_CN: "版本打回失败"
}
rows {
  id: "-10600271"
  zh_CN: "地图维护中"
  showType: ECST_FlyTip
}
rows {
  id: "-10800012"
  zh_CN: "暂时无法前往"
  showType: ECST_FlyTip
}
rows {
  id: "-20017050"
  zh_CN: "当前地图维护中"
}
rows {
  id: "-20020442"
  zh_CN: "玩家发布对局招募失败"
}
rows {
  id: "-20020443"
  zh_CN: "房间密码错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20020444"
  zh_CN: "房间没有设置房间加入信息"
}
rows {
  id: "-20100052"
  zh_CN: "BI动态概率为空"
}
rows {
  id: "-20100053"
  zh_CN: "BI动态概率非法"
}
rows {
  id: "-20100054"
  zh_CN: "BI动态概率功能缺失"
}
rows {
  id: "-20100055"
  zh_CN: "奖池不提供免费抽"
}
rows {
  id: "-20100056"
  zh_CN: "奖池免费抽不在在该时间段"
}
rows {
  id: "-20100057"
  zh_CN: "奖池免费抽已耗尽"
}
rows {
  id: "-20100058"
  zh_CN: "奖池免费抽重复领取"
}
rows {
  id: "-20100059"
  zh_CN: "BI动态概率已关闭"
}
rows {
  id: "-20350015"
  zh_CN: "BP付费等级无效"
}
rows {
  id: "-20370001"
  zh_CN: "Profile异常"
}
rows {
  id: "-10010083"
  zh_CN: "亲密关系已达到置顶上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10010084"
  zh_CN: "与该好友无亲密关系，无法置顶"
}
rows {
  id: "-10010085"
  zh_CN: "与该好友为情侣关系，无需置顶"
}
rows {
  id: "-10010086"
  zh_CN: "该亲密任务已完成"
  showType: ECST_FlyTip
}
rows {
  id: "-10010087"
  zh_CN: "你已邀请其他好友，需取消邀请才能同意其他好好友的的邀请"
}
rows {
  id: "-10130511"
  zh_CN: "对方已拥有通行证"
  showType: ECST_FlyTip
}
rows {
  id: "-10600272"
  zh_CN: "上次商业化合理性审核未通过，请在修改商业化内容后再次提交申请"
  showType: ECST_FlyTip
}
rows {
  id: "-10140046"
  zh_CN: "排行榜后端不支持该操作"
}
rows {
  id: "-20000019"
  zh_CN: "领奖前置检查失败"
}
rows {
  id: "-20300161"
  zh_CN: "水族箱尚未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20300162"
  zh_CN: "水族箱已经开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20300163"
  zh_CN: "水族箱鱼位未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-20300164"
  zh_CN: "鱼摊等级不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20390001"
  zh_CN: "农场小屋访客已满"
  showType: ECST_FlyTip
}
rows {
  id: "-20390002"
  zh_CN: "农场小屋找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20390003"
  zh_CN: "匹配中不能进入"
  showType: ECST_FlyTip
}
rows {
  id: "-20390004"
  zh_CN: "被屏蔽了"
  showType: ECST_FlyTip
}
rows {
  id: "-20390005"
  zh_CN: "屏蔽了对方"
  showType: ECST_FlyTip
}
rows {
  id: "-20390006"
  zh_CN: "对方农场的版本过低，无法访问"
  showType: ECST_FlyTip
}
rows {
  id: "-20390007"
  zh_CN: "自己尚未创建农场小屋"
  showType: ECST_FlyTip
}
rows {
  id: "-20390008"
  zh_CN: "农场小屋当前房间找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20390009"
  zh_CN: "农场小屋建筑配置找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20390010"
  zh_CN: "农场小屋配置找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20390011"
  zh_CN: "农场小屋扩建配置找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20390012"
  zh_CN: "农场小屋扩建等级不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20390013"
  zh_CN: "农场小屋家具未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-20390014"
  zh_CN: "不在自己的农场小屋"
  showType: ECST_FlyTip
}
rows {
  id: "-20390015"
  zh_CN: "农场小屋家具不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20390016"
  zh_CN: "农场小屋不能操作非家具类道具"
  showType: ECST_FlyTip
}
rows {
  id: "-20390017"
  zh_CN: "农场家具模型大小配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20390018"
  zh_CN: "加工器放置数量超过总上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20390020"
  zh_CN: "找不到加工器"
  showType: ECST_FlyTip
}
rows {
  id: "-20390021"
  zh_CN: "加工器已经有加工物"
  showType: ECST_FlyTip
}
rows {
  id: "-20390022"
  zh_CN: "加工器配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20390023"
  zh_CN: "加工器是空的"
  showType: ECST_FlyTip
}
rows {
  id: "-20390024"
  zh_CN: "养殖物不能加工"
  showType: ECST_FlyTip
}
rows {
  id: "-20390025"
  zh_CN: "农场小屋家具超出购买数量"
  showType: ECST_FlyTip
}
rows {
  id: "-20390026"
  zh_CN: "道具不支持当前操作"
  showType: ECST_FlyTip
}
rows {
  id: "-20390027"
  zh_CN: "农场小屋家具超出售卖数量"
  showType: ECST_FlyTip
}
rows {
  id: "-20390028"
  zh_CN: "农场小屋地格与家具不匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-20390029"
  zh_CN: "农场小屋地格冲突"
  showType: ECST_FlyTip
}
rows {
  id: "-20390030"
  zh_CN: "农场小屋家具超出可放置数量"
  showType: ECST_FlyTip
}
rows {
  id: "-20390031"
  zh_CN: "农场小屋家具颜色不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20390032"
  zh_CN: "农场小屋功能未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20300165"
  zh_CN: "暂未开放，敬请期待"
  showType: ECST_FlyTip
}
rows {
  id: "-20390033"
  zh_CN: "小屋找不到农场"
  showType: ECST_FlyTip
}
rows {
  id: "-20360034"
  zh_CN: "该农场现在不能进入"
  showType: ECST_FlyTip
}
rows {
  id: "-10100081"
  zh_CN: "米大师商品ID开关关闭"
  showType: ECST_FlyTip
}
rows {
  id: "-10100082"
  zh_CN: "米大师商品ID所属模块开关关闭"
  showType: ECST_FlyTip
}
rows {
  id: "-10130513"
  zh_CN: "好友版本号较低不能赠送"
  showType: ECST_FlyTip
}
rows {
  id: "-10130514"
  zh_CN: "好友版本号较低不能索要"
  showType: ECST_FlyTip
}
rows {
  id: "20400006"
  zh_CN: "20400006"
}
rows {
  id: "-98"
  zh_CN: "CS协议转发未实现"
}
rows {
  id: "-99"
  zh_CN: "CS协议转发失败"
}
rows {
  id: "-10040374"
  zh_CN: "新玩家或回归玩家需在3天内绑定哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10040375"
  zh_CN: "你不是新玩家或回归玩家，无法绑定哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10040376"
  zh_CN: "邀请码输入错误"
  showType: ECST_FlyTip
}
rows {
  id: "10041060"
  zh_CN: "扣除骰子失败"
}
rows {
  id: "10041070"
  zh_CN: "棋盘生成格子失败"
}
rows {
  id: "10041071"
  zh_CN: "棋盘位置非法"
}
rows {
  id: "10041072"
  zh_CN: "格子挖掘"
}
rows {
  id: "10041073"
  zh_CN: "抽奖失败"
}
rows {
  id: "10041074"
  zh_CN: "生成棋盘事件失败"
}
rows {
  id: "10041075"
  zh_CN: "奖励配置错误"
}
rows {
  id: "10041076"
  zh_CN: "扣除消耗出错"
}
rows {
  id: "10041083"
  zh_CN: "已超过可预约时间"
}
rows {
  id: "-10041083"
  zh_CN: "未加入任何队伍"
}
rows {
  id: "-10041084"
  zh_CN: "已经加入队伍"
}
rows {
  id: "-10041085"
  zh_CN: "小队数据不存在"
}
rows {
  id: "-10041086"
  zh_CN: "小队成员已满"
}
rows {
  id: "-10041087"
  zh_CN: "当前状态不能退出队伍"
}
rows {
  id: "-10041088"
  zh_CN: "当前状态不能加入队伍"
}
rows {
  id: "-10041089"
  zh_CN: "不是队长不能踢人"
}
rows {
  id: "-10041091"
  zh_CN: "队伍里找不到该玩家"
}
rows {
  id: "-10041092"
  zh_CN: "队员已购买不能踢"
}
rows {
  id: "-10041093"
  zh_CN: "已经加入该拼团"
}
rows {
  id: "-10041094"
  zh_CN: "购买后即可领奖"
}
rows {
  id: "-10041095"
  zh_CN: "活动版本号受限，不能加入"
}
rows {
  id: "-10060026"
  zh_CN: "小游戏授权非法操作"
}
rows {
  id: "-10071001"
  zh_CN: "CS转发未知错误"
}
rows {
  id: "-10071002"
  zh_CN: "CS转发错误"
}
rows {
  id: "-10071003"
  zh_CN: "CS转发超时"
}
rows {
  id: "-10071004"
  zh_CN: "CS转发协议解析错误"
}
rows {
  id: "-10071005"
  zh_CN: "CS转发缺少对应的Handler"
}
rows {
  id: "-10071006"
  zh_CN: "CS转发处理错误"
}
rows {
  id: "-10071007"
  zh_CN: "CS转发处理超时"
}
rows {
  id: "-10071008"
  zh_CN: "CS转发服务类型无法识别"
}
rows {
  id: "-10100083"
  zh_CN: "当前穿着套装进入该玩法后会使用替代方案噢"
}
rows {
  id: "-10100084"
  zh_CN: "外观与模式不匹配，进入游戏后启动备用外观"
}
rows {
  id: "-10130101"
  zh_CN: "商品投放修改配置解析失败"
}
rows {
  id: "-10130102"
  zh_CN: "商品投放配置字段解析失败"
}
rows {
  id: "-10130103"
  zh_CN: "商品投放字段key与info的类型不匹配"
}
rows {
  id: "-10130104"
  zh_CN: "商品投放字段类型没有meta配置"
}
rows {
  id: "-10130105"
  zh_CN: "商品投放字段数据类型无法识别"
}
rows {
  id: "-10130106"
  zh_CN: "商品投放字段值与类型不匹配"
}
rows {
  id: "-10130107"
  zh_CN: "商品投放配置key找不到对应模块配置"
}
rows {
  id: "-10130108"
  zh_CN: "商品投放配置字段找不到对应模块配置字段"
}
rows {
  id: "-10130512"
  zh_CN: "商品购买产出检测失败"
}
rows {
  id: "-10600273"
  zh_CN: "内购地图不允许玩家下架，只能申请下架"
}
rows {
  id: "-20000020"
  zh_CN: "领奖前置检查index重复"
}
rows {
  id: "-20000021"
  zh_CN: "领奖前置检查count非法"
}
rows {
  id: "-20000022"
  zh_CN: "领奖前置检查领奖list为空"
}
rows {
  id: "-20000118"
  zh_CN: "许愿树-不允许许愿(不在许愿窗口期|当日已许愿|许愿次数限制)"
}
rows {
  id: "-20000119"
  zh_CN: "许愿树-当前礼包不可被选(已经在许愿记录中|背包已拥有)"
}
rows {
  id: "-20000120"
  zh_CN: "许愿树-不允许领取奖励(已领取|不在领取时间范围)"
}
rows {
  id: "-20000121"
  zh_CN: "许愿树-预约奖励已领取"
}
rows {
  id: "-20000122"
  zh_CN: "许愿树-尚未许愿"
}
rows {
  id: "-20000123"
  zh_CN: "许愿树-不在许愿时间"
}
rows {
  id: "-20000124"
  zh_CN: "许愿树-配置空"
}
rows {
  id: "-20000125"
  zh_CN: "心愿活动-openid转换异常"
}
rows {
  id: "-20000126"
  zh_CN: "心愿活动-今日助力最大次数"
}
rows {
  id: "-20000127"
  zh_CN: "心愿活动-已经给该玩家助力过"
}
rows {
  id: "-20000128"
  zh_CN: "钓鱼名人堂-活动配置空"
}
rows {
  id: "-20017051"
  zh_CN: "目标地图维护中"
}
rows {
  id: "-20020445"
  zh_CN: "BattleSvr迁移DscRegion失败"
}
rows {
  id: "-20100060"
  zh_CN: "试图暂存星钻支付的抽奖结果"
}
rows {
  id: "-20100061"
  zh_CN: "未选择抽奖试水结果"
}
rows {
  id: "-20100062"
  zh_CN: "操作太频繁，请重试"
  showType: ECST_FlyTip
}
rows {
  id: "-20100063"
  zh_CN: "抽奖不允许试水"
}
rows {
  id: "-20100064"
  zh_CN: "抽奖试水失败"
}
rows {
  id: "-20100065"
  zh_CN: "抽奖试水开销冲突"
}
rows {
  id: "-20100066"
  zh_CN: "未找到抽奖试水结果"
}
rows {
  id: "-20100067"
  zh_CN: "抽奖试水结果与的奖池组ID不匹配"
}
rows {
  id: "-20100068"
  zh_CN: "抽奖试水结果对应起始记录丢失"
}
rows {
  id: "-20191009"
  zh_CN: "逻辑错误"
}
rows {
  id: "-20211303"
  zh_CN: "没有配置"
}
rows {
  id: "-203960034"
  zh_CN: "农场小屋开关关闭"
  showType: ECST_FlyTip
}
rows {
  id: "-20390035"
  zh_CN: "农场小屋被下架"
  showType: ECST_FlyTip
}
rows {
  id: "-20390036"
  zh_CN: "加工器计算完成时间错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20390037"
  zh_CN: "加工器计算容量错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20390039"
  zh_CN: "未找到在线陌生人"
  showType: ECST_FlyTip
}
rows {
  id: "-20390050"
  zh_CN: "查找在线陌生人频率过高"
  showType: ECST_FlyTip
}
rows {
  id: "-20390051"
  zh_CN: "小屋房间家具摆放不合规"
  showType: ECST_FlyTip
}
rows {
  id: "20400028"
  zh_CN: "无效的购买英雄折扣配置"
}
rows {
  id: "20400029"
  zh_CN: "该条件不能用于解锁"
}
rows {
  id: "20400030"
  zh_CN: "英雄未解锁"
}
rows {
  id: "20400031"
  zh_CN: "前置条件未完成"
}
rows {
  id: "-20400032"
  zh_CN: "cs转发Arena找不到handle"
}
rows {
  id: "-20400033"
  zh_CN: "cs转发Arena协议解析错误"
}
rows {
  id: "-20400034"
  zh_CN: "cs转发Arena处理超时"
}
rows {
  id: "-20400035"
  zh_CN: "cs转发Arena处理错误"
}
rows {
  id: "-20400036"
  zh_CN: "cs转发Arena错误"
}
rows {
  id: "-20400037"
  zh_CN: "cs转发Arena错误"
}
rows {
  id: "-20400038"
  zh_CN: "英雄皮肤与英雄不匹配"
}
rows {
  id: "-20400039"
  zh_CN: "Arena局内皮肤不能在局外使用"
}
rows {
  id: "-10041096"
  zh_CN: "当前轮次已开启"
}
rows {
  id: "-10041097"
  zh_CN: "参数错误，不是当前轮次"
}
rows {
  id: "-10041098"
  zh_CN: "找不到幸运币转盘配置"
}
rows {
  id: "-10041099"
  zh_CN: "前置轮次未开启"
}
rows {
  id: "-10041100"
  zh_CN: "幸运币转盘发货失败"
}
rows {
  id: "-10060027"
  zh_CN: "小游戏授权查询错误"
}
rows {
  id: "-10060028"
  zh_CN: "小游戏授权地址错误"
}
rows {
  id: "-10100085"
  zh_CN: "登陆态更新失败"
}
rows {
  id: "-20390052"
  zh_CN: "小屋房间缩小不能小于原始大小"
  showType: ECST_FlyTip
}
rows {
  id: "-20110022"
  zh_CN: "对方队伍已经满员了"
  showType: ECST_FlyTip
}
rows {
  id: "-10170040"
  zh_CN: "目前尚未达到该频道聊天等级限制！"
  showType: ECST_FlyTip
}
rows {
  id: "-10170041"
  zh_CN: "该频道不支持当前消息类型！"
  showType: ECST_FlyTip
}
rows {
  id: "-10170042"
  zh_CN: "该频道维护中！"
  showType: ECST_FlyTip
}
rows {
  id: "-10170043"
  zh_CN: "频道维护中！"
  showType: ECST_FlyTip
}
rows {
  id: "-10130515"
  zh_CN: "只有ios小游戏端可以索要"
}
rows {
  id: "-10130516"
  zh_CN: "不允许索取"
}
rows {
  id: "-20100069"
  zh_CN: "已提前获得大奖"
}
rows {
  id: "-20390053"
  zh_CN: "农场小屋家具无法买卖"
  showType: ECST_FlyTip
}
rows {
  id: "-1003975"
  zh_CN: "星世界地图排行榜功能已被封禁"
}
rows {
  id: "-303"
  zh_CN: "irpc发送失败"
}
rows {
  id: "-10000100"
  zh_CN: "兽人进入地图的ugcId和选择的不是一个"
}
rows {
  id: "-10000101"
  zh_CN: "兽人进入地图的levelType错误"
}
rows {
  id: "-10000102"
  zh_CN: "ugc推荐集合type错误"
}
rows {
  id: "-10041101"
  zh_CN: "已翻开花笺。"
  showType: ECST_FlyTip
}
rows {
  id: "-10100086"
  zh_CN: "未到使用时间，新赛季开启时自动打开"
  showType: ECST_FlyTip
}
rows {
  id: "-10130517"
  zh_CN: "不允许赠送"
}
rows {
  id: "-10600280"
  zh_CN: "成就数量超过限制"
}
rows {
  id: "-10600281"
  zh_CN: "地图是已发布的"
}
rows {
  id: "-10600282"
  zh_CN: "成就加载失败"
}
rows {
  id: "-10600283"
  zh_CN: "Ugc成就被关闭了"
}
rows {
  id: "-10600284"
  zh_CN: "已发布的地图删除了成绩数据"
}
rows {
  id: "-10600285"
  zh_CN: "已发布草稿地图删除了成绩数据"
}
rows {
  id: "-10600275"
  zh_CN: "创建单人游玩对局失败"
}
rows {
  id: "-10600276"
  zh_CN: "UGC单人游玩对局结算失败"
}
rows {
  id: "-10600274"
  zh_CN: "推荐集合失败"
}
rows {
  id: "-20000129"
  zh_CN: "没有找到服装"
  showType: ECST_FlyTip
}
rows {
  id: "-20000130"
  zh_CN: "服装幻化过于频繁"
  showType: ECST_FlyTip
}
rows {
  id: "-20000131"
  zh_CN: "服装幻化失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20000132"
  zh_CN: "没有找到可以幻化的服装"
  showType: ECST_FlyTip
}
rows {
  id: "-20000133"
  zh_CN: "暂不可以穿戴服装"
  showType: ECST_FlyTip
}
rows {
  id: "-20000134"
  zh_CN: "没有获取对应的花笺"
  showType: ECST_FlyTip
}
rows {
  id: "-20000135"
  zh_CN: "解锁花笺失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20000136"
  zh_CN: "该服装已收集"
  showType: ECST_FlyTip
}
rows {
  id: "-20000137"
  zh_CN: "赠送祈福牌获取亲密度次数到上限"
}
rows {
  id: "-20010228"
  zh_CN: "不支持直接匹配"
}
rows {
  id: "-20010229"
  zh_CN: "匹配关联队伍非等待"
}
rows {
  id: "-20010231"
  zh_CN: "匹配关联队伍非匹配中"
}
rows {
  id: "-20020446"
  zh_CN: "队友已经拒绝\\"
}
rows {
  id: "-20020447"
  zh_CN: "同游提议已过期"
}
rows {
  id: "-20100070"
  zh_CN: "抽奖福利卡禁止使用"
}
rows {
  id: "-20100071"
  zh_CN: "抽奖福利卡使用冲突"
}
rows {
  id: "-20100072"
  zh_CN: "抽奖福利卡未找到合适配置"
}
rows {
  id: "-20100073"
  zh_CN: "抽奖福利卡未找到对应的奖池"
}
rows {
  id: "-20260033"
  zh_CN: "showdata 对应功能不存在"
}
rows {
  id: "-20260034"
  zh_CN: "showdata 对应功能未开启"
}
rows {
  id: "-20260035"
  zh_CN: "showdata 数据版本太低"
}
rows {
  id: "-20260036"
  zh_CN: "showdata 操作频率限制"
}
rows {
  id: "-20290004"
  zh_CN: "不能使用体验装扮"
}
rows {
  id: "-20290005"
  zh_CN: "没有启用的装扮"
}
rows {
  id: "20400038"
  zh_CN: "英雄皮肤与英雄不匹配"
}
rows {
  id: "20400039"
  zh_CN: "Arena局内皮肤不能在局外使用"
}
rows {
  id: "-20400050"
  zh_CN: "MOBA打赏未开启"
}
rows {
  id: "-20400051"
  zh_CN: "MOBA打赏入参不合法"
}
rows {
  id: "-20400052"
  zh_CN: "MOBA打赏传递的道具不合法"
}
rows {
  id: "-10130519"
  zh_CN: "请在抢购时间内兑换"
  showType: ECST_FlyTip
}
rows {
  id: "-1003974"
  zh_CN: "封禁UGC自定义成就的编辑与创建与被封禁"
}
rows {
  id: "-1004025"
  zh_CN: "活动不存在"
}
rows {
  id: "-10000103"
  zh_CN: "娱乐向导配置错误"
}
rows {
  id: "-10041103"
  zh_CN: "活动不在开启时间"
}
rows {
  id: "-10041104"
  zh_CN: "AMS任务队列没有初始化"
}
rows {
  id: "-10041105"
  zh_CN: "AMS任务队列已经初始化"
}
rows {
  id: "-10041108"
  zh_CN: "柯南预热活动-没有可答题目"
}
rows {
  id: "-10041109"
  zh_CN: "柯南预热活动-已经领奖过"
}
rows {
  id: "-10041111"
  zh_CN: "活动获奖名单尚未确定，请稍后查询"
}
rows {
  id: "-10041112"
  zh_CN: "该奖项获奖名单不对外公布"
}
rows {
  id: "-10041113"
  zh_CN: "当前不在活动时间范围内"
}
rows {
  id: "-10041114"
  zh_CN: "答题题目已完成"
}
rows {
  id: "-10041115"
  zh_CN: "答题前置题目未完成"
}
rows {
  id: "-10041116"
  zh_CN: "题组没有正确回答完成"
}
rows {
  id: "-10100087"
  zh_CN: "购买失败，价格浮动过大"
}
rows {
  id: "-10140047"
  zh_CN: "榜单为非历史榜单"
}
rows {
  id: "-10140048"
  zh_CN: "平台侧同步数据源未找到"
}
rows {
  id: "-10140049"
  zh_CN: "榜单数据不可同步到平台侧"
}
rows {
  id: "-10170044"
  zh_CN: "话题已过期"
}
rows {
  id: "-10170045"
  zh_CN: "同一天重复点赞话题"
}
rows {
  id: "-10170046"
  zh_CN: "热门话题功能已关闭"
}
rows {
  id: "-10470003"
  zh_CN: "玩家标签道具未获得"
}
rows {
  id: "-10470004"
  zh_CN: "玩家标签签道具配置非法"
}
rows {
  id: "-10800013"
  zh_CN: "道具存量超过最大拥有数量"
  showType: ECST_FlyTip
}
rows {
  id: "-20017052"
  zh_CN: "已无法发起同游"
}
rows {
  id: "-20017053"
  zh_CN: "同游提议已失效"
}
rows {
  id: "-20017054"
  zh_CN: "同游提议已失效"
}
rows {
  id: "-20020448"
  zh_CN: "未开启中途加入"
}
rows {
  id: "-20300166"
  zh_CN: "农场宠物不需要喂食"
  showType: ECST_FlyTip
}
rows {
  id: "-20300167"
  zh_CN: "农场宠物未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20300168"
  zh_CN: "农场宠物已经拥有"
  showType: ECST_FlyTip
}
rows {
  id: "-20300169"
  zh_CN: "农场宠物不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300170"
  zh_CN: "农场宠物存储出错"
  showType: ECST_FlyTip
}
rows {
  id: "-20300171"
  zh_CN: "未开启宠物看家护院"
  showType: ECST_FlyTip
}
rows {
  id: "-20300172"
  zh_CN: "水族馆已经被祈愿到全彩了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300173"
  zh_CN: "已拥有该收藏品图鉴"
  showType: ECST_FlyTip
}
rows {
  id: "-20300174"
  zh_CN: "收藏品图鉴配置未找到"
  showType: ECST_FlyTip
}
rows {
  id: "-20300175"
  zh_CN: "宠物目前饥饿"
  showType: ECST_FlyTip
}
rows {
  id: "-20300176"
  zh_CN: "宠物驱逐失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300177"
  zh_CN: "事件没有该值"
}
rows {
  id: "-20300178"
  zh_CN: "收藏品功能没开"
  showType: ECST_FlyTip
}
rows {
  id: "-20300179"
  zh_CN: "场景掉落捡起忽略"
  showType: ECST_FlyTip
}
rows {
  id: "-20300180"
  zh_CN: "您被宠物赶走了，30秒内不能拜访该农场"
  showType: ECST_FlyTip
}
rows {
  id: "-20300181"
  zh_CN: "场景掉落物过期了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300182"
  zh_CN: "场景掉落物不属于自己"
  showType: ECST_FlyTip
}
rows {
  id: "-20300190"
  zh_CN: "农场天气配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300191"
  zh_CN: "农场天气未结束"
  showType: ECST_FlyTip
}
rows {
  id: "-20300192"
  zh_CN: "雨天成熟作物受保护中"
  showType: ECST_FlyTip
}
rows {
  id: "-20300193"
  zh_CN: "宠物祈愿没有跟随目标"
  showType: ECST_FlyTip
}
rows {
  id: "-20300194"
  zh_CN: "宠物礼物未准备"
  showType: ECST_FlyTip
}
rows {
  id: "-20300195"
  zh_CN: "宠物配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300196"
  zh_CN: "宠物互动配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300197"
  zh_CN: "宠物互动要求好感度不够"
  showType: ECST_FlyTip
}
rows {
  id: "-20300198"
  zh_CN: "宠物祈愿跟随记录解析失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300199"
  zh_CN: "水族馆重置cd"
  showType: ECST_FlyTip
}
rows {
  id: "-20300209"
  zh_CN: "事件不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300210"
  zh_CN: "数据过大"
  showType: ECST_FlyTip
}
rows {
  id: "-20350016"
  zh_CN: "BP付费升级不满足购买条件"
}
rows {
  id: "-20390054"
  zh_CN: "农场小屋家具皮肤未拥有"
  showType: ECST_FlyTip
}
rows {
  id: "-20390055"
  zh_CN: "农场小屋家具皮肤配置找不到"
  showType: ECST_FlyTip
}
rows {
  id: "20400040"
  zh_CN: "开放表中没有该英雄"
}
rows {
  id: "-20400053"
  zh_CN: "关卡评论长度超出范围"
}
rows {
  id: "-20400054"
  zh_CN: "关卡ID不合法"
}
rows {
  id: "-20400055"
  zh_CN: "关卡options不合法"
}
rows {
  id: "-20400056"
  zh_CN: "关卡optionsArrayText不合法"
}
rows {
  id: "-20400057"
  zh_CN: "关卡评价写库失败"
}
rows {
  id: "-20400058"
  zh_CN: "关卡评论不存在"
}
rows {
  id: "-20400059"
  zh_CN: "picKey未找到图片"
}
rows {
  id: "-20400060"
  zh_CN: "picKey已经点过攒了"
}
rows {
  id: "-20400062"
  zh_CN: "每日图片点赞数量限制"
}
rows {
  id: "-20400063"
  zh_CN: "狼人满减活动商品配置为空"
}
rows {
  id: "-20400064"
  zh_CN: "狼人满减活动优惠券配置为空"
}
rows {
  id: "-20400065"
  zh_CN: "狼人满减活动购物车数量超过配置限制"
}
rows {
  id: "-20400066"
  zh_CN: "狼人满减活动未达到使用券的条件"
}
rows {
  id: "-20400067"
  zh_CN: "狼人满减活动商品不在销售时间范围内"
}
rows {
  id: "-20400068"
  zh_CN: "狼人满减调用米大师接口异常"
}
rows {
  id: "-2000045"
  zh_CN: "鱼塘幸运星排行榜结算中"
  showType: ECST_FlyTip
}
rows {
  id: "-10130520"
  zh_CN: "您的邮件内已有该物品，请前往领取"
  showType: ECST_FlyTip
}
rows {
  id: "-203"
  zh_CN: "rpc限频"
}
rows {
  id: "-1003973"
  zh_CN: "商城赠送使用赠礼卡已被封禁"
  showType: ECST_FlyTip
}
rows {
  id: "-1003972"
  zh_CN: "农场个性签名修改已被封禁"
  showType: ECST_FlyTip
}
rows {
  id: "-10000104"
  zh_CN: "不允许游玩多场景地图"
}
rows {
  id: "-10000105"
  zh_CN: "不允许发布多场景地图"
}
rows {
  id: "-10041040"
  zh_CN: "动物图鉴活动每日领取次数达到上限"
}
rows {
  id: "-10041041"
  zh_CN: "动物图鉴活动获取每日次数错误"
}
rows {
  id: "-10055000"
  zh_CN: "训练活动配置不存在"
}
rows {
  id: "-10055001"
  zh_CN: "训练活动DB错误"
}
rows {
  id: "-10055002"
  zh_CN: "训练活动数据不存在"
}
rows {
  id: "-10055003"
  zh_CN: "积分不足"
}
rows {
  id: "-10055004"
  zh_CN: "训练活动对象数据不存在"
}
rows {
  id: "-10055005"
  zh_CN: "训练对象已经完训练"
}
rows {
  id: "-10055006"
  zh_CN: "训练活动数据更新失败"
}
rows {
  id: "-10055007"
  zh_CN: "超过每周被助力最大限制"
}
rows {
  id: "-10055008"
  zh_CN: "本周已经被该好友助力过"
}
rows {
  id: "-10055009"
  zh_CN: "增加积分非法"
}
rows {
  id: "-10055010"
  zh_CN: "协助好友培养rpc失败"
}
rows {
  id: "-10130024"
  zh_CN: "赠礼卡参数错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10130025"
  zh_CN: "该商品不能选择赠礼卡"
  showType: ECST_FlyTip
}
rows {
  id: "-10130026"
  zh_CN: "赠礼卡祝福语参数不合法"
  showType: ECST_FlyTip
}
rows {
  id: "-10600286"
  zh_CN: "创建单人游玩对局失败"
}
rows {
  id: "-10600287"
  zh_CN: "UGC单人游玩对局结算失败"
}
rows {
  id: "-10600288"
  zh_CN: "推荐集合失败"
}
rows {
  id: "-10600289"
  zh_CN: "主场景不能删除"
}
rows {
  id: "-20000138"
  zh_CN: "重复领取抓捕奖励"
}
rows {
  id: "-20000139"
  zh_CN: "重复领取免费奖励"
}
rows {
  id: "-20000140"
  zh_CN: "抓捕奖励配置异常"
}
rows {
  id: "-20000141"
  zh_CN: "抓捕道具配置异常"
}
rows {
  id: "-20000142"
  zh_CN: "扣除道具失败"
}
rows {
  id: "-20000143"
  zh_CN: "移动失败"
}
rows {
  id: "-20000144"
  zh_CN: "抓捕奖励领取不满足条件"
}
rows {
  id: "-20100074"
  zh_CN: "奖励选择超出上限"
}
rows {
  id: "-20100075"
  zh_CN: "拒绝删除现有奖励选择"
}
rows {
  id: "-20100076"
  zh_CN: "奖励选择与现有选择重复"
}
rows {
  id: "-20300200"
  zh_CN: "宠物名字为空"
  showType: ECST_FlyTip
}
rows {
  id: "-20300201"
  zh_CN: "宠物名字过长"
  showType: ECST_FlyTip
}
rows {
  id: "-20300202"
  zh_CN: "含有中文、数字、英文以外的内容"
  showType: ECST_FlyTip
}
rows {
  id: "-20300203"
  zh_CN: "宠物改名冷却中"
  showType: ECST_FlyTip
}
rows {
  id: "-20300204"
  zh_CN: "宠物已经在农场中了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300205"
  zh_CN: "宠物服装配置未找到"
  showType: ECST_FlyTip
}
rows {
  id: "-20300206"
  zh_CN: "宠物服装冲突"
  showType: ECST_FlyTip
}
rows {
  id: "-20300207"
  zh_CN: "宠物服装未拥有"
  showType: ECST_FlyTip
}
rows {
  id: "-20300208"
  zh_CN: "宠物服装已拥有"
  showType: ECST_FlyTip
}
rows {
  id: "-20300211"
  zh_CN: "改名不能和原名字一样"
  showType: ECST_FlyTip
}
rows {
  id: "-20300212"
  zh_CN: "下一个事件不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300267"
  zh_CN: "农场村民未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20300269"
  zh_CN: "农场村民不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300270"
  zh_CN: "农场村民存储出错"
  showType: ECST_FlyTip
}
rows {
  id: "-20300271"
  zh_CN: "农场天赋未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20300272"
  zh_CN: "农场天赋前置没完成"
  showType: ECST_FlyTip
}
rows {
  id: "-20300273"
  zh_CN: "农场天赋 作物精华已领取过"
  showType: ECST_FlyTip
}
rows {
  id: "-20300275"
  zh_CN: "农场该村民今日收礼已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300276"
  zh_CN: "农场给村民送礼库存不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20300277"
  zh_CN: "农场给村民送礼类型不支持"
  showType: ECST_FlyTip
}
rows {
  id: "-20300278"
  zh_CN: "农场给村民送礼礼品品质不支持"
  showType: ECST_FlyTip
}
rows {
  id: "-20300279"
  zh_CN: "任务已存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300280"
  zh_CN: "任务不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300281"
  zh_CN: "任务未完成"
  showType: ECST_FlyTip
}
rows {
  id: "-20300282"
  zh_CN: "霸王花无法祈愿"
  showType: ECST_FlyTip
}
rows {
  id: "-20300283"
  zh_CN: "任务未触发"
  showType: ECST_FlyTip
}
rows {
  id: "-20300292"
  zh_CN: "玩家不在自己农场"
  showType: ECST_FlyTip
}
rows {
  id: "-20300293"
  zh_CN: "操作没有被邀请的村民"
  showType: ECST_FlyTip
}
rows {
  id: "-20300294"
  zh_CN: "邀请的不是等待邀请的村民"
  showType: ECST_FlyTip
}
rows {
  id: "-20300295"
  zh_CN: "霸王花处于保护期内"
  showType: ECST_FlyTip
}
rows {
  id: "-20300296"
  zh_CN: "村民交互配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300297"
  zh_CN: "村民不支持的交互"
  showType: ECST_FlyTip
}
rows {
  id: "-20300298"
  zh_CN: "非农场村民支持的协议"
  showType: ECST_FlyTip
}
rows {
  id: "-20300230"
  zh_CN: "农场村民满好感度礼物状态异常"
  showType: ECST_FlyTip
}
rows {
  id: "-20300231"
  zh_CN: "农场村民礼物没准备好"
  showType: ECST_FlyTip
}
rows {
  id: "-20300299"
  zh_CN: "任务无法触发"
  showType: ECST_FlyTip
}
rows {
  id: "-20300300"
  zh_CN: "霸王花种植数量限制"
  showType: ECST_FlyTip
}
rows {
  id: "-20300302"
  zh_CN: "农场村民数量已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300303"
  zh_CN: "农场村民已入住"
  showType: ECST_FlyTip
}
rows {
  id: "-20300304"
  zh_CN: "农场npc系统未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20300305"
  zh_CN: "还没有拥有通灵兽哦"
  showType: ECST_FlyTip
}
rows {
  id: "-20300306"
  zh_CN: "农场小红狐偷菜教学已完成"
  showType: ECST_FlyTip
}
rows {
  id: "-20300307"
  zh_CN: "农场小红狐偷菜教学不可做"
  showType: ECST_FlyTip
}
rows {
  id: "-20300308"
  zh_CN: "禁止改名"
  showType: ECST_FlyTip
}
rows {
  id: "-20390056"
  zh_CN: "农场小屋家具无法批量购买"
  showType: ECST_FlyTip
}
rows {
  id: "-20390058"
  zh_CN: "农场小屋家具星钻购买尚未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20390059"
  zh_CN: "农场小屋家具米大师不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20400041"
  zh_CN: "Arena执行GMCmd失败"
}
rows {
  id: "-20400069"
  zh_CN: "狼人满减活动优惠券数量不足"
}
rows {
  id: "-20400070"
  zh_CN: "狼人满减活动订单结算中, 请稍后再试"
}
rows {
  id: "-20400071"
  zh_CN: "不可填写该内容"
  showType: ECST_FlyTip
}
rows {
  id: "-10130521"
  zh_CN: "该好友已经被赠送过此物品，无法再次赠送"
  showType: ECST_FlyTip
}
rows {
  id: "-10130525"
  zh_CN: "该商品不能自购，只能赠送"
  showType: ECST_FlyTip
}
rows {
  id: "-10130027"
  zh_CN: "心愿单已满，不能再添加"
  showType: ECST_FlyTip
}
rows {
  id: "-10130028"
  zh_CN: "不是好友不能关注心愿单"
  showType: ECST_FlyTip
}
rows {
  id: "-10130029"
  zh_CN: "已拥有，不能添加到心愿单"
  showType: ECST_FlyTip
}
rows {
  id: "-10130030"
  zh_CN: "该商品未上架售卖，不能添加到心愿单"
  showType: ECST_FlyTip
}
rows {
  id: "-10130031"
  zh_CN: "心愿单仅限不可重复拥有的物品"
  showType: ECST_FlyTip
}
rows {
  id: "-10130032"
  zh_CN: "该商品不在心愿单里"
  showType: ECST_FlyTip
}
rows {
  id: "-1004026"
  zh_CN: "不在活动要求版本内，请更新客户端"
}
rows {
  id: "-10000106"
  zh_CN: "不允许导出多场景地图"
}
rows {
  id: "-10000107"
  zh_CN: "ugc推荐集合page错误"
}
rows {
  id: "-10000108"
  zh_CN: "ugc推荐集合page超过了上限错误"
}
rows {
  id: "-10010088"
  zh_CN: "平台好友请求失败"
}
rows {
  id: "-10040393"
  zh_CN: "活动未开放"
}
rows {
  id: "-10040394"
  zh_CN: "用户版本不匹配活动开放条件"
}
rows {
  id: "-10055011"
  zh_CN: "训练活动每次只允许领取一个奖励"
}
rows {
  id: "-10055012"
  zh_CN: "训练对象奖励配置为null"
}
rows {
  id: "-10055013"
  zh_CN: "已经领取过该奖励"
}
rows {
  id: "-10055014"
  zh_CN: "进度不满足"
}
rows {
  id: "-10055015"
  zh_CN: "训练活动奖励配置未找到"
}
rows {
  id: "-10055016"
  zh_CN: "训练活动奖励配置未找到"
}
rows {
  id: "-10055017"
  zh_CN: "训练活动扣除训练积分失败"
}
rows {
  id: "-10055018"
  zh_CN: "训练活动添加训练对象进度失败"
}
rows {
  id: "-10055019"
  zh_CN: "训练所需道具配置错误"
}
rows {
  id: "-10055020"
  zh_CN: "训练所需道具不足"
}
rows {
  id: "-10055021"
  zh_CN: "超过每周助力最大限制"
}
rows {
  id: "-10055022"
  zh_CN: "已经帮助过该对象"
}
rows {
  id: "-10130033"
  zh_CN: "不能添加不允许赠送的商品"
}
rows {
  id: "-10130522"
  zh_CN: "请先抽取商城折扣优惠"
}
rows {
  id: "-10130523"
  zh_CN: "已经抽取过商城折扣优惠"
}
rows {
  id: "-10130524"
  zh_CN: "主题商城购买商品数量限制"
}
rows {
  id: "-10600290"
  zh_CN: "名字无效"
}
rows {
  id: "-10600291"
  zh_CN: "索引无效"
}
rows {
  id: "-10600292"
  zh_CN: "地图信息无效"
}
rows {
  id: "-10600293"
  zh_CN: "地图存储类型为空"
}
rows {
  id: "-10600294"
  zh_CN: "地图version为空"
}
rows {
  id: "-10600295"
  zh_CN: "地图MD5为空"
}
rows {
  id: "-20010232"
  zh_CN: "匹配关联队伍成员不匹配"
}
rows {
  id: "-20017055"
  zh_CN: "其他队伍取消了匹配"
}
rows {
  id: "-20017056"
  zh_CN: "该房间仅房主邀请可加入"
}
rows {
  id: "-20017059"
  zh_CN: "加入失败，登录平台无法游玩该玩法"
  showType: ECST_FlyTip
}
rows {
  id: "-20017060"
  zh_CN: "邀请失败，对方登录平台无法游玩当前玩法"
  showType: ECST_FlyTip
}
rows {
  id: "-20017061"
  zh_CN: "请求失败，登录平台无法游玩对方的玩法"
}
rows {
  id: "-20100077"
  zh_CN: "抽奖积分不匹配"
}
rows {
  id: "-20300313"
  zh_CN: "不需要经验礼物"
  showType: ECST_FlyTip
}
rows {
  id: "-20300314"
  zh_CN: "不需要农场币礼物"
  showType: ECST_FlyTip
}
rows {
  id: "-20300315"
  zh_CN: "不需要鱼礼物"
  showType: ECST_FlyTip
}
rows {
  id: "-20300316"
  zh_CN: "农场村民移除异常"
  showType: ECST_FlyTip
}
rows {
  id: "-20390019"
  zh_CN: "加工器内有加工物，无法拿起"
  showType: ECST_FlyTip
}
rows {
  id: "-20400042"
  zh_CN: "Arena卡牌与英雄不匹配"
}
rows {
  id: "-20400043"
  zh_CN: "Arena卡组内存在重复卡牌"
}
rows {
  id: "-20400044"
  zh_CN: "Arena卡牌品质与槽位不匹配"
}
rows {
  id: "-10472001"
  zh_CN: "奖励未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-10472002"
  zh_CN: "暂无找回次数"
  showType: ECST_FlyTip
}
rows {
  id: "-10472003"
  zh_CN: "不在奖励找回开启时间内"
  showType: ECST_FlyTip
}
rows {
  id: "-10041117"
  zh_CN: "当前阶段错误"
}
rows {
  id: "-10041118"
  zh_CN: "当前狗狗正在旅行"
}
rows {
  id: "10041119"
  zh_CN: "扣除狗粮失败"
}
rows {
  id: "10041120"
  zh_CN: "已经拥有"
}
rows {
  id: "10041121"
  zh_CN: "狗狗不在旅行中"
}
rows {
  id: "-10060029"
  zh_CN: "时尚之路参数错误"
}
rows {
  id: "-10060030"
  zh_CN: "时尚之路等级不足"
}
rows {
  id: "-10060031"
  zh_CN: "时尚之路等级奖励已领取"
}
rows {
  id: "-10060032"
  zh_CN: "时尚之路当前等级没有奖励可以领取"
}
rows {
  id: "-10060033"
  zh_CN: "时尚之路收集奖励已领取"
}
rows {
  id: "-10060035"
  zh_CN: "时尚之路时装尚未集齐"
}
rows {
  id: "-10100088"
  zh_CN: "道具类型不能装扮"
}
rows {
  id: "-10130041"
  zh_CN: "赠礼卡找不到"
}
rows {
  id: "-20017057"
  zh_CN: "功能暂时关闭，敬请期待~"
}
rows {
  id: "-20017058"
  zh_CN: "队伍成员变动，无法发起请求"
  showType: ECST_FlyTip
}
rows {
  id: "-20100078"
  zh_CN: "抽奖暂存箱未找到"
}
rows {
  id: "-20100079"
  zh_CN: "抽奖暂存箱拒绝操作"
}
rows {
  id: "-20100080"
  zh_CN: "抽奖积分不足"
}
rows {
  id: "-20100081"
  zh_CN: "抽奖不允许领奖"
}
rows {
  id: "-20211401"
  zh_CN: "非法配置"
}
rows {
  id: "-20211402"
  zh_CN: "非法配置"
}
rows {
  id: "-20290006"
  zh_CN: "AI伙伴没有开放"
}
rows {
  id: "-20290007"
  zh_CN: "屏蔽词检查超时"
}
rows {
  id: "-20290008"
  zh_CN: "生成伙伴的时间未到"
}
rows {
  id: "-20290009"
  zh_CN: "修改个人信息的时间未到"
}
rows {
  id: "-20290010"
  zh_CN: "字符串太长"
}
rows {
  id: "-20290011"
  zh_CN: "含有不符合规范的字符"
}
rows {
  id: "-20290012"
  zh_CN: "日期不合法"
}
rows {
  id: "-20290013"
  zh_CN: "性别不合法"
}
rows {
  id: "-20290014"
  zh_CN: "参数不合法"
}
rows {
  id: "-20300214"
  zh_CN: "建筑皮肤过期"
  showType: ECST_FlyTip
}
rows {
  id: "-20300324"
  zh_CN: "任务跨天时间未满足"
  showType: ECST_FlyTip
}
rows {
  id: "-20290015"
  zh_CN: "AI伙伴已被封禁"
}
rows {
  id: "-20340100"
  zh_CN: "卡册不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20340105"
  zh_CN: "卡牌获取社交互动信息失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20340106"
  zh_CN: "卡牌社交互动类型不匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-20340109"
  zh_CN: "卡牌社交互动列表为空"
  showType: ECST_FlyTip
}
rows {
  id: "-20340110"
  zh_CN: "卡牌社交互动已被其他玩家抢先完成"
  showType: ECST_FlyTip
}
rows {
  id: "-20340111"
  zh_CN: "卡牌扣除失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20340112"
  zh_CN: "目标玩家账号异常，消息发送失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20340113"
  zh_CN: "卡牌社交互动对象非目标玩家"
  showType: ECST_FlyTip
}
rows {
  id: "-20340115"
  zh_CN: "不支持的社交互动类型"
  showType: ECST_FlyTip
}
rows {
  id: "-20340116"
  zh_CN: "社交互动渠道"
  showType: ECST_FlyTip
}
rows {
  id: "-20340101"
  zh_CN: "卡牌索要冷却中"
  showType: ECST_FlyTip
}
rows {
  id: "-20340102"
  zh_CN: "卡牌数量不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20340103"
  zh_CN: "卡牌已拥有"
  showType: ECST_FlyTip
}
rows {
  id: "-20340104"
  zh_CN: "卡牌赠送次数不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20340107"
  zh_CN: "该卡牌互动已被抢先完成"
  showType: ECST_FlyTip
}
rows {
  id: "-20340108"
  zh_CN: "卡牌互动已过期"
  showType: ECST_FlyTip
}
rows {
  id: "-20340114"
  zh_CN: "发送聊天冷却中"
  showType: ECST_FlyTip
}
rows {
  id: "-10000109"
  zh_CN: "ugc地图发布创作者黑名单"
  showType: ECST_FlyTip
}
rows {
  id: "10041122"
  zh_CN: "奖励已经都领完了"
}
rows {
  id: "-10041125"
  zh_CN: "没有找到阵营PK配置"
}
rows {
  id: "-10041126"
  zh_CN: "没有找到找到用户选择的阵营"
}
rows {
  id: "-10060036"
  zh_CN: "时尚之路收集配置不存在"
}
rows {
  id: "-10060037"
  zh_CN: "时尚之路收集奖励配置错误"
}
rows {
  id: "-10600296"
  zh_CN: "不能重置通关数据"
}
rows {
  id: "-10600297"
  zh_CN: "不是主创不能拷贝图"
}
rows {
  id: "-10600298"
  zh_CN: "地图评审状态改变失败"
}
rows {
  id: "-10600299"
  zh_CN: "地图类型不允许评审"
}
rows {
  id: "-10600300"
  zh_CN: "地图正在评估中"
}
rows {
  id: "-10600301"
  zh_CN: "操作太过频繁"
}
rows {
  id: "-20100082"
  zh_CN: "抽奖跳过这次请求"
}
rows {
  id: "-20211403"
  zh_CN: "分配分片失败"
}
rows {
  id: "-20211404"
  zh_CN: "订阅失败"
}
rows {
  id: "-20211405"
  zh_CN: "非法参数"
}
rows {
  id: "-20211406"
  zh_CN: "取消订阅失败"
}
rows {
  id: "-20211407"
  zh_CN: "查询分片失败"
}
rows {
  id: "-20211408"
  zh_CN: "分配主题失败"
}
rows {
  id: "-20211409"
  zh_CN: "分配主题失败"
}
rows {
  id: "-20290016"
  zh_CN: "禁止使用"
}
rows {
  id: "-20290017"
  zh_CN: "更新个人信息失败"
}
rows {
  id: "-20290018"
  zh_CN: "创建AI伙伴失败"
}
rows {
  id: "-20300213"
  zh_CN: "云游商人道具已经收满了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300337"
  zh_CN: "该鱼不能放水族箱"
  showType: ECST_FlyTip
}
rows {
  id: "-20300338"
  zh_CN: "农场找不到红包"
  showType: ECST_FlyTip
}
rows {
  id: "-20300339"
  zh_CN: "当前场景红包已到达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300340"
  zh_CN: "农场打开红包失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300341"
  zh_CN: "农场红包模块找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20340117"
  zh_CN: "不能完成自己的交易请求"
}
rows {
  id: "-20340118"
  zh_CN: "卡牌未加载"
}
rows {
  id: "-20340119"
  zh_CN: "卡牌索要玩家数量超过限制"
}
rows {
  id: "-20390060"
  zh_CN: "农场小屋找不到红包"
  showType: ECST_FlyTip
}
rows {
  id: "-20390061"
  zh_CN: "农场小屋创建红包失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20390062"
  zh_CN: "农场小屋打开红包失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10170047"
  zh_CN: "当前频道维护升级中，请稍后重试~"
}
rows {
  id: "-20030015"
  zh_CN: "当前状态无法匹配，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-20030016"
  zh_CN: "匹配未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20030017"
  zh_CN: "匹配ID错误"
}
rows {
  id: "-20030018"
  zh_CN: "取消匹配失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20030019"
  zh_CN: "当前状态无法匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-20450001"
  zh_CN: "-20450001"
}
rows {
  id: "-20450002"
  zh_CN: "-20450002"
}
rows {
  id: "-20450003"
  zh_CN: "-20450003"
}
rows {
  id: "-20450004"
  zh_CN: "-20450004"
}
rows {
  id: "-10000110"
  zh_CN: "ugc地图发布审核状态错误"
}
rows {
  id: "-10041120"
  zh_CN: "无效的答案"
}
rows {
  id: "-10600302"
  zh_CN: "发起评审超过限制"
}
rows {
  id: "-20000023"
  zh_CN: "无法使用道具快速完成"
}
rows {
  id: "-20000024"
  zh_CN: "快速完成任务道具不足"
}
rows {
  id: "-2000146"
  zh_CN: "时装基金未买时装"
}
rows {
  id: "-2000147"
  zh_CN: "时装基金未配置未找到"
}
rows {
  id: "-2000148"
  zh_CN: "时装基金返还代币重复领取"
}
rows {
  id: "-2000149"
  zh_CN: "时装基金返还代币不在时间内"
}
rows {
  id: "-2000150"
  zh_CN: "时装基金代币返还错误类型"
}
rows {
  id: "-20030020"
  zh_CN: "其他玩家断开连接，请重新匹配"
  showType: ECST_FlyTip
}
rows {
  id: "-20300359"
  zh_CN: "无人机等待霸王花吞食中"
  showType: ECST_FlyTip
}
rows {
  id: "-20450000"
  zh_CN: "async服务不存在"
}
rows {
  id: "-20380062"
  zh_CN: "-20380062"
}
rows {
  id: "-304"
  zh_CN: "配置不存在"
}
rows {
  id: "-305"
  zh_CN: "implObj获取失败"
}
rows {
  id: "-10701001"
  zh_CN: "NPC系列id参数错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10701002"
  zh_CN: "农场等级不足无法许愿"
  showType: ECST_FlyTip
}
rows {
  id: "-10701004"
  zh_CN: "今日已经许愿过了"
  showType: ECST_FlyTip
}
rows {
  id: "-10701005"
  zh_CN: "许愿结果配置为空"
  showType: ECST_FlyTip
}
rows {
  id: "-10701006"
  zh_CN: "许愿结果配置为空"
  showType: ECST_FlyTip
}
rows {
  id: "-10701007"
  zh_CN: "许愿类型异常"
  showType: ECST_FlyTip
}
rows {
  id: "-10701008"
  zh_CN: "立即召唤事件错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10701009"
  zh_CN: "许愿错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10701010"
  zh_CN: "神像功能未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-10701011"
  zh_CN: "精灵已经在农场内啦！"
  showType: ECST_FlyTip
}
rows {
  id: "-10701012"
  zh_CN: "召唤配置错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10701013"
  zh_CN: "集结铃铛不足"
  showType: ECST_FlyTip
}
rows {
  id: "-10701014"
  zh_CN: "事件系统尚未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-10701015"
  zh_CN: "操作频繁，请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-10701016"
  zh_CN: "未触发任何事件"
  showType: ECST_FlyTip
}
rows {
  id: "-10701017"
  zh_CN: "等雨雪天气结束后再召唤恰克吧！"
  showType: ECST_FlyTip
}
rows {
  id: "-10800014"
  zh_CN: "农场相关的配置未找到"
  showType: ECST_FlyTip
}
rows {
  id: "-20090051"
  zh_CN: "不能进入目标玩家的大厅"
}
rows {
  id: "-20300036"
  zh_CN: "已经成熟了"
  showType: ECST_FlyTip
}
rows {
  id: "-20300215"
  zh_CN: "签名相同，请重新修改"
  showType: ECST_FlyTip
}
rows {
  id: "-20300321"
  zh_CN: "农场村民节日礼物已过期"
  showType: ECST_FlyTip
}
rows {
  id: "-20300323"
  zh_CN: "农场村民今日送礼总次数已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300325"
  zh_CN: "农场村民控制权不对"
}
rows {
  id: "-20300350"
  zh_CN: "农场村民没找到入驻的房子"
  showType: ECST_FlyTip
}
rows {
  id: "-20300351"
  zh_CN: "村民没有入驻房子"
  showType: ECST_FlyTip
}
rows {
  id: "-20300353"
  zh_CN: "村民礼物没有触发"
  showType: ECST_FlyTip
}
rows {
  id: "-20300356"
  zh_CN: "养殖物祈愿未触发保底"
  showType: ECST_FlyTip
}
rows {
  id: "-20300357"
  zh_CN: "民宅无村民入驻，不能访问"
  showType: ECST_FlyTip
}
rows {
  id: "-20300358"
  zh_CN: "农场给村民送礼的礼品不支持"
  showType: ECST_FlyTip
}
rows {
  id: "20300360"
  zh_CN: "农场外部操作次数不足"
  showType: ECST_FlyTip
}
rows {
  id: "20300361"
  zh_CN: "农场外部操作次数不足"
  showType: ECST_FlyTip
}
rows {
  id: "20300362"
  zh_CN: "农场外部操作农场当前有人"
  showType: ECST_FlyTip
}
rows {
  id: "20300363"
  zh_CN: "农场外部操作小屋当前有人"
  showType: ECST_FlyTip
}
rows {
  id: "20300364"
  zh_CN: "农场外部操作不能操作特殊作物"
  showType: ECST_FlyTip
}
rows {
  id: "-20380001"
  zh_CN: "星宝岛未开局或已结算"
}
rows {
  id: "-20380002"
  zh_CN: "地格已被占领"
}
rows {
  id: "-20380003"
  zh_CN: "当前建筑尚解锁"
}
rows {
  id: "-20380004"
  zh_CN: "建筑数量超过限制"
}
rows {
  id: "-20380005"
  zh_CN: "位置非法"
}
rows {
  id: "-20380006"
  zh_CN: "尚未进入星宝岛"
}
rows {
  id: "-20380007"
  zh_CN: "建筑当前状态不支持该操作"
}
rows {
  id: "-20380008"
  zh_CN: "建筑不存在"
}
rows {
  id: "-20380009"
  zh_CN: "建筑等级未解锁"
}
rows {
  id: "-20380010"
  zh_CN: "该建筑无法拆除"
}
rows {
  id: "-20380011"
  zh_CN: "服务器内部错误"
}
rows {
  id: "-20380012"
  zh_CN: "已达建筑升级队列上限"
}
rows {
  id: "-20380013"
  zh_CN: "该区域尚未解锁，不可建造"
}
rows {
  id: "-20380014"
  zh_CN: "资源不足"
}
rows {
  id: "-20380015"
  zh_CN: "资源剩余存储空间不足"
}
rows {
  id: "-20380016"
  zh_CN: "研究所前置科技未解锁，无法研究"
}
rows {
  id: "-20380017"
  zh_CN: "科技前置建筑未解锁，无法研究"
}
rows {
  id: "-20380018"
  zh_CN: "科技研究队列满了，无法升级"
}
rows {
  id: "-20380019"
  zh_CN: "科技已经升级过，无法升级"
}
rows {
  id: "-20380020"
  zh_CN: "科技状态错误，无法立即完成，请重试"
}
rows {
  id: "-20380021"
  zh_CN: "还为达到该段位，无法领取段位奖"
}
rows {
  id: "-20380022"
  zh_CN: "已经领取段位奖励，无法重复领取"
}
rows {
  id: "-20380023"
  zh_CN: "开启战斗异常，重试"
}
rows {
  id: "-20380024"
  zh_CN: "道具配置不存在"
}
rows {
  id: "-20380025"
  zh_CN: "道具配置不存在"
}
rows {
  id: "-20380026"
  zh_CN: "从gs增加配置到coc服出错"
}
rows {
  id: "-20380027"
  zh_CN: "当前已有正在开启宝箱"
}
rows {
  id: "-20380028"
  zh_CN: "匹配失败,请重试"
}
rows {
  id: "-20380029"
  zh_CN: "该区域已经解锁"
}
rows {
  id: "-20380030"
  zh_CN: "不满足区域解锁条件"
}
rows {
  id: "-20380031"
  zh_CN: "大区域未解锁"
}
rows {
  id: "-20380032"
  zh_CN: "关卡配置不存在"
}
rows {
  id: "-20380033"
  zh_CN: "关卡未解锁"
}
rows {
  id: "-20380034"
  zh_CN: "结算关卡信息错误"
}
rows {
  id: "-20380035"
  zh_CN: "居民已经解锁"
}
rows {
  id: "-20380036"
  zh_CN: "居民尚未解锁"
}
rows {
  id: "-20380037"
  zh_CN: "官职与建筑类型不匹配"
}
rows {
  id: "-20380038"
  zh_CN: "coc战斗结算异常"
}
rows {
  id: "-20380039"
  zh_CN: "居民无可用对话"
}
rows {
  id: "-20380040"
  zh_CN: "今日已进行过对话"
}
rows {
  id: "-20380041"
  zh_CN: "居民对话状态错误"
}
rows {
  id: "-20380042"
  zh_CN: "当前没有进行中的对话"
}
rows {
  id: "-20380043"
  zh_CN: "从未注册过coc"
}
rows {
  id: "-20380044"
  zh_CN: "输入包含敏感字符"
}
rows {
  id: "-20380045"
  zh_CN: "居民每日奖励已领取"
}
rows {
  id: "-20380046"
  zh_CN: "该居民无每日奖励"
}
rows {
  id: "-20380047"
  zh_CN: "已经开始战斗，无法切换对手"
}
rows {
  id: "-20380048"
  zh_CN: "还没匹配，无法开始战斗"
}
rows {
  id: "-20380049"
  zh_CN: "匹配中无法进入他人星宝岛"
}
rows {
  id: "-20380050"
  zh_CN: "月卡价格不一致,重登后再试"
}
rows {
  id: "-20380051"
  zh_CN: "月卡已下架，无法购买"
}
rows {
  id: "-20380052"
  zh_CN: "添加coc月卡失败"
}
rows {
  id: "-20380053"
  zh_CN: "月卡未解锁"
}
rows {
  id: "-20380054"
  zh_CN: "月卡今日奖励已经领取过"
}
rows {
  id: "-20380055"
  zh_CN: "条件未实现"
}
rows {
  id: "-20380056"
  zh_CN: "限时升级队列id分配失败"
}
rows {
  id: "-20380057"
  zh_CN: "限时队列剩余时间不足"
}
rows {
  id: "-20380058"
  zh_CN: "官职不允许空缺"
}
rows {
  id: "-20380059"
  zh_CN: "PVE没有士兵不能开局"
}
rows {
  id: "-20380061"
  zh_CN: "村民已离开"
}
rows {
  id: "-20381001"
  zh_CN: "当前士兵未解锁"
}
rows {
  id: "-20381002"
  zh_CN: "当前训练已经在加速中"
}
rows {
  id: "-20381003"
  zh_CN: "当前训练队列已满"
}
rows {
  id: "-20381004"
  zh_CN: "模板编队容量超过上限"
}
rows {
  id: "-20381005"
  zh_CN: "分配开局失败"
}
rows {
  id: "-20381006"
  zh_CN: "已任命该好友"
}
rows {
  id: "-20381007"
  zh_CN: "村长被俘获，无法任命"
}
rows {
  id: "-20381008"
  zh_CN: "今日已援助过该好友"
}
rows {
  id: "-20381009"
  zh_CN: "今日援助次数已满"
}
rows {
  id: "-20381010"
  zh_CN: "好友今日收到援助次数达到上限"
}
rows {
  id: "-20381011"
  zh_CN: "监狱冷却中"
}
rows {
  id: "-20381012"
  zh_CN: "监狱指派中"
}
rows {
  id: "-20381013"
  zh_CN: "建筑类型不匹配"
}
rows {
  id: "-20381014"
  zh_CN: "援助加速失败"
}
rows {
  id: "-20381015"
  zh_CN: "没有到营救时间，无法解救村长"
}
rows {
  id: "-20381016"
  zh_CN: "登录状态异常"
}
rows {
  id: "-20381017"
  zh_CN: "场景状态异常"
}
rows {
  id: "-20381018"
  zh_CN: "没有士兵不能开局"
}
rows {
  id: "-20381019"
  zh_CN: "PVE关卡因为主堡等级未解锁"
}
rows {
  id: "-20382001"
  zh_CN: "防守关卡配置未找到"
}
rows {
  id: "-20382002"
  zh_CN: "防守关卡未解锁"
}
rows {
  id: "-20382003"
  zh_CN: "防守关卡已完成"
}
rows {
  id: "-20382004"
  zh_CN: "防守关卡数据错误"
}
rows {
  id: "-20382005"
  zh_CN: "防守关卡挑战次数不足"
}
rows {
  id: "-20390063"
  zh_CN: "温泉人满了"
  showType: ECST_FlyTip
}
rows {
  id: "-20390064"
  zh_CN: "你还不在温泉里"
  showType: ECST_FlyTip
}
rows {
  id: "-20390065"
  zh_CN: "农场还没有温泉"
  showType: ECST_FlyTip
}
rows {
  id: "-20390100"
  zh_CN: "已经拥有了该仙术"
  showType: ECST_FlyTip
}
rows {
  id: "-20390101"
  zh_CN: "仙术配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20390102"
  zh_CN: "仙术不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20390103"
  zh_CN: "仙术使用方式错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20390104"
  zh_CN: "仙术冷却中"
  showType: ECST_FlyTip
}
rows {
  id: "-20390105"
  zh_CN: "仙术使用对象不对"
  showType: ECST_FlyTip
}
rows {
  id: "-20390106"
  zh_CN: "仙术使用没有足够的蓝条"
  showType: ECST_FlyTip
}
rows {
  id: "-20390107"
  zh_CN: "仙术使用没有符合条件的目标"
  showType: ECST_FlyTip
}
rows {
  id: "-20390108"
  zh_CN: "仙术未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20390109"
  zh_CN: "仙术已装备"
  showType: ECST_FlyTip
}
rows {
  id: "-20390110"
  zh_CN: "仙术未装备"
  showType: ECST_FlyTip
}
rows {
  id: "-10600310"
  zh_CN: "审核中的地图不能下架"
  showType: ECST_FlyTip
}
rows {
  id: "-10041130"
  zh_CN: "美食节赠送物品失败"
}
rows {
  id: "-10041131"
  zh_CN: "美食节赠送精灵不存在"
}
rows {
  id: "-10041132"
  zh_CN: "美食节赠送精灵上限"
}
rows {
  id: "-10041133"
  zh_CN: "美食节赠送制作id错误"
}
rows {
  id: "-10041134"
  zh_CN: "美食节赠送配置错误"
}
rows {
  id: "-10041135"
  zh_CN: "美食节合成物品发送失败"
}
rows {
  id: "-10600303"
  zh_CN: "不允许编辑存在的配置"
}
rows {
  id: "-10600304"
  zh_CN: "不允许编辑发布的配置"
}
rows {
  id: "-10600305"
  zh_CN: "配置不存在"
}
rows {
  id: "-10600306"
  zh_CN: "数据操作失败"
}
rows {
  id: "-10600307"
  zh_CN: "ugc aigc魔法图片生成失败"
}
rows {
  id: "-10600308"
  zh_CN: "地图描述文本超过限制"
}
rows {
  id: "-10600309"
  zh_CN: "附加类型错误"
}
rows {
  id: "-10600311"
  zh_CN: "种草社区通知失败"
}
rows {
  id: "-10701018"
  zh_CN: "雨雪天气无法召唤云精灵"
  showType: ECST_FlyTip
}
rows {
  id: "-20010233"
  zh_CN: "玩法因客户端信息屏蔽"
}
rows {
  id: "-20100083"
  zh_CN: "未找到子奖励"
}
rows {
  id: "-20100084"
  zh_CN: "重复购买子奖励"
}
rows {
  id: "-20300360"
  zh_CN: "农场外部操作次数不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20300361"
  zh_CN: "农场外部操作次数不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20300362"
  zh_CN: "农场外部操作农场当前有人"
  showType: ECST_FlyTip
}
rows {
  id: "-20300363"
  zh_CN: "农场外部操作小屋当前有人"
  showType: ECST_FlyTip
}
rows {
  id: "-20300364"
  zh_CN: "农场外部操作不能操作特殊作物"
  showType: ECST_FlyTip
}
rows {
  id: "-20300365"
  zh_CN: "霸王吞吞花正在吞食~现在不能拿取"
  showType: ECST_FlyTip
}
rows {
  id: "-20300366"
  zh_CN: "村民礼物发现未解锁钓鱼水层"
  showType: ECST_FlyTip
}
rows {
  id: "-20300367"
  zh_CN: "特殊鱼不能合成"
  showType: ECST_FlyTip
}
rows {
  id: "-20300369"
  zh_CN: "农场红包功能未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20340120"
  zh_CN: "不在兑换时间"
}
rows {
  id: "-20340121"
  zh_CN: "兑换cd"
}
rows {
  id: "-20340122"
  zh_CN: "超过兑换最大次数限制"
}
rows {
  id: "-20340123"
  zh_CN: "万能卡不存在"
}
rows {
  id: "-20340124"
  zh_CN: "万能卡兑换失败"
}
rows {
  id: "-20340125"
  zh_CN: "交易获取卡牌超过每日上限"
}
rows {
  id: "-20381020"
  zh_CN: "建筑加速中"
}
rows {
  id: "-20381021"
  zh_CN: "监狱工作中"
}
rows {
  id: "-20390111"
  zh_CN: "仙术参数错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20300370"
  zh_CN: "农场村庄未找到"
  showType: ECST_FlyTip
}
rows {
  id: "-10473001"
  zh_CN: "修改生日CD中"
  showType: ECST_FlyTip
}
rows {
  id: "-10473002"
  zh_CN: "已经祝福过"
  showType: ECST_FlyTip
}
rows {
  id: "-10473003"
  zh_CN: "好友不能祝福"
  showType: ECST_FlyTip
}
rows {
  id: "-10473004"
  zh_CN: "对方没有设置生日"
  showType: ECST_FlyTip
}
rows {
  id: "-10473005"
  zh_CN: "生日时间已经过了"
  showType: ECST_FlyTip
}
rows {
  id: "-10473006"
  zh_CN: "不在可赠送的时间哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10473007"
  zh_CN: "生日赠送贺卡需要提前天数未到"
  showType: ECST_FlyTip
}
rows {
  id: "-10473008"
  zh_CN: "生日时间没到"
  showType: ECST_FlyTip
}
rows {
  id: "-10473009"
  zh_CN: "赠送贺卡自定义参数错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10473010"
  zh_CN: "为TA送的贺卡已达上限哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10473011"
  zh_CN: "今天就是TA的生日，已成功寄出贺卡"
  showType: ECST_FlyTip
}
rows {
  id: "-10473012"
  zh_CN: "赠送太频繁"
  showType: ECST_FlyTip
}
rows {
  id: "-10473013"
  zh_CN: "需要成为好友才能送出祝福哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10473014"
  zh_CN: "暂时无法送出贺卡哦"
  showType: ECST_FlyTip
}
rows {
  id: "-10473015"
  zh_CN: "生日月日解析错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10473016"
  zh_CN: "生日系统未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-10473017"
  zh_CN: "生日官方福利领取时间已经过了"
  showType: ECST_FlyTip
}
rows {
  id: "-10473018"
  zh_CN: "生日官方福利领取过了"
  showType: ECST_FlyTip
}
rows {
  id: "-10473019"
  zh_CN: "生日官方福利配置为空"
  showType: ECST_FlyTip
}
rows {
  id: "-10473020"
  zh_CN: "生日系统上线时间配置错误"
  showType: ECST_FlyTip
}
rows {
  id: "-10473021"
  zh_CN: "你已被禁止赠送贺卡"
  showType: ECST_FlyTip
}
rows {
  id: "-10473022"
  zh_CN: "生日贺卡找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20017062"
  zh_CN: "段位数据错误"
}
rows {
  id: "-20341100"
  zh_CN: "已达分享上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20341101"
  zh_CN: "已达领取上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20341102"
  zh_CN: "手慢一步，福袋已被抢光"
  showType: ECST_FlyTip
}
rows {
  id: "-20341103"
  zh_CN: "福袋已过期"
  showType: ECST_FlyTip
}
rows {
  id: "-20341104"
  zh_CN: "该福袋仅供自己领取"
  showType: ECST_FlyTip
}
rows {
  id: "-20341105"
  zh_CN: "该福袋仅供好友领取"
  showType: ECST_FlyTip
}
rows {
  id: "-20341106"
  zh_CN: "福袋已领取"
  showType: ECST_FlyTip
}
rows {
  id: "-20341201"
  zh_CN: "领取失败，请重试"
  showType: ECST_FlyTip
}
rows {
  id: "-20341202"
  zh_CN: "获取福袋信息失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20341203"
  zh_CN: "获取福袋奖励失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20341204"
  zh_CN: "分享频道失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20380063"
  zh_CN: "coc玩法服务器未启动"
  showType: ECST_FlyTip
}
rows {
  id: "-20400072"
  zh_CN: "赛季回顾传递的参数大于当前最新赛季id"
  showType: ECST_FlyTip
}
rows {
  id: "-20400073"
  zh_CN: "赛季回顾传递的参数大于当前最新赛季id"
  showType: ECST_FlyTip
}
rows {
  id: "-10041173"
  zh_CN: "-10041173"
}
rows {
  id: "-10170050"
  zh_CN: "-10170050"
}
rows {
  id: "-2000153"
  zh_CN: "-2000153"
}
rows {
  id: "-2000154"
  zh_CN: "-2000154"
}
rows {
  id: "-2000155"
  zh_CN: "-2000155"
}
rows {
  id: "-2000156"
  zh_CN: "-2000156"
}
rows {
  id: "-2000157"
  zh_CN: "-2000157"
}
rows {
  id: "-10000111"
  zh_CN: "玩家位置不可达"
}
rows {
  id: "-10041136"
  zh_CN: "评分引导请求datamore接口异常"
}
rows {
  id: "-10041137"
  zh_CN: "美食节已经领取"
}
rows {
  id: "-10041138"
  zh_CN: "玩家与活动版本号不匹配"
}
rows {
  id: "-10041500"
  zh_CN: "匹配搭子活动:奖杯值无效"
}
rows {
  id: "-10041501"
  zh_CN: "匹配搭子活动:未找到数据"
}
rows {
  id: "-10041502"
  zh_CN: "匹配搭子活动:更新奖杯失败"
}
rows {
  id: "-10041503"
  zh_CN: "匹配搭子活动:活动未找到"
}
rows {
  id: "-10041504"
  zh_CN: "匹配搭子活动:错误的活动类型"
}
rows {
  id: "-10041505"
  zh_CN: "匹配搭子活动:未找到奖杯数据"
}
rows {
  id: "-10041506"
  zh_CN: "匹配搭子活动:奖杯数据成员计数异常"
}
rows {
  id: "-10041507"
  zh_CN: "匹配搭子活动:更新玩家奖杯失败"
}
rows {
  id: "-10041508"
  zh_CN: "匹配搭子活动:更新小队奖杯失败"
}
rows {
  id: "-10041509"
  zh_CN: "匹配搭子活动:加入队伍更新奖杯失败"
}
rows {
  id: "-10041510"
  zh_CN: "匹配搭子活动:加入队伍回调失败"
}
rows {
  id: "-10041159"
  zh_CN: "福签活动找不到对于的配置"
}
rows {
  id: "-10041160"
  zh_CN: "抽取福签所需道具不足"
}
rows {
  id: "-10041161"
  zh_CN: "抽取福签随机福签失败"
}
rows {
  id: "-10041162"
  zh_CN: "福签合并所需道具不足"
}
rows {
  id: "-10041163"
  zh_CN: "福签当前无法合成"
}
rows {
  id: "-10041164"
  zh_CN: "用户当前无法邀请助力"
}
rows {
  id: "-10041165"
  zh_CN: "当前助力请求已经失效"
}
rows {
  id: "-10041166"
  zh_CN: "今日已经给该用户助力"
}
rows {
  id: "-10041167"
  zh_CN: "今天助力已达上限"
}
rows {
  id: "-10041168"
  zh_CN: "助力指令发生失败"
}
rows {
  id: "-10041169"
  zh_CN: "用户当前无法邀请福星助力"
}
rows {
  id: "-10041170"
  zh_CN: "用户当前无法邀请福星助力"
}
rows {
  id: "-10041171"
  zh_CN: "今天赠与福签已经到达上线"
}
rows {
  id: "-10041172"
  zh_CN: "今天赠与或者索要福签已经到达上线"
}
rows {
  id: "-10041174"
  zh_CN: "没有需要赠与的福签"
}
rows {
  id: "-10041175"
  zh_CN: "好友助力次数已经满了"
  showType: ECST_FlyTip
}
rows {
  id: "-10041176"
  zh_CN: "已经帮好友助力过了"
  showType: ECST_FlyTip
}
rows {
  id: "-10041177"
  zh_CN: "福签索要已经被赠与"
}
rows {
  id: "-10041178"
  zh_CN: "福签共享已经被领取"
}
rows {
  id: "-10041179"
  zh_CN: "福签索要已经达到上线"
}
rows {
  id: "-10041180"
  zh_CN: "福签共享已经达到上线"
}
rows {
  id: "-10041181"
  zh_CN: "福签不能像自己索要"
}
rows {
  id: "-10041182"
  zh_CN: "福签不能像自己赠与"
}
rows {
  id: "-10041183"
  zh_CN: "您今天已经向对方共享过"
}
rows {
  id: "-10041184"
  zh_CN: "对方玩家今天已经向您索要过"
}
rows {
  id: "-10041185"
  zh_CN: "活动未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-10041186"
  zh_CN: "助力玩家不是您的游戏好友"
  showType: ECST_FlyTip
}
rows {
  id: "-10041187"
  zh_CN: "今天的助力次数已达到上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10041188"
  zh_CN: "精灵好感度不足,无法领取"
}
rows {
  id: "-10041189"
  zh_CN: "当前环境BI配置缺失"
}
rows {
  id: "-10041190"
  zh_CN: "活动页签BI Url失败"
}
rows {
  id: "-10041191"
  zh_CN: "活动页签BI参数编码失败"
}
rows {
  id: "-10041192"
  zh_CN: "活动页签BI结果解码失败"
}
rows {
  id: "-10041193"
  zh_CN: "活动页签BI返回错误码"
}
rows {
  id: "-10041194"
  zh_CN: "活动页签BI HttpPost失败"
}
rows {
  id: "-10041195"
  zh_CN: "活动页签BI结果非法"
}
rows {
  id: "-10041196"
  zh_CN: "活动页签BI响应为空"
}
rows {
  id: "-10055023"
  zh_CN: "补签次数已经用完了"
}
rows {
  id: "-10140050"
  zh_CN: "排行榜LBS信息未开启"
}
rows {
  id: "-10140051"
  zh_CN: "排行榜请求过于频繁"
}
rows {
  id: "-10170048"
  zh_CN: "频道未找到"
}
rows {
  id: "-10170049"
  zh_CN: "获取聊天频道加入时间异常"
}
rows {
  id: "-10473023"
  zh_CN: "配置找不到"
}
rows {
  id: "-10473024"
  zh_CN: "向好友发送赠送邮件失败"
}
rows {
  id: "-10600312"
  zh_CN: "ugc共创作者不存在"
}
rows {
  id: "-10600313"
  zh_CN: "发布态共创权限操作不允许"
}
rows {
  id: "-10600314"
  zh_CN: "联合创作权限已激活，请刷新后查看"
  showType: ECST_FlyTip
}
rows {
  id: "-10600315"
  zh_CN: "联合创作权限已关闭，请刷新后查看"
  showType: ECST_FlyTip
}
rows {
  id: "-10600316"
  zh_CN: "地图描述图片超过限制"
}
rows {
  id: "-10600317"
  zh_CN: "更新地图元数据权限不允许"
}
rows {
  id: "-2000151"
  zh_CN: "时装基金有子页签"
}
rows {
  id: "-20017063"
  zh_CN: "已使用过金币"
}
rows {
  id: "-20017064"
  zh_CN: "金币不足"
}
rows {
  id: "-20017065"
  zh_CN: "moba队伍大小包混合开始匹配失败"
}
rows {
  id: "-20100085"
  zh_CN: "不可购买子奖励"
}
rows {
  id: "-20110023"
  zh_CN: "非队长，无法执行该操作"
}
rows {
  id: "-20110024"
  zh_CN: "该成员近期仍活跃，无法踢出"
}
rows {
  id: "-20110025"
  zh_CN: "团队积分已达到，无法踢出"
}
rows {
  id: "-20110026"
  zh_CN: "当前时段无法浇水"
}
rows {
  id: "-20250010"
  zh_CN: "AMS道具未能等待到结果"
}
rows {
  id: "-20250011"
  zh_CN: "AMS道具无法等待到结果"
}
rows {
  id: "-20250012"
  zh_CN: "AMS道具未能等待到结果"
}
rows {
  id: "-20300371"
  zh_CN: "农场村民小屋找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20300380"
  zh_CN: "有物品设置了禁售"
  showType: ECST_FlyTip
}
rows {
  id: "-20300381"
  zh_CN: "npc农场初始化失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300382"
  zh_CN: "作物被仙术恢复过"
  showType: ECST_FlyTip
}
rows {
  id: "-20300383"
  zh_CN: "农场好友祈福屏蔽数超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300384"
  zh_CN: "被当前农场屏蔽祈福"
  showType: ECST_FlyTip
}
rows {
  id: "-20300385"
  zh_CN: "作物被仙术恢复过"
  showType: ECST_FlyTip
}
rows {
  id: "-20300386"
  zh_CN: "仙术加速作物成熟保护中"
  showType: ECST_FlyTip
}
rows {
  id: "-20300387"
  zh_CN: "农场收藏品不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20300388"
  zh_CN: "农场奶牛任务未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20300389"
  zh_CN: "小红狐农场未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20300391"
  zh_CN: "农场图纸数量已达上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20340126"
  zh_CN: "已经领取了新手福利"
}
rows {
  id: "-20340127"
  zh_CN: "功能暂未开放"
}
rows {
  id: "-20341206"
  zh_CN: "分享礼包系统暂时未开放"
}
rows {
  id: "-20390112"
  zh_CN: "仙术过期"
  showType: ECST_FlyTip
}
rows {
  id: "-20390113"
  zh_CN: "农场小屋新老家具不属于同一替换序列"
  showType: ECST_FlyTip
}
rows {
  id: "-20390114"
  zh_CN: "通灵兽孵化中"
  showType: ECST_FlyTip
}
rows {
  id: "-20390115"
  zh_CN: "通灵兽等级配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20390116"
  zh_CN: "通灵兽等级已达最高"
  showType: ECST_FlyTip
}
rows {
  id: "-20390117"
  zh_CN: "通灵兽进化所需农场等级不够"
  showType: ECST_FlyTip
}
rows {
  id: "-20390118"
  zh_CN: "通灵兽不在孵化中"
  showType: ECST_FlyTip
}
rows {
  id: "-20390119"
  zh_CN: "通灵兽不在工作中"
  showType: ECST_FlyTip
}
rows {
  id: "-20390120"
  zh_CN: "通灵兽仙力不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20390121"
  zh_CN: "图纸与当前房间扩建次数不一致"
  showType: ECST_FlyTip
}
rows {
  id: "-20390122"
  zh_CN: "通灵兽未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20390123"
  zh_CN: "通灵兽未完成孵化"
  showType: ECST_FlyTip
}
rows {
  id: "-20410000"
  zh_CN: "ainpc服务内部错误"
}
rows {
  id: "-20410001"
  zh_CN: "没有找到Ainpc缓存"
}
rows {
  id: "-20410002"
  zh_CN: "获取轮次失败"
}
rows {
  id: "-20410003"
  zh_CN: "cs转发Ainpc找不到handle"
}
rows {
  id: "-20410004"
  zh_CN: "cs转发Ainpc协议解析错误"
}
rows {
  id: "-20410005"
  zh_CN: "cs转发Ainpc处理超时"
}
rows {
  id: "-20410006"
  zh_CN: "cs转发Ainpc处理错误"
}
rows {
  id: "-20410007"
  zh_CN: "cs转发AinpcRpc调用错误"
}
rows {
  id: "-20410008"
  zh_CN: "cs转发Ainpc错误"
}
rows {
  id: "-20460001"
  zh_CN: "livelink签名失败"
}
rows {
  id: "-20460002"
  zh_CN: "livelink请求失败"
}
rows {
  id: "-20460003"
  zh_CN: "livelink更新状态失败"
}
rows {
  id: "-20460004"
  zh_CN: "无效的liveLink玩法配置id"
}
rows {
  id: "-20460005"
  zh_CN: "livelink交互指令错误"
}
rows {
  id: "-10600318"
  zh_CN: "无变更自定义loading权限"
}
rows {
  id: "-10600319"
  zh_CN: "无变更乐园自定义封面权限"
}
rows {
  id: "-10600320"
  zh_CN: "不是乐园地图"
}
rows {
  id: "-10001058"
  zh_CN: "申请角色跨服转移服务中，请稍后登录游戏..."
  showType: ECST_FlyTip
}
rows {
  id: "-10052011"
  zh_CN: "星标邮件只能单独进行删除"
}
rows {
  id: "-20300392"
  zh_CN: "农场图纸名称字数超上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20300393"
  zh_CN: "农场图纸描述字数超上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20400074"
  zh_CN: "主目标系统熟练等级奖励已领取"
}
rows {
  id: "-20400075"
  zh_CN: "主目标系统所有熟练等级奖励已领取"
}
rows {
  id: "-20400076"
  zh_CN: "主目标系统熟练等级奖励领取失败"
}
rows {
  id: "-20400077"
  zh_CN: "主目标系统熟练等级奖励不存在"
}
rows {
  id: "-20400078"
  zh_CN: "主目标系统未开放"
}
rows {
  id: "-20400079"
  zh_CN: "该卡牌已经解锁"
}
rows {
  id: "-20400080"
  zh_CN: "主目标系统熟练等级奖励未找到"
}
rows {
  id: "-20400081"
  zh_CN: "主目标系统熟练等级替换奖励未找到"
}
rows {
  id: "-20400082"
  zh_CN: "主目标系统熟练等级未达到"
}
rows {
  id: "-20400083"
  zh_CN: "主目标系统有前置熟练等级奖励未领取"
}
rows {
  id: "-20470001"
  zh_CN: "该皮肤不能被装备到该暗星上"
}
rows {
  id: "-20470002"
  zh_CN: "该皮肤不能被装备到该星宝上"
}
rows {
  id: "-20470003"
  zh_CN: "该道具类型不能装备到暗星或星宝上"
}
rows {
  id: "-10055024"
  zh_CN: "拼图ID不匹配"
}
rows {
  id: "-10055025"
  zh_CN: "拼图已经揭开"
}
rows {
  id: "-10055026"
  zh_CN: "拼图不存在"
}
rows {
  id: "-10055027"
  zh_CN: "拼图重复"
}
rows {
  id: "-10055028"
  zh_CN: "拼图不足"
}
rows {
  id: "-10055029"
  zh_CN: "拼图已完成"
}
rows {
  id: "-10600321"
  zh_CN: "地图loading不存在"
}
rows {
  id: "-20010132"
  zh_CN: "目标玩家不存在"
}
rows {
  id: "-20300037"
  zh_CN: "建筑未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20400084"
  zh_CN: "队伍中段位不匹配，请去备战界面调整"
  showType: ECST_FlyTip
}
rows {
  id: "-2000152"
  zh_CN: "奖励已经兑换完了"
  showType: ECST_FlyTip
}
rows {
  id: "-100"
  zh_CN: "player对象未找到"
}
rows {
  id: "-101"
  zh_CN: "未定义的事件类型处理"
}
rows {
  id: "-102"
  zh_CN: "事件处理异常"
}
rows {
  id: "-103"
  zh_CN: "事件参数检查失败"
}
rows {
  id: "-20000025"
  zh_CN: "当前时间没有可用免肝券配置"
}
rows {
  id: "-20390124"
  zh_CN: "图纸与扩建次数不符合系统要求"
  showType: ECST_FlyTip
}
rows {
  id: "-20390125"
  zh_CN: "仙术MP满"
  showType: ECST_FlyTip
}
rows {
  id: "-20390126"
  zh_CN: "仙术回复道具不够"
  showType: ECST_FlyTip
}
rows {
  id: "-10600322"
  zh_CN: "联合创作者发布的地图数量已达到上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10600332"
  zh_CN: "处于封禁状态, 暂不能修改留言"
  showType: ECST_FlyTip
}
rows {
  id: "-11030004"
  zh_CN: "不可填写该内容"
  showType: ECST_FlyTip
}
rows {
  id: "-104"
  zh_CN: "http返回空串"
}
rows {
  id: "-105"
  zh_CN: "http返回结果检查失败"
}
rows {
  id: "-106"
  zh_CN: "服务器请求rpc异常"
}
rows {
  id: "-1000883"
  zh_CN: "IDIP用户处于转区流程中"
}
rows {
  id: "-1000882"
  zh_CN: "IDIP用户已经转区"
}
rows {
  id: "-10040904"
  zh_CN: "农场天天权益卡参数错误"
}
rows {
  id: "-10041514"
  zh_CN: "同一登录账号，只能一个角色参与活动"
  showType: ECST_FlyTip
}
rows {
  id: "-10041220"
  zh_CN: "当前口令码已经失效~"
  showType: ECST_FlyTip
}
rows {
  id: "-10041221"
  zh_CN: "生成口令码失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10041222"
  zh_CN: "口令码异常，请星宝重新复制口令码进行粘贴~"
  showType: ECST_FlyTip
}
rows {
  id: "-10041223"
  zh_CN: "不能重复使用同一个口令码哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10041224"
  zh_CN: "不能使用自己的口令码哦~"
  showType: ECST_FlyTip
}
rows {
  id: "-10042000"
  zh_CN: "不符合招募条件"
}
rows {
  id: "-10042001"
  zh_CN: "已被招募"
}
rows {
  id: "-10042002"
  zh_CN: "您不是对方好友，先添加好友吧"
}
rows {
  id: "-10042003"
  zh_CN: "不能招募自己"
}
rows {
  id: "-10043001"
  zh_CN: "晴霜试炼积分不够"
}
rows {
  id: "-10043002"
  zh_CN: "晴霜试炼重复领取"
}
rows {
  id: "-10049999"
  zh_CN: "奖励对应活动id不正确"
}
rows {
  id: "-10091001"
  zh_CN: "同步事件服务识别异常"
}
rows {
  id: "-10091002"
  zh_CN: "同步事件服务不允许转发"
}
rows {
  id: "-10170051"
  zh_CN: "玩家已主动退出了该频道"
}
rows {
  id: "-10190009"
  zh_CN: "上报任务完成数量过多"
}
rows {
  id: "-10190010"
  zh_CN: "任务不存在可选奖励"
}
rows {
  id: "-10400062"
  zh_CN: "一键加入活跃社团失败"
}
rows {
  id: "-10600323"
  zh_CN: "奖章展示数量超过限制"
}
rows {
  id: "-10600324"
  zh_CN: "地图展示数量超过限制"
}
rows {
  id: "-10600325"
  zh_CN: "设置创作者主页失败"
}
rows {
  id: "-10600326"
  zh_CN: "设置创作者主页徽章失败，未获得该奖章"
}
rows {
  id: "-10600327"
  zh_CN: "擅长领域标签数量超过限制"
}
rows {
  id: "-10600328"
  zh_CN: "UGC匹配配置生成失败"
}
rows {
  id: "-10600329"
  zh_CN: "UGC匹配配置锁定失败"
}
rows {
  id: "-10600330"
  zh_CN: "UGC匹配配置存储失败"
}
rows {
  id: "-10600333"
  zh_CN: "idip修改"
}
rows {
  id: "-10600334"
  zh_CN: "协议参数错误 MsgType枚举超出上限"
}
rows {
  id: "-10600335"
  zh_CN: "开发者协议未签署"
}
rows {
  id: "-10600336"
  zh_CN: "文件上传类型错误"
}
rows {
  id: "-10600337"
  zh_CN: "文件上传的类型错误"
}
rows {
  id: "-10600338"
  zh_CN: "联合共创db操作失败"
}
rows {
  id: "-10600339"
  zh_CN: "发布db操作失败"
}
rows {
  id: "-10600340"
  zh_CN: "草稿db操作失败"
}
rows {
  id: "-10600341"
  zh_CN: "不能同时设置中途加入和ai接管配置"
}
rows {
  id: "-10600342"
  zh_CN: "不能设置ai接管配置"
}
rows {
  id: "-10600540"
  zh_CN: "客户端传入参数不合法，请检查参数"
}
rows {
  id: "-10600600"
  zh_CN: "模块已关闭"
}
rows {
  id: "-10600601"
  zh_CN: "数据key已关闭"
}
rows {
  id: "-10600602"
  zh_CN: "数据key对应的proto解析失败"
}
rows {
  id: "-10600603"
  zh_CN: "数据不存在"
}
rows {
  id: "-2000158"
  zh_CN: "柯南二期活跃活动已达挑战奖励上限"
}
rows {
  id: "-2000159"
  zh_CN: "柯南二期活跃活动配置未找到"
}
rows {
  id: "-2000160"
  zh_CN: "柯南二期活跃活动activityId不一致"
}
rows {
  id: "-20300309"
  zh_CN: "请求超时"
  showType: ECST_FlyTip
}
rows {
  id: "-20300394"
  zh_CN: "由于对方权限设置，暂时无法留言"
  showType: ECST_FlyTip
}
rows {
  id: "-20300395"
  zh_CN: "由于对方权限设置，暂时无法访问该农场"
  showType: ECST_FlyTip
}
rows {
  id: "-20300396"
  zh_CN: "野猫找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20300397"
  zh_CN: "农场宠物类别不合法"
  showType: ECST_FlyTip
}
rows {
  id: "-20300398"
  zh_CN: "野猫不在农场里"
  showType: ECST_FlyTip
}
rows {
  id: "-20300399"
  zh_CN: "野猫好感度未满"
  showType: ECST_FlyTip
}
rows {
  id: "-20300400"
  zh_CN: "野猫已拥有"
  showType: ECST_FlyTip
}
rows {
  id: "-20300401"
  zh_CN: "野猫配置找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20300402"
  zh_CN: "签名包含特殊字符，修改失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300403"
  zh_CN: "猫还没准备好捕鱼"
  showType: ECST_FlyTip
}
rows {
  id: "-20300404"
  zh_CN: "猫捕鱼没有对应水层"
  showType: ECST_FlyTip
}
rows {
  id: "-20300405"
  zh_CN: "猫捕鱼没有对应水层"
  showType: ECST_FlyTip
}
rows {
  id: "-20300406"
  zh_CN: "签名字数过长，修改失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20300407"
  zh_CN: "签名包含敏感词，修改失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20340128"
  zh_CN: "必须的卡牌配置错误"
}
rows {
  id: "-20340129"
  zh_CN: "交换使用的卡牌不在可选范围内"
}
rows {
  id: "-20382006"
  zh_CN: "CocGM参数错误"
}
rows {
  id: "-20382007"
  zh_CN: "CocGM执行错误"
}
rows {
  id: "-20382008"
  zh_CN: "CocGM进度表配置未找到"
}
rows {
  id: "-20382009"
  zh_CN: "Coc 建筑已派遣的村民已达上限"
}
rows {
  id: "-20382010"
  zh_CN: "Coc 村民已被派遣至其他建筑"
}
rows {
  id: "-20382011"
  zh_CN: "Coc 村民没有派遣至该建筑"
}
rows {
  id: "-20382012"
  zh_CN: "Coc 建筑没有被派遣该村民"
}
rows {
  id: "-20382013"
  zh_CN: "Coc 当前建筑不支持这种改造"
}
rows {
  id: "-20382014"
  zh_CN: "Coc建筑改造尚未解锁"
}
rows {
  id: "-20382015"
  zh_CN: "Coc建筑改造已达上限"
}
rows {
  id: "-20390066"
  zh_CN: "农场墙纸等已装修不能售卖"
  showType: ECST_FlyTip
}
rows {
  id: "-20390067"
  zh_CN: "农场家具当前内景不支持"
  showType: ECST_FlyTip
}
rows {
  id: "-20390068"
  zh_CN: "农场小屋家具数量太少"
  showType: ECST_FlyTip
}
rows {
  id: "-20390069"
  zh_CN: "你的等级不足以泡这个温泉"
  showType: ECST_FlyTip
}
rows {
  id: "-20390127"
  zh_CN: "农场ES索引查询失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20390128"
  zh_CN: "农场ES索引创建失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20390129"
  zh_CN: "农场ES文档创建失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20390130"
  zh_CN: "农场ES文档更新失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20390131"
  zh_CN: "农场ES创建PIT失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20390132"
  zh_CN: "农场ES查询失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20390133"
  zh_CN: "农场派对模块未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20390134"
  zh_CN: "农场派对简介字数超上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20390135"
  zh_CN: "农场派对简介为空"
  showType: ECST_FlyTip
}
rows {
  id: "-20390136"
  zh_CN: "农场派对禁止发布"
  showType: ECST_FlyTip
}
rows {
  id: "-20390137"
  zh_CN: "农场派对未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20390138"
  zh_CN: "农场派对ES需要更新"
  showType: ECST_FlyTip
}
rows {
  id: "-20390140"
  zh_CN: "农场派对ES不需要更新"
  showType: ECST_FlyTip
}
rows {
  id: "-20390141"
  zh_CN: "农场派对ES查询失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20390142"
  zh_CN: "农场ES获取session失效"
  showType: ECST_FlyTip
}
rows {
  id: "-20390143"
  zh_CN: "农场派对场景不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20390144"
  zh_CN: "农场派对分页查询数量超过上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20390145"
  zh_CN: "农场小屋家具最低版本号限制购买"
  showType: ECST_FlyTip
}
rows {
  id: "-20400061"
  zh_CN: "关卡未解锁不能评价"
}
rows {
  id: "-20400085"
  zh_CN: "照片设置picKey未找到图片"
}
rows {
  id: "-20400086"
  zh_CN: "照片编辑picKey未找到原图片"
}
rows {
  id: "-20400087"
  zh_CN: "Arena屏蔽信息配置错误"
}
rows {
  id: "-20400088"
  zh_CN: "主目标系统熟练等级奖励无法领取"
}
rows {
  id: "-20410009"
  zh_CN: "AiNpcGm命令执行失败"
}
rows {
  id: "-20420000"
  zh_CN: "analyze服务内部错误"
}
rows {
  id: "-20480001"
  zh_CN: "无效的红点模块类型"
}
rows {
  id: "-20480002"
  zh_CN: "非点击消失红点"
}
rows {
  id: "-20500002"
  zh_CN: "农场餐厅功能未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20500003"
  zh_CN: "农场餐厅维护中"
  showType: ECST_FlyTip
}
rows {
  id: "-20500004"
  zh_CN: "农场餐厅被下架"
  showType: ECST_FlyTip
}
rows {
  id: "-20500005"
  zh_CN: "农场餐厅找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20500006"
  zh_CN: "匹配中不能进入"
  showType: ECST_FlyTip
}
rows {
  id: "-20500007"
  zh_CN: "被屏蔽了"
  showType: ECST_FlyTip
}
rows {
  id: "-20500008"
  zh_CN: "屏蔽了对方"
  showType: ECST_FlyTip
}
rows {
  id: "-20500009"
  zh_CN: "对方农场的版本过低，无法访问"
  showType: ECST_FlyTip
}
rows {
  id: "-20500010"
  zh_CN: "自己尚未创建农场餐厅"
  showType: ECST_FlyTip
}
rows {
  id: "-20500011"
  zh_CN: "不在操作的餐厅"
  showType: ECST_FlyTip
}
rows {
  id: "-20500012"
  zh_CN: "不支持的餐厅操作"
  showType: ECST_FlyTip
}
rows {
  id: "-20500013"
  zh_CN: "不是餐厅主人操作"
  showType: ECST_FlyTip
}
rows {
  id: "-20500014"
  zh_CN: "只有好友才能被你雇佣"
}
rows {
  id: "-20500015"
  zh_CN: "你已经雇佣了这个好友"
}
rows {
  id: "-20500016"
  zh_CN: "这个好友已经被多人雇佣了"
}
rows {
  id: "-20500017"
  zh_CN: "农场餐厅点评找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20500018"
  zh_CN: "农场餐厅点评发送失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500019"
  zh_CN: "农场餐厅点评删除失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500020"
  zh_CN: "农场餐厅精选失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500021"
  zh_CN: "农场餐厅取消精选失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500022"
  zh_CN: "农场餐厅点评回复发送失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500023"
  zh_CN: "农场餐厅点评回复删除失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500024"
  zh_CN: "农场餐厅点评数量限制"
  showType: ECST_FlyTip
}
rows {
  id: "-20500025"
  zh_CN: "农场餐厅点评精选数量限制"
  showType: ECST_FlyTip
}
rows {
  id: "-20500026"
  zh_CN: "太多贵宾在你的餐厅等待啦，先去邀请他们落座吃饭吧"
  showType: ECST_FlyTip
}
rows {
  id: "-20500027"
  zh_CN: "餐厅贵宾配置找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20500028"
  zh_CN: "餐厅已经预约过了"
  showType: ECST_FlyTip
}
rows {
  id: "-20500029"
  zh_CN: "餐厅上次预约的贵宾还没接待完成"
  showType: ECST_FlyTip
}
rows {
  id: "-20500030"
  zh_CN: "餐厅贵宾不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20500031"
  zh_CN: "餐厅贵宾拿取在冷却中"
  showType: ECST_FlyTip
}
rows {
  id: "-20500032"
  zh_CN: "餐厅贵宾尚在保护期内"
  showType: ECST_FlyTip
}
rows {
  id: "-20500033"
  zh_CN: "农场餐厅顾客找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20500034"
  zh_CN: "农场餐厅已经满级"
  showType: ECST_FlyTip
}
rows {
  id: "-20500035"
  zh_CN: "农场餐厅升级所需点赞不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20500036"
  zh_CN: "农场餐厅升级所需农场等级不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20500037"
  zh_CN: "农场餐厅尚未开放"
  showType: ECST_FlyTip
}
rows {
  id: "-20500038"
  zh_CN: "农场餐厅点评更新失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500039"
  zh_CN: "农场餐厅点评间隔过短"
  showType: ECST_FlyTip
}
rows {
  id: "-20500040"
  zh_CN: "农场餐厅不是点评顾客"
  showType: ECST_FlyTip
}
rows {
  id: "-20500041"
  zh_CN: "农场餐厅开店菜单不满"
  showType: ECST_FlyTip
}
rows {
  id: "-20500042"
  zh_CN: "农场餐厅菜谱未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-20500043"
  zh_CN: "农场等级配置不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20500044"
  zh_CN: "农场员工刷新费用不存在"
  showType: ECST_FlyTip
}
rows {
  id: "-20500045"
  zh_CN: "农场餐厅雇佣刷新次数今日已满"
  showType: ECST_FlyTip
}
rows {
  id: "-20500046"
  zh_CN: "农场餐厅人才市场刷新职业错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20500047"
  zh_CN: "农场餐厅员工随机Avatar失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500048"
  zh_CN: "农场员工随机昵称失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500049"
  zh_CN: "农场餐厅员工已满，请先解雇一些员工再来招聘吧"
  showType: ECST_FlyTip
}
rows {
  id: "-20500050"
  zh_CN: "农场餐厅该人才已被雇佣"
  showType: ECST_FlyTip
}
rows {
  id: "-20500051"
  zh_CN: "农场餐厅员工工作人数已满"
  showType: ECST_FlyTip
}
rows {
  id: "-20500052"
  zh_CN: "农场餐厅贵宾不在接待中"
  showType: ECST_FlyTip
}
rows {
  id: "-20500053"
  zh_CN: "农场餐厅贵宾不在等待中"
  showType: ECST_FlyTip
}
rows {
  id: "-20500054"
  zh_CN: "农场餐厅贵宾UID未分配"
  showType: ECST_FlyTip
}
rows {
  id: "-20500055"
  zh_CN: "农场餐厅贵宾UID重复"
  showType: ECST_FlyTip
}
rows {
  id: "-20500056"
  zh_CN: "农场餐厅员工已工作"
  showType: ECST_FlyTip
}
rows {
  id: "-20500057"
  zh_CN: "农场餐厅员工已休息"
  showType: ECST_FlyTip
}
rows {
  id: "-20500058"
  zh_CN: "农场餐厅员工找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20500059"
  zh_CN: "餐厅里没有空的厨灶，升级餐厅可能增加可摆放的厨灶数量"
  showType: ECST_FlyTip
}
rows {
  id: "-20500060"
  zh_CN: "农场餐厅找不到可用服务员位置"
  showType: ECST_FlyTip
}
rows {
  id: "-20500061"
  zh_CN: "农场餐厅流动显示屏自定义文本功能未开启"
  showType: ECST_FlyTip
}
rows {
  id: "-20500062"
  zh_CN: "农场餐厅流动显示屏自定义文本达到长度限制"
  showType: ECST_FlyTip
}
rows {
  id: "-20500063"
  zh_CN: "农场餐厅流动显示屏自定义文本禁止设置"
  showType: ECST_FlyTip
}
rows {
  id: "-20500064"
  zh_CN: "农场餐厅贵宾不可预约"
  showType: ECST_FlyTip
}
rows {
  id: "-20500066"
  zh_CN: "农场员工随机品质失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500067"
  zh_CN: "农场员工属性表读取失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500068"
  zh_CN: "预约未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-20500069"
  zh_CN: "餐厅贵宾组配置找不到"
  showType: ECST_FlyTip
}
rows {
  id: "-20500070"
  zh_CN: "餐厅不支持的员工类型"
  showType: ECST_FlyTip
}
rows {
  id: "-20500071"
  zh_CN: "餐厅员工类型错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20500072"
  zh_CN: "餐厅员工雇佣未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-20500073"
  zh_CN: "当前没有菜品可以提供，请先备菜开始经营吧"
  showType: ECST_FlyTip
}
rows {
  id: "-20500074"
  zh_CN: "餐厅图纸不能保存员工"
  showType: ECST_FlyTip
}
rows {
  id: "-20500075"
  zh_CN: "餐厅点评未解锁"
  showType: ECST_FlyTip
}
rows {
  id: "-20500076"
  zh_CN: "农场餐厅点评回复内容为空"
  showType: ECST_FlyTip
}
rows {
  id: "-20500077"
  zh_CN: "农场餐厅点评回复字数超上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20500078"
  zh_CN: "农场餐厅点评回复禁止发送"
  showType: ECST_FlyTip
}
rows {
  id: "-20500079"
  zh_CN: "农场餐厅每日点评回复数量超上限"
  showType: ECST_FlyTip
}
rows {
  id: "-10041600"
  zh_CN: "无效的队伍ID"
}
rows {
  id: "-10041601"
  zh_CN: "找不到队伍配置"
}
rows {
  id: "-10041602"
  zh_CN: "队伍锁定失败"
}
rows {
  id: "-10041603"
  zh_CN: "队伍查询失败"
}
rows {
  id: "-10041604"
  zh_CN: "玩家不在队伍中"
}
rows {
  id: "-10041605"
  zh_CN: "队伍创建失败"
}
rows {
  id: "-10041606"
  zh_CN: "不允许退出队伍"
}
rows {
  id: "-10041607"
  zh_CN: "玩家已在队伍中"
}
rows {
  id: "-10041608"
  zh_CN: "队伍已满"
}
rows {
  id: "-10041609"
  zh_CN: "队伍为空"
}
rows {
  id: "-10041610"
  zh_CN: "队伍数据未找到"
}
rows {
  id: "-10041611"
  zh_CN: "队伍数据更新失败"
}
rows {
  id: "-10600604"
  zh_CN: "http请求出现异常"
}
rows {
  id: "-10600605"
  zh_CN: "httpaction出现异常"
}
rows {
  id: "-10600606"
  zh_CN: "url is blank"
}
rows {
  id: "-10600607"
  zh_CN: "type error"
}
rows {
  id: "-10600608"
  zh_CN: "http body出现异常"
}
rows {
  id: "-10600609"
  zh_CN: "模块已关闭"
}
rows {
  id: "-20017067"
  zh_CN: "快速加入不允许中途加入"
}
rows {
  id: "-20017068"
  zh_CN: "快速加入人数爆满了"
}
rows {
  id: "-20017069"
  zh_CN: "快速加入一般错误"
}
rows {
  id: "-20017070"
  zh_CN: "快速加入超时"
}
rows {
  id: "-20100086"
  zh_CN: "未找到数据"
}
rows {
  id: "-20100087"
  zh_CN: "未找到处理函数"
}
rows {
  id: "-20100088"
  zh_CN: "祈愿支付失败"
}
rows {
  id: "-20100089"
  zh_CN: "祈愿发奖失败"
}
rows {
  id: "-20240012"
  zh_CN: "回归手册奖励已领完"
}
rows {
  id: "-20240013"
  zh_CN: "没有离线奖励可领取"
}
rows {
  id: "-20300408"
  zh_CN: "农场不能直接完成后续所有任务"
  showType: ECST_FlyTip
}
rows {
  id: "-20300409"
  zh_CN: "农场餐厅随机数值范围失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20390146"
  zh_CN: "通灵兽进化必须收集过一次"
  showType: ECST_FlyTip
}
rows {
  id: "-20400089"
  zh_CN: "兑换碎片道具不足"
}
rows {
  id: "-20500080"
  zh_CN: "农场餐厅点赞不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20300410"
  zh_CN: "由于对方权限设置，暂时无法回复"
  showType: ECST_FlyTip
}
rows {
  id: "-21000004"
  zh_CN: "-21000004"
}
rows {
  id: "-10000526"
  zh_CN: "StarP Idip消息入库失败"
}
rows {
  id: "-10000112"
  zh_CN: "不允许客户端申请该cos权限"
}
rows {
  id: "-10001059"
  zh_CN: "账号转区中"
}
rows {
  id: "-10001060"
  zh_CN: "账号转区备份失败"
}
rows {
  id: "-10001061"
  zh_CN: "账号转区RPC失败"
}
rows {
  id: "-10001062"
  zh_CN: "账号转区不可加载缓存"
}
rows {
  id: "-10001063"
  zh_CN: "账号转区功能关闭"
}
rows {
  id: "-10001064"
  zh_CN: "当前登录的设备未提供权限"
}
rows {
  id: "-10001065"
  zh_CN: "因为IP变更遭到账号封禁"
}
rows {
  id: "-10001066"
  zh_CN: "IP所属城市不允许登录，新加这个错误码是因为客户端需要特殊提示"
}
rows {
  id: "-10001067"
  zh_CN: "模拟器不允许登录"
}
rows {
  id: "-10010089"
  zh_CN: "平台好友稳固登入后通知超出限制"
}
rows {
  id: "-10010090"
  zh_CN: "请求得好友uid为空"
}
rows {
  id: "-10010091"
  zh_CN: "好友信息不在IntimateRelationInfo中"
}
rows {
  id: "-10041225"
  zh_CN: "今日狗狗已出行过"
}
rows {
  id: "-10041226"
  zh_CN: "狗狗旅行配置不存在"
}
rows {
  id: "-10041227"
  zh_CN: "狗狗未出行"
}
rows {
  id: "-10041228"
  zh_CN: "狗狗未到回归时间"
}
rows {
  id: "-10041229"
  zh_CN: "宝箱升级活动任务奖励仅周末可领取"
}
rows {
  id: "-10041230"
  zh_CN: "宝箱升级活动宝箱奖励仅工作日可领取"
}
rows {
  id: "-10041231"
  zh_CN: "宝箱升级活动宝箱奖励已领取"
}
rows {
  id: "-10041232"
  zh_CN: "宝箱升级活动配置不存在"
}
rows {
  id: "-10100092"
  zh_CN: "初试装备配置不存在"
}
rows {
  id: "-10100093"
  zh_CN: "已获得初试装备"
}
rows {
  id: "-10170052"
  zh_CN: "聊天频道添加失败"
}
rows {
  id: "-10170053"
  zh_CN: "聊天频道移除失败"
}
rows {
  id: "-10600343"
  zh_CN: "地图操作异常"
}
rows {
  id: "-10600610"
  zh_CN: "数据库操作失败"
}
rows {
  id: "-10600611"
  zh_CN: "骨骼不存在"
}
rows {
  id: "-10600612"
  zh_CN: "设置首页标签数量超过限制"
}
rows {
  id: "-10600613"
  zh_CN: "设置首页标签ID不存在"
}
rows {
  id: "-10600614"
  zh_CN: "今天设置首页标签次数已用完"
}
rows {
  id: "-10600615"
  zh_CN: "ugc当前场景已存在"
}
rows {
  id: "-10600616"
  zh_CN: "http北极星需要url为sid:namespace"
}
rows {
  id: "-10701019"
  zh_CN: "事件触发频率太高"
  showType: ECST_FlyTip
}
rows {
  id: "-10940001"
  zh_CN: "缓存可迁移"
}
rows {
  id: "-10940002"
  zh_CN: "缓存管理器内部错误"
}
rows {
  id: "-20850000"
  zh_CN: "啾灵账号登对象查询失败"
}
rows {
  id: "-20850001"
  zh_CN: "啾灵账号登录状态非法"
}
rows {
  id: "-20850002"
  zh_CN: "啾灵账号数据禁止回写"
}
rows {
  id: "-20850003"
  zh_CN: "已有角色,无法踢人成功不可重复登录"
}
rows {
  id: "-20850004"
  zh_CN: "角色不存在世界"
}
rows {
  id: "-20850105"
  zh_CN: "非法user"
}
rows {
  id: "-20850106"
  zh_CN: "user数量超出上限"
}
rows {
  id: "-20850107"
  zh_CN: "非法装扮"
}
rows {
  id: "-20850108"
  zh_CN: "需要创建角色"
}
rows {
  id: "-20850109"
  zh_CN: "参与的啾灵世界超上限"
}
rows {
  id: "-20850110"
  zh_CN: "需要删除所有的世界"
}
rows {
  id: "-20850111"
  zh_CN: "非法更新类型"
}
rows {
  id: "-20850112"
  zh_CN: "该角色槽位无效"
}
rows {
  id: "-20850113"
  zh_CN: "跨房间频繁"
}
rows {
  id: "-20850114"
  zh_CN: "StarPPlayerMgr为空"
}
rows {
  id: "-20860000"
  zh_CN: "账号不在cache中"
}
rows {
  id: "-20950000"
  zh_CN: "需要先创建DS"
}
rows {
  id: "-20950001"
  zh_CN: "房间名不合法"
}
rows {
  id: "-20950002"
  zh_CN: "房间描述不合法"
}
rows {
  id: "-20950003"
  zh_CN: "创建房间失败"
}
rows {
  id: "-20950004"
  zh_CN: "房间找不到"
}
rows {
  id: "-20950005"
  zh_CN: "禁止角色申请房间"
}
rows {
  id: "-20950006"
  zh_CN: "已有角色,不可重复申请"
}
rows {
  id: "-20950007"
  zh_CN: "已存在申请列表中"
}
rows {
  id: "-20950008"
  zh_CN: "密码错误"
}
rows {
  id: "-20950009"
  zh_CN: "角色已达上限"
}
rows {
  id: "-20950010"
  zh_CN: "申请加入房间异常"
}
rows {
  id: "-20950011"
  zh_CN: "非房间管理员"
}
rows {
  id: "-20950012"
  zh_CN: "私密房间不允许发布"
}
rows {
  id: "-20950013"
  zh_CN: "管理房间数量已达上限"
}
rows {
  id: "-20950014"
  zh_CN: "申请数量过多"
}
rows {
  id: "-20950015"
  zh_CN: "角色封禁中"
}
rows {
  id: "-20950016"
  zh_CN: "找不到角色"
}
rows {
  id: "-20950017"
  zh_CN: "申请已过期"
}
rows {
  id: "-20950018"
  zh_CN: "插入成员并发覆盖版本号错误"
}
rows {
  id: "-20950019"
  zh_CN: "进入失败"
}
rows {
  id: "-20950020"
  zh_CN: "超过48小时无法撤销用户删除"
}
rows {
  id: "-20950021"
  zh_CN: "角色删除中"
}
rows {
  id: "-20950022"
  zh_CN: "请求参数非法"
}
rows {
  id: "-20950023"
  zh_CN: "用户当前状态为封禁或删除中，不支持设置为管理员"
}
rows {
  id: "-20950024"
  zh_CN: "管理员权限已转移或已恢复，不支持抢夺管理权限"
}
rows {
  id: "-20950025"
  zh_CN: "当前为队伍啾灵模式,无法切换到元梦正常模式!"
}
rows {
  id: "-20950026"
  zh_CN: "当前为队伍帕啾灵模式,玩家啾灵角色数据异常,无法加入!"
}
rows {
  id: "-20950027"
  zh_CN: "操作频繁，请稍后再试"
}
rows {
  id: "-20950028"
  zh_CN: "用户为非待删除状态，不能强制删除"
}
rows {
  id: "-20950029"
  zh_CN: "用户等级较高，暂不支持删除"
}
rows {
  id: "-20950030"
  zh_CN: "用户已在世界内"
}
rows {
  id: "-20950031"
  zh_CN: "世界类型不允许邀请"
}
rows {
  id: "-20950032"
  zh_CN: "对方的邀请列表已满"
}
rows {
  id: "-20950033"
  zh_CN: "公会处理删除用户信息失败"
}
rows {
  id: "-20950034"
  zh_CN: "无效的公会名"
}
rows {
  id: "-20950035"
  zh_CN: "该公会名已被占用"
}
rows {
  id: "-20950036"
  zh_CN: "房间角色已达上限"
}
rows {
  id: "-20950037"
  zh_CN: "获取玩家世界信息类型错误"
}
rows {
  id: "-20950038"
  zh_CN: "获取玩家世界信息DB错误"
}
rows {
  id: "-20950039"
  zh_CN: "当前状态idip无法登录"
}
rows {
  id: "-20950040"
  zh_CN: "更新玩家状态失败"
}
rows {
  id: "-20950041"
  zh_CN: "操作过于频繁,请稍后再试"
}
rows {
  id: "-20950042"
  zh_CN: "获取玩家DsCommonDbInfoTable数据错误"
}
rows {
  id: "-20950043"
  zh_CN: "房间邀请列表已满"
}
rows {
  id: "-20950044"
  zh_CN: "频繁创建房间"
}
rows {
  id: "-20950045"
  zh_CN: "创建官方房间失败"
}
rows {
  id: "-20950046"
  zh_CN: "好友的世界拜访者较多，请稍后再试"
}
rows {
  id: "-20950047"
  zh_CN: "服务器尝试连接，请稍后"
}
rows {
  id: "-20950048"
  zh_CN: "访客访问时间未到期,无法申请加入该房间"
}
rows {
  id: "-20950049"
  zh_CN: "该角色今日访问次数已达上限"
}
rows {
  id: "-20950050"
  zh_CN: "已是当前世界成员,禁止访问"
}
rows {
  id: "-20950051"
  zh_CN: "拉取失败"
}
rows {
  id: "-20950052"
  zh_CN: "世界等级不能为0"
}
rows {
  id: "-20950053"
  zh_CN: "世界等级小于旧的等级"
}
rows {
  id: "-20950054"
  zh_CN: "获取玩家DsCommonDbInfo数据失败"
}
rows {
  id: "-20950055"
  zh_CN: "房间创建中，请等待"
}
rows {
  id: "-20950056"
  zh_CN: "房间版本号不匹配"
}
rows {
  id: "-20950057"
  zh_CN: "屏蔽字"
}
rows {
  id: "-20950058"
  zh_CN: "计算ds的groupId失败"
}
rows {
  id: "-20950059"
  zh_CN: "房间已经存在"
}
rows {
  id: "-20950060"
  zh_CN: "进入DS addPlayer IRPC调用超时"
}
rows {
  id: "-20950061"
  zh_CN: "进入DS addPlayer返回错误"
}
rows {
  id: "-20950062"
  zh_CN: "房间DS信息找不到"
}
rows {
  id: "-20950063"
  zh_CN: "房间DS信息GameSession信息不匹配"
}
rows {
  id: "-20950064"
  zh_CN: "爬塔发起挑战失败"
}
rows {
  id: "-20950065"
  zh_CN: "匹配中，不允许进入星灵飞船"
}
rows {
  id: "-20950066"
  zh_CN: "匹配中，不允许进入星灵世界"
}
rows {
  id: "-20950067"
  zh_CN: "DB获取自动创建的公会名前缀失败"
}
rows {
  id: "-20950068"
  zh_CN: "插入公会名字并发覆盖版本号错误"
}
rows {
  id: "-20950069"
  zh_CN: "房间删除列表已满"
}
rows {
  id: "-20950070"
  zh_CN: "房间有未继承的角色"
}
rows {
  id: "-20950071"
  zh_CN: "队伍ID同步ds失败"
}
rows {
  id: "-20950072"
  zh_CN: "删除啾灵角色过于频繁"
}
rows {
  id: "-20950073"
  zh_CN: "管理员强制删除预删除玩家信息，需要等待"
}
rows {
  id: "-20950074"
  zh_CN: "IDIP找不到目标StarPWorld"
}
rows {
  id: "-20950075"
  zh_CN: "IDIP找不到目标玩家"
}
rows {
  id: "-20950076"
  zh_CN: "获取abtest标签失败"
}
rows {
  id: "-20950077"
  zh_CN: "ES在bulk存储时发现世界不存在"
}
rows {
  id: "-20950078"
  zh_CN: "通知startDs失败"
}
rows {
  id: "-20950079"
  zh_CN: "好友尚未建立据点，无法拜访"
}
rows {
  id: "-20950080"
  zh_CN: "好友与你在同一星球，无法前往"
}
rows {
  id: "-20950081"
  zh_CN: "DS迁移前业务检查中"
}
rows {
  id: "-20950082"
  zh_CN: "DS迁移中"
}
rows {
  id: "-20950083"
  zh_CN: "频繁创建角色"
}
rows {
  id: "-20950084"
  zh_CN: "创建角色失败"
}
rows {
  id: "-20950085"
  zh_CN: "访客禁止创建队伍"
}
rows {
  id: "-20950086"
  zh_CN: "不允许移交队长给访客"
}
rows {
  id: "-20950087"
  zh_CN: "正在加入队伍中，无法操作"
}
rows {
  id: "-20950088"
  zh_CN: "申请房间，但是没有正常加入"
}
rows {
  id: "-20950089"
  zh_CN: "无效的离线db类型"
}
rows {
  id: "-20950091"
  zh_CN: "房间为开放，无法获取相关信息"
}
rows {
  id: "-20950100"
  zh_CN: "本世界的可邀请人数已达上限！"
}
rows {
  id: "-20950101"
  zh_CN: "对同一账号的邀请还在冷却期内"
}
rows {
  id: "-20950102"
  zh_CN: "StarPWorldMgr为空"
}
rows {
  id: "-20950103"
  zh_CN: "只允许机器人进行该行为;"
}
rows {
  id: "-20950104"
  zh_CN: "ES服务器关闭"
}
rows {
  id: "-20960001"
  zh_CN: "邮件ID已存在"
}
rows {
  id: "-20960002"
  zh_CN: "邮件还未读"
}
rows {
  id: "-20960003"
  zh_CN: "邮件附件还未领取"
}
rows {
  id: "-20960004"
  zh_CN: "邮件附件已领取"
}
rows {
  id: "-20960005"
  zh_CN: "不是拥有者"
}
rows {
  id: "-20960006"
  zh_CN: "重复删除"
}
rows {
  id: "-20960007"
  zh_CN: "邮件过多"
}
rows {
  id: "-20960008"
  zh_CN: "相同的状态"
}
rows {
  id: "-20960009"
  zh_CN: "邮件线索ID已存在"
}
rows {
  id: "-20960010"
  zh_CN: "邮件已领取标记不能改为未领取"
}
rows {
  id: "-20960011"
  zh_CN: "缓存不存在"
}
rows {
  id: "-20960012"
  zh_CN: "未知的操作"
}
rows {
  id: "-20970001"
  zh_CN: "非法的key1"
}
rows {
  id: "-20970002"
  zh_CN: "非法的partKey"
}
rows {
  id: "-20970003"
  zh_CN: "不存在的fullKey"
}
rows {
  id: "-20970004"
  zh_CN: "不允许的有间隔的partKey"
}
rows {
  id: "-20970005"
  zh_CN: "需要fullKey"
}
rows {
  id: "-20970006"
  zh_CN: "Tcaplus返回了错误"
}
rows {
  id: "-20970007"
  zh_CN: "业务逻辑发生了多写"
}
rows {
  id: "-20970008"
  zh_CN: "ds上编码发生了错误"
}
rows {
  id: "-20970009"
  zh_CN: "缓存不存在"
}
rows {
  id: "-20970010"
  zh_CN: "数据过大"
}
rows {
  id: "-20970011"
  zh_CN: "该表不在白名单内，不能通过该CS协议拉取"
}
rows {
  id: "-20980001"
  zh_CN: "每日奖励已领取"
}
rows {
  id: "-20980002"
  zh_CN: "取冒险团等级对应的配置是吧"
}
rows {
  id: "-20980003"
  zh_CN: "取冒险团等级数据失败"
}
rows {
  id: "-20980004"
  zh_CN: "冒险团商店币已经超过int最大正整数"
}
rows {
  id: "20980001"
  zh_CN: "系统内部错误"
}
rows {
  id: "20980002"
  zh_CN: "通用卡池拒绝错误"
}
rows {
  id: "20980003"
  zh_CN: "卡池不在开放时间内"
}
rows {
  id: "20980004"
  zh_CN: "达到总抽卡数上限"
}
rows {
  id: "20980005"
  zh_CN: "达到周期抽卡数上限"
}
rows {
  id: "20980006"
  zh_CN: "没有足够代币"
}
rows {
  id: "20980007"
  zh_CN: "缺失对应配置"
}
rows {
  id: "20980008"
  zh_CN: "无奖励可抽取"
}
rows {
  id: "20980009"
  zh_CN: "玩家不存在"
}
rows {
  id: "20980010"
  zh_CN: "生成奖励失败"
}
rows {
  id: "20980011"
  zh_CN: "RewardList长度与LotteryCnt不符合"
}
rows {
  id: "20980101"
  zh_CN: "啾灵交换关注数据异常"
}
rows {
  id: "20980102"
  zh_CN: "啾灵交换关注达上限"
}
rows {
  id: "20980103"
  zh_CN: "啾灵交换心愿单中啾灵ID异常"
}
rows {
  id: "20980104"
  zh_CN: "啾灵交换心愿单中可以给的啾灵数量为0或过多"
}
rows {
  id: "20980105"
  zh_CN: "啾灵交换心愿单备注长度超限"
}
rows {
  id: "20980106"
  zh_CN: "啾灵交换心愿单备注存在敏感词"
}
rows {
  id: "20980107"
  zh_CN: "啾灵交换心愿不存在"
}
rows {
  id: "20980201"
  zh_CN: "发布订单正处于CD中"
}
rows {
  id: "20980202"
  zh_CN: "未完成的订单数量超过上限"
}
rows {
  id: "20980203"
  zh_CN: "每日发布订单数量超过上限"
}
rows {
  id: "20980204"
  zh_CN: "订单已被完成"
}
rows {
  id: "20980205"
  zh_CN: "订单材料捐助数量超过上限"
}
rows {
  id: "20980206"
  zh_CN: "订单不存在"
}
rows {
  id: "20980207"
  zh_CN: "订单材料不存在"
}
rows {
  id: "20980208"
  zh_CN: "订单已被完成"
}
rows {
  id: "20980209"
  zh_CN: "该玩家未捐助"
}
rows {
  id: "20980210"
  zh_CN: "捐助的材料错误"
}
rows {
  id: "20980211"
  zh_CN: "不能请求该材料"
}
rows {
  id: "20980212"
  zh_CN: "材料请求的数量超过上限"
}
rows {
  id: "20980213"
  zh_CN: "该玩家已被点赞"
}
rows {
  id: "20980214"
  zh_CN: "该玩家并未捐助材料"
}
rows {
  id: "20980215"
  zh_CN: "该玩家并未捐助材料"
}
rows {
  id: "20980301"
  zh_CN: "玩家不在线"
}
rows {
  id: "20980302"
  zh_CN: "该交互类型不能增加亲密度"
}
rows {
  id: "20980303"
  zh_CN: "不是好友"
}
rows {
  id: "20980304"
  zh_CN: "玩家没有好友"
}
rows {
  id: "20980305"
  zh_CN: "亲密度增长超过了每日上限"
}
rows {
  id: "20980306"
  zh_CN: "亲密度增长超过了每日行为上限"
}
rows {
  id: "20980307"
  zh_CN: "亲密度增长超过了总上限"
}
rows {
  id: "-20990001"
  zh_CN: "系统内部错误"
}
rows {
  id: "-20990002"
  zh_CN: "重复创建公会"
}
rows {
  id: "-20990003"
  zh_CN: "公会名不到最小长度限制"
}
rows {
  id: "-20990004"
  zh_CN: "公会名超过最大长度限制"
}
rows {
  id: "-20990005"
  zh_CN: "公会不存在"
}
rows {
  id: "-20990006"
  zh_CN: "不是公会成员"
}
rows {
  id: "-20990007"
  zh_CN: "公会类型错误"
}
rows {
  id: "-20990008"
  zh_CN: "无效公会图标"
}
rows {
  id: "-20990009"
  zh_CN: "创建公会CD"
}
rows {
  id: "-20990010"
  zh_CN: "公会成员无权操作"
}
rows {
  id: "-20990011"
  zh_CN: "公会已经是个人公会"
}
rows {
  id: "-20990012"
  zh_CN: "公会还有其他成员"
}
rows {
  id: "-20990013"
  zh_CN: "成员已在公会中"
}
rows {
  id: "-20990014"
  zh_CN: "公会状态不正确"
}
rows {
  id: "-20990015"
  zh_CN: "没有离开当前公会"
}
rows {
  id: "-20990016"
  zh_CN: "公会人数已满"
}
rows {
  id: "-20990017"
  zh_CN: "已经申请过"
}
rows {
  id: "-20990018"
  zh_CN: "有终端，无法加入公会"
}
rows {
  id: "-20990019"
  zh_CN: "无法从正式公会中加入公会"
}
rows {
  id: "-20990020"
  zh_CN: "无权加入公会"
}
rows {
  id: "-20990021"
  zh_CN: "加入公会cd"
}
rows {
  id: "-20990022"
  zh_CN: "配置读取错误"
}
rows {
  id: "-20990023"
  zh_CN: "申请不存在"
}
rows {
  id: "-20990024"
  zh_CN: "申请已经处理"
}
rows {
  id: "-20990025"
  zh_CN: "存在个人中枢无法加入"
}
rows {
  id: "-20990026"
  zh_CN: "已经邀请过"
}
rows {
  id: "-20990027"
  zh_CN: "成员已在其他正式公会中"
}
rows {
  id: "-20990028"
  zh_CN: "不允许退出个人公会"
}
rows {
  id: "-20990029"
  zh_CN: "会长不能退出公会"
}
rows {
  id: "-20990030"
  zh_CN: "不能给自己设置职务"
}
rows {
  id: "-20990031"
  zh_CN: "成员等待被踢出"
}
rows {
  id: "-20990032"
  zh_CN: "成员等待被踢出"
}
rows {
  id: "-20990033"
  zh_CN: "邀请不存在"
}
rows {
  id: "-20990034"
  zh_CN: "公会成员在pve中"
}
rows {
  id: "-20990035"
  zh_CN: "公会属性参数无效"
}
rows {
  id: "-20990036"
  zh_CN: "部落请求参数非法"
}
rows {
  id: "-20990037"
  zh_CN: "StarP部落功能未开启"
}
rows {
  id: "-20990101"
  zh_CN: "系统内部错误"
}
rows {
  id: "-20990102"
  zh_CN: "重复创建宗门"
}
rows {
  id: "-20990103"
  zh_CN: "宗门名不到最小长度限制"
}
rows {
  id: "-20990104"
  zh_CN: "宗门名超过最大长度限制"
}
rows {
  id: "-20990105"
  zh_CN: "宗门不存在"
}
rows {
  id: "-20990106"
  zh_CN: "不是宗门成员"
}
rows {
  id: "-20990107"
  zh_CN: "宗门类型错误"
}
rows {
  id: "-20990108"
  zh_CN: "无效宗门图标"
}
rows {
  id: "-20990109"
  zh_CN: "创建宗门CD"
}
rows {
  id: "-20990110"
  zh_CN: "宗门成员无权操作"
}
rows {
  id: "-20990111"
  zh_CN: "宗门已经是个人宗门"
}
rows {
  id: "-20990112"
  zh_CN: "宗门还有其他成员"
}
rows {
  id: "-20990113"
  zh_CN: "成员已在宗门中"
}
rows {
  id: "-20990114"
  zh_CN: "宗门状态不正确"
}
rows {
  id: "-20990115"
  zh_CN: "没有离开当前宗门"
}
rows {
  id: "-20990116"
  zh_CN: "宗门人数已满"
}
rows {
  id: "-20990117"
  zh_CN: "已经申请过"
}
rows {
  id: "-20990118"
  zh_CN: "有终端，无法加入宗门"
}
rows {
  id: "-20990119"
  zh_CN: "无法从正式宗门中加入宗门"
}
rows {
  id: "-20990120"
  zh_CN: "无权加入宗门"
}
rows {
  id: "-20990121"
  zh_CN: "加入宗门cd"
}
rows {
  id: "-20990122"
  zh_CN: "配置读取错误"
}
rows {
  id: "-20990123"
  zh_CN: "申请不存在"
}
rows {
  id: "-20990124"
  zh_CN: "申请已经处理"
}
rows {
  id: "-20990125"
  zh_CN: "存在个人中枢无法加入"
}
rows {
  id: "-20990126"
  zh_CN: "已经邀请过"
}
rows {
  id: "-20990127"
  zh_CN: "成员已在其他正式宗门中"
}
rows {
  id: "-20990128"
  zh_CN: "不允许退出个人宗门"
}
rows {
  id: "-20990129"
  zh_CN: "会长不能退出宗门"
}
rows {
  id: "-20990130"
  zh_CN: "不能给自己设置职务"
}
rows {
  id: "-20990131"
  zh_CN: "成员等待被踢出"
}
rows {
  id: "-20990132"
  zh_CN: "成员等待被踢出"
}
rows {
  id: "-20990133"
  zh_CN: "邀请不存在"
}
rows {
  id: "-20990134"
  zh_CN: "宗门成员在pve中"
}
rows {
  id: "-20990135"
  zh_CN: "宗门属性参数无效"
}
rows {
  id: "-20990136"
  zh_CN: "无效的公会名"
}
rows {
  id: "-20990137"
  zh_CN: "宗门活跃度计算错误"
}
rows {
  id: "-20990138"
  zh_CN: "宗门活跃度重复增加"
}
rows {
  id: "-20990139"
  zh_CN: "StarP宗门功能未开启"
}
rows {
  id: "-20990140"
  zh_CN: "宗门人员不活跃，被动解散"
}
rows {
  id: "20990141"
  zh_CN: "宗门描述无效"
}
rows {
  id: "-20990150"
  zh_CN: "宗门不公开招募，不能从推荐列表加入，请刷新推荐列表"
}
rows {
  id: "-20990151"
  zh_CN: "宗门变更消息保存失败"
}
rows {
  id: "-20990152"
  zh_CN: "宗门变更消息发送失败"
}
rows {
  id: "-20990153"
  zh_CN: "Tcaplus返回了错误"
}
rows {
  id: "-21000001"
  zh_CN: "无效db入参"
}
rows {
  id: "-21000002"
  zh_CN: "数据不存在"
}
rows {
  id: "-21000003"
  zh_CN: "组织类型错误"
}
rows {
  id: "-21010001"
  zh_CN: "宠物配置没找到"
}
rows {
  id: "-21010002"
  zh_CN: "宠物类型配置没找到"
}
rows {
  id: "-21010003"
  zh_CN: "不是乘骑坐骑"
}
rows {
  id: "-21010004"
  zh_CN: "宠物数量超过上限"
}
rows {
  id: "-21010005"
  zh_CN: "宠物阵容数量错误"
}
rows {
  id: "-21010006"
  zh_CN: "处于匹配惩罚状态中，无法开始匹配"
}
rows {
  id: "-21010007"
  zh_CN: "每日奖励处于不可领取状态"
}
rows {
  id: "-21010008"
  zh_CN: "每日奖励已领取"
}
rows {
  id: "-21010010"
  zh_CN: "你不在PVP白名单内"
}
rows {
  id: "-21010011"
  zh_CN: "队伍中有人不在PVP白名单内"
}
rows {
  id: "-21010012"
  zh_CN: "pvp因双方都没连接上开局失败"
}
rows {
  id: "-21020001"
  zh_CN: "系统内部错误"
}
rows {
  id: "-21020002"
  zh_CN: "商店配置未找到"
}
rows {
  id: "-21020003"
  zh_CN: "服务器内部错误"
}
rows {
  id: "-21020004"
  zh_CN: "冒险团等级不足"
}
rows {
  id: "-21020005"
  zh_CN: "商店购买次数达到上限"
}
rows {
  id: "-21020006"
  zh_CN: "商店购买货币不足"
}
rows {
  id: "-21020007"
  zh_CN: "商店购买发送奖励邮件失败"
}
rows {
  id: "-21030001"
  zh_CN: "玩家已经加入过Group"
}
rows {
  id: "-21030002"
  zh_CN: "玩家不在部落中"
}
rows {
  id: "-21030003"
  zh_CN: "退出条件不足"
}
rows {
  id: "-21030004"
  zh_CN: "部落系统内部错误"
}
rows {
  id: "-21030005"
  zh_CN: "部落首领禁止退出"
}
rows {
  id: "-21030006"
  zh_CN: "部落系统内部错误"
}
rows {
  id: "-21030007"
  zh_CN: "部落找不到合适的新首领"
}
rows {
  id: "-21030008"
  zh_CN: "宗门名称已存在"
}
rows {
  id: "-21030009"
  zh_CN: "权限不足"
}
rows {
  id: "-21030010"
  zh_CN: "不在申请列表"
}
rows {
  id: "-21030011"
  zh_CN: "部落操作官职不够"
}
rows {
  id: "-21030012"
  zh_CN: "宗门es搜索失败"
}
rows {
  id: "-21030013"
  zh_CN: "部落成员到达上限"
}
rows {
  id: "-21030014"
  zh_CN: "玩家不在宗门中"
}
rows {
  id: "-21030015"
  zh_CN: "不在邀请列表"
}
rows {
  id: "-21030016"
  zh_CN: "部落最大终端等级不足"
}
rows {
  id: "-21040001"
  zh_CN: "数据库错误"
}
rows {
  id: "-21040002"
  zh_CN: "不支持的卡片类型"
}
rows {
  id: "-21040003"
  zh_CN: "配置错误"
}
rows {
  id: "-21040004"
  zh_CN: "卡片不存在"
}
rows {
  id: "-21040005"
  zh_CN: "卡片已过期"
}
rows {
  id: "-21040006"
  zh_CN: "当前发送的卡片数量已达上限"
}
rows {
  id: "-21040007"
  zh_CN: "对当前玩家发送的该类型的卡片已达到上限"
}
rows {
  id: "-20010133"
  zh_CN: "当前房间中有成员处于CD中"
}
rows {
  id: "-20017071"
  zh_CN: "类型异常，需要检查代码"
}
rows {
  id: "-20017072"
  zh_CN: "不允许组队快速加入"
}
rows {
  id: "-20017073"
  zh_CN: "有成员处于非空闲状态"
}
rows {
  id: "-20017074"
  zh_CN: "有成员不是在同一个啾灵世界"
}
rows {
  id: "-20017075"
  zh_CN: "点击过于频繁，请稍后再试"
}
rows {
  id: "-20017076"
  zh_CN: "有成员未解锁该副本关卡"
}
rows {
  id: "-20017077"
  zh_CN: "未解锁该队伍要挑战的关卡，无法加入"
}
rows {
  id: "-20017078"
  zh_CN: "与队长不在同一世界，无法加入"
}
rows {
  id: "-20020449"
  zh_CN: "ds加载局内玩家数据失败"
}
rows {
  id: "-20211501"
  zh_CN: "非法配置"
}
rows {
  id: "-20211502"
  zh_CN: "非法参数"
}
rows {
  id: "-20211503"
  zh_CN: "不存在的主题"
}
rows {
  id: "-20211504"
  zh_CN: "等待后续执行"
}
rows {
  id: "-20211505"
  zh_CN: "无效的任务句柄"
}
rows {
  id: "-20211506"
  zh_CN: "任务主题冲突"
}
rows {
  id: "-20211507"
  zh_CN: "任务发布失败"
}
rows {
  id: "-20211508"
  zh_CN: "任务调度关闭"
}
rows {
  id: "-20211509"
  zh_CN: "重复的任务消费者"
}
rows {
  id: "-20211510"
  zh_CN: "任务续期失败"
}
rows {
  id: "-20260037"
  zh_CN: "ds版本异常"
}
rows {
  id: "-20260038"
  zh_CN: "ds类型异常"
}
rows {
  id: "-20260039"
  zh_CN: "ds存档SplitKey冲突"
}
rows {
  id: "-20260040"
  zh_CN: "ds存档Key不存在"
}
rows {
  id: "-20260041"
  zh_CN: "ds存档SplitKey迁移失败"
}
rows {
  id: "-20260042"
  zh_CN: "batchGet失败"
}
rows {
  id: "-20260043"
  zh_CN: "立刻存储失败"
}
rows {
  id: "-20260044"
  zh_CN: "batchDelete失败"
}
rows {
  id: "-30010002"
  zh_CN: "DS拉起状态错误"
}
rows {
  id: "-30010003"
  zh_CN: "没有对应的DS版本"
}
rows {
  id: "-30010004"
  zh_CN: "找不到合适的dsa"
}
rows {
  id: "-30010005"
  zh_CN: "创建ds失败"
}
rows {
  id: "-30010006"
  zh_CN: "无效的分配配置"
}
rows {
  id: "-30010007"
  zh_CN: "无效DSA配置"
}
rows {
  id: "-30010008"
  zh_CN: "localds模式下无效的alias"
}
rows {
  id: "-30010009"
  zh_CN: "房间启动DS版本不匹配"
}
rows {
  id: "-30011000"
  zh_CN: "未知的dsc通知结果错误码"
}
rows {
  id: "-30011010"
  zh_CN: "分配结果通知：分配DS整体流程执行超时，常见于DSA拉起DS失败，建议先查看DSA错误日志，确认DS是否拉起失败或初始化超时"
}
rows {
  id: "-30011011"
  zh_CN: "分配结果通知：被分配的DS在分配流程中异常终止"
}
rows {
  id: "-30011012"
  zh_CN: "分配结果通知：DS进程被强制退出（由业务主动触发）"
}
rows {
  id: "-30011013"
  zh_CN: "分配结果通知：所有此Fleet的DS进程被强制退出（由业务主动触发）"
}
rows {
  id: "-30011014"
  zh_CN: "分配结果通知：超过一局game session最长时间（由业务配置），DS超时不TerminateGameSession被强制回收"
}
rows {
  id: "-30013000"
  zh_CN: "GameSession结束通知:GameSession异常结束（具体情况请查看DSA的日志）"
}
rows {
  id: "-30013001"
  zh_CN: "GameSession结束通知:DS进程被强制退出（由业务主动触发）"
}
rows {
  id: "-30013002"
  zh_CN: "GameSession结束通知:所有此Fleet的DS进程被强制退出（由业务主动触发）"
}
rows {
  id: "-30013003"
  zh_CN: "GameSession结束通知:超过一局game session最长时间（由业务配置的maxGameSessionSecond），DS超时不TerminateGameSession被强制回收"
}
rows {
  id: "-30013004"
  zh_CN: "GameSession结束通知:DS侧没有 TerminateGameSession 就调用 ProcessEnding，从v2.12起支持（从GAME_SESSION_ENDCODE_ABNORMAL分离出来）"
}
rows {
  id: "-40010003"
  zh_CN: "Ds添加用户失败"
}
rows {
  id: "-40010004"
  zh_CN: "Ds添加用户失败超时"
}
rows {
  id: "-40010005"
  zh_CN: "Ds进入失败"
}
rows {
  id: "-40010006"
  zh_CN: "Ds添加用户RPC失败"
}
rows {
  id: "-20382016"
  zh_CN: "ds创建失败"
}
rows {
  id: "-20382017"
  zh_CN: "访问者已满"
}
rows {
  id: "-10001068"
  zh_CN: "账号转区步骤中断"
}
rows {
  id: "-10010092"
  zh_CN: "限时体验道具配置为空"
}
rows {
  id: "-10041511"
  zh_CN: "膨胀爆红包活动: 未解锁无法膨胀"
}
rows {
  id: "-10041512"
  zh_CN: "膨胀爆红包活动: 已经领取过"
}
rows {
  id: "-10041513"
  zh_CN: "膨胀爆红包活动: 没有膨胀次数"
}
rows {
  id: "-10600617"
  zh_CN: "获取管理端url失败"
}
rows {
  id: "-20110027"
  zh_CN: "单人小队无法退出"
}
rows {
  id: "-20300411"
  zh_CN: "餐厅解析厨师位置出错"
  showType: ECST_FlyTip
}
rows {
  id: "-20300412"
  zh_CN: "餐厅解析服务员位置出错"
  showType: ECST_FlyTip
}
rows {
  id: "-20390070"
  zh_CN: "农场居民家访客已满"
  showType: ECST_FlyTip
}
rows {
  id: "-20500081"
  zh_CN: "农场餐厅点评池错误"
  showType: ECST_FlyTip
}
rows {
  id: "-20500082"
  zh_CN: "农场餐厅点评照片链接解析失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500083"
  zh_CN: "农场餐厅每条点评下的回复超上限"
  showType: ECST_FlyTip
}
rows {
  id: "-20500084"
  zh_CN: "农场餐厅评价图片ID获取失败"
  showType: ECST_FlyTip
}
rows {
  id: "-20500085"
  zh_CN: "农场餐厅点评的redis已经被删除"
  showType: ECST_FlyTip
}
rows {
  id: "-20500086"
  zh_CN: "农场餐厅必须有一个厨师和灶台"
  showType: ECST_FlyTip
}
rows {
  id: "-20500087"
  zh_CN: "农场餐厅必须有一个服务员"
  showType: ECST_FlyTip
}
rows {
  id: "-20500088"
  zh_CN: "农场餐厅访客已满"
  showType: ECST_FlyTip
}
rows {
  id: "-20390147"
  zh_CN: "请重新安排厨师上岗后再发布吧"
  showType: ECST_FlyTip
}
rows {
  id: "-20510001"
  zh_CN: "月卡剩余时间大于360天，无法使用"
  showType: ECST_FlyTip
}
rows {
  id: "-20510002"
  zh_CN: "使用失败，请先解锁月卡功能"
  showType: ECST_FlyTip
}
rows {
  id: "-20510003"
  zh_CN: "月卡功能奖励已领取"
  showType: ECST_FlyTip
}
rows {
  id: "-20510004"
  zh_CN: "月卡功能未开通"
  showType: ECST_FlyTip
}
rows {
  id: "-20510005"
  zh_CN: "对方月卡剩余时间大于360天，不可以赠送"
  showType: ECST_FlyTip
}
rows {
  id: "-20510006"
  zh_CN: "对方玩家客户端版本过低，不可以赠送"
  showType: ECST_FlyTip
}
rows {
  id: "-10001069"
  zh_CN: "账号转区更新Midas失败"
}
rows {
  id: "-10043003"
  zh_CN: "农场天天领扣除道具失败"
  showType: ECST_FlyTip
}
rows {
  id: "-10043004"
  zh_CN: "已经解锁权益不能购买"
  showType: ECST_FlyTip
}
rows {
  id: "-10043005"
  zh_CN: "农场天天领动订单结算中, 请稍后再试"
  showType: ECST_FlyTip
}
rows {
  id: "-10043006"
  zh_CN: "已经解锁权益不能购买"
  showType: ECST_FlyTip
}
rows {
  id: "-10041612"
  zh_CN: "农场组队不允许退出队伍"
  showType: ECST_FlyTip
}
rows {
  id: "-1060344"
  zh_CN: "共创地图多人编辑申请频繁"
  showType: ECST_FlyTip
}
rows {
  id: "-1060345"
  zh_CN: "共创地图多人编辑编辑人数已满"
  showType: ECST_FlyTip
}
rows {
  id: "-1060346"
  zh_CN: "共创地图多人编辑回复无权限"
  showType: ECST_FlyTip
}
rows {
  id: "-1060347"
  zh_CN: "共创地图多人编辑存在占用状态的共创者"
  showType: ECST_FlyTip
}
rows {
  id: "-1060348"
  zh_CN: "共创地图多人编辑非多人编辑模式"
  showType: ECST_FlyTip
}
rows {
  id: "-1060349"
  zh_CN: "共创地图多人编辑数据更新无权限"
  showType: ECST_FlyTip
}
rows {
  id: "-1060350"
  zh_CN: "共创地图多人编辑占用状态不能被其他用户接管"
  showType: ECST_FlyTip
}
rows {
  id: "-1060351"
  zh_CN: "共创地图多人编辑编辑模式不合法"
  showType: ECST_FlyTip
}
rows {
  id: "-1060352"
  zh_CN: "共创地图多人编辑无权限"
  showType: ECST_FlyTip
}
rows {
  id: "-1060353"
  zh_CN: "共创地图多人编辑版本不支持"
  showType: ECST_FlyTip
}
rows {
  id: "-10600618"
  zh_CN: "获取北极星L5地址失败"
}
rows {
  id: "-10600619"
  zh_CN: "出现异常请关注"
}
rows {
  id: "-10600620"
  zh_CN: "通知管理端消除气泡失败"
}
rows {
  id: "-2000200"
  zh_CN: "抽奖接口为未实现"
}
rows {
  id: "-2000201"
  zh_CN: "抽奖检查未通过"
}
rows {
  id: "-2000202"
  zh_CN: "抽奖消耗未通过"
}
rows {
  id: "-2000203"
  zh_CN: "奖励列表为空"
}
rows {
  id: "-2000204"
  zh_CN: "活动未注册抽奖模块"
}
rows {
  id: "-2000205"
  zh_CN: "活动抽奖奖励列表未空"
}
rows {
  id: "-20000145"
  zh_CN: "每日收集印章已达上限"
}
rows {
  id: "-20000146"
  zh_CN: "该玩家的分享印章已经领取过"
}
rows {
  id: "-20000147"
  zh_CN: "不能领取自己分享的美食"
}
rows {
  id: "-20010134"
  zh_CN: "房间状态在倒计时开始"
}
rows {
  id: "-20010135"
  zh_CN: "房间人没满"
}
rows {
  id: "-20010136"
  zh_CN: "自动开局超时"
}
rows {
  id: "-20017079"
  zh_CN: "队伍成员大小包类型不兼容，无法一起游玩该玩法"
}
rows {
  id: "-20290019"
  zh_CN: "作业帮状态错误"
}
rows {
  id: "-2030417"
  zh_CN: "当前作物类型限制施肥"
  showType: ECST_FlyTip
}
rows {
  id: "-2030418"
  zh_CN: "当前样板间建筑解锁等级不足"
  showType: ECST_FlyTip
}
rows {
  id: "-20300419"
  zh_CN: "月卡米大师不存在，参考摇树米大师，一模一样配置一份"
  showType: ECST_FlyTip
}
rows {
  id: "-20390071"
  zh_CN: "农场小屋家具无法批量购买"
  showType: ECST_FlyTip
}
rows {
  id: "-20500089"
  zh_CN: "农场餐厅找不到服务员"
  showType: ECST_FlyTip
}
rows {
  id: "-20500090"
  zh_CN: "农场餐厅找不到厨师"
  showType: ECST_FlyTip
}
rows {
  id: "-20500091"
  zh_CN: "农场餐厅招聘市场指定刷新未开启"
  showType: ECST_FlyTip
}

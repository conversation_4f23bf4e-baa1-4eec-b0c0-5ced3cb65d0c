com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置_System.xlsx sheet:文本配置
rows {
  content: "需{0}关系达到<Orange2>{1}</>级"
  switch: 1
  stringId: "CommodityBuyCondition2"
}
rows {
  content: "获取默契币可兑换"
  switch: 1
  stringId: "UI_Season_EarnIntimateCoinsPass_Txt"
}
rows {
  content: "提升默契等级可兑换"
  switch: 1
  stringId: "UI_Season_EarnIntimateLevelPass_Txt"
}
rows {
  content: "当前匹配需要下载{0}"
  switch: 1
  stringId: "UI_MatchPakDetail_DownloadTip"
}
rows {
  content: "剩余{0}"
  switch: 1
  stringId: "UI_MatchPakDetail_LeftDownloadTime"
}

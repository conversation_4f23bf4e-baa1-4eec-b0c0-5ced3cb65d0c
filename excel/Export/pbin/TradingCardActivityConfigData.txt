com.tencent.wea.xlsRes.table_TradingCardActivityConfig
excel/xls/K_卡牌.xlsx sheet:卡牌活动
rows {
  id: 1
  beginTime {
    seconds: 1740672000
  }
  endTime {
    seconds: 1741276799
  }
  type: TCAT_Trade
  paramList1: 1020808
  paramList1: 1020908
  paramList1: 1021009
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 102
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 1
  tagName: "限时置换"
}
rows {
  id: 2
  beginTime {
    seconds: 1741276800
  }
  endTime {
    seconds: 1741881599
  }
  type: TCAT_Trade
  paramList1: 1021005
  paramList1: 1021006
  paramList1: 1021108
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 102
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 2
  tagName: "限时置换"
}
rows {
  id: 3
  beginTime {
    seconds: 1741881600
  }
  endTime {
    seconds: 1742486399
  }
  type: TCAT_Trade
  paramList1: 1020709
  paramList1: 1021109
  paramList1: 1021207
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 102
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 3
  tagName: "限时置换"
}
rows {
  id: 4
  beginTime {
    seconds: 1742486400
  }
  endTime {
    seconds: 1743091199
  }
  type: TCAT_Trade
  paramList1: 1021103
  paramList1: 1021104
  paramList1: 1021208
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 102
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 4
  tagName: "限时置换"
}
rows {
  id: 5
  beginTime {
    seconds: 1743091200
  }
  endTime {
    seconds: 1743695999
  }
  type: TCAT_Trade
  paramList1: 1021202
  paramList1: 1021203
  paramList1: 1021209
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 102
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 5
  tagName: "限时置换"
}
rows {
  id: 6
  beginTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1744300799
  }
  type: TCAT_Trade
  paramList1: 1020808
  paramList1: 1020908
  paramList1: 1021009
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 102
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 1
  tagName: "限时置换"
}
rows {
  id: 7
  beginTime {
    seconds: 1744300800
  }
  endTime {
    seconds: 1744905599
  }
  type: TCAT_Trade
  paramList1: 1021005
  paramList1: 1021006
  paramList1: 1021108
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 102
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 2
  tagName: "限时置换"
}
rows {
  id: 8
  beginTime {
    seconds: 1744905600
  }
  endTime {
    seconds: 1745510399
  }
  type: TCAT_Trade
  paramList1: 1020709
  paramList1: 1021109
  paramList1: 1021207
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 102
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 3
  tagName: "限时置换"
}
rows {
  id: 9
  beginTime {
    seconds: 1745510400
  }
  endTime {
    seconds: 1746115199
  }
  type: TCAT_Trade
  paramList1: 1021103
  paramList1: 1021104
  paramList1: 1021208
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 102
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 4
  tagName: "限时置换"
}
rows {
  id: 10
  beginTime {
    seconds: 1745510400
  }
  endTime {
    seconds: 1746115199
  }
  type: TCAT_Trade
  paramList1: 1021202
  paramList1: 1021203
  paramList1: 1021209
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 102
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 5
  tagName: "限时置换"
}
rows {
  id: 101
  beginTime {
    seconds: 1750953600
  }
  endTime {
    seconds: 1751558399
  }
  type: TCAT_Trade
  paramList1: 1031206
  paramList1: 1031205
  paramList1: 1031009
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 1
  tagName: "限时置换"
}
rows {
  id: 102
  beginTime {
    seconds: 1750953600
  }
  endTime {
    seconds: 1751558399
  }
  type: TCAT_Trade
  paramList1: 1031107
  paramList1: 1031106
  paramList1: 1031108
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 2
  tagName: "限时置换"
}
rows {
  id: 103
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1752163199
  }
  type: TCAT_Trade
  paramList1: 1031008
  paramList1: 1031007
  paramList1: 1031109
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 3
  tagName: "限时置换"
}
rows {
  id: 104
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1752163199
  }
  type: TCAT_Trade
  paramList1: 1030909
  paramList1: 1030908
  paramList1: 1031207
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 4
  tagName: "限时置换"
}
rows {
  id: 105
  beginTime {
    seconds: 1752163200
  }
  endTime {
    seconds: 1752767999
  }
  type: TCAT_Trade
  paramList1: 1030809
  paramList1: 1031206
  paramList1: 1031208
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 5
  tagName: "限时置换"
}
rows {
  id: 106
  beginTime {
    seconds: 1752163200
  }
  endTime {
    seconds: 1752767999
  }
  type: TCAT_Trade
  paramList1: 1031205
  paramList1: 1031107
  paramList1: 1031209
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 1
  tagName: "限时置换"
}
rows {
  id: 107
  beginTime {
    seconds: 1752768000
  }
  endTime {
    seconds: 1753372799
  }
  type: TCAT_Trade
  paramList1: 1031106
  paramList1: 1031008
  paramList1: 1031009
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 2
  tagName: "限时置换"
}
rows {
  id: 108
  beginTime {
    seconds: 1752768000
  }
  endTime {
    seconds: 1753372799
  }
  type: TCAT_Trade
  paramList1: 1031007
  paramList1: 1030909
  paramList1: 1031108
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 3
  tagName: "限时置换"
}
rows {
  id: 109
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1753977599
  }
  type: TCAT_Trade
  paramList1: 1030908
  paramList1: 1031109
  paramList1: 1031207
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 4
  tagName: "限时置换"
}
rows {
  id: 110
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1753977599
  }
  type: TCAT_Trade
  paramList1: 1030809
  paramList1: 1031208
  paramList1: 1031209
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 5
  tagName: "限时置换"
}
rows {
  id: 111
  beginTime {
    seconds: 1753977600
  }
  endTime {
    seconds: 1754582399
  }
  type: TCAT_Trade
  paramList1: 1031206
  paramList1: 1031205
  paramList1: 1031009
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 1
  tagName: "限时置换"
}
rows {
  id: 112
  beginTime {
    seconds: 1753977600
  }
  endTime {
    seconds: 1754582399
  }
  type: TCAT_Trade
  paramList1: 1031107
  paramList1: 1031106
  paramList1: 1031108
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 2
  tagName: "限时置换"
}
rows {
  id: 113
  beginTime {
    seconds: 1754582400
  }
  endTime {
    seconds: 1755187199
  }
  type: TCAT_Trade
  paramList1: 1031008
  paramList1: 1031007
  paramList1: 1031109
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 3
  tagName: "限时置换"
}
rows {
  id: 114
  beginTime {
    seconds: 1754582400
  }
  endTime {
    seconds: 1755187199
  }
  type: TCAT_Trade
  paramList1: 1030909
  paramList1: 1030908
  paramList1: 1031207
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 4
  tagName: "限时置换"
}
rows {
  id: 115
  beginTime {
    seconds: 1755187200
  }
  endTime {
    seconds: 1755791999
  }
  type: TCAT_Trade
  paramList1: 1030809
  paramList1: 1031206
  paramList1: 1031208
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 5
  tagName: "限时置换"
}
rows {
  id: 116
  beginTime {
    seconds: 1755187200
  }
  endTime {
    seconds: 1755791999
  }
  type: TCAT_Trade
  paramList1: 1031205
  paramList1: 1031107
  paramList1: 1031209
  paramList2: 1
  paramList2: 2
  paramList2: 3
  enable: true
  collectionId: 103
  showInCardActivityMain: true
  subTitle: "以下卡牌在活动期间可以互相赠送！"
  uiName: "UI_CardActivity_LimitExchange"
  sort: 6
  tagName: "限时置换"
}
rows {
  id: 10001
  beginTime {
    seconds: 1743609600
  }
  endTime {
    seconds: 1746115199
  }
  type: TCAT_Trade_BOUND
  paramList1: 290011
  paramList1: 290012
  paramList1: 290013
  paramList1: 290014
  paramList1: 290015
  enable: true
  collectionId: 102
  showInCardActivityMain: false
}

com.tencent.wea.xlsRes.table_GameOptimizeSettingData
excel/xls/X_性能优化表_基础表.xlsx sheet:基础表
rows {
  id: 90001
  commandKey: "r.SkeletalMeshLODBias"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90002
  commandKey: "r.ViewDistanceScale"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90003
  commandKey: "r.NeverOcclusionTestDistance"
  platform: "All"
  commandValue1: "2000"
  commandValue2: "2000"
  commandValue3: "2000"
  commandValue4: "2000"
  commandValue5: "2000"
  commandValue6: "2000"
  commandValue7: "2000"
  commandValue8: "2000"
}
rows {
  id: 10004
  commandKey: "r.PostProcessAAQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 20004
  commandKey: "r.PostProcessAAQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 30004
  commandKey: "r.PostProcessAAQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "0"
}
rows {
  id: 40004
  commandKey: "r.PostProcessAAQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 50004
  commandKey: "r.PostProcessAAQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 60004
  commandKey: "r.PostProcessAAQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 10005
  commandKey: "r.LightFunctionQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20005
  commandKey: "r.LightFunctionQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30005
  commandKey: "r.LightFunctionQuality"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40005
  commandKey: "r.LightFunctionQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50005
  commandKey: "r.LightFunctionQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60005
  commandKey: "r.LightFunctionQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10006
  commandKey: "r.ShadowQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "3"
  commandValue4: "3"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 20006
  commandKey: "r.ShadowQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "3"
  commandValue4: "3"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 30006
  commandKey: "r.ShadowQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "3"
  commandValue8: "4"
}
rows {
  id: 40006
  commandKey: "r.ShadowQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "3"
  commandValue4: "3"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 50006
  commandKey: "r.ShadowQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "3"
  commandValue4: "3"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 60006
  commandKey: "r.ShadowQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "3"
  commandValue4: "3"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 10007
  commandKey: "r.Shadow.CSM.MaxCascades"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 20007
  commandKey: "r.Shadow.CSM.MaxCascades"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 30007
  commandKey: "r.Shadow.CSM.MaxCascades"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 40007
  commandKey: "r.Shadow.CSM.MaxCascades"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 50007
  commandKey: "r.Shadow.CSM.MaxCascades"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 60007
  commandKey: "r.Shadow.CSM.MaxCascades"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 10008
  commandKey: "r.Shadow.MaxResolution"
  platform: "Android"
  commandValue1: "512"
  commandValue2: "512"
  commandValue3: "1024"
  commandValue4: "1024"
  commandValue5: "512"
  commandValue6: "512"
  commandValue7: "1024"
  commandValue8: "1024"
}
rows {
  id: 20008
  commandKey: "r.Shadow.MaxResolution"
  platform: "IOS"
  commandValue1: "512"
  commandValue2: "512"
  commandValue3: "1024"
  commandValue4: "1024"
  commandValue5: "512"
  commandValue6: "512"
  commandValue7: "1024"
  commandValue8: "1024"
}
rows {
  id: 30008
  commandKey: "r.Shadow.MaxResolution"
  platform: "Windows"
  commandValue1: "1024"
  commandValue2: "2048"
  commandValue3: "4096"
  commandValue4: "4096"
  commandValue5: "1024"
  commandValue6: "2048"
  commandValue7: "4096"
  commandValue8: "4096"
}
rows {
  id: 40008
  commandKey: "r.Shadow.MaxResolution"
  platform: "Harmony"
  commandValue1: "512"
  commandValue2: "512"
  commandValue3: "1024"
  commandValue4: "1024"
  commandValue5: "512"
  commandValue6: "512"
  commandValue7: "1024"
  commandValue8: "1024"
}
rows {
  id: 50008
  commandKey: "r.Shadow.MaxResolution"
  platform: "CloudGame"
  commandValue1: "512"
  commandValue2: "512"
  commandValue3: "1024"
  commandValue4: "1024"
  commandValue5: "512"
  commandValue6: "512"
  commandValue7: "1024"
  commandValue8: "1024"
}
rows {
  id: 60008
  commandKey: "r.Shadow.MaxResolution"
  platform: "MiniBox"
  commandValue1: "512"
  commandValue2: "512"
  commandValue3: "1024"
  commandValue4: "1024"
  commandValue5: "512"
  commandValue6: "512"
  commandValue7: "1024"
  commandValue8: "1024"
}
rows {
  id: 10009
  commandKey: "r.Shadow.MaxCSMResolution"
  platform: "Android"
  commandValue1: "512"
  commandValue2: "1024"
  commandValue3: "2048"
  commandValue4: "2048"
  commandValue5: "512"
  commandValue6: "1024"
  commandValue7: "2048"
  commandValue8: "2048"
}
rows {
  id: 20009
  commandKey: "r.Shadow.MaxCSMResolution"
  platform: "IOS"
  commandValue1: "512"
  commandValue2: "1024"
  commandValue3: "2048"
  commandValue4: "2048"
  commandValue5: "512"
  commandValue6: "1024"
  commandValue7: "2048"
  commandValue8: "2048"
}
rows {
  id: 30009
  commandKey: "r.Shadow.MaxCSMResolution"
  platform: "Windows"
  commandValue1: "1024"
  commandValue2: "2048"
  commandValue3: "4096"
  commandValue4: "4096"
  commandValue5: "1024"
  commandValue6: "2048"
  commandValue7: "4096"
  commandValue8: "4096"
}
rows {
  id: 40009
  commandKey: "r.Shadow.MaxCSMResolution"
  platform: "Harmony"
  commandValue1: "512"
  commandValue2: "1024"
  commandValue3: "2048"
  commandValue4: "2048"
  commandValue5: "512"
  commandValue6: "1024"
  commandValue7: "2048"
  commandValue8: "2048"
}
rows {
  id: 50009
  commandKey: "r.Shadow.MaxCSMResolution"
  platform: "CloudGame"
  commandValue1: "512"
  commandValue2: "1024"
  commandValue3: "2048"
  commandValue4: "2048"
  commandValue5: "512"
  commandValue6: "1024"
  commandValue7: "2048"
  commandValue8: "2048"
}
rows {
  id: 60009
  commandKey: "r.Shadow.MaxCSMResolution"
  platform: "MiniBox"
  commandValue1: "512"
  commandValue2: "1024"
  commandValue3: "2048"
  commandValue4: "2048"
  commandValue5: "512"
  commandValue6: "1024"
  commandValue7: "2048"
  commandValue8: "2048"
}
rows {
  id: 10010
  commandKey: "r.Shadow.RadiusThreshold"
  platform: "Android"
  commandValue1: "0.06"
  commandValue2: "0.06"
  commandValue3: "0.03"
  commandValue4: "0.03"
  commandValue5: "0.06"
  commandValue6: "0.06"
  commandValue7: "0.03"
  commandValue8: "0.03"
}
rows {
  id: 20010
  commandKey: "r.Shadow.RadiusThreshold"
  platform: "IOS"
  commandValue1: "0.06"
  commandValue2: "0.06"
  commandValue3: "0.03"
  commandValue4: "0.03"
  commandValue5: "0.06"
  commandValue6: "0.06"
  commandValue7: "0.03"
  commandValue8: "0.03"
}
rows {
  id: 30010
  commandKey: "r.Shadow.RadiusThreshold"
  platform: "Windows"
  commandValue1: "0.03"
  commandValue2: "0.03"
  commandValue3: "0.03"
  commandValue4: "0.03"
  commandValue5: "0.03"
  commandValue6: "0.03"
  commandValue7: "0.03"
  commandValue8: "0.03"
}
rows {
  id: 40010
  commandKey: "r.Shadow.RadiusThreshold"
  platform: "Harmony"
  commandValue1: "0.06"
  commandValue2: "0.06"
  commandValue3: "0.03"
  commandValue4: "0.03"
  commandValue5: "0.06"
  commandValue6: "0.06"
  commandValue7: "0.03"
  commandValue8: "0.03"
}
rows {
  id: 50010
  commandKey: "r.Shadow.RadiusThreshold"
  platform: "CloudGame"
  commandValue1: "0.06"
  commandValue2: "0.06"
  commandValue3: "0.03"
  commandValue4: "0.03"
  commandValue5: "0.06"
  commandValue6: "0.06"
  commandValue7: "0.03"
  commandValue8: "0.03"
}
rows {
  id: 60010
  commandKey: "r.Shadow.RadiusThreshold"
  platform: "MiniBox"
  commandValue1: "0.06"
  commandValue2: "0.06"
  commandValue3: "0.03"
  commandValue4: "0.03"
  commandValue5: "0.06"
  commandValue6: "0.06"
  commandValue7: "0.03"
  commandValue8: "0.03"
}
rows {
  id: 10011
  commandKey: "r.Shadow.DistanceScale"
  platform: "Android"
  commandValue1: "0.5"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20011
  commandKey: "r.Shadow.DistanceScale"
  platform: "IOS"
  commandValue1: "0.5"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30011
  commandKey: "r.Shadow.DistanceScale"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40011
  commandKey: "r.Shadow.DistanceScale"
  platform: "Harmony"
  commandValue1: "0.5"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50011
  commandKey: "r.Shadow.DistanceScale"
  platform: "CloudGame"
  commandValue1: "0.5"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60011
  commandKey: "r.Shadow.DistanceScale"
  platform: "MiniBox"
  commandValue1: "0.5"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10012
  commandKey: "r.Shadow.CSM.TransitionScale"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20012
  commandKey: "r.Shadow.CSM.TransitionScale"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30012
  commandKey: "r.Shadow.CSM.TransitionScale"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40012
  commandKey: "r.Shadow.CSM.TransitionScale"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50012
  commandKey: "r.Shadow.CSM.TransitionScale"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60012
  commandKey: "r.Shadow.CSM.TransitionScale"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10013
  commandKey: "r.Shadow.PreShadowResolutionFactor"
  platform: "Android"
  commandValue1: "0.5"
  commandValue2: "0.5"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.5"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20013
  commandKey: "r.Shadow.PreShadowResolutionFactor"
  platform: "IOS"
  commandValue1: "0.5"
  commandValue2: "0.5"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.5"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30013
  commandKey: "r.Shadow.PreShadowResolutionFactor"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40013
  commandKey: "r.Shadow.PreShadowResolutionFactor"
  platform: "Harmony"
  commandValue1: "0.5"
  commandValue2: "0.5"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.5"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50013
  commandKey: "r.Shadow.PreShadowResolutionFactor"
  platform: "CloudGame"
  commandValue1: "0.5"
  commandValue2: "0.5"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.5"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60013
  commandKey: "r.Shadow.PreShadowResolutionFactor"
  platform: "MiniBox"
  commandValue1: "0.5"
  commandValue2: "0.5"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.5"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10014
  commandKey: "r.DistanceFieldShadowing"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20014
  commandKey: "r.DistanceFieldShadowing"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30014
  commandKey: "r.DistanceFieldShadowing"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40014
  commandKey: "r.DistanceFieldShadowing"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50014
  commandKey: "r.DistanceFieldShadowing"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60014
  commandKey: "r.DistanceFieldShadowing"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10015
  commandKey: "r.DistanceFieldAO"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20015
  commandKey: "r.DistanceFieldAO"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30015
  commandKey: "r.DistanceFieldAO"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40015
  commandKey: "r.DistanceFieldAO"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50015
  commandKey: "r.DistanceFieldAO"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60015
  commandKey: "r.DistanceFieldAO"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90016
  commandKey: "r.AOQuality"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 10017
  commandKey: "r.VolumetricFog"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20017
  commandKey: "r.VolumetricFog"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30017
  commandKey: "r.VolumetricFog"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40017
  commandKey: "r.VolumetricFog"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50017
  commandKey: "r.VolumetricFog"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60017
  commandKey: "r.VolumetricFog"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10018
  commandKey: "r.VolumetricFog.GridPixelSize"
  platform: "Android"
  commandValue1: "64"
  commandValue2: "32"
  commandValue3: "16"
  commandValue4: "8"
  commandValue5: "64"
  commandValue6: "32"
  commandValue7: "16"
  commandValue8: "8"
}
rows {
  id: 20018
  commandKey: "r.VolumetricFog.GridPixelSize"
  platform: "IOS"
  commandValue1: "64"
  commandValue2: "32"
  commandValue3: "16"
  commandValue4: "8"
  commandValue5: "64"
  commandValue6: "32"
  commandValue7: "16"
  commandValue8: "8"
}
rows {
  id: 30018
  commandKey: "r.VolumetricFog.GridPixelSize"
  platform: "Windows"
  commandValue1: "32"
  commandValue2: "16"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "32"
  commandValue6: "16"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 40018
  commandKey: "r.VolumetricFog.GridPixelSize"
  platform: "Harmony"
  commandValue1: "64"
  commandValue2: "32"
  commandValue3: "16"
  commandValue4: "8"
  commandValue5: "64"
  commandValue6: "32"
  commandValue7: "16"
  commandValue8: "8"
}
rows {
  id: 50018
  commandKey: "r.VolumetricFog.GridPixelSize"
  platform: "CloudGame"
  commandValue1: "64"
  commandValue2: "32"
  commandValue3: "16"
  commandValue4: "8"
  commandValue5: "64"
  commandValue6: "32"
  commandValue7: "16"
  commandValue8: "8"
}
rows {
  id: 60018
  commandKey: "r.VolumetricFog.GridPixelSize"
  platform: "MiniBox"
  commandValue1: "64"
  commandValue2: "32"
  commandValue3: "16"
  commandValue4: "8"
  commandValue5: "64"
  commandValue6: "32"
  commandValue7: "16"
  commandValue8: "8"
}
rows {
  id: 10019
  commandKey: "r.VolumetricFog.GridSizeZ"
  platform: "Android"
  commandValue1: "16"
  commandValue2: "32"
  commandValue3: "64"
  commandValue4: "128"
  commandValue5: "16"
  commandValue6: "32"
  commandValue7: "64"
  commandValue8: "128"
}
rows {
  id: 20019
  commandKey: "r.VolumetricFog.GridSizeZ"
  platform: "IOS"
  commandValue1: "16"
  commandValue2: "32"
  commandValue3: "64"
  commandValue4: "128"
  commandValue5: "16"
  commandValue6: "32"
  commandValue7: "64"
  commandValue8: "128"
}
rows {
  id: 30019
  commandKey: "r.VolumetricFog.GridSizeZ"
  platform: "Windows"
  commandValue1: "128"
  commandValue2: "128"
  commandValue3: "128"
  commandValue4: "128"
  commandValue5: "128"
  commandValue6: "128"
  commandValue7: "128"
  commandValue8: "128"
}
rows {
  id: 40019
  commandKey: "r.VolumetricFog.GridSizeZ"
  platform: "Harmony"
  commandValue1: "16"
  commandValue2: "32"
  commandValue3: "64"
  commandValue4: "128"
  commandValue5: "16"
  commandValue6: "32"
  commandValue7: "64"
  commandValue8: "128"
}
rows {
  id: 50019
  commandKey: "r.VolumetricFog.GridSizeZ"
  platform: "CloudGame"
  commandValue1: "16"
  commandValue2: "32"
  commandValue3: "64"
  commandValue4: "128"
  commandValue5: "16"
  commandValue6: "32"
  commandValue7: "64"
  commandValue8: "128"
}
rows {
  id: 60019
  commandKey: "r.VolumetricFog.GridSizeZ"
  platform: "MiniBox"
  commandValue1: "16"
  commandValue2: "32"
  commandValue3: "64"
  commandValue4: "128"
  commandValue5: "16"
  commandValue6: "32"
  commandValue7: "64"
  commandValue8: "128"
}
rows {
  id: 90020
  commandKey: "r.VolumetricFog.HistoryMissSupersampleCount"
  platform: "All"
  commandValue1: "4"
  commandValue2: "4"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "4"
  commandValue6: "4"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 10021
  commandKey: "r.LightMaxDrawDistanceScale"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0.5"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0.5"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20021
  commandKey: "r.LightMaxDrawDistanceScale"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0.5"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0.5"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30021
  commandKey: "r.LightMaxDrawDistanceScale"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40021
  commandKey: "r.LightMaxDrawDistanceScale"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0.5"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0.5"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50021
  commandKey: "r.LightMaxDrawDistanceScale"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0.5"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0.5"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60021
  commandKey: "r.LightMaxDrawDistanceScale"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0.5"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0.5"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10022
  commandKey: "r.CapsuleShadows"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20022
  commandKey: "r.CapsuleShadows"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30022
  commandKey: "r.CapsuleShadows"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40022
  commandKey: "r.CapsuleShadows"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50022
  commandKey: "r.CapsuleShadows"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60022
  commandKey: "r.CapsuleShadows"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10023
  commandKey: "r.Shadow.CSM.MaxMobileCascades"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 20023
  commandKey: "r.Shadow.CSM.MaxMobileCascades"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 30023
  commandKey: "r.Shadow.CSM.MaxMobileCascades"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "1"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 40023
  commandKey: "r.Shadow.CSM.MaxMobileCascades"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 50023
  commandKey: "r.Shadow.CSM.MaxMobileCascades"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 60023
  commandKey: "r.Shadow.CSM.MaxMobileCascades"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 10024
  commandKey: "r.MotionBlurQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "4"
}
rows {
  id: 20024
  commandKey: "r.MotionBlurQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "4"
}
rows {
  id: 30024
  commandKey: "r.MotionBlurQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "4"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "4"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 40024
  commandKey: "r.MotionBlurQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "4"
}
rows {
  id: 50024
  commandKey: "r.MotionBlurQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "4"
}
rows {
  id: 60024
  commandKey: "r.MotionBlurQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "4"
}
rows {
  id: 10025
  commandKey: "r.AmbientOcclusionMipLevelFactor"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0.6"
  commandValue4: "0.4"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0.6"
  commandValue8: "0.4"
}
rows {
  id: 20025
  commandKey: "r.AmbientOcclusionMipLevelFactor"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0.6"
  commandValue4: "0.4"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0.6"
  commandValue8: "0.4"
}
rows {
  id: 30025
  commandKey: "r.AmbientOcclusionMipLevelFactor"
  platform: "Windows"
  commandValue1: "0.6"
  commandValue2: "0.4"
  commandValue3: "0.4"
  commandValue4: "0.4"
  commandValue5: "0.6"
  commandValue6: "0.4"
  commandValue7: "0.4"
  commandValue8: "0.4"
}
rows {
  id: 40025
  commandKey: "r.AmbientOcclusionMipLevelFactor"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0.6"
  commandValue4: "0.4"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0.6"
  commandValue8: "0.4"
}
rows {
  id: 50025
  commandKey: "r.AmbientOcclusionMipLevelFactor"
  platform: "CloudGame"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0.6"
  commandValue4: "0.4"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0.6"
  commandValue8: "0.4"
}
rows {
  id: 60025
  commandKey: "r.AmbientOcclusionMipLevelFactor"
  platform: "MiniBox"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0.6"
  commandValue4: "0.4"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0.6"
  commandValue8: "0.4"
}
rows {
  id: 10026
  commandKey: "r.AmbientOcclusionMaxQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "60"
  commandValue3: "100"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "60"
  commandValue7: "100"
  commandValue8: "100"
}
rows {
  id: 20026
  commandKey: "r.AmbientOcclusionMaxQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "60"
  commandValue3: "100"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "60"
  commandValue7: "100"
  commandValue8: "100"
}
rows {
  id: 30026
  commandKey: "r.AmbientOcclusionMaxQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "100"
  commandValue3: "100"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "100"
  commandValue7: "100"
  commandValue8: "100"
}
rows {
  id: 40026
  commandKey: "r.AmbientOcclusionMaxQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "60"
  commandValue3: "100"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "60"
  commandValue7: "100"
  commandValue8: "100"
}
rows {
  id: 50026
  commandKey: "r.AmbientOcclusionMaxQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "60"
  commandValue3: "100"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "60"
  commandValue7: "100"
  commandValue8: "100"
}
rows {
  id: 60026
  commandKey: "r.AmbientOcclusionMaxQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "60"
  commandValue3: "100"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "60"
  commandValue7: "100"
  commandValue8: "100"
}
rows {
  id: 10027
  commandKey: "r.AmbientOcclusionLevels"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "-1"
  commandValue3: "-1"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "-1"
  commandValue7: "-1"
  commandValue8: "-1"
}
rows {
  id: 20027
  commandKey: "r.AmbientOcclusionLevels"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "-1"
  commandValue3: "-1"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "-1"
  commandValue7: "-1"
  commandValue8: "-1"
}
rows {
  id: 30027
  commandKey: "r.AmbientOcclusionLevels"
  platform: "Windows"
  commandValue1: "-1"
  commandValue2: "-1"
  commandValue3: "-1"
  commandValue4: "-1"
  commandValue5: "-1"
  commandValue6: "-1"
  commandValue7: "-1"
  commandValue8: "-1"
}
rows {
  id: 40027
  commandKey: "r.AmbientOcclusionLevels"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "-1"
  commandValue3: "-1"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "-1"
  commandValue7: "-1"
  commandValue8: "-1"
}
rows {
  id: 50027
  commandKey: "r.AmbientOcclusionLevels"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "-1"
  commandValue3: "-1"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "-1"
  commandValue7: "-1"
  commandValue8: "-1"
}
rows {
  id: 60027
  commandKey: "r.AmbientOcclusionLevels"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "-1"
  commandValue3: "-1"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "-1"
  commandValue7: "-1"
  commandValue8: "-1"
}
rows {
  id: 10028
  commandKey: "r.AmbientOcclusionRadiusScale"
  platform: "Android"
  commandValue1: "1.2"
  commandValue2: "1.5"
  commandValue3: "1.5"
  commandValue4: "1"
  commandValue5: "1.2"
  commandValue6: "1.5"
  commandValue7: "1.5"
  commandValue8: "1"
}
rows {
  id: 20028
  commandKey: "r.AmbientOcclusionRadiusScale"
  platform: "IOS"
  commandValue1: "1.2"
  commandValue2: "1.5"
  commandValue3: "1.5"
  commandValue4: "1"
  commandValue5: "1.2"
  commandValue6: "1.5"
  commandValue7: "1.5"
  commandValue8: "1"
}
rows {
  id: 30028
  commandKey: "r.AmbientOcclusionRadiusScale"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40028
  commandKey: "r.AmbientOcclusionRadiusScale"
  platform: "Harmony"
  commandValue1: "1.2"
  commandValue2: "1.5"
  commandValue3: "1.5"
  commandValue4: "1"
  commandValue5: "1.2"
  commandValue6: "1.5"
  commandValue7: "1.5"
  commandValue8: "1"
}
rows {
  id: 50028
  commandKey: "r.AmbientOcclusionRadiusScale"
  platform: "CloudGame"
  commandValue1: "1.2"
  commandValue2: "1.5"
  commandValue3: "1.5"
  commandValue4: "1"
  commandValue5: "1.2"
  commandValue6: "1.5"
  commandValue7: "1.5"
  commandValue8: "1"
}
rows {
  id: 60028
  commandKey: "r.AmbientOcclusionRadiusScale"
  platform: "MiniBox"
  commandValue1: "1.2"
  commandValue2: "1.5"
  commandValue3: "1.5"
  commandValue4: "1"
  commandValue5: "1.2"
  commandValue6: "1.5"
  commandValue7: "1.5"
  commandValue8: "1"
}
rows {
  id: 10029
  commandKey: "r.DepthOfFieldQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20029
  commandKey: "r.DepthOfFieldQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30029
  commandKey: "r.DepthOfFieldQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40029
  commandKey: "r.DepthOfFieldQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50029
  commandKey: "r.DepthOfFieldQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60029
  commandKey: "r.DepthOfFieldQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10030
  commandKey: "r.RenderTargetPoolMin"
  platform: "Android"
  commandValue1: "300"
  commandValue2: "350"
  commandValue3: "400"
  commandValue4: "400"
  commandValue5: "300"
  commandValue6: "350"
  commandValue7: "400"
  commandValue8: "400"
}
rows {
  id: 20030
  commandKey: "r.RenderTargetPoolMin"
  platform: "IOS"
  commandValue1: "300"
  commandValue2: "350"
  commandValue3: "400"
  commandValue4: "400"
  commandValue5: "300"
  commandValue6: "350"
  commandValue7: "400"
  commandValue8: "400"
}
rows {
  id: 30030
  commandKey: "r.RenderTargetPoolMin"
  platform: "Windows"
  commandValue1: "350"
  commandValue2: "400"
  commandValue3: "400"
  commandValue4: "500"
  commandValue5: "350"
  commandValue6: "400"
  commandValue7: "400"
  commandValue8: "500"
}
rows {
  id: 40030
  commandKey: "r.RenderTargetPoolMin"
  platform: "Harmony"
  commandValue1: "300"
  commandValue2: "350"
  commandValue3: "400"
  commandValue4: "400"
  commandValue5: "300"
  commandValue6: "350"
  commandValue7: "400"
  commandValue8: "400"
}
rows {
  id: 50030
  commandKey: "r.RenderTargetPoolMin"
  platform: "CloudGame"
  commandValue1: "300"
  commandValue2: "350"
  commandValue3: "400"
  commandValue4: "400"
  commandValue5: "300"
  commandValue6: "350"
  commandValue7: "400"
  commandValue8: "400"
}
rows {
  id: 60030
  commandKey: "r.RenderTargetPoolMin"
  platform: "MiniBox"
  commandValue1: "300"
  commandValue2: "350"
  commandValue3: "400"
  commandValue4: "400"
  commandValue5: "300"
  commandValue6: "350"
  commandValue7: "400"
  commandValue8: "400"
}
rows {
  id: 10031
  commandKey: "r.LensFlareQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 20031
  commandKey: "r.LensFlareQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 30031
  commandKey: "r.LensFlareQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 40031
  commandKey: "r.LensFlareQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 50031
  commandKey: "r.LensFlareQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 60031
  commandKey: "r.LensFlareQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 90032
  commandKey: "r.SceneColorFringeQuality"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90033
  commandKey: "r.EyeAdaptationQuality"
  platform: "All"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 10034
  commandKey: "r.BloomQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 20034
  commandKey: "r.BloomQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "4"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "4"
  commandValue7: "4"
  commandValue8: "3"
}
rows {
  id: 30034
  commandKey: "r.BloomQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "4"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "4"
  commandValue7: "4"
  commandValue8: "3"
}
rows {
  id: 40034
  commandKey: "r.BloomQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 50034
  commandKey: "r.BloomQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 60034
  commandKey: "r.BloomQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "0"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 10035
  commandKey: "r.FastBlurThreshold"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "3"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "3"
  commandValue8: "100"
}
rows {
  id: 20035
  commandKey: "r.FastBlurThreshold"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "3"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "3"
  commandValue8: "100"
}
rows {
  id: 30035
  commandKey: "r.FastBlurThreshold"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "100"
  commandValue3: "100"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "100"
  commandValue7: "100"
  commandValue8: "100"
}
rows {
  id: 40035
  commandKey: "r.FastBlurThreshold"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "3"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "3"
  commandValue8: "100"
}
rows {
  id: 50035
  commandKey: "r.FastBlurThreshold"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "3"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "3"
  commandValue8: "100"
}
rows {
  id: 60035
  commandKey: "r.FastBlurThreshold"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "3"
  commandValue4: "100"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "3"
  commandValue8: "100"
}
rows {
  id: 10036
  commandKey: "r.Upscale.Quality"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "3"
  commandValue5: "1"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 20036
  commandKey: "r.Upscale.Quality"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "3"
  commandValue5: "1"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 30036
  commandKey: "r.Upscale.Quality"
  platform: "Windows"
  commandValue1: "2"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "3"
  commandValue5: "2"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "2"
}
rows {
  id: 40036
  commandKey: "r.Upscale.Quality"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "3"
  commandValue5: "1"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 50036
  commandKey: "r.Upscale.Quality"
  platform: "CloudGame"
  commandValue1: "1"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "3"
  commandValue5: "1"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 60036
  commandKey: "r.Upscale.Quality"
  platform: "MiniBox"
  commandValue1: "1"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "3"
  commandValue5: "1"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 10037
  commandKey: "r.Tonemapper.GrainQuantization"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20037
  commandKey: "r.Tonemapper.GrainQuantization"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30037
  commandKey: "r.Tonemapper.GrainQuantization"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40037
  commandKey: "r.Tonemapper.GrainQuantization"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50037
  commandKey: "r.Tonemapper.GrainQuantization"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60037
  commandKey: "r.Tonemapper.GrainQuantization"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90038
  commandKey: "r.LightShaftQuality"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 10039
  commandKey: "r.Filter.SizeScale"
  platform: "Android"
  commandValue1: "0.6"
  commandValue2: "0.7"
  commandValue3: "0.8"
  commandValue4: "1"
  commandValue5: "0.6"
  commandValue6: "0.7"
  commandValue7: "0.8"
  commandValue8: "1"
}
rows {
  id: 20039
  commandKey: "r.Filter.SizeScale"
  platform: "IOS"
  commandValue1: "0.6"
  commandValue2: "0.7"
  commandValue3: "0.8"
  commandValue4: "1"
  commandValue5: "0.6"
  commandValue6: "0.7"
  commandValue7: "0.8"
  commandValue8: "1"
}
rows {
  id: 30039
  commandKey: "r.Filter.SizeScale"
  platform: "Windows"
  commandValue1: "0.8"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.8"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40039
  commandKey: "r.Filter.SizeScale"
  platform: "Harmony"
  commandValue1: "0.6"
  commandValue2: "0.7"
  commandValue3: "0.8"
  commandValue4: "1"
  commandValue5: "0.6"
  commandValue6: "0.7"
  commandValue7: "0.8"
  commandValue8: "1"
}
rows {
  id: 50039
  commandKey: "r.Filter.SizeScale"
  platform: "CloudGame"
  commandValue1: "0.6"
  commandValue2: "0.7"
  commandValue3: "0.8"
  commandValue4: "1"
  commandValue5: "0.6"
  commandValue6: "0.7"
  commandValue7: "0.8"
  commandValue8: "1"
}
rows {
  id: 60039
  commandKey: "r.Filter.SizeScale"
  platform: "MiniBox"
  commandValue1: "0.6"
  commandValue2: "0.7"
  commandValue3: "0.8"
  commandValue4: "1"
  commandValue5: "0.6"
  commandValue6: "0.7"
  commandValue7: "0.8"
  commandValue8: "1"
}
rows {
  id: 10040
  commandKey: "r.Tonemapper.Quality"
  platform: "Android"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "5"
  commandValue4: "5"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "5"
  commandValue8: "5"
}
rows {
  id: 20040
  commandKey: "r.Tonemapper.Quality"
  platform: "IOS"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "5"
  commandValue4: "5"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "5"
  commandValue8: "5"
}
rows {
  id: 30040
  commandKey: "r.Tonemapper.Quality"
  platform: "Windows"
  commandValue1: "2"
  commandValue2: "5"
  commandValue3: "5"
  commandValue4: "5"
  commandValue5: "2"
  commandValue6: "5"
  commandValue7: "5"
  commandValue8: "5"
}
rows {
  id: 40040
  commandKey: "r.Tonemapper.Quality"
  platform: "Harmony"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "5"
  commandValue4: "5"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "5"
  commandValue8: "5"
}
rows {
  id: 50040
  commandKey: "r.Tonemapper.Quality"
  platform: "CloudGame"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "5"
  commandValue4: "5"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "5"
  commandValue8: "5"
}
rows {
  id: 60040
  commandKey: "r.Tonemapper.Quality"
  platform: "MiniBox"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "5"
  commandValue4: "5"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "5"
  commandValue8: "5"
}
rows {
  id: 10041
  commandKey: "r.DOF.Gather.AccumulatorQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 20041
  commandKey: "r.DOF.Gather.AccumulatorQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 30041
  commandKey: "r.DOF.Gather.AccumulatorQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40041
  commandKey: "r.DOF.Gather.AccumulatorQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 50041
  commandKey: "r.DOF.Gather.AccumulatorQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 60041
  commandKey: "r.DOF.Gather.AccumulatorQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 10042
  commandKey: "r.DOF.Gather.PostfilterMethod"
  platform: "Android"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "1"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "1"
}
rows {
  id: 20042
  commandKey: "r.DOF.Gather.PostfilterMethod"
  platform: "IOS"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "1"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "1"
}
rows {
  id: 30042
  commandKey: "r.DOF.Gather.PostfilterMethod"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40042
  commandKey: "r.DOF.Gather.PostfilterMethod"
  platform: "Harmony"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "1"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "1"
}
rows {
  id: 50042
  commandKey: "r.DOF.Gather.PostfilterMethod"
  platform: "CloudGame"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "1"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "1"
}
rows {
  id: 60042
  commandKey: "r.DOF.Gather.PostfilterMethod"
  platform: "MiniBox"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "1"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "1"
}
rows {
  id: 90043
  commandKey: "r.DOF.Gather.EnableBokehSettings"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 10044
  commandKey: "r.DOF.Gather.RingCount"
  platform: "Android"
  commandValue1: "3"
  commandValue2: "3"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "3"
  commandValue6: "3"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 20044
  commandKey: "r.DOF.Gather.RingCount"
  platform: "IOS"
  commandValue1: "3"
  commandValue2: "3"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "3"
  commandValue6: "3"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 30044
  commandKey: "r.DOF.Gather.RingCount"
  platform: "Windows"
  commandValue1: "4"
  commandValue2: "4"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "4"
  commandValue6: "4"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 40044
  commandKey: "r.DOF.Gather.RingCount"
  platform: "Harmony"
  commandValue1: "3"
  commandValue2: "3"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "3"
  commandValue6: "3"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 50044
  commandKey: "r.DOF.Gather.RingCount"
  platform: "CloudGame"
  commandValue1: "3"
  commandValue2: "3"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "3"
  commandValue6: "3"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 60044
  commandKey: "r.DOF.Gather.RingCount"
  platform: "MiniBox"
  commandValue1: "3"
  commandValue2: "3"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "3"
  commandValue6: "3"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 10045
  commandKey: "r.DOF.Scatter.ForegroundCompositing"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20045
  commandKey: "r.DOF.Scatter.ForegroundCompositing"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30045
  commandKey: "r.DOF.Scatter.ForegroundCompositing"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40045
  commandKey: "r.DOF.Scatter.ForegroundCompositing"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50045
  commandKey: "r.DOF.Scatter.ForegroundCompositing"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60045
  commandKey: "r.DOF.Scatter.ForegroundCompositing"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10046
  commandKey: "r.DOF.Scatter.BackgroundCompositing"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "2"
}
rows {
  id: 20046
  commandKey: "r.DOF.Scatter.BackgroundCompositing"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "2"
}
rows {
  id: 30046
  commandKey: "r.DOF.Scatter.BackgroundCompositing"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 40046
  commandKey: "r.DOF.Scatter.BackgroundCompositing"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "2"
}
rows {
  id: 50046
  commandKey: "r.DOF.Scatter.BackgroundCompositing"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "2"
}
rows {
  id: 60046
  commandKey: "r.DOF.Scatter.BackgroundCompositing"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "2"
}
rows {
  id: 10047
  commandKey: "r.DOF.Scatter.EnableBokehSettings"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 20047
  commandKey: "r.DOF.Scatter.EnableBokehSettings"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 30047
  commandKey: "r.DOF.Scatter.EnableBokehSettings"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40047
  commandKey: "r.DOF.Scatter.EnableBokehSettings"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 50047
  commandKey: "r.DOF.Scatter.EnableBokehSettings"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 60047
  commandKey: "r.DOF.Scatter.EnableBokehSettings"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 10048
  commandKey: "r.DOF.Scatter.MaxSpriteRatio"
  platform: "Android"
  commandValue1: "0.04"
  commandValue2: "0.04"
  commandValue3: "0.04"
  commandValue4: "0.1"
  commandValue5: "0.04"
  commandValue6: "0.04"
  commandValue7: "0.04"
  commandValue8: "0.1"
}
rows {
  id: 20048
  commandKey: "r.DOF.Scatter.MaxSpriteRatio"
  platform: "IOS"
  commandValue1: "0.04"
  commandValue2: "0.04"
  commandValue3: "0.04"
  commandValue4: "0.1"
  commandValue5: "0.04"
  commandValue6: "0.04"
  commandValue7: "0.04"
  commandValue8: "0.1"
}
rows {
  id: 30048
  commandKey: "r.DOF.Scatter.MaxSpriteRatio"
  platform: "Windows"
  commandValue1: "0.04"
  commandValue2: "0.1"
  commandValue3: "0.1"
  commandValue4: "0.1"
  commandValue5: "0.04"
  commandValue6: "0.1"
  commandValue7: "0.1"
  commandValue8: "0.1"
}
rows {
  id: 40048
  commandKey: "r.DOF.Scatter.MaxSpriteRatio"
  platform: "Harmony"
  commandValue1: "0.04"
  commandValue2: "0.04"
  commandValue3: "0.04"
  commandValue4: "0.1"
  commandValue5: "0.04"
  commandValue6: "0.04"
  commandValue7: "0.04"
  commandValue8: "0.1"
}
rows {
  id: 50048
  commandKey: "r.DOF.Scatter.MaxSpriteRatio"
  platform: "CloudGame"
  commandValue1: "0.04"
  commandValue2: "0.04"
  commandValue3: "0.04"
  commandValue4: "0.1"
  commandValue5: "0.04"
  commandValue6: "0.04"
  commandValue7: "0.04"
  commandValue8: "0.1"
}
rows {
  id: 60048
  commandKey: "r.DOF.Scatter.MaxSpriteRatio"
  platform: "MiniBox"
  commandValue1: "0.04"
  commandValue2: "0.04"
  commandValue3: "0.04"
  commandValue4: "0.1"
  commandValue5: "0.04"
  commandValue6: "0.04"
  commandValue7: "0.04"
  commandValue8: "0.1"
}
rows {
  id: 10049
  commandKey: "r.DOF.Recombine.Quality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 20049
  commandKey: "r.DOF.Recombine.Quality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 30049
  commandKey: "r.DOF.Recombine.Quality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40049
  commandKey: "r.DOF.Recombine.Quality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 50049
  commandKey: "r.DOF.Recombine.Quality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 60049
  commandKey: "r.DOF.Recombine.Quality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 90050
  commandKey: "r.DOF.Recombine.EnableBokehSettings"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 10051
  commandKey: "r.DOF.TemporalAAQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 20051
  commandKey: "r.DOF.TemporalAAQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 30051
  commandKey: "r.DOF.TemporalAAQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40051
  commandKey: "r.DOF.TemporalAAQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 50051
  commandKey: "r.DOF.TemporalAAQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 60051
  commandKey: "r.DOF.TemporalAAQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 10052
  commandKey: "r.DOF.Kernel.MaxForegroundRadius"
  platform: "Android"
  commandValue1: "0.006"
  commandValue2: "0.006"
  commandValue3: "0.012"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.006"
  commandValue7: "0.012"
  commandValue8: "0.025"
}
rows {
  id: 20052
  commandKey: "r.DOF.Kernel.MaxForegroundRadius"
  platform: "IOS"
  commandValue1: "0.006"
  commandValue2: "0.006"
  commandValue3: "0.012"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.006"
  commandValue7: "0.012"
  commandValue8: "0.025"
}
rows {
  id: 30052
  commandKey: "r.DOF.Kernel.MaxForegroundRadius"
  platform: "Windows"
  commandValue1: "0.006"
  commandValue2: "0.025"
  commandValue3: "0.025"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.025"
  commandValue7: "0.025"
  commandValue8: "0.025"
}
rows {
  id: 40052
  commandKey: "r.DOF.Kernel.MaxForegroundRadius"
  platform: "Harmony"
  commandValue1: "0.006"
  commandValue2: "0.006"
  commandValue3: "0.012"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.006"
  commandValue7: "0.012"
  commandValue8: "0.025"
}
rows {
  id: 50052
  commandKey: "r.DOF.Kernel.MaxForegroundRadius"
  platform: "CloudGame"
  commandValue1: "0.006"
  commandValue2: "0.006"
  commandValue3: "0.012"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.006"
  commandValue7: "0.012"
  commandValue8: "0.025"
}
rows {
  id: 60052
  commandKey: "r.DOF.Kernel.MaxForegroundRadius"
  platform: "MiniBox"
  commandValue1: "0.006"
  commandValue2: "0.006"
  commandValue3: "0.012"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.006"
  commandValue7: "0.012"
  commandValue8: "0.025"
}
rows {
  id: 10053
  commandKey: "r.DOF.Kernel.MaxBackgroundRadius"
  platform: "Android"
  commandValue1: "0.006"
  commandValue2: "0.006"
  commandValue3: "0.012"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.006"
  commandValue7: "0.012"
  commandValue8: "0.025"
}
rows {
  id: 20053
  commandKey: "r.DOF.Kernel.MaxBackgroundRadius"
  platform: "IOS"
  commandValue1: "0.006"
  commandValue2: "0.006"
  commandValue3: "0.012"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.006"
  commandValue7: "0.012"
  commandValue8: "0.025"
}
rows {
  id: 30053
  commandKey: "r.DOF.Kernel.MaxBackgroundRadius"
  platform: "Windows"
  commandValue1: "0.006"
  commandValue2: "0.025"
  commandValue3: "0.025"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.025"
  commandValue7: "0.025"
  commandValue8: "0.025"
}
rows {
  id: 40053
  commandKey: "r.DOF.Kernel.MaxBackgroundRadius"
  platform: "Harmony"
  commandValue1: "0.006"
  commandValue2: "0.006"
  commandValue3: "0.012"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.006"
  commandValue7: "0.012"
  commandValue8: "0.025"
}
rows {
  id: 50053
  commandKey: "r.DOF.Kernel.MaxBackgroundRadius"
  platform: "CloudGame"
  commandValue1: "0.006"
  commandValue2: "0.006"
  commandValue3: "0.012"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.006"
  commandValue7: "0.012"
  commandValue8: "0.025"
}
rows {
  id: 60053
  commandKey: "r.DOF.Kernel.MaxBackgroundRadius"
  platform: "MiniBox"
  commandValue1: "0.006"
  commandValue2: "0.006"
  commandValue3: "0.012"
  commandValue4: "0.025"
  commandValue5: "0.006"
  commandValue6: "0.006"
  commandValue7: "0.012"
  commandValue8: "0.025"
}
rows {
  id: 10054
  commandKey: "r.RefractionQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 20054
  commandKey: "r.RefractionQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 30054
  commandKey: "r.RefractionQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 40054
  commandKey: "r.RefractionQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 50054
  commandKey: "r.RefractionQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 60054
  commandKey: "r.RefractionQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 90055
  commandKey: "r.Mobile.AmbientOcclusionQuality"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90056
  commandKey: "r.Mobile.PixelProjectedReflectionQuality"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90057
  commandKey: "r.Streaming.MipBias"
  platform: "All"
  commandValue1: "1"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90058
  commandKey: "r.Streaming.AmortizeCPUToGPUCopy"
  platform: "All"
  commandValue1: "1"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90059
  commandKey: "r.Streaming.MaxNumTexturesToStreamPerFrame"
  platform: "All"
  commandValue1: "1"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90060
  commandKey: "r.Streaming.Boost"
  platform: "All"
  commandValue1: "0.3"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.3"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90061
  commandKey: "r.MaxAnisotropy"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90062
  commandKey: "r.VT.MaxAnisotropy"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10063
  commandKey: "r.Streaming.LimitPoolSizeToVRAM"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "0"
}
rows {
  id: 20063
  commandKey: "r.Streaming.LimitPoolSizeToVRAM"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "0"
}
rows {
  id: 30063
  commandKey: "r.Streaming.LimitPoolSizeToVRAM"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 40063
  commandKey: "r.Streaming.LimitPoolSizeToVRAM"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "0"
}
rows {
  id: 50063
  commandKey: "r.Streaming.LimitPoolSizeToVRAM"
  platform: "CloudGame"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "0"
}
rows {
  id: 60063
  commandKey: "r.Streaming.LimitPoolSizeToVRAM"
  platform: "MiniBox"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "0"
}
rows {
  id: 10064
  commandKey: "r.Streaming.PoolSize"
  platform: "Android"
  commandValue1: "400"
  commandValue2: "500"
  commandValue3: "700"
  commandValue4: "900"
  commandValue5: "400"
  commandValue6: "500"
  commandValue7: "700"
  commandValue8: "900"
}
rows {
  id: 20064
  commandKey: "r.Streaming.PoolSize"
  platform: "IOS"
  commandValue1: "400"
  commandValue2: "500"
  commandValue3: "700"
  commandValue4: "900"
  commandValue5: "400"
  commandValue6: "500"
  commandValue7: "700"
  commandValue8: "900"
}
rows {
  id: 30064
  commandKey: "r.Streaming.PoolSize"
  platform: "Windows"
  commandValue1: "600"
  commandValue2: "1000"
  commandValue3: "1600"
  commandValue4: "2000"
  commandValue5: "600"
  commandValue6: "1000"
  commandValue7: "1600"
  commandValue8: "2000"
}
rows {
  id: 40064
  commandKey: "r.Streaming.PoolSize"
  platform: "Harmony"
  commandValue1: "400"
  commandValue2: "500"
  commandValue3: "700"
  commandValue4: "900"
  commandValue5: "400"
  commandValue6: "500"
  commandValue7: "700"
  commandValue8: "900"
}
rows {
  id: 50064
  commandKey: "r.Streaming.PoolSize"
  platform: "CloudGame"
  commandValue1: "400"
  commandValue2: "500"
  commandValue3: "700"
  commandValue4: "900"
  commandValue5: "400"
  commandValue6: "500"
  commandValue7: "700"
  commandValue8: "900"
}
rows {
  id: 60064
  commandKey: "r.Streaming.PoolSize"
  platform: "MiniBox"
  commandValue1: "400"
  commandValue2: "500"
  commandValue3: "700"
  commandValue4: "900"
  commandValue5: "400"
  commandValue6: "500"
  commandValue7: "700"
  commandValue8: "900"
}
rows {
  id: 90065
  commandKey: "r.Streaming.MaxEffectiveScreenSize"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 10066
  commandKey: "r.TranslucencyLightingVolumeDim"
  platform: "Android"
  commandValue1: "24"
  commandValue2: "24"
  commandValue3: "32"
  commandValue4: "48"
  commandValue5: "24"
  commandValue6: "24"
  commandValue7: "32"
  commandValue8: "48"
}
rows {
  id: 20066
  commandKey: "r.TranslucencyLightingVolumeDim"
  platform: "IOS"
  commandValue1: "24"
  commandValue2: "24"
  commandValue3: "32"
  commandValue4: "48"
  commandValue5: "24"
  commandValue6: "24"
  commandValue7: "32"
  commandValue8: "48"
}
rows {
  id: 30066
  commandKey: "r.TranslucencyLightingVolumeDim"
  platform: "Windows"
  commandValue1: "32"
  commandValue2: "48"
  commandValue3: "48"
  commandValue4: "48"
  commandValue5: "32"
  commandValue6: "48"
  commandValue7: "48"
  commandValue8: "48"
}
rows {
  id: 40066
  commandKey: "r.TranslucencyLightingVolumeDim"
  platform: "Harmony"
  commandValue1: "24"
  commandValue2: "24"
  commandValue3: "32"
  commandValue4: "48"
  commandValue5: "24"
  commandValue6: "24"
  commandValue7: "32"
  commandValue8: "48"
}
rows {
  id: 50066
  commandKey: "r.TranslucencyLightingVolumeDim"
  platform: "CloudGame"
  commandValue1: "24"
  commandValue2: "24"
  commandValue3: "32"
  commandValue4: "48"
  commandValue5: "24"
  commandValue6: "24"
  commandValue7: "32"
  commandValue8: "48"
}
rows {
  id: 60066
  commandKey: "r.TranslucencyLightingVolumeDim"
  platform: "MiniBox"
  commandValue1: "24"
  commandValue2: "24"
  commandValue3: "32"
  commandValue4: "48"
  commandValue5: "24"
  commandValue6: "24"
  commandValue7: "32"
  commandValue8: "48"
}
rows {
  id: 10067
  commandKey: "r.SSR.Quality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "2"
}
rows {
  id: 20067
  commandKey: "r.SSR.Quality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "2"
}
rows {
  id: 30067
  commandKey: "r.SSR.Quality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 40067
  commandKey: "r.SSR.Quality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "2"
}
rows {
  id: 50067
  commandKey: "r.SSR.Quality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "2"
}
rows {
  id: 60067
  commandKey: "r.SSR.Quality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "2"
}
rows {
  id: 90068
  commandKey: "r.SSR.HalfResSceneColor"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90069
  commandKey: "r.SceneColorFormat"
  platform: "All"
  commandValue1: "3"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "3"
  commandValue5: "3"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 90070
  commandKey: "r.DetailMode"
  platform: "All"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 10071
  commandKey: "r.TranslucencyVolumeBlur"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 20071
  commandKey: "r.TranslucencyVolumeBlur"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 30071
  commandKey: "r.TranslucencyVolumeBlur"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40071
  commandKey: "r.TranslucencyVolumeBlur"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 50071
  commandKey: "r.TranslucencyVolumeBlur"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 60071
  commandKey: "r.TranslucencyVolumeBlur"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 10072
  commandKey: "r.MaterialQualityLevel"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20072
  commandKey: "r.MaterialQualityLevel"
  platform: "IOS"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30072
  commandKey: "r.MaterialQualityLevel"
  platform: "Windows"
  commandValue1: "2"
  commandValue2: "1"
  commandValue3: "3"
  commandValue4: "3"
  commandValue5: "2"
  commandValue6: "1"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 40072
  commandKey: "r.MaterialQualityLevel"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50072
  commandKey: "r.MaterialQualityLevel"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60072
  commandKey: "r.MaterialQualityLevel"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10073
  commandKey: "r.AnisotropicMaterials"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20073
  commandKey: "r.AnisotropicMaterials"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30073
  commandKey: "r.AnisotropicMaterials"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40073
  commandKey: "r.AnisotropicMaterials"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50073
  commandKey: "r.AnisotropicMaterials"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60073
  commandKey: "r.AnisotropicMaterials"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10074
  commandKey: "r.SSS.Scale"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0.75"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0.75"
  commandValue8: "1"
}
rows {
  id: 20074
  commandKey: "r.SSS.Scale"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0.75"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0.75"
  commandValue8: "1"
}
rows {
  id: 30074
  commandKey: "r.SSS.Scale"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40074
  commandKey: "r.SSS.Scale"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0.75"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0.75"
  commandValue8: "1"
}
rows {
  id: 50074
  commandKey: "r.SSS.Scale"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0.75"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0.75"
  commandValue8: "1"
}
rows {
  id: 60074
  commandKey: "r.SSS.Scale"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0.75"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0.75"
  commandValue8: "1"
}
rows {
  id: 10075
  commandKey: "r.SSS.SampleSet"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 20075
  commandKey: "r.SSS.SampleSet"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 30075
  commandKey: "r.SSS.SampleSet"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40075
  commandKey: "r.SSS.SampleSet"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 50075
  commandKey: "r.SSS.SampleSet"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 60075
  commandKey: "r.SSS.SampleSet"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 10076
  commandKey: "r.SSS.Quality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "-1"
}
rows {
  id: 20076
  commandKey: "r.SSS.Quality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "-1"
}
rows {
  id: 30076
  commandKey: "r.SSS.Quality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "-1"
  commandValue3: "-1"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "-1"
  commandValue7: "-1"
  commandValue8: "-1"
}
rows {
  id: 40076
  commandKey: "r.SSS.Quality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "-1"
}
rows {
  id: 50076
  commandKey: "r.SSS.Quality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "-1"
}
rows {
  id: 60076
  commandKey: "r.SSS.Quality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "-1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "-1"
}
rows {
  id: 90077
  commandKey: "r.SSS.HalfRes"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10078
  commandKey: "r.SSGI.Quality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "2"
}
rows {
  id: 20078
  commandKey: "r.SSGI.Quality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "2"
}
rows {
  id: 30078
  commandKey: "r.SSGI.Quality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 40078
  commandKey: "r.SSGI.Quality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "2"
}
rows {
  id: 50078
  commandKey: "r.SSGI.Quality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "2"
}
rows {
  id: 60078
  commandKey: "r.SSGI.Quality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "2"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "2"
}
rows {
  id: 90079
  commandKey: "r.EmitterSpawnRateScale"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10080
  commandKey: "r.ParticleLightQuality"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20080
  commandKey: "r.ParticleLightQuality"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30080
  commandKey: "r.ParticleLightQuality"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40080
  commandKey: "r.ParticleLightQuality"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50080
  commandKey: "r.ParticleLightQuality"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60080
  commandKey: "r.ParticleLightQuality"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90081
  commandKey: "r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90082
  commandKey: "r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90083
  commandKey: "r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution"
  platform: "All"
  commandValue1: "4"
  commandValue2: "4"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "4"
  commandValue6: "4"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 90084
  commandKey: "r.SkyAtmosphere.FastSkyLUT"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90085
  commandKey: "r.SkyAtmosphere.FastSkyLUT.SampleCountMin"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90086
  commandKey: "r.SkyAtmosphere.FastSkyLUT.SampleCountMax"
  platform: "All"
  commandValue1: "8"
  commandValue2: "8"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "8"
  commandValue6: "8"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 10087
  commandKey: "r.SkyAtmosphere.SampleCountMin"
  platform: "Android"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 20087
  commandKey: "r.SkyAtmosphere.SampleCountMin"
  platform: "IOS"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 30087
  commandKey: "r.SkyAtmosphere.SampleCountMin"
  platform: "Windows"
  commandValue1: "2"
  commandValue2: "4"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "2"
  commandValue6: "4"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 40087
  commandKey: "r.SkyAtmosphere.SampleCountMin"
  platform: "Harmony"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 50087
  commandKey: "r.SkyAtmosphere.SampleCountMin"
  platform: "CloudGame"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 60087
  commandKey: "r.SkyAtmosphere.SampleCountMin"
  platform: "MiniBox"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 10088
  commandKey: "r.SkyAtmosphere.SampleCountMax"
  platform: "Android"
  commandValue1: "16"
  commandValue2: "16"
  commandValue3: "32"
  commandValue4: "64"
  commandValue5: "16"
  commandValue6: "16"
  commandValue7: "32"
  commandValue8: "64"
}
rows {
  id: 20088
  commandKey: "r.SkyAtmosphere.SampleCountMax"
  platform: "IOS"
  commandValue1: "16"
  commandValue2: "16"
  commandValue3: "32"
  commandValue4: "64"
  commandValue5: "16"
  commandValue6: "16"
  commandValue7: "32"
  commandValue8: "64"
}
rows {
  id: 30088
  commandKey: "r.SkyAtmosphere.SampleCountMax"
  platform: "Windows"
  commandValue1: "16"
  commandValue2: "64"
  commandValue3: "64"
  commandValue4: "64"
  commandValue5: "16"
  commandValue6: "64"
  commandValue7: "64"
  commandValue8: "64"
}
rows {
  id: 40088
  commandKey: "r.SkyAtmosphere.SampleCountMax"
  platform: "Harmony"
  commandValue1: "16"
  commandValue2: "16"
  commandValue3: "32"
  commandValue4: "64"
  commandValue5: "16"
  commandValue6: "16"
  commandValue7: "32"
  commandValue8: "64"
}
rows {
  id: 50088
  commandKey: "r.SkyAtmosphere.SampleCountMax"
  platform: "CloudGame"
  commandValue1: "16"
  commandValue2: "16"
  commandValue3: "32"
  commandValue4: "64"
  commandValue5: "16"
  commandValue6: "16"
  commandValue7: "32"
  commandValue8: "64"
}
rows {
  id: 60088
  commandKey: "r.SkyAtmosphere.SampleCountMax"
  platform: "MiniBox"
  commandValue1: "16"
  commandValue2: "16"
  commandValue3: "32"
  commandValue4: "64"
  commandValue5: "16"
  commandValue6: "16"
  commandValue7: "32"
  commandValue8: "64"
}
rows {
  id: 90089
  commandKey: "r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90090
  commandKey: "r.SkyAtmosphere.TransmittanceLUT.SampleCount"
  platform: "All"
  commandValue1: "10"
  commandValue2: "10"
  commandValue3: "10"
  commandValue4: "10"
  commandValue5: "10"
  commandValue6: "10"
  commandValue7: "10"
  commandValue8: "10"
}
rows {
  id: 90091
  commandKey: "r.SkyAtmosphere.MultiScatteringLUT.SampleCount"
  platform: "All"
  commandValue1: "15"
  commandValue2: "15"
  commandValue3: "15"
  commandValue4: "15"
  commandValue5: "15"
  commandValue6: "15"
  commandValue7: "15"
  commandValue8: "15"
}
rows {
  id: 90092
  commandKey: "r.SkyLight.RealTimeReflectionCapture"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 10093
  commandKey: "r.ParticleLODBias"
  platform: "Android"
  commandValue1: "2"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "2"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 20093
  commandKey: "r.ParticleLODBias"
  platform: "IOS"
  commandValue1: "2"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "2"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 30093
  commandKey: "r.ParticleLODBias"
  platform: "Windows"
  commandValue1: "2"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "2"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 40093
  commandKey: "r.ParticleLODBias"
  platform: "Harmony"
  commandValue1: "2"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "2"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 50093
  commandKey: "r.ParticleLODBias"
  platform: "CloudGame"
  commandValue1: "2"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "2"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 60093
  commandKey: "r.ParticleLODBias"
  platform: "MiniBox"
  commandValue1: "2"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "2"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90094
  commandKey: "r.SkyAtmosphere.FastSkyLUT.Width"
  platform: "All"
  commandValue1: "96"
  commandValue2: "96"
  commandValue3: "96"
  commandValue4: "96"
  commandValue5: "96"
  commandValue6: "96"
  commandValue7: "96"
  commandValue8: "96"
}
rows {
  id: 90095
  commandKey: "r.SkyAtmosphere.FastSkyLUT.Height"
  platform: "All"
  commandValue1: "50"
  commandValue2: "50"
  commandValue3: "50"
  commandValue4: "50"
  commandValue5: "50"
  commandValue6: "50"
  commandValue7: "50"
  commandValue8: "50"
}
rows {
  id: 10096
  commandKey: "foliage.DensityScale"
  platform: "Android"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20096
  commandKey: "foliage.DensityScale"
  platform: "IOS"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30096
  commandKey: "foliage.DensityScale"
  platform: "Windows"
  commandValue1: "0.8"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.8"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40096
  commandKey: "foliage.DensityScale"
  platform: "Harmony"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50096
  commandKey: "foliage.DensityScale"
  platform: "CloudGame"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60096
  commandKey: "foliage.DensityScale"
  platform: "MiniBox"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10097
  commandKey: "grass.DensityScale"
  platform: "Android"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20097
  commandKey: "grass.DensityScale"
  platform: "IOS"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30097
  commandKey: "grass.DensityScale"
  platform: "Windows"
  commandValue1: "0.8"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.8"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40097
  commandKey: "grass.DensityScale"
  platform: "Harmony"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50097
  commandKey: "grass.DensityScale"
  platform: "CloudGame"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60097
  commandKey: "grass.DensityScale"
  platform: "MiniBox"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10098
  commandKey: "grass.CullDistanceScale"
  platform: "Android"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20098
  commandKey: "grass.CullDistanceScale"
  platform: "IOS"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30098
  commandKey: "grass.CullDistanceScale"
  platform: "Windows"
  commandValue1: "0.8"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.8"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40098
  commandKey: "grass.CullDistanceScale"
  platform: "Harmony"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50098
  commandKey: "grass.CullDistanceScale"
  platform: "CloudGame"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60098
  commandKey: "grass.CullDistanceScale"
  platform: "MiniBox"
  commandValue1: "0.5"
  commandValue2: "0.8"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0.5"
  commandValue6: "0.8"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90099
  commandKey: "r.HairStrands.SkyLighting.IntegrationType"
  platform: "All"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 10100
  commandKey: "r.HairStrands.SkyAO.SampleCount"
  platform: "Android"
  commandValue1: "4"
  commandValue2: "4"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "4"
  commandValue6: "4"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 20100
  commandKey: "r.HairStrands.SkyAO.SampleCount"
  platform: "IOS"
  commandValue1: "4"
  commandValue2: "4"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "4"
  commandValue6: "4"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 30100
  commandKey: "r.HairStrands.SkyAO.SampleCount"
  platform: "Windows"
  commandValue1: "8"
  commandValue2: "8"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "8"
  commandValue6: "8"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 40100
  commandKey: "r.HairStrands.SkyAO.SampleCount"
  platform: "Harmony"
  commandValue1: "4"
  commandValue2: "4"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "4"
  commandValue6: "4"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 50100
  commandKey: "r.HairStrands.SkyAO.SampleCount"
  platform: "CloudGame"
  commandValue1: "4"
  commandValue2: "4"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "4"
  commandValue6: "4"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 60100
  commandKey: "r.HairStrands.SkyAO.SampleCount"
  platform: "MiniBox"
  commandValue1: "4"
  commandValue2: "4"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "4"
  commandValue6: "4"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 10101
  commandKey: "r.HairStrands.Visibility.MSAA.SamplePerPixel"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 20101
  commandKey: "r.HairStrands.Visibility.MSAA.SamplePerPixel"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 30101
  commandKey: "r.HairStrands.Visibility.MSAA.SamplePerPixel"
  platform: "Windows"
  commandValue1: "8"
  commandValue2: "8"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "8"
  commandValue6: "8"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 40101
  commandKey: "r.HairStrands.Visibility.MSAA.SamplePerPixel"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 50101
  commandKey: "r.HairStrands.Visibility.MSAA.SamplePerPixel"
  platform: "CloudGame"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 60101
  commandKey: "r.HairStrands.Visibility.MSAA.SamplePerPixel"
  platform: "MiniBox"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "8"
  commandValue4: "8"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "8"
  commandValue8: "8"
}
rows {
  id: 90102
  commandKey: "r.Shadow.CSMDepthBias"
  platform: "All"
  commandValue1: "10"
  commandValue2: "10"
  commandValue3: "10"
  commandValue4: "10"
  commandValue5: "10"
  commandValue6: "10"
  commandValue7: "10"
  commandValue8: "10"
}
rows {
  id: 90103
  commandKey: "r.Shadow.CSMSlopeScaleDepthBias"
  platform: "All"
  commandValue1: "3"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "3"
  commandValue5: "3"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 10104
  commandKey: "Slate.ForceBackgroundBlurLowQualityOverride"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 20104
  commandKey: "Slate.ForceBackgroundBlurLowQualityOverride"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 30104
  commandKey: "Slate.ForceBackgroundBlurLowQualityOverride"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 40104
  commandKey: "Slate.ForceBackgroundBlurLowQualityOverride"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 50104
  commandKey: "Slate.ForceBackgroundBlurLowQualityOverride"
  platform: "CloudGame"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 60104
  commandKey: "Slate.ForceBackgroundBlurLowQualityOverride"
  platform: "MiniBox"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90105
  commandKey: "r.StaticMeshLODDistanceScale"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90106
  commandKey: "r.StaticMeshLODBias"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90107
  commandKey: "r.HZBOcclusion"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90108
  commandKey: "r.EarlyZPass"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90109
  commandKey: "r.TranslucentLightingVolume"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90110
  commandKey: "r.AllowPointLightCubemapShadows"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90111
  commandKey: "r.Decal.StencilSizeThreshold"
  platform: "All"
  commandValue1: "-1"
  commandValue2: "-1"
  commandValue3: "-1"
  commandValue4: "-1"
  commandValue5: "-1"
  commandValue6: "-1"
  commandValue7: "-1"
  commandValue8: "-1"
}
rows {
  id: 90112
  commandKey: "slate.AbsoluteIndices"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90113
  commandKey: "r.MorphTarget.Mode"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90114
  commandKey: "r.DefaultBackBufferPixelFormat"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90115
  commandKey: "r.Mobile.ShadowmapRoundUpToPowerOfTwo"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90116
  commandKey: "r.TemporalAA.Upsampling"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90117
  commandKey: "r.Mobile.TonemapperFilm"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90118
  commandKey: "r.Streaming.UsePerTextureBias"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10119
  commandKey: "r.DisableDistortion"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 20119
  commandKey: "r.DisableDistortion"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 30119
  commandKey: "r.DisableDistortion"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 40119
  commandKey: "r.DisableDistortion"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 50119
  commandKey: "r.DisableDistortion"
  platform: "CloudGame"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 60119
  commandKey: "r.DisableDistortion"
  platform: "MiniBox"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90120
  commandKey: "a.URO.GlobalAlgo"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90121
  commandKey: "r.MipMapLODBias"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90122
  commandKey: "rhi.Metal.ForceIOSTexturesShared"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90123
  commandKey: "r.Mobile.AmbientOcclusionDownSample"
  platform: "All"
  commandValue1: "4"
  commandValue2: "4"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "4"
  commandValue6: "4"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 90124
  commandKey: "r.UseFixedFrameRate"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 10125
  commandKey: "r.TextureStreaming"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20125
  commandKey: "r.TextureStreaming"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30125
  commandKey: "r.TextureStreaming"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40125
  commandKey: "r.TextureStreaming"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50125
  commandKey: "r.TextureStreaming"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 60125
  commandKey: "r.TextureStreaming"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90126
  commandKey: "r.AllowOcclusionQueries"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90127
  commandKey: "r.SkeletalMeshLODRadiusScale"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10128
  commandKey: "r.FidelityFX.FSR.Enabled"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 20128
  commandKey: "r.FidelityFX.FSR.Enabled"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 30128
  commandKey: "r.FidelityFX.FSR.Enabled"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40128
  commandKey: "r.FidelityFX.FSR.Enabled"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 50128
  commandKey: "r.FidelityFX.FSR.Enabled"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 60128
  commandKey: "r.FidelityFX.FSR.Enabled"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 10129
  commandKey: "r.FidelityFX.FSR.Debug.ForcePS"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20129
  commandKey: "r.FidelityFX.FSR.Debug.ForcePS"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30129
  commandKey: "r.FidelityFX.FSR.Debug.ForcePS"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40129
  commandKey: "r.FidelityFX.FSR.Debug.ForcePS"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50129
  commandKey: "r.FidelityFX.FSR.Debug.ForcePS"
  platform: "CloudGame"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60129
  commandKey: "r.FidelityFX.FSR.Debug.ForcePS"
  platform: "MiniBox"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90130
  commandKey: "r.SeparateTranslucency"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90131
  commandKey: "r.SeparateTranslucencyScreenPercentage"
  platform: "All"
  commandValue1: "100"
  commandValue2: "100"
  commandValue3: "100"
  commandValue4: "100"
  commandValue5: "100"
  commandValue6: "100"
  commandValue7: "100"
  commandValue8: "100"
}
rows {
  id: 90132
  commandKey: "r.Mobile.EyeAdaptation"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90133
  commandKey: "r.Fog"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10134
  commandKey: "r.MobileHDR"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 20134
  commandKey: "r.MobileHDR"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 30134
  commandKey: "r.MobileHDR"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40134
  commandKey: "r.MobileHDR"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 50134
  commandKey: "r.MobileHDR"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 60134
  commandKey: "r.MobileHDR"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "1"
}
rows {
  id: 10135
  commandKey: "a.Budget.BudgetMs"
  platform: "Android"
  commandValue1: "3.25"
  commandValue2: "2.5"
  commandValue3: "1.85"
  commandValue4: "1.25"
  commandValue5: "3.25"
  commandValue6: "2.5"
  commandValue7: "1.85"
  commandValue8: "1.25"
}
rows {
  id: 20135
  commandKey: "a.Budget.BudgetMs"
  platform: "IOS"
  commandValue1: "3"
  commandValue2: "2.3"
  commandValue3: "1.75"
  commandValue4: "1.25"
  commandValue5: "3"
  commandValue6: "2.3"
  commandValue7: "1.75"
  commandValue8: "1.25"
}
rows {
  id: 30135
  commandKey: "a.Budget.BudgetMs"
  platform: "Windows"
  commandValue1: "2.5"
  commandValue2: "1.25"
  commandValue3: "1.25"
  commandValue4: "1.25"
  commandValue5: "2.5"
  commandValue6: "1.25"
  commandValue7: "1.25"
  commandValue8: "1.25"
}
rows {
  id: 40135
  commandKey: "a.Budget.BudgetMs"
  platform: "Harmony"
  commandValue1: "3.25"
  commandValue2: "2.5"
  commandValue3: "1.85"
  commandValue4: "1.25"
  commandValue5: "3.25"
  commandValue6: "2.5"
  commandValue7: "1.85"
  commandValue8: "1.25"
}
rows {
  id: 50135
  commandKey: "a.Budget.BudgetMs"
  platform: "CloudGame"
  commandValue1: "3.25"
  commandValue2: "2.5"
  commandValue3: "1.85"
  commandValue4: "1.25"
  commandValue5: "3.25"
  commandValue6: "2.5"
  commandValue7: "1.85"
  commandValue8: "1.25"
}
rows {
  id: 60135
  commandKey: "a.Budget.BudgetMs"
  platform: "MiniBox"
  commandValue1: "3.25"
  commandValue2: "2.5"
  commandValue3: "1.85"
  commandValue4: "1.25"
  commandValue5: "3.25"
  commandValue6: "2.5"
  commandValue7: "1.85"
  commandValue8: "1.25"
}
rows {
  id: 10136
  commandKey: "r.Mobile.SceneColorFormat"
  platform: "Android"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 20136
  commandKey: "r.Mobile.SceneColorFormat"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30136
  commandKey: "r.Mobile.SceneColorFormat"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40136
  commandKey: "r.Mobile.SceneColorFormat"
  platform: "Harmony"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 50136
  commandKey: "r.Mobile.SceneColorFormat"
  platform: "CloudGame"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 60136
  commandKey: "r.Mobile.SceneColorFormat"
  platform: "MiniBox"
  commandValue1: "2"
  commandValue2: "2"
  commandValue3: "2"
  commandValue4: "2"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 10137
  commandKey: "p.Budget.BudgetMs"
  platform: "Android"
  commandValue1: "8"
  commandValue2: "7"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "8"
  commandValue6: "7"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20137
  commandKey: "p.Budget.BudgetMs"
  platform: "IOS"
  commandValue1: "8"
  commandValue2: "7"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "8"
  commandValue6: "7"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30137
  commandKey: "p.Budget.BudgetMs"
  platform: "Windows"
  commandValue1: "6"
  commandValue2: "5"
  commandValue3: "5"
  commandValue4: "5"
  commandValue5: "6"
  commandValue6: "5"
  commandValue7: "5"
  commandValue8: "5"
}
rows {
  id: 40137
  commandKey: "p.Budget.BudgetMs"
  platform: "Harmony"
  commandValue1: "8"
  commandValue2: "7"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "8"
  commandValue6: "7"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50137
  commandKey: "p.Budget.BudgetMs"
  platform: "CloudGame"
  commandValue1: "8"
  commandValue2: "7"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "8"
  commandValue6: "7"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60137
  commandKey: "p.Budget.BudgetMs"
  platform: "MiniBox"
  commandValue1: "8"
  commandValue2: "7"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "8"
  commandValue6: "7"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10138
  commandKey: "c.Budget.BudgetMs"
  platform: "Android"
  commandValue1: "3.5"
  commandValue2: "3.5"
  commandValue3: "4.75"
  commandValue4: "5"
  commandValue5: "3.5"
  commandValue6: "3.5"
  commandValue7: "4.75"
  commandValue8: "5"
}
rows {
  id: 20138
  commandKey: "c.Budget.BudgetMs"
  platform: "IOS"
  commandValue1: "2.5"
  commandValue2: "2.5"
  commandValue3: "2.75"
  commandValue4: "3"
  commandValue5: "2.5"
  commandValue6: "2.5"
  commandValue7: "2.75"
  commandValue8: "3"
}
rows {
  id: 30138
  commandKey: "c.Budget.BudgetMs"
  platform: "Windows"
  commandValue1: "2.75"
  commandValue2: "3"
  commandValue3: "3"
  commandValue4: "3"
  commandValue5: "2.75"
  commandValue6: "3"
  commandValue7: "3"
  commandValue8: "3"
}
rows {
  id: 40138
  commandKey: "c.Budget.BudgetMs"
  platform: "Harmony"
  commandValue1: "3.5"
  commandValue2: "3.5"
  commandValue3: "4.75"
  commandValue4: "5"
  commandValue5: "3.5"
  commandValue6: "3.5"
  commandValue7: "4.75"
  commandValue8: "5"
}
rows {
  id: 50138
  commandKey: "c.Budget.BudgetMs"
  platform: "CloudGame"
  commandValue1: "3.5"
  commandValue2: "3.5"
  commandValue3: "4.75"
  commandValue4: "5"
  commandValue5: "3.5"
  commandValue6: "3.5"
  commandValue7: "4.75"
  commandValue8: "5"
}
rows {
  id: 60138
  commandKey: "c.Budget.BudgetMs"
  platform: "MiniBox"
  commandValue1: "3.5"
  commandValue2: "3.5"
  commandValue3: "4.75"
  commandValue4: "5"
  commandValue5: "3.5"
  commandValue6: "3.5"
  commandValue7: "4.75"
  commandValue8: "5"
}
rows {
  id: 10139
  commandKey: "moe.MaxWaterFx"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "20"
  commandValue4: "20"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "20"
  commandValue8: "20"
}
rows {
  id: 20139
  commandKey: "moe.MaxWaterFx"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "20"
  commandValue4: "20"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "20"
  commandValue8: "20"
}
rows {
  id: 30139
  commandKey: "moe.MaxWaterFx"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "20"
  commandValue3: "20"
  commandValue4: "20"
  commandValue5: "0"
  commandValue6: "20"
  commandValue7: "20"
  commandValue8: "20"
}
rows {
  id: 40139
  commandKey: "moe.MaxWaterFx"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "20"
  commandValue4: "20"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "20"
  commandValue8: "20"
}
rows {
  id: 50139
  commandKey: "moe.MaxWaterFx"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "20"
  commandValue4: "20"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "20"
  commandValue8: "20"
}
rows {
  id: 60139
  commandKey: "moe.MaxWaterFx"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "20"
  commandValue4: "20"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "20"
  commandValue8: "20"
}
rows {
  id: 10140
  commandKey: "r.CSAA"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20140
  commandKey: "r.CSAA"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 30140
  commandKey: "r.CSAA"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 40140
  commandKey: "r.CSAA"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50140
  commandKey: "r.CSAA"
  platform: "CloudGame"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60140
  commandKey: "r.CSAA"
  platform: "MiniBox"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10141
  commandKey: "r.ReflectionCaptureUpdateWorldActor"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20141
  commandKey: "r.ReflectionCaptureUpdateWorldActor"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30141
  commandKey: "r.ReflectionCaptureUpdateWorldActor"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40141
  commandKey: "r.ReflectionCaptureUpdateWorldActor"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50141
  commandKey: "r.ReflectionCaptureUpdateWorldActor"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60141
  commandKey: "r.ReflectionCaptureUpdateWorldActor"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10142
  commandKey: "r.CharacterFullySimulate"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20142
  commandKey: "r.CharacterFullySimulate"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30142
  commandKey: "r.CharacterFullySimulate"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40142
  commandKey: "r.CharacterFullySimulate"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50142
  commandKey: "r.CharacterFullySimulate"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60142
  commandKey: "r.CharacterFullySimulate"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90143
  commandKey: "LetsGo.EnableShowFastMotionVFX"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90144
  commandKey: "r.SDOC.OccluderMaxTriNum"
  platform: "All"
  commandValue1: "10000"
  commandValue2: "15000"
  commandValue3: "20000"
  commandValue4: "30000"
  commandValue5: "10000"
  commandValue6: "15000"
  commandValue7: "20000"
  commandValue8: "30000"
}
rows {
  id: 90145
  commandKey: "r.SDOC.RenderMode"
  platform: "All"
  commandValue1: "2"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "2"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90146
  commandKey: "r.SDOC.SameFrameSkip"
  platform: "All"
  commandValue1: "1"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90147
  commandKey: "r.SDOC.OccludeeExpland"
  platform: "All"
  commandValue1: "25"
  commandValue2: "25"
  commandValue3: "25"
  commandValue4: "25"
  commandValue5: "25"
  commandValue6: "25"
  commandValue7: "25"
  commandValue8: "25"
}
rows {
  id: 30147
  commandKey: "Moe.UGCVisibleDistanceBias"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10148
  commandKey: "r.ApplyResolutionFractionBy1080p"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20148
  commandKey: "r.ApplyResolutionFractionBy1080p"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30148
  commandKey: "r.ApplyResolutionFractionBy1080p"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 30149
  commandKey: "r.ScreenPercentage"
  platform: "Windows"
  commandValue1: "67"
  commandValue2: "77"
  commandValue3: "77"
  commandValue4: "77"
  commandValue5: "67"
  commandValue6: "77"
  commandValue7: "77"
  commandValue8: "77"
}
rows {
  id: 10150
  commandKey: "r.MobileMSAA"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20150
  commandKey: "r.MobileMSAA"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30150
  commandKey: "r.MobileMSAA"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "2"
  commandValue3: "4"
  commandValue4: "4"
  commandValue5: "1"
  commandValue6: "2"
  commandValue7: "4"
  commandValue8: "4"
}
rows {
  id: 40150
  commandKey: "r.MobileMSAA"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50150
  commandKey: "r.MobileMSAA"
  platform: "CloudGame"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60150
  commandKey: "r.MobileMSAA"
  platform: "MiniBox"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90151
  commandKey: "r.MinAspecRatioFor1920p"
  platform: "All"
  commandValue1: "10"
  commandValue2: "10"
  commandValue3: "10"
  commandValue4: "10"
  commandValue5: "10"
  commandValue6: "10"
  commandValue7: "10"
  commandValue8: "10"
}
rows {
  id: 10152
  commandKey: "r.MobileContentScaleFactor"
  platform: "Android"
  commandValue1: "1.5"
  commandValue2: "1.5"
  commandValue3: "1.5"
  commandValue4: "1.5"
  commandValue5: "1.5"
  commandValue6: "1.5"
  commandValue7: "1.5"
  commandValue8: "1.5"
}
rows {
  id: 20152
  commandKey: "r.MobileContentScaleFactor"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 30152
  commandKey: "r.MobileContentScaleFactor"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 40152
  commandKey: "r.MobileContentScaleFactor"
  platform: "Harmony"
  commandValue1: "1.5"
  commandValue2: "1.5"
  commandValue3: "1.5"
  commandValue4: "1.5"
  commandValue5: "1.5"
  commandValue6: "1.5"
  commandValue7: "1.5"
  commandValue8: "1.5"
}
rows {
  id: 50152
  commandKey: "r.MobileContentScaleFactor"
  platform: "CloudGame"
  commandValue1: "1.5"
  commandValue2: "1.5"
  commandValue3: "1.5"
  commandValue4: "1.5"
  commandValue5: "1.5"
  commandValue6: "1.5"
  commandValue7: "1.5"
  commandValue8: "1.5"
}
rows {
  id: 60152
  commandKey: "r.MobileContentScaleFactor"
  platform: "MiniBox"
  commandValue1: "1.5"
  commandValue2: "1.5"
  commandValue3: "1.5"
  commandValue4: "1.5"
  commandValue5: "1.5"
  commandValue6: "1.5"
  commandValue7: "1.5"
  commandValue8: "1.5"
}
rows {
  id: 90153
  commandKey: "MoeLua|SetMaxFPS"
  platform: "All"
  commandValue1: "30"
  commandValue2: "30"
  commandValue3: "30"
  commandValue4: "30"
  commandValue5: "60"
  commandValue6: "60"
  commandValue7: "60"
  commandValue8: "60"
}
rows {
  id: 90154
  commandKey: "MoeLua|SetFPSCheckAgentEnable"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90155
  commandKey: "t.GEnableVariablefps"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90156
  commandKey: "MoeLua|EnableAnimationBudget"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90157
  commandKey: "MoeLua|EnableCharacterMovementBudget"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90158
  commandKey: "fx.ParticleMinimalScreenSize"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90159
  commandKey: "fx.EnableEmitterBudget"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90160
  commandKey: "MoeLua|SetEmitterBudgetValue"
  platform: "All"
  commandValue1: "50"
  commandValue2: "50"
  commandValue3: "50"
  commandValue4: "50"
  commandValue5: "50"
  commandValue6: "50"
  commandValue7: "50"
  commandValue8: "50"
}
rows {
  id: 90161
  commandKey: "MoeLua|SetEmitterBudgetLowerValue"
  platform: "All"
  commandValue1: "100"
  commandValue2: "100"
  commandValue3: "100"
  commandValue4: "100"
  commandValue5: "100"
  commandValue6: "100"
  commandValue7: "100"
  commandValue8: "100"
}
rows {
  id: 90162
  commandKey: "MoeLua|SetEmitterBudgetLowerEnd"
  platform: "All"
  commandValue1: "150"
  commandValue2: "150"
  commandValue3: "150"
  commandValue4: "150"
  commandValue5: "150"
  commandValue6: "150"
  commandValue7: "150"
  commandValue8: "150"
}
rows {
  id: 90163
  commandKey: "Moe.Gameplay.EnableIdleShowScreenSizeOpt"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90164
  commandKey: "Moe.Gameplay.AnimEnableIKUtilsACMBlendLikeNode"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90165
  commandKey: "Moe.Gameplay.AnimBPEnableSimplifiedPath"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90166
  commandKey: "a.URO.Enable"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90167
  commandKey: "a.EnableDecorateAnimURO"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90168
  commandKey: "a.EnableChangeDecorateTickOption"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90169
  commandKey: "gc.TimeBetweenPurgingPendingKillObjects"
  platform: "All"
  commandValue1: "60"
  commandValue2: "60"
  commandValue3: "60"
  commandValue4: "60"
  commandValue5: "60"
  commandValue6: "60"
  commandValue7: "60"
  commandValue8: "60"
}
rows {
  id: 90170
  commandKey: "a.Budget.Enabled"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90171
  commandKey: "c.Budget.Enabled"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90172
  commandKey: "Moe.Gameplay.AnimBPEnableTickReplaceAnim"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90173
  commandKey: "MoeLua|EnablePhysicsBudget"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90174
  commandKey: "p.Budget.Enabled"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90175
  commandKey: "p.Budget.AlwaysTickFalloffAggression"
  platform: "All"
  commandValue1: "600"
  commandValue2: "600"
  commandValue3: "600"
  commandValue4: "600"
  commandValue5: "600"
  commandValue6: "600"
  commandValue7: "600"
  commandValue8: "600"
}
rows {
  id: 90176
  commandKey: "p.Budget.BudgetFactorBeforeReducedWork"
  platform: "All"
  commandValue1: "0.5"
  commandValue2: "0.5"
  commandValue3: "0.5"
  commandValue4: "0.5"
  commandValue5: "0.5"
  commandValue6: "0.5"
  commandValue7: "0.5"
  commandValue8: "0.5"
}
rows {
  id: 90177
  commandKey: "p.Budget.BudgetFactorBeforeReducedWorkEpsilon"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90178
  commandKey: "MoePerformance.EnableAsyncLoadCharAnim"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90179
  commandKey: "MoeLua|SetHandholdAsync"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90180
  commandKey: "Moe.LazyTable.PreviewActorConfig"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90181
  commandKey: "Moe.LazyTable.SpecialAvatarAssetsData"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90182
  commandKey: "p.UseAsyncInterpolation"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90183
  commandKey: "fx.TickCulling"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90184
  commandKey: "MoeLua|UseUpdatedParticleFixedBoundingBox"
  platform: "All"
  commandValue1: "/"
  commandValue2: "/"
  commandValue3: "/"
  commandValue4: "/"
  commandValue5: "/"
  commandValue6: "/"
  commandValue7: "/"
  commandValue8: "/"
}
rows {
  id: 90185
  commandKey: "fx.UseCullingBeforeSort"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90186
  commandKey: "fx.S9"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90187
  commandKey: "fx.BudgetDistance"
  platform: "All"
  commandValue1: "20"
  commandValue2: "20"
  commandValue3: "20"
  commandValue4: "20"
  commandValue5: "20"
  commandValue6: "20"
  commandValue7: "20"
  commandValue8: "20"
}
rows {
  id: 90188
  commandKey: "fx.EnableDistanceCull"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90189
  commandKey: "fx.ForceCullDistance"
  platform: "All"
  commandValue1: "4000"
  commandValue2: "4000"
  commandValue3: "4000"
  commandValue4: "4000"
  commandValue5: "4000"
  commandValue6: "4000"
  commandValue7: "4000"
  commandValue8: "4000"
}
rows {
  id: 90190
  commandKey: "fx.UseScreenSizeBias"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90191
  commandKey: "fx.ScreenSizeBias1"
  platform: "All"
  commandValue1: "0.08"
  commandValue2: "0.08"
  commandValue3: "0.08"
  commandValue4: "0.08"
  commandValue5: "0.08"
  commandValue6: "0.08"
  commandValue7: "0.08"
  commandValue8: "0.08"
}
rows {
  id: 90192
  commandKey: "fx.ScreenSizeBias2"
  platform: "All"
  commandValue1: "0.04"
  commandValue2: "0.04"
  commandValue3: "0.04"
  commandValue4: "0.04"
  commandValue5: "0.04"
  commandValue6: "0.04"
  commandValue7: "0.04"
  commandValue8: "0.04"
}
rows {
  id: 90193
  commandKey: "fx.UseScreenSizeCull"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90194
  commandKey: "Slate.LobbyUIDrawcallOptimize"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90195
  commandKey: "MoePerformance.EnableAsyncLoadCharEmitter"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90196
  commandKey: "MoePerformance.EnableAsyncLoadProjectionAnim"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90197
  commandKey: "MoePerformance.EnableAsyncLoadCharJumpAnim"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90198
  commandKey: "MoePerformance.EnableAsyncLoadSuitMoveAnim"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90199
  commandKey: "MoePerformance.EnableAsyncLoadAnimNotify"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90200
  commandKey: "MoePerformance.EnablePreLoadBaseJumpAnim"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90201
  commandKey: "MoePerformance.EnableAsyncLoadEnterGameAni"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90202
  commandKey: "fx.EnableFrontDetect"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90203
  commandKey: "fx.UseUpdatedParticleFixedBoundingBox"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 30204
  commandKey: "r.Vsync"
  platform: "Windows"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90205
  commandKey: "MoeLua|SetCharChangeSkinDispatchOpt"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10206
  commandKey: "Slate.EnableGlobalInvalidation"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20206
  commandKey: "Slate.EnableGlobalInvalidation"
  platform: "IOS"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30206
  commandKey: "Slate.EnableGlobalInvalidation"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 40206
  commandKey: "Slate.EnableGlobalInvalidation"
  platform: "Harmony"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50206
  commandKey: "Slate.EnableGlobalInvalidation"
  platform: "CloudGame"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60206
  commandKey: "Slate.EnableGlobalInvalidation"
  platform: "MiniBox"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90207
  commandKey: "MoePerformance.EnableWidgetPool"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90208
  commandKey: "fx.AvoidViewFrustumCopy"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90209
  commandKey: "MoeLua|SetEnableLuaLimit30FPS"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90210
  commandKey: "MoeLua|SetBillboardInitUIOptimize"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90211
  commandKey: "fx.ParticleGTTimeBudgetInMS"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90212
  commandKey: "c.Budget.EnableMovementRenderOpt"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90213
  commandKey: "p.RagdollPhysics"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90214
  commandKey: "fx.UpdateSecondsBeforeInactive"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90215
  commandKey: "fx.VisibleCullingNoLoopingParticle"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90216
  commandKey: "fx.VisibleCullingNoRenderingParticle"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90217
  commandKey: "Slate.UIFixedUpdate"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90218
  commandKey: "Slate.MaxScrollVelocity"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90219
  commandKey: "fx.CullingInvisibleUIParticle"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90220
  commandKey: "fx.UpdateUIParticleLastRenderTime"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 10221
  commandKey: "p.PhysicsAsyncMode"
  platform: "Android"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 20221
  commandKey: "p.PhysicsAsyncMode"
  platform: "IOS"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 30221
  commandKey: "p.PhysicsAsyncMode"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 40221
  commandKey: "p.PhysicsAsyncMode"
  platform: "Harmony"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 50221
  commandKey: "p.PhysicsAsyncMode"
  platform: "CloudGame"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 60221
  commandKey: "p.PhysicsAsyncMode"
  platform: "MiniBox"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90222
  commandKey: "MoeLua|SetOptimizeStrategyGraphicQualityLevel"
  platform: "All"
  commandValue1: "1"
  commandValue2: "2"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "1"
  commandValue6: "2"
  commandValue7: "3"
  commandValue8: "4"
}
rows {
  id: 90223
  commandKey: "MoeLua|SetOptimizeStrategyFpsLevel"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 90224
  commandKey: "MoeLua|SetSignificanceEvaluateCheckLevel"
  platform: "All"
  commandValue1: "1"
  commandValue2: "2"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "1"
  commandValue6: "2"
  commandValue7: "3"
  commandValue8: "4"
}
rows {
  id: 90225
  commandKey: "MoeLua|UpdateDeviceHeatLevel"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "2"
  commandValue6: "2"
  commandValue7: "2"
  commandValue8: "2"
}
rows {
  id: 90226
  commandKey: "r.ProjectGSupportTextureLODBias"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90227
  commandKey: "MoeLua|SetTextureLODBiasSettingStartupQuality"
  platform: "All"
  commandValue1: "1"
  commandValue2: "2"
  commandValue3: "3"
  commandValue4: "4"
  commandValue5: "1"
  commandValue6: "2"
  commandValue7: "3"
  commandValue8: "4"
}
rows {
  id: 90228
  commandKey: "r.OpenGL.IgnoreLinkFailure"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90229
  commandKey: "a.Budget.EnableHighFrameRateOpt"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90230
  commandKey: "MoeLua|SetAnimBudgetParams"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90231
  commandKey: "a.Budget.EnableIdleLowFrame"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90232
  commandKey: "c.Budget.EnableHighFrameRateOpt"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90233
  commandKey: "c.EnableSimplifiedSimulateMove"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90234
  commandKey: "MoeLua|SetLowAnimBudgetParams"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90235
  commandKey: "a.ShouldBlendPhysicsBonesTickRate"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 10236
  commandKey: "t.usleepreduce"
  platform: "Android"
  commandValue1: "0.00075"
  commandValue2: "0.00075"
  commandValue3: "0.00075"
  commandValue4: "0.00075"
  commandValue5: "0.00075"
  commandValue6: "0.00075"
  commandValue7: "0.00075"
  commandValue8: "0.00075"
}
rows {
  id: 20236
  commandKey: "t.usleepreduce"
  platform: "IOS"
  commandValue1: "0.00075"
  commandValue2: "0.00075"
  commandValue3: "0.00075"
  commandValue4: "0.00075"
  commandValue5: "0.00075"
  commandValue6: "0.00075"
  commandValue7: "0.00075"
  commandValue8: "0.00075"
}
rows {
  id: 30236
  commandKey: "t.usleepreduce"
  platform: "Windows"
  commandValue1: "0.00075"
  commandValue2: "0.00075"
  commandValue3: "0.00075"
  commandValue4: "0.00075"
  commandValue5: "0.00075"
  commandValue6: "0.00075"
  commandValue7: "0.00075"
  commandValue8: "0.00075"
}
rows {
  id: 40236
  commandKey: "t.usleepreduce"
  platform: "Harmony"
  commandValue1: "0.00075"
  commandValue2: "0.00075"
  commandValue3: "0.00075"
  commandValue4: "0.00075"
  commandValue5: "0.00075"
  commandValue6: "0.00075"
  commandValue7: "0.00075"
  commandValue8: "0.00075"
}
rows {
  id: 50236
  commandKey: "t.usleepreduce"
  platform: "CloudGame"
  commandValue1: "0.00015"
  commandValue2: "0.00015"
  commandValue3: "0.00015"
  commandValue4: "0.00015"
  commandValue5: "0.00015"
  commandValue6: "0.00015"
  commandValue7: "0.00015"
  commandValue8: "0.00015"
}
rows {
  id: 60236
  commandKey: "t.usleepreduce"
  platform: "MiniBox"
  commandValue1: "0.00075"
  commandValue2: "0.00075"
  commandValue3: "0.00075"
  commandValue4: "0.00075"
  commandValue5: "0.00075"
  commandValue6: "0.00075"
  commandValue7: "0.00075"
  commandValue8: "0.00075"
}
rows {
  id: 90237
  commandKey: "g.ForceEnableObjectPool_ForDebug"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90238
  commandKey: "r.Compatible16BitSceneColor"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90239
  commandKey: "crowd.EnableVATAnimBlend"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90240
  commandKey: "r.Shadow.CacheMobileCSM"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90241
  commandKey: "r.Shadow.CacheMobileCSM.ExtraRotation"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90242
  commandKey: "r.Shadow.CacheMobileCSM.MovableSplitNum"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90243
  commandKey: "r.EnableReverseCullOpt"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90244
  commandKey: "r.Streaming.FullyLoadUsedTextures"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90245
  commandKey: "Slate.EnableInvalidateAllWidgets"
  platform: "All"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90246
  commandKey: "r.enableoffscreenparticle"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90247
  commandKey: "r.IgnoreLightMap"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90248
  commandKey: "r.IgnoreShadowMap"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90249
  commandKey: "r.DynamicMeshElements.EnableParticleInstancingBatch"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 30250
  commandKey: "r.SCSeparateTranslucency"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90251
  commandKey: "r.ManuallyEnableSOC"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90252
  commandKey: "r.SDOC.PVS"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90253
  commandKey: "r.SDOC.Async"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90254
  commandKey: "r.EnableOrthoShadow"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90255
  commandKey: "r.enablehasoffscreenparticle"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90256
  commandKey: "r.OffscreenParticleScreenPercentage"
  platform: "All"
  commandValue1: "0.7"
  commandValue2: "0.7"
  commandValue3: "0.7"
  commandValue4: "0.7"
  commandValue5: "0.7"
  commandValue6: "0.7"
  commandValue7: "0.7"
  commandValue8: "0.7"
}
rows {
  id: 10257
  commandKey: "r.ShouldResolveMSAASceneDepth"
  platform: "Android"
  commandValue1: "1"
  commandValue2: "1"
  commandValue3: "1"
  commandValue4: "1"
  commandValue5: "1"
  commandValue6: "1"
  commandValue7: "1"
  commandValue8: "1"
}
rows {
  id: 90257
  commandKey: "Moe.NameplateForceSlowPaint"
  platform: "All"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}
rows {
  id: 90258
  commandKey: "r.Shadow.EnableShadowMeshLODBias"
  platform: "Windows"
  commandValue1: "0"
  commandValue2: "0"
  commandValue3: "0"
  commandValue4: "0"
  commandValue5: "0"
  commandValue6: "0"
  commandValue7: "0"
  commandValue8: "0"
}

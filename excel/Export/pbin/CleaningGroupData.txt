com.tencent.wea.xlsRes.table_CleaningGroup
excel/xls/Q_清理条件表.xlsx sheet:组合
rows {
  id: "A1"
  desc: "在大版本更新时，清理所有30天内没玩过的玩法的Pak和缓存"
  time: "1"
  condition: "F2,30;F4,Game"
  action: "O2"
}
rows {
  id: "A2"
  desc: "在大版本更新时，清理时装"
  time: "1"
  condition: "F4,Avatar"
  action: "O5,72000|73000"
}
rows {
  id: "A3"
  desc: "在大版本更新时，清理系统缓存"
  time: "1"
  condition: "F4,System"
  action: "O2"
}
rows {
  id: "A4"
  desc: "在大版本更新时，清理SDK缓存"
  time: "1"
  condition: "F4,SDK"
  action: "O2"
}
rows {
  id: "A5"
  desc: "在大版本更新时，清理框架缓存"
  time: "1"
  condition: "F4,FrameWorkWork"
  action: "O2"
}
rows {
  id: "A6"
  desc: "在大版本更新时，清理引擎缓存"
  time: "1"
  condition: "F4,Engine"
  action: "O2"
}
rows {
  id: "A7"
  desc: "在大版本更新时，清理UGC地图缓存"
  time: "1"
  condition: "F3,Game_UGC"
  action: "O4"
}
rows {
  id: "A8"
  desc: "在从玩法退到大厅时，若当前手机空间小于15%且可清理资源>200M时，弹窗提示玩家可以清理，弹窗CD为3天 "
  time: "2"
  condition: "F7,0.15;F9,0.2"
  action: "O1,当前可清理资源较多，为了保证您的使用体验，是否前往清理部分非必要资源？/n（包括缓存资源、近30天内未游玩的玩法资源等）!,3"
}
rows {
  id: "A9"
  desc: "在从玩法退到大厅时，若当前手机空间小于5GB且可清理资源>200M时，弹窗提示玩家可以清理，弹窗CD为3天 "
  time: "2"
  condition: "F8,5;F9,0.2"
  action: "O1,当前可清理资源较多，为了保证您的使用体验，是否前往清理部分非必要资源？/n（包括缓存资源、近30天内未游玩的玩法资源等）!,3"
}
rows {
  id: "A10"
  desc: "在从玩法退到大厅时，若可清理资源>1G时，"
  time: "2"
  condition: "F9,1"
  action: "O1,当前可清理资源较多，为了保证您的使用体验，是否前往清理部分非必要资源？/n（包括缓存资源、近30天内未游玩的玩法资源等）!,3"
}
rows {
  id: "A11"
  desc: "在从玩法退到大厅时，非新手（累计活跃天数大于7天）游戏包体首次超过8G且可清理资源>200M"
  time: "2"
  condition: "F9,0.2;F10,7;F11,8"
  action: "O1,当前可清理资源较多，为了保证您的使用体验，是否前往清理部分非必要资源？/n（包括缓存资源、近30天内未游玩的玩法资源等）!,3"
}
rows {
  id: "A13"
  desc: "在刷新存储数据信息后,UGC缓存>200M， 清理UGC缓存"
  time: "100"
  condition: "F3,Game_UGC;F19,200"
  action: "O4"
}

com.tencent.wea.xlsRes.table_MatchModeSortInfo
excel/xls/W_玩法模式_排序.xlsx sheet:玩法排序
rows {
  id: 1
  idGroup: 4
  idGroup: 5
  idGroup: 6
  name: "排位赛"
  sort: 1
  modeGroupId: 1
  isWxgameShow: 1
  isWxgameSort: 1
  platform: 0
  showInNewView: true
  recIndex: 1
  recImage: "CDN:T_NewWXGame_Img_LetsGoBtn"
  miniGameSort: 1
  isRec: true
}
rows {
  id: 2
  idGroup: 7
  idGroup: 8
  idGroup: 9
  name: "休闲赛"
  sort: 2
  modeGroupId: 1
  isWxgameShow: 1
  isWxgameSort: 1
  platform: 0
}
rows {
  id: 3
  idGroup: 666
  name: "农场"
  sort: 3
  isWxgameShow: 1
  platform: 0
  isAppgameSort: 1
  isRec: true
}
rows {
  id: 4
  idGroup: 105
  idGroup: 106
  idGroup: 109
  idGroup: 151
  idGroup: 112
  name: "谁是狼人"
  sort: 4
  modeGroupId: 2
  isWxgameShow: 1
  isWxgameSort: 1
  platform: 0
  showInNewView: true
  recIndex: 3
  recImage: "CDN:T_NewWXGame_Img_NR3EBtn"
  miniGameSort: 1
  isRec: true
}
rows {
  id: 5
  idGroup: 6102
  idGroup: 6101
  name: "峡谷5v5"
  sort: 5
  WxgameDefaultId: 6102
  isWxgameSort: 1
  platform: 0
  showInNewView: true
  recIndex: 2
  recImage: "CDN:T_NewWXGame_Img_5V5Btn"
  miniGameSort: 1
  isRec: true
}
rows {
  id: 9
  idGroup: 350
  idGroup: 351
  idGroup: 352
  idGroup: 353
  name: "大王别抓我"
  sort: 6
  isWxgameShow: 1
  WxgameDefaultId: 354
  platform: 0
  showInNewView: true
  recIndex: 3
  recImage: "CDN:T_ModelSelect_Img_Type_DaWang_001"
  miniGameSort: 3
  isRec: true
}
rows {
  id: 6
  idGroup: 5600
  idGroup: 5601
  name: "峡谷3v3"
  sort: 7
  isWxgameShow: 1
  WxgameDefaultId: 5600
  platform: 0
  showInNewView: true
  recIndex: 2
  recImage: "CDN:T_ModelSelect_Img_Type_Moba3V3_001"
  miniGameSort: 7
}
rows {
  id: 7
  idGroup: 22016
  name: "UGC1"
  sort: 8
  isWxgameShow: 1
  WxgameDefaultId: 22016
  platform: 0
}
rows {
  id: 8
  idGroup: 104
  idGroup: 141
  name: "躲猫猫"
  sort: 9
  modeGroupId: 6
  isWxgameShow: 1
  platform: 0
  showInNewView: true
  recIndex: 3
  recImage: "CDN:T_ModelSelect_Img_Type_HideAndSeek_001"
  miniGameSort: 6
  isRec: true
}
rows {
  id: 10
  idGroup: 503
  idGroup: 501
  idGroup: 701
  idGroup: 703
  name: "武器大师"
  sort: 10
  modeGroupId: 5
  isWxgameShow: 1
  platform: 0
  showInNewView: true
  recIndex: 3
  recImage: "CDN:T_ModelSelect_Img_Type_WeaponMaster_001"
  miniGameSort: 4
  isRec: true
}
rows {
  id: 11
  idGroup: 380
  idGroup: 381
  idGroup: 382
  name: "大王排名"
  sort: 11
  platform: 0
  showInNewView: true
  recIndex: 3
  miniGameSort: 8
}
rows {
  id: 12
  idGroup: 604
  idGroup: 605
  idGroup: 606
  idGroup: 607
  idGroup: 608
  idGroup: 609
  name: "极速飞车"
  sort: 12
  modeGroupId: 3
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 9
  isRec: true
}
rows {
  id: 13
  idGroup: 5700
  name: "峡谷占点"
  sort: 13
  platform: 0
  showInNewView: true
  recIndex: 3
  miniGameSort: 10
}
rows {
  id: 14
  idGroup: 6006
  name: "峡谷吃鸡"
  sort: 14
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 11
}
rows {
  id: 15
  idGroup: 103
  name: "卧底行动"
  sort: 15
  modeGroupId: 9
  isWxgameShow: 1
  platform: 0
  showInNewView: true
  recIndex: 3
  miniGameSort: 12
}
rows {
  id: 16
  idGroup: 12
  idGroup: 13
  idGroup: 14
  name: "泡泡大战"
  sort: 16
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 13
}
rows {
  id: 17
  idGroup: 15
  idGroup: 16
  idGroup: 17
  name: "闪电赛"
  sort: 17
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 14
}
rows {
  id: 18
  idGroup: 669
  name: "隐形守护者"
  sort: 18
  isWxgameShow: 1
  WxgameDefaultId: 669
  platform: 0
  showInNewView: true
  recIndex: 3
  miniGameSort: 15
}
rows {
  id: 19
  idGroup: 807
  idGroup: 808
  idGroup: 809
  idGroup: 810
  name: "兽人塔防"
  sort: 19
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 16
  isRec: true
}
rows {
  id: 20
  idGroup: 401
  idGroup: 4011
  idGroup: 4012
  idGroup: 4013
  name: "大乱斗"
  sort: 20
  modeGroupId: 7
  isWxgameShow: 1
  WxgameDefaultId: 401
  platform: 0
  showInNewView: true
  recIndex: 3
  miniGameSort: 17
}
rows {
  id: 21
  idGroup: 513
  idGroup: 514
  idGroup: 515
  name: "宝藏猎人"
  sort: 21
  isWxgameShow: 1
  platform: 0
  showInNewView: true
  recIndex: 3
  miniGameSort: 18
}
rows {
  id: 22
  idGroup: 506
  name: "冲锋竞技"
  sort: 22
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 19
  isRec: true
}
rows {
  id: 23
  idGroup: 502
  name: "逃脱派对"
  sort: 23
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 20
}
rows {
  id: 24
  idGroup: 508
  idGroup: 509
  idGroup: 510
  idGroup: 708
  idGroup: 709
  idGroup: 710
  name: "突围梦幻岛"
  sort: 24
  modeGroupId: 8
  isWxgameShow: 1
  platform: 0
  showInNewView: true
  recIndex: 3
  miniGameSort: 21
}
rows {
  id: 25
  idGroup: 5300
  name: "拍击球"
  sort: 25
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 22
}
rows {
  id: 26
  idGroup: 505
  name: "变装对决"
  sort: 26
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 23
}
rows {
  id: 27
  idGroup: 520
  idGroup: 521
  idGroup: 522
  name: "夺宝奇星"
  sort: 27
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 24
}
rows {
  id: 28
  idGroup: 801
  name: "塔防大亨"
  sort: 28
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 25
}
rows {
  id: 29
  idGroup: 803
  name: "塔防大集结"
  sort: 29
  modeGroupId: 10
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 26
}
rows {
  id: 30
  idGroup: 601
  idGroup: 602
  idGroup: 603
  name: "极限竞速"
  sort: 30
  modeGroupId: 4
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 27
}
rows {
  id: 31
  idGroup: 805
  name: "塔防大乱斗"
  sort: 31
  modeGroupId: 11
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 28
}
rows {
  id: 32
  idGroup: 531
  name: "红包特攻"
  sort: 32
  platform: 1
  platform: 3
  platform: 7
  showInNewView: true
  recIndex: 3
  miniGameSort: 29
}
rows {
  id: 33
  idGroup: 5000
  name: "星世界推荐"
  sort: 33
  isWxgameShow: 1
  desc: "UGC"
  platform: 2
  platform: 4
  platform: 6
}
rows {
  id: 34
  idGroup: 21
  idGroup: 22
  idGroup: 23
  name: "闪电赛新赛季关"
  sort: 6
  platform: 0
}
rows {
  id: 35
  idGroup: 188
  name: "E8-RICH"
  sort: 30
  platform: 4
}
rows {
  id: 37
  idGroup: 22099
  name: "星世界推荐地图"
  sort: 6
  isWxgameShow: 1
  WxgameDefaultId: 22099
  desc: "UGC"
  platform: 0
  showInNewView: true
  recIndex: 3
  miniGameSort: 2
  isRec: true
}
rows {
  id: 38
  idGroup: 22100
  name: "星世界推荐地图"
  sort: 7
  isWxgameShow: 1
  WxgameDefaultId: 22100
  desc: "UGC"
  platform: 0
  showInNewView: true
  recIndex: 3
  miniGameSort: 5
  isRec: true
}

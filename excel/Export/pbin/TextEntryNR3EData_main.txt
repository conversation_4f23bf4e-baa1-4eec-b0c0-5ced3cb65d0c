com.tencent.wea.xlsRes.table_TextEntryNR3EData
excel/xls/W_文本表_文本配置_NR3E.xlsx sheet:文本配置
rows {
  id: "InLevel_NR3E_GiveUpVote"
  content: "放弃投票"
  switch: 1
}
rows {
  id: "InLevel_NR3E0_Pretender"
  content: "伪装者"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_Searcher"
  content: "搜捕者"
  switch: 1
}
rows {
  id: "InLevel_NR3E0_PretenderGameTarget"
  content: "伪装起来，躲藏到游戏结束"
  switch: 1
}
rows {
  id: "InLevel_NR3E0_SearcherGameTarget"
  content: "在时间结束前，抓到所有的伪装者"
  switch: 1
}
rows {
  id: "InLevel_NR3E0_PretenderSkillDes"
  content: "使用伪装技能，可以变成一个随机道具"
  switch: 1
}
rows {
  id: "InLevel_NR3E0_PretenderGameTips"
  content: "有危险时，可以尝试隐身进行逃脱"
  switch: 1
}
rows {
  id: "InLevel_NR3E0_PretenderGameTips1"
  content: "伪装可以重复使用，方便隐藏或逃脱"
  switch: 1
}
rows {
  id: "InLevel_NR3E0_SearcherGameTips"
  content: "注意观察，攻击可疑的物品"
  switch: 1
}
rows {
  id: "InLevel_NR3E0_UserItem_Scan"
  content: "使用扫描，搜索身边的伪装者"
  switch: 1
}
rows {
  id: "InLevel_NR3E0_UserItem_Exploration"
  content: "使用探查，会获得一个伪装者的方向"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_ResiduePretender"
  content: "剩余伪装者"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_Pertendered"
  content: "已躲藏"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_SearcherArrestCount"
  content: "我已抓捕"
  switch: 1
}
rows {
  id: "InLevel_NR3E0_Rule"
  content: "规则说明"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_SearcherTarget"
  content: "目标：在时间结束前找到所有的伪装者"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_PretenderTarget"
  content: "目标：伪装并躲藏起来，直到游戏时间结束"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_SearcherGameTips"
  content: "在时间结束前，抓到所有的伪装者。"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_PretenderGameTips"
  content: "利用伪装，躲藏起来，直到游戏时间结束。"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_PretendSkillName"
  content: "伪装"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_HideSkillName"
  content: "隐形"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_SearcherSkillName"
  content: "扫描"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_DetectSkillName"
  content: "探查"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_OnlySearcherGet"
  content: "只有搜捕者可以拾取"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_SkillCD"
  content: "技能还未准备好"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_PowerMax"
  content: "拥有能量已达上限"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_DetctTargetArrested"
  content: "探查目标被抓捕啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_GameFinishTips1"
  content: "个人生存时间越久，获得的积分也越多。\n每次使用<Blue>[伪装]</>技能，也可以获得一定积\n分。"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_GameFinishTips2"
  content: "淘汰伪装者可以获得较多积分。\n攻击场景中的一些道具，也可获得积分。"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_FindDetectItem"
  content: "新的<InLevel_Highlight28>探查道具</>出现啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_DefineBackSquare"
  content: "确认返回广场？"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_BackSquare_WinTips"
  content: "你已是本关胜利者，本关结束后会进入下关比赛，退出将失去比赛资格，确认返回广场吗？"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_Arrested"
  content: "被<InLevel_Highlight28>{0}</>抓到啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_ArrestPlayer"
  content: "抓到<InLevel_Highlight28>{0}</>啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_ThrowSkillName"
  content: "投掷"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_TarpSkillName"
  content: "陷阱"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_AidSkillName"
  content: "解救"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_ShootSkillName"
  content: "射击"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_PropSkillName"
  content: "道具"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_TrapNull"
  content: "陷阱已经用完啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_DollAllOut"
  content: "星宝全部被淘汰"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_BadGuyAllOut"
  content: "卧底全部被淘汰"
  switch: 1
}
rows {
  id: "NR3E_EndReason"
  content: "用来拼接胜利原因值"
  switch: 1
}
rows {
  id: "NR3E_EndReason_1"
  content: "狼人全部被淘汰了"
  switch: 1
}
rows {
  id: "NR3E_EndReason_2"
  content: "任务进度已达标"
  switch: 1
}
rows {
  id: "NR3E_EndReason_3"
  content: "已搜集到足够的宝物"
  switch: 1
}
rows {
  id: "NR3E_EndReason_51"
  content: "狼人取得人数优势"
  switch: 1
}
rows {
  id: "NR3E_EndReason_52"
  content: "游戏时间结束"
  switch: 1
}
rows {
  id: "NR3E_EndReason_53"
  content: "平民们未及时灭火"
  switch: 1
}
rows {
  id: "NR3E_EndReason_110"
  content: "捕捉到了4个灵魂"
  switch: 2
}
rows {
  id: "InLevel_NR3E_OutTips1"
  content: "被<White>{0}</>淘汰啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E_OutTips2"
  content: "淘汰<White>{0}</>啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E_OutTips3"
  content: "大明星被淘汰啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E_Talking"
  content: "讨论中{0}"
  switch: 1
}
rows {
  id: "InLevel_NR3E_Voting"
  content: "投票中{0}"
  switch: 1
}
rows {
  id: "InLevel_NR3E_Voted"
  content: "投票结束"
  switch: 1
}
rows {
  id: "InLevel_NR3E_IsGiveUpVoted"
  content: "是否要放弃本轮的投票呢？"
  switch: 1
}
rows {
  id: "InLevel_NR3E_TalkState"
  content: "讨论阶段"
  switch: 1
}
rows {
  id: "InLevel_NR3E_VoteState"
  content: "投票阶段"
  switch: 1
}
rows {
  id: "InLevel_NR3E_BeginSay"
  content: "从{0}号开始，顺序发言"
  switch: 1
}
rows {
  id: "InLevel_NR3E_ChooseOutDoll"
  content: "选择你希望驱逐的玩家"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleDes_Hunter"
  content: "他是一个狼人"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleDes_Host"
  content: "他是一个主持人"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleDes_Angle"
  content: "他是一个天使"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleDes_People"
  content: "他是一个平民"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleName_Hunter"
  content: "狼人"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleName_Angle"
  content: "天使"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleName_Host"
  content: "哨子"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTarget_Dancer"
  content: "使大家同时跳舞来获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTarget_Delivery"
  content: "送达7次快递可获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTarget_Delivery_HyperCore"
  content: "送达6次快递可获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleTarget_Hunter"
  content: "淘汰所有平民，同时伪装自己的身份。"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleTarget_Common"
  content: "完成任务，或淘汰掉所有的狼人"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleTarget_NaoNao"
  content: "放4次鞭炮便可获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3ESkillDes_Hunter"
  content: "隐藏在平民中的狼人，可对平民发起攻击，也可破坏设施来制造混乱。"
  switch: 1
}
rows {
  id: "InLevel_NR3ESkillDes_Host"
  content: "淘汰你的玩家会自动报告并发起会议"
  switch: 1
}
rows {
  id: "InLevel_NR3ESkillDes_Angle"
  content: "你可以选择保护一名其他玩家，使该玩家在本回合内免疫一次攻击"
  switch: 1
}
rows {
  id: "InLevel_NR3E_SliderSpeed_OneParam"
  content: "总任务进度{0}%"
  switch: 1
}
rows {
  id: "InLevel_NR3E_TaskDes"
  content: "平民们快去<InLevel_Highlight28>灭火呀！</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E_CompleteFireMission"
  content: "成功灭火啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E_CompleteFogMission"
  content: "成功驱散迷雾啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E_CompleteEatMission"
  content: "成功完成吃饭任务啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E_CampInfo"
  content: "{0}阵营:{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E_ChampionTitle"
  content: "冠军标题"
  switch: 1
}
rows {
  id: "InLevel_NR3E_ChampionDes"
  content: "冠军描述"
  switch: 1
}
rows {
  id: "InLevel_NR3E_ScoreProtect"
  content: "积分保护"
  switch: 1
}
rows {
  id: "InLevel_NR3E_RankProtect"
  content: "段位保护"
  switch: 1
}
rows {
  id: "InLevel_NR3E_FristGameRankProtect"
  content: "首局段位保护"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_Settings1"
  content: "您已被淘汰，无法进行聊天！"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_Settings2"
  content: "没有申请语音权限,请系统申请后重试"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ControlPad_SideA1"
  content: "你是狼人，你的任务是虚假的"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ControlPad_SideOther1"
  content: "保护"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ControlPad_SideOther2"
  content: "请选择一个保护的目标"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ControlPad_SideOther3"
  content: "使用次数已耗尽"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ControlPad_SideOther4"
  content: "技能还未准备好"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule1"
  content: "平民"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule2"
  content: "哨子"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule3"
  content: "天使"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule4"
  content: "狼人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule5"
  content: "目标：完成全部任务，或驱逐所有狼人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule6"
  content: "天使可以给别人施加护盾，帮他抵挡一次攻击伤害"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule7"
  content: "目标：淘汰所有的平民"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule8"
  content: "规则说明"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VoiceMicroCell"
  content: "附近"
  switch: 1
}
rows {
  id: "InLevel_NR3E31"
  content: "淘汰<InLevel_KnockedOut> {0} </><InLevel_Highlight28>{1}</>啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E32"
  content: "某位<InLevel_Highlight28>大明星</>被淘汰啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameFinish"
  content: "游戏结束"
  switch: 1
}
rows {
  id: "InLevel_Chat_NR3E3Meeting"
  content: "次会议"
  switch: 1
}
rows {
  id: "InLevel_Chat_NR3E3MeetingInput"
  content: "你的发言只有被淘汰的玩家才能看到"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting"
  content: "{0}发言中({1}秒)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting2"
  content: "号"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting3"
  content: "<ChatSysGrey>别的小队发言时，你可以与队友沟通交流</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting4"
  content: "队友"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting5"
  content: "公开"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting6"
  content: "小队频道"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting7"
  content: "已结束"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting8"
  content: "按住说话"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingStateChange"
  content: "讨论阶段"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingStateChange1"
  content: "投票阶段"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingStateChange2"
  content: "投票结束"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingStateChange3"
  content: "从{0}开始，顺序发言"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingStateChange4"
  content: "选择你希望驱逐的玩家"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ChildTask_103"
  content: "次数不足无法召开会议"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ChildTask_103_1"
  content: "紧急任务期间不可召开会议"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_1"
  content: "总任务进度"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_2"
  content: "{0}阵营-{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_TaskPanel_EmergentMeeting"
  content: "点击摇铃召开会议（剩余次数：%d）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameStart"
  content: "你可以选择保护一名其他玩家，使该玩家在本回合内免疫一次攻击"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameStart1"
  content: "秒后即将开始游戏"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameStart2"
  content: "{0}阵营:{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_JobInfo"
  content: "狼人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_JobInfo_1"
  content: "哨子"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_JobInfo_2"
  content: "天使"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_JobInfo_3"
  content: "平民"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_JobInfo_4"
  content: "隐藏在人群中的狼人，可对平民发起攻击，也可破坏设施来制造混乱。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_JobInfo_5"
  content: "当你被淘汰时，将立即进入会议。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_JobInfo_6"
  content: "你可以选择保护一名其他玩家，使该玩家在本回合内免疫一次攻击"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_JobInfo_7"
  content: "一个普通的平民，没有什么特殊的能力。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_KnockedOut"
  content: "被<InLevel_KnockedOut> {0} </><InLevel_KnockedOut1>{1}</>击倒啦!"
  switch: 1
}
rows {
  id: "InLevelNR3E2FinishName1"
  content: "最佳星宝"
  switch: 1
}
rows {
  id: "InLevelNR3E2FinishName2"
  content: "最佳卧底"
  switch: 1
}
rows {
  id: "InLevelNR3E1FinishName1"
  content: "最佳搜捕者"
  switch: 1
}
rows {
  id: "InLevelNR3E1FinishName2"
  content: "最佳伪装者"
  switch: 1
}
rows {
  id: "InLevelNR3E1FinishWin1"
  content: "搜捕者获胜"
  switch: 1
}
rows {
  id: "InLevelNR3E1FinishWin2"
  content: "伪装者获胜"
  switch: 1
}
rows {
  id: "InLevelNR3E3FinishWinCamp1"
  content: "狼人"
  switch: 1
}
rows {
  id: "InLevelNR3E3FinishWinCamp2"
  content: "平民"
  switch: 1
}
rows {
  id: "BP_NR3E3MeetingManager"
  content: "<ChatSysGrey>点击星宝可进行投票</>"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent1"
  content: "解救失败"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent2"
  content: "你被解救了！"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent3"
  content: "攻击警卫星宝，自己被淘汰"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent4"
  content: "攻击星宝，自己被淘汰"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent5"
  content: "天呐，警卫星宝被淘汰啦！"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent6"
  content: "卧底被淘汰啦！"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent7"
  content: "某个可怜的<InLevel_Highlight28>星宝</>被淘汰啦！"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent8"
  content: "被<InLevel_Highlight28>{0}</>淘汰啦！"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent9"
  content: "淘汰<InLevel_Highlight28>{0}</>啦！"
  switch: 1
}
rows {
  id: "NR3E2_AIWarningBubbleTips"
  content: "我是星宝，不要打我"
  switch: 1
}
rows {
  id: "NR3E2_AIWarningBubbleTips1"
  content: "别开枪，攻击我你会被淘汰的"
  switch: 1
}
rows {
  id: "NR3E2_AIWarningBubbleTips2"
  content: "我是好人，拿着棒子的才是卧底"
  switch: 1
}
rows {
  id: "NR3E2_AIWarningBubbleTips3"
  content: "不要瞄准我，你应该去寻找卧底"
  switch: 1
}
rows {
  id: "NR3E3_BaseComponent"
  content: "被淘汰了，还可<InLevel_Highlight28>继续完成任务</>！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1"
  content: "主院：摘莲花"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel2"
  content: "酒窖：摆放酒坛"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel3"
  content: "酒窖：寻找证物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel4"
  content: "东厢房：摘果子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel5"
  content: "茶室：验毒"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel6"
  content: "正房：砸锁"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel7"
  content: "正房：焚香"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel8"
  content: "东厢房：擦拭院墙"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel9"
  content: "东厢房：清扫落叶"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel10"
  content: "书画房：练字"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel11"
  content: "书画房：鉴别仿品"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel12"
  content: "瓷器房：擦拭瓷器"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel13"
  content: "前庭：浇花"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel14"
  content: "后花园：浇花"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel15"
  content: "凉亭：钓鱼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel16"
  content: "灶房：劈柴"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel17"
  content: "灶房：煮鱼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel18"
  content: "码头：取货"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel19"
  content: "码头：搬货"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel20"
  content: "灶房：搬货"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel21"
  content: "后花园：寻找玉佩"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel22"
  content: "棋房：整理棋盘"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel23"
  content: "棋房：对弈"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel24"
  content: "灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel25"
  content: "驱散迷雾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel26"
  content: "吃饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel27"
  content: "正厅：清除标记"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel28"
  content: "正厅：破解暗格"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel29"
  content: "前院：锁门"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel30"
  content: "训练场：固定武器架"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel31"
  content: "训练场：摆放铠甲"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel32"
  content: "河道：清理河道"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel33"
  content: "酒窖：清除老鼠"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel34"
  content: "酒窖：取酒"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel35"
  content: "侧院：隐藏的书籍"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel36"
  content: "侧院：丢失的宝石"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel37"
  content: "书房：摆放书籍"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel38"
  content: "书房：找出凶手"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel39"
  content: "占卜屋：占卜"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel40"
  content: "占卜屋：奇怪的图案"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel41"
  content: "卧室：关窗户"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel42"
  content: "卧室：归还手链"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel43"
  content: "餐厅：切水果"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel44"
  content: "餐厅：添柴"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel45"
  content: "陈列室：物品归位"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel46"
  content: "陈列室：复原画像"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel47"
  content: "大露台：开启暗门"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPane303"
  content: "驱散迷雾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPane502"
  content: "驱散迷雾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel48"
  content: "大露台：流星雨"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel401"
  content: "营地：锯木"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel402"
  content: "营地：煮菜"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel403"
  content: "门廊：拍照"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel404"
  content: "门廊：修复石像"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel405"
  content: "蘑菇园：采蘑菇"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel406"
  content: "蘑菇园：地质探测"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel407"
  content: "大瀑布：奇异的果子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel408"
  content: "大瀑布：石钥"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel409"
  content: "宝藏室：开启宝箱"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel410"
  content: "沼泽地：扫描"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel411"
  content: "生活区：采集毛发"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel412"
  content: "生活区：收取陶罐"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel413"
  content: "祭坛：清扫遗迹"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel414"
  content: "神像台：清除藤蔓"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel415"
  content: "神像台：点燃火炬桩"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel416"
  content: "山洞：采矿"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel417"
  content: "山洞：寻找笔记"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel418"
  content: "联络站：追踪线索"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel419"
  content: "联络站：采野菜"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel420"
  content: "沼泽地：空气检测"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel601"
  content: "校门：擦玻璃"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel602"
  content: "大厅：安全检查"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel603"
  content: "一班：禁止作弊"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel604"
  content: "二班：推开窗户"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel605"
  content: "一班：擦黑板"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel606"
  content: "二班：打扫卫生"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel607"
  content: "三班：答题"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel608"
  content: "三班：收试卷"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel609"
  content: "美术室：上色"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel610"
  content: "美术室：剪贴画"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel611"
  content: "实验室：混合试剂"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel612"
  content: "实验室：摆放器具"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel613"
  content: "车棚：破损的单车"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel614"
  content: "大食堂：询问"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel615"
  content: "小食堂：调查菜品"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel616"
  content: "小食堂：打饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel617"
  content: "阅览室：找书"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel618"
  content: "阅览室：整理书籍"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel619"
  content: "活动室：捡球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel620"
  content: "活动室：喝水"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel801"
  content: "发射场：太阳能板"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel802"
  content: "发射场：货物装卸"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel803"
  content: "观测站：太空观测"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel804"
  content: "观测站：设备校准"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel805"
  content: "生态舱：显微镜"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel806"
  content: "生态舱：培育作物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel807"
  content: "能源室：设备检修"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel808"
  content: "实验室：重力实验"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel809"
  content: "实验室：样本收集"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel810"
  content: "矿物舱：分拣矿石"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel811"
  content: "矿场：挖掘矿石"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel812"
  content: "医疗室：体检"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel813"
  content: "医疗室：体能训练"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel814"
  content: "餐厅：洗碗"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel815"
  content: "餐厅：点餐"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel816"
  content: "指挥室：开启设备"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel817"
  content: "卧室：查看留言"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel818"
  content: "卧室：保洁"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel819"
  content: "娱乐房：王牌狙击手"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel820"
  content: "娱乐房：星宝战机"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel821"
  content: "实验室：装卸货物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel822"
  content: "矿物舱：开启设备"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel127"
  content: "寻找药物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1001"
  content: "小广场：购买雪糕"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1002"
  content: "小广场：定制纪念品"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1003"
  content: "时光城堡：清理涂鸦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1004"
  content: "时光城堡：护照盖章"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1005"
  content: "宝藏湾：观赏"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1006"
  content: "宝藏湾：打卡"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1008"
  content: "飓风飞椅：护照盖章"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1009"
  content: "飓风飞椅：开盲盒"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1010"
  content: "幻想剧场：潜伏的凶手"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1011"
  content: "幻想剧场：修复监控"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1012"
  content: "化妆间：查找线索"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1013"
  content: "化妆间：练习化妆"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1014"
  content: "休闲乐园：喝冷饮"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1015"
  content: "休闲乐园：射击游戏"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1016"
  content: "恐龙乐园：留影"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1017"
  content: "恐龙乐园：遗失的龙蛋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1018"
  content: "恐龙乐园：归还龙蛋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1019"
  content: "展厅：制作恐龙餐"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1020"
  content: "展厅：恐龙护理"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1021"
  content: "云霄飞车(北)：检修"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1022"
  content: "云霄飞车(南)：隐患排查"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1201"
  content: "咖啡店：磨咖啡豆"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1202"
  content: "咖啡店：制作拿铁"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1203"
  content: "唱片店：播放唱片"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1204"
  content: "唱片店：挑选唱片"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1205"
  content: "潮鞋店：买鞋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1206"
  content: "潮鞋店：比对鞋印"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1207"
  content: "电玩室：窃取信息"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1208"
  content: "电玩室：泡面"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1209"
  content: "花店：修剪花枝"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1210"
  content: "苏宅：隐藏的字条"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1211"
  content: "苏宅：拍摄脚印"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1212"
  content: "超市：加热盒饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1213"
  content: "超市：购买关东煮"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1214"
  content: "水果店：苹果分级"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1215"
  content: "水果店：挑水果"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1216"
  content: "花灯店：送水果"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1217"
  content: "花灯店：花灯委托"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1218"
  content: "花店：挂花灯"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1219"
  content: "早餐铺：买早餐"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1220"
  content: "地铁站：线索追踪"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1222"
  content: "运动用品店：投篮"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1223"
  content: "运动用品店：隐藏的包"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1401"
  content: "农家乐：预定"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1402"
  content: "小卖部：冰糖葫芦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1403"
  content: "小卖部：购买糕点"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1404"
  content: "老王家：捡鸡蛋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1405"
  content: "老王家：捡鸡蛋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1406"
  content: "老王家：喂马"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1407"
  content: "大舞台：添柴"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1408"
  content: "大舞台：挂灯笼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1409"
  content: "农家乐：挂灯笼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1410"
  content: "老张家：清理冰溜子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1411"
  content: "老张家：蹦爆米花"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1412"
  content: "渔夫家：修补渔网"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1413"
  content: "渔夫家：修电视"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1414"
  content: "松树林：捡松果"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1415"
  content: "松树林：试衣"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1416"
  content: "度假区：拍照"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1417"
  content: "度假区：堆雪人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1418"
  content: "休息室：烧水"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1419"
  content: "休息室：泡茶"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1420"
  content: "温泉：泡温泉"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1421"
  content: "温泉：撒花瓣"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1601"
  content: "月亮泉：喂养小精灵"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1602"
  content: "谷口：贪玩的走地菇"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1603"
  content: "鹿林：点亮符文"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1604"
  content: "鹿林：物种采集"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1605"
  content: "鹿林：拍摄灵鹿"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1606"
  content: "洞口：采果子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1607"
  content: "月亮泉：酿灵泉"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1608"
  content: "洞口：祈祷"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1609"
  content: "晶矿洞：采矿"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1610"
  content: "晶矿洞：寻找小精灵"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1611"
  content: "南瓜屋：寻找小精灵"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1612"
  content: "洞底灵树：捕捉萤火虫"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1613"
  content: "妙蛙山：采集晨露"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1614"
  content: "妙蛙山：采集晨露"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1615"
  content: "妙蛙山：清扫石像"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1616"
  content: "南瓜屋：寄出矿石"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1617"
  content: "南瓜屋：收取物资"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1618"
  content: "精灵屋：浇灌幼苗"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1619"
  content: "精灵屋：收集花蜜"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1620"
  content: "精灵屋：送糖果"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1701"
  content: "灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1702"
  content: "驱散迷雾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1703"
  content: "吃饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel1705"
  content: "寻找药物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1"
  content: "点击摘取莲花"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes2"
  content: "拖动酒坛，将其摆放到正确的位置上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes3"
  content: "点击砸碎酒坛"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes4"
  content: "点击摘取果子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes5"
  content: "拖动银针到茶杯上方并松开，验出有毒的茶水"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes6"
  content: "多次点击锁，将锁砸开"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes7"
  content: "拖动蜡烛点燃檀香，点燃需要数秒"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes8"
  content: "拖动扫把，扫去脚印"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes9"
  content: "拖动落叶到簸箕中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes10"
  content: "拖动毛笔，画出字符"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes11"
  content: "点击两幅画中不同的地方"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes12"
  content: "拖动掸子，擦除手印"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes13"
  content: "拖动水壶到鲜花上方并松开"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes14"
  content: "拖动水壶到鲜花上方并松开"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes15"
  content: "等待鱼漂下沉时点击鱼竿"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes16"
  content: "沿虚线划动，将木柴劈开"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes17"
  content: "拖动配料到铁锅上方并松开"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes18"
  content: "点击箱子，将蓝色箱子移到出口"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes19"
  content: "点击货物进行搬运"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes20"
  content: "拖动货物，将其放入货架中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes21"
  content: "拖动掸子，拂去玉佩上的土"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes22"
  content: "点击棋盘上的棋子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes23"
  content: "点击提示位置进行下棋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes24"
  content: "点击着火点灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes25"
  content: "拖动石块盖住井口"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes26"
  content: "点击食用烤鸡"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes27"
  content: "擦掉玻璃上的标记"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes28"
  content: "点击特殊的墙砖，找出藏匿的物品"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes29"
  content: "向左拖动门闩，把门锁上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes30"
  content: "重复点击钉子，修正木架"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes31"
  content: "拾取掉落的部位，拼接到铠甲上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes32"
  content: "点击拾取河道中的木桶碎片"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes33"
  content: "点击出现的老鼠，消灭它们"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes34"
  content: "点击水龙头，等待瓶子装满"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes35"
  content: "点击杂草，找到隐藏的书"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes36"
  content: "点击杂草，找到发光的物品"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes37"
  content: "拖动书籍，将其正确的放入书架中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes38"
  content: "点击凶手的照片"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes39"
  content: "点燃所有蜡烛"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes40"
  content: "转动两层圆环，对齐图案"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes41"
  content: "点击窗户，把窗户关上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes42"
  content: "拖动手链，放入首饰盒中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes43"
  content: "沿虚线划动，切开水果"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes44"
  content: "拖动木柴到壁炉里"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes45"
  content: "对照照片，拖动物品恢复原位"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes46"
  content: "拖动碎片，正确拼接到画框中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes47"
  content: "拖动宝石，正确镶嵌在石门上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes303"
  content: "拖动瓶盖封印魔瓶"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes502"
  content: "拖动箱盖封住宝箱"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes48"
  content: "静静欣赏一会流星雨"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes401"
  content: "拖动手锯，锯断木头"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes402"
  content: "拖动食材到锅中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes403"
  content: "点击拍照按钮"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes404"
  content: "控制吊爪，吊起石像头部，放到石像身上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes405"
  content: "点击采摘蘑菇"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes406"
  content: "点击开关按钮，等待探测完成"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes407"
  content: "点击摘取果子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes408"
  content: "点击拾取石钥"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes409"
  content: "拖动石钥，嵌入到凹槽中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes410"
  content: "等待扫描完成"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes411"
  content: "拖动毛发，放入密封箱中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes412"
  content: "拖动陶罐到木箱中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes413"
  content: "拖动刷子，扫去遗迹上的苔藓"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes414"
  content: "重复点击藤蔓，将其砍掉"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes415"
  content: "拖动火把点燃火炬桩"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes416"
  content: "重复点击晶矿，开采矿石"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes417"
  content: "点击拾取笔记本"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes418"
  content: "点击拍照按钮"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes419"
  content: "拖动镰刀，割掉全部野菜"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes420"
  content: "按顺序点击开关按钮，启动仪器"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes601"
  content: "拖动报纸，擦掉玻璃上的涂鸦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes602"
  content: "点击打开消火栓"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes603"
  content: "找到{0}处用来作弊的小抄"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes604"
  content: "向左推开窗户"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes605"
  content: "擦掉黑板上的字迹"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes606"
  content: "拖动扫把，将垃圾扫入簸箕中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes607"
  content: "答对两道选择题"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes608"
  content: "点击收取桌面上的试卷"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes609"
  content: "先点击调色盘中的颜色，再点击画像进行上色"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes610"
  content: "拖动贴纸贴到画像上，每个部位都要贴一种"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes611"
  content: "拖动胶头滴管到锥形瓶上方并松开"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes612"
  content: "摆放好使用过的实验器具"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes613"
  content: "点击找出右侧单车上损坏的部位"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes614"
  content: "点击对话，完成询问"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes615"
  content: "点击三道可疑的菜名"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes616"
  content: "点击对话，完成打饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes617"
  content: "点击书本，找到{0}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes618"
  content: "拖动书籍，将其正确的放入书架中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes619"
  content: "点击拾取地上散落的球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes620"
  content: "点击开关，接取一杯水"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes621"
  content: "点击下拉电闸开启通风扇"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes801"
  content: "点击修复损坏的部分"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes802"
  content: "拖动货物到推车，完成装货"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes803"
  content: "拖动镜头，然后拍摄包含完整地球的照片"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes804"
  content: "点击按钮，将指针停在正确的位置"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes805"
  content: "点击按钮调整显微镜的清晰度"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes806"
  content: "点击培养皿添加营养液"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes807"
  content: "点击按钮检修设备"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes808"
  content: "拖动小球到任意一侧开始实验"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes809"
  content: "点击敲碎矿石，收集样本"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes810"
  content: "点击石堆，找出稀有矿石"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes811"
  content: "点击土堆，挖掘矿石"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes812"
  content: "长按屏幕完成体检"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes813"
  content: "多次点击沙袋完成训练"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes814"
  content: "拖动餐具，将其放入洗碗机中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes815"
  content: "点击选择今日午餐"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes816"
  content: "点击按钮打开所有开关"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes817"
  content: "点击选择一条留言查看"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes818"
  content: "拖动吸尘器，打扫地面的垃圾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes819"
  content: "点击射击狼人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes820"
  content: "拖动飞机，控制子弹消灭怪物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes821"
  content: "拖动货物到货架，完成卸货"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes822"
  content: "点击按钮打开所有开关"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes901"
  content: "点击着火点灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes902"
  content: "点击修复能源装置"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes903"
  content: "点击食用月饼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes905"
  content: "点击服用药物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes127"
  content: "点击服用药物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1001"
  content: "点击选择3种自己喜欢的口味"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1002"
  content: "点击挑选喜欢的图案，每种图案都需要选1种"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1003"
  content: "拖动抹布，擦掉涂鸦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1004"
  content: "等待盖章完成"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1005"
  content: "静静观赏一会"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1006"
  content: "点击拍摄按钮，进行拍照"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1008"
  content: "等待盖章完成"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1009"
  content: "点击打开盲盒，直到开出蜜蜂手办"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1010"
  content: "点击找出伪装的狼人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1011"
  content: "对接同种颜色的电线"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1012"
  content: "点击打开桌子上的笔记本"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1013"
  content: "拖动妆容到模型脸上，每种妆容都需要使用1种"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1014"
  content: "点击挑选一杯自己喜欢的冷饮"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1015"
  content: "点击投掷飞镖，射中气球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1016"
  content: "点击拍摄按钮，进行拍照"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1017"
  content: "点击拾取恐龙蛋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1018"
  content: "将恐龙蛋放入巢穴中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1019"
  content: "重复点击案板上的食材，将其切开"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1020"
  content: "拖动刷子，擦掉恐龙身上的污泥"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1021"
  content: "将螺丝拖到缺少螺丝的孔位中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1022"
  content: "点击找出1处安全隐患"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1102"
  content: "点击拉下电闸开启通风扇"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1103"
  content: "点击食用甜甜圈"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1201"
  content: "顺时针旋转把手，研磨咖啡豆"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1202"
  content: "先向杯中倒入牛奶，再向杯中倒入咖啡"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1203"
  content: "先拖动唱片到唱片机上，再拖动唱针到唱片上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes12041"
  content: "点击挑选2张粉色心情的唱片"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes12042"
  content: "点击挑选2张黑猫的唱片"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes12043"
  content: "点击挑选2张蓝色海洋的唱片"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes12044"
  content: "点击挑选2张紫火的唱片"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1205"
  content: "根据线稿点击点击购买对应的鞋子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1206"
  content: "找出与照片中鞋印相同的鞋子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1207"
  content: "插入U盘，拷贝资料"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1208"
  content: "打开热水开关"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1209"
  content: "拖动剪子，剪掉枯叶"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1210"
  content: "找到隐藏的字条"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1211"
  content: "点击拍摄按钮，拍摄脚印"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1212"
  content: "将盒饭放入微波炉中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1213"
  content: "点击挑选5串自己喜欢的关东煮"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1214"
  content: "将称量后的苹果放入对应等级的筐中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1216"
  content: "将水果篮放到桌子上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1217"
  content: "点击拾取制作好的花灯"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1218"
  content: "将花灯挂在墙上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1219"
  content: "任意挑选3份食物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1220"
  content: "拖动手电筒，找到隐藏的标记"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1222"
  content: "滑条移动到绿色区域时点击屏幕，进行投球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1223"
  content: "点击拾取手提包"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1301"
  content: "点击着火点灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1302"
  content: "点击拉下电闸开启通风扇"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1303"
  content: "点击食用水果"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1305"
  content: "点击服用药物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1401"
  content: "选择人数、菜品和时间后，点击预定"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1402"
  content: "任意挑选一串冰糖葫芦进行品尝"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1403"
  content: "点击购买任意8块糕点"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1404"
  content: "将地上的鸡蛋拖动到篮子里"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1405"
  content: "将地上的鸡蛋拖动到篮子里"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1406"
  content: "将草料放入槽子中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1407"
  content: "向篝火中放入两根木柴"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1408"
  content: "将灯笼挂到杆子上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1409"
  content: "将灯笼挂到门上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1410"
  content: "点击敲掉屋檐上的冰溜子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1411"
  content: "点击掰动铁壶上的铁栓"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1412"
  content: "点击渔网上的窟窿"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1413"
  content: "点击拍打电视，直到电视画面正常"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1414"
  content: "点击拾取地上的松果"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1415"
  content: "将每类衣物都穿一件到身上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1416"
  content: "拍摄冰雕"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1417"
  content: "装扮雪人，每类装饰物都需要使用1个"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1418"
  content: "将水壶放到炉子上，等水烧开"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1419"
  content: "将水壶中的水倒入茶缸中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1420"
  content: "泡一会温泉"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1421"
  content: "拖动篮子中的花瓣，将其都撒入温泉中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1502"
  content: "将供奉的食物放入盘子中，驱散迷雾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1503"
  content: "点击食用铁锅炖"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1601"
  content: "拖动小精灵，将其全部放入灵泉内滋养"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1602"
  content: "点击蘑菇，找出全部的走地菇精灵"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1603"
  content: "长按符文直到符文完全亮起"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1604"
  content: "等待扫描完成"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1605"
  content: "点击拍摄按钮，拍摄灵鹿"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1606"
  content: "点击拾取地上的果子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1607"
  content: "拖动果子，将其撒入灵泉中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1608"
  content: "虔诚地祈祷一会"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1609"
  content: "连续点击，敲碎矿石"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1610"
  content: "提起小精灵，将其放到竹篓里"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1611"
  content: "提起小精灵，将其放到竹篓里"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1612"
  content: "点击飞舞的萤火虫，进行捕捉"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1613"
  content: "点击叶子，让露水滑落"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1614"
  content: "点击叶子，让露水滑落"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1615"
  content: "拖动刷子，擦掉石像上的污泥"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1616"
  content: "拖动矿石，将其全部放入胖鸟的背包中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1617"
  content: "拖动胖鸟身上的背包，将其放到旁边的地上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1618"
  content: "拖动装有晨露的瓶子到幼苗上方，浇灌幼苗"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1619"
  content: "将瓶子放到花朵下，接取花蜜"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1620"
  content: "将糖果篮挂到门柱的挂钩上"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1701"
  content: "点击着火点灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1702"
  content: "将灵泉倒在树根上，唤醒灵树，驱散迷雾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1703"
  content: "点击食用水果"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes1705"
  content: "点击服用药物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName1"
  content: "正房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName2"
  content: "灶房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName3"
  content: "棋房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName4"
  content: "前庭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName5"
  content: "瓷器房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName6"
  content: "酒窖"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName7"
  content: "后花园"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName8"
  content: "东厢房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName101"
  content: "陈列室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName102"
  content: "书房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName103"
  content: "酒窖"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName104"
  content: "餐厅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName105"
  content: "前院"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName106"
  content: "占卜屋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName107"
  content: "训练场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName108"
  content: "卧室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName109"
  content: "卧室1"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName110"
  content: "卧室2"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName201"
  content: "联络站"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName202"
  content: "蘑菇园"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName203"
  content: "祭坛"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName204"
  content: "沼泽地"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName205"
  content: "营地"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName206"
  content: "大瀑布"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName207"
  content: "山洞"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName208"
  content: "门廊"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName209"
  content: "卧室1"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName210"
  content: "卧室2"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName301"
  content: "校史馆"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName302"
  content: "二班"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName303"
  content: "活动室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName304"
  content: "实验室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName305"
  content: "小食堂"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName306"
  content: "一班"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName307"
  content: "美术室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName308"
  content: "阅览室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName401"
  content: "指挥室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName402"
  content: "矿场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName403"
  content: "实验室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName404"
  content: "餐厅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName405"
  content: "发射场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName406"
  content: "能源室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName407"
  content: "娱乐房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName408"
  content: "矿物舱"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName501"
  content: "恐龙乐园"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName502"
  content: "小广场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName503"
  content: "飓风飞椅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName504"
  content: "宝藏湾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName505"
  content: "云霄飞车(北)"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName506"
  content: "幻想剧场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName507"
  content: "云霄飞车(南)"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName508"
  content: "休闲乐园"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName601"
  content: "潮鞋店"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName602"
  content: "水果店"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName603"
  content: "超市"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName604"
  content: "电玩室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName605"
  content: "运动用品店"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName606"
  content: "早餐铺"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName607"
  content: "小广场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName608"
  content: "地铁站"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName801"
  content: "温泉"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName802"
  content: "村口"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName803"
  content: "老王家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName804"
  content: "松树林"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName805"
  content: "渔夫家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName806"
  content: "休息室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName807"
  content: "度假村"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName808"
  content: "大舞台"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName901"
  content: "鹿林"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName902"
  content: "精灵屋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName903"
  content: "洞底灵树"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName904"
  content: "南瓜屋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName905"
  content: "谷口"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName906"
  content: "洞口"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName907"
  content: "月亮泉"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName908"
  content: "妙蛙山"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName9901"
  content: "祭坛"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName9902"
  content: "营地"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName9903"
  content: "沙滩"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName9904"
  content: "回廊"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName9905"
  content: "鸟巢"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName9906"
  content: "码头"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName9907"
  content: "篝火台"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleName9908"
  content: "勘测区"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName1"
  content: "茶室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName2"
  content: "瓷器房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName3"
  content: "东厢房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName4"
  content: "后花园"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName5"
  content: "酒窖"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName6"
  content: "凉亭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName7"
  content: "码头"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName8"
  content: "前庭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9"
  content: "棋房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName10"
  content: "书画房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName11"
  content: "灶房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName12"
  content: "正房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName13"
  content: "主院"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_1_3_1_Report3"
  content: "发现有人倒地就立即报告了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_1_5_1_Report3"
  content: "我怀疑3号是狼"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_1_6_1_Report3"
  content: "感觉是3号"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_1_8_1_Report3"
  content: "投3号"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_1_4_1_Report4"
  content: "1号当着我的面，刀了6号"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_1_5_1_Report4"
  content: "4号说谎"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_1_7_1_Report4"
  content: "我信5号，我也投4号"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_1_8_1_Report4"
  content: "我没线索"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_2_2_1_Report1"
  content: "怎么开会了？"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_2_3_1_Report1"
  content: "怎么开会了？"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_2_4_1_Report1"
  content: "没信息就弃票吧"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_2_5_1_Report1"
  content: "没信息就弃票吧"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_2_6_1_Report1"
  content: "有信息我可以开紧急会议"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_2_7_1_Report1"
  content: "我弃票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_2_8_1_Report1"
  content: "我弃票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_2_4_1_Report1"
  content: "弃票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_2_5_1_Report1"
  content: "弃票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_2_6_1_Report1"
  content: "快点"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_2_7_1_Report1"
  content: "快点投票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_2_8_1_Report1"
  content: "我要继续做任务，快点投票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_2_1_Report2"
  content: "没看到其他人"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_3_1_Report2"
  content: "没看到其他人"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_4_1_Report2"
  content: "人倒在哪里啊？"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_5_1_Report2"
  content: "人倒在哪里啊？"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_6_1_Report2"
  content: "{0}号发言不像好人"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_7_1_Report2"
  content: "我是平民，一直在做任务"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_8_1_Report2"
  content: "我是平民，一直在做任务"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_2_1_Report2"
  content: "真不是我"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_3_1_Report2"
  content: "真不是我"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_4_1_Report2"
  content: "投{0}号吧"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_5_1_Report2"
  content: "投{0}号吧"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_7_1_Report2"
  content: "我是平民"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_8_1_Report2"
  content: "我是平民"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_2_1_Report2"
  content: "抱团了一会，后来走散了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_3_1_Report2"
  content: "抱团了一会，后来走散了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_4_1_Report2"
  content: "我看到你俩抱团了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_5_1_Report2"
  content: "我看到你俩抱团了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_6_1_Report2"
  content: "肯定投{0}号呀"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_7_1_Report2"
  content: "{0}号说漏嘴了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_8_1_Report2"
  content: "{0}号说漏嘴了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_2_1_Report3"
  content: "没看到其他人"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_3_1_Report3"
  content: "没看到其他人"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_4_1_Report3"
  content: "人倒在哪里啊？"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_5_1_Report3"
  content: "人倒在哪里啊？"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_6_1_Report3"
  content: "{0}号发言不像好人"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_7_1_Report3"
  content: "我是平民，一直在做任务"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_8_1_Report3"
  content: "我是平民，一直在做任务"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_2_1_Report3"
  content: "真不是我"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_3_1_Report3"
  content: "真不是我"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_4_1_Report3"
  content: "投{0}号吧"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_5_1_Report3"
  content: "投{0}号吧"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_7_1_Report3"
  content: "我是平民"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_8_1_Report3"
  content: "我是平民"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_2_1_Report3"
  content: "抱团了一会，后来走散了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_3_1_Report3"
  content: "抱团了一会，后来走散了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_4_1_Report3"
  content: "我看到你俩抱团了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_5_1_Report3"
  content: "我看到你俩抱团了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_6_1_Report3"
  content: "肯定投{0}号呀"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_7_1_Report3"
  content: "{0}号说漏嘴了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_8_1_Report3"
  content: "{0}号说漏嘴了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_2_1_Report4"
  content: "{0}号说漏嘴了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_3_1_Report4"
  content: "{0}号说漏嘴了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_4_1_Report4"
  content: "抱团了一会，后来走散了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_5_1_Report4"
  content: "抱团了一会，后来走散了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_6_1_Report4"
  content: "我看到你俩抱团了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_7_1_Report4"
  content: "肯定投{0}号呀"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_8_1_Report4"
  content: "肯定投{0}号呀"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_2_1_Report5"
  content: "{0}号说漏嘴了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_3_1_Report5"
  content: "{0}号说漏嘴了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_4_1_Report5"
  content: "抱团了一会，后来走散了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_5_1_Report5"
  content: "抱团了一会，后来走散了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_6_1_Report5"
  content: "我看到你俩抱团了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_7_1_Report5"
  content: "肯定投{0}号呀"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SecondReport_Taking_2_8_1_Report5"
  content: "肯定投{0}号呀"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_2_1_Report1"
  content: "怎么开会了？"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_3_1_Report1"
  content: "怎么开会了？"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_4_1_Report1"
  content: "没信息就弃票吧"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_5_1_Report1"
  content: "没信息就弃票吧"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_6_1_Report1"
  content: "有信息我可以开紧急会议"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_7_1_Report1"
  content: "我弃票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_2_8_1_Report1"
  content: "我弃票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_4_1_Report1"
  content: "弃票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_5_1_Report1"
  content: "弃票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_6_1_Report1"
  content: "快点"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_7_1_Report1"
  content: "快点投票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_2_8_1_Report1"
  content: "快点投票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_3_2_1_Report1"
  content: "3号面刀，1号也看到了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_3_3_1_Report1"
  content: "明明是你刀的"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_3_6_1_Report1"
  content: "2号比较可信"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_3_7_1_Report1"
  content: "3号想贴我"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_3_8_1_Report1"
  content: "没警长，刀人的肯定是狼"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_3_2_1_Report1"
  content: "投票投3号"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_3_8_1_Report1"
  content: "出3号，他是狼人"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_3_7_1_Report1"
  content: "我是平民，别出我"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_3_2_1_Report2"
  content: "3号面刀，1号也看到了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_3_3_1_Report2"
  content: "明明是你刀的"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_3_6_1_Report2"
  content: "2号比较可信"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_3_7_1_Report2"
  content: "3号想贴我"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Taking_3_8_1_Report2"
  content: "没警长，刀人的肯定是狼"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_3_2_1_Report2"
  content: "投票投3号"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_3_8_1_Report2"
  content: "出3号，他是狼人"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_FirstReport_Voting_3_7_1_Report2"
  content: "我是平民，别出我"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_3_2_1_Report1"
  content: "怎么开会了？"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_3_3_1_Report1"
  content: "没信息就弃票吧"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_3_6_1_Report1"
  content: "有信息我可以开紧急会议"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_3_7_1_Report1"
  content: "多做任务可以赢"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_3_3_1_Report1"
  content: "弃票弃票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_3_6_1_Report1"
  content: "快点"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_3_7_1_Report1"
  content: "我要继续做任务，快点投票"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_3_2_1_Report99"
  content: "6号是狼，我看到他钻密道了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_3_6_1_Report99"
  content: "你看错了，我刚才在做任务呢"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_3_7_1_Report99"
  content: "我也看到6号钻密道了"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Taking_3_8_1_Report99"
  content: "两个人都看到了，那就投6号吧"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_3_2_1_Report99"
  content: "看他追我，所以我一直跑"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_3_7_1_Report99"
  content: "投6号"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_First_UrgentMeeting_Voting_3_8_1_Report99"
  content: "大家一起投6号"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_1"
  content: "多做任务可以赢"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_2"
  content: "我是好人，不乱带节奏"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_3"
  content: "没什么信息"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_4"
  content: "我自己单走"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_5"
  content: "我铁好人"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_6"
  content: "来个人归票吧"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_7"
  content: "什么也不知道"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_8"
  content: "没什么线索"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_9"
  content: "没什么信息"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_10"
  content: "有没有抱团的"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_11"
  content: "没发现狼"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_12"
  content: "我做了摘果子任务，其他不知道"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_13"
  content: "我在下棋"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_14"
  content: "在浇花"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_15"
  content: "刚做完瓷器任务"
  switch: 1
}
rows {
  id: "NR3E3_AIProxyMessage_Talking_16"
  content: "我刚刚在劈柴"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Meeting1"
  content: "会议开始"
  switch: 1
}
rows {
  id: "NR3E3_NoVoiceAuthorit"
  content: "无法使用语音功能，请先打开麦克风权限"
  switch: 1
}
rows {
  id: "NR3E3_PermissionPrompt"
  content: "权限提示"
  switch: 1
}
rows {
  id: "NR3E3_PermissionConfirm"
  content: "去设置"
  switch: 1
}
rows {
  id: "NR3E3Meeting_Discuss"
  content: "%s<ChatSysWhite>%s</><ChatSysGrey>开始发言</>"
  switch: 1
}
rows {
  id: "NR3E3Meeting_DeathTip"
  content: "你已出局，还可以和其他出局玩家继续交流"
  switch: 1
}
rows {
  id: "InLevel_NR3E2_VoiceMicroCell"
  content: "附近"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent10"
  content: "天呐，警卫星宝被攻击啦！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTips"
  content: "<InLevel_UrgentTips1>狼人们可以打开地图，选择发起一个</><InLevel_UrgentTips>紧急任务</><InLevel_UrgentTips1>了。</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTips1"
  content: "吃药"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTips2"
  content: "服用药物中"
  switch: 1
}
rows {
  id: "NR3E_EndReason_101_Tips"
  content: "<YellowCurrencytTip>{0}</>淘汰了{1}名悬赏目标"
  switch: 1
}
rows {
  id: "NR3E_EndReason_102_Tips"
  content: "<YellowCurrencytTip>{0}</>猜对了{1}名玩家的身份"
  switch: 1
}
rows {
  id: "NR3E_EndReason_104_Tips"
  content: "<YellowCurrencytTip>{0}</>标记了全部玩家"
  switch: 1
}
rows {
  id: "NR3E_EndReason_101"
  content: "赏金猎人淘汰了{0}名悬赏目标"
  switch: 1
}
rows {
  id: "NR3E_EndReason_102"
  content: "赌徒猜对了{0}名玩家的身份"
  switch: 1
}
rows {
  id: "NR3E_EndReason_103"
  content: "小丑被投票出局"
  switch: 1
}
rows {
  id: "NR3E_EndReason_104"
  content: "臭鼬标记了全部玩家"
  switch: 1
}
rows {
  id: "NR3E_EndReasonType1"
  content: "{0}获胜"
  switch: 1
}
rows {
  id: "NR3E_EndReasonType2"
  content: "平民阵营"
  switch: 1
}
rows {
  id: "NR3E_EndReasonType3"
  content: "狼人阵营"
  switch: 1
}
rows {
  id: "NR3E_EndReasonType4"
  content: "啦"
  switch: 1
}
rows {
  id: "NR3E_TransferBomb"
  content: "有炸弹，快传给其他玩家！"
  switch: 1
}
rows {
  id: "NR3E_TransferBomb_IconText"
  content: "传炸弹"
  switch: 1
}
rows {
  id: "NR3E_PlacedBomb_IconText"
  content: "炸弹"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule9"
  content: "打开地图查看任务地点"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule10"
  content: "使用设施，完成相应任务"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule11"
  content: "发现并报告尸体，开始会议讨论"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule12"
  content: "参与讨论，找到并驱逐狼人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule13"
  content: "身份介绍"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule14"
  content: "平民阵营"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule15"
  content: "警长"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule16"
  content: "天使"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule17"
  content: "侦探"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule18"
  content: "哨子"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule19"
  content: "警长可以攻击其他玩家。如果这个玩家是平民阵营的，则该玩家存活，自己被淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule20"
  content: "天使可以选择保护一名玩家，使他在本回合内免疫一次攻击。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule21"
  content: "侦探可以调查一名玩家，从而知道他的阵营。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule22"
  content: "淘汰哨子的玩家会自动报告并发起会议。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule23"
  content: "狼人阵营"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule24"
  content: "消防员可以使用密道，也可以立刻完成紧急任务。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule25"
  content: "伪装狼可以随机变身成其他玩家，持续一段时间。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule26"
  content: "刺客狼可以在会议中猜测一名玩家的身份，如果猜对，则淘汰掉该玩家，如果猜错，则自己被淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule27"
  content: "在行动和会议中隐藏自己的身份"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule28"
  content: "攻击平民，就可以淘汰掉他"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule29"
  content: "使用密道，可以藏身或快速通行"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule30"
  content: "消防员"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule31"
  content: "伪装狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule32"
  content: "刺客狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule33"
  content: "小心隐藏在人群中的狼人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule34"
  content: "投票时，主持人的投票被计为两票。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule35"
  content: "学者完成任务时，可以使总任务进度增加的更多。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule36"
  content: "主持人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule37"
  content: "学者"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Bell"
  content: "摇动铃铛，发起一次紧急会议"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskTips1"
  content: "紧急任务"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskTips2"
  content: "狼人们可以在游戏内发起紧急任务，给平民制造各类麻烦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem1"
  content: "点起大火，平民如果没有在<LobbyChatYellow>限时内完成灭火</>，则狼人直接获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem2"
  content: "所有平民将陷入迷雾，<LobbyChatYellow>视野受到影响</>。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem3"
  content: "放火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem4"
  content: "迷雾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem5"
  content: "开饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem6"
  content: "所有平民<LobbyChatYellow>吃饭</>补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem11"
  content: "生病"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem12"
  content: "所有平民将生病，需要在<LobbyChatYellow>限时内寻找各自的药物</>，否则将直接淘汰。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_VocationItemTip"
  content: "我的身份"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft1"
  content: "投票"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft2"
  content: "取消"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft3"
  content: "本次会议已结束"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft4"
  content: "每次会议仅可{0}一次"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft5"
  content: "{0}次数已用尽"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft6"
  content: "选择正确则淘汰该玩家，选择错误则使自己被淘汰。\n本局你还可以进行<GuideHighlight21>{0}</>次{1}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft7"
  content: "请选择该玩家的职业，进行{0}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft8"
  content: "暗算"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft9"
  content: "选择玩家，可以进行{0}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft10"
  content: "你被{0}了"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft11"
  content: "{0}失败"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft12"
  content: "猜错了，{0}号不是{1}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft13"
  content: "猜对了，{0}号是{1}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft14"
  content: "选择玩家，可以进行猜测"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft15"
  content: "请选择该玩家的身份，进行猜测"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft16"
  content: "猜测"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft17"
  content: "不能猜测自己的身份"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft18"
  content: "你已猜对该玩家的身份"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft19"
  content: "你已猜错，请下次会议再猜测"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft20"
  content: "每次会议仅可猜测两次"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft21"
  content: "请选择该玩家的身份，进行封印"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft22"
  content: "封印"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft23"
  content: "选择正确则封印该玩家，使其本局内不能使用身份技能与投票\n本局你还可以进行<GuideHighlight21>{0}</>次封印"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft24"
  content: "你被秘术狼封印啦！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft25"
  content: "你被封印了，无法投票"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft26"
  content: "你被封印了，无法使用技能"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft27"
  content: "你已封印失败，请下次会议再封印"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft28"
  content: "你已封印该玩家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft29"
  content: "封印次数已用尽"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft30"
  content: "本局无法使用身份技能与投票了"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft31"
  content: "封印成功"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft32"
  content: "封印失败，{0}号不是{1}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft33"
  content: "每次会议仅可封印一次"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft34"
  content: "判决"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft35"
  content: "该玩家已经被淘汰啦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft36"
  content: "您已经被淘汰啦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft37"
  content: "每次会议仅可猜测一次"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft38"
  content: "仅可猜测指定角色身份"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft39"
  content: "查验"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft40"
  content: "本次会议已结束"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft41"
  content: "本次会议只能查验一次"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft42"
  content: "无法查验队友"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft43"
  content: "{0}不是狼人！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft44"
  content: "{0}是狼人！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft45"
  content: "查验次数已耗尽"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft46"
  content: "确认"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft47"
  content: "确定要查验<Yellow32>{0}号</>吗？"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft48"
  content: "查验后，你将知道TA是否是狼人阵营"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft49"
  content: "本局游戏，你还可以查验：<Yellow21>{0}次</>。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft50"
  content: "你已查验该玩家的身份"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_VocationTitle"
  content: "本局身份"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SheriffAttackDoll"
  content: "你攻击了一名平民"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task1"
  content: "紧急任务：灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task2"
  content: "紧急任务：驱散迷雾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task3"
  content: "平民们需要在限时内进行灭火，否则，狼人将直接获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task4"
  content: "迷雾将影响平民的视野，驱散迷雾后恢复正常。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task5"
  content: "紧急任务：吃饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task6"
  content: "平民需要吃饭补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task11"
  content: "紧急任务：寻找药物"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task12"
  content: "需要在限时内找到药物，否则将立即被淘汰（狼人阵营不受生病影响）。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_DetectTargetDisappear"
  content: "目标消失，调查失败"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_DetectTargetTooFarAway"
  content: "距离过远，调查失败"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_DetectPleaseComeCloser"
  content: "距离过远，请靠近"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_DetectResult"
  content: "{0}号是{1}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_DetectNotTarget"
  content: "缺少调查目标"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_UndercoverTips"
  content: "卧底时刻：卧底可感知星宝位置"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_UndercoverMoment1"
  content: "卧底时刻"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_UndercoverMoment"
  content: "卧底可以感知星宝们的位置了！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_GameStartTip"
  content: "确认身份({0})"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_GameStartTip1"
  content: "玩家确认身份中({0})"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Bell1"
  content: "其他星宝正在使用铃铛，请稍候"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTask1"
  content: "当前不可发起紧急任务"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgetnTask2"
  content: "生病了，要按时吃药呀！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotAttackTarget"
  content: "缺少攻击目标"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Detect"
  content: "调查"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation1"
  content: "你可以攻击其他玩家，小心误伤平民"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation2"
  content: "你可以调查一名玩家，从而知道他的阵营"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation3"
  content: "你可以立刻完成紧急任务和使用密道"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation4"
  content: "你可以随机变身成一名其他玩家"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation5"
  content: "你可以在会议中猜测身份来淘汰别人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation6"
  content: "你可以在游戏中保护其他玩家"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation7"
  content: "你完成的任务可以增加更多的进度"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation8"
  content: "投票时，你的投票被计为两票"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_NR3ESkillDes1"
  content: "平民阵营的常规身份，没有特殊能力。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_NR3ESkillDes2"
  content: "狼人阵营的常规身份，没有特殊能力。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingSysTip1"
  content: "%s<ChatSysWhite>%s</><ChatSysGrey>已投票</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingSysTip2"
  content: "<ChatSysOrange>第%d次会议</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingSysTip3"
  content: "<ChatSysGrey>本轮弃票玩家：</><ChatSysRed>%d位</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingSysTip4"
  content: "轮到你发言啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingSysTip5"
  content: "弃票数最多，没有人被驱逐"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingSysTip6"
  content: "得票数相同，没有人被驱逐"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingSysTip7"
  content: "有{0}位玩家弃票"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent11"
  content: "解救失败"
  switch: 1
}
rows {
  id: "NR3E2_DollsRunComponent12"
  content: "你成功解救了<White>{0}</>！"
  switch: 1
}
rows {
  id: "NR3E1_SkillName"
  content: "透视"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MarkNotTarget"
  content: "缺少标记目标"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_WarriorTip"
  content: "获得<img id=\"AttackIcon\"/><InLevel_Highlight28>武器，</>可以进行1次攻击"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule38"
  content: "勇士"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule39"
  content: "炸弹狼可以将炸弹放在其他玩家身上，炸弹到时会自动爆炸并淘汰携带者；炸弹在爆炸前可以在玩家间互相传递。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule40"
  content: "臭鼬可以用臭味标记其他玩家，如果在一个回合内，所有存活的玩家都被标记，则臭鼬获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule41"
  content: "会议期间，赌徒可以猜测一名玩家的身份，如果累计猜对3名，则赌徒获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule42"
  content: "每个回合赏金猎人都有一个赏金目标，如果赏金猎人亲手攻击并淘汰3名目标，则获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule43"
  content: "如果小丑在会议期间被投票驱逐，则小丑获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule69"
  content: "密探需要在会议中猜测2名指定的玩家身份，如果猜测成功，则获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation9"
  content: "你可以将一枚炸弹放在其他玩家身上"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation10"
  content: "用臭味标记其他玩家可获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation11"
  content: "会议中猜对3名玩家身份可获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation12"
  content: "淘汰3名赏金目标可获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation13"
  content: "在会议中被投票驱逐可获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation14"
  content: "当你完成3个任务后，将获得攻击能力"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation15"
  content: "勇士完成3个常规任务后，将获得武器，拥有攻击能力。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_IdentityInformation16"
  content: "会议中猜对2名玩家身份可获得胜利"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule1_GameInfo"
  content: "一场趣味的躲猫猫，一方伪装躲藏，另一方努力抓捕。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule1_HideWinInfo"
  content: "任意一个伪装者存活到游戏时间结束，便可获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule1_HideSkillInfo1"
  content: "变身成场景中的一个随机道具，可反复使用。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule1_HideSkillInfo2"
  content: "隐形一小段时间，关键时刻逃命必备。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule1_HideSkillInfo3"
  content: "可以透过墙壁等障碍，观察到其他玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule1_SeekWinInfo"
  content: "在游戏时间结束前，抓到全部的伪装者，便可获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule1_SeekSkillInfo1"
  content: "攻击任何可疑的道具，抓住那些伪装者。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule1_SeekSkillInfo2"
  content: "扫描自己周围，如果有伪装者，则会发出警报。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule1_SeekSkillInfo3"
  content: "拾取场景中的探查道具后，便可以使用该技能。\n探查后，可以侦测某个伪装者的方位。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule2_GameInfo"
  content: "星宝与卧底之间的激烈对抗！努力的存活，并淘汰敌人吧。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule2_HideWinInfo"
  content: "淘汰隐藏中的卧底，或任意一个星宝存活到最后，都可获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule2_HideSkillInfo1"
  content: "警卫星宝可以进行射击，攻击卧底，保护其他星宝。\n如果攻击了星宝，则警卫星宝自己也会被淘汰！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule2_HideSkillInfo2"
  content: "警卫星宝被淘汰后，手枪会掉落在地上。\n星宝拾取手枪后，可以成为新的警卫星宝。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule2_HideSkillInfo3"
  content: "拾取星星，可以增加个人积分，同时减少游戏时间。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule2_SeekWinInfo"
  content: "在游戏时间结束前，淘汰所有星宝，便可获胜。\n适当的隐藏身份，可能有利于获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule2_SeekSkillInfo1"
  content: "使用近身挥棒或远程投掷来攻击星宝。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule2_SeekSkillInfo2"
  content: "使用陷阱可以困住星宝一小段时间。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule2_SeekSkillInfo3"
  content: "最后90秒时，将进入卧底时刻，卧底们可以感知星宝们的位置。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_LoseOut"
  content: "你已被淘汰"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule44"
  content: "赌徒"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule45"
  content: "赏金猎人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule46"
  content: "小丑"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule47"
  content: "主持人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule48"
  content: "学者"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule49"
  content: "臭鼬"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule50"
  content: "炸弹狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule51"
  content: "人偶师"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule52"
  content: "搬运工"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule53"
  content: "大力狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule54"
  content: "魔爆狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule55"
  content: "独行侠"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule56"
  content: "铁蛋"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule57"
  content: "流浪汉"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule58"
  content: "秘术狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule59"
  content: "长老"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule60"
  content: "法医"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_JobInfo_Zoli"
  content: "中立"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotTarget"
  content: "缺少目标"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_FakerTaskTip"
  content: "你的任务是虚假的"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_BombFire"
  content: "炸弹爆炸啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_BountyTarget_Changed"
  content: "目标已淘汰，出现新目标"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_NeutralCamp_CompleteMissionFailed"
  content: "你是中立，你的任务是虚假的"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_NeutralCamp_DeathNotify"
  content: "被淘汰了！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_GameInfo"
  content: "平民与狼人间的烧脑对决！\n危险与谎言遍布，平民们能否找出隐藏在人群中的狼人呢？"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_GameTypeInfo1"
  content: "当前模式为8人模式，游戏内有6名平民，2名狼人。\n部分玩家拥有特殊的身份。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_GameTypeInfo2"
  content: "当前模式为10人模式，有6名平民，2名狼人，2名中立阵营。\n每个玩家都有随机的身份。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_Target_Normal"
  content: "驱逐、淘汰所有狼人，或累积任务进度至100%，均可获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_Target_Killer"
  content: "攻击、投票驱逐平民阵营的玩家，达到人数优势，便可获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_Target_Neutral"
  content: "中立阵营的玩家各自为战，根据身份不同，胜利的条件也不同。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_Normal_Task"
  content: "平民玩家都可以进行普通任务，每个任务需要完成相应的操作。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_Urgency_Task"
  content: "狼人可以发布各种类型的紧急任务，给平民们制造麻烦。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_Report"
  content: "发现尸体后，可以进行报告，从而开始一场会议讨论。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_Meeting"
  content: "在会议中，大家可以分享信息，投票驱逐可疑的玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_SoulState"
  content: "被淘汰的玩家会变成观战状态，在游戏中观战，或进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_Bell"
  content: "使用铃铛，可以立刻召集大家，开始一次紧急会议。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_Broadcas"
  content: "使用广播，可以向所有人发送语音，传递消息。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_Radar"
  content: "使用水晶球，可以查看地图中每个区域的玩家分布。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_HiddenPath"
  content: "狼人可以使用密道，在密道间快速通行，或藏身观察。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo1"
  content: "平民阵营的常规身份，可以进行任务、报告尸体，没有特殊能力。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo2"
  content: "警长可以攻击其他玩家。如果这个玩家是平民阵营的，则该玩家存活，警长自己被淘汰。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo3"
  content: "天使可以保护一名玩家，使他在本回合内免疫一次攻击。\n这个能力每个回合只能使用1次。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo4"
  content: "侦探可以调查一名玩家，从而知道他的阵营。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo5"
  content: "淘汰哨子的玩家会自动报告并发起会议。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo6"
  content: "投票时，主持人的投票被计为两票。\t"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo7"
  content: "消防员可以使用密道，也可以立刻完成紧急任务。\n该能力每局游戏只能使用1次。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo8"
  content: "勇士完成3个常规任务后，将获得武器，拥有攻击能力。\n该能力每局游戏只能使用1次。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo9"
  content: "学者完成任务时，可以使总任务进度增加的更多。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo10"
  content: "狼人阵营的常规身份，可以攻击平民、使用密道，没有其他特殊能力。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo11"
  content: "伪装狼可以随机变身成一名其他玩家，持续一段时间。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo12"
  content: "炸弹狼可以将一枚炸弹放在其他玩家身上，炸弹到时会自动爆炸并淘汰携带者。\n炸弹在爆炸前可以在玩家间互相传递。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo13"
  content: "刺客狼可以在会议中猜测一名玩家的身份，如果猜对，则暗算掉该玩家，如果猜错，则自己被淘汰。\n这个能力每局游戏只能使用2次"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo14"
  content: "会议期间，赌徒可以猜测一名玩家的身份，如果累计猜对3名，则赌徒获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo15"
  content: "每个回合赏金猎人都有一个赏金目标，如果赏金猎人亲手攻击并淘汰3名目标，则获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo16"
  content: "如果小丑在会议期间被投票驱逐，则小丑获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo17"
  content: "臭鼬可以用臭味标记其他玩家，如果在一个回合内，所有存活的玩家都被标记，则臭鼬获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo18"
  content: "占星师可以随时使用占卜球，查看地图中的玩家分布。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo19"
  content: "潜行狼可以隐身一段时间。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo20"
  content: "捕梦者可以看见并捕捉被淘汰的观战玩家，捕捉到4个灵魂便可获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo21"
  content: "雾隐狼会在迷雾阶段进入隐身状态。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo22"
  content: "会议期间，密探可以猜测1名玩家的身份，如果累计猜对2名，则密探获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_DreamCatcher"
  content: "捕捉到4个灵魂可获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NoSelect"
  content: "请先选择该玩家的身份"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SendChat_1"
  content: "我是平民，铁好人"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SendChat_2"
  content: "你不要过来呀"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SendChat_3"
  content: "抱团，来贴贴"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SendChat_4"
  content: "跟着我，一起做任务"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SendChat_5"
  content: "你不会敢刀我吧？"
  switch: 1
}
rows {
  id: "NR3E3_AIMessage_SendChat_6"
  content: "你看着像是好人"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_RoomTitle1"
  content: "房间信息"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_RoomTitle2"
  content: "身份一览"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_RoomTitle3"
  content: "游戏规则"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Rule1"
  content: "人数上限"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Rule2"
  content: "阵营身份"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Rule3"
  content: "会议形式"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Rule4"
  content: "会议时长"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Rule5"
  content: "紧急会议次数"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Rule6"
  content: "任务总数量"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_JobTitle1"
  content: "{0}阵营  ({1}/{2})"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_JobTitle2"
  content: "将用{0}个随机身份补足空缺"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_JobBtn"
  content: "身份"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Tip1"
  content: "阵营："
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Tip2"
  content: "能力："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_ArriveInfo"
  content: "剩余星宝"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_RandomSide"
  content: "随机{0}"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_RandomSideTip"
  content: "将从{0}未选择的身份中随机一个出现在本局游戏中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NaughtyTrick"
  content: "捣蛋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NoTrickTarget"
  content: "缺少捣蛋目标"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Tricking"
  content: "调皮鬼正在对你捣蛋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Teambanned"
  content: "组队队员全部淘汰后才可使用"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_CantUseAbility"
  content: "当前状态无法使用技能"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_MeetingStateChange5"
  content: "自由讨论"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting_ChatTip_Voice1"
  content: "语音转文字"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting_ChatTip_Input"
  content: "打字"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting_ChatTip_Voice2"
  content: "文"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_CantUseRadar"
  content: "紧急任务期间不可使用水晶球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_CantUsRadioMsg1"
  content: "本局已经使用过广播了"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_CantUsRadioMsg2"
  content: "正在广播中，请稍候"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_CantUsRadioMsg3"
  content: "紧急任务期间不可使用广播"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E0_RuleTitle1"
  content: "基本介绍"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E0_RuleTitle2"
  content: "胜利条件"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E1_RuleTitle1"
  content: "伪装者"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E1_RuleTitle2"
  content: "技能：伪装"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E1_RuleTitle3"
  content: "技能：隐形"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E1_RuleTitle4"
  content: "技能：透视"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E1_RuleTitle5"
  content: "搜捕者"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E1_RuleTitle6"
  content: "技能：射击"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E1_RuleTitle7"
  content: "技能：扫描"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E1_RuleTitle8"
  content: "技能：探查"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_RuleTitle1"
  content: "星宝阵营"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_RuleTitle2"
  content: "技能：射击"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_RuleTitle3"
  content: "手枪"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_RuleTitle4"
  content: "星星"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_RuleTitle5"
  content: "卧底阵营"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_RuleTitle6"
  content: "技能：攻击"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_RuleTitle7"
  content: "技能：陷阱"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E2_RuleTitle8"
  content: "卧底时刻"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle1"
  content: "模式"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle2"
  content: "平民阵营"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle3"
  content: "狼人阵营"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle4"
  content: "中立阵营"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle5"
  content: "任务"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle6"
  content: "普通任务"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle7"
  content: "紧急任务"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle8"
  content: "报告"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle9"
  content: "会议"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle10"
  content: "观战"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle11"
  content: "设施"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle12"
  content: "铃铛"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle13"
  content: "密道"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle14"
  content: "身份介绍"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle15"
  content: "平民"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle16"
  content: "警长"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle17"
  content: "天使"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle18"
  content: "侦探"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle19"
  content: "哨子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle20"
  content: "主持人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle21"
  content: "消防员"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle22"
  content: "勇士"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle23"
  content: "学者"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle24"
  content: "狼人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle25"
  content: "伪装狼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle26"
  content: "炸弹狼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle27"
  content: "刺客狼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle28"
  content: "赌徒"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle29"
  content: "赏金猎人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle30"
  content: "小丑"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle31"
  content: "臭鼬"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle34"
  content: "密探"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_CloseDoor_Description"
  content: "<LobbyChatYellow>关闭场景中的门</>，持续一小段时间"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_CloseDoor_ButtonName"
  content: "关门"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_JobInfoCampNum"
  content: "总人数：{0}"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_CommonCampNum"
  content: "{0}人"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_CampTitle3"
  content: "中立阵营"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Rule7"
  content: "投票形式"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_CampNum_1"
  content: "狼人{0}人"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_CampNum_2"
  content: "平民{0}人"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_CampNum_3"
  content: "中立{0}人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_LoneWolfEnhanceCoolDown"
  content: "<InLevel_Highlight28>杀意觉醒，</>攻击冷却时间减少！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Drawer_UseRadar"
  content: "占卜球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SneakWolfAbilityName"
  content: "潜行"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingVoteTip"
  content: "弃票"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_title"
  content: "信誉分"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_title1"
  content: "我的分数"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_title2"
  content: "违规行为"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_title3"
  content: "加分行为"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_title4"
  content: "历史分数"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content1"
  content: "在谁是狼人中的违规行为将扣除信誉分"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content2"
  content: "信誉分低于{0}分将无法匹配谁是狼人玩法"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content3"
  content: "<reputationScore23Red>中途退出</>：未被淘汰时，强制退出将扣除信誉分。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content4"
  content: "<reputationScore23Red>挂机</>：未被淘汰时，局内长时间未进行有效行为将扣除信誉分。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content5"
  content: "<reputationScore23Red>不当言论</>：局内辱骂他人，传播广告或违法信息等行为将扣除信誉分。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content6"
  content: "<reputationScore23Red>举报核实</>：其他违规行为，被玩家举报核实后将扣除信誉分。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content7"
  content: "<reputationScore23>正常对局</>：正常完成谁是狼人对局，没有违规行为，信誉分+{0}分。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content8"
  content: "<reputationScore23>每日登录</>：每日首次登录，信誉分+{0}分。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content9"
  content: "时间"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content10"
  content: "变动"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content11"
  content: "变动后"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content12"
  content: "原因"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content13"
  content: "中途退出"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content14"
  content: "挂机"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content15"
  content: "正常对局"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content16"
  content: "每日登录"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content17"
  content: "<reputationScore23Red>场外沟通</>：局内场外沟通，被玩家举报核实后将扣除信誉分。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ReputationScore_content18"
  content: "场外沟通"
  switch: 1
}
rows {
  id: "UI_NR3E3_Reputation_Points_Quit_Hint"
  content: "未被淘汰，退出会影响其他玩家体验\n\n强制退出将<Red28>扣除{0}信誉分</>"
  switch: 1
}
rows {
  id: "UI_NR3E3_Reputation_Points_Quit_Button"
  content: "强制退出"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Empty"
  content: "无"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_LoneWolf"
  content: "当狼人仅剩独狼时，独狼的攻击冷却时间减半。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_SneakWolf"
  content: "潜行狼可以隐身一段时间。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_FogSneakWolf"
  content: "雾隐狼可以在迷雾中获得持续性的隐身效果。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Naughty"
  content: "调皮鬼被淘汰后，可以对狼人进行捣蛋，使他一段时间内无法移动。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Drawer"
  content: "占星师可以随时使用占卜球，查看地图中的玩家分布。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_LoneWolf2"
  content: "当狼人仅剩自己时，攻击冷却时间减半"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_SneakWolf2"
  content: "你可以隐身一段时间"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_FogSneakWolf2"
  content: "你可以在迷雾中获得隐身效果。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_FirstAider"
  content: "急救员可以救活一名15秒内刚被攻击淘汰的玩家。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_FirstAider2"
  content: "你可以救活一名刚被淘汰的玩家。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Naughty2"
  content: "你被淘汰后可以捣蛋活着的狼人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Drawer2"
  content: "你可以随时随地使用占卜球"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_DanceWolf"
  content: "舞蹈狼可以使周围的玩家同时进行一段舞蹈。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_DanceWolf2"
  content: "你可以使周围的人开始跳舞。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Dancer"
  content: "舞者可以使周围的玩家同时进行一段舞蹈，如果同时起舞的玩家超过存活玩家的半数，则舞者获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Dancer2"
  content: "使大家同时跳舞来获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Delivery"
  content: "快递员需要将快递送到指定玩家那，成功送7次快递，则快递员获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Delivery2"
  content: "送达7次快递可获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameFinish_WinInfo1"
  content: "狼人阵营胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameFinish_WinInfo2"
  content: "赏金猎人胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameFinish_WinInfo3"
  content: "赌徒胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameFinish_WinInfo4"
  content: "小丑胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameFinish_WinInfo5"
  content: "臭鼬胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task207_Tip"
  content: "消灭老鼠（{0}/{1}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task206_Tip"
  content: "拾取碎木片（{0}/{1}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameFinish_WinInfo6"
  content: "平民阵营胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task222_Tip"
  content: "欣赏中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule_RoleInfoPainter"
  content: "占星师可以使用占卜球，查看地图中的玩家分布。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule_RolePainter"
  content: "占星师"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule_StalkerWolf"
  content: "潜行狼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule_RoleInfoStalkerWolf"
  content: "潜行狼可以隐身一段时间。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule_RadarInfo"
  content: "使用水晶球，可以查看地图中每个区域的玩家分布。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule_RadioInfo"
  content: "使用广播，可以向所有人发送语音，传递消息。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule_Radar"
  content: "水晶球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule_Radio"
  content: "广播"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UseRadioWhitOutMic"
  content: "请打开麦克风重试。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes301"
  content: "点击着火点灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes302"
  content: "点击食用披萨"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPane301"
  content: "灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPane302"
  content: "吃饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task7"
  content: "紧急任务：吃饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task8"
  content: "紧急任务：灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task9"
  content: "平民需要吃饭补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task10"
  content: "平民们需要在限时内进行灭火，否则，狼人将直接获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem7"
  content: "点起大火，平民如果没有在<LobbyChatYellow>限时内完成灭火</>，则狼人直接获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem8"
  content: "放火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem9"
  content: "所有平民<LobbyChatYellow>吃饭</>补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem10"
  content: "吃饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName21"
  content: "占卜屋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName22"
  content: "河道"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName23"
  content: "酒窖"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName24"
  content: "书房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName25"
  content: "训练场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName26"
  content: "前院"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName27"
  content: "侧院"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName28"
  content: "正厅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName29"
  content: "卧室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName30"
  content: "餐厅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName31"
  content: "回廊"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName32"
  content: "陈列室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName33"
  content: "大露台"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName41"
  content: "营地"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName42"
  content: "山洞"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName43"
  content: "联络站"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName44"
  content: "生活区"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName45"
  content: "宝藏室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName46"
  content: "门廊"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName47"
  content: "蘑菇园"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName48"
  content: "大瀑布"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName49"
  content: "祭坛"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName50"
  content: "神像台"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName51"
  content: "沼泽地"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName61"
  content: "校门"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName62"
  content: "大厅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName63"
  content: "一班"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName64"
  content: "二班"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName65"
  content: "三班"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName66"
  content: "美术室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName67"
  content: "实验室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName68"
  content: "车棚"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName69"
  content: "大食堂"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName70"
  content: "小食堂"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName71"
  content: "校史馆"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName72"
  content: "阅览室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName73"
  content: "活动室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName74"
  content: "求食路"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName75"
  content: "寻味路"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName76"
  content: "矿场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName77"
  content: "医疗室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName78"
  content: "餐厅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName79"
  content: "卧室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName80"
  content: "娱乐房"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName81"
  content: "发射场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName82"
  content: "指挥室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName83"
  content: "能源室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName84"
  content: "观测站"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName85"
  content: "生态舱"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName86"
  content: "实验室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName87"
  content: "矿物舱"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName88"
  content: "玉兔街"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName89"
  content: "小广场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName90"
  content: "时光城堡"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName91"
  content: "幻想剧场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName92"
  content: "化妆间"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName93"
  content: "休闲乐园"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName94"
  content: "云霄飞车(北)"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName95"
  content: "云霄飞车(南)"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName96"
  content: "恐龙乐园"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName97"
  content: "展厅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName98"
  content: "宝藏湾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName99"
  content: "飓风飞椅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName100"
  content: "咖啡店"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName101"
  content: "唱片店"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName102"
  content: "潮鞋店"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName103"
  content: "电玩室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName104"
  content: "花店"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName105"
  content: "苏宅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName106"
  content: "超市"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName107"
  content: "水果店"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName108"
  content: "花灯店"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName109"
  content: "早餐铺"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName110"
  content: "地铁站"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName111"
  content: "运动用品店"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName112"
  content: "小广场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName801"
  content: "农家乐"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName802"
  content: "小卖部"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName803"
  content: "老王家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName804"
  content: "大舞台"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName805"
  content: "老张家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName806"
  content: "渔夫家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName807"
  content: "松树林"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName808"
  content: "度假区"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName809"
  content: "休息室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName810"
  content: "温泉"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName811"
  content: "村口"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName901"
  content: "月亮泉"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName902"
  content: "谷口"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName903"
  content: "鹿林"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName904"
  content: "洞口"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName905"
  content: "晶矿洞"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName906"
  content: "南瓜屋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName907"
  content: "洞底灵树"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName908"
  content: "妙蛙山"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName909"
  content: "精灵屋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9901"
  content: "码头"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9902"
  content: "篝火台"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9903"
  content: "沙滩"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9904"
  content: "营地"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9905"
  content: "遗迹"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9906"
  content: "鸟巢"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9907"
  content: "海底"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9908"
  content: "杂物间"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9909"
  content: "祭坛"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9910"
  content: "密室"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9911"
  content: "勘测区"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapName9912"
  content: "回廊"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle32"
  content: "调皮鬼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle33"
  content: "独狼"
  switch: 1
}
rows {
  id: "UI_Competition_HonorRank4"
  content: "比赛已经结束"
  switch: 1
}
rows {
  id: "UI_Competition_HonorRank5"
  content: "比赛结束"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task217_Tip"
  content: "切开水果（{0}/{1}）"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Rule8"
  content: "淘汰后显示身份"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotTrickHunterVocation"
  content: "只可对狼人使用"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_GreatStar"
  content: "大明星被淘汰时，所有平民阵营的玩家都会收到提示信息"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_GreatStar2"
  content: "你被淘汰时，所有平民会获得信息"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_GreatStarTip"
  content: "大明星"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_OutTip"
  content: "被淘汰啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleTarget_PuppetMaster"
  content: "放置假人欺骗别人来获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_PuppetMaster"
  content: "人偶师可以放置一个随机玩家的假人，假人被攻击后将消失；如果有2个假人被攻击，则人偶师获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_SkillName_Puppet"
  content: "假人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceTaskName"
  content: "任务地点"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceHoleName"
  content: "密道"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceRadioName"
  content: "广播"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceBellName"
  content: "铃铛"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceRadarName"
  content: "水晶球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceTaskDesc"
  content: "前往这里，可以进行一项常规任务"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceHoleDesc"
  content: "场景中有若干个密道供狼人使用。\n狼人可以在密道中藏身、观察。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceHoleDesc1"
  content: "前往这里，可以进行一项常规任务。\n狼人可以在密道中藏身、观察。\n狼人可以在多个密道间快速通行。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceRadioDesc"
  content: "使用广播，可以同时对所有人聊天发言"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceBellDesc"
  content: "摇动铃铛，可以发起一次紧急会议"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MapIntroduceRadarDesc"
  content: "通过水晶球，可以观察大家的位置分布"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Porter_Carry"
  content: "搬运"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Porter_CarryDrop"
  content: "松手"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleTarget_Porter"
  content: "成功搬运2名淘汰的玩家可获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Porter"
  content: "搬运工可以拖动一名被淘汰的玩家，并将他搬运到密道之中；成功搬运2次，则搬运工个人获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleTarget_StrongHunter"
  content: "淘汰所有的平民"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_StrongHunter"
  content: "大力狼可以拖动一名被淘汰的玩家，并将他藏匿在密道之中"
  switch: 1
}
rows {
  id: "NR3E_EndReason_105_Tips"
  content: "<YellowCurrencytTip>{0}</>完成了2次人偶骗局"
  switch: 1
}
rows {
  id: "NR3E_EndReason_105"
  content: "人偶师完成了2次人偶骗局"
  switch: 1
}
rows {
  id: "NR3E_EndReason_106_Tips"
  content: "<YellowCurrencytTip>{0}</>完成了2次搬运任务"
  switch: 1
}
rows {
  id: "NR3E_EndReason_106"
  content: "搬运工完成了2次搬运任务"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotCarryDeathBody"
  content: "缺少搬运目标"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameFinish_WinInfo7"
  content: "人偶师胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameFinish_WinInfo8"
  content: "搬运工胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GameFinish_WinInfo9"
  content: "{0}胜利"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingTimeOutSkipSpeech"
  content: "您的发言回合即将提前结束（{0}）"
  switch: 1
}
rows {
  id: "UI_Chat_NR3E3MeetingNotChat"
  content: "当前阶段不能发言"
  switch: 1
}
rows {
  id: "UI_RankGameFinish_ScoreTips_Title"
  content: "积分规则-"
  switch: 1
}
rows {
  id: "NR3E2InLevel_RoleRule_Killer"
  content: "·淘汰星宝可以获得积分。\n·淘汰警卫星宝获得的积分更多。"
  switch: 1
}
rows {
  id: "NR3E2InLevel_RoleRule_Guard"
  content: "·淘汰卧底可以获得高额积分。\n·拾取星星、解救别人也可以获得积分。"
  switch: 1
}
rows {
  id: "NR3E2InLevel_RoleRule_Killer1"
  content: "·淘汰星宝或警卫星宝可以获得积分。\n·淘汰警卫星宝获得的积分更多。\n·排名将影响玩家的结算奖励。"
  switch: 1
}
rows {
  id: "NR3E2InLevel_RoleRule_Guard1"
  content: "·淘汰卧底可以获得高额积分。\n·拾取星星、解救别人也可以获得积分。\n·排名将影响玩家的结算奖励。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side1_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side1_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side1_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side2_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side2_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side2_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side2_4"
  content: "被攻击淘汰时，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side3_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side3_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side3_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side3_4"
  content: "保护其他玩家，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side4_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side4_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side4_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side5_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side5_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side5_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side5_4"
  content: "成功攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side6_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side6_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side6_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side6_4"
  content: "调查其他玩家，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side7_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side7_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side7_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side7_4"
  content: "快速完成紧急任务，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side7_5"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side8_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side8_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side8_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side9_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side9_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side9_3"
  content: "成功进行暗算，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side9_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side10_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side10_2"
  content: "炸弹淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side10_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side11_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side11_2"
  content: "臭味标记其他玩家，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side11_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side12_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side12_2"
  content: "成功进行猜测，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side12_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side13_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side13_2"
  content: "攻击赏金目标，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side13_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side14_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side14_2"
  content: "被其他人投票，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side14_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side15_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side15_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side15_3"
  content: "成功投票给狼人，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side16_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side16_2"
  content: "完成各类任务，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side16_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side17_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side17_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side17_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side17_4"
  content: "攻击狼人或中立阵营的玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side18_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side18_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side18_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side19_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side19_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side19_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side20_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side20_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side20_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side20_4"
  content: "对狼人进行捣乱，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side21_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side21_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side21_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side21_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side22_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side22_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side22_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side22_4"
  content: "被攻击淘汰时，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side23_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side23_2"
  content: "成功进行人偶骗局，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side23_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side24_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side24_2"
  content: "成功搬运被淘汰的玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side24_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side25_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side25_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side25_3"
  content: "藏匿被淘汰的玩家，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side25_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side26_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side26_2"
  content: "魔爆弹淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side26_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side27_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side27_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side27_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side27_4"
  content: "攻击狼人或中立阵营的玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side28_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side28_2"
  content: "每存活过一次会议，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side28_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side29_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side29_2"
  content: "成功装入其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side29_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side30_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side30_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side30_3"
  content: "成功进行封印，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side30_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side31_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side31_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side31_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side31_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side32_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side32_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side32_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side32_4"
  content: "成功完成调查，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side33_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side33_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side33_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side35_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side35_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side35_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side35_4"
  content: "成功进行审判，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side36_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side36_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side36_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side36_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side37_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side37_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side37_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side38_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side38_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side38_3"
  content: "幽魂淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side38_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side39_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side39_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side39_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side39_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side40_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side40_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side40_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side41_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side41_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side41_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side41_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side42_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side42_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side42_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side42_4"
  content: "每存活过一次会议，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side43_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side43_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side43_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side43_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side44_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side44_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side44_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side44_4"
  content: "根据保护时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side45_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side45_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side45_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side46_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side46_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side46_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side46_4"
  content: "每次关闭管道，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side47_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side47_2"
  content: "成功捕捉被淘汰的观战玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side47_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side48_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side48_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side48_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side49_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side49_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side49_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side49_4"
  content: "攻击狼人或中立阵营的玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side50_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side50_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side50_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side51_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side51_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side51_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side51_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side52_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side52_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side52_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side52_4"
  content: "成功救活平民玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side53_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side53_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side53_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side54_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side54_2"
  content: "和其他玩家一起跳舞，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side54_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side55_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side55_2"
  content: "每次成功送快递，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side55_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side56_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side56_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side56_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side56_4"
  content: "成功完成调查，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side57_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side57_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side57_3"
  content: "成功投票给狼人，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side58_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side58_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side58_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side59_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side59_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side59_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side59_4"
  content: "攻击狼人或中立阵营的玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side60_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side60_2"
  content: "成功进行猜测，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side60_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side61_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side61_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side61_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side61_4"
  content: "攻击狼人或中立阵营的玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side62_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side62_2"
  content: "每次成功放鞭炮，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side62_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side63_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side63_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side63_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side63_4"
  content: "在迷雾中放出烟花，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side64_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side64_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side64_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side65_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side65_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side65_3"
  content: "成功投票给狼人，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side65_4"
  content: "与狼人共生时，被淘汰获得大量积分；"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side65_5"
  content: "与平民共生时，存活将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side66_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side66_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side66_3"
  content: "成功投票给狼人，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side66_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side67_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side67_2"
  content: "年兽成功淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side67_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side68_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side68_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side68_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side68_4"
  content: "被攻击淘汰时，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side69_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side69_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side69_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side69_4"
  content: "成功使用陷阱困住其他玩家，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side70_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side70_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side70_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side70_4"
  content: "成功使用查验，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side71_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side71_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side71_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side72_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side72_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side72_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side73_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side73_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side73_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side74_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side74_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side74_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side74_4"
  content: "通过快速决斗淘汰其他玩家，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side75_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side75_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side75_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side75_4"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side76_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side76_2"
  content: "成功投票给非狼人玩家，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side76_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side76_4"
  content: "参与投票驱逐非狼人玩家，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side77_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side77_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side77_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side77_4"
  content: "赠送礼物给平民玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side78_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side78_2"
  content: "攻击淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side78_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side79_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side79_2"
  content: "每次钉刺其他玩家，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side79_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side80_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side80_2"
  content: "诅咒淘汰其他玩家，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side80_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side81_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side81_2"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side81_3"
  content: "成功投票给狼人，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side81_4"
  content: "对平民玩家进行讲课，将获得一些积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side82_1"
  content: "获得游戏胜利，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side82_2"
  content: "攻击指定的目标，将获得大量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_Side82_3"
  content: "根据存活时间，将持续获得少量积分。"
  switch: 1
}
rows {
  id: "UI_Chat_OB_NR3E3MeetingPlayer1"
  content: "第{0}次会议"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_OB_Task1"
  content: "功能未开放"
  switch: 1
}
rows {
  id: "UI_FinalAccount_QualifyScoreTitle1"
  content: "排名得分"
  switch: 1
}
rows {
  id: "UI_FinalAccount_QualifyScoreTitle2"
  content: "胜负得分"
  switch: 1
}
rows {
  id: "InLevel_NR3E_CompleteSickenMission"
  content: "成功找到药物啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E_SickenOutTip"
  content: "未能及时找到药物"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_NuClearBombFire_DeathNotify"
  content: "魔爆弹爆炸啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_NuClearHunter"
  content: "魔爆狼可以放置一枚狼人可见的魔爆弹，一段时间后将会爆炸，淘汰周围大范围内的所有玩家。"
  switch: 1
}
rows {
  id: "NR3E_NuClearBomb_IconText"
  content: "魔爆弹"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Ranger"
  content: "独行侠拥有攻击能力，只能在与他人独处时使用。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_AbilityFailed_Ranger"
  content: "未独行，不可使用"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_IronMan"
  content: "铁蛋不会被攻击淘汰，若经过3次会议后铁蛋仍然存活，则铁蛋获得胜利。"
  switch: 1
}
rows {
  id: "NR3E_EndReason_107_Tips"
  content: "<YellowCurrencytTip>{0}</>存活过了3次会议"
  switch: 1
}
rows {
  id: "NR3E_EndReason_107"
  content: "铁蛋存活过了3次会议"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTarget_IronMan"
  content: "存活过3次会议可获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTarget_Tramp"
  content: "将其他人装入口袋中来获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Tramp"
  content: "流浪汉可以将其他玩家装入随身的大口袋中，他们将在会议开始时被淘汰；当仅剩自己存活时便获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTips_Tramp"
  content: "你被流浪汉装入袋中啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTips1_Tramp"
  content: "下次会议开始时，你将被淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTips2_Tramp"
  content: "下次会议开始前，若流浪汉被淘汰，你将被解救出出来。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTips3_Tramp"
  content: "流浪汉的口袋可以同时装下多个玩家"
  switch: 1
}
rows {
  id: "NR3E_EndReason_108_Tips"
  content: "<YellowCurrencytTip>{0}</>存活到了最后"
  switch: 1
}
rows {
  id: "NR3E_EndReason_108"
  content: "流浪汉存活到了最后"
  switch: 1
}
rows {
  id: "NR3E_Vocation_Dance_IconText"
  content: "舞蹈"
  switch: 1
}
rows {
  id: "NR3E_TrampPack_IconText"
  content: "装入"
  switch: 1
}
rows {
  id: "NR3E_Vocation_Delivery_IconText"
  content: "送达"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_SealingWolf"
  content: "秘术狼可以在会议中猜测一名平民玩家的身份，如果猜对，则封印该玩家，使其不能使用身份技能与投票。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_SealingWolf"
  content: "你可以在会议中猜测身份来封印别人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_StrongHunter2"
  content: "你可以拖动并藏匿被淘汰的玩家"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_NuClearHunter2"
  content: "你可以放置魔爆弹来攻击范围内的玩家"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Ranger2"
  content: "你可以在与人独处时进行攻击"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_SealingWolf2"
  content: "你可以在会议中猜测身份来封印别人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel501"
  content: "灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanel503"
  content: "吃饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes501"
  content: "点击着火点灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskPanelDes503"
  content: "点击食用果实"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem13"
  content: "点起大火，平民如果没有在<LobbyChatYellow>限时内完成灭火</>，则狼人直接获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem14"
  content: "放火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem15"
  content: "所有平民<LobbyChatYellow>吃饭</>补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem16"
  content: "吃饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task13"
  content: "紧急任务：灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task14"
  content: "平民们需要在限时内进行灭火，否则，狼人将直接获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task15"
  content: "紧急任务：吃饭"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task16"
  content: "平民需要吃饭补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskTips1"
  content: "距离任务点太远，任务失败"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_JobInfoEasterEgg"
  content: "本局身份一览（彩蛋局：{0}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Failed_AlreadyHasBomb"
  content: "目标已有炸弹，无法进行传递"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Elder"
  content: "长老可以看到被投票驱逐的玩家的身份。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Elder"
  content: "你可以看到被驱逐的玩家的身份"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Survey_WidgetName"
  content: "调查"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Forensic"
  content: "法医可以调查一名被淘汰的玩家，从而知道他的身份"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_OB1"
  content: "双击屏幕，可以退出电影模式"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Forensic"
  content: "你可以调查出被淘汰的玩家的身份"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_OB2"
  content: "当前场景无可用的固定摄像机"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildBountyHunterItem"
  content: "已淘汰目标：<childItemYellow>{0}个</>\n当前目标：<childItemRed>{1}号</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildIronManSurviveItem"
  content: "已存活过会议：<childItemYellow>{0}次</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildPorterCarryItem"
  content: "已成功搬运玩家：<childItemYellow>{0}名</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildDreamcatcherItem"
  content: "已捕捉到的灵魂：<childItemYellow>{0}名</>"
  switch: 2
}
rows {
  id: "UI_InLevel_NR3E4_ChildPuppetMasterItem"
  content: "已成功完成骗局：<childItemYellow>{0}次</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildDeliveryItem"
  content: "已成功送达快递：<childItemYellow>{0}次</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_DeliveryTip"
  content: "快递客户目标已更新"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_DeliveryTip2"
  content: "快递客户目标已更新2"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_DeliveryTip3"
  content: "缺少送达目标"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_PuppetMasterTip"
  content: "已成功完成1次骗局"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_XRayHunter"
  content: "火眼狼攻击淘汰其他玩家时，可以看到他的身份"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_XRayHunter2"
  content: "你淘汰别人时，可以看到他的身份"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule61"
  content: "火眼狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_PhotoGrapher"
  content: "摄影师可以拍摄当前画面，如果画面内有当前存活的所有玩家，则摄影师个人获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_PhotoGrapher2"
  content: "拍摄到包含所有人的照片可获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleTarget_PhotaGrapher"
  content: "拍摄到包含所有人的照片可获得胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule62"
  content: "摄影师"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_PhotoGraper_WidgetName"
  content: "拍照"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule63"
  content: "法官"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Judge"
  content: "法官可以在会议中猜测狼人或中立玩家的身份，如果猜对，则淘汰掉该玩家，如果猜错，则自己被淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Judge"
  content: "你可以在会议中猜测身份来淘汰别人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_BellCD"
  content: "正在冷却中，请稍等"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_EagleEye"
  content: "{0}使用了透视。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_1"
  content: "{0}攻击了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_2"
  content: "{0}攻击了悬赏目标{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_3"
  content: "{0}攻击了{1}的人偶。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_4"
  content: "{0}攻击了{1}，攻击无效。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_5"
  content: "{0}攻击了{1}，但被护盾抵挡。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_6"
  content: "{0}拥有护盾，抵挡了本次爆炸。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_7"
  content: "{0}攻击了{1}，自己被淘汰了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_8"
  content: "{0}成功调查了{1}的身份。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_9"
  content: "{0}扔给{1}一颗炸弹。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_10"
  content: "{0}扔给{1}的人偶一颗炸弹。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_11"
  content: "炸弹爆炸，{0}被淘汰了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_12"
  content: "炸弹爆炸，{0}的人偶被消灭了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_13"
  content: "{0}将{1}装入了麻袋。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_14"
  content: "{0}从麻袋中逃出。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_15"
  content: "{0}成功调查了{1}的身份。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_16"
  content: "{0}将倒地的{1}投入了密道。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_17"
  content: "{0}将{1}投入了密道"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_18"
  content: "{0}放置了魔爆弹。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_19"
  content: "魔爆弹爆炸，{0}被淘汰了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_20"
  content: "魔爆弹爆炸，{0}的人偶被消灭了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_21"
  content: "总任务进度{0}%"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_22"
  content: "{0}发起了紧急任务（{1}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_23"
  content: "{0}未及时吃药，被淘汰了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_24"
  content: "{0}使用技能，完成了紧急任务（{1}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_25"
  content: "{0}完成了紧急任务（{1}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_26"
  content: "平民阵营胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_27"
  content: "狼人阵营胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_28"
  content: "{0}胜利(淘汰了{1}名目标)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_29"
  content: "任务进度已达标，平民阵营胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_30"
  content: "平民未及时灭火，狼人阵营胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_31"
  content: "时间结束，狼人阵营胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_32"
  content: "{0}发起了紧急会议。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_33"
  content: "{0}报告发现了倒地的{1}，开启了会议。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_34"
  content: "{0}被淘汰了"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_35"
  content: "{0}暗算淘汰了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_36"
  content: "暗算失败，{0}被淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_37"
  content: "{0}成功猜测了{1}的身份。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_38"
  content: "{0}封印了{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_39"
  content: "{0}投票给了{1}（共{2}票）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_40"
  content: "{0}弃票了（共{1}票）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_41"
  content: "{0}被驱逐了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_42"
  content: "{0}票数相同，无人被驱逐。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_43"
  content: "弃票数最多，无人被驱逐。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_44"
  content: "{0}胜利(标记了所有人)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_45"
  content: "{0}胜利(成功搬运{1}次)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_46"
  content: "{0}胜利(完成了{1}次骗局)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_47"
  content: "{0}胜利(存活到了最后)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_48"
  content: "{0}胜利(存活过{1}次会议)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_49"
  content: "{0}胜利(被投票驱逐)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_50"
  content: "{0}胜利(成功猜测了{1}次)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_51"
  content: "{0}发起了紧急任务(关门)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_1"
  content: "{0}对{1}添加了护盾。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_2"
  content: "{0}获得了攻击技能。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_3"
  content: "{0}捣蛋了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_4"
  content: "{0}伪装成了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_5"
  content: "{0}把炸弹传给了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_6"
  content: "{0}把炸弹传给了{1}的人偶。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_7"
  content: "{0}进入了潜行。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_8"
  content: "{0}觉醒了杀意。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_9"
  content: "{0}将倒地的{1}投入了密道。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_10"
  content: "{0}标记了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_11"
  content: "{0}放置了假人。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_12"
  content: "{0}完成了任务（{1}）。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_13"
  content: "{0}尝试猜测{1}的身份，但猜错了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_OrdinaryEvents_14"
  content: "{0}尝试封印{1}，但没有成功。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_EventGameFinish"
  content: "本局游戏共进行：<playerAllColorYellow>{0}个回合</>，<playerAllColorYellow>{1}次会议</>，<playerAllColorYellow>{2}次紧急会议</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_EventGameFinish1"
  content: "本局游戏共进行：<playerAllColorYellow>{0}个回合</>，<playerAllColorYellow>{1}次会议</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_EventGameFinish2"
  content: "本局游戏共进行：<playerAllColorYellow>{0}个回合</>，<playerAllColorYellow>{1}次紧急会议</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_EventGameFinish3"
  content: "本局游戏共进行：<playerAllColorYellow>{0}个回合</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Listener"
  content: "监听者可以把窃听器放在其他玩家身上，时刻监听该玩家。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Listener"
  content: "你可以监听其他玩家"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule64"
  content: "监听员"
  switch: 1
}
rows {
  id: "InLevel_Listener_Widget_Name"
  content: "监听"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentityChildContent1"
  content: "阵营卡数量不足"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentityChildContent2"
  content: "身份卡数量不足"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentityNoSelect"
  content: "8人模式不可选择中立阵营"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentitySelect1"
  content: "1.你可以选择指定的身份，用于下局游戏，每次\n   消耗<identitySelectTips18>1个身份卡</>。\n2.你也可以选择指定的阵营，在该阵营中获得一\n   个随机身份，每次消耗<identitySelectTips18>1个阵营卡</>。\n3.没有选择时，默认为：随机身份。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentitySelect2"
  content: "<identitySelectTips18>身份卡</>:在[谁是狼人]玩法中消耗身份卡，可自选\n身份进入游戏。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentitySelect3"
  content: "<identitySelectTips18>阵营卡</>:在[谁是狼人]玩法中消耗阵营卡，可自选\n阵营进入游戏。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentitySelect4"
  content: "下局游戏中，你将是：{0}</>\n消耗{1}</>"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentitySelect5"
  content: "一张阵营卡"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentitySelect6"
  content: "一张身份卡"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_FlashWolf"
  content: "闪光狼可以掷出一枚闪光弹，致盲周围范围内的其他阵营玩家。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_GhostWolf"
  content: "幽灵狼死后可以化身缓慢的幽魂追击并淘汰其他玩家。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Psychic"
  content: "通灵师可以化成灵魂，脱离身体自由移动，并在结束时返回身体内。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_MaskWolf"
  content: "假面狼攻击淘汰其他玩家后，将变身成该玩家，直到下一次会议。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_FlashWolf"
  content: "你可以使用闪光弹致盲别人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_GhostWolf"
  content: "你被淘汰后可以化作幽魂追击别人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Psychic"
  content: "你可以化成灵魂自由移动"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_MaskWolf"
  content: "你淘汰别人时，将与他交换外型与编号"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule65"
  content: "闪光狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule66"
  content: "幽灵狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule67"
  content: "通灵师"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Rule68"
  content: "假面狼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_OB_EventDetails1"
  content: "第{0}回合"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_OB_EventDetails2"
  content: "紧急会议"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason19"
  content: "{0}胜利(捕捉到{1}个灵魂)"
  switch: 0
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Dreamcatcher"
  content: "{0}捕获了{1}的灵魂。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_GhostAttackTarget"
  content: "{0}变成幽魂，开始追击{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_MaskAttack"
  content: "{0}变身成了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_GhostAttackSuccess"
  content: "幽魂攻击了{0}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_AttackSuccess"
  content: "{0}攻击了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_PlaceBomb"
  content: "{0}扔给{1}一颗炸弹。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BombKill "
  content: "炸弹爆炸，{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_SheriffSuicide"
  content: "{0}攻击了{1}，自己被淘汰了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DetectSurvey"
  content: "{0}成功调查了{1}的身份。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TrampPack"
  content: "{0}将{1}装入了麻袋。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TrampOut"
  content: "{0}从麻袋中逃出。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_ForensicSurvey"
  content: "{0}成功调查了{1}的身份"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_PorterThrowDeathBody"
  content: "{0}将倒地的{1}投入了密道。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_PorterThrowBody"
  content: "{0}将{1}投入了密道"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_PlaceNuClear"
  content: "{0}放置了魔爆弹。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_NuClearKill"
  content: "魔爆弹爆炸，{0}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_SickenDead"
  content: "{0}未及时吃药，被淘汰了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_FireMenComplete"
  content: "{0}使用技能，完成了紧急任务（{1}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_ReportDeathBody"
  content: "{0}报告发现了倒地的{1}，开启了会议。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TrampKill"
  content: "{1}被淘汰了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_AssassinSuccess"
  content: "{0}暗算淘汰了{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_AssassinFailed"
  content: "{0}猜测{1}的身份为{2}，导致刺杀失败了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_GuessSuccess"
  content: "{0}成功猜测了{1}的身份。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_SealSuccess"
  content: "{0}封印了{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_MercyShield"
  content: "{0}对{1}添加了护盾。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_WarriorWeapon"
  content: "{0}获得了攻击技能"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_NaughtyTrick"
  content: "{0}捣蛋了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TrickWolfChangeAvatar"
  content: "{0}伪装成了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TransBombToPlayer"
  content: "{0}把炸弹传给了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_SneakWolfSneak"
  content: "{0}进入了潜行。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_FogSneakWolfSneak"
  content: "{0}进入了潜行。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_LoneWolfFire"
  content: "{0}觉醒了杀意。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_StrongWolfThrow"
  content: "{0}将倒地的{1}投入了密道。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_SkunkFart"
  content: "{0}标记了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_GeneratePuppet"
  content: "{0}放置了假人。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_GuessFailed"
  content: "{0}尝试猜测{1}的身份为{2}，但猜错了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_SealFailed"
  content: "{0}猜测{1}的身份为{2}，导致封印失败了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason1"
  content: "平民阵营胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason2"
  content: "任务进度已达标，平民阵营胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason3"
  content: "狼人阵营胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason4"
  content: "时间结束，狼人阵营胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason5"
  content: "平民未及时灭火，狼人阵营胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason10"
  content: "{0}胜利(成功猜测了3次)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason11"
  content: "{0}胜利(淘汰了3名目标)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason12"
  content: "{0}胜利(被投票驱逐)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason13"
  content: "{0}胜利(标记了所有人)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason15"
  content: "{0}胜利(完成了2次骗局)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason16"
  content: "{0}胜利(成功搬运2次)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason17"
  content: "{0}胜利(存活到了最后)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason18"
  content: "{0}胜利(存活过3次会议)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason20"
  content: "{0}胜利(邀请了过半人数进行舞蹈)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason21"
  content: "{0}胜利(成功送达了{1}次快递)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason21_HyperCore"
  content: "{0}胜利(成功送达了6次快递)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason22"
  content: "{0}胜利(成功猜测了指定角色的身份)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason24"
  content: "{0}胜利(成功放了4次鞭炮)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason25"
  content: "{0}胜利(淘汰2名角色)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason26"
  content: "{0}胜利(参与驱逐{1}名非狼人角色)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_EmergencyTaskPublish"
  content: "{0}发起了紧急任务（{1}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_CompleteEmergencyTask"
  content: "{0}完成了紧急任务（{1}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_CompleteNormalTask"
  content: "{0}完成了任务（{1}）。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Vote"
  content: "{0}投票给了{1}（共{2}票）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Abstain"
  content: "{0}弃票了（共{1}票）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_VoteOutcomeElimination"
  content: "{0}被驱逐了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_VoteOutcomeTie"
  content: "{0}票数相同，无人被驱逐。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_VoteOutcomeMostAbstentions"
  content: "弃票数最多，无人被驱逐。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TaskProgressUpdate"
  content: "总任务进度{0}%"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_EmergencyMeetingInitiate"
  content: "{0}发起了紧急会议。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_NoPlayerCasualties"
  content: "魔爆弹爆炸，无人被淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Doll"
  content: "{0}的人偶"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Target"
  content: "悬赏目标{0}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Shielded"
  content: "{0}有护盾未被淘汰"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_AttackBlocked"
  content: "{0},但被护盾抵挡"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Eliminated"
  content: "{0},攻击无效"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_IneffectiveAttack"
  content: "{0}被淘汰了"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_JudgmentSuccess"
  content: "{0}审判了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Listen"
  content: "{0}开始监听{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_JudgmentFailed"
  content: "{0}猜测{1}的身份为{2}，导致审判失败了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_PsychicFinish"
  content: "{0}结束了通灵。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_PsychicBegin"
  content: "{0}开始进行通灵。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_FlashBombEffect"
  content: "{0}受到了闪光弹影响。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_FlashBomb"
  content: "{0}扔出了一颗闪光弹。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DefenderProtected"
  content: "{0}开始保护{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DefenderProtectedFailed"
  content: "{0}脱离了保护。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_AttackProtectedTarget"
  content: "{0}，但被保镖抵挡"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TrackBegin"
  content: "{0}追踪到了{1}的位置。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DefenderProtectedRemoved"
  content: "{0}结束了保护。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_HoleSpawn"
  content: "{0}挖出了一条新密道。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_CloseHole"
  content: "{0}封闭了密道。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_ReOpenHole"
  content: "{0}开启了密道。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DeedWolf"
  content: "{0}附身在{1}身上。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_OstrichCancelDodge"
  content: "{0}睁开了眼睛。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DancerDance"
  content: "{0}邀请了，{1}进行一段舞蹈"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DanceWolfDance"
  content: "{0}邀请了，{1}进行一段舞蹈"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DancerDanceNoPlayer"
  content: "{0}邀请他人进行一段舞蹈，但无人与其共舞"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DanceWolfDanceNoPlayer"
  content: "{0}邀请他人进行一段舞蹈，但无人与其共舞"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_OstrichUseDodge"
  content: "{0}闭上了眼睛。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DeliveryComplete"
  content: "{0}成功将快递送达给{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_JugglerMark"
  content: "{0}在会议中标记了{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_ThemeEventChest"
  content: "{0}借助奇趣魔术盒变身了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Rescue"
  content: "{0}对{1}实施了急救"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_RescueFailed"
  content: "{0}对{1}实施了急救，但急救失败"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Rescued"
  content: "{0}成功复活了{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BlackWolf"
  content: "{0}将所有人变身成了黑衣人。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BestDetectiveSurvey"
  content: "{0}知道了{1}是淘汰{2}的凶手。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_HamsterStorage"
  content: "{0}存储了2票(当前存票{1})。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_SnowWolf"
  content: "{0}使用了风雪。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Fireworks"
  content: "{0}释放了烟花。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TwinLink"
  content: "{0}与{1}完成了共生。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TwinDamage"
  content: "{0}因共生原因而被淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_NianBeastAttackSuccess"
  content: "{0}的年兽攻击了{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_GenerateNianBeast"
  content: "{0}召唤了年兽。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Pack"
  content: "{0}将{1}装入了口袋。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Release"
  content: "{0}将{1}从口袋中放出。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_HighlightMoment"
  content: "{0}达成了高光时刻：{1}（{2}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_HasSurpriseGiftShield"
  content: "{0}，但被圆盾抵挡"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_SurpriseGift"
  content: "{0}从福袋中领取了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_LittleBone"
  content: "{0}复活了"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_SmokeWolf"
  content: "{0}释放了烟雾"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_PostControl"
  content: "{0}控制了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_ControlReady"
  content: "{0}成功猜测了{1}的身份。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_ControlFailed"
  content: "{0}尝试猜测{1}的身份为{2}，但猜错了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_ProphetCheckWolf"
  content: "{0}查验了{1}的阵营为{2}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BrawlWin"
  content: "{0}在决斗中战胜了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BrawlFailed"
  content: "{0}在决斗中惜败于{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BrawlThemeConsumed"
  content: "{0}同意了{1}的决斗邀请。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BrawlThemeWin"
  content: "{0}在决斗场中战胜了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BrawlThemeDraw"
  content: "{0}与{1}进行了决斗，但无事发生。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_CurseBegin"
  content: "{0}对{1}实施了诅咒。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_CurseConsumed"
  content: "{1}因诅咒而淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_CurseStart"
  content: "{0}开始施法。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_CurseInterrupt"
  content: "{0}的施法被打断了。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Psychic_IconName"
  content: "通灵"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Psychic_IconName1"
  content: "返回"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_FinalVocationExp"
  content: "（{0}）{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_PreparationsVocation_TotalTimes"
  content: "总局数"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_PreparationsVocation_MvpTimes"
  content: "MVP次数"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_PreparationsVocation_WinTimes"
  content: "胜利局数"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_PreparationsVocation_SkillTip"
  content: "能力：{0}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_00"
  content: "天使的护盾可以抵挡哪种形式的伤害？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_00_Answer_0"
  content: "狼人的攻击"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_00_Answer_1"
  content: "炸弹狼的炸弹"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_00_Answer_2"
  content: "魔爆狼的魔爆弹"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_00_Answer_3"
  content: "以上全部"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_00_RightAnswer"
  content: "3"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_01"
  content: "侦探调查其他玩家时，可以知道哪些信息？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_01_Answer_0"
  content: "知道玩家的身份"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_01_Answer_1"
  content: "知道玩家的阵营"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_01_Answer_2"
  content: "知道玩家刚才干了什么"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_01_Answer_3"
  content: "以上全部"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_01_RightAnswer"
  content: "1"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_02"
  content: "以下身份，谁不可以使用密道？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_02_Answer_0"
  content: "消防员"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_02_Answer_1"
  content: "狼人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_02_Answer_2"
  content: "平民"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_02_Answer_3"
  content: "独狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_02_RightAnswer"
  content: "2"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_03"
  content: "以下身份，谁没有攻击技能？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_03_Answer_0"
  content: "勇士"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_03_Answer_1"
  content: "调皮鬼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_03_Answer_2"
  content: "独行侠"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_03_Answer_3"
  content: "赏金猎人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_03_RightAnswer"
  content: "1"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_04"
  content: "以下身份，谁的投票会计为两票？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_04_Answer_0"
  content: "主持人"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_04_Answer_1"
  content: "学者"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_04_Answer_2"
  content: "长老"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_04_Answer_3"
  content: "法医"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_04_RightAnswer"
  content: "0"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_05"
  content: "大明星被淘汰时，谁可以收到提示？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_05_Answer_0"
  content: "长老"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_05_Answer_1"
  content: "赌徒"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_05_Answer_2"
  content: "小丑"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_05_Answer_3"
  content: "伪装狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_05_RightAnswer"
  content: "0"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_06"
  content: "调皮鬼被淘汰后，可以对谁使用技能？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_06_Answer_0"
  content: "法医"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_06_Answer_1"
  content: "搬运工"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_06_Answer_2"
  content: "人偶师"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_06_Answer_3"
  content: "大力狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_06_RightAnswer"
  content: "3"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_07"
  content: "以下身份，谁可以在会议中淘汰他人？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_07_Answer_0"
  content: "赌徒"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_07_Answer_1"
  content: "臭鼬"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_07_Answer_2"
  content: "秘术狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_07_Answer_3"
  content: "刺客狼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_07_RightAnswer"
  content: "3"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_08"
  content: "铁蛋无法免疫那种形式的伤害？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_08_Answer_0"
  content: "警长的攻击"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_08_Answer_1"
  content: "赏金猎人的攻击"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_08_Answer_2"
  content: "炸弹狼的炸弹"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_08_Answer_3"
  content: "勇士的攻击"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_08_RightAnswer"
  content: "2"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_09"
  content: "以下身份，谁被投票出局时会获得胜利？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_09_Answer_0"
  content: "铁蛋"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_09_Answer_1"
  content: "小丑"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_09_Answer_2"
  content: "流浪汉"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_09_Answer_3"
  content: "哨子"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_607_Question_09_RightAnswer"
  content: "1"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_1"
  content: "手撕仙人掌"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_2"
  content: "鲱鱼生腌"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_3"
  content: "爆炒鹅卵石"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_4"
  content: "鸭屎香烤鸭"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_5"
  content: "草莓红烧肉"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_6"
  content: "苹果炒西瓜"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_7"
  content: "蒜香樱桃"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_8"
  content: "辣子榴莲壳"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_9"
  content: "糖醋开心果"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_10"
  content: "番茄炒月饼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_11"
  content: "红油麻薯"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_12"
  content: "橘子擂辣椒"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_13"
  content: "孟婆汤"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_14"
  content: "冰镇麻辣烫"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_15"
  content: "菠萝酸菜汁"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_Food_16"
  content: "巧克力豆汁"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_615_1"
  content: "经过我帅气的推理，\n你涉嫌用这几道菜毒害星宝！\t\t\t\t\t\t\t\t\t"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food_1"
  content: "羊肉水饺"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food_2"
  content: "柠檬炸鸡"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food_3"
  content: "脆鸡堡"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food_4"
  content: "咖喱鱼蛋"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food_5"
  content: "猪扒捞丁"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food_6"
  content: "手撕仙人掌"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food_7"
  content: "清蒸东星斑"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food_8"
  content: "麻婆布丁"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food_9"
  content: "九转大肠"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food_10"
  content: "酸豆角"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_1"
  content: "想吃些什么呢？"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food2_1"
  content: "二两米饭"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food2_2"
  content: "五两米饭"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_616_Food2_3"
  content: "一斤米饭"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_617_Book_1"
  content: "大侦探洁西卡"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_617_Book_2"
  content: "刺客狼的七宗罪"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_617_Book_3"
  content: "臭鼬野史"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_617_Book_4"
  content: "警长的自我修养"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_617_Book_5"
  content: "铁蛋是怎样炼成的"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_617_1"
  content: "请点击拿取《{0}》"
  switch: 1
}
rows {
  id: "UI_Chat_NR3E3_SelectTipNotSend"
  content: "还未轮到你发言，暂时不可发送消息"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GhostChase"
  content: "追击"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GhostEndChase"
  content: "结束"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GhostTips1"
  content: "目标丢失，追击失败"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GhostTips2"
  content: "请注意，你已被幽魂追击！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GhostTips3"
  content: "距离过远，追击失败"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GhostTips4"
  content: "目标已被淘汰，追击失败"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GhostTips5"
  content: "请注意，你已被幽魂追击！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_FlashWolf_IconName"
  content: "闪光弹"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Jump2MasterRoad"
  content: "来源：<NewChat_Yellow>大师之路</>中获取"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_614_Text_1"
  content: "1:{阿姨，你有没有发现可疑的同学？(InLevel_NR3E3_Task_614_Text_2|1)}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_614_Text_2"
  content: "2:{什么算可疑的？(InLevel_NR3E3_Task_614_Text_3)}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_614_Text_3"
  content: "1:{比如饭量特别大的那种(InLevel_NR3E3_Task_614_Text_4|0.65)(InLevel_NR3E3_Task_614_Text_7|0.35)}{比如看着星宝流口水那种(InLevel_NR3E3_Task_614_Text_5|0.65)(InLevel_NR3E3_Task_614_Text_7|0.35)}{比如有暴力倾向的那种(InLevel_NR3E3_Task_614_Text_6|0.65)(InLevel_NR3E3_Task_614_Text_7|0.35)}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_614_Text_4"
  content: "2:{有，兔叽叽一顿能吃20根胡萝卜！(InLevel_NR3E3_Task_614_Text_8)}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_614_Text_5"
  content: "2:{有，小新同学总是看着美女同学流口水！(InLevel_NR3E3_Task_614_Text_8)}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_614_Text_6"
  content: "2:{有，塞罗总是殴打小怪兽！(InLevel_NR3E3_Task_614_Text_8)}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_614_Text_7"
  content: "2:{没有！(END)}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Task_614_Text_8"
  content: "1:{...(END)}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_703_1"
  content: "所有平民<LobbyChatYellow>吃饭</>补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_703_2"
  content: "平民需要吃饭补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_PopNewIdentity_1"
  content: "解锁新身份"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_PopNewIdentity_2"
  content: "-玩法：谁是狼人-"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_PopNewIdentity_3"
  content: "你可以在游戏中随机或自选该身份了"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_EagleEye"
  content: "你可以透视观察到其他玩家的位置。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_EagleEye"
  content: "千里眼可以使用透视能力，观察到大范围内的其他玩家的位置。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Dream"
  content: "捕捉"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_EyeChase"
  content: "透视"
  switch: 1
}
rows {
  id: "NR3E3_VocationAbility_MeetingEmojiCD"
  content: "会议表情发送的太频繁了，请稍后再发送"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Hacker"
  content: "黑客可以在会议中看到每个玩家的投票对象。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Hacker"
  content: "你可以看到每个玩家的投票对象。"
  switch: 1
}
rows {
  id: "CustomRoom_NR3E3_Rule9"
  content: "游戏类型"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_PropSummon_SeekerTrap"
  content: "这是一个诱捕伪装者的道具"
  switch: 1
}
rows {
  id: "InLevel_NR3E1_PropSummon_HiderTrap"
  content: "这是一个诱捕搜捕者的道具"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChairSit"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChairLeave"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Jump2MasterRoad_2"
  content: "来源：<NewChat_Yellow>狼人通行证</>中获取"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  content: "来源：<NewChat_Yellow>兑换商店</>中获取"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Jump2MasterRoad_4"
  content: "来源：<NewChat_Yellow>奖杯征程</>中获取"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Jump2MasterRoad_5"
  content: "来源：<NewChat_Yellow>限时活动</>中获取"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Tracker_TrackingLost"
  content: "目标已淘汰"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Tracker"
  content: "追踪者每完成2个任务后，都可以短暂追踪到某个狼人或中立玩家的位置"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Tracker"
  content: "每当你完成2个任务后，将获得追踪能力"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle43"
  content: "追踪者"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Protected_FarAway"
  content: "距离过远，保护失败"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Protected_NotProtectedTarget"
  content: "缺少保护目标"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Defender"
  content: "保镖可以暂时保护一名玩家，在他身边时，该玩家不会被攻击淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Defender"
  content: "你可以保护别人，使他不会被攻击淘汰。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleTitle44"
  content: "保镖"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_InLevelFinalPointsTip"
  content: "获得<Yellow18>{0}</>，专精点数+{1}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_InLevelFinalPointsTip1"
  content: "<Yellow18>使用</>此身份，专精点数+{0}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_InLevelFinalPoints"
  content: "胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_HoleWolf"
  content: "密道狼可以原地挖出一个新的密道，它将与最近的密道相连着。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_HoleWolf"
  content: "你可以挖出新的密道。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleWolf_IconName"
  content: "密道"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HoleWolf_TempHoleName"
  content: "临时密道"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapName"
  content: "地图"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapName1"
  content: "古宅"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapName2"
  content: "暗夜堡"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapName3"
  content: "遗迹"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapName4"
  content: "学校"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapName5"
  content: "月球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapName6"
  content: "游乐园"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapName7"
  content: "牛牛街"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapName8"
  content: "冰湖村"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapName9"
  content: "精灵谷"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapDes1"
  content: "古朴典雅的庭院与建筑充满着国风韵味。\n古宅中会随机出现一些宝箱，里面有惊奇的能力哦。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapDes2"
  content: "静谧的夜晚，失踪的主人，这个城堡透着一丝阴冷的气息。\n暗夜堡中会随机出现传送门，他会通向哪里呢？"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapDes3"
  content: "原始森林中的一处遗迹，传闻是狼人先祖生活过的地方。\n遗迹中会随机出现迷雾区域，里面危机重重！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapDes4"
  content: "校园里每个角落都洋溢着青春的气息，这里是知识的殿堂。\n三五成群的学生遍布校园里，他们与玩家真假难辨。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapDes5"
  content: "这是月球上的科学基地，是星宝走向星辰大海的重要一步。\n穿上太空服，便可在基地外的低重力环境中探索。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapDes6"
  content: "这是一个充满欢乐和刺激的游乐场，诸多惊喜在等待着你。\n坐上云霄飞车，感受心跳的加速，或者浏览花车，享受微风的吹拂。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapDes7"
  content: "这是一个宁静舒适的社区。在牛牛街，你可以细细品味一杯咖啡，\n也可以与店员们聊天，听听他们的故事。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapDes8"
  content: "一座被皑皑白雪覆盖的小小村庄，纯净的似一尘不染。\n快叫上小伙伴一起，在这片冰雪中斗智斗勇吧。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleMapDes9"
  content: "精灵谷中，花草缤纷，溪流潺潺，仿佛藏着精灵的梦。\n神奇的草丛，可以藏身其中，不用担心被人发现。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleDoubleDes1"
  content: "当前为10人模式，有6名平民，2名狼人，2名中立阵营。\n每名玩家会有一名队友，两人一起协作交流。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleDoubleDes2"
  content: "同小队的两名玩家一定是相同阵营，两人之间互相知道身份，且会出生在一起。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleDoubleDes3"
  content: "在会议中，同小队玩家将一起进行发言。\n未轮到本小队发言时，玩家可以进行小队内的聊天交流。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleDoubleDes4"
  content: "平民与狼人阵营的获胜条件不变，中立小队中有一人达成胜利条件，则小队获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleDoubleMode1"
  content: "模式说明"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleDoubleMode2"
  content: "小队"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleDoubleMode3"
  content: "会议交流"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_RuleDoubleMode4"
  content: "胜利条件"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Plumber_IconName"
  content: "关闭密道"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Plumber"
  content: "管道工可以使用密道，也可以临时关闭一组密道"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Plumber"
  content: "你可以使用或关闭密道"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_UseHoleFailedReason_Close"
  content: "密道被关闭，无法使用"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Failed_NoClosedHole"
  content: "附近没有可关闭的管道"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_DreamcatcherInPackage1"
  content: "<gradeYellow_dreamDesc>抓到{0}个灵魂</>,则获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_DreamcatcherInPackage"
  content: "·捕梦者可以捕捉被淘汰的观战玩家；"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_UseHoleFailedReason_AlreadyClosed"
  content: "密道已被关闭，无法使用技能"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_DoubleHint1"
  content: "双人模式中，基础规则与常规模式相似。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_DoubleHint2"
  content: "你会拥有一名同阵营的队友，一起协作交流。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_DeedWolf"
  content: "契约狼被淘汰后会附身在其他狼人身上，使该狼人所有技能的冷却时间减少。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_DeedWolf"
  content: "你被淘汰后会附身到其他狼人身上。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_GetSeasonTheme"
  content: "{0}+{1} 今日已获得奖励（{2}/{3}）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_DeedWolf_DeedTips"
  content: "你附身在了{0}身上"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_DeedWolf_BeenDeedTips"
  content: "你被{0}附身了"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Juggler"
  content: "如果杂耍艺人在会议中投票给某个玩家，便可以在下个回合里攻击该玩家"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Juggler"
  content: "你投票给别人后，便可以攻击他。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_817Content1"
  content: "母上大人："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_817Content2"
  content: "今天是你的生日，全家都在一起为你庆生"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_817Content3"
  content: "还拍了全家福，大家都很开心"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_817Content4"
  content: "你在月球也记得许愿哦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_817Content5"
  content: "大哥："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_817Content6"
  content: "这是我自己亲手做的月饼，味道好极了"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_817Content7"
  content: "等你回来我给你露两手"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_817Content8"
  content: "二姐："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_817Content9"
  content: "家里的小兔啾啾生小小兔啦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildTask_817Content10"
  content: "你看，是不是很可爱呀"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Settings1"
  content: "双人模式"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_SkillName_EyesClosed"
  content: "闭眼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Ostrich"
  content: "鸵鸟可以闭上双眼，持续一段时间，若你被攻击，自己不会被淘汰，且可以看到该次攻击效果。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Ostrich"
  content: "你可以闭眼，期间不会被攻击淘汰。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task901"
  content: "平民们需要在限时内进行灭火，否则，狼人将直接获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task902"
  content: "迷雾将影响平民的视野，驱散迷雾后恢复正常。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task903"
  content: "平民需要吃饭补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task905"
  content: "需要在限时内找到药物，否则将立即被淘汰（狼人阵营不受生病影响）。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task1101"
  content: "平民们需要在限时内进行灭火，否则，狼人将直接获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task1102"
  content: "迷雾将影响平民的视野，驱散迷雾后恢复正常。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task1103"
  content: "平民需要吃饭补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task1105"
  content: "需要在限时内找到药物，否则将立即被淘汰（狼人阵营不受生病影响）。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task1301"
  content: "平民们需要在限时内进行灭火，否则，狼人将直接获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task1302"
  content: "迷雾将影响平民的视野，驱散迷雾后恢复正常。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task1303"
  content: "平民需要吃饭补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Task1305"
  content: "需要在限时内找到药物，否则将立即被淘汰（狼人阵营不受生病影响）。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem901"
  content: "点起大火，平民如果没有在<LobbyChatYellow>限时内完成灭火</>，则狼人直接获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem902"
  content: "所有平民将陷入迷雾，<LobbyChatYellow>视野受到影响</>。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem903"
  content: "所有平民<LobbyChatYellow>吃饭</>补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem905"
  content: "所有平民将生病，需要在<LobbyChatYellow>限时内寻找各自的药物</>，否则将直接淘汰。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem1101"
  content: "点起大火，平民如果没有在<LobbyChatYellow>限时内完成灭火</>，则狼人直接获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem1102"
  content: "所有平民将陷入迷雾，<LobbyChatYellow>视野受到影响</>。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem1103"
  content: "所有平民<LobbyChatYellow>吃饭</>补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem1105"
  content: "所有平民将生病，需要在<LobbyChatYellow>限时内寻找各自的药物</>，否则将直接淘汰。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem1301"
  content: "点起大火，平民如果没有在<LobbyChatYellow>限时内完成灭火</>，则狼人直接获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem1302"
  content: "所有平民将陷入迷雾，<LobbyChatYellow>视野受到影响</>。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem1303"
  content: "所有平民<LobbyChatYellow>吃饭</>补充体力后，才可以继续进行任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_UrgentTaskItem1305"
  content: "所有平民将生病，需要在<LobbyChatYellow>限时内寻找各自的药物</>，否则将直接淘汰。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskUrgentTaskDesInfo9901"
  content: "点起大火，平民如果没有在<LobbyChatYellow>限时内完成灭火</>，则狼人直接获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskUrgentTaskDesInfo9902"
  content: "所有平民将陷入迷雾，<LobbyChatYellow>视野受到影响</>。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskUrgentTaskDesInfo9903"
  content: "所有平民<LobbyChatYellow>吃饭</>补充体力后，才可以继续挖宝和搬运宝物。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskUrgentTaskDesInfo9905"
  content: "所有平民将生病，需要在<LobbyChatYellow>限时内寻找各自的药物</>，否则将直接淘汰。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskUrgentTaskContentInfo9901"
  content: "平民们需要在限时内进行灭火，否则，狼人将直接获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskUrgentTaskContentInfo9902"
  content: "迷雾将影响平民的视野，驱散迷雾后恢复正常。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskUrgentTaskContentInfo9903"
  content: "平民需要吃饭补充体力后，才可以继续挖宝和搬运宝物。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TaskUrgentTaskContentInfo9905"
  content: "需要在限时内找到药物，否则将立即被淘汰（狼人阵营不受生病影响）。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_SkillName_FirstAider_Rescue"
  content: "急救"
  switch: 1
}
rows {
  id: "NR3E_EndReason_111"
  content: "舞者与半数以上的玩家同时起舞"
  switch: 1
}
rows {
  id: "NR3E_EndReason_112"
  content: "快递员送达{0}份快递"
  switch: 1
}
rows {
  id: "NR3E_EndReason_112_HyperCore"
  content: "快递员送达6份快递"
  switch: 1
}
rows {
  id: "NR3E_EndReason_113"
  content: "密探成功猜测了指定角色的身份"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_SkillName_EyesOpen"
  content: "睁眼"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Ostrich_CutDown"
  content: "闭眼中{0}秒"
  switch: 1
}
rows {
  id: "UI_Report_NR3E3_Meeting_Tips1"
  content: "不能举报自己。"
  switch: 1
}
rows {
  id: "UI_Report_NR3E3_Meeting_Tips2"
  content: "请选择举报玩家"
  switch: 1
}
rows {
  id: "UI_Report_NR3E3_Meeting_Tips3"
  content: "请选择举报原因"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_FirstAider_PermanentDead"
  content: "彻底凉凉啦，不能急救了"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_FirstAider_NoTarget"
  content: "暂无可急救目标"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_BestDetective"
  content: "名侦探可以调查一名被淘汰的玩家，从而知道谁是凶手"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_BestDetective"
  content: "你调查被淘汰的玩家，可以知道凶手"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_BestDetective_Tips"
  content: "{0}号是凶手"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Ability_Hamster_VoteTip"
  content: "弃票将使下次投票数增加{0}票"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Ability_Hamster_MaxTip"
  content: "存票已达上限"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Hamster"
  content: "仓鼠可以在会议中存票，并在下次投票时全部投出；每弃票1次，可获得存票2张。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Hamster"
  content: "你可以在会议中存票并一次投出"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_SwordsMan"
  content: "侠客可以攻击别人且不受任何惩罚，每局游戏只能攻击1次。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_SwordsMan2"
  content: "你可以攻击别人1次。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_BlackWolf"
  content: "黑衣狼可以使所有人都变成黑衣人，持续一段时间"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_BlackWolf"
  content: "你可以使所有人都变成黑衣人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_BlackWolf_Widget_Name"
  content: "黑衣装"
  switch: 1
}
rows {
  id: "InLevel_NR3E_Hamster_IsGiveUpVoted"
  content: "   是否要放弃本轮的投票呢？\n<GoldYellow>（弃票将使下次投票数增加2票）</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_BestDetective_Tips1"
  content: "{0}号淘汰了自己"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_BestDetective_Tips2"
  content: "死于不明原因，没有凶手"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Decorate_AnimationPrepareSuitPropTips"
  content: "同时使用攻击动画<GoldYellow>[{0}]</>和报告动画<GoldYellow>[{1}]</>后将自动激活。"
  switch: 1
}
rows {
  id: "UI_NR3E1_ItemOwned"
  content: "已拥有"
  switch: 1
}
rows {
  id: "UI_NR3E1_ItemLimitTime"
  content: "限时活动"
  switch: 1
}
rows {
  id: "UI_NR3E1_ItemBuy"
  content: "{0}购买"
  switch: 1
}
rows {
  id: "UI_NR3E1_ItemEquiped"
  content: "已装备"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ShareGameFinish1"
  content: "狼人决胜时刻，智取MVP！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ShareGameFinish2"
  content: "看我狼人表演，小小{0}连胜，拿捏！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ShareGameFinish3"
  content: "元梦最强狼王，豪取{0}连胜！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ShareGameFinish4"
  content: "连胜停不下来，达成巅峰{0}连胜！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo1"
  content: "{0}号是不具备攻击能力的。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo2"
  content: "{0}号的身份一定是{1}。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo3"
  content: "{0}号的身份一定不是{1}。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo4"
  content: "{0}号的身份可能是{1}。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo5"
  content: "{0}号的身份可能不会是{1}。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo6"
  content: "{0}号和{1}号{2}一个阵营的。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo7"
  content: "{0}号没有动手的嫌疑。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo8"
  content: "目前还有{0}个狼人存活。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo9"
  content: "本轮开始{0}一个任务都未完成。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo10"
  content: "本回合被攻击倒地的玩家有{0}个。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo11"
  content: "还有{0}个有攻击能力的玩家存活在场。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo12"
  content: "狼人目前一共淘汰了{0}个玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo13"
  content: "狼人的紧急任务还在冷却中！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo14"
  content: "狼人的紧急任务随时可以使用啦！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo15"
  content: "现在还有{0}个平民存活。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo16"
  content: "到目前为止，中立阵营还有{0}个存活。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo17"
  content: "这就是上次完成迷雾任务{0}号的脚印。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo18"
  content: "这就是上次完成救火任务{0}号的脚印。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo19"
  content: "场上还有{0}个玩家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo20"
  content: "这就是在{1}出现过的{0}号脚印。"
  switch: 2
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo21"
  content: "有狼人在会议中被驱逐啦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo22"
  content: "上次会议中，有人对{0}号进行了猜测，猜测他身份是{1}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo23"
  content: "目前死亡的角色里有{0}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo24"
  content: "狼人正处于密道之中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo25"
  content: "目前{0}号正处于{1}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo26"
  content: "目前被淘汰的角色阵营里有{0}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo27"
  content: "刚刚，有狼人在{0}淘汰了{1}号"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo28"
  content: "目前被淘汰的角色里有带有攻击能力的的身份 "
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo29"
  content: "{0}号的技能目前正处于冷却中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo30"
  content: "{0}号拥有主动技能"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo31"
  content: "{0}号的尸体被销毁啦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo32"
  content: "场上目前还剩余{0}个平民，{1}个狼人，{2}个中立。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo33"
  content: "被淘汰的角色里包括{0}个狼人，{1}个平民，{2}个中立"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo34"
  content: "在本回合，其中一个狼人利用密道出现在了{0} "
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo35"
  content: "目前，处于{0}的某人是平民阵营"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo36"
  content: "有一名平民玩家误杀了其他平民"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo101"
  content: "{0}号是{1}，小心他！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo102"
  content: "{0}号玩家是{1}！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo103"
  content: "{0}号玩家是{1}！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo104"
  content: "这就是上次完成迷雾任务{0}号的脚印。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo105"
  content: "这就是上次完成救火任务{0}号的脚印。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo106"
  content: "{0}号和{1}号{2}一个阵营的。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo107"
  content: "目前有{0}个玩家倒在了狼人手中。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo108"
  content: "{0}号目前完成的普通任务数量最多。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo109"
  content: "{0}号在会议中投票给了你！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo110"
  content: "场上还有{0}个玩家存活。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo111"
  content: "{0}号刚刚完成了{1}任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo112"
  content: "这就是在{1}出现过的{0}号脚印。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo113"
  content: "中立阵营目前还存活{0}个玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo114"
  content: "{0}号在会议里选择了弃票。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo115"
  content: "其中一个狼人目前位于{0}。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo116"
  content: "{0}号是中立阵营的！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo117"
  content: "这就是淘汰{1}号的{0}号脚印！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo118"
  content: "本侦探留下的{0}本笔记还未被使用。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo119"
  content: "{0}号具有攻击能力。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo120"
  content: "{0}目前完成的任务数量最多"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo121"
  content: "有一名平民玩家误杀了其他平民"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo201"
  content: "{0}号具有攻击能力。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo202"
  content: "{0}号是不具备攻击能力的。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo203"
  content: "狼人目前在{0}附近。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo204"
  content: "{0}号和{1}号{2}一个阵营的。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo205"
  content: "目前有{0}个玩家倒在了狼人手中。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo206"
  content: "目前还有{0}个狼人存活。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo207"
  content: "这就是淘汰{1}号的{0}号脚印！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo208"
  content: "{0}号是中立阵营的！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo209"
  content: "场上还有{0}个玩家存活。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo210"
  content: "狼人的紧急任务可以使用啦。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo211"
  content: "狼人的紧急任务还在冷却中！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo212"
  content: "上次会议中，{0}号选择了弃票。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo213"
  content: "{0}号在上次会议中投票给了你！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo214"
  content: "本侦探留下的{0}本笔记还未被使用。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo215"
  content: "这就是上次完成迷雾任务{0}号的脚印。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo216"
  content: "这就是上次完成救火任务{0}号的脚印。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo217"
  content: "有狼人在会议中被驱逐啦"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo218"
  content: "上次会议中，有人对{0}号进行了猜测，猜测他身份是{1}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo219"
  content: "目前死亡的角色里有{0}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo220"
  content: "上次会议召开前，{0}号正处于{1}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo221"
  content: "目前死亡的角色里有{0}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo222"
  content: "{0}号倒地时，{1}号正处于{2}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo223"
  content: "{0}号倒地后，{1}号在不久后也倒地了"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo224"
  content: "有狼人在{0}管道钻出，淘汰了{1}号"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo225"
  content: "目前被淘汰的角色里有带有攻击能力的的身份 "
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo226"
  content: "本回合，{0}号在{1}使用了他的技能"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo227"
  content: "场上目前还剩余{0}个平民，{1}个狼人，{2}个中立。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo228"
  content: "被淘汰的角色里包括{0}个狼人，{1}个平民，{2}个中立"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo229"
  content: "在本回合，其中一个狼人利用密道出现在了{0}"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo230"
  content: "目前，处于{0}的某人是平民阵营"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextInfo231"
  content: "有一名平民玩家误杀了其他平民"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo1"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo2"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo3"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo4"
  content: "从目前手头的线索来看，我猜测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo5"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo6"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo7"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo8"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo9"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo10"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo11"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo12"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo13"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo14"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo15"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo16"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo17"
  content: "这个有点奇怪的脚印，我不会认错："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo18"
  content: "这个有点奇怪的脚印，我不会认错："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo19"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo20"
  content: "这个有点奇怪的脚印，我不会认错："
  switch: 2
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo21"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo22"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo23"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo24"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo25"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo26"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo27"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo28"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo29"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo30"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo31"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo32"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo33"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo34"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo35"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo36"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo101"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo102"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo103"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo104"
  content: "这个有点奇怪的脚印，我不会认错："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo105"
  content: "这个有点奇怪的脚印，我不会认错："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo106"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo107"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo108"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo109"
  content: "在会议里，我注意到了一些情况："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo110"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo111"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo112"
  content: "这个有点奇怪的脚印，我不会认错："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo113"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo114"
  content: "在会议里，我注意到了一些情况："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo115"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo116"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo117"
  content: "这个有点奇怪的脚印，我不会认错："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo118"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo119"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo120"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo121"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo201"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo202"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo203"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo204"
  content: "仔细检查现场后，我能很肯定地说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo205"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo206"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo207"
  content: "这个有点奇怪的脚印，我不会认错："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo208"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo209"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo210"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo211"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo212"
  content: "在会议里，我注意到了一些情况："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo213"
  content: "在会议里，我注意到了一些情况："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo214"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo215"
  content: "这个有点奇怪的脚印，我不会认错："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo216"
  content: "这个有点奇怪的脚印，我不会认错："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo217"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo218"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo219"
  content: "从目前手头的线索来看，我推测："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo220"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo221"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo222"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo223"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo224"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo225"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo226"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo227"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo228"
  content: "根据可靠线人来报，似乎："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo229"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo230"
  content: "从时间上来说，可以得出结论："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NotesTextClueInfo231"
  content: "仔细检查现场后，我能很肯定的说："
  switch: 1
}
rows {
  id: "Common_ItemUse"
  content: "使用"
  switch: 1
}
rows {
  id: "Common_Report"
  content: "报告"
  switch: 1
}
rows {
  id: "InLevel_SkillName_Attack"
  content: "攻击"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Fireworks"
  content: "在迷雾开启时，烟花师可以放一束烟花，暂时的驱散迷雾。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Fireworks"
  content: "你可以放烟花，在迷雾中进行照明。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo24"
  content: "在迷雾开启时，烟花师可以放一束烟花，暂时的驱散迷雾。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Fireworks_Widget_Name"
  content: "烟花"
  switch: 1
}
rows {
  id: "NR3E_E3_Ability_FailedReason_NoInFog"
  content: "没有迷雾出现"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SnowWolf_Widget_Name"
  content: "风雪"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_SnowWolf"
  content: "雪狼可以变成一团风雪快速移动，停止移动时将恢复正常。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_SnowWolf2"
  content: "你可以变成风雪进行移动。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTip_SnowWolf"
  content: "风雪状态，不可使用"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingPropItem_1"
  content: "是否消耗{0}{1}购买<MantelCardCount>{2}</>"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_NianNian"
  content: "年年可以召唤一只年兽，它会在一段时间里攻击附近的玩家，如果它成功攻击淘汰2名玩家，则年年获得胜利。\t"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo25"
  content: "年年可以召唤一只年兽，它会在一段时间里攻击附近的玩家，如果它成功攻击淘汰2名玩家，则年年获得胜利。\t"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleTarget_NianNian"
  content: "召唤年兽攻击别人来获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_NianNian"
  content: "召唤年兽攻击别人来获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NianBeast_Widget_Name"
  content: "年兽"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NianNian1"
  content: "年兽已淘汰玩家：{0}个"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NianNian2"
  content: "年年获胜啦"
  switch: 1
}
rows {
  id: "NR3E_EndReason_115"
  content: "年兽成功淘汰了{0}名玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NianNian4"
  content: "被年兽淘汰啦！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NianNian5"
  content: "年兽吞掉了一名玩家！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_AvengersTip"
  content: "复仇时刻！你获得了攻击能力"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Avengers"
  content: "当任意3名玩家被淘汰后，复仇者将获得攻击能力。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Avengers2"
  content: "3名玩家被淘汰后，你将获得攻击能力。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_NaoNao"
  content: "闹闹可以放一串鞭炮，放鞭炮的动静很大，如果成功放4次鞭炮，则闹闹获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_NaoNao2"
  content: "放4次鞭炮便可获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo23"
  content: "闹闹可以放一串鞭炮，放鞭炮的动静很大，如果成功放4次鞭炮，则闹闹获得胜利。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChildNaoNaoItem"
  content: "已成功放鞭炮：<childItemYellow>{0}次</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingPropItem_2"
  content: "（后续不再提醒）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_TwinAlive"
  content: "双子可以选择与他人进行共生。共生后，你们俩会互相知道身份，并且一人被淘汰后，另一人也自动被淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_TwinAlive"
  content: "你可以与他人进行共生。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_NoTwinAliveTarget"
  content: "缺少共生目标"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_TwinAliveFailed"
  content: "距离过远，共生失败"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_TwinLinkWithComplete"
  content: "你与{0}号进行了共生！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_TwinLinkIconName"
  content: "共生"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_TwinLinkTrigger"
  content: "共生的玩家被淘汰啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason23"
  content: "{0}胜利(成功放了4次鞭炮)"
  switch: 1
}
rows {
  id: "NR3E_EndReason_114"
  content: "闹闹成功放了4次鞭炮"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_NaoNaoSetOffFirecrackers"
  content: "{0}放了一次鞭炮。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_GiveAvengersAttack"
  content: "{0}获得了攻击技能。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_CantLinkWithPuppet"
  content: "无法与人偶共生"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_CantLinkWithTeammate"
  content: "无法和队友共生"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Kangaroo"
  content: "袋鼠可以把一名玩家装入口袋中，也可以随时把他放出来（会议开始时，口袋中的玩家会被自动放出来）"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Kangaroo"
  content: "你可以把一名玩家装入口袋中。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Kangaroo_IconName1"
  content: "装入"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Kangaroo_IconName2"
  content: "放出"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TrapWolfTrap"
  content: "{0}放置了陷阱。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_InTrap"
  content: "{1}触发了{0}放置的陷阱。"
  switch: 1
}
rows {
  id: "NR3E_TrapWolf_IconText"
  content: "陷阱"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTip_TrapWolf"
  content: "你踩中陷阱啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationTip_TrapWolf2"
  content: "陷阱被人触发了！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_TrapWolf"
  content: "陷阱狼可以摆放陷阱，其他玩家踩中陷阱后将被困住一些时间。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_TrapWolf2"
  content: "你可以利用陷阱困住其他玩家。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_TwinLinkWithComplete2"
  content: "{0}号与你进行了共生！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Cuttlefish"
  content: "攻击淘汰墨鱼的玩家会被喷上墨汁，下次会议开始后，墨汁才会消失。\t\t\t\t\t\t\t\t\t\t\t\t\t"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Cuttlefish"
  content: "淘汰你的玩家会被喷上墨汁。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Kangaroo_WidgetName"
  content: "放出"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule01_Title"
  content: "工作狂"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule02_Title"
  content: "救火达人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule03_Title"
  content: "除雾达人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule04_Title"
  content: "病毒发作"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule05_Title"
  content: "无情猎手"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule06_Title"
  content: "横扫千军"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule07_Title"
  content: "火眼金睛"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule08_Title"
  content: "制胜拼刀"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule09_Title"
  content: "轻松躺赢"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule10_Title"
  content: "狼人克星"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule11_Title"
  content: "夹缝求生"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule12_Title"
  content: "平民火种"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule13_Title"
  content: "发现狼人！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule14_Title"
  content: "正义判决"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule15_Title"
  content: "最佳辅助"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule16_Title"
  content: "金牌法医"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule17_Title"
  content: "完美防御"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule18_Title"
  content: "狼人入袋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule19_Title"
  content: "精准暗算"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule20_Title"
  content: "打扫现场"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule21_Title"
  content: "魔爆打击"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule22_Title"
  content: "精准投弹"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule23_Title"
  content: "一起跳舞"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule24_Title"
  content: "危险陷阱"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule01_ShortDescription"
  content: "累计完成{0}个普通任务"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule02_ShortDescription"
  content: "累计完成{0}次灭火"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule03_ShortDescription"
  content: "累计完成{0}次驱散迷雾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule04_ShortDescription"
  content: "生病淘汰{0}名玩家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule05_ShortDescription"
  content: "成功淘汰{0}名玩家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule06_ShortDescription"
  content: "成功淘汰{0}名玩家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule07_ShortDescription"
  content: "投票淘汰{0}名狼人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule08_ShortDescription"
  content: "攻击淘汰最后一名狼人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule09_ShortDescription"
  content: "全场第一个被淘汰后依然获胜"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule10_ShortDescription"
  content: "攻击淘汰全部狼人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule11_ShortDescription"
  content: "仅剩{0}名玩家时获胜"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule12_ShortDescription"
  content: "仅剩自己{0}位平民时获胜"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule13_ShortDescription"
  content: "调查发现{0}名狼人"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule14_ShortDescription"
  content: "成功进行{0}次判决"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule15_ShortDescription"
  content: "成功保护{0}次"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule16_ShortDescription"
  content: "成功调查{0}次"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule17_ShortDescription"
  content: "闭眼躲避{0}次攻击"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule18_ShortDescription"
  content: "装入狼人{0}次"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule19_ShortDescription"
  content: "成功进行{0}次暗算"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule20_ShortDescription"
  content: "藏匿{0}名被淘汰的玩家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule21_ShortDescription"
  content: "魔爆弹同时淘汰{0}名玩家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule22_ShortDescription"
  content: "闪光弹同时致盲{0}名玩家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule23_ShortDescription"
  content: "同时与{0}名玩家跳舞"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule24_ShortDescription"
  content: "使用陷阱困住{0}名玩家"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule01_LongDescription"
  content: "一局游戏中，累计完成{0}个普通任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule02_LongDescription"
  content: "一局游戏中，累计完成{0}个灭火任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule03_LongDescription"
  content: "一局游戏中，累计完成{0}个驱散迷雾任务。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule04_LongDescription"
  content: "使用一次生病，同时淘汰{0}名玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule05_LongDescription"
  content: "一局游戏中，累计淘汰{0}名玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule06_LongDescription"
  content: "一局游戏中，累计淘汰{0}名玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule07_LongDescription"
  content: "一局游戏中，累计投票淘汰{0}名狼人。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule08_LongDescription"
  content: "攻击淘汰最后一名狼人。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule09_LongDescription"
  content: "全场第一个被淘汰后依然获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule10_LongDescription"
  content: "攻击淘汰全部狼人。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule11_LongDescription"
  content: "全场仅剩{0}名玩家时，作为中立获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule12_LongDescription"
  content: "全场仅剩自己{0}位平民时，平民阵营获胜。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule13_LongDescription"
  content: "一局游戏中，累计调查发现{0}名狼人。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule14_LongDescription"
  content: "一局游戏中，累计成功进行{0}次判决。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule15_LongDescription"
  content: "一局游戏中，累计成功进行{0}次保护。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule16_LongDescription"
  content: "一局游戏中，累计成功进行{0}次调查。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule17_LongDescription"
  content: "一局游戏中，闭眼期间累计躲避{0}次攻击。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule18_LongDescription"
  content: "一局游戏中，累计将狼人装入口袋{0}次。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule19_LongDescription"
  content: "一局游戏中，累计成功进行{0}次暗算。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule20_LongDescription"
  content: "一局游戏中，累计藏匿{0}名被淘汰的玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule21_LongDescription"
  content: "魔爆弹同时淘汰{0}名玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule22_LongDescription"
  content: "闪光弹同时致盲{0}名玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule23_LongDescription"
  content: "同时与{0}名玩家跳舞。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HightlightMoment_Rule24_LongDescription"
  content: "一局游戏中，使用陷阱困住{0}名玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_VocationAbility_OpenRadioWnd"
  content: "未检测到麦克风设备，请先插入麦克风"
  switch: 1
}
rows {
  id: "NR3E_Vocation_NaoNao_IconText"
  content: "鞭炮"
  switch: 1
}
rows {
  id: "InLevel_Listener_Succ_Tips"
  content: "你监听了{0}号"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_NoValidListenerResponer"
  content: "该玩家已出局，无法监听"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ChoiceListenerResponer"
  content: "请选择一个目标"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift1"
  content: "灯笼"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift2"
  content: "皮手套"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift3"
  content: "萝卜刀"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift4"
  content: "沙漏"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift5"
  content: "手表"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift6"
  content: "运动鞋"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift7"
  content: "小圆盾"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift8"
  content: "生命球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift9"
  content: "电影票"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift10"
  content: "地图册"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift11"
  content: "指南针"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGift12"
  content: "草裙子"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc1"
  content: "灯笼可以扩大一些你在迷雾中的视野。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc2"
  content: "你完成任务时可以额外获得一点进度值。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc3"
  content: "攻击技能的冷却时间减少20%。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc4"
  content: "所有紧急任务的冷却时间减少10秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc5"
  content: "你的技能冷却时间减少15%。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc6"
  content: "你的移动速度增加15%。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc7"
  content: "小圆盾可以默默帮你抵挡1次攻击，仅本回合中有效。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc8"
  content: "生命球上持续显示着当前存活玩家的数量。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc9"
  content: "仅限下次会议中，你的投票数增加1"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc10"
  content: "你可以随时查看地图中的玩家分布。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc11"
  content: "当有人被淘汰时，你将获得黄箭头提示，仅可用2次。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SurpriseGiftDesc12"
  content: "当你周围没人且站立不动5秒后，你将变成一堆小草。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_OnlyOncePerGame"
  content: "本局只能领取一次"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_LittleBone"
  content: "完成{0}个任务后可复活"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_LittleBone2"
  content: "任务达成，你复活啦！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_LittleBone"
  content: "小骨被淘汰后，完成6个普通任务，便可复活。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_LittleBone"
  content: "被淘汰后，你可以完成6个任务来复活。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SmokeWolfAbilityName"
  content: "烟雾"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_SmokeWolf"
  content: "烟雾狼可以释放一团烟雾，每次释放时间越久，烟雾就越大，烟雾会在本回合内一直存在。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_SmokeWolf"
  content: "你可以释放出一团团的烟雾。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Prophet"
  content: "预言家"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Prophet1"
  content: "预言家可以在会议中查验其他玩家是不是狼人，每局游戏中最多查验2次。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Prophet2"
  content: "你可以在会议中查验别人是不是狼人。"
  switch: 1
}
rows {
  id: "NR3E_E3_Ability_FailedReason_VocationBlocked"
  content: "不允许选择的职业，连猜测按钮都没有"
  switch: 1
}
rows {
  id: "NR3E_E3_Ability_FailedReason_CampBlocked"
  content: "不允许选择的阵营，连猜测按钮都没有"
  switch: 1
}
rows {
  id: "NR3E_E3_Ability_FailedReason_VocationSideGuessBlocked"
  content: "猜测不能选择该职业"
  switch: 1
}
rows {
  id: "NR3E_E3_Ability_FailedReason_GameFinish"
  content: "游戏已结束"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_ControllerWolf"
  content: "傀儡狼可以在会议中猜测一名玩家的身份，若猜测成功，你可以在下个回合中控制该玩家进行攻击。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_ControllerWolf"
  content: "你可以在会议中猜测身份来控制别人。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ControllerWolf_IconName"
  content: "控制"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ControllerWolf_NoControlEntry"
  content: "没有角色可控制"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ControllerWolf_NoRoleControl"
  content: "角色已淘汰，无法操控"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ControllerWolf_ControlTips"
  content: "你正在控制{0}号"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ControllerWolf_BeControlTips"
  content: "你被傀儡狼控制啦！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_MeetingLeft_ControlWolf"
  content: "猜测成功后，可以在下回合中控制该玩家进行攻击。\n每个回合只能猜测1次；每个玩家每局游戏最多被控制1次。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Decorate_PropsTreasureLvLimit"
  content: "珍宝{0}级时解锁"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_EventItemNoWolfFaction"
  content: "非狼人阵营"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_3"
  content: "每回合可以进行2次保护。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_5"
  content: "攻击的冷却时间减少至15秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_6"
  content: "每局可以调查4次，略微减少调查时间。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_16"
  content: "完成任务的进度会额外增加。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_17"
  content: "每完成2个任务便增加1次攻击能力。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_27"
  content: "独行的判定范围会减少一些。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_35"
  content: "每局游戏可以进行3次判决。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_39"
  content: "通灵时的移动速度增加40%。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_41"
  content: "透视范围增加一些，持续时间增加至9秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_43"
  content: "追踪效果的持续时间增加至12秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_51"
  content: "闭眼持续时间增加至15秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_52"
  content: "可以救活20秒内被淘汰的玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_59"
  content: "每局游戏能进行2次攻击。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_61"
  content: "2名玩家被淘汰后，将激活攻击能力。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_66"
  content: "每回合可以使用2次装入技能。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_70"
  content: "每局游戏可以进行3次查验。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_71"
  content: "完成4个普通任务，便可复活。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_8"
  content: "伪装的持续时间增加至20秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_9"
  content: "每局游戏可以进行3次暗算。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_10"
  content: "炸弹的冷却时间减少至14秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_19"
  content: "隐身的持续时间增加至12秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_25"
  content: "搬运的冷却时间减少至15秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_26"
  content: "每回合可以使用10次魔爆弹。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_30"
  content: "每局游戏可进行3次封印。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_33"
  content: "攻击的冷却时间减少25%。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_37"
  content: "攻击的冷却时间减少25%。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_40"
  content: "攻击的冷却时间减少25%。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_50"
  content: "隐身时移动速度增加至50%。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_53"
  content: "舞蹈的持续时间增加至12秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_58"
  content: "黑衣持续时间增加至16秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_69"
  content: "陷阱的冷却时间减少至18秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_73"
  content: "控制技能的持续时间增加至30秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_72"
  content: "烟雾范围增加3米。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_13"
  content: "攻击的冷却时间减少25%。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_29"
  content: "装人的冷却时间减少至15秒。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_54"
  content: "舞蹈的范围增加一些。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_HyperCore_VocationDrs_55"
  content: "成功送6次快递即可获胜。"
  switch: 1
}
rows {
  id: "NR3E_E3_Ability_MeetingThrowProps_FailedReason_CoolDown"
  content: "互动道具使用的太频繁了，请稍后再使用"
  switch: 1
}
rows {
  id: "UI_InLevel_UI_InLevel_NR3E3_VocationSelect"
  content: "请选择你本局的身份（{0}）"
  switch: 1
}
rows {
  id: "UI_InLevel_UI_InLevel_NR3E3_VocationSelect1"
  content: "等待玩家选择身份（{0}）"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Decorate_Interaction_SubQualityName1"
  content: "专属"
  switch: 1
}
rows {
  id: "NR3E3_VocationAbility_MeetingThrowPropsName"
  content: "互动"
  switch: 1
}
rows {
  id: "NR3E3_VocationAbility_MeetingEmojiName"
  content: "表情"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_BrawlWolf"
  content: "当乱斗狼被投票驱逐时，会随机选择2名投票者，进行强制的快速决斗；若投票者决斗失败，则立刻被淘汰。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_BrawlWolf"
  content: "你被投票驱逐时将发起快速决斗。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Brawl_Begin"
  content: "乱斗时间：乱斗狼被驱逐前，将进行快速决斗"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ConspiratorsProgress_Hint"
  content: "参与投票驱逐非狼人玩家：{0}个"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleTarget_Conspirators"
  content: "参与投票驱逐2名非狼人玩家可获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Conspirators"
  content: "阴谋家每次可以投出2票，当他参与投票驱逐任意2名非狼人玩家，则获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Conspirators"
  content: "参与投票驱逐2名非狼人玩家可获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_XianZhiLevelUp"
  content: "水晶球升级了"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_XianZhiLevel2"
  content: "水晶球技能冷却时间变为{0}秒啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_XianZhiLevel3"
  content: "现在可以看到被淘汰的玩家啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_XianZhiLevel4"
  content: "现在可以所有存活玩家编号啦"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_XianZhi"
  content: "先知可以打开随身的水晶球，查看玩家分布。每完成3次常规任务，可以升级1次水晶球，最多升级2次，分别是：可查看倒地的玩家、可查看玩家编号。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_XianZhi"
  content: "你拥有可升级的水晶球，查看别人位置。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_XianZhi2"
  content: "你拥有可升级的水晶球，查看别人位置。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Rule3_RoleInfo26"
  content: "你拥有可升级的水晶球，查看别人位置。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_XianZhi"
  content: "水晶球"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_XianZhi2"
  content: "水晶球"
  switch: 1
}
rows {
  id: "NR3E_EndReason_116"
  content: "参与驱逐了{0}名非狼人玩家"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_LuckyStar"
  content: "小福星可以送给其他玩家一份礼物，礼物中包含几种随机道具，该玩家可以从中选择一个。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_LuckyStar1"
  content: "你可以送给别人一份礼物。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Tips1"
  content: "小福袋<Yellow16>（未送出）</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Tips2"
  content: "小福袋<Yellow16>（已送出）</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Tips3"
  content: "选择一名玩家，送出福袋，会随机出3种礼物供选择。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Tips4"
  content: "已送给{0}号玩家，他选择了礼物:{1}。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Gift1"
  content: "博学之书"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Gift2"
  content: "窃听器"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Gift3"
  content: "扳手"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Gift4"
  content: "指南针"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Gift5"
  content: "长老权杖"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_GiftDes1"
  content: "你完成常规任务时可以获得额外的任务进度。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_GiftDes2"
  content: "你可以在会议中看到每个玩家的投票对象。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_GiftDes3"
  content: "你可以钻入与钻出密道。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_GiftDes4"
  content: "当有人被淘汰时，你将获得黄箭头提示。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_GiftDes5"
  content: "你可以看到被投票驱逐的玩家的身份。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Tips5"
  content: "选择了"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Tips6"
  content: "小福星送你一份礼物，请选择："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_SkillName"
  content: "送福袋"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_XianZhiLevelUp"
  content: "{0}将水晶球升级为{1}级"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_XianZhiOpenMap"
  content: "{0}查看了水晶球"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Brawl_Open"
  content: "决斗场已经开放！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Brawl_CountsFailed"
  content: "本局游戏最多进行1次决斗"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Brawl_WaitResponse"
  content: "该玩家正在决斗邀请中"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Brawl_CantBrawlRequest"
  content: "本回合无法向该玩家请求决斗"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Brawl_BrawlFailed"
  content: "未知原因，决斗失效啦"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_BlackList"
  content: "选择屏蔽一个{0}阵营的身份"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Brawl_BrawlWolf_Draw"
  content: "平局"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Tips_Info"
  content: "选择了"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_GiftDesColor1"
  content: "你完成常规任务时可以获得<Yellow19>额外的任务进度</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_GiftDesColor2"
  content: "你可以在会议中看到<Yellow19>每个玩家的投票对象</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_GiftDesColor3"
  content: "你可以<Yellow19>钻入与钻出密道</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_GiftDesColor4"
  content: "当有人被淘汰时，<Yellow19>你将获得黄箭头提示</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_GiftDesColor5"
  content: "你可以看到被投票<Yellow19>驱逐的玩家的身份</>"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_luckystar_Name"
  content: "送礼物"
  switch: 1
}
rows {
  id: "UI_NR3E3_BlackList_AlreadyBlocked"
  content: "该身份已屏蔽"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_LuckyStar"
  content: "{0}将礼物送给了{1}。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_LuckyStarSelect"
  content: "{0}选择了{1}作为礼物。"
  switch: 1
}
rows {
  id: "UI_NR3E3_BlackList_UnselectVocation"
  content: "请选择身份"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SkillProgress_Default"
  content: "释放中"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_SkillProgress_FogSneak"
  content: "结束隐身"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Brawl_BattleTextA"
  content: "等待双方出拳"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_Brawl_BattleTextB"
  content: "等待中"
  switch: 1
}
rows {
  id: "UI_NR3E3_BlackList_VocationExpired"
  content: "该共享身份已过期"
  switch: 1
}
rows {
  id: "UI_PracticeGM_Hint1"
  content: "AI停止行动"
  switch: 1
}
rows {
  id: "UI_PracticeGM_Hint2"
  content: "AI开始行动"
  switch: 1
}
rows {
  id: "UI_PracticeGM_Hint3"
  content: "技能冷却时间减少"
  switch: 1
}
rows {
  id: "UI_PracticeGM_Hint4"
  content: "技能冷却时间恢复"
  switch: 1
}
rows {
  id: "UI_PracticeGM_Hint5"
  content: "身份已显示"
  switch: 1
}
rows {
  id: "UI_PracticeGM_Hint6"
  content: "身份已隐藏"
  switch: 1
}
rows {
  id: "UI_PracticeGM_Hint7"
  content: "当前没有可完成的任务"
  switch: 1
}
rows {
  id: "UI_PracticeGM_Hint8"
  content: "其他紧急任务生效中，无法开启"
  switch: 1
}
rows {
  id: "UI_PracticeGM_Hint9"
  content: "当前任务数量已达上限。"
  switch: 1
}
rows {
  id: "UI_PracticeGM_Hint10"
  content: "不可选择已淘汰的目标"
  switch: 1
}
rows {
  id: "UI_InLevel_BrawlLoseSenceTips"
  content: "决斗失败，被淘汰了"
  switch: 1
}
rows {
  id: "UI_DoubleAttackWolf_IconName"
  content: "短刀"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_DoubleAttackWolf"
  content: "双刀狼拥有一个额外的攻击技能，冷却时间较长。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_DoubleAttackWolf"
  content: "你拥有一个额外的攻击技能。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_StingWolf"
  content: "毒刺可以钉刺其他玩家，该玩家几秒后便中毒僵在原地，需要其他玩家触碰来解救；累计钉到2个平民和1个狼人便可获胜。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_StingWolf"
  content: "钉刺指定的玩家来获得胜利。"
  switch: 1
}
rows {
  id: "UI_StingWolf_IconName"
  content: "毒刺"
  switch: 1
}
rows {
  id: "UI_StingWolf_FailedTips"
  content: "缺少钉刺目标"
  switch: 1
}
rows {
  id: "UI_NR3E3_BlackList_Tips1"
  content: "在游戏对局中，你将不会被随机分配到这些被屏蔽的身份。"
  switch: 1
}
rows {
  id: "UI_NR3E3_BlackList_Tips2"
  content: "屏蔽身份不影响身份卡自选、共享身份等其他功能。"
  switch: 1
}
rows {
  id: "UI_NR3E3_BlackList_Tips3"
  content: "随着月卡等级的提升，你可以屏蔽更多的身份。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Decorate_PropsMonthCardLimit"
  content: "开通月卡后解锁"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent1"
  content: "寻宝小队集结，出发：秘境寻宝！平民们的任务是搜寻并上交各种宝物。"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent2"
  content: "搜寻足够价值的宝物，或驱逐、淘汰所有狼人，均可取胜。"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent3"
  content: "灵魂状态"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent4"
  content: "被淘汰的玩家会变成灵魂状态，在游戏中观战。"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent5"
  content: "失落的海岛危机四伏，埋藏的宝物背后有什么秘密？"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent6"
  content: "寻宝"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent7"
  content: "搜寻宝物"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent8"
  content: "上交宝物"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent9"
  content: "营地小店"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent10"
  content: "使用古银币在营地小店购买道具，道具有各种作用。"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent11"
  content: "扫描仪"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent12"
  content: "使用扫描仪，可以观测附近的宝物与玩家。"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent13"
  content: "所有人可搜寻藏宝点，挖掘后可掉落随机宝物。"
  switch: 1
}
rows {
  id: "UI_NR3E3_NewRuleContent14"
  content: "将搜寻到的宝物交给吴小宝，会积累宝物进度并得到古银币奖励。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Decorate_PropsMonthCardLimit2"
  content: "月卡已过期，无法购买"
  switch: 1
}
rows {
  id: "InLevel_NR3ERoleTarget_StingWolf"
  content: "钉刺指定的玩家来获得胜利。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Decorate_InteractionItem"
  content: "限时试用"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Decorate_EmoteMonthCardLimit"
  content: "会议表情已过期，无法使用"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_Decorate_EmoteMonthCardLimit2"
  content: "已过期"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_MonthCardOpenMax"
  content: "月卡剩余时间大于360天，不可以续费"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_MonthCardOpenConfirm"
  content: "是否消耗{0}{1}购买<MantelCardCount>{2}</>"
  switch: 1
}
rows {
  id: "NR3E_EndReason_117_Tips"
  content: "<YellowCurrencytTip>{0}</>钉刺了指定玩家"
  switch: 1
}
rows {
  id: "NR3E_EndReason_117"
  content: "毒刺钉刺了指定玩家"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_CurseWolf"
  content: "咒术狼可以靠近别人并留下咒印，随后在任意地方施法10秒，诅咒该玩家使其死亡"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_CurseWolf"
  content: "你可以远程诅咒并淘汰别人"
  switch: 1
}
rows {
  id: "UI_NR3E3_CurseWolf_CurseIconName"
  content: "咒印"
  switch: 1
}
rows {
  id: "UI_NR3E3_CurseWolf_ConsumeCurseIconName"
  content: "诅咒"
  switch: 1
}
rows {
  id: "UI_InLevel_KillByCurse"
  content: "你被咒术狼诅咒了"
  switch: 1
}
rows {
  id: "UI_InLevel_Teacher_Icon"
  content: "讲课"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Teacher"
  content: "讲师可以对其他玩家传授知识，使他在本回合内完成任务时获得额外的任务进度。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Teacher"
  content: "你可以帮助别人获得更多的任务进度。"
  switch: 1
}
rows {
  id: "UI_FailedReason_MaxTeachCounts"
  content: "本回合最多对他讲课3次"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_Teach"
  content: "{0}对{1}进行了授课。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_MonthCardGiveMax"
  content: "月卡剩余时间大于360天，不可以赠送"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_MonthCardAskMax"
  content: "月卡剩余时间大于360天，不可以索要"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_MonthCardLocked"
  content: "使用失败，请先解锁月卡功能"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_StartView1"
  content: "寻宝小队来到了神秘的海岛，平民们的任务是在场景中搜寻各种宝物，并搬运回营地上交给吴小宝。"
}
rows {
  id: "NR3E3_ScoreTips_CommonTask"
  content: "完成各类任务，将获得少量积分。"
  switch: 1
}
rows {
  id: "NR3E3_ScoreTips_TreasureHunt_Common"
  content: "搜寻各类宝物，将获得少量积分。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text1"
  content: "目标"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text2"
  content: "淘汰平民玩家，阻止平民收集到足够的宝物。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text3"
  content: "宝物价值："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text4"
  content: "平民玩家共同<TreasureHuntYellow>搜集到价值{0}</>的宝物，便可<TreasureHuntYellow>获得胜利</>。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text5"
  content: "在场景中搜寻，找到并挖掘出宝物。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text6"
  content: "将宝物搬运到<TreasureHuntYellow>中间营地吴小宝</>那进行上交。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text7"
  content: "不同的宝物价值也不同。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text8"
  content: "上交宝物，可以获得古银币，在吴小宝那里可以购买道具。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text9"
  content: "丢弃"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text10"
  content: "确定要丢弃{0}吗？"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text11"
  content: "你将失去这个道具，并且无法找回。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text12"
  content: "开始寻宝"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text13"
  content: "秘境寻宝"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text14"
  content: "价值："
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureHunt_Text15"
  content: "搜寻并上交总价值{0}的宝物，或淘汰所有狼人。"
  switch: 1
}
rows {
  id: "NR3E3_TreasureHunt_Skill1008Tips"
  content: "已处于紧急任务阶段"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_BlackList_MonthCardExpired"
  content: "月卡已过期，无法使用身份屏蔽功能"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TreasureHuntAddItem"
  content: "{0}购买了道具<Highlight24>[{1}]</>。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TreasureHuntUseItem"
  content: "{0}使用了道具<Highlight24>[{1}]</>。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TreasureHuntDistardItem"
  content: "{0}丢弃了道具<Highlight24>[{1}]</>。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_FinishCommonExcavate"
  content: "{0}完成了挖掘（<Highlight24>{1}</>）。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_HandTreasure"
  content: "{0}上交了宝物<Highlight24>[{1}]</>（价值{2}）。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_GetTreasureHuntCoins"
  content: "{0}获得了<Highlight24>[{1}]</>古银币。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_DestoryTreasure"
  content: "{0}破坏了宝物<Highlight24>[{1}]</>。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TreasureHuntProgress"
  content: "总寻宝进度{0}%。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TreasureHuntProgressMax"
  content: "寻宝进度已达标，平民阵营胜利"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_ChickenMonsterAttact"
  content: "野怪攻击了{0}。"
}
rows {
  id: "UI_Preparations_NR3E3_EasterEgg_MonthCardExpired"
  content: "月卡已过期，无法开启彩蛋局"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Agent"
  content: "特工有一个指定身份的目标，攻击淘汰该目标，便获得胜利。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Agent"
  content: "攻击淘汰你的目标可获得胜利"
  switch: 1
}
rows {
  id: "NR3E_EndReason_118_Tips"
  content: "<YellowCurrencytTip>{0}</>成功淘汰了目标"
  switch: 1
}
rows {
  id: "NR3E_EndReason_118"
  content: "特工成功淘汰了目标"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_RoloTarget_Agent"
  content: "淘汰指定的玩家来获得胜利"
  switch: 1
}
rows {
  id: "NR3E3_TreasureHunt_SkillDigName"
  content: "挖宝"
  switch: 1
}
rows {
  id: "NR3E3_TreasureHunt_SkillCarryName"
  content: "搬起"
  switch: 1
}
rows {
  id: "NR3E3_TreasureHunt_SkillDropName"
  content: "放下"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_TrampKillPuppet"
  content: "{0}将{1}装入了麻袋淘汰了。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ScannerDes"
  content: "扫描周围，可以看到附近的宝物与玩家。"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_ScannerDes1"
  content: "前往这里，可以上交宝物和购买道具。"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_BattleEndReason28"
  content: "{0}胜利(成功淘汰了指定目标)"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_ChickenMonster_DeathNotify"
  content: "你被巨鸟淘汰了"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureNPC_Tips1"
  content: "天气真好呀！！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_TreasureNPC_Tips2"
  content: "挖到宝物拉！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E0_GameFinish1"
  content: "{0}个奖励可以领取"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_LieRenSelfTargetTip"
  content: "获得攻击与追踪能力！"
  switch: 1
}
rows {
  id: "UI_InLevel_NR3E3_LieRenHunterTargetTip"
  content: "小心！猎人追踪到了你！"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationDes_Weizhuang"
  content: "伪装者可以攻击淘汰一名玩家，从而获得该玩家的身份与能力，并加入到他的阵营之中"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_VocationIntro_Weizhuang"
  content: "你可以淘汰一名玩家并获得他的身份"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_OB_KeyEvents_LieRenTarget"
  content: "{0}追踪了{1}"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting9"
  content: "已淘汰频道"
  switch: 1
}
rows {
  id: "InLevel_NR3E3_Meeting10"
  content: "已淘汰"
  switch: 1
}

com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_主表.xlsx sheet:活动-臻藏卡池
rows {
  raffleId: 5301
  name: "月夜歌吟"
  startTime {
    seconds: 1707408000
  }
  endTime {
    seconds: 1713455999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5301
  }
  dailyLimit: 300
  maxLimit: 999999
  textRuleId: 110
  text: "皎皎月华，流淌入夜"
  lowestVersion: "1.2.80.1"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 60
    atDraws: 120
    atDraws: 180
    giftIds: 310201
    giftIds: 310202
    giftIds: 310203
    giftIds: 310204
    giftIds: 310205
  }
  jumpIds: 25
  commodityIds: 119995
  commodityIds: 119996
  commodityIds: 119997
  showStartTime {
    seconds: 1707408000
  }
  showEndTime {
    seconds: 1713455999
  }
  isShow: true
}
rows {
  raffleId: 5302
  name: "霜天冰雨"
  startTime {
    seconds: 1712160000
  }
  endTime {
    seconds: 1726761599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5302
  }
  dailyLimit: 300
  maxLimit: 999999
  textRuleId: 132
  text: "寒霜箭影 晶花盛放"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 60
    atDraws: 120
    atDraws: 180
    giftIds: 310201
    giftIds: 310202
    giftIds: 310203
    giftIds: 310204
    giftIds: 310205
  }
  jumpIds: 25
  commodityIds: 120021
  commodityIds: 120022
  showStartTime {
    seconds: 1712160000
  }
  showEndTime {
    seconds: 1726761599
  }
  isShow: true
}
rows {
  raffleId: 5303
  name: "永恒之誓"
  startTime {
    seconds: 1715875200
  }
  endTime {
    seconds: 1722441599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5303
  }
  dailyLimit: 500
  maxLimit: 999999
  textRuleId: 148
  lowestVersion: "1.2.100.1"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 60
    atDraws: 120
    atDraws: 180
    giftIds: 310213
    giftIds: 310214
    giftIds: 310215
    giftIds: 310216
    giftIds: 310217
  }
  jumpIds: 25
  commodityIds: 120034
  commodityIds: 120035
  commodityIds: 120036
  commodityIds: 120037
  viewIndex: 21
  viewIndex: 11
  viewIndex: 19
  viewIndex: 14
  viewIndex: 9
  viewIndex: 7
  viewIndex: 16
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 10
  viewIndex: 12
  viewIndex: 8
  viewIndex: 13
  viewIndex: 18
  viewIndex: 15
  viewIndex: 20
  viewIndex: 17
  showStartTime {
    seconds: 1715875200
  }
  showEndTime {
    seconds: 1722441599
  }
  isShow: true
  drawOverState: 2
}
rows {
  raffleId: 5304
  name: "夏夜绮梦"
  startTime {
    seconds: 1723737600
  }
  endTime {
    seconds: 1730995199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5304
  }
  dailyLimit: 500
  maxLimit: 999999
  textRuleId: 211
  text: "<SwingSubViewDrowTips>每十次</>祈愿必得外观或非凡以上奖励"
  lowestVersion: "**********"
  milestone {
    atDraws: 20
    atDraws: 40
    atDraws: 80
    atDraws: 120
    atDraws: 160
    giftIds: 310030
    giftIds: 310031
    giftIds: 310032
    giftIds: 310033
    giftIds: 310034
  }
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 8
  previewTag: "Camera_Ornament8"
  showStartTime {
    seconds: 1723737600
  }
  showEndTime {
    seconds: 1730995199
  }
  isShow: true
  relateUmgSetting: "15;UI_Common_PackageRewardView"
  cratesItemId: 217
  cratesItemNum: 300
  cratesCommodityIds: 8377
  cratesCommodityIds: 8378
  firstTimeTaskId: 500161
  defaultPreviewItemId: 640035
}
rows {
  raffleId: 5305
  name: "长相思"
  startTime {
    seconds: 1721923200
  }
  endTime {
    seconds: 1724342399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5305
  }
  dailyLimit: 300
  maxLimit: 999999
  textRuleId: 206
  text: "前80次祈愿必得非凡时装"
  lowestVersion: "1.3.12.47"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 50
    atDraws: 80
    atDraws: 120
    giftIds: 310229
    giftIds: 310230
    giftIds: 310231
    giftIds: 310232
    giftIds: 310233
  }
  jumpIds: 25
  commodityIds: 120034
  commodityIds: 120035
  commodityIds: 120036
  commodityIds: 120037
  viewIndex: 13
  viewIndex: 12
  viewIndex: 11
  viewIndex: 10
  viewIndex: 9
  viewIndex: 8
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 14
  viewIndex: 15
  viewIndex: 16
  showStartTime {
    seconds: 1721923200
  }
  showEndTime {
    seconds: 1724342399
  }
  isShow: true
  drawOverState: 2
  relateUmgSetting: "5;UI_Lottery_LostForever_ChoiceView#3;UI_Lottery_GloryRoadPanel"
}
rows {
  raffleId: 5306
  name: "凤求凰"
  startTime {
    seconds: 1723132800
  }
  endTime {
    seconds: 1730390399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5306
  }
  dailyLimit: 500
  maxLimit: 999999
  textRuleId: 208
  lowestVersion: "1.3.12.90"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 60
    atDraws: 120
    atDraws: 180
    giftIds: 310236
    giftIds: 310237
    giftIds: 310238
    giftIds: 310239
    giftIds: 310240
  }
  jumpIds: 25
  commodityIds: 120073
  commodityIds: 120074
  commodityIds: 120075
  commodityIds: 120076
  viewIndex: 21
  viewIndex: 20
  viewIndex: 14
  viewIndex: 18
  viewIndex: 16
  viewIndex: 12
  viewIndex: 11
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  viewIndex: 10
  viewIndex: 13
  viewIndex: 17
  viewIndex: 19
  viewIndex: 15
  viewIndex: 23
  viewIndex: 22
  showStartTime {
    seconds: 1723132800
  }
  showEndTime {
    seconds: 1730390399
  }
  isShow: true
  drawOverState: 2
  relateUmgSetting: "5;UI_Lottery_QiXiFestival_ChoiceView"
}
rows {
  raffleId: 5307
  name: "月夜歌吟"
  startTime {
    seconds: 1730131200
  }
  endTime {
    seconds: 1731859199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5307
  }
  dailyLimit: 300
  maxLimit: 999999
  textRuleId: 110
  text: "皎皎月华，流淌入夜"
  lowestVersion: "1.3.26.33"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 60
    atDraws: 120
    atDraws: 180
    giftIds: 310201
    giftIds: 310202
    giftIds: 310203
    giftIds: 310204
    giftIds: 310205
  }
  jumpIds: 25
  commodityIds: 119995
  commodityIds: 119996
  commodityIds: 119997
  showStartTime {
    seconds: 1730131200
  }
  showEndTime {
    seconds: 1731859199
  }
  isShow: true
}
rows {
  raffleId: 5308
  name: "幻音喵境"
  startTime {
    seconds: 1749744000
  }
  endTime {
    seconds: 1755791999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5308
  }
  dailyLimit: 30
  maxLimit: 999999
  textRuleId: 324
  text: "<ActivationSubView5Yellow>每十次</>祈愿必得外观或非凡以上奖励"
  lowestVersion: "1.3.88.155"
  milestone {
    atDraws: 20
    atDraws: 40
    atDraws: 80
    atDraws: 120
    atDraws: 160
    giftIds: 310333
    giftIds: 310334
    giftIds: 310335
    giftIds: 310336
    giftIds: 310337
  }
  previewTag: "Camera_Ornament11"
  showStartTime {
    seconds: 1749744000
  }
  showEndTime {
    seconds: 1755791999
  }
  isShow: true
  animType: 4
  relateUmgSetting: "15;UI_Common_PackageRewardView"
  cratesItemId: 227
  cratesItemNum: 300
  cratesCommodityIds: 9529
  cratesCommodityIds: 9530
  firstTimeTaskId: 500377
  defaultPreviewItemId: 640169
  cratesBounceItems {
    itemId: 320137
    itemNum: 1
  }
  previewScaleThenSetPos: true
  previewActorDress: "400100|401850"
}

--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_NR3E.xlsx: 伪装特效

local data = {
[241201] = {
id = 241201,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.36.1",
quality = 4,
name = "伪装者特效：默认",
desc = "【躲猫猫】玩法中伪装者的默认特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_000",
picture = "CDN:T_E3_PropsIcon_001",
getWay = "默认获得",
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_NR3E1_Player_Transfiguration_426",
scale = 100
},
materialPath = "MI_NR3E_EF_Invisible_Inst",
soundId = "2066"
},
pack2 = {
effect = {
path = "FX_NR3E1_Player_invisible_429",
offset = "0,0,0"
},
soundId = "2067"
},
pack3 = {
effect = {
path = "FX_NR3E1_Player_Invisible_422",
offset = "0,0,0"
},
soundId = "2068"
},
pack4 = {
effect = {
scale = 100,
offset = "0,0,0"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241202] = {
id = 241202,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.36.1",
quality = 2,
name = "伪装者特效：繁花",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_001",
picture = "CDN:T_E1_Icon_DisguiserVFX_001",
getWay = "活动获得",
jumpId = {
457
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise1_Tran_GHX_401",
scale = 100
},
materialPath = "MI_NR3E1_Disguise1_Invisible_101",
soundId = "51035"
},
pack2 = {
effect = {
path = "FX_P_NR3E1_Disguise1_Trail_GHX_403",
offset = "0,0,50"
},
soundId = "51036"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise1_Inv_GHX_402",
offset = "0,0,0"
},
soundId = "51037"
},
pack4 = {
effect = {
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241203] = {
id = 241203,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.36.1",
quality = 2,
name = "伪装者特效：烈焰",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_002",
picture = "CDN:T_E1_Icon_DisguiserVFX_002",
getWay = "活动获得",
jumpId = {
456
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731945600
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise2_Tran_GHX_401",
scale = 100
},
materialPath = "MI_NR3E1_Disguise2_Noise_SWB_402",
soundId = "51038"
},
pack2 = {
effect = {
path = "FX_P_NR3E1_Disguise2_Trail_GHX_403",
offset = "0,0,50"
},
soundId = "51039"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise2_Inv_GHX_402",
offset = "0,0,0"
},
soundId = "51040"
},
pack4 = {
effect = {
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241204] = {
id = 241204,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.36.1",
quality = 2,
name = "伪装者特效：永恒誓言",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_003",
picture = "CDN:T_E1_Icon_DisguiserVFX_003",
getWay = "活动获得",
jumpId = {
456
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise3_Tran_ZWY_401_401",
scale = 100
},
materialPath = "MI_NR3E1_Disguise3_Depth_ZWY_401",
soundId = "51041"
},
pack2 = {
effect = {
path = "FX_P_NR3E1_Disguise3_Trail_ZWY_403",
offset = "0,0,50"
},
soundId = "51042"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise3_Inv_ZWY_402",
offset = "0,0,0"
},
soundId = "51043"
},
pack4 = {
effect = {
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241205] = {
id = 241205,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.68.1",
quality = 2,
name = "伪装者特效：恭喜发财",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_004",
picture = "CDN:T_E1_Icon_DisguiserVFX_003",
getWay = "活动获得",
jumpId = {
543
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise4_Tran_GHX_401",
scale = 100
},
materialPath = "MI_NR3E1_Disguise4_Invisible_101",
soundId = "51051"
},
pack2 = {
effect = {
path = "FX_P_NR3E1_Disguise4_Trail_GHX_403",
offset = "0,0,50"
},
soundId = "51052"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise4_Inv_GHX_402",
offset = "0,0,0"
},
soundId = "51053"
},
pack4 = {
effect = {
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241206] = {
id = 241206,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.68.1",
quality = 2,
name = "伪装者特效：暴风雪",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_005",
picture = "CDN:T_E1_Icon_DisguiserVFX_003",
getWay = "活动获得",
jumpId = {
544
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise5_Tran_HGZ_701",
scale = 100
},
materialPath = "MI_NR3E1_Disguise5_Com_Invisible_HGZ_701",
soundId = "51048"
},
pack2 = {
effect = {
path = "FX_P_NR3E1_Disguise5_Trail_HGZ_703",
offset = "0,0,50"
},
soundId = "51049"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise5_Inv_HGZ_702",
offset = "0,0,0"
},
soundId = "51050"
},
pack4 = {
effect = {
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241207] = {
id = 241207,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.78.1",
quality = 2,
name = "伪装者特效：墨染身",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_006",
picture = "CDN:T_E1_Icon_DisguiserVFX_003",
getWay = "活动获得",
jumpId = {
583
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise6_Tran_LEX_401",
scale = 100
},
materialPath = "MI_NR3E1_Disguise6_Com_Invisible_HGZ_701",
soundId = "51054"
},
pack2 = {
effect = {
path = "FX_P_NR3E1_Disguise6_Trail_LEX_403",
offset = "0,0,50"
},
soundId = "51055"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise6_Inv_LEX_402",
offset = "0,0,0"
},
soundId = "51056"
},
pack4 = {
effect = {
path = "FX_P_NR3E1_Disguise6_Trail_LEX_404 ",
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241208] = {
id = 241208,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.78.1",
quality = 2,
name = "伪装者特效：雨后霓虹",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_007",
picture = "CDN:T_E1_Icon_DisguiserVFX_003",
getWay = "活动获得",
jumpId = {
584
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise7_Tran_XJP_401",
scale = 100
},
materialPath = "MI_NR3E1_Disguise7_Invisible_101",
soundId = "51057"
},
pack2 = {
effect = {
path = "FX_P_NR3E1_Disguise7_Trail_XJP_403",
offset = "0,0,50"
},
soundId = "51058"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise7_Inv_XJP_402",
offset = "0,0,0"
},
soundId = "51059"
},
pack4 = {
effect = {
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241209] = {
id = 241209,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.88.1",
quality = 2,
name = "伪装者特效：赤芒晶核",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_008",
picture = "CDN:T_E1_Icon_DisguiserVFX_003",
getWay = "活动获得",
jumpId = {
670
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise8_Tran_XJP_401",
scale = 100
},
materialPath = "MI_NR3E1_Disguise8_Invisible_101",
soundId = "51064"
},
pack2 = {
effect = {
path = "FX_P_NR3E1_Disguise8_Trail_XJP_403",
offset = "0,0,50"
},
soundId = "51065"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise8_Inv_XJP_402",
offset = "0,0,0"
},
soundId = "51066"
},
pack4 = {
effect = {
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241210] = {
id = 241210,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.88.1",
quality = 2,
name = "伪装者特效：弹跳布丁",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_009",
picture = "CDN:T_E1_Icon_DisguiserVFX_003",
getWay = "活动获得",
jumpId = {
671
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise9_Tran_LYT_401",
scale = 100
},
materialPath = "MI_NR3E1_Disguise9_Invisible_102",
soundId = "51067"
},
pack2 = {
model = {
scale = 102
},
effect = {
path = "FX_P_NR3E1_Disguise9_Trail_LYT_405",
offset = "0,0,50"
},
materialPath = "MI_NR3E1_Disguise9_Invisible_104",
soundId = "51068"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise9_Inv_LYT_402",
offset = "0,0,0"
},
soundId = "51069"
},
pack4 = {
effect = {
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241211] = {
id = 241211,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.99.1",
quality = 2,
name = "伪装者特效：星空",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_010",
picture = "CDN:T_E1_Icon_DisguiserVFX_003",
getWay = "活动获得",
jumpId = {
671
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1763395200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise10_Tran_FXY_401",
scale = 100
},
materialPath = "MI_NR3E1_Disguise10_Invisible_101",
soundId = "51083"
},
pack2 = {
model = {
scale = 100
},
effect = {
path = "FX_P_NR3E1_Disguise10_Trail_FXY_403",
offset = "0,0,50"
},
materialPath = "MI_NR3E1_Disguise10_Invisible_102",
soundId = "51084"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise10_Inv_FXY_402",
offset = "0,0,0"
},
soundId = "51085"
},
pack4 = {
effect = {
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
},
[241212] = {
id = 241212,
effect = true,
type = "ItemType_NR3E_DisguiseEffect",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.99.1",
quality = 2,
name = "伪装者特效：动感灯光",
desc = "【躲猫猫】玩法中伪装者的技能特效，可在备战中更换。",
icon = "CDN:T_E1_Icon_DisguiserVFX_011",
picture = "CDN:T_E1_Icon_DisguiserVFX_003",
getWay = "活动获得",
jumpId = {
671
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -180,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1763395200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_DE_WineBucket_001",
type = 1,
scale = 110
},
effect = {
path = "FX_P_NR3E1_Disguise11_Tran_MJ_401",
scale = 100
},
materialPath = "MI_NR3E1_Disguise11_Invisible_102",
soundId = "51080"
},
pack2 = {
model = {
scale = 100
},
effect = {
path = "FX_P_NR3E1_Disguise11_Trail_MJ_403",
offset = "0,0,0"
},
materialPath = "MI_NR3E1_Disguise11_Invisible_103",
soundId = "51081"
},
pack3 = {
effect = {
path = "FX_P_NR3E1_Disguise11_Inv_MJ_402",
offset = "0,0,-96"
},
soundId = "51082"
},
pack4 = {
effect = {
path = "FX_P_NR3E1_Disguise11_Trail_MJ_404",
scale = 100,
offset = "0,0,50"
}
}
},
mallviewNR3EDisguiseEffectScale = 66
}
}

local mt = {
effect = false,
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
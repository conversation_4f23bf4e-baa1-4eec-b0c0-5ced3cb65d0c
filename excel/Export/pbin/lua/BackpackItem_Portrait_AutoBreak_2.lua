--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 头像

local data = {
[860198] = {
id = 860198,
name = "气愤精灵头像",
desc = "限时活动获得",
icon = "CDN:T_Head_705",
resourceConf = {
bg = "CDN:T_Head_bg_705",
cdnFileName = "CDN:T_Head_bg_705_hd"
}
},
[860199] = {
id = 860199,
name = "喵喵节头像",
desc = "限时活动获得",
icon = "CDN:T_Head_175",
resourceConf = {
bg = "CDN:T_Head_bg_175",
cdnFileName = "CDN:T_Head_bg_175_hd"
}
},
[860200] = {
id = 860200,
lowVer = "1.3.78.33",
name = "甜梦嘉年华头像",
desc = "甜梦嘉年华祈愿活动获得",
icon = "CDN:T_Head_177",
resourceConf = {
bg = "CDN:T_Head_bg_177",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Head_bg_177_hd.astc",
cdnFileName = "CDN:T_Head_bg_177_hd"
},
beginTime = {
seconds = 1744128000
}
},
[860201] = {
id = 860201,
lowVer = "1.3.88.1",
name = "喵妮斯头像",
desc = "超能学园限时活动获得",
icon = "CDN:T_Head_181",
resourceConf = {
bg = "CDN:T_Head_bg_181",
cdnFileName = "CDN:T_Head_bg_181_hd"
},
beginTime = {
seconds = 1746115200
}
},
[860202] = {
id = 860202,
lowVer = "1.3.88.1",
name = "无限喵妮斯头像",
desc = "超能学园限时活动获得",
icon = "CDN:T_Head_182",
resourceConf = {
bg = "CDN:T_Head_bg_182",
cdnFileName = "CDN:T_Head_bg_182_hd"
},
beginTime = {
seconds = 1746115200
}
},
[860203] = {
id = 860203,
lowVer = "1.3.88.1",
name = "司小礼头像",
desc = "超能学园通行证获得",
icon = "CDN:T_Head_183",
resourceConf = {
bg = "CDN:T_Head_bg_183",
cdnFileName = "CDN:T_Head_bg_183_hd"
},
beginTime = {
seconds = 1746115200
}
},
[860204] = {
id = 860204,
lowVer = "1.3.88.1",
name = "酷乐星头像",
desc = "超能学园通行证获得",
icon = "CDN:T_Head_184",
resourceConf = {
bg = "CDN:T_Head_bg_184",
cdnFileName = "CDN:T_Head_bg_184_hd"
},
beginTime = {
seconds = 1746115200
}
},
[860208] = {
id = 860208,
name = "超能小宝头像",
desc = "王冠商铺兑换中获得",
icon = "CDN:T_Head_176",
resourceConf = {
bg = "CDN:T_Head_bg_176",
cdnFileName = "CDN:T_Head_bg_176_hd"
}
},
[860209] = {
id = 860209,
name = "喵小萌头像",
desc = "限时活动获得",
icon = "CDN:T_Head_174",
resourceConf = {
bg = "CDN:T_Head_bg_174",
cdnFileName = "CDN:T_Head_bg_174_hd"
}
},
[860210] = {
id = 860210,
lowVer = "1.3.88.1",
quality = 2,
name = "幻彩画匠头像",
desc = "幻彩调律祈愿活动获得",
icon = "CDN:T_Head_186",
resourceConf = {
bg = "CDN:T_Head_bg_186",
cdnFileName = "CDN:T_Head_bg_186_hd"
},
beginTime = {
seconds = 1747324800
}
},
[860211] = {
id = 860211,
lowVer = "1.3.88.1",
quality = 2,
name = "凡妮莎头像",
desc = "蝶舞花间祈愿活动获得",
icon = "CDN:T_Head_173",
resourceConf = {
bg = "CDN:T_Head_bg_173",
cdnFileName = "CDN:T_Head_bg_173_hd"
},
beginTime = {
seconds = 1749139200
}
},
[860212] = {
id = 860212,
lowVer = "1.3.88.1",
quality = 2,
name = "奖杯征程占坑头像紫",
desc = "蝶舞花间祈愿活动获得",
icon = "CDN:T_Head_173",
resourceConf = {
bg = "CDN:T_Head_bg_173",
cdnFileName = "CDN:T_Head_bg_173_hd"
},
beginTime = {
seconds = 1749139200
}
},
[860213] = {
id = 860213,
name = "白泽小仙头像",
desc = "王冠商铺兑换中获得",
icon = "CDN:T_Head_176",
resourceConf = {
bg = "CDN:T_Head_bg_176",
cdnFileName = "CDN:T_Head_bg_176_hd"
}
},
[860214] = {
id = 860214,
name = "美味鸡腿头像",
desc = "限时活动获得",
icon = "CDN:T_Head_185",
resourceConf = {
bg = "CDN:T_Head_bg_185",
cdnFileName = "CDN:T_Head_bg_185_hd"
}
},
[860205] = {
id = 860205,
lowVer = "1.3.78.29",
name = "晴霜头像",
desc = "星运宝箱获得",
icon = "CDN:T_Head_179",
resourceConf = {
bg = "CDN:T_Head_bg_179",
cdnFileName = "CDN:T_Head_bg_179_hd"
}
},
[860206] = {
id = 860206,
lowVer = "1.3.78.29",
name = "项羽头像",
desc = "星运宝箱获得",
icon = "CDN:T_Head_180",
resourceConf = {
bg = "CDN:T_Head_bg_180",
cdnFileName = "CDN:T_Head_bg_180_hd"
}
},
[860207] = {
id = 860207,
lowVer = "1.3.78.29",
name = "绮头像",
desc = "星运宝箱获得",
icon = "CDN:T_Head_178",
resourceConf = {
bg = "CDN:T_Head_bg_178",
cdnFileName = "CDN:T_Head_bg_178_hd"
}
},
[860215] = {
id = 860215,
lowVer = "1.3.88.105",
name = "艾琳头像",
desc = "奇遇舞章祈愿活动获得",
icon = "CDN:T_Head_189",
resourceConf = {
bg = "CDN:T_Head_bg_189",
cdnFileName = "CDN:T_Head_bg_189_hd"
},
beginTime = {
seconds = 1749830400
}
},
[860216] = {
id = 860216,
lowVer = "1.3.88.1",
name = "昆仑守护神头像",
desc = "S13限时活动获得",
icon = "CDN:T_Head_181",
resourceConf = {
bg = "CDN:T_Head_bg_181",
cdnFileName = "CDN:T_Head_bg_181_hd"
},
beginTime = {
seconds = 1746115200
}
},
[860217] = {
id = 860217,
lowVer = "1.3.88.1",
name = "昆仑之心头像",
desc = "S13限时活动获得",
icon = "CDN:T_Head_182",
resourceConf = {
bg = "CDN:T_Head_bg_182",
cdnFileName = "CDN:T_Head_bg_182_hd"
},
beginTime = {
seconds = 1746115200
}
},
[860218] = {
id = 860218,
lowVer = "1.3.88.1",
name = "当康穗穗头像",
desc = "S13通行证获得",
icon = "CDN:T_Head_183",
resourceConf = {
bg = "CDN:T_Head_bg_183",
cdnFileName = "CDN:T_Head_bg_183_hd"
},
beginTime = {
seconds = 1746115200
}
},
[860219] = {
id = 860219,
lowVer = "1.3.88.1",
name = "天狗烬头像",
desc = "S13通行证获得",
icon = "CDN:T_Head_184",
resourceConf = {
bg = "CDN:T_Head_bg_184",
cdnFileName = "CDN:T_Head_bg_184_hd"
},
beginTime = {
seconds = 1746115200
}
},
[860223] = {
id = 860223,
lowVer = "1.3.88.105",
name = "航海计划头像",
desc = "航海计划活动获得",
icon = "CDN:T_Head_185",
resourceConf = {
bg = "CDN:T_Head_bg_185",
cdnFileName = "CDN:T_Head_bg_185_hd"
},
beginTime = {
seconds = 1749830400
}
}
}

local mt = {
type = "ItemType_Portrait",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
quality = 3,
showInView = 1,
isDynamic = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
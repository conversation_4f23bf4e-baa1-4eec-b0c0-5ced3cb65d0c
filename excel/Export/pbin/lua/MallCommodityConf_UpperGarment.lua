--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-上装

local v0 = {
seconds = 1676390400
}

local v1 = {
seconds = 1676476800
}

local v2 = {
seconds = 4074854400
}

local v3 = 1

local v4 = "赛季祈愿"

local v5 = "印章祈愿"

local v6 = 6

local v7 = 10000

local v8 = "1.2.67.1"

local v9 = "1.2.100.1"

local v10 = "1.3.6.1"

local data = {
[20001] = {
commodityId = 20001,
commodityName = "国潮少年上装",
beginTime = v0,
endTime = v1,
shopSort = 4,
jumpId = 38,
jumpText = "新星指南",
itemIds = {
510001
}
},
[20002] = {
commodityId = 20002,
commodityName = "夏日微风上装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 2,
jumpId = 11,
jumpText = v4,
itemIds = {
510004
},
bOpenSuit = true,
suitId = 1
},
[20003] = {
commodityId = 20003,
commodityName = "最佳员工上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510005
},
bOpenSuit = true,
suitId = 2
},
[20004] = {
commodityId = 20004,
commodityName = "蓝白运动服上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510006
}
},
[20005] = {
commodityId = 20005,
commodityName = "黑粉运动服上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510007
}
},
[20006] = {
commodityId = 20006,
commodityName = "清爽运动上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510008
},
bOpenSuit = true,
suitId = 3
},
[20007] = {
commodityId = 20007,
commodityName = "拔腿就跑T恤",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510009
}
},
[20008] = {
commodityId = 20008,
commodityName = "紫色印花T恤",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510010
}
},
[20009] = {
commodityId = 20009,
commodityName = "黑底小紫花T恤",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510011
}
},
[20010] = {
commodityId = 20010,
commodityName = "蓝色polo衫上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510012
}
},
[20011] = {
commodityId = 20011,
commodityName = "米色夹克T恤",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510013
}
},
[20012] = {
commodityId = 20012,
commodityName = "粉蓝夹克T恤",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510014
}
},
[20013] = {
commodityId = 20013,
commodityName = "活力街头上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510015
}
},
[20014] = {
commodityId = 20014,
commodityName = "蓝紫渐变卫衣",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510016
}
},
[20015] = {
commodityId = 20015,
commodityName = "棒球少年上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510017
}
},
[20016] = {
commodityId = 20016,
commodityName = "灰色边缘上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510018
},
bOpenSuit = true,
suitId = 4
},
[20017] = {
commodityId = 20017,
commodityName = "星小递上装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1704383999
},
shopSort = 5,
jumpId = 44,
jumpText = "小乔送星运",
itemIds = {
510019
},
bOpenSuit = true,
suitId = 5
},
[20018] = {
commodityId = 20018,
commodityName = "沙滩旅客上装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 6,
jumpId = 9,
jumpText = "桃源通行证",
itemIds = {
510020
},
bOpenSuit = true,
suitId = 6
},
[20019] = {
commodityId = 20019,
commodityName = "紫色棉服上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510021
}
},
[20020] = {
commodityId = 20020,
commodityName = "黑白夹心上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510022
}
},
[20021] = {
commodityId = 20021,
commodityName = "挎包短袖套装上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510023
}
},
[20022] = {
commodityId = 20022,
commodityName = "荧光字母上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510024
}
},
[20023] = {
commodityId = 20023,
commodityName = "篮球少年上装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 7,
jumpId = 33,
jumpText = "闯关挑战",
itemIds = {
510025
},
bOpenSuit = true,
suitId = 7
},
[20024] = {
commodityId = 20024,
commodityName = "粉墨工匠上装",
beginTime = v0,
endTime = v1,
shopSort = 2,
jumpId = 24,
itemIds = {
510026
},
suitId = 8
},
[20025] = {
commodityId = 20025,
commodityName = "赤土探险上装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 2,
jumpId = 11,
jumpText = v4,
itemIds = {
510027
},
bOpenSuit = true,
suitId = 9
},
[20026] = {
commodityId = 20026,
commodityName = "彩虹旋律上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510028
},
bOpenSuit = true,
suitId = 10
},
[20027] = {
commodityId = 20027,
commodityName = "烘焙甜心上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510029
},
bOpenSuit = true,
suitId = 11
},
[20028] = {
commodityId = 20028,
commodityName = "摩登学院上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510030
},
bOpenSuit = true,
suitId = 12
},
[20029] = {
commodityId = 20029,
commodityName = "草莓奶昔上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510031
}
},
[20030] = {
commodityId = 20030,
commodityName = "红莓丝绒上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510032
},
bOpenSuit = true,
suitId = 13
},
[20031] = {
commodityId = 20031,
commodityName = "芝士桃桃上装",
coinType = 6,
price = 150,
beginTime = {
seconds = 1719504000
},
itemIds = {
510041
},
suitId = 428
},
[20032] = {
commodityId = 20032,
commodityName = "榛果布丁上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510042
}
},
[20033] = {
commodityId = 20033,
commodityName = "三花摩卡上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510043
},
bOpenSuit = true,
suitId = 14
},
[20034] = {
commodityId = 20034,
commodityName = "白桃气泡上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510044
},
bOpenSuit = true,
suitId = 15
},
[20035] = {
commodityId = 20035,
commodityName = "蓝色童话上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510045
},
bOpenSuit = true,
suitId = 16
},
[20036] = {
commodityId = 20036,
commodityName = "海盐奶盖上装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 2,
jumpId = 11,
jumpText = v4,
itemIds = {
510046
},
bOpenSuit = true,
suitId = 17
},
[20037] = {
commodityId = 20037,
commodityName = "紫藤幻梦上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510047
}
},
[20038] = {
commodityId = 20038,
commodityName = "青柠马卡龙上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510048
}
},
[20039] = {
commodityId = 20039,
commodityName = "黑莓可可上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510049
},
bOpenSuit = true,
suitId = 18
},
[20040] = {
commodityId = 20040,
commodityName = "蓝莓戚风上装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
510050
},
bOpenSuit = true,
suitId = 19
},
[20041] = {
commodityId = 20041,
commodityName = "黄绿开衫",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510033
}
},
[20042] = {
commodityId = 20042,
commodityName = "粉红樱桃",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510034
}
},
[20043] = {
commodityId = 20043,
commodityName = "奶香榛子",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
shopSort = 3,
itemIds = {
510035
}
},
[20044] = {
commodityId = 20044,
commodityName = "蓝白水手裙",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510036
}
},
[20045] = {
commodityId = 20045,
commodityName = "红色线衫",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510037
}
},
[20046] = {
commodityId = 20046,
commodityName = "绿荫粉梦",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
shopSort = 9,
itemIds = {
510038
}
},
[20047] = {
commodityId = 20047,
commodityName = "白玫荣耀",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510039
}
},
[20048] = {
commodityId = 20048,
commodityName = "粉白格子裙",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510040
}
},
[20049] = {
commodityId = 20049,
commodityName = "酷橙嘻哈侠上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510051
}
},
[20050] = {
commodityId = 20050,
commodityName = "萤绿嘻哈侠下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510052
}
},
[20051] = {
commodityId = 20051,
commodityName = "桃心初绽",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510053
}
},
[20052] = {
commodityId = 20052,
commodityName = "梨花春雪",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510054
}
},
[20053] = {
commodityId = 20053,
commodityName = "翠丝云裳",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510055
}
},
[20054] = {
commodityId = 20054,
commodityName = "紫烟云梦",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510056
}
},
[20055] = {
commodityId = 20055,
commodityName = "青柠薄荷",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510057
}
},
[20056] = {
commodityId = 20056,
commodityName = "红莓夹心",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510058
}
},
[20057] = {
commodityId = 20057,
commodityName = "和平套装上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510059
}
},
[20058] = {
commodityId = 20058,
commodityName = "活力运动上装",
coinType = 6,
price = 150,
beginTime = {
seconds = 1719504000
},
itemIds = {
510060
},
suitId = 427
},
[20059] = {
commodityId = 20059,
commodityName = "灰色边缘B上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510061
}
},
[20060] = {
commodityId = 20060,
commodityName = "麻将发财套装上装",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopSort = 5,
jumpId = 1063,
jumpText = "守护之翼",
minVersion = "1.3.26.33",
itemIds = {
510062
},
bOpenSuit = true,
suitId = 60
},
[20061] = {
commodityId = 20061,
commodityName = "造梦星衣",
beginTime = v0,
jumpId = 74,
jumpText = "造梦之旅",
itemIds = {
510063
},
bOpenSuit = true,
suitId = 176
},
[20062] = {
commodityId = 20062,
commodityName = "潮蓝嘻哈侠上装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
510064
}
},
[20063] = {
commodityId = 20063,
commodityName = "甜心画师",
beginTime = {
seconds = 1704643200
},
endTime = {
seconds = 1707062399
},
shopSort = 3,
jumpId = 48,
jumpText = "Toby贴纸簿",
itemIds = {
510065
},
bOpenSuit = true,
suitId = 137
},
[20064] = {
commodityId = 20064,
commodityName = "悠闲瓜瓜",
beginTime = {
seconds = 1704643200
},
endTime = {
seconds = 1707062399
},
shopSort = 4,
jumpId = 48,
jumpText = "Toby贴纸簿",
itemIds = {
510066
},
bOpenSuit = true,
suitId = 138
},
[20065] = {
commodityId = 20065,
commodityName = "墨绿大衣",
coinType = 6,
price = 350,
beginTime = {
seconds = 1705334400
},
endTime = {
seconds = 7258003199
},
shopSort = 1,
itemIds = {
510072
},
canGift = true,
addIntimacy = 35,
giftCoinType = 1,
giftPrice = 350,
suitId = 62
},
[20066] = {
commodityId = 20066,
commodityName = "花棉袄",
coinType = 6,
price = 150,
beginTime = {
seconds = 1705334400
},
endTime = {
seconds = 7258003199
},
shopSort = 2,
itemIds = {
510089
},
canGift = true,
addIntimacy = 15,
giftCoinType = 1,
giftPrice = 150,
suitId = 63
},
[21000] = {
commodityId = 21000,
commodityName = "奥特曼-胜利队服",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510070
},
suitId = 82
},
[21001] = {
commodityId = 21001,
commodityName = "新中式男装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510071
},
suitId = 83
},
[21002] = {
commodityId = 21002,
commodityName = "像素风上装",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 175,
jumpText = v4,
minVersion = v8,
itemIds = {
510073
},
bOpenSuit = true,
suitId = 84
},
[21003] = {
commodityId = 21003,
commodityName = "涂鸦上装",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 175,
jumpText = v4,
minVersion = v8,
itemIds = {
510074
},
bOpenSuit = true,
suitId = 85
},
[21004] = {
commodityId = 21004,
commodityName = "豹纹野人套装",
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1710086399
},
shopSort = 1,
jumpId = 183,
jumpText = "冰雪玫瑰",
minVersion = "1.2.80.1",
itemIds = {
510075
},
bOpenSuit = true,
suitId = 86
},
[21005] = {
commodityId = 21005,
commodityName = "麻将套红中上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510076
},
suitId = 87
},
[21006] = {
commodityId = 21006,
commodityName = "麻将套南风上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510077
},
suitId = 88
},
[21007] = {
commodityId = 21007,
commodityName = "染色套-围巾A",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510078
},
suitId = 89
},
[21008] = {
commodityId = 21008,
commodityName = "染色套-围巾B",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510079
},
suitId = 90
},
[21009] = {
commodityId = 21009,
commodityName = "校服上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510080
},
suitId = 91
},
[21010] = {
commodityId = 21010,
commodityName = "薯条上装",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 175,
jumpText = v4,
minVersion = v8,
itemIds = {
510081
},
bOpenSuit = true,
suitId = 92
},
[21011] = {
commodityId = 21011,
commodityName = "FPS射击类特警套装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510082
},
suitId = 93
},
[21012] = {
commodityId = 21012,
commodityName = "FPS射击类匪装上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510083
},
suitId = 94
},
[21013] = {
commodityId = 21013,
commodityName = "唐装-CP上装",
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708185599
},
jumpId = 171,
jumpText = "充值送好礼",
minVersion = "1.2.80.1",
itemIds = {
510084
},
bOpenSuit = true,
suitId = 95
},
[21014] = {
commodityId = 21014,
commodityName = "奶牛上装",
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = 1713455999
},
jumpId = 181,
jumpText = "绮梦灯",
minVersion = "1.2.80.1",
itemIds = {
510085
},
bOpenSuit = true,
suitId = 96
},
[21015] = {
commodityId = 21015,
commodityName = "小老鼠上装",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 9,
jumpText = "山海通行证",
minVersion = v8,
itemIds = {
510086
},
bOpenSuit = true,
suitId = 97
},
[21016] = {
commodityId = 21016,
commodityName = "唐装上装",
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708185599
},
jumpId = 171,
jumpText = "充值送好礼",
minVersion = "1.2.80.1",
itemIds = {
510087
},
bOpenSuit = true,
suitId = 98
},
[21017] = {
commodityId = 21017,
commodityName = "马面裙",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510088
},
suitId = 99
},
[21018] = {
commodityId = 21018,
commodityName = "水瓶座上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510090
},
suitId = 100
},
[21019] = {
commodityId = 21019,
commodityName = "浴衣",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510091
},
suitId = 101
},
[21020] = {
commodityId = 21020,
commodityName = "赞不绝手上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510092
}
},
[21021] = {
commodityId = 21021,
commodityName = "寻味奇趣上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510093
}
},
[21022] = {
commodityId = 21022,
commodityName = "红莓酥糖上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
510094
}
},
[21023] = {
commodityId = 21023,
commodityName = "绝对爆发财上装",
coinType = 6,
price = 150,
beginTime = {
seconds = 1719504000
},
minVersion = v8,
itemIds = {
510095
},
suitId = 426
},
[21024] = {
commodityId = 21024,
commodityName = "绅士礼服上装",
coinType = 6,
price = 150,
beginTime = {
seconds = 1708704000
},
minVersion = v8,
itemIds = {
510096
},
canGift = true,
addIntimacy = 15,
giftCoinType = 1,
giftPrice = 150,
suitId = 179
},
[21025] = {
commodityId = 21025,
commodityName = "淑女礼裙",
coinType = 6,
price = 350,
beginTime = {
seconds = 1708704000
},
minVersion = v8,
itemIds = {
510097
},
canGift = true,
addIntimacy = 35,
giftCoinType = 1,
giftPrice = 350,
suitId = 177
},
[21026] = {
commodityId = 21026,
commodityName = "翩翩长衫",
coinType = 6,
price = 350,
beginTime = {
seconds = 1708704000
},
minVersion = v8,
itemIds = {
510098
},
canGift = true,
addIntimacy = 35,
giftCoinType = 1,
giftPrice = 350,
suitId = 178
},
[21101] = {
commodityId = 21101,
commodityName = "惊喜派对",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510099
},
suitId = 152
},
[21102] = {
commodityId = 21102,
commodityName = "智慧之光",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510100
},
suitId = 151
},
[21103] = {
commodityId = 21103,
commodityName = "制胜代码",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
jumpId = 189,
jumpText = v4,
itemIds = {
510101
},
bOpenSuit = true,
suitId = 160
},
[21104] = {
commodityId = 21104,
commodityName = "清理大师",
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 186,
jumpText = "春水溯游",
minVersion = v9,
itemIds = {
510102
},
bOpenSuit = true,
suitId = 212
},
[21105] = {
commodityId = 21105,
commodityName = "浪漫双鱼",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510103
}
},
[21106] = {
commodityId = 21106,
commodityName = "星夜绅士上装",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
jumpId = 9,
jumpText = "时光通行证",
itemIds = {
510104
},
bOpenSuit = true,
suitId = 156
},
[21107] = {
commodityId = 21107,
commodityName = "幸运橙意",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
jumpId = 189,
jumpText = v4,
itemIds = {
510105
},
bOpenSuit = true,
suitId = 159
},
[21108] = {
commodityId = 21108,
commodityName = "拼贴风尚上装",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = v5,
itemIds = {
510106
},
bOpenSuit = true,
suitId = 180
},
[21109] = {
commodityId = 21109,
commodityName = "日落海岛上装",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = v5,
itemIds = {
510107
},
bOpenSuit = true,
suitId = 181
},
[21110] = {
commodityId = 21110,
commodityName = "热带风情上装",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = v5,
itemIds = {
510108
},
bOpenSuit = true,
suitId = 182
},
[21111] = {
commodityId = 21111,
commodityName = "新奇撞色上装",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = v5,
itemIds = {
510109
},
bOpenSuit = true,
suitId = 183
},
[21112] = {
commodityId = 21112,
commodityName = "彩虹心情",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510110
},
suitId = 150
},
[21113] = {
commodityId = 21113,
commodityName = "晚安草莓糖",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510111
}
},
[21114] = {
commodityId = 21114,
commodityName = "素雅新装上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510112
}
},
[21115] = {
commodityId = 21115,
commodityName = "莹紫葡萄上装",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
jumpId = 189,
jumpText = v4,
itemIds = {
510113
},
bOpenSuit = true,
suitId = 158
},
[21116] = {
commodityId = 21116,
commodityName = "星彩恋恋上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510114
}
},
[21117] = {
commodityId = 21117,
commodityName = "星海领航员上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510115
}
},
[21118] = {
commodityId = 21118,
commodityName = "幻想童话",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510116
}
},
[21119] = {
commodityId = 21119,
commodityName = "功夫学徒",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510117
},
suitId = 164
},
[21120] = {
commodityId = 21120,
commodityName = "小真练功服",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510118
},
suitId = 165
},
[21121] = {
commodityId = 21121,
commodityName = "荣誉新生上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510119
},
suitId = 166
},
[21122] = {
commodityId = 21122,
commodityName = "爱意热诚上装",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 9,
jumpText = "潮音通行证",
itemIds = {
510120
},
bOpenSuit = true,
suitId = 203
},
[21123] = {
commodityId = 21123,
commodityName = "涂鸦印记上装",
beginTime = {
seconds = 1713542400
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 177,
jumpText = "暗夜冰羽",
itemIds = {
510121
},
bOpenSuit = true,
suitId = 186
},
[21124] = {
commodityId = 21124,
commodityName = "极速风潮上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510122
}
},
[21125] = {
commodityId = 21125,
commodityName = "口袋宝宝",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510123
}
},
[21126] = {
commodityId = 21126,
commodityName = "清凉海浪上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510124
}
},
[21127] = {
commodityId = 21127,
commodityName = "丝绒格调上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510125
}
},
[21128] = {
commodityId = 21128,
commodityName = "捞门围裙",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510126
}
},
[21129] = {
commodityId = 21129,
commodityName = "功夫巨星",
beginTime = {
seconds = 1717776000
},
endTime = {
seconds = 1718985599
},
shopSort = 1,
jumpId = 25,
jumpText = "特惠礼包",
minVersion = v9,
itemIds = {
510127
},
bOpenSuit = true,
suitId = 418
},
[21130] = {
commodityId = 21130,
commodityName = "别来沾边上装",
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 186,
jumpText = "春水溯游",
minVersion = v9,
itemIds = {
510128
},
bOpenSuit = true,
suitId = 213
},
[21131] = {
commodityId = 21131,
commodityName = "无语时刻上装",
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 186,
jumpText = "春水溯游",
minVersion = v9,
itemIds = {
510129
},
bOpenSuit = true,
suitId = 214
},
[21132] = {
commodityId = 21132,
commodityName = "春日校园上装",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v9,
itemIds = {
510130
},
bOpenSuit = true,
suitId = 201
},
[21133] = {
commodityId = 21133,
commodityName = "春风探险上装",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v9,
itemIds = {
510131
},
bOpenSuit = true,
suitId = 202
},
[21134] = {
commodityId = 21134,
commodityName = "蛙仔旅装",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v9,
itemIds = {
510132
},
bOpenSuit = true,
suitId = 200
},
[21135] = {
commodityId = 21135,
commodityName = "邻家学妹上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510133
}
},
[21136] = {
commodityId = 21136,
commodityName = "学生会长上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510134
}
},
[21137] = {
commodityId = 21137,
commodityName = "暖暖白羊上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510135
}
},
[21138] = {
commodityId = 21138,
commodityName = "隐藏富豪上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510136
}
},
[21139] = {
commodityId = 21139,
commodityName = "年会爆款上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510137
}
},
[21140] = {
commodityId = 21140,
commodityName = "进步青年上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510138
}
},
[21141] = {
commodityId = 21141,
commodityName = "职业轻装上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510139
}
},
[21142] = {
commodityId = 21142,
commodityName = "战国策士",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510140
}
},
[21143] = {
commodityId = 21143,
commodityName = "江南风致",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510141
}
},
[21144] = {
commodityId = 21144,
commodityName = "夏日炎炎上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510142
}
},
[21145] = {
commodityId = 21145,
commodityName = "小丸子联名运动服",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510143
},
suitId = 209
},
[21146] = {
commodityId = 21146,
commodityName = "小丸子爷爷联名款",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v9,
itemIds = {
510144
},
suitId = 210
},
[21147] = {
commodityId = 21147,
commodityName = "草莓抹茶上装",
beginTime = {
seconds = 1719244800
},
endTime = {
seconds = 1720540799
},
jumpId = 1046,
jumpText = "星梭祈愿",
minVersion = "1.3.7.53",
itemIds = {
510145
},
bOpenSuit = true,
suitId = 450
},
[21148] = {
commodityId = 21148,
commodityName = "元气之星上装",
beginTime = {
seconds = 1716566400
},
endTime = {
seconds = 1798732799
},
jumpId = 176,
jumpText = "星梦使者",
itemIds = {
510146
},
bOpenSuit = true,
suitId = 233
},
[21149] = {
commodityId = 21149,
commodityName = "梦里寻粽上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510147
}
},
[21150] = {
commodityId = 21150,
commodityName = "布朗尼尼上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510148
}
},
[21151] = {
commodityId = 21151,
commodityName = "粉桃朵朵上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510149
}
},
[21152] = {
commodityId = 21152,
commodityName = "晚宴淑女",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510150
}
},
[21153] = {
commodityId = 21153,
commodityName = "晚宴绅士上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510151
}
},
[21154] = {
commodityId = 21154,
commodityName = "通行卫士上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510152
}
},
[21155] = {
commodityId = 21155,
commodityName = "古韵长衣上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510153
},
suitId = 416
},
[21156] = {
commodityId = 21156,
commodityName = "气息归元上装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510154
},
suitId = 417
},
[21157] = {
commodityId = 21157,
commodityName = "星夜国粹",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
510155
},
suitId = 400
},
[21158] = {
commodityId = 21158,
commodityName = "青春领航上装",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v10,
itemIds = {
510156
},
bOpenSuit = true,
suitId = 401
},
[21159] = {
commodityId = 21159,
commodityName = "海岛一刻上装",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v10,
itemIds = {
510157
},
bOpenSuit = true,
suitId = 402
},
[21160] = {
commodityId = 21160,
commodityName = "海盐兔兔上装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
510158
},
bOpenSuit = true,
suitId = 403
},
[21161] = {
commodityId = 21161,
commodityName = "灰调潮流上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510159
},
suitId = 404
},
[21162] = {
commodityId = 21162,
commodityName = "灰粉嘻哈上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510160
},
suitId = 405
},
[21163] = {
commodityId = 21163,
commodityName = "多元时尚上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510161
},
suitId = 406
},
[21164] = {
commodityId = 21164,
commodityName = "轻盈起步",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510162
},
suitId = 407
},
[21165] = {
commodityId = 21165,
commodityName = "多云转晴上装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
510163
},
bOpenSuit = true,
suitId = 408
},
[21166] = {
commodityId = 21166,
commodityName = "蔚蓝动力",
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1734623945
},
jumpId = 800,
jumpText = "战神颂歌",
minVersion = "1.3.7.53",
itemIds = {
510164
},
bOpenSuit = true,
suitId = 409
},
[21167] = {
commodityId = 21167,
commodityName = "慢热金牛上衣",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510165
},
suitId = 410
},
[21168] = {
commodityId = 21168,
commodityName = "蔚蓝晴空上装",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v10,
itemIds = {
510166
},
bOpenSuit = true,
suitId = 411
},
[21169] = {
commodityId = 21169,
commodityName = "春城粉桃上装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
510167
},
bOpenSuit = true,
suitId = 412
},
[21170] = {
commodityId = 21170,
commodityName = "奇喵物语上装",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v10,
itemIds = {
510168
},
bOpenSuit = true,
suitId = 413
},
[21171] = {
commodityId = 21171,
commodityName = "萌宠围兜",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510169
},
suitId = 414
},
[21172] = {
commodityId = 21172,
commodityName = "青春筑梦上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510170
},
suitId = 415
},
[21173] = {
commodityId = 21173,
commodityName = "茸茸睡衣上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510171
},
suitId = 419
},
[21174] = {
commodityId = 21174,
commodityName = "蒸汽工程上装",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1798732799
},
jumpId = 311,
jumpText = "守护圣翼",
minVersion = "1.3.7.75",
itemIds = {
510172
},
bOpenSuit = true,
suitId = 420
},
[21175] = {
commodityId = 21175,
commodityName = "异域织梦",
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1734623999
},
jumpId = 800,
jumpText = "战神颂歌",
minVersion = "1.3.7.53",
itemIds = {
510173
},
bOpenSuit = true,
suitId = 421
},
[21176] = {
commodityId = 21176,
commodityName = "果味缤纷",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510174
},
suitId = 422
},
[21177] = {
commodityId = 21177,
commodityName = "甜心梦幻",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510175
},
suitId = 423
},
[21178] = {
commodityId = 21178,
commodityName = "玫瑰豆沙",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510176
},
suitId = 424
},
[21179] = {
commodityId = 21179,
commodityName = "桃心小猫",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510177
},
suitId = 425
},
[21180] = {
commodityId = 21180,
commodityName = "波普艺术",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = "1.3.12.1",
itemIds = {
510178
},
bOpenSuit = true,
suitId = 429
},
[21181] = {
commodityId = 21181,
commodityName = "熊熊出游上装",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = "1.3.12.1",
itemIds = {
510179
},
bOpenSuit = true,
suitId = 430
},
[21182] = {
commodityId = 21182,
commodityName = "悠扬青春上装",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = "1.3.12.1",
itemIds = {
510180
},
bOpenSuit = true,
suitId = 431
},
[21183] = {
commodityId = 21183,
commodityName = "酸甜柠趣上装",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
jumpId = 694,
jumpText = "幻音喵境",
minVersion = v10,
itemIds = {
510181
},
bOpenSuit = true,
suitId = 432
},
[21184] = {
commodityId = 21184,
commodityName = "夏意清新上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510182
},
suitId = 433
},
[21185] = {
commodityId = 21185,
commodityName = "盛夏果实上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510183
},
suitId = 434
},
[21186] = {
commodityId = 21186,
commodityName = "时尚教主上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510184
},
suitId = 435
},
[21187] = {
commodityId = 21187,
commodityName = "欢乐鹿鹿",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510185
},
suitId = 436
},
[21188] = {
commodityId = 21188,
commodityName = "牧场物语",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510186
},
suitId = 437
},
[21189] = {
commodityId = 21189,
commodityName = "咖啡馆服务生",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510187
},
suitId = 438
},
[21190] = {
commodityId = 21190,
commodityName = "胧月公主",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510188
},
suitId = 439
},
[21191] = {
commodityId = 21191,
commodityName = "古韵中轴上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510189
},
suitId = 440
},
[21192] = {
commodityId = 21192,
commodityName = "芝芝桃桃上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510190
},
suitId = 441
},
[21193] = {
commodityId = 21193,
commodityName = "缤纷夏花上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510191
},
suitId = 442
},
[21194] = {
commodityId = 21194,
commodityName = "首席射手上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510192
},
suitId = 443
},
[21195] = {
commodityId = 21195,
commodityName = "静谧狩猎上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510193
},
suitId = 444
},
[21196] = {
commodityId = 21196,
commodityName = "弹跳乒乓上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510194
},
suitId = 445
},
[21197] = {
commodityId = 21197,
commodityName = "无限换防上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510195
},
suitId = 446
},
[21198] = {
commodityId = 21198,
commodityName = "活力网坛上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510196
},
suitId = 447
},
[21199] = {
commodityId = 21199,
commodityName = "精准投手上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510197
},
suitId = 448
},
[21200] = {
commodityId = 21200,
commodityName = "凌空飞跃上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510198
},
suitId = 449
},
[21201] = {
commodityId = 21201,
commodityName = "郊游小熊上装",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 9,
jumpText = "乐园通行证",
minVersion = "1.3.12.1",
itemIds = {
510199
},
bOpenSuit = true,
suitId = 451
},
[21202] = {
commodityId = 21202,
commodityName = "仲夏芳菲上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510200
},
suitId = 452
},
[21203] = {
commodityId = 21203,
commodityName = "童梦奇缘上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510201
},
suitId = 453
},
[21204] = {
commodityId = 21204,
commodityName = "青柠之恋上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510202
},
suitId = 454
},
[21205] = {
commodityId = 21205,
commodityName = "霁雨初虹",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510203
},
suitId = 455
},
[21206] = {
commodityId = 21206,
commodityName = "球场裁判上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510204
},
suitId = 456
},
[21207] = {
commodityId = 21207,
commodityName = "数字休闲上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510205
},
suitId = 457
},
[21208] = {
commodityId = 21208,
commodityName = "活力绅士上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510206
},
suitId = 458
},
[21209] = {
commodityId = 21209,
commodityName = "气质暖狼上装",
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730390399
},
jumpId = 610,
jumpText = "夏夜绮梦",
minVersion = v10,
itemIds = {
510207
},
bOpenSuit = true,
suitId = 459
},
[21210] = {
commodityId = 21210,
commodityName = "开心一刻上装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
510208
},
bOpenSuit = true,
suitId = 460
},
[21211] = {
commodityId = 21211,
commodityName = "揶揄时光上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510209
},
suitId = 461
},
[21212] = {
commodityId = 21212,
commodityName = "假日悠悠",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510210
},
suitId = 462
},
[21213] = {
commodityId = 21213,
commodityName = "牛奶草莓",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510211
},
suitId = 463
},
[21214] = {
commodityId = 21214,
commodityName = "快乐鼬鼬上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510212
},
suitId = 464
},
[21215] = {
commodityId = 21215,
commodityName = "精致领班上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510213
},
suitId = 465
},
[21216] = {
commodityId = 21216,
commodityName = "美味晨光上装",
beginTime = {
seconds = 1726329600
},
endTime = {
seconds = 1727366399
},
shopSort = 1,
jumpId = 364,
jumpText = "山河卷扇",
minVersion = "1.3.18.37",
itemIds = {
510214
},
bOpenSuit = true,
suitId = 466
},
[21217] = {
commodityId = 21217,
commodityName = "萌虎出动上装",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
jumpId = 619,
jumpText = v4,
minVersion = v10,
itemIds = {
510215
},
bOpenSuit = true,
suitId = 467
},
[21218] = {
commodityId = 21218,
commodityName = "专业态度上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510216
},
suitId = 468
},
[21219] = {
commodityId = 21219,
commodityName = "晴空花语上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510217
},
suitId = 469
},
[21220] = {
commodityId = 21220,
commodityName = "潮酷玩家上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510218
},
suitId = 470
},
[21221] = {
commodityId = 21221,
commodityName = "赛场飞驰上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510219
},
suitId = 471
},
[21222] = {
commodityId = 21222,
commodityName = "浅蓝节拍上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510220
},
suitId = 472
},
[21223] = {
commodityId = 21223,
commodityName = "春日运动上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510221
},
suitId = 473
},
[21224] = {
commodityId = 21224,
commodityName = "甜心宝贝",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510222
},
suitId = 474
},
[21225] = {
commodityId = 21225,
commodityName = "林语清风",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510223
},
suitId = 475
},
[21226] = {
commodityId = 21226,
commodityName = "甜心宝贝",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510224
},
suitId = 476
},
[21227] = {
commodityId = 21227,
commodityName = "摩登新贵上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510225
},
suitId = 477
},
[21228] = {
commodityId = 21228,
commodityName = "清凉午后",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510226
},
suitId = 478
},
[21229] = {
commodityId = 21229,
commodityName = "饭团星球上装",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
jumpId = 619,
jumpText = v4,
minVersion = v10,
itemIds = {
510227
},
bOpenSuit = true,
suitId = 479
},
[21230] = {
commodityId = 21230,
commodityName = "蔷薇星云上装",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
jumpId = 619,
jumpText = v4,
minVersion = v10,
itemIds = {
510228
},
bOpenSuit = true,
suitId = 480
},
[21231] = {
commodityId = 21231,
commodityName = "粉红童话",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510229
},
suitId = 481
},
[21232] = {
commodityId = 21232,
commodityName = "LULU猪围裙",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510230
},
suitId = 482
},
[21233] = {
commodityId = 21233,
commodityName = "来伊份小伊",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510231
},
suitId = 483
},
[21234] = {
commodityId = 21234,
commodityName = "甜梦畅游家上装",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = v10,
itemIds = {
510232
},
bOpenSuit = true,
suitId = 484
},
[21235] = {
commodityId = 21235,
commodityName = "职场萌星上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510233
},
suitId = 485
},
[21236] = {
commodityId = 21236,
commodityName = "料理宗师",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510234
},
suitId = 486
},
[21237] = {
commodityId = 21237,
commodityName = "荷风雅韵",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510235
},
suitId = 487
},
[21238] = {
commodityId = 21238,
commodityName = "微笑向阳上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510236
},
suitId = 488
},
[21239] = {
commodityId = 21239,
commodityName = "校园传说上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510237
},
suitId = 489
},
[21240] = {
commodityId = 21240,
commodityName = "松果收藏家上装",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
jumpId = 88888,
jumpText = "幻彩调律",
minVersion = v10,
itemIds = {
510238
},
bOpenSuit = true,
suitId = 490
},
[21241] = {
commodityId = 21241,
commodityName = "占星学院上装",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = v10,
itemIds = {
510239
},
bOpenSuit = true,
suitId = 491
},
[21242] = {
commodityId = 21242,
commodityName = "富贵公子上装",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = v10,
itemIds = {
510240
},
bOpenSuit = true,
suitId = 492
},
[21243] = {
commodityId = 21243,
commodityName = "浪尖飞驰上装",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v10,
itemIds = {
510241
},
bOpenSuit = true,
suitId = 493
},
[21244] = {
commodityId = 21244,
commodityName = "田园牧歌上装",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732723200
},
jumpId = 33,
jumpText = "闯关挑战",
minVersion = v10,
itemIds = {
510242
},
bOpenSuit = true,
suitId = 494
},
[21245] = {
commodityId = 21245,
commodityName = "松间晨雾上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510243
},
suitId = 495
},
[21246] = {
commodityId = 21246,
commodityName = "星空梦想上装",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = v10,
itemIds = {
510244
},
bOpenSuit = true,
suitId = 496
},
[21247] = {
commodityId = 21247,
commodityName = "快乐小狗",
beginTime = {
seconds = 1731081600
},
endTime = {
seconds = 1732291199
},
jumpId = 1065,
jumpText = "超值礼包",
minVersion = "1.3.26.81",
itemIds = {
510245
},
bOpenSuit = true,
suitId = 497
},
[21248] = {
commodityId = 21248,
commodityName = "暖冬物语上装",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
510246
},
bOpenSuit = true,
suitId = 498
},
[21249] = {
commodityId = 21249,
commodityName = "赐福锦鲤上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510247
},
suitId = 499
},
[21250] = {
commodityId = 21250,
commodityName = "芝香披萨上装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
510248
},
bOpenSuit = true,
suitId = 501
},
[21251] = {
commodityId = 21251,
commodityName = "爱心波波上装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
510249
},
bOpenSuit = true,
suitId = 503
},
[21252] = {
commodityId = 21252,
commodityName = "堡你喜欢上装",
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738425599
},
jumpId = 628,
jumpText = "星之恋空",
minVersion = v10,
itemIds = {
510250
},
bOpenSuit = true,
suitId = 505
},
[21253] = {
commodityId = 21253,
commodityName = "星动曲奇上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510251
},
suitId = 507
},
[21254] = {
commodityId = 21254,
commodityName = "吾皇马甲",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510252
},
suitId = 509
},
[21255] = {
commodityId = 21255,
commodityName = "甜蜜烘焙",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510253
},
suitId = 511
},
[21256] = {
commodityId = 21256,
commodityName = "粉焰旋风上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510254
},
suitId = 513
},
[21257] = {
commodityId = 21257,
commodityName = "蔚蓝闪电上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510255
},
suitId = 515
},
[21258] = {
commodityId = 21258,
commodityName = "逐梦狂飙上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510256
},
suitId = 517
},
[21259] = {
commodityId = 21259,
commodityName = "云龙武袍上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510257
},
suitId = 519
},
[21260] = {
commodityId = 21260,
commodityName = "蓝调夜语",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510258
},
suitId = 521
},
[21261] = {
commodityId = 21261,
commodityName = "粉绒熊熊",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510259
},
suitId = 523
},
[21262] = {
commodityId = 21262,
commodityName = "小鸡日记",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510260
},
suitId = 525
},
[21263] = {
commodityId = 21263,
commodityName = "蜜语精灵",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510261
},
suitId = 527
},
[21264] = {
commodityId = 21264,
commodityName = "点点乐",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510262
},
suitId = 529
},
[21265] = {
commodityId = 21265,
commodityName = "魔法南瓜",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510263
},
suitId = 531
},
[21266] = {
commodityId = 21266,
commodityName = "挽春衫",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510264
},
suitId = 533
},
[21267] = {
commodityId = 21267,
commodityName = "唐宫夜宴",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510265
},
suitId = 535
},
[21268] = {
commodityId = 21268,
commodityName = "至尊星爵",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510266
},
suitId = 537
},
[21269] = {
commodityId = 21269,
commodityName = "蝶舞奇缘",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510267
},
suitId = 539
},
[21270] = {
commodityId = 21270,
commodityName = "青岚短衫上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510268
},
suitId = 541
},
[21271] = {
commodityId = 21271,
commodityName = "沙滩宝贝上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510269
},
suitId = 543
},
[21272] = {
commodityId = 21272,
commodityName = "酣眠之冬上装",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = v10,
itemIds = {
510270
},
bOpenSuit = true,
suitId = 545
},
[21273] = {
commodityId = 21273,
commodityName = "素食潮流上装",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v10,
itemIds = {
510271
},
bOpenSuit = true,
suitId = 547
},
[21274] = {
commodityId = 21274,
commodityName = "暖樱冬语上装",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = v10,
itemIds = {
510272
},
bOpenSuit = true,
suitId = 549
},
[21275] = {
commodityId = 21275,
commodityName = "速食美学上装",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = v10,
itemIds = {
510273
},
bOpenSuit = true,
suitId = 551
},
[21276] = {
commodityId = 21276,
commodityName = "目览金秋上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510274
},
suitId = 553
},
[21277] = {
commodityId = 21277,
commodityName = "民德正雅上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510275
},
suitId = 555
},
[21278] = {
commodityId = 21278,
commodityName = "企鹅牛仔",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510276
},
suitId = 557
},
[21279] = {
commodityId = 21279,
commodityName = "腾云一舞上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510277
},
suitId = 559
},
[21280] = {
commodityId = 21280,
commodityName = "竹韵清扬上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510278
},
suitId = 561
},
[21281] = {
commodityId = 21281,
commodityName = "墨韵乘云上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510279
},
suitId = 563
},
[21282] = {
commodityId = 21282,
commodityName = "桃心王子上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510280
},
suitId = 565
},
[21283] = {
commodityId = 21283,
commodityName = "梅花公爵上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510281
},
suitId = 567
},
[21284] = {
commodityId = 21284,
commodityName = "粉恋周年",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510282
},
suitId = 569
},
[21285] = {
commodityId = 21285,
commodityName = "喵趣横生",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = v10,
itemIds = {
510283
},
bOpenSuit = true,
suitId = 571
},
[21286] = {
commodityId = 21286,
commodityName = "黑糖公主",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510284
},
suitId = 573
},
[21287] = {
commodityId = 21287,
commodityName = "暗夜魅紫",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510285
},
suitId = 575
},
[21288] = {
commodityId = 21288,
commodityName = "黑白迷踪上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510286
},
suitId = 577
},
[21289] = {
commodityId = 21289,
commodityName = "樱花班校服",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510287
},
suitId = 579
},
[21290] = {
commodityId = 21290,
commodityName = "经典夹克",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510288
},
suitId = 581
},
[21291] = {
commodityId = 21291,
commodityName = "绅士衬衫裤",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510289
},
suitId = 583
},
[21292] = {
commodityId = 21292,
commodityName = "舞王摇摆裤",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510290
},
suitId = 585
},
[21293] = {
commodityId = 21293,
commodityName = "花园漫步裙",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510291
},
suitId = 587
},
[21294] = {
commodityId = 21294,
commodityName = "宝宝背带裙",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510292
},
suitId = 589
},
[21295] = {
commodityId = 21295,
commodityName = "蜜罐小熊",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510293
},
suitId = 591
},
[21296] = {
commodityId = 21296,
commodityName = "奶芙蝶语上装",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = v10,
itemIds = {
510294
},
bOpenSuit = true,
suitId = 593
},
[21297] = {
commodityId = 21297,
commodityName = "自愿上学上装",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = v10,
itemIds = {
510295
},
bOpenSuit = true,
suitId = 595
},
[21298] = {
commodityId = 21298,
commodityName = "自愿上班上装",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = v10,
itemIds = {
510296
},
bOpenSuit = true,
suitId = 597
},
[21299] = {
commodityId = 21299,
commodityName = "冬日暖喵上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510297
},
suitId = 599
},
[21300] = {
commodityId = 21300,
commodityName = "林深如墨上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510298
},
suitId = 601
},
[21301] = {
commodityId = 21301,
commodityName = "冬夜星火上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510299
},
suitId = 603
},
[21302] = {
commodityId = 21302,
commodityName = "鸭鸭护卫队上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510300
},
suitId = 605
},
[21303] = {
commodityId = 21303,
commodityName = "脆弱鸭鸭上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510301
},
suitId = 607
},
[21304] = {
commodityId = 21304,
commodityName = "冬暖花眠上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510302
},
suitId = 609
},
[21305] = {
commodityId = 21305,
commodityName = "懵懂回忆",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510303
},
suitId = 611
},
[21306] = {
commodityId = 21306,
commodityName = "戏春浅",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510304
},
suitId = 613
},
[21307] = {
commodityId = 21307,
commodityName = "拥抱侏罗纪上装",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = v10,
itemIds = {
510305
},
bOpenSuit = true,
suitId = 615
},
[21308] = {
commodityId = 21308,
commodityName = "漆墨拼图上装",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = v10,
itemIds = {
510306
},
bOpenSuit = true,
suitId = 617
},
[21309] = {
commodityId = 21309,
commodityName = "蓝庭花语上装",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = v10,
itemIds = {
510307
},
bOpenSuit = true,
suitId = 619
},
[21310] = {
commodityId = 21310,
commodityName = "冬夜欢歌",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510308
},
suitId = 621
},
[21311] = {
commodityId = 21311,
commodityName = "自然精灵上装",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
jumpId = 88888,
jumpText = "幻彩调律",
minVersion = v10,
itemIds = {
510309
},
bOpenSuit = true,
suitId = 623
},
[21312] = {
commodityId = 21312,
commodityName = "冬日多巴胺上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510310
},
suitId = 625
},
[21313] = {
commodityId = 21313,
commodityName = "平安喜乐上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510311
},
suitId = 627
},
[21314] = {
commodityId = 21314,
commodityName = "欢语秋藏上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510312
},
suitId = 629
},
[21315] = {
commodityId = 21315,
commodityName = "层林覆雪上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510313
},
suitId = 631
},
[21316] = {
commodityId = 21316,
commodityName = "凤语玲珑",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510314
},
suitId = 633
},
[21317] = {
commodityId = 21317,
commodityName = "幸运四叶草",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510315
},
suitId = 635
},
[21318] = {
commodityId = 21318,
commodityName = "遗迹寻踪上装",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v10,
itemIds = {
510316
},
bOpenSuit = true,
suitId = 637
},
[21319] = {
commodityId = 21319,
commodityName = "大发特发上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510317
},
suitId = 639
},
[21320] = {
commodityId = 21320,
commodityName = "暖心猫趣",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510318
},
suitId = 641
},
[21321] = {
commodityId = 21321,
commodityName = "半糖心织上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510319
},
suitId = 643
},
[21322] = {
commodityId = 21322,
commodityName = "鲤跃福临上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510320
},
suitId = 645
},
[21323] = {
commodityId = 21323,
commodityName = "锦裾新朝",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510321
},
suitId = 647
},
[21324] = {
commodityId = 21324,
commodityName = "落樱衬衫上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510322
},
suitId = 649
},
[21325] = {
commodityId = 21325,
commodityName = "祥龙送宝上装",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
510323
},
bOpenSuit = true,
suitId = 651
},
[21326] = {
commodityId = 21326,
commodityName = "美乐蒂滑雪服",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510324
},
suitId = 653
},
[21327] = {
commodityId = 21327,
commodityName = "绒绒狐宝",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510325
},
suitId = 655
},
[21328] = {
commodityId = 21328,
commodityName = "元宵宝宝上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510326
},
suitId = 679
},
[21329] = {
commodityId = 21329,
commodityName = "铃兰物语",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510327
},
suitId = 681
},
[21330] = {
commodityId = 21330,
commodityName = "秋香暖柿",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510328
},
suitId = 683
},
[21331] = {
commodityId = 21331,
commodityName = "狂欢进行曲",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510329
},
suitId = 685
},
[21332] = {
commodityId = 21332,
commodityName = "樱雨之华",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510330
},
suitId = 687
},
[21333] = {
commodityId = 21333,
commodityName = "萌动旋风上装",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
jumpId = 640,
jumpText = v4,
minVersion = v10,
itemIds = {
510331
},
bOpenSuit = true,
suitId = 689
},
[21334] = {
commodityId = 21334,
commodityName = "悦动甜心上装",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
jumpId = 640,
jumpText = v4,
minVersion = v10,
itemIds = {
510332
},
bOpenSuit = true,
suitId = 691
},
[21335] = {
commodityId = 21335,
commodityName = "原野兔踪上装",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
jumpId = 640,
jumpText = v4,
minVersion = v10,
itemIds = {
510333
},
bOpenSuit = true,
suitId = 693
},
[21336] = {
commodityId = 21336,
commodityName = "向阳之花上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510334
},
suitId = 695
},
[21337] = {
commodityId = 21337,
commodityName = "牛油果之友上装",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = v5,
minVersion = v10,
itemIds = {
510335
},
bOpenSuit = true,
suitId = 697
},
[21338] = {
commodityId = 21338,
commodityName = "果冻熊之友上装",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = v5,
minVersion = v10,
itemIds = {
510336
},
bOpenSuit = true,
suitId = 699
},
[21339] = {
commodityId = 21339,
commodityName = "棉花狗之友上装",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = v5,
minVersion = v10,
itemIds = {
510337
},
bOpenSuit = true,
suitId = 701
},
[21340] = {
commodityId = 21340,
commodityName = "樱野纷纷上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510338
},
suitId = 703
},
[21341] = {
commodityId = 21341,
commodityName = "吟游之声上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510339
},
suitId = 705
},
[21342] = {
commodityId = 21342,
commodityName = "幻域之风上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510340
},
suitId = 707
},
[21343] = {
commodityId = 21343,
commodityName = "迷彩风尚上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510341
},
suitId = 709
},
[21344] = {
commodityId = 21344,
commodityName = "月夜狼宝",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510342
},
suitId = 711
},
[21345] = {
commodityId = 21345,
commodityName = "兔萝少女",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510343
},
suitId = 833
},
[21346] = {
commodityId = 21346,
commodityName = "拂春语",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510344
},
suitId = 835
},
[21347] = {
commodityId = 21347,
commodityName = "花香精灵",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510345
},
suitId = 837
},
[21348] = {
commodityId = 21348,
commodityName = "晨曦之花",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510346
},
suitId = 839
},
[21349] = {
commodityId = 21349,
commodityName = "果橘派对",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510347
},
suitId = 841
},
[21350] = {
commodityId = 21350,
commodityName = "活力竖纹上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510348
},
suitId = 843
},
[21351] = {
commodityId = 21351,
commodityName = "雪人物语上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510349
},
suitId = 845
},
[21352] = {
commodityId = 21352,
commodityName = "海岛风情上装",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = v5,
minVersion = v10,
itemIds = {
510350
},
bOpenSuit = true,
suitId = 847
},
[21353] = {
commodityId = 21353,
commodityName = "花舞樱樱上装",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = v5,
minVersion = v10,
itemIds = {
510351
},
bOpenSuit = true,
suitId = 849
},
[21354] = {
commodityId = 21354,
commodityName = "荣冕侍卫上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510352
},
suitId = 851
},
[21355] = {
commodityId = 21355,
commodityName = "空野之啼上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510353
},
suitId = 853
},
[21356] = {
commodityId = 21356,
commodityName = "兰花倩影上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510354
},
suitId = 855
},
[21357] = {
commodityId = 21357,
commodityName = "深谷幽蔷上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510355
},
suitId = 857
},
[21358] = {
commodityId = 21358,
commodityName = "碧竹丛意上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510356
},
suitId = 859
},
[21359] = {
commodityId = 21359,
commodityName = "绵绵武堂",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510357
},
suitId = 861
},
[21360] = {
commodityId = 21360,
commodityName = "青山风起",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510358
},
suitId = 863
},
[21361] = {
commodityId = 21361,
commodityName = "云分浪浅",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510359
},
suitId = 865
},
[21362] = {
commodityId = 21362,
commodityName = "青葱校园",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510360
},
suitId = 867
},
[21363] = {
commodityId = 21363,
commodityName = "竹熊戏影",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510361
},
suitId = 869
},
[21364] = {
commodityId = 21364,
commodityName = "律动多巴胺上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510362
},
suitId = 871
},
[21365] = {
commodityId = 21365,
commodityName = "像素叠叠乐上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510363
},
suitId = 873
},
[21366] = {
commodityId = 21366,
commodityName = "火树银花上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510364
},
suitId = 875
},
[21367] = {
commodityId = 21367,
commodityName = "数字边缘上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510365
},
suitId = 877
},
[21368] = {
commodityId = 21368,
commodityName = "淡淡甜意上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510366
},
suitId = 879
},
[21369] = {
commodityId = 21369,
commodityName = "道士下山上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510367
},
suitId = 881
},
[21370] = {
commodityId = 21370,
commodityName = "狗宝特攻",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510368
},
suitId = 883
},
[21371] = {
commodityId = 21371,
commodityName = "蔷薇鸣泣",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510369
},
suitId = 885
},
[21372] = {
commodityId = 21372,
commodityName = "喵影甜心",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510370
},
suitId = 887
},
[21373] = {
commodityId = 21373,
commodityName = "甜蜜梦乡上装",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = v5,
minVersion = v10,
itemIds = {
510371
},
bOpenSuit = true,
suitId = 889
},
[21374] = {
commodityId = 21374,
commodityName = "花间睡服上装",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = v5,
minVersion = v10,
itemIds = {
510372
},
bOpenSuit = true,
suitId = 891
},
[21375] = {
commodityId = 21375,
commodityName = "星河入梦上装",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = v5,
minVersion = v10,
itemIds = {
510373
},
bOpenSuit = true,
suitId = 893
},
[21376] = {
commodityId = 21376,
commodityName = "蜜桃小哭包",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510374
},
suitId = 895
},
[21377] = {
commodityId = 21377,
commodityName = "无贝不宝上装",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = v5,
minVersion = v10,
itemIds = {
510375
},
bOpenSuit = true,
suitId = 897
},
[21378] = {
commodityId = 21378,
commodityName = "无宝不贝上装",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = v5,
minVersion = v10,
itemIds = {
510376
},
bOpenSuit = true,
suitId = 899
},
[21379] = {
commodityId = 21379,
commodityName = "森猎原野上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510377
},
suitId = 901
},
[21380] = {
commodityId = 21380,
commodityName = "薄荷微夏",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510378
},
suitId = 903
},
[21381] = {
commodityId = 21381,
commodityName = "梦回部落",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510379
},
suitId = 905
},
[21382] = {
commodityId = 21382,
commodityName = "晚安兔子",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510380
},
suitId = 907
},
[21383] = {
commodityId = 21383,
commodityName = "企鹅爬爬服",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510381
},
suitId = 909
},
[21384] = {
commodityId = 21384,
commodityName = "阿卓背带裤",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510382
},
suitId = 911
},
[21385] = {
commodityId = 21385,
commodityName = "雾都情书上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510383
},
suitId = 913
},
[21386] = {
commodityId = 21386,
commodityName = "像素小红狐上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510384
},
suitId = 915
},
[21387] = {
commodityId = 21387,
commodityName = "紫罗兰之约",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510385
},
suitId = 917
},
[21388] = {
commodityId = 21388,
commodityName = "匠人拉面",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510386
},
suitId = 919
},
[21389] = {
commodityId = 21389,
commodityName = "哼哈背带裤",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510387
},
suitId = 921
},
[21390] = {
commodityId = 21390,
commodityName = "花浅染浪",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510388
},
suitId = 923
},
[21391] = {
commodityId = 21391,
commodityName = "软糖绵绵",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510389
},
suitId = 925
},
[21392] = {
commodityId = 21392,
commodityName = "硬糖暖暖",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510390
},
suitId = 927
},
[21393] = {
commodityId = 21393,
commodityName = "怪怪萌仔",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510391
},
suitId = 929
},
[21394] = {
commodityId = 21394,
commodityName = "流浪幽咪上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510392
},
suitId = 931
},
[21395] = {
commodityId = 21395,
commodityName = "半糖青春上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510393
},
suitId = 933
},
[21396] = {
commodityId = 21396,
commodityName = "快快向左上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510394
},
suitId = 935
},
[21397] = {
commodityId = 21397,
commodityName = "快快向右上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510395
},
suitId = 937
},
[21398] = {
commodityId = 21398,
commodityName = "乐在中央上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510396
},
suitId = 939
},
[21399] = {
commodityId = 21399,
commodityName = "酷鲨航海家上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510397
},
suitId = 941
},
[21400] = {
commodityId = 21400,
commodityName = "萌鲨旅行家上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510398
},
suitId = 943
},
[21401] = {
commodityId = 21401,
commodityName = "星梦飞扬上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510399
},
suitId = 945
},
[21402] = {
commodityId = 21402,
commodityName = "紫雾蝶语上装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510400
},
suitId = 947
},
[21403] = {
commodityId = 21403,
commodityName = "桃屿晴波",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510401
},
suitId = 949
},
[21404] = {
commodityId = 21404,
commodityName = "雨衣",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v10,
itemIds = {
510402
},
suitId = 951
}
}

local mt = {
mallId = 7,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074768000
},
shopTag = {
2,
8
},
gender = 0,
canDirectBuy = false,
itemNums = {
1
},
bOpenSuit = false,
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
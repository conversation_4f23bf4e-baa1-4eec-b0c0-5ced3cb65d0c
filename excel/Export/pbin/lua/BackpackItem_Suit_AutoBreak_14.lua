--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 套装

local data = {
[411632] = {
id = 411632,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "昆仑守护神 白泽",
desc = "此身愿做昆仑雪，散入千门万户灯",
icon = "CDN:Icon_OG_048_02",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
belongTo = 411630,
fashionValue = 125,
belongToGroup = {
411631,
411632
}
},
resourceConf = {
model = "SK_OG_048",
material = "MI_OG_048_1_HP02;MI_OG_048_2_HP02;MI_OG_048_3_HP02;MI_OG_048_4_HP02;MI_OG_048_5_HP02;MI_OG_048_6_HP02;MI_OG_048_7_HP02;MI_OG_048_8_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_048_Physics",
materialSlot = "Skin;Skin_Opaque_02;Skin_Translucent_01;Skin_Opaque_03;Skin_Translucent_02;Skin_Opaque_04;Skin_Translucent_03;Skin_Opaque_05",
skeletalMesh = "SK_OG_048"
},
commodityId = 12189,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_048_PV/Level_OG_048_Intact",
outIdle = "AS_CH_OutIdle_001_OG_048",
outShow = "AS_CH_IdleShow_OG_048_HP02",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4099,
4098
},
outIdle_pv = "LS_PV_OG_048_HP02_Idle",
outEnterSequence = "LS_PV_OG_048_HP02",
shareTexts = {
"许我凡心，阅尽山河"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0
},
[411640] = {
id = 411640,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "昆仑之心 白泽",
desc = "白云出山海，我欲向昆仑",
icon = "CDN:Icon_OG_049",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_049",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_049_Physics",
IconLabelId = 101,
skeletalMesh = "SK_OG_049"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_048_PV/Level_OG_048_Intact",
outIdle = "AS_CH_OutIdle_001_OG_049",
outShow = "AS_CH_IdleShow_OG_049",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4101,
4100
},
outIdle_pv = "LS_PV_OG_049_Idle",
outEnterSequence = "LS_PV_OG_049",
shareTexts = {
"五行聚合，天地归一"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
beginTime = {
seconds = 4101552000
},
suitStoryTextKey = [[日往月来，物换星移，岁月又流逝了千年，白泽修复昆仑之心的故事已然成为了传说。失去神力的白泽虽有着永恒的寿命，但日复一日，过往的记忆早已被深深掩埋，她开始疑惑存在的意义，只身行遍天下，想要寻找答案。

遥远的昆仑之心，混沌之灵蠢蠢欲动，黑气从五色石的裂缝上慢慢溢出。

白泽莫名感到一阵心悸，这是不详的征兆，她加快了自己的脚步。冥冥之中，一切皆有定数，白泽回到了她曾降下神力的五行灵域。土域灵芝遭蚀，她悉心照顾，还土地以灵气；火行宫重明鸟被梦魇缠身，烈火不息，她温柔拥抱，赐予内心清明；大泽水域聚污，水兽狂乱，她净化水源，再现川流不息；木域参林皆腐朽，她献出自己一半的生息，还参林以生机。

最后，在金灵山，白泽见到白虎族众正在对抗混沌之灵放出的黑影。她知道自己要做些什么，却又不知该如何开始。忽然她身下有四色神力生辉，那是她曾给予各个灵域的神力，在她驱散混沌之灵的邪气后，重新聚集在她身边。记忆的大门缓缓打开，追寻的答案显然就在眼前。一只白虎来到白泽身前，献上了最后一道神力。

五行集齐，记忆断片在白泽脑海中快速闪过，她想起来了，自己是昆仑最后的守护神，循着一代代守护神的记忆，她找到了解决一切的办法。在五灵域众生的注目下，白泽吞吐五行神力，缓缓贴近昆仑之心，金色耀光闪烁，混沌之灵退无可退，白泽回头望向尘世，随即步入昆仑之心，往后余生，她将用自己的力量镇压与炼化混沌。

岁月不居，时节如流。又是百年，忽焉已过。

五行灵域近来有传言，山巅常有金色倩影遥望昆仑之心，或许白泽一直就在大家身边，不是吗？]],
suitId = 1166,
suitName = "昆仑之心 白泽",
suitIcon = "CDN:Icon_OG_049",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0
},
[411650] = {
id = 411650,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "翩翩蝶",
desc = "蝴蝶结中，存入一缕阳光，取出满手花香",
icon = "CDN:Icon_BU_319",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_319",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareTexts = {
"蝶翼翩跹，把美梦送给花朵"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
beginTime = {
seconds = 4101552000
},
suitId = 1168,
suitName = "翩翩蝶",
suitIcon = "CDN:Icon_BU_319",
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
},
[411651] = {
id = 411651,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "翩翩蝶",
desc = "蝴蝶结中，存入一缕阳光，取出满手花香",
icon = "CDN:Icon_BU_319_01",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
belongTo = 411650,
fashionValue = 25,
belongToGroup = {
411651
}
},
resourceConf = {
model = "SK_BU_319",
material = "MI_BU_319_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12192,
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareTexts = {
"蝶翼翩跹，把美梦送给花朵"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
},
[411660] = {
id = 411660,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "彼岸花者 姝华",
desc = "随我共游忘川，沉于彼岸花海",
icon = "CDN:Icon_OG_051",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_051",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_051_Physics",
skeletalMesh = "SK_OG_051"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_051_PV/Level_OG_051_Intact",
outIdle = "AS_CH_OutIdle_OG_051",
outShow = "AS_CH_IdleShow_OG_051",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4099,
4098
},
outIdle_pv = "LS_PV_OG_051_Idle",
outEnterSequence = "LS_PV_OG_051",
shareTexts = {
"彼岸盛放，绝世倾城"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
beginTime = {
seconds = 4101552000
},
suitStoryTextKey = [[彼岸花海盛开在忘川的尽头，那里是凋零与重生之地，星宝们关于过往的回忆聚集于此，与彼岸红花相生相伴，花者姝华守护着这片花海。 

新千年到来之时，花海外的世界举行了盛大的烟火晚会，姝华也受邀参加。盛情难却，她暂时离开了花海。而阴暗的角落里，遗忘的黑烟悄悄打开了前往花海的通道，它闻到了回忆甜美的气息，灾难即将到来。

待姝华回来时，彼岸花海已经乱作一团，黑烟无休止的作乱，她知晓自己失职了。姝华默念祷词，释放力量，以盛放姿态与黑烟对抗。远远看去，整个花海平原上乌黑一片，唯有她所在的位置闪烁着红色光芒。姝华正在燃烧自己，花瓣于火焰中飘荡，渐渐落成灰烬，黑烟终是吞没了一切，花海再无一点红色。

黑暗中，姝华感受到些许温暖，星宝们的回忆化成气泡聚集在她周身，那是她尽力保护下来的回忆，不同星宝的面孔在各个气泡中闪过，那是多么温馨美好的过往啊，却因为自己的疏忽大意而要消逝。但姝华还不愿放弃，“将你们的力量借给我吧……”她低声说着，气泡托着她升起，一层又一层，碎裂的气泡则化成烬蝶飞舞，一只两只……越来越多，星星之火，刺破黑暗。

黑烟上方，姝华身旁飞舞着无数的烬蝶，她轻轻挥手，烬蝶散开飞入黑暗笼罩的花海，顷刻间，红光闪耀，花海再次绽放。彼岸花中伸出金色的丝线将姝华紧紧包裹，燃烬的花瓣再次从她身上绽放，姝华重生了，现在的她将永远盛放。

黑烟终于被驱散，姝华看着指尖飞舞的烬蝶，这是回忆的另一种姿态，旧的过往已逝去，新的未来将开启，烬蝶将飞往元梦世界，引领星宝们前行。]],
suitId = 1170,
suitName = "彼岸花者 姝华",
suitIcon = "CDN:Icon_OG_051",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0,
suitSkillId = 41166001
},
[411661] = {
id = 411661,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "彼岸花者 姝华",
desc = "随我共游忘川，沉于彼岸花海",
icon = "CDN:Icon_OG_051_01",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
belongTo = 411660,
fashionValue = 125,
belongToGroup = {
411661,
411662
}
},
resourceConf = {
model = "SK_OG_051",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_051_Physics",
skeletalMesh = "SK_OG_051"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_051_PV/Level_OG_051_Intact",
outIdle = "AS_CH_OutIdle_OG_051",
outShow = "AS_CH_IdleShow_OG_051_HP01",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4099,
4098
},
outIdle_pv = "LS_PV_OG_051_HP01_Idle",
outEnterSequence = "LS_PV_OG_051_HP01",
shareTexts = {
"彼岸盛放，绝世倾城"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0,
suitSkillId = 41166101
},
[411662] = {
id = 411662,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "彼岸花者 姝华",
desc = "随我共游忘川，沉于彼岸花海",
icon = "CDN:Icon_OG_051_02",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
belongTo = 411660,
fashionValue = 125,
belongToGroup = {
411661,
411662
}
},
resourceConf = {
model = "SK_OG_051",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_051_Physics",
skeletalMesh = "SK_OG_051"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_051_PV/Level_OG_051_Intact",
outIdle = "AS_CH_OutIdle_OG_051",
outShow = "AS_CH_IdleShow_OG_051_HP02",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4099,
4098
},
outIdle_pv = "LS_PV_OG_051_HP02_Idle",
outEnterSequence = "LS_PV_OG_051_HP02",
shareTexts = {
"彼岸盛放，绝世倾城"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0,
suitSkillId = 41166201
},
[411670] = {
id = 411670,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "彼岸花者 姝华",
desc = "随我共游忘川，沉于彼岸花海",
icon = "CDN:Icon_OG_051",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_051",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_051_Physics",
skeletalMesh = "SK_OG_051"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_051_PV/Level_OG_051_Intact",
outIdle = "AS_CH_OutIdle_OG_051",
outShow = "AS_CH_IdleShow_OG_051",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4099,
4098
},
outIdle_pv = "LS_PV_OG_051_Idle",
outEnterSequence = "LS_PV_OG_051",
shareTexts = {
"彼岸盛放，绝世倾城"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
suitStoryTextKey = [[彼岸花海盛开在忘川的尽头，那里是凋零与重生之地，星宝们关于过往的回忆聚集于此，与彼岸红花相生相伴，花者姝华守护着这片花海。 

新千年到来之时，花海外的世界举行了盛大的烟火晚会，姝华也受邀参加。盛情难却，她暂时离开了花海。而阴暗的角落里，遗忘的黑烟悄悄打开了前往花海的通道，它闻到了回忆甜美的气息，灾难即将到来。

待姝华回来时，彼岸花海已经乱作一团，黑烟无休止的作乱，她知晓自己失职了。姝华默念祷词，释放力量，以盛放姿态与黑烟对抗。远远看去，整个花海平原上乌黑一片，唯有她所在的位置闪烁着红色光芒。姝华正在燃烧自己，花瓣于火焰中飘荡，渐渐落成灰烬，黑烟终是吞没了一切，花海再无一点红色。

黑暗中，姝华感受到些许温暖，星宝们的回忆化成气泡聚集在她周身，那是她尽力保护下来的回忆，不同星宝的面孔在各个气泡中闪过，那是多么温馨美好的过往啊，却因为自己的疏忽大意而要消逝。但姝华还不愿放弃，“将你们的力量借给我吧……”她低声说着，气泡托着她升起，一层又一层，碎裂的气泡则化成烬蝶飞舞，一只两只……越来越多，星星之火，刺破黑暗。

黑烟上方，姝华身旁飞舞着无数的烬蝶，她轻轻挥手，烬蝶散开飞入黑暗笼罩的花海，顷刻间，红光闪耀，花海再次绽放。彼岸花中伸出金色的丝线将姝华紧紧包裹，燃烬的花瓣再次从她身上绽放，姝华重生了，现在的她将永远盛放。

黑烟终于被驱散，姝华看着指尖飞舞的烬蝶，这是回忆的另一种姿态，旧的过往已逝去，新的未来将开启，烬蝶将飞往元梦世界，引领星宝们前行。]],
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0,
suitSkillId = 41167001
},
[411671] = {
id = 411671,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "彼岸花者 姝华",
desc = "随我共游忘川，沉于彼岸花海",
icon = "CDN:Icon_OG_051_01",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
belongTo = 411670,
fashionValue = 125,
belongToGroup = {
411671,
411672
}
},
resourceConf = {
model = "SK_OG_051",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_051_Physics",
skeletalMesh = "SK_OG_051"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_051_PV/Level_OG_051_Intact",
outIdle = "AS_CH_OutIdle_OG_051",
outShow = "AS_CH_IdleShow_OG_051_HP01",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4099,
4098
},
outIdle_pv = "LS_PV_OG_051_HP01_Idle",
outEnterSequence = "LS_PV_OG_051_HP01",
shareTexts = {
"彼岸盛放，绝世倾城"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0,
suitSkillId = 41167101
},
[411672] = {
id = 411672,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "彼岸花者 姝华",
desc = "随我共游忘川，沉于彼岸花海",
icon = "CDN:Icon_OG_051_02",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
belongTo = 411670,
fashionValue = 125,
belongToGroup = {
411671,
411672
}
},
resourceConf = {
model = "SK_OG_051",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_051_Physics",
skeletalMesh = "SK_OG_051"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_051_PV/Level_OG_051_Intact",
outIdle = "AS_CH_OutIdle_OG_051",
outShow = "AS_CH_IdleShow_OG_051_HP02",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4099,
4098
},
outIdle_pv = "LS_PV_OG_051_HP02_Idle",
outEnterSequence = "LS_PV_OG_051_HP02",
shareTexts = {
"彼岸盛放，绝世倾城"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0,
suitSkillId = 41167201
},
[411680] = {
id = 411680,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "鲤仙儿",
desc = "锦鲤化瑞，愿你平安喜乐~",
icon = "CDN:Icon_BU_320",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_320",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareTexts = {
"转发这只锦鲤，富贵盈门~"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
beginTime = {
seconds = 4101552000
},
suitId = 1172,
suitName = "鲤仙儿",
suitIcon = "CDN:Icon_BU_320",
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
},
[411681] = {
id = 411681,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "鲤仙儿",
desc = "锦鲤化瑞，愿你平安喜乐~",
icon = "CDN:Icon_BU_320_01",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
belongTo = 411680,
fashionValue = 25,
belongToGroup = {
411681
}
},
resourceConf = {
model = "SK_BU_320",
material = "MI_BU_320_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12194,
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareTexts = {
"转发这只锦鲤，富贵盈门~"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
},
[411690] = {
id = 411690,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "软糯狗宝",
desc = "开心时，也会转圈圈追自己的尾巴吗？",
icon = "CDN:Icon_BU_312",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_312",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareTexts = {
"和可爱的小狗一起成长~"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
beginTime = {
seconds = 4101552000
},
suitId = 1174,
suitName = "软糯狗宝",
suitIcon = "CDN:Icon_BU_312",
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
},
[411691] = {
id = 411691,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "软糯狗宝",
desc = "开心时，也会转圈圈追自己的尾巴吗？",
icon = "CDN:Icon_BU_312_01",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
belongTo = 411690,
fashionValue = 25,
belongToGroup = {
411691
}
},
resourceConf = {
model = "SK_BU_312",
material = "MI_BU_312_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12196,
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareTexts = {
"和可爱的小狗一起成长~"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
},
[411700] = {
id = 411700,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 300
}
},
quality = 2,
name = "霜龙断孽 冰夷",
desc = "众善奉行，诸恶勿作",
icon = "CDN:Icon_PL_321",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_321",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_321_Physics"
},
outEnter = "AS_CH_Enter_PL_321",
outIdle = "AS_CH_OutIdle_001",
outShow = "AS_CH_IdleShow_PL_321",
outShowIntervalTime = 10,
scaleTimes = 100,
outEnterSequence = "SQC_Enter_PL_321",
shareTexts = {
"恶念已断，新鳞当生"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
beginTime = {
seconds = 4101552000
},
suitId = 1176,
suitName = "霜龙断孽 冰夷",
suitIcon = "CDN:Icon_PL_321",
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
},
[411710] = {
id = 411710,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "银角大王",
desc = "你看见我哥哥了吗？",
icon = "CDN:Icon_BU_352",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_352",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareTexts = {
"我叫你，你敢答应吗？"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
beginTime = {
seconds = 4101552000
},
suitId = 1178,
suitName = "银角大王",
suitIcon = "CDN:Icon_BU_352",
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
},
[411711] = {
id = 411711,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "银角大王",
desc = "你看见我哥哥了吗？",
icon = "CDN:Icon_BU_352_01",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
belongTo = 411710,
fashionValue = 25,
belongToGroup = {
411711
}
},
resourceConf = {
model = "SK_BU_352",
material = "MI_BU_352_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12199,
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareTexts = {
"我叫你，你敢答应吗？"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
},
[411720] = {
id = 411720,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "压龙大仙",
desc = "美丽的秘诀？我可不会告诉你~",
icon = "CDN:Icon_BU_353",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_353",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareTexts = {
"小家伙，被姐姐美到了吗？"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
beginTime = {
seconds = 4101552000
},
suitId = 1180,
suitName = "压龙大仙",
suitIcon = "CDN:Icon_BU_353",
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
},
[411721] = {
id = 411721,
effect = true,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
name = "压龙大仙",
desc = "美丽的秘诀？我可不会告诉你~",
icon = "CDN:Icon_BU_353_01",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
belongTo = 411720,
fashionValue = 25,
belongToGroup = {
411721
}
},
resourceConf = {
model = "SK_BU_353",
material = "MI_BU_353_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12201,
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareTexts = {
"小家伙，被姐姐美到了吗？"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
}
}

local mt = {
effect = false,
lotteryRewardShow = false,
lotteryPreviewShow = false,
mallHotShow = false,
mallAvatarShow = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_特色玩法运营.xlsx: 道具

local v0 = {
{
itemId = 4,
itemNum = 1
}
}

local data = {
[317100] = {
id = 317100,
effect = true,
expiredReplaceItem = v0,
name = "清凉西瓜",
desc = "用于在农场新篇章活动中兑换奖励",
icon = "CDN:T_Common_Item_System_Bag_083",
getWay = "完成农场新篇章任务",
bHideInBag = true
},
[4001] = {
id = 4001,
effect = true,
expiredReplaceItem = v0,
name = "狗狗铃铛",
desc = "用于在农场集市行活动中兑换奖励",
icon = "CDN:T_Common_Icon_Coin_18",
getWay = "完成农场集市行任务获得",
bHideInBag = true
},
[317101] = {
id = 317101,
effect = true,
expiredReplaceItem = v0,
name = "折纸爱心",
desc = "用于在农场礼物盒活动中兑换奖励",
icon = "CDN:T_Common_Item_System_Bag_094",
getWay = "完成农场礼物盒任务",
commodityId = 8595,
plusjumpId = 7,
bHideInBag = true
},
[317102] = {
id = 317102,
effect = true,
expiredReplaceItem = v0,
name = "大苹果",
desc = "用于在农场星天气活动中兑换奖励",
icon = "CDN:T_Common_Item_Farm_Apple",
getWay = "完成农场星天气任务",
commodityId = 8596,
plusjumpId = 7,
bHideInBag = true
},
[317103] = {
id = 317103,
effect = true,
expiredReplaceItem = v0,
name = "雨露",
desc = "用于在一起养绿植活动中兑换奖励",
icon = "CDN:T_Common_Item_Farm_WaterDroplet",
getWay = "完成一起养绿植任务",
bHideInBag = true
},
[317104] = {
id = 317104,
effect = true,
expiredReplaceItem = v0,
name = "温情礼物盒",
desc = "用于在农场赠礼季活动中兑换奖励",
icon = "CDN:T_Common_Item_System_Giftbox",
getWay = "完成农场赠礼季任务",
bHideInBag = true
},
[317105] = {
id = 317105,
effect = true,
expiredReplaceItem = v0,
name = "狗狗零食",
desc = "用于在旅行狗狗活动中让小狗出行",
icon = "CDN:T_Common_Item_System_Bone",
getWay = "完成旅行狗狗任务",
bHideInBag = true
},
[317106] = {
id = 317106,
effect = true,
expiredReplaceItem = v0,
name = "玫瑰",
desc = "用于在旅行狗狗活动中兑换奖励",
icon = "CDN:T_Common_Icon_Coin_38",
getWay = "狗狗每日旅行带回",
bHideInBag = true
},
[317107] = {
id = 317107,
effect = true,
expiredReplaceItem = v0,
name = "胡萝卜",
desc = "用于在农场星朋友活动中兑换奖励",
icon = "CDN:T_Common_Item_Farm_Carrot",
getWay = "完成农场星朋友任务",
bHideInBag = true
},
[317108] = {
id = 317108,
effect = true,
expiredReplaceItem = v0,
name = "农场币宝箱",
desc = "可以获得农场币奖励",
icon = "CDN:T_Common_Item_Farm_Apple",
getWay = "在农场祈福搭子活动获得",
bHideInBag = true
},
[317109] = {
id = 317109,
effect = true,
expiredReplaceItem = v0,
name = "作物霸福",
desc = "农作物丰收概率少量提升",
icon = "CDN:T_Common_Item_System_Bag_FarmCard02",
getWay = "在农场祈福搭子活动获得",
bHideInBag = true
},
[317110] = {
id = 317110,
effect = true,
expiredReplaceItem = v0,
name = "动物霸福",
desc = "动物丰收概率少量提升",
icon = "CDN:T_Common_Item_System_Bag_FarmCard01",
getWay = "在农场祈福搭子活动获得",
bHideInBag = true
},
[317111] = {
id = 317111,
effect = true,
expiredReplaceItem = v0,
name = "鱼饵霸福",
desc = "鱼饵价格降低5%",
icon = "CDN:T_Common_Item_System_Bag_FarmCard04",
getWay = "在农场祈福搭子活动获得",
bHideInBag = true
},
[317112] = {
id = 317112,
effect = true,
expiredReplaceItem = v0,
name = "幸运霸福",
desc = "祈福&被祈福次数少量提升",
icon = "CDN:T_Common_Item_System_Bag_FarmCard03",
getWay = "在农场祈福搭子活动获得",
bHideInBag = true
},
[317113] = {
id = 317113,
effect = true,
expiredReplaceItem = v0,
name = "浇水次数",
desc = "浇水次数",
icon = "CDN:T_Common_Item_System_BagBig_041",
getWay = "在农场祈福搭子活动获得",
bHideInBag = true
},
[317114] = {
id = 317114,
effect = true,
expiredReplaceItem = v0,
name = "活跃度",
desc = "活跃度",
icon = "CDN:T_Common_Icon_Token_FarmBlessingTeam",
getWay = "在农场祈福搭子活动获得",
bHideInBag = true
},
[317116] = {
id = 317116,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "作物增益",
desc = "农作物产量提升",
icon = "CDN:T_Common_Item_System_Bag_FarmCard06",
getWay = "在重返农场活动获得",
useType = "IUTO_FarmBuffAdd",
useParam = {
2026,
7
},
bHideInBag = true
},
[317117] = {
id = 317117,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "动物增益",
desc = "动物产量提升",
icon = "CDN:T_Common_Item_System_Bag_FarmCard05",
getWay = "在重返农场活动获得",
useType = "IUTO_FarmBuffAdd",
useParam = {
2027,
7
},
bHideInBag = true
},
[317118] = {
id = 317118,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "加工器增益",
desc = "加工器加工容量提升",
icon = "CDN:T_Common_Item_System_Bag_FarmCard07",
getWay = "在重返农场活动获得",
useType = "IUTO_FarmBuffAdd",
useParam = {
2025,
7
},
bHideInBag = true
},
[317115] = {
id = 317115,
effect = true,
expiredReplaceItem = v0,
name = "香甜玉米",
desc = "用于在农场温泉季活动中兑换奖励",
icon = "CDN:T_Common_Item_Farm_Corn",
getWay = "在农场温泉季活动获得",
bHideInBag = true
},
[317119] = {
id = 317119,
effect = true,
expiredReplaceItem = v0,
name = "紫葡萄",
desc = "用于在农场送装饰活动中兑换奖励",
icon = "CDN:T_Common_Item_Farm_Grape",
getWay = "在农场送装饰活动获得",
commodityId = 9174,
plusjumpId = 7,
bHideInBag = true
},
[317220] = {
id = 317220,
effect = true,
expiredReplaceItem = v0,
name = "美味饮料",
desc = "用于在农场美食节活动中增加好感度",
icon = "CDN:T_Common_Item_System_Bag_081",
getWay = "在农场美食节活动获得",
bHideInBag = true
},
[317221] = {
id = 317221,
effect = true,
expiredReplaceItem = v0,
name = "美味甜甜圈",
desc = "用于在农场美食节活动中增加好感度",
icon = "CDN:T_Common_Item_System_BagBig_042",
getWay = "在农场美食节活动获得",
bHideInBag = true
},
[317222] = {
id = 317222,
effect = true,
expiredReplaceItem = v0,
name = "美味蛋糕",
desc = "用于在农场美食节活动中增加好感度",
icon = "CDN:T_Common_Item_System_Bag_087",
getWay = "在农场美食节活动获得",
bHideInBag = true
},
[317223] = {
id = 317223,
effect = true,
expiredReplaceItem = v0,
name = "合成材料",
desc = "用于在农场美食节活动中合成美食",
icon = "CDN:T_Common_Item_System_FoodStuff",
getWay = "在农场美食节活动获得",
bHideInBag = true
},
[317224] = {
id = 317224,
effect = true,
expiredReplaceItem = v0,
name = "恋恋礼盒",
desc = "用于在田园恋语活动中兑换奖励",
icon = "CDN:T_Common_Item_System_GiftBox",
getWay = "在田园恋语活动获得",
bHideInBag = true
},
[317225] = {
id = 317225,
effect = true,
expiredReplaceItem = v0,
name = "田园小耙",
desc = "用于在田园春日季活动中兑换奖励",
icon = "CDN:T_Common_Item_Farm_Rake",
getWay = "在田园春日季活动获得",
bHideInBag = true
},
[317226] = {
id = 317226,
effect = true,
expiredReplaceItem = v0,
name = "星愿贴纸",
desc = "用于在贴纸刮刮乐活动中撕开贴纸",
icon = "CDN:CDN:T_Common_Item_System_Bag_124",
getWay = "在贴纸刮刮乐活动获得",
bHideInBag = true
},
[317127] = {
id = 317127,
effect = true,
expiredReplaceItem = v0,
name = "香甜玉米",
desc = "用于在再遇小红狐活动中兑换奖励",
icon = "CDN:T_Common_Item_Farm_Corn",
getWay = "在再遇小红狐活动获得",
bHideInBag = true
},
[317128] = {
id = 317128,
effect = true,
name = "知识雨露",
desc = "用于在农场知识星活动中兑换奖励",
icon = "CDN:T_Common_Item_Farm_WaterDroplet",
getWay = "完成农场知识星任务"
},
[317129] = {
id = 317129,
effect = true,
expiredReplaceItem = v0,
name = "田园水壶",
desc = "用于在田园绽芳华活动中抽奖",
icon = "CDN:T_Common_Item_System_Bag_091",
getWay = "完成田园绽芳华任务",
bHideInBag = true
},
[317130] = {
id = 317130,
effect = true,
expiredReplaceItem = v0,
name = "玫瑰",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_Common_Icon_Coin_38",
getWay = "在旅行小狗活动获得"
},
[317131] = {
id = 317131,
effect = true,
name = "草莓大福",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_FarmRestaurant_Icon_Dish01",
getWay = "在餐厅星开业活动获得"
},
[317132] = {
id = 317132,
effect = true,
name = "拍黄瓜",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_FarmRestaurant_Icon_Dish02",
getWay = "在餐厅星开业活动获得"
},
[317133] = {
id = 317133,
effect = true,
name = "白灼菜心",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_FarmRestaurant_Icon_Dish04",
getWay = "在餐厅星开业活动获得"
},
[317134] = {
id = 317134,
effect = true,
name = "凉拌海带",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_FarmRestaurant_Icon_Dish03",
getWay = "在餐厅星开业活动获得"
},
[317135] = {
id = 317135,
effect = true,
name = "红烧香菇",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_FarmRestaurant_Icon_Dish05",
getWay = "在餐厅星开业活动获得"
},
[317136] = {
id = 317136,
effect = true,
name = "卤鸡腿",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_FarmRestaurant_Icon_Dish08",
getWay = "在餐厅星开业活动获得"
},
[317137] = {
id = 317137,
effect = true,
name = "糖醋排骨",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_FarmRestaurant_Icon_Dish06",
getWay = "在餐厅星开业活动获得"
},
[317138] = {
id = 317138,
effect = true,
name = "黑椒牛排",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_FarmRestaurant_Icon_Dish09",
getWay = "在餐厅星开业活动获得"
},
[317139] = {
id = 317139,
effect = true,
name = "烤鱼",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_FarmRestaurant_Icon_Dish10",
getWay = "在餐厅星开业活动获得"
},
[317140] = {
id = 317140,
effect = true,
name = "叉烧",
desc = "用于在旅行小狗活动中兑换奖励",
icon = "CDN:T_FarmRestaurant_Icon_Dish07",
getWay = "在餐厅星开业活动获得"
},
[317141] = {
id = 317141,
effect = true,
expiredReplaceItem = v0,
name = "合成材料",
desc = "用于在农场美食节活动中合成美食",
icon = "CDN:T_Common_Item_System_FoodStuff",
getWay = "在餐厅星开业活动获得",
bHideInBag = true
},
[317142] = {
id = 317142,
effect = true,
expiredReplaceItem = v0,
name = "萌趣贴纸撕",
desc = "用于在快乐撕贴纸活动中撕开贴纸",
icon = "CDN:T_Common_Item_System_Bag_124",
getWay = "在快乐撕贴纸活动获得",
bHideInBag = true
},
[317143] = {
id = 317143,
effect = true,
name = "粽宝宝",
desc = "用于在农场小队活动中兑换奖励",
icon = "CDN:T_Common_Item_System_Bag_151",
getWay = "完成农场小队任务",
bHideInBag = true
},
[317144] = {
id = 317144,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "新人农场主增益",
desc = "作物&动物产量提升，加工器容量提升，收获时经验获取提升，作物&动物&钓鱼（含鱼卡）熟练度提升",
icon = "CDN:T_Common_Item_System_Bag_147",
getWay = "在新人农场主活动获得",
useType = "IUTO_FarmBuffAdd",
useParam = {
2033,
7
},
bHideInBag = true
},
[317145] = {
id = 317145,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "新人农场主增益",
desc = "作物&动物产量提升，加工器容量提升，收获时经验获取提升，作物&动物&钓鱼（含鱼卡）熟练度提升",
icon = "CDN:T_Common_Item_System_Bag_147",
getWay = "在新人农场主活动获得",
useType = "IUTO_FarmBuffAdd",
useParam = {
2034,
7
},
bHideInBag = true
},
[317146] = {
id = 317146,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "新人农场主增益",
desc = "作物&动物产量提升，加工器容量提升，收获时经验获取提升，作物&动物&钓鱼（含鱼卡）熟练度提升",
icon = "CDN:T_Common_Item_System_Bag_147",
getWay = "在新人农场主活动获得",
useType = "IUTO_FarmBuffAdd",
useParam = {
2035,
7
},
bHideInBag = true
},
[317147] = {
id = 317147,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "新人农场主增益",
desc = "作物&动物产量提升，加工器容量提升，收获时经验获取提升，作物&动物&钓鱼（含鱼卡）熟练度提升",
icon = "CDN:T_Common_Item_System_Bag_147",
getWay = "在新人农场主活动获得",
useType = "IUTO_FarmBuffAdd",
useParam = {
2033,
14
},
bHideInBag = true
},
[317148] = {
id = 317148,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "新人农场主增益",
desc = "作物&动物产量提升，加工器容量提升，收获时经验获取提升，作物&动物&钓鱼（含鱼卡）熟练度提升",
icon = "CDN:T_Common_Item_System_Bag_147",
getWay = "在新人农场主活动获得",
useType = "IUTO_FarmBuffAdd",
useParam = {
2034,
14
},
bHideInBag = true
},
[317149] = {
id = 317149,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "新人农场主增益",
desc = "作物&动物产量提升，加工器容量提升，收获时经验获取提升，作物&动物&钓鱼（含鱼卡）熟练度提升",
icon = "CDN:T_Common_Item_System_Bag_147",
getWay = "在新人农场主活动获得",
useType = "IUTO_FarmBuffAdd",
useParam = {
2035,
14
},
bHideInBag = true
},
[317150] = {
id = 317150,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "新人限时增益",
desc = "作物&动物产量提升，加工器容量提升，收获时经验获取提升，作物&动物&钓鱼（含鱼卡）熟练度提升",
icon = "CDN:T_Common_Item_System_Bag_147",
getWay = "在新人农场主活动获得",
useType = "IUTO_FarmBuffAdd",
useParam = {
2036,
7
},
bHideInBag = true
},
[317151] = {
id = 317151,
effect = true,
expiredReplaceItem = v0,
name = "合成材料",
desc = "用于在农场美食节活动中合成美食",
icon = "CDN:T_Common_Item_System_FoodStuff",
getWay = "在农场美食节活动获得",
bHideInBag = true
},
[317152] = {
id = 317152,
effect = true,
expiredReplaceItem = v0,
name = "菠萝包",
desc = "用于在农场美食节活动中增加好感度",
icon = "CDN:T_FarmRestaurant_Icon_Dish11",
getWay = "在农场美食节活动获得",
bHideInBag = true
},
[317153] = {
id = 317153,
effect = true,
expiredReplaceItem = v0,
name = "虾饺",
desc = "用于在农场美食节活动中增加好感度",
icon = "CDN:T_FarmRestaurant_Icon_Dish13",
getWay = "在农场美食节活动获得",
bHideInBag = true
},
[317154] = {
id = 317154,
effect = true,
expiredReplaceItem = v0,
name = "马卡龙",
desc = "用于在农场美食节活动中增加好感度",
icon = "CDN:T_FarmRestaurant_Icon_Dish12",
getWay = "在农场美食节活动获得",
bHideInBag = true
},
[317155] = {
id = 317155,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "航海计划专属增益",
desc = "作物产量提升10%",
icon = "CDN:T_Common_Item_System_Bag_FarmCard06",
getWay = "在航海计划活动获得",
bHideInBag = true
},
[317156] = {
id = 317156,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "航海计划专属增益",
desc = "加工器容量提升20%",
icon = "CDN:T_Common_Item_System_Bag_FarmCard07",
getWay = "在航海计划活动获得",
bHideInBag = true
},
[317157] = {
id = 317157,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "航海计划专属增益",
desc = "动物产量提升10%",
icon = "CDN:T_Common_Item_System_Bag_FarmCard05",
getWay = "在航海计划活动获得",
bHideInBag = true
},
[317158] = {
id = 317158,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "航海计划专属增益",
desc = "幸运钩出现概率提升",
icon = "CDN:T_Common_Item_System_Bag_157",
getWay = "在航海计划活动获得",
bHideInBag = true
},
[317159] = {
id = 317159,
effect = true,
type = "ItemType_AutoUse",
expiredReplaceItem = v0,
name = "航海计划专属增益",
desc = "餐厅美食售价提升10%",
icon = "CDN:T_Common_Item_System_Bag_156",
getWay = "在航海计划活动获得",
bHideInBag = true
},
[317160] = {
id = 317160,
effect = true,
expiredReplaceItem = v0,
name = "航海经验",
desc = "用于提升航海计划的等级",
icon = "CDN:T_Common_Item_System_Bag_155",
getWay = "在航海计划活动获得",
bHideInBag = true
}
}

local mt = {
effect = false,
type = "ItemType_Currency",
stackedNum = 1,
maxNum = 999999,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 4,
bagId = 1,
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
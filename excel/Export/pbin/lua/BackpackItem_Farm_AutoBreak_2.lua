--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_农场.xlsx: 农场道具

local v0 = {
{
itemId = 6,
itemNum = 100
}
}

local v1 = 3

local v2 = 1

local v3 = "（星宝农场宠物装饰）可以装扮你的宠物。"

local v4 = "（星宝农场藏品）"

local v5 = 225

local v6 = "0,0"

local data = {
[218145] = {
id = 218145,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.68.114",
quality = 3,
name = "绿绒绒小铺",
desc = "（星宝农场动物小铺装饰）可以降低开垦/升级牧场的价格。",
icon = "CDN:Icon_Farm_BuComm_CLXumu_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CLXumu_001_Comm",
modelType = 1
},
scaleTimes = 23,
bHideInBag = true,
rotateYaw = 225,
buff = "牧场开垦/升级价格降低",
buffValue = "-2%",
buffViewOffset = v6
},
[218146] = {
id = 218146,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.68.114",
quality = 3,
name = "叶泡泡鱼店",
desc = "（星宝农场水产摊装饰）可以提升水族箱获取农场币的效率。",
icon = "CDN:Icon_Farm_BuComm_CLFishStall_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CLFishStall_001_Comm",
modelType = 1
},
scaleTimes = 20,
bHideInBag = true,
rotateYaw = 225,
buff = "水族箱获取农场币效率提升",
buffValue = "+3%",
buffViewOffset = v6
},
[218168] = {
id = 218168,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.88.1",
quality = 1,
name = "蜜糖喵巡游站",
desc = "（星宝农场小屋装饰）提升钓鱼时获得的农场经验。",
icon = "CDN:Icon_Farm_BuComm_TPHome_001_A_Comm",
resourceConf = {
model = "SK_Farm_OG_013_A3",
modelType = 2,
idleAnim = "SK_Farm_OG_013_A3_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_013_PV/Farm_OG_013_Intact",
scaleTimes = 10,
soundId = {
40665
},
outEnterSequence = "Farm_PV_OG_013",
shareTexts = {
"蜜糖小屋，暖心归宿。"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218168.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218168.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218168.astc",
buff = "钓鱼时获得农场经验",
buffValue = "+20%",
buffViewOffset = "320,300"
},
[218169] = {
id = 218169,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.88.1",
quality = 1,
name = "奶油云朵乐园",
desc = "（星宝农场风景装饰）动物熟练度获取提升10%；动物丰收的产量倍率提升1倍，即月卡玩家从4倍提升至5倍，非月卡玩家从3倍提升至4倍。",
icon = "CDN:Icon_Farm_Env_008_A_Comm",
picture = "T_Farmyard_BigIcon_Landscape_8",
bHideInBag = true,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218169.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218169.astc"
},
[218170] = {
id = 218170,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.78.94",
quality = 1,
name = "蜜糖饼庭院",
desc = "（星宝农场院落装饰）作物丰收的产量倍率提升1倍，即月卡玩家从4倍提升至5倍，非月卡玩家从3倍提升至4倍。",
icon = "CDN:Icon_Farm_Env_008_B_Comm",
picture = "T_Farmyard_BigIcon_Yard_8",
bHideInBag = true,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218170.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218170.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218170.astc"
},
[218171] = {
id = 218171,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.78.31",
quality = 1,
name = "快乐涮涮屋",
desc = "（星宝农场小屋装饰）提升收获加工物时获得的农场经验。",
icon = "CDN:Icon_Farm_BuComm_SWHome_001_A_Comm",
resourceConf = {
model = "SK_Farm_OG_010",
modelType = 2,
idleAnim = "SK_Farm_OG_010_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_010_PV/Farm_OG_010_Intact",
scaleTimes = 10,
soundId = {
40662
},
outEnterSequence = "Farm_PV_OG_010",
shareTexts = {
"麻辣鲜香，家味浓浓。"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218171.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218171.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218171.astc",
buff = "收获加工物获得农场经验",
buffValue = "+30%",
buffViewOffset = v6
},
[218172] = {
id = 218172,
exceedReplaceItem = v0,
lowVer = "1.3.78.31",
name = "三明治果行",
desc = "（星宝农场蔬菜摊装饰）可以提升蔬菜摊售价。",
icon = "CDN:Icon_Farm_BuComm_SWVegetableStall_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_SWVegetableStall_001_Comm",
modelType = 1
},
scaleTimes = 10,
shareTexts = {
"层层美味，幸福滋味。"
},
bHideInBag = true,
rotateYaw = 215,
shareOffset = {
-10,
-80
},
buff = "蔬菜摊售价提升",
buffValue = "+10%",
buffViewOffset = v6,
shareScaleTimes = 7,
shareRotateYaw = 210
},
[218173] = {
id = 218173,
exceedReplaceItem = v0,
lowVer = "1.3.78.31",
name = "罐罐茶小铺",
desc = "（星宝农场动物小铺装饰）可以提高动物大丰收产量。",
icon = "CDN:Icon_Farm_BuComm_SWXumu_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_SWXumu_001_Comm",
modelType = 1
},
scaleTimes = 10,
shareTexts = {
"慢煮时光，品味生活。"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-80
},
buff = "动物大丰收产量倍率",
buffValue = "+1",
buffViewOffset = "250,-200",
shareScaleTimes = 7,
shareRotateYaw = 210
},
[218174] = {
id = 218174,
exceedReplaceItem = v0,
lowVer = "1.3.78.31",
name = "豪华寿司鱼店",
desc = "（星宝农场水产摊装饰）可以提升鱼卡&钓鱼获取的熟练度。",
icon = "CDN:Icon_Farm_BuComm_SWFishStall_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_SWFishStall_001_Comm",
modelType = 1
},
scaleTimes = 18,
shareTexts = {
"鱼你相遇，满口鲜活。"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-70
},
buff = "钓鱼获取熟练度提升",
buffValue = "+10%",
buffViewOffset = v6,
shareScaleTimes = 12,
shareRotateYaw = 210
},
[218176] = {
id = 218176,
exceedReplaceItem = v0,
lowVer = "1.3.78.1",
name = "蒸蒸日上小窝",
desc = "（星宝农场宠物屋装饰）可以提升加工器的基础容量。",
icon = "CDN:Icon_Farm_BuComm_SWDogHouse_001_A_1_Comm",
resourceConf = {
model = "SM_Farm_BuComm_SWDogHouse_001_A_1_Comm",
modelType = 1
},
scaleTimes = 32,
shareTexts = {
"热气腾腾，包宠幸福。"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-8,
-65
},
buff = "加工器基础容量提升",
buffValue = "+5%",
buffViewOffset = v6,
shareScaleTimes = 25,
shareRotateYaw = 200
},
[218177] = {
id = 218177,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.78.82",
quality = 1,
name = "煎饼超人",
desc = "（星宝农场稻草人装饰）可以提升动物产量。",
icon = "CDN:Icon_Farm_BuComm_SWMessageBoard_002_A_Comm",
resourceConf = {
model = "SM_Farm_OG_011",
modelType = 2,
idleAnim = "AS_Farm_OG_011_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_011_PV/Farm_OG_011_Intact",
scaleTimes = 26,
soundId = {
40664
},
outEnterSequence = "Farm_PV_OG_011",
shareTexts = {
"金黄香脆，生活滋味。"
},
bHideInBag = true,
rotateYaw = 215,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218177.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218177.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218177.astc",
buff = "动物产量提升",
buffValue = "+10%",
buffViewOffset = v6
},
[218178] = {
id = 218178,
exceedReplaceItem = v0,
lowVer = "1.3.78.31",
name = "披萨时钟",
desc = "（星宝农场时钟装饰）可以增加时光跳跃仙术的每日积攒时间。",
icon = "CDN:Icon_Farm_FU_PizzaClock_001_A_1_Comm",
resourceConf = {
model = "SM_Farm_FU_PizzaClock_001_Comm",
modelType = 1
},
scaleTimes = 27,
shareTexts = {
"滴答滴答，美味出发。"
},
bHideInBag = true,
rotateYaw = 280,
shareOffset = {
-10,
-70
},
buff = "时光跳跃积攒时间提升",
buffValue = "+20%",
buffViewOffset = v6,
shareScaleTimes = 18,
shareRotateYaw = 270
},
[218179] = {
id = 218179,
exceedReplaceItem = v0,
lowVer = "1.3.78.82",
name = "猪猪包礼盒",
desc = "（星宝农场装饰）获得后可以改变送礼时礼物盒的样式。",
icon = "CDN:Icon_Farm_Tool_Gift_007_A_Comm",
resourceConf = {
model = "SM_Farm_Tool_Gift_007_Comm",
modelType = 2,
idleAnim = "SM_Farm_Tool_Gift_007_Idle_Comm"
},
scaleTimes = 40,
shareTexts = {
"猪猪包礼，美味心意。"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-6,
-85
}
},
[218180] = {
id = 218180,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.78.1",
quality = 3,
name = "小红狐农场小屋",
icon = "CDN:",
resourceConf = {
modelType = 1
},
scaleTimes = 23,
bHideInBag = true,
rotateYaw = 225,
buffViewOffset = v6
},
[218181] = {
id = 218181,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.78.1",
quality = 3,
name = "小红狐农场风景",
icon = "CDN:",
resourceConf = {
modelType = 1
},
scaleTimes = 23,
bHideInBag = true,
rotateYaw = 225,
buffViewOffset = v6
},
[218182] = {
id = 218182,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.78.1",
quality = 3,
name = "小红狐农场院落",
icon = "CDN:",
resourceConf = {
modelType = 1
},
scaleTimes = 23,
bHideInBag = true,
rotateYaw = 225,
buffViewOffset = v6
},
[218183] = {
id = 218183,
exceedReplaceItem = v0,
lowVer = "1.3.78.31",
name = "狐爷爷",
desc = "（星宝农场稻草人装饰）可以提高加工物大丰收产量。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_011_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_MessageBoard_011_A_Comm",
modelType = 1
},
scaleTimes = 48,
shareTexts = {
"敲敲草帽，满满丰收。"
},
bHideInBag = true,
rotateYaw = 205,
shareOffset = {
-10,
-70
},
buff = "加工物大丰收产量倍率",
buffValue = "+3",
buffViewOffset = "20,0",
shareScaleTimes = 35
},
[218184] = {
id = 218184,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.78.45",
quality = 1,
name = "翡光仙灵庭",
desc = "（星宝农场蔬菜摊装饰）可以提升作物产量和获取的作物熟练度。",
icon = "CDN:Icon_Farm_BuComm_GloriaVegetableStall_001_A_Comm",
resourceConf = {
model = "SK_Farm_OG_012",
modelType = 2,
idleAnim = "SK_Farm_OG_012_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_012_PV/Farm_OG_012_Intact",
scaleTimes = 15,
soundId = {
40663
},
outEnterSequence = "Farm_PV_OG_012",
shareTexts = {
"瓜果飘香，宝石蝶舞。"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218184.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218184.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218184.astc",
buff = "作物产量提升",
buffValue = "+10%",
buffViewOffset = "-200,100",
buffSecondLevel = -2,
buffSecond = "作物熟练度获取提升",
buffSecondValue = "+10%"
},
[219304] = {
id = 219304,
exceedReplaceItem = v0,
name = "甜萝卜兔",
desc = v3,
icon = "CDN:Icon_Farm_Suit_007_Comm",
resourceConf = {
model = "SK_Farm_Suit_007_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-50
},
shareScaleTimes = 80,
shareRotateYaw = 210
},
[219415] = {
id = 219415,
exceedReplaceItem = v0,
quality = 3,
name = "珍珠耳环",
desc = v3,
icon = "CDN:Icon_Farm_Head_017",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
rotateYaw = 225
},
[219613] = {
id = 219613,
exceedReplaceItem = v0,
quality = 3,
name = "针织花环",
desc = v3,
icon = "CDN:Icon_Farm_Neck_011_Comm",
resourceConf = {
model = "SK_Farm_Neck_011_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
rotateYaw = 225
},
[219811] = {
id = 219811,
exceedReplaceItem = v0,
quality = 3,
name = "春日纸鸢",
desc = v3,
icon = "CDN:Icon_Farm_Body_011",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
rotateYaw = 225
},
[218050] = {
id = 218050,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "时光沙漏",
desc = "（农场专用道具）在农场入口-神像处使用，可用来增加“时光跳跃”仙术的积攒时间。",
icon = "CDN:Icon_Farm_Magic_Energy",
getWay = "活动",
bagType = "IBT_Farm"
},
[218826] = {
id = 218826,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "香橙面包床",
desc = "星宝农场小屋家具。",
icon = "CDN:ICON_SHOP_Farm_Be_S11_FoodBed_01_1",
useParam = {
10626
},
bHideInBag = true,
bagType = "IBT_Farm"
},
[219203] = {
id = 219203,
exceedReplaceItem = v0,
quality = 3,
name = "小雪球",
desc = "（星宝农场宠物）可以保护农场产物。",
icon = "CDN:Icon_Farm_Poodle_001_Comm",
resourceConf = {
model = "SK_Farm_Poodle_001_Comm",
modelType = 2,
idleAnim = "SK_Farm_Poodle_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[218827] = {
id = 218827,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "祈福桃枝盆栽",
desc = "星宝农场小屋家具。",
icon = "ICON_SHOP_Farm_De_S11_TaoHuaPlantPot_01_1",
useParam = {
10617
},
bHideInBag = true,
bagType = "IBT_Farm"
},
[218185] = {
id = 218185,
exceedReplaceItem = v0,
lowVer = "1.3.88.1",
name = "蜜桃猫星星杯",
desc = "（星宝农场稻草人装饰）可以提高动物大丰收产量。",
icon = "CDN:Icon_Farm_BuComm_SWMessageBoard_001_A_Comm",
resourceConf = {
model = "SK_Farm_BuComm_SWMessageBoard_001_A_Comm",
modelType = 2,
idleAnim = "SK_Farm_BuComm_SWMessageBoard_001_A_Idle_Comm",
IconLabelId = 102
},
scaleTimes = 38,
shareTexts = {
"蜜桃在手，甜美不溜走。"
},
bHideInBag = true,
rotateYaw = 205,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218185.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218185.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218185.astc",
shareOffset = {
-5,
-70
},
buff = "动物大丰收产量倍率",
buffValue = "+1",
buffViewOffset = "10,30",
shareScaleTimes = 30
},
[218186] = {
id = 218186,
exceedReplaceItem = v0,
lowVer = "1.3.88.1",
name = "蜜桃猫星礼盒",
desc = "（星宝农场装饰）获得后可以改变送礼时礼物盒的样式。",
icon = "CDN:Icon_Farm_Tool_Gift_006_A_Comm",
resourceConf = {
model = "SM_Farm_Tool_Gift_006_Comm",
modelType = 2,
idleAnim = "SM_Farm_Tool_Gift_006_Idle_Comm",
IconLabelId = 102
},
scaleTimes = 40,
shareTexts = {
"星月为伴，友谊相牵。"
},
bHideInBag = true,
rotateYaw = 210,
shareOffset = {
-5,
-75
}
},
[218187] = {
id = 218187,
exceedReplaceItem = v0,
lowVer = "1.3.88.39",
name = "星星咖啡礼盒",
desc = "（星宝农场装饰）获得后可以改变送礼时礼物盒的样式。",
icon = "CDN:Icon_FarmGiftActor_5_Comm",
resourceConf = {
model = "SM_Farm_Tool_Gift_005_Comm",
modelType = 2,
idleAnim = "SM_Farm_Tool_Gift_005_Idle_Comm"
},
scaleTimes = 40,
shareTexts = {
"传递温暖，礼遇甜蜜。"
},
bHideInBag = true,
rotateYaw = 206,
shareOffset = {
-5,
-70
}
},
[218188] = {
id = 218188,
exceedReplaceItem = v0,
lowVer = "1.3.88.1",
name = "海盐甜筒果行",
desc = "（星宝农场蔬菜摊装饰）可以提高作物大丰收产量。",
icon = "CDN:Icon_Farm_BuComm_TPVegetableStall_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_TPVegetableStall_001_Comm",
modelType = 1
},
scaleTimes = 18,
shareTexts = {
"清爽一口，活力入口。 "
},
bHideInBag = true,
rotateYaw = 210,
shareOffset = {
-10,
-65
},
buff = "作物大丰收产量倍率",
buffValue = "+1",
buffViewOffset = "150,0",
shareScaleTimes = 12,
shareRotateYaw = 210
},
[218189] = {
id = 218189,
exceedReplaceItem = v0,
lowVer = "1.3.88.1",
name = "樱桃蛋糕小铺",
desc = "（星宝农场动物小铺装饰）可以提升动物小铺售价。",
icon = "CDN:Icon_Farm_BuComm_TPXumu_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_TPXumu_001_Comm",
modelType = 1
},
scaleTimes = 18,
shareTexts = {
"甜美时光，小铺珍藏。"
},
bHideInBag = true,
rotateYaw = 210,
shareOffset = {
-10,
-65
},
buff = "动物小铺售价提升",
buffValue = "+10%",
buffViewOffset = "40,80",
shareScaleTimes = 12,
shareRotateYaw = 210
},
[218190] = {
id = 218190,
exceedReplaceItem = v0,
lowVer = "1.3.88.1",
name = "海盗星船鱼铺",
desc = "（星宝农场水产摊装饰）可以提升水族箱获取农场币的效率。",
icon = "CDN:Icon_Farm_BuComm_TPFishStall_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_TPFishStall_001_Comm",
modelType = 1
},
scaleTimes = 18,
shareTexts = {
"星光点点，鲜鱼满店。"
},
bHideInBag = true,
rotateYaw = 210,
shareOffset = {
-10,
-65
},
buff = "水族箱获取农场币效率提升",
buffValue = "+5%",
buffViewOffset = "90,90",
shareScaleTimes = 12,
shareRotateYaw = 210
},
[218191] = {
id = 218191,
exceedReplaceItem = v0,
lowVer = "1.3.78.94",
name = "大力猫爪时钟",
desc = "（星宝农场时钟装饰）可以使钓鱼成熟时间缩短。",
icon = "CDN:Icon_Farm_FU_TPClock_001_A_1_Comm",
resourceConf = {
model = "SM_Farm_FU_TPClock_001_Comm",
modelType = 1
},
scaleTimes = 30,
shareTexts = {
"萌爪时钟，喵不可言。"
},
bHideInBag = true,
rotateYaw = 310,
shareOffset = {
-5,
-70
},
buff = "钓鱼成熟时间缩短",
buffValue = "-2%",
buffViewOffset = "-70,0",
shareScaleTimes = 25,
shareRotateYaw = 290
},
[218192] = {
id = 218192,
exceedReplaceItem = v0,
lowVer = "1.3.88.161",
name = "甜甜杯礼盒",
desc = "（星宝农场装饰）获得后可以改变送礼时礼物盒的样式。",
icon = "CDN:Icon_Farm_Tool_TPGift_001_A_Comm",
resourceConf = {
model = "SM_Farm_Tool_TPGift_001_Comm",
modelType = 2,
idleAnim = "SM_Farm_Tool_TPGift_001_Idle_Comm"
},
scaleTimes = 40,
shareTexts = {
"甜蜜分享，快乐加倍。"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-70
},
shareScaleTimes = 40,
shareRotateYaw = 270
},
[218193] = {
id = 218193,
exceedReplaceItem = v0,
lowVer = "1.3.88.1",
name = "布丁狗小窝",
desc = "（星宝农场宠物屋装饰）可以提升加工物的售价。",
icon = "CDN:Icon_Farm_BuComm_TPDoghouse_001_A_1_Comm",
resourceConf = {
model = "SM_Farm_BuComm_TPDoghouse_001_Comm",
modelType = 1
},
scaleTimes = 38,
shareTexts = {
"舒适布丁，萌宠天堂。"
},
bHideInBag = true,
rotateYaw = 125,
shareOffset = {
-25,
-65
},
buff = "加工物售价提升",
buffValue = "+15%",
buffViewOffset = "-30,-30",
shareScaleTimes = 25,
shareRotateYaw = 120
},
[218194] = {
id = 218194,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.88.161",
quality = 1,
name = "甜心琪琪",
desc = "（星宝农场稻草人装饰）可以提高作物大丰收产量。",
icon = "CDN:Icon_Farm_BuComm_TPMessageBoard_001_A_Comm",
resourceConf = {
model = "SM_Farm_OG_016",
modelType = 2,
idleAnim = "SK_Farm_OG_016_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_016_PV/Farm_OG_016_Intact",
scaleTimes = 60,
soundId = {
40666
},
outEnterSequence = "Farm_PV_OG_016",
shareTexts = {
"琪琪抱抱，烦恼跑跑。"
},
bHideInBag = true,
rotateYaw = 275,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218194.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218194.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218194.astc",
shareOffset = {
-5,
-65
},
buff = "作物大丰收产量倍率",
buffValue = "+3",
buffViewOffset = v6,
shareScaleTimes = 6,
shareRotateYaw = 210
},
[218555] = {
id = 218555,
lowVer = "1.3.88.1",
quality = 4,
name = "春蝉",
desc = v4,
bHideInBag = true
},
[218556] = {
id = 218556,
lowVer = "1.3.88.1",
name = "龙蛋",
desc = v4,
bHideInBag = true
},
[218557] = {
id = 218557,
lowVer = "1.3.88.1",
quality = 3,
name = "公牛雕塑",
desc = v4,
bHideInBag = true
},
[218558] = {
id = 218558,
lowVer = "1.3.88.1",
quality = 1,
name = "鹿头挂饰",
desc = v4,
bHideInBag = true
},
[218559] = {
id = 218559,
lowVer = "1.3.88.1",
quality = 4,
name = "船长的船舵",
desc = v4,
bHideInBag = true
},
[218560] = {
id = 218560,
lowVer = "1.3.88.1",
quality = 3,
name = "水母灯",
desc = v4,
bHideInBag = true
},
[218561] = {
id = 218561,
lowVer = "1.3.88.1",
name = "海豚水晶雕像",
desc = v4,
bHideInBag = true
},
[218562] = {
id = 218562,
lowVer = "1.3.88.1",
quality = 4,
name = "神秘纸箱",
desc = v4,
bHideInBag = true
},
[218563] = {
id = 218563,
lowVer = "1.3.88.1",
quality = 4,
name = "皮栓手电筒",
desc = v4,
bHideInBag = true
},
[218564] = {
id = 218564,
lowVer = "1.3.88.1",
quality = 3,
name = "玉米轮胎",
desc = v4,
bHideInBag = true
},
[218565] = {
id = 218565,
lowVer = "1.3.88.1",
quality = 1,
name = "强化锤",
desc = v4,
bHideInBag = true
},
[218828] = {
id = 218828,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "蜜糖沙发",
desc = "星宝农场小屋家具。",
icon = "ICON_SHOP_Farm_Be_S12_TPChair_01_1",
useParam = {
10704
},
bHideInBag = true,
bagType = "IBT_Farm"
},
[219305] = {
id = 219305,
exceedReplaceItem = v0,
name = "快乐小丑",
desc = v3,
icon = "CDN:Icon_Farm_Suit_016_Comm",
resourceConf = {
model = "SK_Farm_Suit_016_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-20,
-75
},
shareScaleTimes = 80,
shareRotateYaw = 210
},
[218197] = {
id = 218197,
exceedReplaceItem = v0,
lowVer = "1.3.88.39",
name = "奶油萌宠屋",
desc = "（星宝农场宠物屋装饰）可以提升加工物的售价。",
icon = "CDN:Icon_Farm_Suit_019_Comm",
resourceConf = {
model = "SM_Farm_Doghouse_003_A_1_Comm",
modelType = 1
},
scaleTimes = 28,
shareTexts = {
"甜蜜诱惑，只为宠爱。"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-15,
-70
},
buff = "加工物售价提升",
buffValue = "+15%",
buffViewOffset = "66,0,50",
shareScaleTimes = 20,
shareRotateYaw = 210
},
[219306] = {
id = 219306,
exceedReplaceItem = v0,
name = "冲浪鲨鲨",
desc = v3,
icon = "CDN:Icon_Farm_Suit_019_Comm",
resourceConf = {
model = "SK_Farm_Suit_019_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-5,
-70
},
shareScaleTimes = 80,
shareRotateYaw = 210
},
[219307] = {
id = 219307,
exceedReplaceItem = v0,
name = "暗夜伯爵",
desc = v3,
icon = "CDN:Icon_Farm_Suit_023_Comm",
resourceConf = {
model = "SK_Farm_BodySuit_023_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-15,
-75
},
shareScaleTimes = 80,
shareRotateYaw = 210
},
[219416] = {
id = 219416,
exceedReplaceItem = v0,
quality = 3,
name = "菠萝墨镜",
desc = v3,
icon = "CDN:Icon_Farm_HeadSuit_006_Comm",
resourceConf = {
model = "SK_Farm_HeadSuit_006_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219417] = {
id = 219417,
exceedReplaceItem = v0,
quality = 3,
name = "花盈冠",
desc = v3,
icon = "CDN:Icon_Farm_HeadSuit_009_Comm",
resourceConf = {
model = "SK_Farm_Suit_009_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219418] = {
id = 219418,
exceedReplaceItem = v0,
quality = 3,
name = "星夜光环",
desc = v3,
icon = "CDN:Icon_Farm_HeadSuit_012_Comm",
resourceConf = {
model = "SK_Farm_HeadSuit_012_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219614] = {
id = 219614,
exceedReplaceItem = v0,
quality = 3,
name = "热带花环",
desc = v3,
icon = "CDN:Icon_Farm_NeckSuit_006_Comm",
resourceConf = {
model = "SK_Farm_NeckSuit_006_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219615] = {
id = 219615,
exceedReplaceItem = v0,
quality = 3,
name = "皎珠链",
desc = v3,
icon = "CDN:Icon_Farm_NeckSuit_009_Comm",
resourceConf = {
model = "SK_Farm_Suit_009_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219616] = {
id = 219616,
exceedReplaceItem = v0,
quality = 3,
name = "繁星领结",
desc = v3,
icon = "CDN:Icon_Farm_NeckSuit_012_Comm",
resourceConf = {
model = "SK_Farm_NeckSuit_012_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219812] = {
id = 219812,
exceedReplaceItem = v0,
quality = 3,
name = "棕榈叶裙",
desc = v3,
icon = "CDN:Icon_Farm_BodySuit_006_Comm",
resourceConf = {
model = "SK_Farm_BodySuit_006_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219813] = {
id = 219813,
exceedReplaceItem = v0,
quality = 3,
name = "汉堡背包",
desc = v3,
icon = "CDN:Icon_Farm_Body_010_Comm",
resourceConf = {
model = "SK_Farm_Body_010_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219814] = {
id = 219814,
exceedReplaceItem = v0,
quality = 3,
name = "绮罗装",
desc = v3,
icon = "CDN:Icon_Farm_BodySuit_009_Comm",
resourceConf = {
model = "SK_Farm_Suit_009_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219815] = {
id = 219815,
exceedReplaceItem = v0,
quality = 3,
name = "星翼短裙",
desc = v3,
icon = "CDN:Icon_Farm_BodySuit_012_Comm",
resourceConf = {
model = "SK_Farm_BodySuit_012_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[218202] = {
id = 218202,
exceedReplaceItem = v0,
lowVer = "1.3.88.105",
name = "沙洲旅人石屋",
desc = "（星宝农场小屋装饰）可以增加时光跳跃仙术的每日积攒时间。",
icon = "CDN:Icon_Farm_BuComm_SHAHome_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_SHAHome_001_Comm",
modelType = 1
},
scaleTimes = 10,
shareTexts = {
"石屋听风，安然入梦。"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-65
},
buff = "时光跳跃积攒时间提升",
buffValue = "+30%",
buffViewOffset = "0,0,220",
shareScaleTimes = 7,
shareRotateYaw = 210
},
[218829] = {
id = 218829,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "宝石甜粽树",
desc = "星宝农场小屋家具。",
icon = "ICON_SHOP_Farm_De_S10_DWPlantPot_01_1",
useParam = {
10711
},
bHideInBag = true,
bagType = "IBT_Farm"
},
[218165] = {
id = 218165,
exceedReplaceItem = v0,
lowVer = "1.3.88.161",
name = "杏花酒家",
desc = "（星宝农场餐厅装饰）提升餐厅营业获得的餐厅经验。",
icon = "CDN:Icon_Farm_BuComm_CNRestaurant_001_A",
resourceConf = {
model = "SM_Farm_BuComm_CNRestaurant_001_Comm",
modelType = 1
},
scaleTimes = 9,
shareTexts = {
"杏花春雨，回味悠长。"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-2,
-80
},
buff = "餐厅营业获得餐厅经验",
buffValue = "+5%",
buffViewOffset = "0,0,400",
shareScaleTimes = 6,
shareRotateYaw = 210
},
[218195] = {
id = 218195,
exceedReplaceItem = v0,
name = "热浪岛屿",
desc = "（星宝农场风景装饰）时光跳跃积攒时间提升20%；时光跳跃积攒上限增加60分钟。",
icon = "CDN:Icon_Farm_Env_005_A_Comm",
picture = "T_Farmyard_BigIcon_Landscape_5",
bHideInBag = true
},
[218196] = {
id = 218196,
exceedReplaceItem = v0,
name = "石纹部落",
desc = "（星宝农场院落装饰）动物大丰收的产量倍率提升1倍，即月卡玩家从13倍提升至14倍，非月卡玩家从10倍提升至11倍。 ",
icon = "CDN:Icon_Farm_Env_005_B_Comm",
picture = "T_Farmyard_BigIcon_Yard_5",
bHideInBag = true
},
[218198] = {
id = 218198,
exceedReplaceItem = v0,
name = "海洋水族箱",
desc = "（星宝农场水族箱装饰）",
icon = "CDN:",
bHideInBag = true
},
[218199] = {
id = 218199,
exceedReplaceItem = v0,
name = "徽派温泉",
desc = "（星宝农场温泉装饰）",
icon = "CDN:",
resourceConf = {
modelType = 1
},
scaleTimes = 28,
bHideInBag = true,
rotateYaw = 225,
buff = "气泡临时",
buffValue = "+1",
buffViewOffset = v6
},
[218200] = {
id = 218200,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.88.161",
quality = 1,
name = "怒海鲨王号",
desc = "（星宝农场水产摊装饰）可以提升水产摊售价，鱼卡&钓鱼获取的熟练度。",
icon = "CDN:Icon_Farm_BuComm_SharkFishStall_001_A_Comm",
resourceConf = {
model = "SK_Farm_OG_015",
modelType = 2,
idleAnim = "SK_Farm_OG_015_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_015_PV/Farm_OG_015_Intact",
scaleTimes = 12,
soundId = {
40667
},
outEnterSequence = "Farm_PV_OG_015",
shareTexts = {
"沉金鲜鱼，一口千金。"
},
bHideInBag = true,
rotateYaw = 150,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218200.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218200.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218200.astc",
shareOffset = {
-10,
-65
},
buff = "水产摊售价提升",
buffValue = "+30%",
buffViewOffset = "-180,-0,150",
buffSecondLevel = -2,
buffSecond = "钓鱼获取熟练度提升",
buffSecondValue = "+10%",
shareScaleTimes = 25,
shareRotateYaw = 210
},
[218201] = {
id = 218201,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.99.1",
quality = 1,
name = "圣灵之庭",
desc = "（星宝农场小屋装饰）提升收获作物时获得的农场经验。",
icon = "CDN:",
resourceConf = {
model = "SK_Farm_OG_019",
modelType = 2,
idleAnim = "SK_Farm_OG_019_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_019_PV/Farm_OG_019_Intact",
scaleTimes = 28,
outEnterSequence = "Farm_PV_OG_019",
bHideInBag = true,
rotateYaw = 225,
buff = "收获作物获得农场经验",
buffValue = "+15%",
buffViewOffset = v6
},
[218206] = {
id = 218206,
exceedReplaceItem = v0,
lowVer = "1.3.88.161",
name = "云晶花语时钟",
desc = "（星宝农场时钟装饰）可以使餐厅预约贵宾的到访时间缩短。",
icon = "CDN:Icon_Farm_BuComm_CastleClock_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CastleClock_001_Comm",
modelType = 2,
idleAnim = "SM_Farm_BuComm_CastleClock_001_Anim_Comm"
},
scaleTimes = 28,
shareTexts = {
"云端花时，静享光阴。"
},
bHideInBag = true,
rotateYaw = 280,
shareOffset = {
-10,
-65
},
buff = "预约贵宾到访时间缩短",
buffValue = "-5%",
buffViewOffset = "0,100,200",
shareScaleTimes = 20,
shareRotateYaw = 260
},
[218203] = {
id = 218203,
exceedReplaceItem = v0,
name = "临时温泉1",
desc = "（星宝农场温泉装饰）",
icon = "CDN:",
resourceConf = {
modelType = 1
},
scaleTimes = 23,
bHideInBag = true,
rotateYaw = 225,
buff = "气泡临时",
buffValue = "+1",
buffViewOffset = v6
},
[218204] = {
id = 218204,
exceedReplaceItem = v0,
name = "临时温泉2",
desc = "（星宝农场温泉装饰）",
icon = "CDN:",
resourceConf = {
modelType = 1
},
scaleTimes = 23,
bHideInBag = true,
rotateYaw = 225,
buff = "气泡临时",
buffValue = "+1",
buffViewOffset = v6
},
[218205] = {
id = 218205,
exceedReplaceItem = v0,
lowVer = "1.3.99.1",
name = "蓝鲸号餐厅",
desc = "（星宝农场餐厅装饰）可以提高餐厅预约的金色贵宾收益倍率。",
icon = "CDN:Icon_Farm_BuComm_Restaurant_002_A",
resourceConf = {
model = "SM_Farm_BuComm_Restaurant_002_Comm",
modelType = 1
},
scaleTimes = 9,
bHideInBag = true,
rotateYaw = 205,
buff = "金色预约贵宾收益倍率",
buffValue = "+1",
buffViewOffset = v6
},
[218567] = {
id = 218567,
lowVer = "1.3.99.1",
quality = 4,
name = "神秘脚印",
desc = v4,
bHideInBag = true
},
[218568] = {
id = 218568,
lowVer = "1.3.99.1",
quality = 3,
name = "狼蛛",
desc = v4,
bHideInBag = true
},
[218569] = {
id = 218569,
lowVer = "1.3.99.1",
quality = 3,
name = "消音爪套",
desc = v4,
bHideInBag = true
},
[218570] = {
id = 218570,
lowVer = "1.3.99.1",
name = "魔术兔",
desc = v4,
bHideInBag = true
},
[218571] = {
id = 218571,
lowVer = "1.3.99.1",
quality = 3,
name = "发条蛙",
desc = v4,
bHideInBag = true
},
[218572] = {
id = 218572,
lowVer = "1.3.99.1",
quality = 1,
name = "一箱宝藏",
desc = v4,
bHideInBag = true
},
[218573] = {
id = 218573,
lowVer = "1.3.99.1",
quality = 4,
name = "玩具大炮",
desc = v4,
bHideInBag = true
},
[218574] = {
id = 218574,
lowVer = "1.3.99.1",
name = "复古风筝",
desc = v4,
bHideInBag = true
},
[218575] = {
id = 218575,
lowVer = "1.3.99.1",
quality = 4,
name = "黄金号角",
desc = v4,
bHideInBag = true
},
[218576] = {
id = 218576,
lowVer = "1.3.99.1",
quality = 4,
name = "宝石吞吞花",
desc = v4,
bHideInBag = true
},
[218577] = {
id = 218577,
lowVer = "1.3.99.1",
name = "金牌牛排",
desc = v4,
bHideInBag = true
},
[218578] = {
id = 218578,
lowVer = "1.3.99.1",
name = "鸭鸭",
desc = v4,
bHideInBag = true
},
[218579] = {
id = 218579,
lowVer = "1.3.99.1",
quality = 1,
name = "剑鱼",
desc = v4,
bHideInBag = true
},
[218580] = {
id = 218580,
lowVer = "1.3.99.1",
quality = 4,
name = "灯塔锚",
desc = v4,
bHideInBag = true
},
[218581] = {
id = 218581,
lowVer = "1.3.99.1",
quality = 4,
name = "星星鱼捏捏",
desc = v4,
bHideInBag = true
},
[218582] = {
id = 218582,
lowVer = "1.3.99.1",
name = "花饰",
desc = v4,
bHideInBag = true
},
[218207] = {
id = 218207,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
quality = 1,
name = "畜牧摊铺子",
icon = "CDN:",
resourceConf = {
model = "SK_Farm_OG_017",
modelType = 2,
idleAnim = "SK_Farm_OG_017_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_017_PV/Farm_OG_017_Intact",
scaleTimes = 28,
outEnterSequence = "Farm_PV_OG_017",
bHideInBag = true,
rotateYaw = 225,
buff = "气泡临时",
buffValue = "+1",
buffViewOffset = v6
},
[218208] = {
id = 218208,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
quality = 1,
name = "斗罗稻草人",
icon = "CDN:",
resourceConf = {
model = "SK_Farm_OG_018",
modelType = 2,
idleAnim = "SK_Farm_OG_018_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_018_PV/Farm_OG_018_Intact",
scaleTimes = 28,
outEnterSequence = "Farm_PV_OG_018",
bHideInBag = true,
rotateYaw = 225,
buff = "气泡临时",
buffValue = "+1",
buffViewOffset = v6
},
[219308] = {
id = 219308,
exceedReplaceItem = v0,
name = "格格吉祥",
desc = v3,
icon = "CDN:Icon_Farm_Suit_009_Comm",
resourceConf = {
model = "SK_Farm_BodySuit_009_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-8,
-70
},
shareScaleTimes = 80,
shareRotateYaw = 210
},
[219205] = {
id = 219205,
exceedReplaceItem = v0,
quality = 3,
name = "小金毛",
desc = "（星宝农场宠物）可以保护农场产物，重复获得会转化为云朵币。",
icon = "CDN:Icon_Farm_Golden_001_Comm",
resourceConf = {
model = "SK_Farm_Golden_001_Comm",
modelType = 2,
idleAnim = "SK_Farm_Golden_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[218209] = {
id = 218209,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.99.1",
quality = 3,
name = "牦铃货铺",
desc = "（星宝农场动物小铺装饰）可以提升动物小铺售价。",
icon = "CDN:Icon_Farm_BuComm_SHAXumu_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_SHAXumu_001_Comm",
modelType = 1
},
scaleTimes = 18,
bHideInBag = true,
rotateYaw = 210,
buff = "动物小铺售价提升",
buffValue = "+5%",
buffViewOffset = v6
},
[219617] = {
id = 219617,
exceedReplaceItem = v0,
quality = 4,
name = "红绳铃铛",
desc = v3,
icon = "Icon_Farm_Neck_016",
resourceConf = {
model = "SK_Farm_Neck_002_CAT"
},
bHideInBag = true,
rotateYaw = 225
},
[219309] = {
id = 219309,
exceedReplaceItem = v0,
name = "水手套装",
desc = v3,
icon = "CDN:Icon_Farm_Suit_010_Comm",
resourceConf = {
model = "SK_Farm_BodySuit_010_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225,
shareScaleTimes = 80,
shareRotateYaw = 210
},
[219310] = {
id = 219310,
exceedReplaceItem = v0,
name = "教皇套装",
desc = v3,
icon = "CDN:Icon_Farm_Suit_020_Comm",
resourceConf = {
model = "SK_Farm_BodySuit_020_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225,
shareScaleTimes = 80,
shareRotateYaw = 210
},
[219419] = {
id = 219419,
exceedReplaceItem = v0,
quality = 3,
name = "魔法发带",
desc = v3,
icon = "CDN:Icon_Farm_HeadSuit_005_Comm",
resourceConf = {
model = "SK_Farm_HeadSuit_005_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
}
}

local mt = {
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
quality = 2,
useType = "IUTO_SendToFarm",
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
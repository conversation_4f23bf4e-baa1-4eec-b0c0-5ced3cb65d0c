--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_元梦卡牌.xlsx: 卡包

local v0 = "卡包抽取获得"

local data = {
[290001] = {
id = 290001,
effect = true,
type = "ItemType_CardBag",
name = "田园牧歌炫彩包",
desc = "专属【田园牧歌】卡册的卡包，可固定抽出1张卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_01",
picture = "CDN:T_Common_Item_CardBag_01",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
0,
0,
902
},
useParam = {
290001
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_01",
descForActivity = "专属【田园牧歌】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290002] = {
id = 290002,
effect = true,
type = "ItemType_CardBag",
quality = 4,
name = "田园牧歌稀有包",
desc = "专属【田园牧歌】卡册的卡包，可固定抽出2张卡牌，保底至少1张2星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_02",
picture = "CDN:T_Common_Item_CardBag_02",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
0,
0,
902
},
useParam = {
290002
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_02",
descForActivity = "专属【田园牧歌】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290003] = {
id = 290003,
effect = true,
type = "ItemType_CardBag",
quality = 3,
name = "田园牧歌非凡包",
desc = "专属【田园牧歌】卡册的卡包，可固定抽出3张卡牌，保底至少1张3星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_03",
picture = "CDN:T_Common_Item_CardBag_03",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
0,
0,
902
},
useParam = {
290003
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_03",
descForActivity = "专属【田园牧歌】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290004] = {
id = 290004,
effect = true,
type = "ItemType_CardBag",
quality = 2,
name = "田园牧歌臻藏包",
desc = "专属【田园牧歌】卡册的卡包，可固定抽出4张卡牌，保底至少1张4星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_04",
picture = "CDN:T_Common_Item_CardBag_04",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
0,
0,
902
},
useParam = {
290004
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_04",
descForActivity = "专属【田园牧歌】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290005] = {
id = 290005,
effect = true,
type = "ItemType_CardBag",
quality = 1,
name = "田园牧歌超凡包",
desc = "专属【田园牧歌】卡册的卡包，可固定抽出5张卡牌，保底至少1张5星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_05",
picture = "CDN:T_Common_Item_CardBag_05",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
0,
0,
902
},
useParam = {
290005
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_05",
descForActivity = "专属【田园牧歌】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290011] = {
id = 290011,
effect = true,
type = "ItemType_CardBag",
name = "田园牧歌炫彩包",
desc = "专属【田园牧歌】卡册的卡包，可固定抽出1张卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_11",
picture = "CDN:T_Common_Item_CardBag_01",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
0,
902
},
useParam = {
290011
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_11",
descForActivity = "专属【田园牧歌】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290012] = {
id = 290012,
effect = true,
type = "ItemType_CardBag",
quality = 4,
name = "田园牧歌稀有包",
desc = "专属【田园牧歌】卡册的卡包，可固定抽出2张卡牌，保底至少1张2星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_12",
picture = "CDN:T_Common_Item_CardBag_02",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
0,
902
},
useParam = {
290012
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_12",
descForActivity = "专属【田园牧歌】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290013] = {
id = 290013,
effect = true,
type = "ItemType_CardBag",
quality = 3,
name = "田园牧歌非凡包",
desc = "专属【田园牧歌】卡册的卡包，可固定抽出3张卡牌，保底至少1张3星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_13",
picture = "CDN:T_Common_Item_CardBag_03",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
0,
902
},
useParam = {
290013
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_13",
descForActivity = "专属【田园牧歌】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290014] = {
id = 290014,
effect = true,
type = "ItemType_CardBag",
quality = 2,
name = "田园牧歌臻藏包",
desc = "专属【田园牧歌】卡册的卡包，可固定抽出4张卡牌，保底至少1张4星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_14",
picture = "CDN:T_Common_Item_CardBag_04",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
0,
902
},
useParam = {
290014
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_14",
descForActivity = "专属【田园牧歌】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290015] = {
id = 290015,
effect = true,
type = "ItemType_CardBag",
quality = 1,
name = "田园牧歌超凡包",
desc = "专属【田园牧歌】卡册的卡包，可固定抽出5张卡牌，保底至少1张5星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_15",
picture = "CDN:T_Common_Item_CardBag_05",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
0,
902
},
useParam = {
290015
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_15",
descForActivity = "专属【田园牧歌】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290018] = {
id = 290018,
effect = true,
type = "ItemType_WildCard",
quality = 1,
name = "田园牧歌万能卡",
desc = "【田园牧歌】卡册的专属万能卡牌。获得后可在限时有效期内兑换【田园牧歌】卡册中的任意1张卡牌（包括普通卡牌和金色卡牌）。超过有效期后会失效哦！",
icon = "CDN:T_Common_Item_WildCard_01",
picture = "CDN:T_Common_Item_CardBag_05",
getWay = "【活动】获得",
useParam = {
290018
},
autoUse = true
},
[290021] = {
id = 290021,
effect = true,
type = "ItemType_CardBag",
name = "缤纷市集炫彩包",
desc = "专属【缤纷市集】卡册的卡包，可固定抽出1张卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_21",
picture = "CDN:T_Common_Item_CardBag_21",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290021
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_21",
descForActivity = "专属【缤纷市集】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290022] = {
id = 290022,
effect = true,
type = "ItemType_CardBag",
quality = 4,
name = "缤纷市集稀有包",
desc = "专属【缤纷市集】卡册的卡包，可固定抽出2张卡牌，保底至少1张2星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_22",
picture = "CDN:T_Common_Item_CardBag_22",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290022
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_22",
descForActivity = "专属【缤纷市集】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290023] = {
id = 290023,
effect = true,
type = "ItemType_CardBag",
quality = 3,
name = "缤纷市集非凡包",
desc = "专属【缤纷市集】卡册的卡包，可固定抽出3张卡牌，保底至少1张3星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_23",
picture = "CDN:T_Common_Item_CardBag_23",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290023
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_23",
descForActivity = "专属【缤纷市集】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290024] = {
id = 290024,
effect = true,
type = "ItemType_CardBag",
quality = 2,
name = "缤纷市集臻藏包",
desc = "专属【缤纷市集】卡册的卡包，可固定抽出4张卡牌，保底至少1张4星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_24",
picture = "CDN:T_Common_Item_CardBag_24",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290024
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_24",
descForActivity = "专属【缤纷市集】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290025] = {
id = 290025,
effect = true,
type = "ItemType_CardBag",
quality = 1,
name = "缤纷市集超凡包",
desc = "专属【缤纷市集】卡册的卡包，可固定抽出5张卡牌，保底至少1张5星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_25",
picture = "CDN:T_Common_Item_CardBag_25",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290025
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_25",
descForActivity = "专属【缤纷市集】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290028] = {
id = 290028,
effect = true,
type = "ItemType_WildCard",
quality = 1,
name = "缤纷市集万能卡",
desc = "【缤纷市集】卡册的专属万能卡牌。获得后可在限时有效期内兑换【缤纷市集】卡册中的任意1张卡牌（包括普通卡牌和金色卡牌）。超过有效期后会失效哦！",
icon = "CDN:T_Common_Item_WildCard_01",
picture = "CDN:T_Common_Item_WildCard_01",
getWay = "【活动】获得",
useParam = {
290028
},
autoUse = true
},
[290101] = {
id = 290101,
effect = true,
name = "紫葡萄",
desc = "吃不到的葡萄最甜了",
getWay = v0,
useParam = {
1010201
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290102] = {
id = 290102,
effect = true,
name = "蓝莓",
desc = "一颗下肚，霉运全无",
getWay = v0,
useParam = {
1010202
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290103] = {
id = 290103,
effect = true,
name = "草莓",
desc = "吃颗草莓，烦恼全没",
getWay = v0,
useParam = {
1010203
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290104] = {
id = 290104,
effect = true,
name = "菠萝",
desc = "外壳带刺，内心酸甜",
getWay = v0,
useParam = {
1010204
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290105] = {
id = 290105,
effect = true,
name = "甜瓜",
desc = "你我缘分，来自糖分",
getWay = v0,
useParam = {
1010205
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290106] = {
id = 290106,
effect = true,
name = "西瓜",
desc = "中间一勺，全瓜最贵",
getWay = v0,
useParam = {
1010206
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290107] = {
id = 290107,
effect = true,
name = "苹果",
desc = "早安晚安，平平安安",
getWay = v0,
useParam = {
1010207
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290108] = {
id = 290108,
effect = true,
name = "桑葚",
desc = "桑葚拯救发际线",
getWay = v0,
useParam = {
1010208
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290109] = {
id = 290109,
effect = true,
name = "香蕉",
desc = "香蕉在手，烦恼没有",
getWay = v0,
useParam = {
1010209
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290110] = {
id = 290110,
effect = true,
name = "奶牛",
desc = "和奶牛一起变社牛！",
getWay = v0,
useParam = {
1010501
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290111] = {
id = 290111,
effect = true,
name = "湖羊",
desc = "披上小羊皮很幸“湖”",
getWay = v0,
useParam = {
1010502
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290112] = {
id = 290112,
effect = true,
name = "青羊",
desc = "青羊出街，掀起自然风",
getWay = v0,
useParam = {
1010503
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290113] = {
id = 290113,
effect = true,
name = "金棕猪",
desc = "幸福就是吃吃睡睡",
getWay = v0,
useParam = {
1010504
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290114] = {
id = 290114,
effect = true,
name = "小茶鸭",
desc = "呷一口生活的小确幸",
getWay = v0,
useParam = {
1010505
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290115] = {
id = 290115,
effect = true,
name = "小青驴",
desc = "倔强的小青驴从不认输",
getWay = v0,
useParam = {
1010506
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290116] = {
id = 290116,
effect = true,
name = "雪羽鸡",
desc = "让你实现吃“鸡”自由",
getWay = v0,
useParam = {
1010507
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290117] = {
id = 290117,
effect = true,
name = "汗血马",
desc = "是传说中的“宝马”",
getWay = v0,
useParam = {
1010508
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290118] = {
id = 290118,
effect = true,
name = "灰雁鹅",
desc = "梦想是成为白天鹅",
getWay = v0,
useParam = {
1010509
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290119] = {
id = 290119,
effect = true,
name = "玉米",
desc = "熟透了，就会咧开嘴",
getWay = v0,
useParam = {
1010701
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290120] = {
id = 290120,
effect = true,
name = "麦子",
desc = "春种秋收，使命必达",
getWay = v0,
useParam = {
1010702
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290121] = {
id = 290121,
effect = true,
name = "棉花",
desc = "你也是惹到棉花啦",
getWay = v0,
useParam = {
1010703
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290122] = {
id = 290122,
effect = true,
name = "花生",
desc = "花生磕出百味心情",
getWay = v0,
useParam = {
1010704
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290123] = {
id = 290123,
effect = true,
name = "葫芦",
desc = "装下整个世界的内心",
getWay = v0,
useParam = {
1010705
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290124] = {
id = 290124,
effect = true,
name = "豆薯",
desc = "每一口，都接地气",
getWay = v0,
useParam = {
1010706
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290125] = {
id = 290125,
effect = true,
name = "魔芋",
desc = "除了健康，一无所有",
getWay = v0,
useParam = {
1010707
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290126] = {
id = 290126,
effect = true,
name = "竹笋",
desc = "土生土长，宝宝探头",
getWay = v0,
useParam = {
1010708
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290127] = {
id = 290127,
effect = true,
name = "洋葱头蔬果屋",
desc = "层层叠叠的简单内心",
getWay = v0,
useParam = {
1010709
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290128] = {
id = 290128,
effect = true,
name = "激流大眼鱼",
desc = "勇者总会逆流而上",
getWay = v0,
useParam = {
1011001
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290129] = {
id = 290129,
effect = true,
name = "丁香海牛",
desc = "擅长在水中打盹",
getWay = v0,
useParam = {
1011002
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290130] = {
id = 290130,
effect = true,
name = "胭脂鱼",
desc = "自带口红，香吻奉上",
getWay = v0,
useParam = {
1011003
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290131] = {
id = 290131,
effect = true,
name = "璀璨星空",
desc = "像是海底流动的星河",
getWay = v0,
useParam = {
1011004
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290132] = {
id = 290132,
effect = true,
name = "樱花太阳鱼",
desc = "水族箱里的“显眼包”",
getWay = v0,
useParam = {
1011005
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290133] = {
id = 290133,
effect = true,
name = "玫瑰王鲨",
desc = "一抹华贵的玫红掠过",
getWay = v0,
useParam = {
1011006
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290134] = {
id = 290134,
effect = true,
name = "电光犬",
desc = "电光犬会一直跟你游",
getWay = v0,
useParam = {
1011007
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290135] = {
id = 290135,
effect = true,
name = "幽灵火箭",
desc = "一起坐火箭遨游海底吧",
getWay = v0,
useParam = {
1011008
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290136] = {
id = 290136,
effect = true,
name = "沙沙渔获",
desc = "装下每一次捕捞的喜悦",
getWay = v0,
useParam = {
1011009
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290201] = {
id = 290201,
effect = true,
name = "卷心菜",
desc = "卷且不菜，十分可爱",
useParam = {
1020101
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290202] = {
id = 290202,
effect = true,
name = "冬瓜",
desc = "翠绿外表，白嫩内心",
useParam = {
1020102
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290203] = {
id = 290203,
effect = true,
name = "南瓜",
desc = "挖空心思，吓你一跳",
useParam = {
1020103
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290204] = {
id = 290204,
effect = true,
name = "胡萝卜",
desc = "拔萝卜比赛冠军选手！",
useParam = {
1020104
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290205] = {
id = 290205,
effect = true,
name = "香菇",
desc = "美味香菇，不可估量",
useParam = {
1020105
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290206] = {
id = 290206,
effect = true,
name = "大葱",
desc = "好菜必备，没你不行",
useParam = {
1020106
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290207] = {
id = 290207,
effect = true,
name = "洋葱",
desc = "再坚强，也会为我流泪",
useParam = {
1020107
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290208] = {
id = 290208,
effect = true,
name = "娃娃菜",
desc = "根茎的花纹是我的年轮",
useParam = {
1020108
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290209] = {
id = 290209,
effect = true,
name = "花菜",
desc = "全世界最白的菜",
useParam = {
1020109
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290210] = {
id = 290210,
effect = true,
name = "紫葡萄",
desc = "吃不到的葡萄最甜了",
useParam = {
1020201
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290211] = {
id = 290211,
effect = true,
name = "蓝莓",
desc = "一颗下肚，霉运全无",
useParam = {
1020202
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290212] = {
id = 290212,
effect = true,
name = "草莓",
desc = "吃颗草莓，烦恼全没",
useParam = {
1020203
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290213] = {
id = 290213,
effect = true,
name = "菠萝",
desc = "外壳带刺，内心酸甜",
useParam = {
1020204
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290214] = {
id = 290214,
effect = true,
name = "甜瓜",
desc = "你我缘分，来自糖分",
useParam = {
1020205
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290215] = {
id = 290215,
effect = true,
name = "西瓜",
desc = "中间一勺，全瓜最贵",
useParam = {
1020206
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290216] = {
id = 290216,
effect = true,
name = "苹果",
desc = "早安晚安，平平安安",
useParam = {
1020207
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290217] = {
id = 290217,
effect = true,
name = "桑葚",
desc = "桑葚拯救发际线",
useParam = {
1020208
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290218] = {
id = 290218,
effect = true,
name = "香蕉",
desc = "香蕉在手，烦恼没有",
useParam = {
1020209
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290219] = {
id = 290219,
effect = true,
name = "黄牛奶",
desc = "一杯牛奶，暖胃暖心",
useParam = {
1020301
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290220] = {
id = 290220,
effect = true,
name = "白水豚毛",
desc = "水豚没毛，保暖不妙",
useParam = {
1020302
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290221] = {
id = 290221,
effect = true,
name = "金耳廓狐毛",
desc = "对于美丽，略懂皮毛",
useParam = {
1020303
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290222] = {
id = 290222,
effect = true,
name = "蓝颈鸵鸟蛋",
desc = "各就各位，我要出生了",
useParam = {
1020304
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290223] = {
id = 290223,
effect = true,
name = "金丝羊驼毛",
desc = "羊驼也有脱发烦恼",
useParam = {
1020305
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290224] = {
id = 290224,
effect = true,
name = "森林鹿玩偶",
desc = "森林之谜，小鹿哟哟",
useParam = {
1020306
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290225] = {
id = 290225,
effect = true,
name = "小熊猫钱包",
desc = "专业管家，随时候命",
useParam = {
1020307
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290226] = {
id = 290226,
effect = true,
name = "袋鼠抱枕",
desc = "小宝宝，快来我怀里",
useParam = {
1020308
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290227] = {
id = 290227,
effect = true,
name = "小香猪存钱罐",
desc = "投币吧，倒数幸福时刻",
useParam = {
1020309
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290228] = {
id = 290228,
effect = true,
name = "向日葵",
desc = "每天都在忙着追太阳",
useParam = {
1020401
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290229] = {
id = 290229,
effect = true,
name = "彩色风铃草",
desc = "听，是风在唱歌",
useParam = {
1020402
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290230] = {
id = 290230,
effect = true,
name = "黄色郁金香",
desc = "看，郁金香裹上了阳光",
useParam = {
1020403
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290231] = {
id = 290231,
effect = true,
name = "紫色风信子",
desc = "风传花信，纸短情长",
useParam = {
1020404
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290232] = {
id = 290232,
effect = true,
name = "红玫瑰",
desc = "王子也会为我弯腰",
useParam = {
1020405
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290233] = {
id = 290233,
effect = true,
name = "火鹤花",
desc = "是花海中的红云一朵",
useParam = {
1020406
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290234] = {
id = 290234,
effect = true,
name = "黄百合",
desc = "一抹明黄，点亮阴雨天",
useParam = {
1020407
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290235] = {
id = 290235,
effect = true,
name = "红色虞美人",
desc = "去虞美人盛开的山坡上",
useParam = {
1020408
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290236] = {
id = 290236,
effect = true,
name = "粉目菊",
desc = "秋末冬初的一抹温柔",
useParam = {
1020409
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290237] = {
id = 290237,
effect = true,
name = "奶牛",
desc = "和奶牛一起变社牛！",
useParam = {
1020501
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290238] = {
id = 290238,
effect = true,
name = "湖羊",
desc = "披上小羊皮很幸“湖”",
useParam = {
1020502
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290239] = {
id = 290239,
effect = true,
name = "青羊",
desc = "青羊出街，掀起自然风",
useParam = {
1020503
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290240] = {
id = 290240,
effect = true,
name = "金棕猪",
desc = "幸福就是吃吃睡睡",
useParam = {
1020504
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290241] = {
id = 290241,
effect = true,
name = "小茶鸭",
desc = "呷一口生活的小确幸",
useParam = {
1020505
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290242] = {
id = 290242,
effect = true,
name = "小青驴",
desc = "倔强的小青驴从不认输",
useParam = {
1020506
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290243] = {
id = 290243,
effect = true,
name = "雪羽鸡",
desc = "让你实现吃“鸡”自由",
useParam = {
1020507
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290244] = {
id = 290244,
effect = true,
name = "汗血马",
desc = "是传说中的“宝马”",
useParam = {
1020508
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290245] = {
id = 290245,
effect = true,
name = "灰雁鹅",
desc = "梦想是成为白天鹅",
useParam = {
1020509
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290246] = {
id = 290246,
effect = true,
name = "钻嘴鱼",
desc = "钻石嘴巴豆腐心",
useParam = {
1020601
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290247] = {
id = 290247,
effect = true,
name = "海金鱼",
desc = "勇敢的金鱼会游向大海",
useParam = {
1020602
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
}
}

local mt = {
effect = false,
type = "ItemType_Card",
stackedNum = 999999,
maxNum = 999999,
quality = 5,
getWay = "卡包抽取",
bagId = 1,
autoUse = false,
bHideInBag = false,
noShowTipsNum = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
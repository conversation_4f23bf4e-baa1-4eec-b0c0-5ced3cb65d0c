--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-农场

local v0 = 1

local v1 = {
limitType = "MBCT_FarmLevel",
limitParam = 3,
limitTips = "目前无法购买，请先将【农场等级】提升到3级"
}

local v2 = "*********"

local v3 = "*********"

local v4 = "MCL_LifeLongLimit"

local data = {
[103504] = {
commodityId = 103504,
commodityName = "星宝农场月卡",
coinType = 1,
price = 298,
beginTime = {
seconds = 1720627200
},
itemIds = {
218002
},
buyChecker = v1
},
[113505] = {
commodityId = 113505,
commodityName = "甜兔屋",
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1729785599
},
jumpId = 366,
jumpText = "甜兔仙踪",
minVersion = "*********",
itemIds = {
218103
},
cumuRecvNumMax = 1
},
[113506] = {
commodityId = 113506,
commodityName = "稻草人汉克",
limitNum = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1729785599
},
jumpId = 366,
jumpText = "甜兔仙踪",
minVersion = "*********",
itemIds = {
218102
},
cumuRecvNumMax = 1
},
[113509] = {
commodityId = 113509,
commodityName = "甜兔仙踪礼盒",
coinType = 1,
price = 1200,
limitNum = 1,
beginTime = {
seconds = 1724342400
},
minVersion = "1.3.12.148",
itemIds = {
310253
},
canGift = true,
addIntimacy = 120,
giftCoinType = 1,
giftPrice = 1200,
cumuRecvNumMax = 1
},
[113510] = {
commodityId = 113510,
commodityName = "小云宝",
limitNum = 1,
beginTime = {
seconds = 1724385600
},
jumpId = 5100,
jumpText = "前往星宝农场",
minVersion = "1.3.12.148",
itemIds = {
218104
},
buyChecker = v1,
cumuRecvNumMax = 1
},
[113507] = {
commodityId = 113507,
commodityName = "洋葱头蔬果屋",
limitNum = 1,
beginTime = {
seconds = 1725033600
},
endTime = {
seconds = 1726243199
},
jumpId = 412,
jumpText = "洋葱牛奶祈愿",
minVersion = "1.3.18.1",
itemIds = {
218105
},
canGift = true,
addIntimacy = 100,
giftCoinType = 1,
giftPrice = 980,
cumuRecvNumMax = 1
},
[113508] = {
commodityId = 113508,
commodityName = "牛牛牧场小店",
limitNum = 1,
beginTime = {
seconds = 1725033600
},
endTime = {
seconds = 1726243199
},
jumpId = 413,
jumpText = "洋葱牛奶祈愿",
minVersion = "1.3.18.1",
itemIds = {
218106
},
canGift = true,
addIntimacy = 100,
giftCoinType = 1,
giftPrice = 980,
cumuRecvNumMax = 1
},
[113511] = {
commodityId = 113511,
commodityName = "丰收兔",
limitNum = 1,
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1735228799
},
jumpId = 368,
jumpText = "丰收兔",
minVersion = v2,
itemIds = {
218107
},
cumuRecvNumMax = 1
},
[113512] = {
commodityId = 113512,
commodityName = "沙沙渔获",
limitNum = 1,
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1735228799
},
jumpId = 368,
jumpText = "丰收兔",
minVersion = v2,
itemIds = {
218110
},
cumuRecvNumMax = 1
},
[113513] = {
commodityId = 113513,
commodityName = "丰收兔礼盒",
coinType = 1,
price = 3930,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1736438400
},
minVersion = "1.3.37.98",
itemIds = {
310672
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3930,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113514] = {
commodityId = 113514,
commodityName = "洋葱头蔬果屋",
coinType = 1,
price = 980,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1726243200
},
minVersion = "1.3.18.1",
itemIds = {
218105
},
canGift = true,
addIntimacy = 100,
giftCoinType = 1,
giftPrice = 980,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113515] = {
commodityId = 113515,
commodityName = "牛牛牧场小店",
coinType = 1,
price = 980,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1726243200
},
minVersion = "1.3.18.1",
itemIds = {
218106
},
canGift = true,
addIntimacy = 100,
giftCoinType = 1,
giftPrice = 980,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113516] = {
commodityId = 113516,
commodityName = "仙人掌花屋",
limitNum = 1,
beginTime = {
seconds = 1726761600
},
endTime = {
seconds = 1729785599
},
jumpId = 372,
jumpText = "绿洲奇遇",
minVersion = "1.3.18.1",
itemIds = {
218116
},
canGift = true,
addIntimacy = 220,
giftCoinType = 1,
giftPrice = 2120,
cumuRecvNumMax = 1
},
[113517] = {
commodityId = 113517,
commodityName = "胡萝卜时钟",
limitNum = 1,
beginTime = {
seconds = 1726804800
},
jumpId = 5100,
jumpText = "前往星宝农场",
minVersion = v2,
itemIds = {
218114
},
buyChecker = v1,
cumuRecvNumMax = 1
},
[113518] = {
commodityId = 113518,
commodityName = "告白熊梦幻屋",
limitNum = 1,
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1741881599
},
jumpId = 573,
jumpText = "梦幻告白",
minVersion = v2,
itemIds = {
218115
},
cumuRecvNumMax = 1
},
[113519] = {
commodityId = 113519,
commodityName = "蔷薇花车",
limitNum = 1,
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1741881599
},
jumpId = 573,
jumpText = "梦幻告白",
minVersion = v2,
itemIds = {
218119
},
cumuRecvNumMax = 1
},
[113520] = {
commodityId = 113520,
commodityName = "彩虹牧场",
limitNum = 1,
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1741881599
},
jumpId = 573,
jumpText = "梦幻告白",
minVersion = v2,
itemIds = {
218120
},
cumuRecvNumMax = 1
},
[113521] = {
commodityId = 113521,
commodityName = "梦幻海洋屋",
limitNum = 1,
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1741881599
},
jumpId = 573,
jumpText = "梦幻告白",
minVersion = v2,
itemIds = {
218121
},
cumuRecvNumMax = 1
},
[113522] = {
commodityId = 113522,
commodityName = "云朵奶油时钟",
limitNum = 1,
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1741881599
},
jumpId = 573,
jumpText = "梦幻告白",
minVersion = v2,
itemIds = {
218118
},
cumuRecvNumMax = 1
},
[113523] = {
commodityId = 113523,
commodityName = "梦幻告白礼盒",
coinType = 1,
price = 16200,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1739462400
},
minVersion = v2,
itemIds = {
310687
},
canGift = true,
addIntimacy = 1600,
giftCoinType = 1,
giftPrice = 16200,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113524] = {
commodityId = 113524,
commodityName = "狐仙",
limitNum = 1,
beginTime = {
seconds = 1727884800
},
endTime = {
seconds = 1732204799
},
jumpId = 379,
jumpText = "仙狐花隐",
minVersion = "1.3.18.1",
itemIds = {
218112
},
cumuRecvNumMax = 1
},
[113525] = {
commodityId = 113525,
commodityName = "花蔓时钟",
limitNum = 1,
beginTime = {
seconds = 1727884800
},
endTime = {
seconds = 1732204799
},
jumpId = 379,
jumpText = "仙狐花隐",
minVersion = "1.3.18.1",
itemIds = {
218122
},
cumuRecvNumMax = 1
},
[113526] = {
commodityId = 113526,
commodityName = "仙狐花隐礼盒",
limitNum = 1,
beginTime = {
seconds = 1727884800
},
endTime = {
seconds = 1732204799
},
jumpId = 379,
jumpText = "仙狐花隐",
minVersion = "1.3.18.1",
itemIds = {
310688
},
canGift = true,
addIntimacy = 200,
giftCoinType = 1,
giftPrice = 1960,
cumuRecvNumMax = 1
},
[113527] = {
commodityId = 113527,
commodityName = "招财喵",
limitNum = 1,
beginTime = {
seconds = 1747670400
},
endTime = {
seconds = 1750348799
},
jumpId = 400,
jumpText = "招财喵",
minVersion = "1.3.88.92",
itemIds = {
218117
},
cumuRecvNumMax = 1
},
[113528] = {
commodityId = 113528,
commodityName = "梦幻熊礼盒",
limitNum = 1,
beginTime = {
seconds = 1747670400
},
endTime = {
seconds = 1750348799
},
jumpId = 400,
jumpText = "招财喵",
minVersion = "1.3.88.92",
itemIds = {
218130
},
cumuRecvNumMax = 1
},
[113529] = {
commodityId = 113529,
commodityName = "招财喵礼包",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1747670400
},
endTime = {
seconds = 1750348799
},
jumpId = 400,
jumpText = "招财喵",
minVersion = "1.3.88.92",
itemIds = {
310707
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3600,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113530] = {
commodityId = 113530,
commodityName = "绮丽海螺城堡",
limitNum = 1,
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1747324799
},
jumpId = 450,
jumpText = "蔚海绮梦",
minVersion = "1.3.78.96",
itemIds = {
218123
},
cumuRecvNumMax = 1
},
[113531] = {
commodityId = 113531,
commodityName = "璃海星光果行",
limitNum = 1,
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1747324799
},
jumpId = 450,
jumpText = "蔚海绮梦",
minVersion = "1.3.78.96",
itemIds = {
218125
},
cumuRecvNumMax = 1
},
[113532] = {
commodityId = 113532,
commodityName = "海盗宝藏小店",
limitNum = 1,
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1747324799
},
jumpId = 450,
jumpText = "蔚海绮梦",
minVersion = "1.3.78.96",
itemIds = {
218126
},
cumuRecvNumMax = 1
},
[113533] = {
commodityId = 113533,
commodityName = "海妖鱼店",
limitNum = 1,
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1747324799
},
jumpId = 450,
jumpText = "蔚海绮梦",
minVersion = "1.3.78.96",
itemIds = {
218127
},
cumuRecvNumMax = 1
},
[113534] = {
commodityId = 113534,
commodityName = "深海时钟",
limitNum = 1,
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1747324799
},
jumpId = 450,
jumpText = "蔚海绮梦",
minVersion = "1.3.78.96",
itemIds = {
218128
},
cumuRecvNumMax = 1
},
[113535] = {
commodityId = 113535,
commodityName = "蔚海绮梦礼盒",
coinType = 1,
price = 16200,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1747324800
},
minVersion = "1.3.78.96",
itemIds = {
310708
},
canGift = true,
addIntimacy = 1600,
giftCoinType = 1,
giftPrice = 16200,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113536] = {
commodityId = 113536,
commodityName = "冰晶星梦城堡",
limitNum = 1,
beginTime = {
seconds = 1747065600
},
endTime = {
seconds = 1751558399
},
jumpId = 509,
jumpText = "雪境欢颂",
minVersion = v3,
itemIds = {
218142
},
cumuRecvNumMax = 1
},
[113537] = {
commodityId = 113537,
commodityName = "雪花轻语果行",
limitNum = 1,
beginTime = {
seconds = 1747065600
},
endTime = {
seconds = 1751558399
},
jumpId = 509,
jumpText = "雪境欢颂",
minVersion = v3,
itemIds = {
218134
},
cumuRecvNumMax = 1
},
[113538] = {
commodityId = 113538,
commodityName = "水晶鹿角小店",
limitNum = 1,
beginTime = {
seconds = 1747065600
},
endTime = {
seconds = 1751558399
},
jumpId = 509,
jumpText = "雪境欢颂",
minVersion = v3,
itemIds = {
218135
},
cumuRecvNumMax = 1
},
[113539] = {
commodityId = 113539,
commodityName = "雪乡小Q鱼铺",
limitNum = 1,
beginTime = {
seconds = 1747065600
},
endTime = {
seconds = 1751558399
},
jumpId = 509,
jumpText = "雪境欢颂",
minVersion = v3,
itemIds = {
218136
},
cumuRecvNumMax = 1
},
[113540] = {
commodityId = 113540,
commodityName = "糖果松树时钟",
limitNum = 1,
beginTime = {
seconds = 1747065600
},
endTime = {
seconds = 1751558399
},
jumpId = 509,
jumpText = "雪境欢颂",
minVersion = v3,
itemIds = {
218138
},
cumuRecvNumMax = 1
},
[113541] = {
commodityId = 113541,
commodityName = "雪境欢颂礼盒",
coinType = 1,
price = 16200,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1747065600
},
jumpId = 509,
jumpText = "雪境欢颂",
minVersion = v3,
itemIds = {
310709
},
canGift = true,
addIntimacy = 1600,
giftCoinType = 1,
giftPrice = 16200,
buyChecker = v1,
cumuRecvNumMax = 1,
jumpBeginTime = {
seconds = 1749225600
},
jumpEndTime = {
seconds = 1751558399
}
},
[113542] = {
commodityId = 113542,
commodityName = "海狮公主",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1740153600
},
endTime = {
seconds = 1743091199
},
jumpId = 510,
jumpText = "海狮公主",
minVersion = v2,
itemIds = {
218124
},
cumuRecvNumMax = 1
},
[113543] = {
commodityId = 113543,
commodityName = "泡泡鱼礼盒",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1740153600
},
endTime = {
seconds = 1743091199
},
jumpId = 510,
jumpText = "海狮公主",
minVersion = v2,
itemIds = {
218133
},
cumuRecvNumMax = 1
},
[113544] = {
commodityId = 113544,
commodityName = "海狮公主礼包",
coinType = 1,
price = 3930,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1742313600
},
minVersion = "1.3.68.98",
itemIds = {
310710
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3930,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113545] = {
commodityId = 113545,
commodityName = "雪球精灵",
limitNum = 1,
beginTime = {
seconds = 1736438400
},
endTime = {
seconds = 1738252799
},
jumpId = 521,
jumpText = "雪球精灵",
minVersion = v2,
itemIds = {
218137
},
cumuRecvNumMax = 1
},
[113546] = {
commodityId = 113546,
commodityName = "冬日萌宠屋",
limitNum = 1,
beginTime = {
seconds = 1736438400
},
endTime = {
seconds = 1738252799
},
jumpId = 521,
jumpText = "雪球精灵",
minVersion = v2,
itemIds = {
218139
},
cumuRecvNumMax = 1
},
[113547] = {
commodityId = 113547,
commodityName = "雪球精灵礼包",
limitNum = 1,
beginTime = {
seconds = 1736438400
},
endTime = {
seconds = 1738252799
},
jumpId = 521,
jumpText = "雪球精灵",
minVersion = v2,
itemIds = {
310711
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3930,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113548] = {
commodityId = 113548,
commodityName = "庆丰年宅院",
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1743350399
},
jumpId = 538,
jumpText = "仙福盈门",
minVersion = v2,
itemIds = {
218159
},
cumuRecvNumMax = 1
},
[113549] = {
commodityId = 113549,
commodityName = "仙福满满时钟",
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1743350399
},
jumpId = 538,
jumpText = "仙福盈门",
minVersion = v2,
itemIds = {
218156
},
cumuRecvNumMax = 1
},
[113550] = {
commodityId = 113550,
commodityName = "仙福盈门礼盒",
coinType = 1,
price = 3600,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1740672000
},
minVersion = v2,
itemIds = {
310719
},
canGift = true,
addIntimacy = 360,
giftCoinType = 1,
giftPrice = 3600,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113551] = {
commodityId = 113551,
commodityName = "琳琅摘星阁",
limitNum = 1,
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740671999
},
jumpId = 546,
jumpText = "福运琳琅",
minVersion = v3,
itemIds = {
218151
},
cumuRecvNumMax = 1
},
[113552] = {
commodityId = 113552,
commodityName = "金玉醒狮果行",
limitNum = 1,
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740671999
},
jumpId = 546,
jumpText = "福运琳琅",
minVersion = v3,
itemIds = {
218152
},
cumuRecvNumMax = 1
},
[113553] = {
commodityId = 113553,
commodityName = "金闪闪小铺",
limitNum = 1,
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740671999
},
jumpId = 546,
jumpText = "福运琳琅",
minVersion = v3,
itemIds = {
218153
},
cumuRecvNumMax = 1
},
[113554] = {
commodityId = 113554,
commodityName = "鲤跃龙门鱼铺",
limitNum = 1,
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740671999
},
jumpId = 546,
jumpText = "福运琳琅",
minVersion = v3,
itemIds = {
218154
},
cumuRecvNumMax = 1
},
[113555] = {
commodityId = 113555,
commodityName = "福运琳琅礼盒",
coinType = 1,
price = 14600,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1740326400
},
minVersion = v3,
itemIds = {
310720
},
canGift = true,
addIntimacy = 1450,
giftCoinType = 1,
giftPrice = 14600,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113556] = {
commodityId = 113556,
commodityName = "嘶嘶灵宝",
limitNum = 1,
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740067199
},
jumpId = 551,
jumpText = "嘶嘶灵宝",
minVersion = v2,
itemIds = {
218155
},
cumuRecvNumMax = 1
},
[113557] = {
commodityId = 113557,
commodityName = "宝莲灯礼盒",
limitNum = 1,
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740067199
},
jumpId = 551,
jumpText = "嘶嘶灵宝",
minVersion = v2,
itemIds = {
218157
},
cumuRecvNumMax = 1
},
[113558] = {
commodityId = 113558,
commodityName = "嘶嘶灵宝礼包",
coinType = 1,
price = 3930,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1740585600
},
minVersion = v2,
itemIds = {
310721
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3930,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113559] = {
commodityId = 113559,
commodityName = "梦幻萌宠屋",
coinType = 1,
price = 1260,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1741708800
},
minVersion = v2,
itemIds = {
218160
},
canGift = true,
addIntimacy = 130,
giftCoinType = 1,
giftPrice = 1260,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113560] = {
commodityId = 113560,
commodityName = "雪境欢颂礼盒",
coinType = 1,
price = 16200,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 4072953600
},
minVersion = v3,
itemIds = {
310709
},
canGift = true,
addIntimacy = 1600,
giftCoinType = 1,
giftPrice = 16200,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113561] = {
commodityId = 113561,
commodityName = "雪球精灵礼包",
coinType = 1,
price = 3930,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1738252800
},
minVersion = "1.3.68.50",
itemIds = {
310711
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3930,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113562] = {
commodityId = 113562,
commodityName = "卧龙宝宝",
limitNum = 1,
beginTime = {
seconds = 1739505600
},
jumpId = 5100,
jumpText = "前往星宝农场",
minVersion = "1.3.12.148",
itemIds = {
218108
},
buyChecker = v1,
cumuRecvNumMax = 1
},
[113563] = {
commodityId = 113563,
commodityName = "烟雨小筑",
limitNum = 1,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742486399
},
jumpId = 575,
jumpText = "桃坞问春",
minVersion = v3,
itemIds = {
218161
},
cumuRecvNumMax = 1
},
[113564] = {
commodityId = 113564,
commodityName = "江南果行",
limitNum = 1,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742486399
},
jumpId = 575,
jumpText = "桃坞问春",
minVersion = v3,
itemIds = {
218162
},
cumuRecvNumMax = 1
},
[113565] = {
commodityId = 113565,
commodityName = "芙蓉商行",
limitNum = 1,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742486399
},
jumpId = 575,
jumpText = "桃坞问春",
minVersion = v3,
itemIds = {
218163
},
cumuRecvNumMax = 1
},
[113566] = {
commodityId = 113566,
commodityName = "观鱼小铺",
limitNum = 1,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742486399
},
jumpId = 575,
jumpText = "桃坞问春",
minVersion = v3,
itemIds = {
218164
},
cumuRecvNumMax = 1
},
[113567] = {
commodityId = 113567,
commodityName = "悠悠亭",
limitNum = 1,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742486399
},
jumpId = 575,
jumpText = "桃坞问春",
minVersion = v3,
itemIds = {
218166
},
cumuRecvNumMax = 1
},
[113568] = {
commodityId = 113568,
commodityName = "日晷",
limitNum = 1,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742486399
},
jumpId = 575,
jumpText = "桃坞问春",
minVersion = v3,
itemIds = {
218167
},
cumuRecvNumMax = 1
},
[113569] = {
commodityId = 113569,
commodityName = "桃坞问春礼盒",
coinType = 1,
price = 10800,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1741795200
},
minVersion = v3,
itemIds = {
310732
},
canGift = true,
addIntimacy = 1100,
giftCoinType = 1,
giftPrice = 10800,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113570] = {
commodityId = 113570,
commodityName = "幽灵巫师",
limitNum = 1,
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1741017599
},
jumpId = 582,
jumpText = "丰收派对",
minVersion = v3,
itemIds = {
219302
},
canGift = true,
addIntimacy = 90,
giftCoinType = 1,
giftPrice = 860,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113571] = {
commodityId = 113571,
commodityName = "收获日",
limitNum = 1,
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1741017599
},
jumpId = 582,
jumpText = "丰收派对",
minVersion = v3,
itemIds = {
219300
},
canGift = true,
addIntimacy = 90,
giftCoinType = 1,
giftPrice = 860,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113572] = {
commodityId = 113572,
commodityName = "快乐涮涮屋",
limitNum = 1,
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1746028799
},
jumpId = 586,
jumpText = "珍馐百味",
minVersion = "1.3.78.33",
itemIds = {
218171
},
cumuRecvNumMax = 1
},
[113573] = {
commodityId = 113573,
commodityName = "三明治果行",
limitNum = 1,
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1746028799
},
jumpId = 586,
jumpText = "珍馐百味",
minVersion = "1.3.78.33",
itemIds = {
218172
},
cumuRecvNumMax = 1
},
[113574] = {
commodityId = 113574,
commodityName = "罐罐茶小铺",
limitNum = 1,
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1746028799
},
jumpId = 586,
jumpText = "珍馐百味",
minVersion = "1.3.78.33",
itemIds = {
218173
},
cumuRecvNumMax = 1
},
[113575] = {
commodityId = 113575,
commodityName = "豪华寿司鱼店",
limitNum = 1,
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1746028799
},
jumpId = 586,
jumpText = "珍馐百味",
minVersion = "1.3.78.33",
itemIds = {
218174
},
cumuRecvNumMax = 1
},
[113576] = {
commodityId = 113576,
commodityName = "珍馐百味礼盒",
coinType = 1,
price = 14600,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1744646400
},
minVersion = "1.3.78.33",
itemIds = {
310737
},
canGift = true,
addIntimacy = 1450,
giftCoinType = 1,
giftPrice = 14600,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113577] = {
commodityId = 113577,
commodityName = "狐爷爷",
limitNum = 1,
beginTime = {
seconds = 1743177600
},
endTime = {
seconds = 1745510399
},
jumpId = 592,
jumpText = "狐爷爷",
minVersion = "1.3.78.58",
itemIds = {
218183
},
cumuRecvNumMax = 1
},
[113578] = {
commodityId = 113578,
commodityName = "披萨时钟",
limitNum = 1,
beginTime = {
seconds = 1743177600
},
endTime = {
seconds = 1745510399
},
jumpId = 592,
jumpText = "狐爷爷",
minVersion = "1.3.78.58",
itemIds = {
218178
},
cumuRecvNumMax = 1
},
[113579] = {
commodityId = 113579,
commodityName = "狐爷爷礼盒",
limitNum = 1,
beginTime = {
seconds = 1743177600
},
endTime = {
seconds = 1745510399
},
jumpId = 592,
jumpText = "狐爷爷",
minVersion = "1.3.78.58",
itemIds = {
310739
},
canGift = true,
addIntimacy = 200,
giftCoinType = 1,
giftPrice = 1960,
cumuRecvNumMax = 1
},
[113580] = {
commodityId = 113580,
commodityName = "蜜糖饼庭院",
limitNum = 1,
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
jumpId = 594,
jumpText = "蜜糖彩虹之梦",
minVersion = "1.3.78.96",
itemIds = {
218170
},
cumuRecvNumMax = 1
},
[113581] = {
commodityId = 113581,
commodityName = "大力猫爪时钟",
limitNum = 1,
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
jumpId = 594,
jumpText = "蜜糖彩虹之梦",
minVersion = "1.3.78.96",
itemIds = {
218191
},
cumuRecvNumMax = 1
},
[113582] = {
commodityId = 113582,
commodityName = "蜜糖彩虹礼盒",
limitNum = 1,
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
jumpId = 594,
jumpText = "蜜糖彩虹之梦",
minVersion = "1.3.78.96",
itemIds = {
310740
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3980,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113583] = {
commodityId = 113583,
commodityName = "翡光仙灵庭",
limitNum = 1,
beginTime = {
seconds = 1743782400
},
endTime = {
seconds = 1746460799
},
jumpId = 598,
jumpText = "翡光仙灵",
minVersion = "1.3.78.72",
itemIds = {
218184
},
cumuRecvNumMax = 1
},
[113584] = {
commodityId = 113584,
commodityName = "蒸蒸日上小窝",
limitNum = 1,
beginTime = {
seconds = 1743782400
},
endTime = {
seconds = 1746460799
},
jumpId = 598,
jumpText = "翡光仙灵",
minVersion = "1.3.78.72",
itemIds = {
218176
},
cumuRecvNumMax = 1
},
[113585] = {
commodityId = 113585,
commodityName = "翡光仙灵礼包",
limitNum = 1,
beginTime = {
seconds = 1743782400
},
endTime = {
seconds = 1746460799
},
jumpId = 598,
jumpText = "翡光仙灵",
minVersion = "1.3.78.72",
itemIds = {
310745
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 4980,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113586] = {
commodityId = 113586,
commodityName = "煎饼果子超人",
limitNum = 1,
beginTime = {
seconds = 1744387200
},
endTime = {
seconds = 1746719999
},
jumpId = 643,
jumpText = "煎饼超人",
minVersion = "1.3.78.82",
itemIds = {
218177
},
cumuRecvNumMax = 1
},
[113587] = {
commodityId = 113587,
commodityName = "猪猪包礼盒",
limitNum = 1,
beginTime = {
seconds = 1744387200
},
endTime = {
seconds = 1746719999
},
jumpId = 643,
jumpText = "煎饼超人",
minVersion = "1.3.78.82",
itemIds = {
218179
},
cumuRecvNumMax = 1
},
[113588] = {
commodityId = 113588,
commodityName = "煎饼超人礼盒",
limitNum = 1,
beginTime = {
seconds = 1744387200
},
endTime = {
seconds = 1746719999
},
jumpId = 643,
jumpText = "煎饼超人",
minVersion = "1.3.78.82",
itemIds = {
310748
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3930,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113589] = {
commodityId = 113589,
commodityName = "蜜桃猫星星杯",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1749743999
},
jumpId = 659,
jumpText = "满杯蜜桃猫",
minVersion = "1.3.78.58",
itemIds = {
218185
},
cumuRecvNumMax = 1
},
[113590] = {
commodityId = 113590,
commodityName = "蜜桃猫星礼盒",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1749743999
},
jumpId = 659,
jumpText = "满杯蜜桃猫",
minVersion = "1.3.78.58",
itemIds = {
218186
},
cumuRecvNumMax = 1
},
[113591] = {
commodityId = 113591,
commodityName = "满杯蜜桃猫礼盒",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1749743999
},
jumpId = 659,
jumpText = "满杯蜜桃猫",
minVersion = "1.3.78.58",
itemIds = {
310750
},
canGift = true,
addIntimacy = 230,
giftCoinType = 1,
giftPrice = 2280,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113592] = {
commodityId = 113592,
commodityName = "蜜糖喵巡游站",
limitNum = 1,
beginTime = {
seconds = 1744128000
},
endTime = {
seconds = 1749139199
},
jumpId = 669,
jumpText = "甜梦嘉年华",
minVersion = "1.3.78.33",
itemIds = {
218168
},
cumuRecvNumMax = 1
},
[113593] = {
commodityId = 113593,
commodityName = "海盐甜筒果行",
limitNum = 1,
beginTime = {
seconds = 1744128000
},
endTime = {
seconds = 1749139199
},
jumpId = 669,
jumpText = "甜梦嘉年华",
minVersion = "1.3.78.33",
itemIds = {
218188
},
cumuRecvNumMax = 1
},
[113594] = {
commodityId = 113594,
commodityName = "樱桃蛋糕小铺",
limitNum = 1,
beginTime = {
seconds = 1744128000
},
endTime = {
seconds = 1749139199
},
jumpId = 669,
jumpText = "甜梦嘉年华",
minVersion = "1.3.78.33",
itemIds = {
218189
},
cumuRecvNumMax = 1
},
[113595] = {
commodityId = 113595,
commodityName = "海盗星船鱼铺",
limitNum = 1,
beginTime = {
seconds = 1744128000
},
endTime = {
seconds = 1749139199
},
jumpId = 669,
jumpText = "甜梦嘉年华",
minVersion = "1.3.78.33",
itemIds = {
218190
},
cumuRecvNumMax = 1
},
[113596] = {
commodityId = 113596,
commodityName = "甜梦嘉年华礼盒",
limitNum = 1,
beginTime = {
seconds = 1744128000
},
endTime = {
seconds = 1749139199
},
jumpId = 669,
jumpText = "甜梦嘉年华",
minVersion = "1.3.78.33",
itemIds = {
310752
},
canGift = true,
addIntimacy = 1450,
giftCoinType = 1,
giftPrice = 14600,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113597] = {
commodityId = 113597,
commodityName = "狐爷爷礼盒",
coinType = 1,
price = 1960,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1745856000
},
minVersion = "1.3.78.58",
itemIds = {
310739
},
canGift = true,
addIntimacy = 200,
giftCoinType = 1,
giftPrice = 1960,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113598] = {
commodityId = 113598,
commodityName = "奶油云朵乐园",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1742140800
},
jumpId = 599,
jumpText = "奶油乐园奇遇",
minVersion = "1.3.78.58",
itemIds = {
218169
},
cumuRecvNumMax = 1
},
[113599] = {
commodityId = 113599,
commodityName = "布丁狗小窝",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1742140800
},
jumpId = 599,
jumpText = "奶油乐园奇遇",
minVersion = "1.3.78.58",
itemIds = {
218193
},
cumuRecvNumMax = 1
},
[113600] = {
commodityId = 113600,
commodityName = "奶油乐园礼包",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1742140800
},
jumpId = 599,
jumpText = "奶油乐园奇遇",
minVersion = "1.3.78.58",
itemIds = {
310753
},
canGift = true,
addIntimacy = 740,
giftCoinType = 1,
giftPrice = 7380,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113601] = {
commodityId = 113601,
commodityName = "珍馐百味礼盒",
coinType = 1,
price = 14600,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
minVersion = "1.3.78.33",
itemIds = {
310737
},
canGift = true,
addIntimacy = 1450,
giftCoinType = 1,
giftPrice = 14600,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113602] = {
commodityId = 113602,
commodityName = "翡光仙灵礼包",
coinType = 1,
price = 4980,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1746460800
},
minVersion = "1.3.78.72",
itemIds = {
310745
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 4980,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113603] = {
commodityId = 113603,
commodityName = "煎饼超人礼盒",
coinType = 1,
price = 3930,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1746720000
},
minVersion = "1.3.78.82",
itemIds = {
310748
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3930,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113607] = {
commodityId = 113607,
commodityName = "沙洲旅人石屋",
coinType = 1,
price = 2120,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1748534400
},
jumpId = 690,
jumpText = "旅者驿站",
minVersion = "1.3.88.112",
itemIds = {
218202
},
canGift = true,
addIntimacy = 220,
giftCoinType = 1,
giftPrice = 2120,
buyChecker = v1,
cumuRecvNumMax = 1,
jumpBeginTime = {
seconds = 1748534400
},
jumpEndTime = {
seconds = 1750953599
}
},
[113604] = {
commodityId = 113604,
commodityName = "蜜糖彩虹礼盒",
coinType = 1,
price = 3980,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1748534400
},
minVersion = "1.3.78.96",
itemIds = {
310740
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3980,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113605] = {
commodityId = 113605,
commodityName = "甜梦嘉年华礼盒",
coinType = 1,
price = 14600,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1749139200
},
minVersion = "1.3.78.33",
itemIds = {
310752
},
canGift = true,
addIntimacy = 1450,
giftCoinType = 1,
giftPrice = 14600,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113606] = {
commodityId = 113606,
commodityName = "奶油乐园礼包",
coinType = 1,
price = 7380,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1749139200
},
minVersion = "1.3.78.58",
itemIds = {
310753
},
canGift = true,
addIntimacy = 740,
giftCoinType = 1,
giftPrice = 7380,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113608] = {
commodityId = 113608,
commodityName = "怒海鲨王号",
limitNum = 1,
beginTime = {
seconds = 1747065600
},
endTime = {
seconds = 1752767999
},
jumpId = 691,
jumpText = "怒海狂鲨",
minVersion = "1.3.78.72",
itemIds = {
218200
},
cumuRecvNumMax = 1
},
[113609] = {
commodityId = 113609,
commodityName = "云晶花语时钟",
limitNum = 1,
beginTime = {
seconds = 1747065600
},
endTime = {
seconds = 1752767999
},
jumpId = 691,
jumpText = "怒海狂鲨",
minVersion = "1.3.78.72",
itemIds = {
218206
},
cumuRecvNumMax = 1
},
[113610] = {
commodityId = 113610,
commodityName = "怒海狂鲨礼包",
coinType = 1,
price = 4980,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1747065600
},
jumpId = 691,
jumpText = "怒海狂鲨",
minVersion = "1.3.78.72",
itemIds = {
310756
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 4980,
buyChecker = v1,
cumuRecvNumMax = 1,
jumpBeginTime = {
seconds = 1747065600
},
jumpEndTime = {
seconds = 1752767999
}
},
[113611] = {
commodityId = 113611,
commodityName = "甜心琪琪",
limitNum = 1,
beginTime = {
seconds = 1747584000
},
endTime = {
seconds = 1753372799
},
jumpId = 900,
jumpText = "甜心琪琪",
minVersion = "1.3.78.72",
itemIds = {
218194
},
cumuRecvNumMax = 1
},
[113612] = {
commodityId = 113612,
commodityName = "甜甜杯礼盒",
limitNum = 1,
beginTime = {
seconds = 1747584000
},
endTime = {
seconds = 1753372799
},
jumpId = 900,
jumpText = "甜心琪琪",
minVersion = "1.3.78.72",
itemIds = {
218192
},
cumuRecvNumMax = 1
},
[113613] = {
commodityId = 113613,
commodityName = "甜心琪琪礼包",
coinType = 1,
price = 3930,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1747584000
},
jumpId = 900,
jumpText = "甜心琪琪",
minVersion = "1.3.78.72",
itemIds = {
310757
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3930,
buyChecker = v1,
cumuRecvNumMax = 1,
jumpBeginTime = {
seconds = 1747584000
},
jumpEndTime = {
seconds = 1753372799
}
},
[113614] = {
commodityId = 113614,
commodityName = "杏花酒家",
coinType = 1,
price = 1620,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1750089600
},
jumpId = 710,
jumpText = "拾味桃源",
minVersion = "1.3.88.153",
itemIds = {
218165
},
canGift = true,
addIntimacy = 170,
giftCoinType = 1,
giftPrice = 1620,
buyChecker = v1,
cumuRecvNumMax = 1,
jumpBeginTime = {
seconds = 1750089600
},
jumpEndTime = {
seconds = 1753372799
}
},
[113615] = {
commodityId = 113615,
commodityName = "招财喵礼包",
coinType = 1,
price = 3600,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1750348800
},
minVersion = "1.3.88.92",
itemIds = {
310707
},
canGift = true,
addIntimacy = 400,
giftCoinType = 1,
giftPrice = 3600,
buyChecker = v1,
cumuRecvNumMax = 1
},
[113616] = {
commodityId = 113616,
commodityName = "热浪岛屿",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1748448000
},
endTime = {
seconds = 1753372799
},
jumpId = 720,
jumpText = "炽光海岸",
minVersion = "1.3.88.1",
itemIds = {
218195
},
cumuRecvNumMax = 1
},
[113617] = {
commodityId = 113617,
commodityName = "石纹部落",
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1748448000
},
endTime = {
seconds = 1753372799
},
jumpId = 720,
jumpText = "炽光海岸",
minVersion = "1.3.88.1",
itemIds = {
218196
},
cumuRecvNumMax = 1
},
[113618] = {
commodityId = 113618,
commodityName = "炽光海岸礼包",
coinType = 1,
price = 4680,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1748448000
},
jumpId = 720,
jumpText = "炽光海岸",
minVersion = "1.3.88.1",
itemIds = {
310759
},
canGift = true,
addIntimacy = 470,
giftCoinType = 1,
giftPrice = 4680,
buyChecker = v1,
jumpBeginTime = {
seconds = 1748448000
},
jumpEndTime = {
seconds = 1753372799
}
},
[113619] = {
commodityId = 113619,
commodityName = "圣灵之庭",
limitNum = 1,
beginTime = {
seconds = 1748707200
},
endTime = {
seconds = 1753372799
},
jumpId = 722,
jumpText = "天穹圣域",
minVersion = "1.3.88.1",
itemIds = {
218201
},
cumuRecvNumMax = 1
},
[113620] = {
commodityId = 113620,
commodityName = "星露花台",
limitNum = 1,
beginTime = {
seconds = 1748707200
},
endTime = {
seconds = 1753372799
},
jumpId = 722,
jumpText = "天穹圣域",
minVersion = "1.3.88.1",
itemIds = {
218210
},
cumuRecvNumMax = 1
},
[113621] = {
commodityId = 113621,
commodityName = "天穹彩虹小店",
limitNum = 1,
beginTime = {
seconds = 1748707200
},
endTime = {
seconds = 1753372799
},
jumpId = 722,
jumpText = "天穹圣域",
minVersion = "1.3.88.1",
itemIds = {
218211
},
cumuRecvNumMax = 1
},
[113622] = {
commodityId = 113622,
commodityName = "灵泉圣亭",
limitNum = 1,
beginTime = {
seconds = 1748707200
},
endTime = {
seconds = 1753372799
},
jumpId = 722,
jumpText = "天穹圣域",
minVersion = "1.3.88.1",
itemIds = {
218212
},
cumuRecvNumMax = 1
},
[113623] = {
commodityId = 113623,
commodityName = "天穹圣域礼盒",
coinType = 1,
price = 14600,
limitType = v4,
limitNum = 1,
beginTime = {
seconds = 1748707200
},
jumpId = 722,
jumpText = "天穹圣域",
minVersion = "1.3.88.1",
itemIds = {
310760
},
canGift = true,
addIntimacy = 1450,
giftCoinType = 1,
giftPrice = 14600,
buyChecker = v1,
cumuRecvNumMax = 1,
jumpBeginTime = {
seconds = 1748707200
},
jumpEndTime = {
seconds = 1753372799
}
},
[113624] = {
commodityId = 113624,
commodityName = "小金毛",
limitNum = 1,
beginTime = {
seconds = 1747843200
},
endTime = {
seconds = 1751039999
},
jumpId = 1066,
jumpText = "特色礼包",
minVersion = "1.3.88.97",
itemIds = {
219205
},
buyChecker = v1,
cumuRecvNumMax = 1
},
[113625] = {
commodityId = 113625,
commodityName = "收获日",
limitNum = 1,
beginTime = {
seconds = 1747843200
},
endTime = {
seconds = 1751039999
},
jumpId = 1066,
jumpText = "特色礼包",
minVersion = "1.3.88.97",
itemIds = {
219300
},
buyChecker = v1,
cumuRecvNumMax = 1
},
[113626] = {
commodityId = 113626,
commodityName = "狸猫精灵",
limitNum = 1,
beginTime = {
seconds = 1751558400
},
endTime = {
seconds = 1753372799
},
jumpId = 1066,
jumpText = "特色礼包",
minVersion = "1.3.99.1",
itemIds = {
218113
},
buyChecker = v1,
cumuRecvNumMax = 1
}
}

local mt = {
mallId = 130,
shopTag = {
6,
27
},
gender = 0,
itemNums = {
1
},
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/C_场景礼包.xlsx: 场景礼包商品

local v0 = 2

local v1 = 60

local v2 = 120

local v3 = 0

local v4 = {
1
}

local v5 = {
60
}

local v6 = {
20
}

local v7 = {
condition = {
{
conditionType = 2
}
}
}

local data = {
[160001] = {
commodityId = 160001,
commodityName = "限时礼包1",
price = 900,
discountPrice = 300,
itemIds = {
2,
6
},
itemNums = {
30,
600
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = v4
}
}
}
}
},
showCondition = v7
},
[160002] = {
commodityId = 160002,
commodityName = "限时礼包2",
price = 60,
discountPrice = 30,
canDirectBuy = true,
itemIds = {
2,
1
},
itemNums = {
3,
30
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
2
}
}
}
}
}
},
showCondition = v7
},
[160003] = {
commodityId = 160003,
commodityName = "唐僧",
price = 400,
discountPrice = 60,
shopTag = {
2,
7
},
imgUrl = "Icon_Gift_Ts",
itemIds = {
400660
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
4
}
}
}
}
}
},
showCondition = v7
},
[160004] = {
commodityId = 160004,
commodityName = "孙悟空原价",
price = 400,
discountPrice = 60,
shopTag = {
2,
7
},
imgUrl = "Icon_Gift_Wk",
itemIds = {
400670
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
4
}
}
}
}
}
},
showCondition = v7
},
[160005] = {
commodityId = 160005,
commodityName = "唐僧",
price = 400,
shopTag = {
2,
7
},
imgUrl = "Icon_Gift_Ts",
itemIds = {
400660
},
buyCondition = {
condition = {
{
conditionType = 63,
value = 1,
subConditionList = {
{
type = 66,
value = {
4,
160003
}
}
}
}
}
},
showCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 59,
value = 1,
subConditionList = {
{
type = 66,
value = {
4,
160003
}
}
}
},
{
conditionType = 63,
value = 1,
subConditionList = {
{
type = 66,
value = {
4,
160003
}
}
}
}
}
}
},
[160007] = {
commodityId = 160007,
commodityName = "唐僧",
coinType = 200006,
price = 12,
itemIds = {
400661
}
},
[160006] = {
commodityId = 160006,
commodityName = "孙悟空",
price = 400,
shopTag = {
2,
7
},
imgUrl = "Icon_Gift_Wk",
itemIds = {
400670
},
buyCondition = {
condition = {
{
conditionType = 63,
value = 1,
subConditionList = {
{
type = 66,
value = {
4,
160004
}
}
}
}
}
},
showCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 59,
value = 1,
subConditionList = {
{
type = 66,
value = {
4,
160004
}
}
}
},
{
conditionType = 63,
value = 1,
subConditionList = {
{
type = 66,
value = {
4,
160004
}
}
}
}
}
}
},
[160008] = {
commodityId = 160008,
commodityName = "孙悟空",
coinType = 200006,
price = 12,
itemIds = {
400671
}
},
[160009] = {
commodityId = 160009,
commodityName = "星愿币限时礼包",
price = 600,
discountPrice = 300,
canDirectBuy = true,
itemIds = {
2
},
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
5
}
}
}
}
}
},
showCondition = v7
},
[160015] = {
commodityId = 160015,
commodityName = "小真",
price = 400,
discountPrice = 400,
itemIds = {
401610
},
buyCondition = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
401130,
401740
}
}
}
}
}
},
showCondition = v7
},
[160016] = {
commodityId = 160016,
commodityName = "悟小能",
price = 400,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
401750
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
6
}
}
}
}
}
},
showCondition = v7
},
[160017] = {
commodityId = 160017,
coinType = 2,
discountPrice = 0,
itemNums = {
10
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
9,
0
}
}
}
}
}
},
showCondition = v7
},
[160018] = {
commodityId = 160018,
price = 10,
discountPrice = 10,
canDirectBuy = true,
itemIds = v4,
itemNums = {
10
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
9,
1
}
}
}
}
}
},
showCondition = v7
},
[160019] = {
commodityId = 160019,
coinType = 2,
discountPrice = 0,
itemIds = {
860016,
860017
},
itemNums = {
1,
1
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
9,
2
}
}
}
}
}
},
showCondition = v7
},
[160020] = {
commodityId = 160020,
coinType = 2,
discountPrice = 0,
itemIds = {
710149,
710150
},
itemNums = {
1,
1
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
9,
3
}
}
}
}
}
},
showCondition = v7
},
[160021] = {
commodityId = 160021,
commodityName = "星愿币限时礼包",
price = 600,
discountPrice = 300,
canDirectBuy = true,
itemIds = {
209
},
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
10
}
}
}
}
}
},
showCondition = v7
},
[160022] = {
commodityId = 160022,
commodityName = "沙小净",
price = 400,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
401720
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
11
}
}
}
}
}
},
showCondition = v7
},
[160023] = {
commodityId = 160023,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
12,
0
}
}
}
}
}
},
showCondition = v7
},
[160024] = {
commodityId = 160024,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
12,
1
}
}
}
}
}
},
showCondition = v7
},
[160025] = {
commodityId = 160025,
coinType = 2,
discountPrice = 0,
itemIds = {
1005
},
itemNums = {
300
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
12,
2
}
}
}
}
}
},
showCondition = v7
},
[160026] = {
commodityId = 160026,
price = 400,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
640001
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
12,
3
}
}
}
}
}
},
showCondition = v7
},
[160028] = {
commodityId = 160028,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
14,
0
}
}
}
}
}
},
showCondition = v7
},
[160029] = {
commodityId = 160029,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
14,
1
}
}
}
}
}
},
showCondition = v7
},
[160030] = {
commodityId = 160030,
coinType = 2,
discountPrice = 0,
itemIds = {
720128
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
14,
2
}
}
}
}
}
},
showCondition = v7
},
[160031] = {
commodityId = 160031,
price = 400,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
640004
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
14,
3
}
}
}
}
}
},
showCondition = v7
},
[160032] = {
commodityId = 160032,
commodityName = "悟小能",
price = 400,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
401750
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
15
}
}
}
}
}
},
showCondition = v7
},
[160033] = {
commodityId = 160033,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
16,
0
}
}
}
}
}
},
showCondition = v7
},
[160034] = {
commodityId = 160034,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
16,
1
}
}
}
}
}
},
showCondition = v7
},
[160035] = {
commodityId = 160035,
coinType = 2,
discountPrice = 0,
itemIds = {
720153
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
16,
2
}
}
}
}
}
},
showCondition = v7
},
[160036] = {
commodityId = 160036,
price = 400,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
640004
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
16,
3
}
}
}
}
}
},
showCondition = v7
},
[160037] = {
commodityId = 160037,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
17,
0
}
}
}
}
}
},
showCondition = v7
},
[160038] = {
commodityId = 160038,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
17,
1
}
}
}
}
}
},
showCondition = v7
},
[160039] = {
commodityId = 160039,
coinType = 2,
discountPrice = 0,
itemIds = {
710250
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
17,
2
}
}
}
}
}
},
showCondition = v7
},
[160040] = {
commodityId = 160040,
price = 400,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
640005
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
17,
3
}
}
}
}
}
},
showCondition = v7
},
[160041] = {
commodityId = 160041,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
18,
0
}
}
}
}
}
},
showCondition = v7
},
[160042] = {
commodityId = 160042,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
18,
1
}
}
}
}
}
},
showCondition = v7
},
[160043] = {
commodityId = 160043,
coinType = 2,
discountPrice = 0,
itemIds = {
610155
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
18,
2
}
}
}
}
}
},
showCondition = v7
},
[160044] = {
commodityId = 160044,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
620193
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
18,
3
}
}
}
}
}
},
showCondition = v7
},
[160045] = {
commodityId = 160045,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
19,
0
}
}
}
}
}
},
showCondition = v7
},
[160046] = {
commodityId = 160046,
price = 10,
discountPrice = 10,
canDirectBuy = true,
itemIds = {
320106
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
19,
1
}
}
}
}
}
},
showCondition = v7
},
[160047] = {
commodityId = 160047,
price = 30,
discountPrice = 30,
canDirectBuy = true,
itemIds = {
320107
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
19,
2
}
}
}
}
}
},
showCondition = v7
},
[160048] = {
commodityId = 160048,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
320108
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
19,
3
}
}
}
}
}
},
showCondition = v7
},
[160049] = {
commodityId = 160049,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
20,
0
}
}
}
}
}
},
showCondition = v7
},
[160050] = {
commodityId = 160050,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
20,
1
}
}
}
}
}
},
showCondition = v7
},
[160051] = {
commodityId = 160051,
coinType = 2,
discountPrice = 0,
itemIds = {
610198
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
20,
2
}
}
}
}
}
},
showCondition = v7
},
[160052] = {
commodityId = 160052,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
620409
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
20,
3
}
}
}
}
}
},
showCondition = v7
},
[160053] = {
commodityId = 160053,
commodityName = "峡谷英雄孙尚香",
price = 300,
discountPrice = 60,
itemIds = {
301114
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
21
}
}
}
}
}
},
showCondition = v7
},
[160054] = {
commodityId = 160054,
commodityName = "幸运币礼包",
price = 90,
discountPrice = 60,
limitNum = 5,
canDirectBuy = true,
itemIds = {
3
},
itemNums = {
9
}
},
[160055] = {
commodityId = 160055,
commodityName = "幸运币礼包",
price = 150,
discountPrice = 120,
limitNum = 5,
canDirectBuy = true,
itemIds = {
3
},
itemNums = {
15
}
},
[160056] = {
commodityId = 160056,
commodityName = "幸运币礼包",
price = 780,
discountPrice = 680,
limitNum = 5,
canDirectBuy = true,
itemIds = {
3
},
itemNums = {
78
}
},
[160057] = {
commodityId = 160057,
commodityName = "星愿币礼包",
price = 90,
discountPrice = 60,
limitNum = 5,
canDirectBuy = true,
itemIds = {
2
},
itemNums = {
9
}
},
[160058] = {
commodityId = 160058,
commodityName = "星愿币礼包",
price = 150,
discountPrice = 120,
limitNum = 5,
canDirectBuy = true,
itemIds = {
2
},
itemNums = {
15
}
},
[160059] = {
commodityId = 160059,
commodityName = "星愿币礼包",
price = 780,
discountPrice = 680,
limitNum = 5,
canDirectBuy = true,
itemIds = {
2
},
itemNums = {
78
}
},
[160060] = {
commodityId = 160060,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
28,
0
}
}
}
}
}
},
showCondition = v7
},
[160061] = {
commodityId = 160061,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
403240
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
28,
1
}
}
}
}
}
},
showCondition = v7
},
[160062] = {
commodityId = 160062,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
640036
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
28,
2
}
}
}
}
}
},
showCondition = v7
},
[160063] = {
commodityId = 160063,
coinType = 2,
discountPrice = 0,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
28,
3
}
}
}
}
}
},
showCondition = v7
},
[160064] = {
commodityId = 160064,
commodityName = "峡谷英雄李白",
price = 300,
discountPrice = 180,
itemIds = {
301119
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
29
}
}
}
},
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1019
}
}
}
}
}
},
showCondition = v7
},
[160065] = {
commodityId = 160065,
commodityName = "限时礼包",
price = 420,
discountPrice = 180,
canDirectBuy = true,
itemIds = {
1,
630240,
640032
},
itemNums = {
60,
1,
1
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
30
}
}
}
}
}
},
showCondition = v7
},
[160066] = {
commodityId = 160066,
commodityName = "峡谷英雄钟馗",
price = 150,
discountPrice = 60,
itemIds = {
301115
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
31
}
}
}
},
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1015
}
}
}
}
}
},
showCondition = v7
},
[160067] = {
commodityId = 160067,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
32,
0
}
}
}
}
}
},
showCondition = v7
},
[160068] = {
commodityId = 160068,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
32,
1
}
}
}
}
}
},
showCondition = v7
},
[160069] = {
commodityId = 160069,
coinType = 2,
discountPrice = 0,
itemIds = {
630300
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
32,
2
}
}
}
}
}
},
showCondition = v7
},
[160070] = {
commodityId = 160070,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
403370
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
32,
3
}
}
}
}
}
},
showCondition = v7
},
[160074] = {
commodityId = 160074,
commodityName = "龟蜜礼包",
price = 900,
discountPrice = 300,
canDirectBuy = true,
itemIds = {
400930
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
36
}
}
}
}
}
},
showCondition = v7
},
[160075] = {
commodityId = 160075,
commodityName = "限时礼包",
price = 360,
discountPrice = 180,
canDirectBuy = true,
itemIds = {
610210,
3
},
itemNums = {
1,
6
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
37
}
}
}
}
}
},
showCondition = v7
},
[160076] = {
commodityId = 160076,
commodityName = "峡谷英雄云缨",
price = 300,
discountPrice = 180,
itemIds = {
301117
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
31
}
}
}
},
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1017
}
}
}
}
}
},
showCondition = v7
},
[160081] = {
commodityId = 160081,
commodityName = "桃子背包",
price = 10,
canDirectBuy = true,
itemIds = {
620312
}
},
[160082] = {
commodityId = 160082,
commodityName = "沙滩卡车",
price = 10,
canDirectBuy = true,
itemIds = {
620338
}
},
[160083] = {
commodityId = 160083,
commodityName = "阳光柠檬",
price = 10,
canDirectBuy = true,
itemIds = {
620226
}
},
[160084] = {
commodityId = 160084,
commodityName = "汤圆宝宝",
price = 60,
canDirectBuy = true,
itemIds = {
630055,
2,
6
},
itemNums = {
1,
1,
10
}
},
[160085] = {
commodityId = 160085,
commodityName = "中国结",
price = 60,
canDirectBuy = true,
itemIds = {
1,
620110
},
itemNums = {
60,
1
}
},
[160086] = {
commodityId = 160086,
commodityName = "蕉绿绿",
price = 120,
canDirectBuy = true,
itemIds = {
400910,
2,
6
},
itemNums = {
1,
3,
30
}
},
[160087] = {
commodityId = 160087,
commodityName = "悟小能",
price = 120,
canDirectBuy = true,
itemIds = {
401750
}
},
[160088] = {
commodityId = 160088,
commodityName = "马赛克眼镜",
price = 60,
canDirectBuy = true,
itemIds = {
1,
610077
},
itemNums = {
60,
1
}
},
[160089] = {
commodityId = 160089,
commodityName = "极速飞车",
price = 600,
canDirectBuy = true,
itemIds = {
222011
}
},
[160090] = {
commodityId = 160090,
commodityName = "绿植花洒",
price = 60,
canDirectBuy = true,
itemIds = {
640002
}
},
[160091] = {
commodityId = 160091,
commodityName = "蔬菜筐筐",
price = 60,
canDirectBuy = true,
itemIds = {
620276
}
},
[160092] = {
commodityId = 160092,
commodityName = "鸭梨宝背",
price = 120,
canDirectBuy = true,
itemIds = {
620220,
2,
6
},
itemNums = {
1,
3,
30
}
},
[160093] = {
commodityId = 160093,
commodityName = "峡谷英雄王昭君",
price = 300,
discountPrice = 180,
itemIds = {
301118
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
51
}
}
}
},
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1018
}
}
}
}
}
},
showCondition = v7
},
[160094] = {
commodityId = 160094,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
52,
0
}
}
}
}
}
},
showCondition = v7
},
[160095] = {
commodityId = 160095,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
52,
1
}
}
}
}
}
},
showCondition = v7
},
[160096] = {
commodityId = 160096,
coinType = 2,
discountPrice = 0,
itemIds = {
610243
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
52,
2
}
}
}
}
}
},
showCondition = v7
},
[160097] = {
commodityId = 160097,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
620410
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
52,
3
}
}
}
}
}
},
showCondition = v7
},
[160098] = {
commodityId = 160098,
commodityName = "限时礼包",
price = 420,
discountPrice = 180,
canDirectBuy = true,
itemIds = {
403930,
640038
},
itemNums = {
1,
1
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
56
}
}
}
}
}
},
showCondition = v7
},
[160099] = {
commodityId = 160099,
commodityName = "魔力限时礼包",
price = 1000,
discountPrice = 240,
canDirectBuy = true,
itemIds = {
403700,
630384,
1,
840190
},
itemNums = {
1,
1,
60,
1
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
57
}
}
}
}
}
},
showCondition = v7
},
[160100] = {
commodityId = 160100,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
59,
0
}
}
}
}
}
},
showCondition = v7
},
[160101] = {
commodityId = 160101,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
59,
1
}
}
}
}
}
},
showCondition = v7
},
[160102] = {
commodityId = 160102,
coinType = 2,
discountPrice = 0,
itemIds = {
610217
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
59,
2
}
}
}
}
}
},
showCondition = v7
},
[160103] = {
commodityId = 160103,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
620440
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
59,
3
}
}
}
}
}
},
showCondition = v7
},
[160104] = {
commodityId = 160104,
commodityName = "体验卡",
price = 10,
discountPrice = 10,
canDirectBuy = true,
itemIds = {
200104
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
56
}
}
}
}
}
},
showCondition = v7
},
[160105] = {
commodityId = 160105,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
61,
0
}
}
}
}
}
},
showCondition = v7
},
[160106] = {
commodityId = 160106,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
61,
1
}
}
}
}
}
},
showCondition = v7
},
[160107] = {
commodityId = 160107,
coinType = 2,
discountPrice = 0,
itemIds = {
630389
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
61,
2
}
}
}
}
}
},
showCondition = v7
},
[160108] = {
commodityId = 160108,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
640057
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
61,
3
}
}
}
}
}
},
showCondition = v7
},
[160109] = {
commodityId = 160109,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
62,
0
}
}
}
}
}
},
showCondition = v7
},
[160110] = {
commodityId = 160110,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
62,
1
}
}
}
}
}
},
showCondition = v7
},
[160111] = {
commodityId = 160111,
coinType = 2,
discountPrice = 0,
itemIds = {
610155
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
62,
2
}
}
}
}
}
},
showCondition = v7
},
[160112] = {
commodityId = 160112,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
620193
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
62,
3
}
}
}
}
}
},
showCondition = v7
},
[160113] = {
commodityId = 160113,
commodityName = "限时礼包",
price = 420,
discountPrice = 180,
canDirectBuy = true,
itemIds = {
1,
630240,
640032
},
itemNums = {
60,
1,
1
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
63
}
}
}
}
}
},
showCondition = v7
},
[160114] = {
commodityId = 160114,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
64,
0
}
}
}
}
}
},
showCondition = v7
},
[160115] = {
commodityId = 160115,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
64,
1
}
}
}
}
}
},
showCondition = v7
},
[160116] = {
commodityId = 160116,
coinType = 2,
discountPrice = 0,
itemIds = {
630426
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
64,
2
}
}
}
}
}
},
showCondition = v7
},
[160117] = {
commodityId = 160117,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
640090
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
64,
3
}
}
}
}
}
},
showCondition = v7
},
[160118] = {
commodityId = 160118,
commodityName = "限时礼包",
price = 420,
discountPrice = 180,
canDirectBuy = true,
itemIds = {
1,
630433,
640065
},
itemNums = {
60,
1,
1
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
75
}
}
}
}
}
},
showCondition = v7
},
[160119] = {
commodityId = 160119,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
76,
0
}
}
}
}
}
},
showCondition = v7
},
[160120] = {
commodityId = 160120,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
620699
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
76,
1
}
}
}
}
}
},
showCondition = v7
},
[160121] = {
commodityId = 160121,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
630364
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
76,
2
}
}
}
}
}
},
showCondition = v7
},
[160122] = {
commodityId = 160122,
coinType = 2,
discountPrice = 0,
itemIds = {
2
},
itemNums = {
6
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
76,
3
}
}
}
}
}
},
showCondition = v7
},
[160127] = {
commodityId = 160127,
commodityName = "湮灭之锁",
price = 180,
itemIds = {
640052
},
showCondition = v7
},
[160128] = {
commodityId = 160128,
commodityName = "阳光柠檬",
price = 10,
itemIds = {
620226
},
showCondition = v7
},
[160129] = {
commodityId = 160129,
commodityName = "沙滩卡车",
price = 10,
itemIds = {
620338
},
showCondition = v7
},
[160130] = {
commodityId = 160130,
commodityName = "绿植花洒",
price = 60,
itemIds = {
640002
},
showCondition = v7
},
[160131] = {
commodityId = 160131,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
82,
0
}
}
}
}
}
},
showCondition = v7
},
[160132] = {
commodityId = 160132,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
630467
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
82,
1
}
}
}
}
}
},
showCondition = v7
},
[160133] = {
commodityId = 160133,
coinType = 2,
discountPrice = 0,
itemIds = {
200008
},
itemNums = {
10
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
82,
2
}
}
}
}
}
},
showCondition = v7
},
[160134] = {
commodityId = 160134,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
640135
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
82,
3
}
}
}
}
}
},
showCondition = v7
},
[160135] = {
commodityId = 160135,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
83,
0
}
}
}
}
}
},
showCondition = v7
},
[160136] = {
commodityId = 160136,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
630368
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
83,
1
}
}
}
}
}
},
showCondition = v7
},
[160137] = {
commodityId = 160137,
coinType = 2,
discountPrice = 0,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
83,
2
}
}
}
}
}
},
showCondition = v7
},
[160138] = {
commodityId = 160138,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
630364
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
83,
3
}
}
}
}
}
},
showCondition = v7
},
[160139] = {
commodityId = 160139,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
85,
0
}
}
}
}
}
},
showCondition = v7
},
[160140] = {
commodityId = 160140,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
640036
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
85,
1
}
}
}
}
}
},
showCondition = v7
},
[160141] = {
commodityId = 160141,
coinType = 2,
discountPrice = 0,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
85,
2
}
}
}
}
}
},
showCondition = v7
},
[160142] = {
commodityId = 160142,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
403240
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
85,
3
}
}
}
}
}
},
showCondition = v7
},
[160143] = {
commodityId = 160143,
commodityName = "限时礼包",
price = 80,
discountPrice = 80,
canDirectBuy = true,
itemIds = {
720845,
640101
},
itemNums = {
1,
1
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
86
}
}
}
}
}
},
showCondition = v7
},
[160145] = {
commodityId = 160145,
commodityName = "毛绒老鼠",
price = 600,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
620594
},
showCondition = v7
},
[160146] = {
commodityId = 160146,
commodityName = "纸盒小怪兽",
price = 600,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
404160
},
showCondition = v7
},
[160147] = {
commodityId = 160147,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
89,
0
}
}
}
}
}
},
showCondition = v7
},
[160148] = {
commodityId = 160148,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
630368
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
89,
1
}
}
}
}
}
},
showCondition = v7
},
[160149] = {
commodityId = 160149,
coinType = 2,
discountPrice = 0,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
89,
2
}
}
}
}
}
},
showCondition = v7
},
[160150] = {
commodityId = 160150,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
630364
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
89,
3
}
}
}
}
}
},
showCondition = v7
},
[160151] = {
commodityId = 160151,
commodityName = "星钻宝箱",
price = 10,
discountPrice = 10,
canDirectBuy = true,
itemIds = {
320201
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
90
}
}
}
}
}
},
showCondition = v7
},
[160152] = {
commodityId = 160152,
commodityName = "星钻宝箱",
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
320202
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
90
}
}
}
}
}
},
showCondition = v7
},
[160153] = {
commodityId = 160153,
commodityName = "三选一礼包",
price = 400,
discountPrice = 60,
beginTime = {
seconds = 1747497600
},
endTime = {
seconds = 1750780799
},
itemIds = {
410540
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
91
}
}
}
},
{
conditionType = 5,
value = 5
}
}
},
showCondition = v7
},
[160154] = {
commodityId = 160154,
commodityName = "三选一礼包",
price = 400,
discountPrice = 60,
beginTime = {
seconds = 1747497600
},
endTime = {
seconds = 1750780799
},
itemIds = {
410550
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
91
}
}
}
},
{
conditionType = 5,
value = 5
}
}
},
showCondition = v7
},
[160155] = {
commodityId = 160155,
commodityName = "三选一礼包",
price = 400,
discountPrice = 60,
beginTime = {
seconds = 1747497600
},
endTime = {
seconds = 1750780799
},
itemIds = {
410810
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
91
}
}
}
},
{
conditionType = 5,
value = 5
}
}
},
showCondition = v7
},
[160156] = {
commodityId = 160156,
commodityName = "超值体验礼包",
price = 40,
discountPrice = 10,
canDirectBuy = true,
itemIds = {
200201,
1
},
itemNums = {
1,
1
},
buyCondition = {
condition = {
{
conditionType = 63,
value = 1,
subConditionList = {
{
type = 66,
value = {
84,
120216
}
}
}
}
}
},
showCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 59,
value = 1,
subConditionList = {
{
type = 66,
value = {
84,
120216
}
}
}
},
{
conditionType = 63,
value = 1,
subConditionList = {
{
type = 66,
value = {
84,
120216
}
}
}
}
}
}
},
[160157] = {
commodityId = 160157,
commodityName = "限时礼包",
price = 180,
discountPrice = 180,
canDirectBuy = true,
itemIds = {
403930,
640038
},
itemNums = {
1,
1
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
93
}
}
}
}
}
},
showCondition = v7
},
[160158] = {
commodityId = 160158,
commodityName = "限时礼包",
price = 420,
discountPrice = 180,
canDirectBuy = true,
itemIds = {
2,
630596,
620842
},
itemNums = {
6,
1,
1
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
94
}
}
}
}
}
},
showCondition = v7
},
[160159] = {
commodityId = 160159,
commodityName = "限时礼包",
price = 420,
discountPrice = 180,
canDirectBuy = true,
itemIds = {
1,
610389,
630559
},
itemNums = {
60,
1,
1
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
95
}
}
}
}
}
},
showCondition = v7
},
[160160] = {
commodityId = 160160,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
96,
0
}
}
}
}
}
},
showCondition = v7
},
[160161] = {
commodityId = 160161,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
96,
1
}
}
}
}
}
},
showCondition = v7
},
[160162] = {
commodityId = 160162,
coinType = 2,
discountPrice = 0,
itemIds = {
620896
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
96,
2
}
}
}
}
}
},
showCondition = v7
},
[160163] = {
commodityId = 160163,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
640155
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
96,
3
}
}
}
}
}
},
showCondition = v7
},
[160164] = {
commodityId = 160164,
commodityName = "星钻宝箱",
price = 10,
discountPrice = 10,
canDirectBuy = true,
itemIds = {
320201
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
92
}
}
}
}
}
},
showCondition = v7
},
[160165] = {
commodityId = 160165,
commodityName = "星钻宝箱",
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
320202
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
92
}
}
}
}
}
},
showCondition = v7
},
[160166] = {
commodityId = 160166,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
97,
0
}
}
}
}
}
},
showCondition = v7
},
[160167] = {
commodityId = 160167,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
620886
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
97,
1
}
}
}
}
}
},
showCondition = v7
},
[160168] = {
commodityId = 160168,
coinType = 2,
discountPrice = 0,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
97,
2
}
}
}
}
}
},
showCondition = v7
},
[160169] = {
commodityId = 160169,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
640039
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
97,
3
}
}
}
}
}
},
showCondition = v7
},
[160170] = {
commodityId = 160170,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
102,
0
}
}
}
}
}
},
showCondition = v7
},
[160171] = {
commodityId = 160171,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = {
610400
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
102,
1
}
}
}
}
}
},
showCondition = v7
},
[160172] = {
commodityId = 160172,
coinType = 2,
discountPrice = 0,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
102,
2
}
}
}
}
}
},
showCondition = v7
},
[160173] = {
commodityId = 160173,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
640166
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
102,
3
}
}
}
}
}
},
showCondition = v7
},
[160174] = {
commodityId = 160174,
commodityName = "限时礼包",
price = 360,
discountPrice = 180,
canDirectBuy = true,
itemIds = {
610210,
3
},
itemNums = {
1,
6
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
103
}
}
}
}
}
},
showCondition = v7
},
[160175] = {
commodityId = 160175,
coinType = 2,
discountPrice = 0,
itemNums = v6,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
105,
0
}
}
}
}
}
},
showCondition = v7
},
[160176] = {
commodityId = 160176,
price = 60,
discountPrice = 60,
canDirectBuy = true,
itemIds = v4,
itemNums = v5,
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
105,
1
}
}
}
}
}
},
showCondition = v7
},
[160177] = {
commodityId = 160177,
coinType = 2,
discountPrice = 0,
itemIds = {
1005
},
itemNums = {
300
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
105,
2
}
}
}
}
}
},
showCondition = v7
},
[160178] = {
commodityId = 160178,
price = 120,
discountPrice = 120,
canDirectBuy = true,
itemIds = {
640001
},
buyCondition = {
condition = {
{
conditionType = 144,
value = 1,
subConditionList = {
{
type = 66,
value = {
105,
3
}
}
}
}
}
},
showCondition = v7
},
[160179] = {
commodityId = 160179,
commodityName = "限时礼包",
price = 360,
discountPrice = 180,
canDirectBuy = true,
itemIds = {
1,
610366,
630548
},
itemNums = {
60,
1,
1
},
buyCondition = {
condition = {
{
conditionType = 60,
value = 1,
subConditionList = {
{
type = 66,
value = {
106
}
}
}
}
}
},
showCondition = v7
}
}

local mt = {
mallId = 31,
commodityName = "逐级解锁礼包",
coinType = 1,
price = 0,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
canDirectBuy = false,
itemIds = {
6
},
itemNums = v4
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
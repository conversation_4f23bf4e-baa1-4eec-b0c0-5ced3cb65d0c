--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 上装

local data = {
[510393] = {
id = 510393,
effect = true,
type = "ItemType_UpperGarment",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 4,
name = "半糖青春上装",
desc = "爱吃甜品？完全没问题！",
icon = "CDN:Icon_TY_Upper_420",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_TY_Upper_420",
upperType = 1
},
beginTime = {
seconds = 4101552000
},
suitId = 933,
suitName = "半糖青春",
suitIcon = "CDN:Icon_TY_420"
},
[510394] = {
id = 510394,
effect = true,
type = "ItemType_UpperGarment",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 4,
name = "快快向左上装",
desc = "情感的秘密，藏于此处",
icon = "CDN:Icon_TY_Upper_421",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_TY_Upper_421",
upperType = 1
},
beginTime = {
seconds = 4101552000
},
suitId = 935,
suitName = "快快向左",
suitIcon = "CDN:Icon_TY_421"
},
[510395] = {
id = 510395,
effect = true,
type = "ItemType_UpperGarment",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 4,
name = "快快向右上装",
desc = "怏怏不乐？快来快乐！",
icon = "CDN:Icon_TY_Upper_422",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_TY_Upper_422",
upperType = 1
},
beginTime = {
seconds = 4101552000
},
suitId = 937,
suitName = "快快向右",
suitIcon = "CDN:Icon_TY_422"
},
[510396] = {
id = 510396,
effect = true,
type = "ItemType_UpperGarment",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 4,
name = "乐在中央上装",
desc = "人生必须品，不乐可不行！",
icon = "CDN:Icon_TY_Upper_423",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_TY_Upper_423",
upperType = 1
},
beginTime = {
seconds = 4101552000
},
suitId = 939,
suitName = "乐在中央",
suitIcon = "CDN:Icon_TY_423"
},
[510397] = {
id = 510397,
effect = true,
type = "ItemType_UpperGarment",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 4,
name = "酷鲨航海家上装",
desc = "海中霸主，舍我其谁！",
icon = "CDN:Icon_TY_Upper_416",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_TY_Upper_416",
upperType = 1
},
beginTime = {
seconds = 4101552000
},
suitId = 941,
suitName = "酷鲨航海家",
suitIcon = "CDN:Icon_TY_416"
},
[510398] = {
id = 510398,
effect = true,
type = "ItemType_UpperGarment",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 4,
name = "萌鲨旅行家上装",
desc = "鲨鲨也能很温柔",
icon = "CDN:Icon_TY_Upper_417",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_TY_Upper_417",
upperType = 1
},
beginTime = {
seconds = 4101552000
},
suitId = 943,
suitName = "萌鲨旅行家",
suitIcon = "CDN:Icon_TY_417"
},
[510399] = {
id = 510399,
effect = true,
type = "ItemType_UpperGarment",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 4,
name = "星梦飞扬上装",
desc = "大声欢笑，快乐永远最重要",
icon = "CDN:Icon_TY_Upper_408",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_TY_Upper_408",
upperType = 1
},
beginTime = {
seconds = 4101552000
},
suitId = 945,
suitName = "星梦飞扬",
suitIcon = "CDN:Icon_TY_408"
},
[510400] = {
id = 510400,
effect = true,
type = "ItemType_UpperGarment",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 4,
name = "紫雾蝶语上装",
desc = "左翼托着晨露，右翼载着晚霞",
icon = "CDN:Icon_TY_Upper_413",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_TY_Upper_413",
upperType = 1
},
beginTime = {
seconds = 4101552000
},
suitId = 947,
suitName = "紫雾蝶语",
suitIcon = "CDN:Icon_TY_413"
},
[510401] = {
id = 510401,
effect = true,
type = "ItemType_UpperGarment",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 4,
name = "桃屿晴波",
desc = "来自【星衣妙想】创作者小糕的创意！涨潮时，浪花偷走了她裙摆的桃色",
icon = "CDN:Icon_TY_Suit_433",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_TY_Upper_433",
bottom = "SK_TY_Under_433",
upperType = 2
},
beginTime = {
seconds = 4101552000
},
suitId = 949,
suitName = "桃屿晴波",
suitIcon = "CDN:Icon_TY_433"
},
[510402] = {
id = 510402,
effect = true,
type = "ItemType_UpperGarment",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 4,
name = "雨衣",
desc = "宽松自如~",
icon = "CDN:Icon_TY_Suit_231",
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_TY_Upper_231",
bottom = "SK_TY_Under_231",
upperType = 2
},
beginTime = {
seconds = 4101552000
},
suitId = 951,
suitName = "雨衣",
suitIcon = "CDN:Icon_TY_231"
}
}

local mt = {
effect = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
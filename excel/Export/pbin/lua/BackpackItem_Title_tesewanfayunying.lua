--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_称号_特色玩法运营.xlsx: 称号

local data = {
[850485] = {
id = 850485,
quality = 3,
name = "农场星朋友",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_002",
picture = "CDN:T_Designation_Img_002"
},
[861000] = {
id = 861000,
quality = 3,
name = "最佳狼搭子",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_147",
picture = "CDN:T_Designation_Img_147"
},
[861001] = {
id = 861001,
quality = 3,
name = "最损狼队友",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_148",
picture = "CDN:T_Designation_Img_148"
},
[861005] = {
id = 861005,
name = "丰收峡谷星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_128",
picture = "CDN:T_Designation_Img_128"
},
[861003] = {
id = 861003,
name = "农场小能手",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003"
},
[861006] = {
id = 861006,
name = "梦幻粉熊星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_128",
picture = "CDN:T_Designation_Img_003"
},
[861007] = {
id = 861007,
quality = 1,
name = "峡谷精锐",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_128",
picture = "CDN:T_Designation_Img_128"
},
[861009] = {
id = 861009,
name = "乘风破浪",
desc = "扬帆起航祈愿活动获得",
icon = "CDN:Icon_Designation_Img_161",
picture = "CDN:T_Designation_Img_161",
showInView = 1
},
[861010] = {
id = 861010,
name = "缤纷绘卷",
desc = "扬帆起航祈愿活动获得",
icon = "CDN:Icon_Designation_Img_162",
picture = "CDN:T_Designation_Img_162",
showInView = 1
},
[861012] = {
id = 861012,
name = "冰雪峡谷星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_128",
picture = "CDN:T_Designation_Img_128"
},
[861011] = {
id = 861011,
name = "天天大丰收",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003"
},
[861013] = {
id = 861013,
name = "偷菜也偷心",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003"
},
[861014] = {
id = 861014,
name = "荣耀峡谷星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_128",
picture = "CDN:T_Designation_Img_128"
},
[861015] = {
id = 861015,
name = "头号侦探",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_166",
picture = "CDN:T_Designation_Img_166"
},
[861016] = {
id = 861016,
name = "蛇年行大运",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_189",
picture = "CDN:T_Designation_Img_189"
},
[861017] = {
id = 861017,
name = "好运上上签",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_190",
picture = "CDN:T_Designation_Img_190"
},
[861018] = {
id = 861018,
name = "瑞雪峡谷星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_128",
picture = "CDN:T_Designation_Img_128"
},
[861019] = {
id = 861019,
name = "带月荷锄归",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_203",
picture = "CDN:T_Designation_Img_203"
},
[861020] = {
id = 861020,
name = "花朝峡谷星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_128",
picture = "CDN:T_Designation_Img_128"
},
[861021] = {
id = 861021,
name = "彩虹猫猫条",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_212",
picture = "CDN:T_Designation_Img_212"
},
[861022] = {
id = 861022,
name = "S12峡谷星",
desc = "S12赛季峡谷玩法段位奖励",
icon = "CDN:Icon_Designation_Img_216",
picture = "CDN:T_Designation_Img_216"
},
[861023] = {
id = 861023,
name = "天选之星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_225",
picture = "CDN:T_Designation_Img_225"
},
[861024] = {
id = 861024,
name = "金牌幸运星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_226",
picture = "CDN:T_Designation_Img_226"
},
[861025] = {
id = 861025,
name = "S13峡谷星",
desc = "S13赛季峡谷玩法段位奖励",
icon = "CDN:Icon_Designation_Img_216",
picture = "CDN:T_Designation_Img_216"
},
[861026] = {
id = 861026,
quality = 3,
name = "航海计划",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_002",
picture = "CDN:T_Designation_Img_002"
}
}

local mt = {
type = "ItemType_Title",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 4,
itemNum = 10
}
},
quality = 2,
showInView = 0,
isDynamic = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
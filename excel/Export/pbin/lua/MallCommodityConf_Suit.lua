--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-套装

local v0 = {
seconds = 1676390400
}

local v1 = {
seconds = 1702569600
}

local v2 = {
seconds = 4074768000
}

local v3 = {
seconds = 1730563200
}

local v4 = {
2,
7
}

local v5 = 2

local v6 = 1

local v7 = 9

local v8 = "赛季祈愿"

local v9 = "峡谷幻梦"

local v10 = 0

local v11 = {
seconds = 1676476800
}

local v12 = {
seconds = 4074854400
}

local v13 = 6

local v14 = 200006

local v15 = 12

local v16 = 20

local v17 = 80

local v18 = 1000000

local v19 = 160

local v20 = 211

local v21 = 218

local v22 = 2240

local v23 = 1260

local v24 = 520

local v25 = "1.2.100.1"

local v26 = "1.2.67.1"

local v27 = "1.3.7.31"

local v28 = "1.3.12.1"

local v29 = "1.3.12.118"

local v30 = "1.3.68.100"

local data = {
[10001] = {
commodityId = 10001,
commodityName = "易斑斑",
beginTime = v0,
shopTag = v4,
shopSort = 2,
jumpId = 1808,
jumpText = "个人成就",
gender = 0,
itemIds = {
400020
},
bOpenSuit = true,
suitId = 20
},
[10002] = {
commodityId = 10002,
commodityName = "超新猩",
beginTime = v0,
endTime = v11,
shopTag = v4,
shopSort = 2,
jumpId = 41,
jumpText = "开局有好礼",
gender = 0,
itemIds = {
400030
}
},
[10003] = {
commodityId = 10003,
commodityName = "河小呆",
beginTime = v0,
endTime = v11,
shopTag = v4,
shopSort = 6,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
400040
}
},
[10004] = {
commodityId = 10004,
commodityName = "勇敢牛牛",
beginTime = v0,
shopTag = v4,
shopSort = 2,
jumpId = 1808,
jumpText = "个人成就",
gender = 0,
itemIds = {
400070
},
bOpenSuit = true,
suitId = 21
},
[10005] = {
commodityId = 10005,
commodityName = "虎子哥",
beginTime = v0,
shopTag = v4,
shopSort = 2,
jumpId = 1808,
jumpText = "个人成就",
gender = 0,
itemIds = {
400080
},
bOpenSuit = true,
suitId = 22
},
[10006] = {
commodityId = 10006,
commodityName = "哈士奇",
coinType = 6,
price = 100,
beginTime = v0,
shopTag = v4,
gender = 0,
itemIds = {
400100
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
400100,
1,
0
}
}
}
}
}
},
canGift = true,
addIntimacy = 10,
giftCoinType = 1,
giftPrice = 100,
bOpenSuit = true,
suitId = 23
},
[10007] = {
commodityId = 10007,
commodityName = "好好鸭",
coinType = 6,
price = 100,
beginTime = v0,
shopTag = v4,
gender = 0,
itemIds = {
400110
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
400110,
1,
0
}
}
}
}
}
},
canGift = true,
addIntimacy = 10,
giftCoinType = 1,
giftPrice = 100,
bOpenSuit = true,
suitId = 24
},
[10008] = {
commodityId = 10008,
commodityName = "喵小困",
beginTime = v0,
endTime = v11,
shopTag = v4,
shopSort = 10,
jumpId = 43,
jumpText = "限时福利赏",
gender = 0,
itemIds = {
400130
}
},
[10009] = {
commodityId = 10009,
commodityName = "胖胖达",
beginTime = v0,
endTime = v11,
shopTag = v4,
shopSort = 11,
jumpId = 38,
jumpText = "新手任务",
gender = 0,
itemIds = {
400140
},
bOpenSuit = true,
suitId = 81
},
[10010] = {
commodityId = 10010,
commodityName = "巨鳄霸",
coinType = 6,
price = 100,
beginTime = v0,
shopTag = v4,
gender = 0,
itemIds = {
400210
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
400210,
1,
0
}
}
}
}
}
},
canGift = true,
addIntimacy = 10,
giftCoinType = 1,
giftPrice = 100,
bOpenSuit = true,
suitId = 29
},
[10011] = {
commodityId = 10011,
commodityName = "小红狐",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 12,
jumpId = 39,
jumpText = "冲段挑战",
gender = 0,
itemIds = {
400470
},
bOpenSuit = true,
suitId = 39
},
[10012] = {
commodityId = 10012,
commodityName = "羊小萌",
coinType = 6,
price = 100,
beginTime = v0,
shopTag = v4,
gender = 0,
itemIds = {
400480
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
400480,
1,
0
}
}
}
}
}
},
canGift = true,
addIntimacy = 10,
giftCoinType = 1,
giftPrice = 100,
bOpenSuit = true,
suitId = 40
},
[10013] = {
commodityId = 10013,
commodityName = "紫萝萝",
coinType = 6,
price = 100,
beginTime = v0,
shopTag = v4,
gender = 0,
itemIds = {
400380
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
400380,
1,
0
}
}
}
}
}
},
canGift = true,
addIntimacy = 10,
giftCoinType = 1,
giftPrice = 100,
bOpenSuit = true,
suitId = 34
},
[10014] = {
commodityId = 10014,
commodityName = "玫珊珊",
coinType = 6,
price = 100,
beginTime = v0,
shopTag = v4,
gender = 0,
itemIds = {
400390
},
buyCondition = {
condition = {
{
conditionType = 179,
value = 1,
subConditionList = {
{
type = 153,
value = {
400390,
1,
0
}
}
}
}
}
},
canGift = true,
addIntimacy = 10,
giftCoinType = 1,
giftPrice = 100,
bOpenSuit = true,
suitId = 35
},
[10015] = {
commodityId = 10015,
commodityName = "蘑咕咕",
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 4080211199
},
shopTag = v4,
shopSort = 2,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
itemIds = {
400160
},
canGift = true,
addIntimacy = 80,
giftCoinType = 205,
giftPrice = 40,
bOpenSuit = true,
suitId = 25
},
[10016] = {
commodityId = 10016,
commodityName = "蘑咕咕",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1703174400
},
gender = 0,
itemIds = {
400161
}
},
[10017] = {
commodityId = 10017,
commodityName = "涂鸦小子",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
itemIds = {
400180
},
bOpenSuit = true,
suitId = 26
},
[10018] = {
commodityId = 10018,
commodityName = "涂鸦小子",
coinType = 200006,
price = 12,
beginTime = v1,
itemIds = {
400181
}
},
[10019] = {
commodityId = 10019,
commodityName = "致命啄客",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 3,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
itemIds = {
400190
},
bOpenSuit = true,
suitId = 27
},
[10020] = {
commodityId = 10020,
commodityName = "致命啄客",
coinType = 200006,
price = 12,
beginTime = v1,
gender = 0,
itemIds = {
400191
}
},
[10021] = {
commodityId = 10021,
commodityName = "鲨拉拉",
beginTime = v0,
endTime = {
seconds = 1737820799
},
shopTag = v4,
shopSort = 5,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
400200
},
bOpenSuit = true,
suitId = 28
},
[10022] = {
commodityId = 10022,
commodityName = "鲨拉拉",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400201
}
},
[10023] = {
commodityId = 10023,
commodityName = "名侦探",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v4,
shopSort = 5,
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
400220
},
bOpenSuit = true,
suitId = 123
},
[10024] = {
commodityId = 10024,
commodityName = "名侦探",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400221
}
},
[10025] = {
commodityId = 10025,
commodityName = "冰糕糕",
beginTime = v0,
shopTag = v4,
shopSort = 6,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
400230
}
},
[10026] = {
commodityId = 10026,
commodityName = "冰糕糕",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400231
}
},
[10027] = {
commodityId = 10027,
commodityName = "渔小夫",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
itemIds = {
400260
},
bOpenSuit = true,
suitId = 30
},
[10028] = {
commodityId = 10028,
commodityName = "渔小夫",
coinType = 200006,
price = 12,
beginTime = v1,
gender = 0,
itemIds = {
400261
}
},
[10029] = {
commodityId = 10029,
commodityName = "星宝守护者",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400270
},
bOpenSuit = true,
suitId = 108
},
[10030] = {
commodityId = 10030,
commodityName = "星宝守护者",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400271
}
},
[10031] = {
commodityId = 10031,
commodityName = "星骑士",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 7,
jumpId = 40,
jumpText = "庆典签到",
gender = 0,
itemIds = {
400280
},
bOpenSuit = true,
suitId = 31
},
[10032] = {
commodityId = 10032,
commodityName = "兔叽叽",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 8,
jumpId = 39,
jumpText = "冲段挑战",
gender = 0,
itemIds = {
400290
},
bOpenSuit = true,
suitId = 32
},
[10033] = {
commodityId = 10033,
commodityName = "兔叽叽",
coinType = 200006,
price = 12,
beginTime = v1,
gender = 0,
itemIds = {
400291
}
},
[10034] = {
commodityId = 10034,
commodityName = "黑曜斩  夜魇",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1726761599
},
shopTag = v4,
shopSort = 2,
jumpId = 185,
jumpText = "霜天冰雨",
gender = 0,
itemIds = {
400300
},
canGift = true,
addIntimacy = 500,
giftCoinType = 203,
giftPrice = 320,
bOpenSuit = true,
suitId = 169
},
[10035] = {
commodityId = 10035,
commodityName = "追风者 墨斯",
beginTime = v0,
endTime = v11,
shopTag = v4,
jumpId = 114,
jumpText = "薅鹅毛",
gender = 0,
itemIds = {
400310
}
},
[10036] = {
commodityId = 10036,
commodityName = "追风者 墨斯",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400311
}
},
[10037] = {
commodityId = 10037,
commodityName = "追风者 墨斯",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400312
}
},
[10038] = {
commodityId = 10038,
commodityName = "星梦者 梦谜 ",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744905599
},
shopTag = v4,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
itemIds = {
400330
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 65
},
[10039] = {
commodityId = 10039,
commodityName = "星梦者 梦谜",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1704643200
},
gender = 0,
itemIds = {
400331
}
},
[10040] = {
commodityId = 10040,
commodityName = "星梦者 梦谜",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1704643200
},
gender = 0,
itemIds = {
400332
}
},
[10041] = {
commodityId = 10041,
commodityName = "狐剑仙 逸影",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
itemIds = {
400340
},
bOpenSuit = true,
suitId = 33
},
[10042] = {
commodityId = 10042,
commodityName = "狐剑仙 逸影",
coinType = 200006,
price = 80,
beginTime = v1,
gender = 0,
itemIds = {
400341
}
},
[10043] = {
commodityId = 10043,
commodityName = "狐剑仙 逸影",
coinType = 200006,
price = 80,
beginTime = v1,
gender = 0,
itemIds = {
400342
}
},
[10044] = {
commodityId = 10044,
commodityName = "灵魂歌姬 宝拉",
beginTime = {
seconds = 1703174400
},
endTime = {
seconds = 1704988799
},
shopTag = v4,
jumpId = 51,
jumpText = "舞动青春秀",
gender = 0,
itemIds = {
400350
},
bOpenSuit = true,
suitId = 57
},
[10045] = {
commodityId = 10045,
commodityName = "灵魂歌姬 宝拉",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400351
}
},
[10046] = {
commodityId = 10046,
commodityName = "灵魂歌姬 宝拉",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400352
}
},
[10047] = {
commodityId = 10047,
commodityName = "小旋风 辛西亚 ",
beginTime = v0,
endTime = v11,
shopTag = v4,
jumpId = 114,
jumpText = "薅鹅毛",
gender = 0,
itemIds = {
400360
}
},
[10048] = {
commodityId = 10048,
commodityName = "小旋风 辛西亚 ",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400361
}
},
[10049] = {
commodityId = 10049,
commodityName = "小旋风 辛西亚 ",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400362
}
},
[10050] = {
commodityId = 10050,
commodityName = "都市猎人 明彦",
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1734623999
},
shopTag = v4,
shopSort = 2,
jumpId = 800,
jumpText = "战神颂歌",
gender = 0,
minVersion = "1.3.7.53",
itemIds = {
400370
},
bOpenSuit = true,
suitId = 275
},
[10051] = {
commodityId = 10051,
commodityName = "都市猎人 明彦",
coinType = 200006,
price = 20,
beginTime = v0,
shopSort = 2,
gender = 0,
minVersion = "1.3.7.53",
itemIds = {
400371
}
},
[10052] = {
commodityId = 10052,
commodityName = "都市猎人 明彦",
coinType = 200006,
price = 20,
beginTime = v0,
shopSort = 2,
gender = 0,
minVersion = "1.3.7.53",
itemIds = {
400372
}
},
[10053] = {
commodityId = 10053,
commodityName = "蓝白风暴",
beginTime = v0,
endTime = v11,
shopTag = v4,
shopSort = 4,
jumpId = 42,
jumpText = "随心挑战礼",
gender = 0,
itemIds = {
400400
}
},
[10054] = {
commodityId = 10054,
commodityName = "蓝白风暴",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400401
}
},
[10055] = {
commodityId = 10055,
commodityName = "荧光新秀",
coinType = 6,
price = 800,
beginTime = v0,
shopTag = v4,
gender = 0,
itemIds = {
400410
},
canGift = true,
addIntimacy = 80,
giftCoinType = 1,
giftPrice = 800,
bOpenSuit = true,
suitId = 36
},
[10056] = {
commodityId = 10056,
commodityName = "荧光新秀",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400411
}
},
[10057] = {
commodityId = 10057,
commodityName = "樱花蓝调",
coinType = 6,
price = 800,
beginTime = v0,
shopTag = v4,
gender = 0,
itemIds = {
400420
},
canGift = true,
addIntimacy = 80,
giftCoinType = 1,
giftPrice = 800,
bOpenSuit = true,
suitId = 37
},
[10058] = {
commodityId = 10058,
commodityName = "樱花蓝调",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400421
}
},
[10059] = {
commodityId = 10059,
commodityName = "芋泥啵啵",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400430
}
},
[10060] = {
commodityId = 10060,
commodityName = "芋泥啵啵",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400431
}
},
[10061] = {
commodityId = 10061,
commodityName = "大魔术师 尼克",
coinType = 6,
price = 1600,
beginTime = v0,
shopTag = v4,
shopSort = 123,
gender = 0,
itemIds = {
400440
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1600,
bOpenSuit = true,
suitId = 38
},
[10062] = {
commodityId = 10062,
commodityName = "大魔术师 尼克",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400441
}
},
[10063] = {
commodityId = 10063,
commodityName = "大魔术师 尼克",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400442
}
},
[10064] = {
commodityId = 10064,
commodityName = "甜橙喵 桑妮",
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1709827199
},
shopTag = v4,
jumpId = 171,
jumpText = "充值送好礼",
gender = 0,
itemIds = {
400450
},
bOpenSuit = true,
suitId = 139
},
[10065] = {
commodityId = 10065,
commodityName = "甜橙喵 桑妮",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1708704000
},
gender = 0,
itemIds = {
400451
}
},
[10066] = {
commodityId = 10066,
commodityName = "甜橙喵 桑妮",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1708704000
},
gender = 0,
itemIds = {
400452
}
},
[10067] = {
commodityId = 10067,
commodityName = "米花娘 爆爆",
beginTime = v0,
endTime = v11,
shopTag = v4,
jumpId = 43,
jumpText = "限时福利赏",
gender = 0,
itemIds = {
400460
},
bOpenSuit = true,
suitId = 530
},
[10068] = {
commodityId = 10068,
commodityName = "米花娘 爆爆",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400461
}
},
[10069] = {
commodityId = 10069,
commodityName = "米花娘 爆爆",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400462
}
},
[10070] = {
commodityId = 10070,
commodityName = "兰小铃",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
itemIds = {
400510
},
bOpenSuit = true,
suitId = 41
},
[10071] = {
commodityId = 10071,
commodityName = "兰小铃",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400511
}
},
[10072] = {
commodityId = 10072,
commodityName = "电音迷妹",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400500
}
},
[10073] = {
commodityId = 10073,
commodityName = "电音迷妹",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400501
}
},
[10074] = {
commodityId = 10074,
commodityName = "蝶语仙 莉琦",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
jumpId = 11,
jumpText = v8,
gender = 0,
itemIds = {
400540
},
bOpenSuit = true,
suitId = 42
},
[10075] = {
commodityId = 10075,
commodityName = "蝶语仙 莉琦",
coinType = 200006,
price = 20,
beginTime = v1,
gender = 0,
itemIds = {
400541
}
},
[10076] = {
commodityId = 10076,
commodityName = "蝶语仙 莉琦",
coinType = 200006,
price = 20,
beginTime = v1,
gender = 0,
itemIds = {
400542
}
},
[10077] = {
commodityId = 10077,
commodityName = "桃中仙 桃夭",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
jumpId = 11,
jumpText = v8,
gender = 0,
itemIds = {
400550
},
bOpenSuit = true,
suitId = 43
},
[10078] = {
commodityId = 10078,
commodityName = "桃中仙 桃夭",
coinType = 200006,
price = 20,
beginTime = v1,
gender = 0,
itemIds = {
400551
}
},
[10079] = {
commodityId = 10079,
commodityName = "桃中仙 桃夭",
coinType = 200006,
price = 20,
beginTime = v1,
gender = 0,
itemIds = {
400552
}
},
[10080] = {
commodityId = 10080,
commodityName = "橙小兔",
beginTime = v0,
shopTag = v4,
shopSort = 3,
jumpId = 27,
jumpText = "首充",
gender = 0,
itemIds = {
400560
},
bOpenSuit = true,
suitId = 44
},
[10081] = {
commodityId = 10081,
commodityName = "橙小兔",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400561
}
},
[10082] = {
commodityId = 10082,
commodityName = "黑曜斩  夜魇",
coinType = 200006,
price = 80,
beginTime = v0,
gender = 0,
itemIds = {
400301
}
},
[10083] = {
commodityId = 10083,
commodityName = "黑曜斩  夜魇",
coinType = 200006,
price = 80,
beginTime = v0,
gender = 0,
itemIds = {
400302
}
},
[10084] = {
commodityId = 10084,
commodityName = "狐剑仙 炎炀",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
itemIds = {
400570
},
bOpenSuit = true,
suitId = 45
},
[10085] = {
commodityId = 10085,
commodityName = "狼少年 格雷",
beginTime = {
seconds = 1709827200
},
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 56,
jumpText = "超值礼包",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
400580
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 800,
bOpenSuit = true,
suitId = 116
},
[10086] = {
commodityId = 10086,
commodityName = "狼少年 格雷",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1708012800
},
gender = 0,
itemIds = {
400581
}
},
[10087] = {
commodityId = 10087,
commodityName = "狼少年 格雷",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1708012800
},
gender = 0,
itemIds = {
400582
}
},
[10088] = {
commodityId = 10088,
commodityName = "招财元宝 小满",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
jumpId = 9,
jumpText = "桃源通行证",
gender = 0,
itemIds = {
400590
},
bOpenSuit = true,
suitId = 46
},
[10089] = {
commodityId = 10089,
commodityName = "招财元宝 小满",
coinType = 200006,
price = 20,
beginTime = v1,
gender = 0,
itemIds = {
400591
}
},
[10090] = {
commodityId = 10090,
commodityName = "招财元宝 小满",
coinType = 200006,
price = 20,
beginTime = v1,
gender = 0,
itemIds = {
400592
}
},
[10091] = {
commodityId = 10091,
commodityName = "舞狮少女",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400600
}
},
[10092] = {
commodityId = 10092,
commodityName = "舞狮少女",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400601
}
},
[10093] = {
commodityId = 10093,
commodityName = "舞狮少女",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400602
}
},
[10094] = {
commodityId = 10094,
commodityName = "海精灵 海伦娜",
beginTime = {
seconds = 1709827200
},
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 57,
jumpText = "时装礼包",
gender = 0,
itemIds = {
400610
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 630,
bOpenSuit = true,
suitId = 59
},
[10095] = {
commodityId = 10095,
commodityName = "海精灵 海伦娜",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1704038400
},
gender = 0,
itemIds = {
400611
}
},
[10096] = {
commodityId = 10096,
commodityName = "海精灵 海伦娜",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1704038400
},
gender = 0,
itemIds = {
400612
}
},
[10097] = {
commodityId = 10097,
commodityName = "小红帽",
beginTime = {
seconds = 1709827200
},
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 56,
jumpText = "超值礼包",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
400620
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 800,
bOpenSuit = true,
suitId = 115
},
[10098] = {
commodityId = 10098,
commodityName = "小红帽",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1708012800
},
gender = 0,
itemIds = {
400621
}
},
[10099] = {
commodityId = 10099,
commodityName = "小红帽",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1708012800
},
gender = 0,
itemIds = {
400622
}
},
[10100] = {
commodityId = 10100,
commodityName = "动感超人小新",
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1715875199
},
shopTag = v4,
shopSort = 108,
jumpId = 78,
jumpText = "盛装小新祈愿",
gender = 0,
itemIds = {
400640
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 47
},
[10101] = {
commodityId = 10101,
commodityName = "龙虾小新",
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1715875199
},
shopTag = v4,
shopSort = 109,
jumpId = 80,
jumpText = "盛装小新祈愿",
gender = 0,
itemIds = {
400650
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 48
},
[10102] = {
commodityId = 10102,
commodityName = "超音速少女",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400490
}
},
[10103] = {
commodityId = 10103,
commodityName = "超音速少女",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400491
}
},
[10104] = {
commodityId = 10104,
commodityName = "梨小棠",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 9,
jumpId = 16,
jumpText = "赛季兑换",
gender = 0,
itemIds = {
400680
},
bOpenSuit = true,
suitId = 51
},
[10105] = {
commodityId = 10105,
commodityName = "梨小棠",
coinType = 200006,
price = 12,
beginTime = v1,
gender = 0,
itemIds = {
400681
}
},
[10106] = {
commodityId = 10106,
commodityName = "蜡笔小新",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v4,
shopSort = 110,
jumpId = 75,
jumpText = "小新饼干屋",
gender = 0,
itemIds = {
400690
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 900,
bOpenSuit = true,
suitId = 52
},
[10107] = {
commodityId = 10107,
commodityName = "小爱",
beginTime = {
seconds = 1703786400
},
endTime = {
seconds = 1705247999
},
shopTag = v4,
jumpId = 118,
jumpText = "跨年领小爱",
gender = 0,
itemIds = {
400700
},
bOpenSuit = true,
suitId = 56
},
[10108] = {
commodityId = 10108,
commodityName = "黑夜蔷薇",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400520
}
},
[10109] = {
commodityId = 10109,
commodityName = "黑夜蔷薇",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400521
}
},
[10110] = {
commodityId = 10110,
commodityName = "黑夜蔷薇",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400522
}
},
[10111] = {
commodityId = 10111,
commodityName = "阿童木妹妹",
beginTime = {
seconds = 1717171200
},
endTime = {
seconds = 1718899199
},
shopTag = v4,
shopSort = 2,
jumpId = 183,
jumpText = "阿童木祈愿",
gender = 0,
minVersion = v25,
itemIds = {
400720
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 241
},
[10112] = {
commodityId = 10112,
commodityName = "阿童木",
beginTime = {
seconds = 1717171200
},
endTime = {
seconds = 1718899199
},
shopTag = v4,
shopSort = 2,
jumpId = 182,
jumpText = "阿童木祈愿",
gender = 0,
minVersion = v25,
itemIds = {
400730
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 242
},
[10113] = {
commodityId = 10113,
commodityName = "瑶光公主 伊莎贝尔",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400530
}
},
[10114] = {
commodityId = 10114,
commodityName = "瑶光公主 伊莎贝尔",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400531
}
},
[10115] = {
commodityId = 10115,
commodityName = "瑶光公主 伊莎贝尔",
coinType = 200006,
price = 20,
beginTime = v0,
gender = 0,
itemIds = {
400532
}
},
[10116] = {
commodityId = 10116,
commodityName = "王者-瑶（时之祈愿）",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400750
},
bOpenSuit = true,
suitId = 133
},
[10117] = {
commodityId = 10117,
commodityName = "王者-孙尚香（经典）",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400760
},
bOpenSuit = true,
suitId = 131
},
[10118] = {
commodityId = 10118,
commodityName = "王者-鲁班七号（星空梦想）",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400770
},
bOpenSuit = true,
suitId = 134
},
[10119] = {
commodityId = 10119,
commodityName = "王者-小乔（山海琳琅生）",
beginTime = v1,
endTime = {
seconds = 1704470399
},
shopTag = v4,
jumpId = 43,
jumpText = "福袋满琳琅",
gender = 0,
itemIds = {
400780
},
bOpenSuit = true,
suitId = 55
},
[10120] = {
commodityId = 10120,
commodityName = "王者-梦奇（经典）",
beginTime = {
seconds = 1703174400
},
endTime = {
seconds = 1704383999
},
shopTag = v4,
jumpId = 50,
jumpText = "和梦奇玩耍",
gender = 0,
itemIds = {
400790
},
bOpenSuit = true,
suitId = 58
},
[10121] = {
commodityId = 10121,
commodityName = "月光女神",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v4,
shopSort = 3,
jumpId = 1062,
jumpText = "月夜歌吟",
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
400630
},
canGift = true,
addIntimacy = 500,
giftCoinType = 203,
giftPrice = 320,
bOpenSuit = true,
suitId = 112
},
[10122] = {
commodityId = 10122,
commodityName = "月光女神",
coinType = 200006,
price = 80,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
400631
}
},
[10123] = {
commodityId = 10123,
commodityName = "月光女神",
coinType = 200006,
price = 80,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
400632
}
},
[10124] = {
commodityId = 10124,
commodityName = "王者-妲己（时之奇旅）",
coinType = 6,
price = 1000000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400800
},
bOpenSuit = true,
suitId = 132
},
[10125] = {
commodityId = 10125,
commodityName = "缤纷奶糖",
coinType = 6,
price = 1000000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400810
}
},
[10126] = {
commodityId = 10126,
commodityName = "缤纷奶糖",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400811
}
},
[10127] = {
commodityId = 10127,
commodityName = "珍包包",
beginTime = v0,
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
400820
},
bOpenSuit = true,
suitId = 119
},
[10128] = {
commodityId = 10128,
commodityName = "珍包包",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400821
}
},
[10129] = {
commodityId = 10129,
commodityName = "羊彬彬",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1713974400
},
shopTag = v4,
jumpId = 16,
jumpText = "赛季兑换",
gender = 0,
itemIds = {
400830
},
bOpenSuit = true,
suitId = 157
},
[10130] = {
commodityId = 10130,
commodityName = "羊彬彬",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400831
}
},
[10131] = {
commodityId = 10131,
commodityName = "羊雅雅",
coinType = 6,
price = 1000000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400840
}
},
[10132] = {
commodityId = 10132,
commodityName = "羊雅雅",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400841
}
},
[10133] = {
commodityId = 10133,
commodityName = "荷小悦",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "桃源通行证",
gender = 0,
itemIds = {
400850
},
bOpenSuit = true,
suitId = 53
},
[10134] = {
commodityId = 10134,
commodityName = "荷小悦",
coinType = 200006,
price = 12,
beginTime = v1,
gender = 0,
itemIds = {
400851
}
},
[10135] = {
commodityId = 10135,
commodityName = "跆拳道新星",
beginTime = {
seconds = 1706846400
},
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 114,
jumpText = "薅鹅毛",
gender = 0,
itemIds = {
400860
},
bOpenSuit = true,
suitId = 109
},
[10136] = {
commodityId = 10136,
commodityName = "跆拳道新星",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1706846400
},
gender = 0,
itemIds = {
400861
}
},
[10137] = {
commodityId = 10137,
commodityName = "守护星",
beginTime = {
seconds = 1706846400
},
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 114,
jumpText = "薅鹅毛",
gender = 0,
itemIds = {
400870
},
bOpenSuit = true,
suitId = 124
},
[10138] = {
commodityId = 10138,
commodityName = "守护星",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1706846400
},
gender = 0,
itemIds = {
400871
}
},
[10139] = {
commodityId = 10139,
commodityName = "魔力厨娘",
beginTime = {
seconds = 1706846400
},
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 114,
jumpText = "薅鹅毛",
gender = 0,
itemIds = {
400880
},
bOpenSuit = true,
suitId = 111
},
[10140] = {
commodityId = 10140,
commodityName = "魔力厨娘",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1706846400
},
gender = 0,
itemIds = {
400881
}
},
[10141] = {
commodityId = 10141,
commodityName = "知识甜心",
beginTime = {
seconds = 1706846400
},
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 114,
jumpText = "薅鹅毛",
gender = 0,
itemIds = {
400890
},
bOpenSuit = true,
suitId = 110
},
[10142] = {
commodityId = 10142,
commodityName = "知识甜心",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1706846400
},
gender = 0,
itemIds = {
400891
}
},
[10143] = {
commodityId = 10143,
commodityName = "蛙亚蛙",
coinType = 6,
price = 1000000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400900
}
},
[10144] = {
commodityId = 10144,
commodityName = "蛙亚蛙",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400901
}
},
[10145] = {
commodityId = 10145,
commodityName = "蕉绿绿",
beginTime = {
seconds = 1722614400
},
endTime = {
seconds = 1723823999
},
shopTag = v4,
shopSort = 99,
jumpId = 1057,
jumpText = "幸运星",
gender = 0,
minVersion = "1.3.12.70",
itemIds = {
400910
},
bOpenSuit = true,
suitId = 140
},
[10146] = {
commodityId = 10146,
commodityName = "蕉绿绿",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1709308800
},
gender = 0,
minVersion = "1.3.12.70",
itemIds = {
400911
}
},
[10147] = {
commodityId = 10147,
commodityName = "香嘟嘟",
beginTime = {
seconds = 1722614400
},
endTime = {
seconds = 1723823999
},
shopTag = v4,
shopSort = 99,
jumpId = 1057,
jumpText = "幸运星",
gender = 0,
minVersion = "1.3.12.70",
itemIds = {
400920
},
bOpenSuit = true,
suitId = 141
},
[10148] = {
commodityId = 10148,
commodityName = "香嘟嘟",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1709222400
},
gender = 0,
minVersion = "1.3.12.70",
itemIds = {
400921
}
},
[10149] = {
commodityId = 10149,
commodityName = "龟蜜",
coinType = 6,
price = 1000000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400930
},
bOpenSuit = true,
suitId = 64
},
[10150] = {
commodityId = 10150,
commodityName = "龟蜜",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400931
}
},
[10151] = {
commodityId = 10151,
commodityName = "特战小子",
coinType = 6,
price = 1000000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400940
}
},
[10152] = {
commodityId = 10152,
commodityName = "特战小子",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400941
}
},
[10153] = {
commodityId = 10153,
commodityName = "唐僧",
beginTime = v0,
shopTag = v4,
jumpId = 27,
jumpText = "首充",
gender = 0,
itemIds = {
400660
},
bOpenSuit = true,
suitId = 49
},
[10154] = {
commodityId = 10154,
commodityName = "唐僧",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400661
}
},
[10155] = {
commodityId = 10155,
commodityName = "孙悟空",
beginTime = v0,
shopTag = v4,
jumpId = 27,
jumpText = "首充",
gender = 0,
itemIds = {
400670
},
bOpenSuit = true,
suitId = 50
},
[10156] = {
commodityId = 10156,
commodityName = "孙悟空",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
itemIds = {
400671
}
},
[10157] = {
commodityId = 10157,
commodityName = "荷小颜",
beginTime = v1,
endTime = {
seconds = 1706198399
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "桃源通行证",
gender = 0,
itemIds = {
400950
},
bOpenSuit = true,
suitId = 54
},
[10158] = {
commodityId = 10158,
commodityName = "荷小颜",
coinType = 200006,
price = 12,
beginTime = v1,
gender = 0,
itemIds = {
400951
}
},
[10159] = {
commodityId = 10159,
commodityName = "气泡狗阿绿",
beginTime = v0,
endTime = v11,
shopTag = v4,
jumpId = 42,
jumpText = "星宝来了",
gender = 0,
itemIds = {
400960
},
bOpenSuit = true,
suitId = 135
},
[10160] = {
commodityId = 10160,
commodityName = "幸运鹅",
beginTime = v0,
endTime = v11,
shopTag = v4,
jumpId = 42,
jumpText = "星宝来了",
gender = 0,
itemIds = {
400970
},
bOpenSuit = true,
suitId = 136
},
[10161] = {
commodityId = 10161,
commodityName = "Human",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v4,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.78.58",
itemIds = {
400990
},
bOpenSuit = true,
suitId = 662
},
[10162] = {
commodityId = 10162,
commodityName = "Toby",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v4,
shopSort = 2,
jumpId = 1091,
jumpText = "摩天乐园",
gender = 0,
minVersion = "1.3.88.53",
itemIds = {
401000
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 61
},
[10163] = {
commodityId = 10163,
commodityName = "薯星星",
coinType = 6,
price = 1000000,
beginTime = v0,
endTime = v11,
shopTag = v4,
gender = 0,
itemIds = {
400980
}
},
[10164] = {
commodityId = 10164,
commodityName = "龟蜜",
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 4080211199
},
shopTag = v4,
shopSort = 2,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
itemIds = {
400930
},
canGift = true,
addIntimacy = 80,
giftCoinType = 205,
giftPrice = 40,
bOpenSuit = true,
suitId = 64
},
[10165] = {
commodityId = 10165,
commodityName = "龟蜜",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1705075200
},
gender = 0,
itemIds = {
400931
}
},
[11000] = {
commodityId = 11000,
commodityName = " 鲤小仙  锦瞳",
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 175,
jumpText = v8,
gender = 0,
minVersion = v26,
itemIds = {
401020
},
bOpenSuit = true,
suitId = 66
},
[11001] = {
commodityId = 11001,
commodityName = " 鲤小仙  锦瞳",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v26,
itemIds = {
401021
}
},
[11002] = {
commodityId = 11002,
commodityName = " 鲤小仙  锦瞳",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v26,
itemIds = {
401022
}
},
[11003] = {
commodityId = 11003,
commodityName = "奥特曼-迪迦",
beginTime = {
seconds = 1706803200
},
endTime = {
seconds = 1711295999
},
shopTag = v4,
jumpId = 179,
jumpText = "奥特曼祈愿",
gender = 0,
minVersion = v26,
itemIds = {
401030
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1570,
bOpenSuit = true,
suitId = 67
},
[11004] = {
commodityId = 11004,
commodityName = "魔法师 哈奇",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v4,
jumpId = 1062,
jumpText = "月夜歌吟",
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
401040
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 68
},
[11005] = {
commodityId = 11005,
commodityName = "魔法师 哈奇",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
401041
}
},
[11006] = {
commodityId = 11006,
commodityName = "魔法师 哈奇",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
401042
}
},
[11007] = {
commodityId = 11007,
commodityName = "虎啸龙 毕小烈",
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 175,
jumpText = v8,
gender = 0,
minVersion = v26,
itemIds = {
401050
},
bOpenSuit = true,
suitId = 69
},
[11008] = {
commodityId = 11008,
commodityName = "虎啸龙 毕小烈",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v26,
itemIds = {
401051
}
},
[11009] = {
commodityId = 11009,
commodityName = "虎啸龙 毕小烈",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v26,
itemIds = {
401052
}
},
[11010] = {
commodityId = 11010,
commodityName = "四叶草精灵  克洛洛",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v4,
jumpId = 1062,
jumpText = "月夜歌吟",
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
401060
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 70
},
[11011] = {
commodityId = 11011,
commodityName = "四叶草精灵  克洛洛",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
401061
}
},
[11012] = {
commodityId = 11012,
commodityName = "四叶草精灵  克洛洛",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
401062
}
},
[11013] = {
commodityId = 11013,
commodityName = "雪小熊  雪诺比",
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 9,
jumpText = "山海通行证",
gender = 0,
minVersion = v26,
itemIds = {
401070
},
bOpenSuit = true,
suitId = 71
},
[11014] = {
commodityId = 11014,
commodityName = "雪小熊  雪诺比",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v26,
itemIds = {
401071
}
},
[11015] = {
commodityId = 11015,
commodityName = "雪小熊  雪诺比",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v26,
itemIds = {
401072
}
},
[11016] = {
commodityId = 11016,
commodityName = "小龙人 辰儿",
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708531200
},
shopTag = v4,
jumpId = 143,
jumpText = "福星手账簿",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
401080
},
bOpenSuit = true,
suitId = 72
},
[11017] = {
commodityId = 11017,
commodityName = "小龙人 辰儿",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
401081
}
},
[11018] = {
commodityId = 11018,
commodityName = "小龙人 辰儿",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
401082
}
},
[11019] = {
commodityId = 11019,
commodityName = "未来之星 欧米",
beginTime = {
seconds = 1709827200
},
endTime = {
seconds = 1711555200
},
shopTag = v4,
jumpId = 188,
jumpText = "幸运祈愿",
gender = 0,
minVersion = v26,
itemIds = {
401090
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 73
},
[11020] = {
commodityId = 11020,
commodityName = "未来之星 欧米",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1709827200
},
gender = 0,
minVersion = v26,
itemIds = {
401091
}
},
[11021] = {
commodityId = 11021,
commodityName = "未来之星 欧米",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1709827200
},
gender = 0,
minVersion = v26,
itemIds = {
401092
}
},
[11022] = {
commodityId = 11022,
commodityName = "奥特曼-赛罗",
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1711295999
},
shopTag = v4,
jumpId = 179,
jumpText = "奥特曼祈愿",
gender = 0,
minVersion = v26,
itemIds = {
401100
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1570,
bOpenSuit = true,
suitId = 106
},
[11023] = {
commodityId = 11023,
commodityName = "奥特曼-小怪兽杰顿",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v26,
itemIds = {
401110
},
suitId = 211
},
[11024] = {
commodityId = 11024,
commodityName = "奥特曼-泽塔",
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1711295999
},
shopTag = v4,
jumpId = 179,
jumpText = "奥特曼祈愿",
gender = 0,
minVersion = v26,
itemIds = {
401120
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1570,
bOpenSuit = true,
suitId = 107
},
[11025] = {
commodityId = 11025,
commodityName = "功夫熊猫",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopTag = v4,
shopSort = 95,
jumpId = 1048,
jumpText = "功夫熊猫返场",
gender = 0,
minVersion = "1.3.7.97",
itemIds = {
401130
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2450,
bOpenSuit = true,
suitId = 161
},
[11026] = {
commodityId = 11026,
commodityName = "精小卫",
endTime = {
seconds = 1710431999
},
shopTag = v4,
shopSort = 2,
jumpId = 175,
jumpText = v8,
gender = 0,
minVersion = v26,
itemIds = {
401150
},
bOpenSuit = true,
suitId = 76
},
[11027] = {
commodityId = 11027,
commodityName = "精小卫",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v26,
itemIds = {
401151
}
},
[11028] = {
commodityId = 11028,
commodityName = "夔牛牛",
endTime = {
seconds = 1710431999
},
shopTag = v4,
shopSort = 2,
jumpId = 175,
jumpText = v8,
gender = 0,
minVersion = v26,
itemIds = {
401160
},
bOpenSuit = true,
suitId = 77
},
[11029] = {
commodityId = 11029,
commodityName = "夔牛牛",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v26,
itemIds = {
401161
}
},
[11030] = {
commodityId = 11030,
commodityName = "鹿灵灵",
endTime = {
seconds = 1710431999
},
shopTag = v4,
shopSort = 2,
jumpId = 175,
jumpText = v8,
gender = 0,
minVersion = v26,
itemIds = {
401170
},
bOpenSuit = true,
suitId = 78
},
[11031] = {
commodityId = 11031,
commodityName = "鹿灵灵",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v26,
itemIds = {
401171
}
},
[11032] = {
commodityId = 11032,
commodityName = "梅中客",
endTime = {
seconds = 1710431999
},
shopTag = v4,
shopSort = 3,
jumpId = 9,
jumpText = "山海通行证",
gender = 0,
minVersion = v26,
itemIds = {
401180
},
bOpenSuit = true,
suitId = 79
},
[11033] = {
commodityId = 11033,
commodityName = "梅中客",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v26,
itemIds = {
401181
}
},
[11034] = {
commodityId = 11034,
commodityName = "梅上仙",
endTime = {
seconds = 1710431999
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "山海通行证",
gender = 0,
minVersion = v26,
itemIds = {
401190
},
bOpenSuit = true,
suitId = 80
},
[11035] = {
commodityId = 11035,
commodityName = "梅上仙",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v26,
itemIds = {
401191
}
},
[11036] = {
commodityId = 11036,
commodityName = "角梦梦",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v4,
shopSort = 2,
jumpId = 1062,
jumpText = "月夜歌吟",
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
401200
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 118
},
[11037] = {
commodityId = 11037,
commodityName = "角梦梦",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
401201
}
},
[11038] = {
commodityId = 11038,
commodityName = "花灯灯",
coinType = 1,
price = 400,
beginTime = {
seconds = 1708790400
},
endTime = v12,
shopTag = v4,
shopSort = 2,
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
401210
},
canGift = true,
addIntimacy = 80,
giftCoinType = 1,
giftPrice = 400,
bOpenSuit = true,
suitId = 128
},
[11039] = {
commodityId = 11039,
commodityName = "花灯灯",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1708704000
},
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
401211
}
},
[11040] = {
commodityId = 11040,
commodityName = "游梦梦",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v4,
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
401220
},
bOpenSuit = true,
suitId = 129
},
[11041] = {
commodityId = 11041,
commodityName = "游梦梦",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v26,
itemIds = {
401221
}
},
[11042] = {
commodityId = 11042,
commodityName = "凌小霜",
beginTime = {
seconds = 1707148800
},
endTime = {
seconds = 1710431999
},
shopTag = v4,
jumpId = 16,
jumpText = "赛季兑换",
gender = 0,
minVersion = v26,
itemIds = {
401230
},
bOpenSuit = true,
suitId = 125
},
[11043] = {
commodityId = 11043,
commodityName = "凌小霜",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1707148800
},
gender = 0,
minVersion = v26,
itemIds = {
401231
}
},
[11044] = {
commodityId = 11044,
commodityName = "豹卷卷",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v4,
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
401330
},
bOpenSuit = true,
suitId = 127
},
[11045] = {
commodityId = 11045,
commodityName = "豹卷卷",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1707148800
},
gender = 0,
minVersion = v26,
itemIds = {
401331
}
},
[11046] = {
commodityId = 11046,
commodityName = "小龙女 洛灵",
endTime = {
seconds = 1710431999
},
shopTag = v4,
shopSort = 2,
jumpId = 175,
jumpText = v8,
gender = 0,
minVersion = v26,
itemIds = {
401340
},
bOpenSuit = true,
suitId = 74
},
[11047] = {
commodityId = 11047,
commodityName = "小龙女 洛灵",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v26,
itemIds = {
401341
}
},
[11048] = {
commodityId = 11048,
commodityName = "小龙女 洛灵",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v26,
itemIds = {
401342
}
},
[11049] = {
commodityId = 11049,
commodityName = "小龙女 隐藏款",
endTime = {
seconds = 1710431999
},
shopTag = v4,
shopSort = 3,
jumpId = 175,
jumpText = v8,
gender = 0,
minVersion = v26,
itemIds = {
401350
},
bOpenSuit = true,
suitId = 75
},
[11050] = {
commodityId = 11050,
commodityName = "暴暴龙",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopTag = v4,
shopSort = 97,
jumpId = 1049,
jumpText = "暴暴龙返场",
gender = 0,
minVersion = "1.3.7.97",
itemIds = {
401360
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 720,
bOpenSuit = true,
suitId = 104
},
[11051] = {
commodityId = 11051,
commodityName = "漂泊王子 安德尔",
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 4080211199
},
shopTag = v4,
shopSort = 121,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
401140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 205,
giftPrice = 180,
bOpenSuit = true,
suitId = 142
},
[11052] = {
commodityId = 11052,
commodityName = "漂泊王子 安德尔",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1709222400
},
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
401141
}
},
[11053] = {
commodityId = 11053,
commodityName = "漂泊王子 安德尔",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1709222400
},
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
401142
}
},
[11054] = {
commodityId = 11054,
commodityName = "树芽芽",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v4,
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
401240
},
bOpenSuit = true,
suitId = 130
},
[11055] = {
commodityId = 11055,
commodityName = "树芽芽",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v26,
itemIds = {
401241
}
},
[11056] = {
commodityId = 11056,
commodityName = "摩羯座",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v26,
itemIds = {
401250
},
suitId = 120
},
[11057] = {
commodityId = 11057,
commodityName = "摩羯座",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v26,
itemIds = {
401251
}
},
[11058] = {
commodityId = 11058,
commodityName = "奶龙",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopTag = v4,
shopSort = 96,
jumpId = 1047,
jumpText = "奶龙返场",
gender = 0,
minVersion = "1.3.7.97",
itemIds = {
401010
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 103
},
[11059] = {
commodityId = 11059,
commodityName = "萧遥遥",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v4,
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
401260
},
bOpenSuit = true,
suitId = 121
},
[11060] = {
commodityId = 11060,
commodityName = "萧遥遥",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
401261
}
},
[11061] = {
commodityId = 11061,
commodityName = "鸽王",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v4,
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
401270
},
bOpenSuit = true,
suitId = 122
},
[11062] = {
commodityId = 11062,
commodityName = "鸽王",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1707408000
},
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
401271
}
},
[11063] = {
commodityId = 11063,
commodityName = "瑞幸女",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v26,
itemIds = {
401280
},
suitId = 126
},
[11064] = {
commodityId = 11064,
commodityName = "卡皮巴拉",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v26,
itemIds = {
401370
}
},
[11065] = {
commodityId = 11065,
commodityName = "栗栗子",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v26,
itemIds = {
401390
}
},
[11066] = {
commodityId = 11066,
commodityName = "栗栗子",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v26,
itemIds = {
401391
}
},
[11067] = {
commodityId = 11067,
commodityName = "最佳笋友",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v26,
itemIds = {
401290
}
},
[11068] = {
commodityId = 11068,
commodityName = "最佳笋友",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v26,
itemIds = {
401291
}
},
[11069] = {
commodityId = 11069,
commodityName = "小金龙",
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708185599
},
shopTag = v4,
shopSort = 2,
jumpId = 171,
jumpText = "充值送好礼",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
401430
},
bOpenSuit = true,
suitId = 117
},
[11101] = {
commodityId = 11101,
commodityName = "时空守护神 尤诺亚",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v4,
shopSort = 2,
jumpId = 180,
jumpText = v8,
gender = 0,
itemIds = {
401440
},
bOpenSuit = true,
suitId = 143
},
[11102] = {
commodityId = 11102,
commodityName = "时空守护神 尤诺亚",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
401441
}
},
[11103] = {
commodityId = 11103,
commodityName = "时空守护神 尤诺亚",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
401442
}
},
[11104] = {
commodityId = 11104,
commodityName = "超维守护神 尤诺亚",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v4,
shopSort = 2,
jumpId = 180,
jumpText = v8,
gender = 0,
itemIds = {
401450
},
bOpenSuit = true,
suitId = 149
},
[11105] = {
commodityId = 11105,
commodityName = "相柳",
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1724342399
},
shopTag = v4,
shopSort = 2,
jumpId = 1054,
jumpText = "长相思",
gender = 0,
minVersion = "1.3.12.47",
itemIds = {
401460
},
bOpenSuit = true,
suitId = 316
},
[11106] = {
commodityId = 11106,
commodityName = "赤水丰隆",
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1724342399
},
shopTag = v4,
shopSort = 2,
jumpId = 1054,
jumpText = "长相思",
gender = 0,
minVersion = "1.3.12.47",
itemIds = {
401470
},
bOpenSuit = true,
suitId = 317
},
[11107] = {
commodityId = 11107,
commodityName = "玱玹",
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1724342399
},
shopTag = v4,
shopSort = 2,
jumpId = 1054,
jumpText = "长相思",
gender = 0,
minVersion = "1.3.12.47",
itemIds = {
401480
},
bOpenSuit = true,
suitId = 318
},
[11108] = {
commodityId = 11108,
commodityName = "涂山璟",
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1724342399
},
shopTag = v4,
shopSort = 2,
jumpId = 1054,
jumpText = "长相思",
gender = 0,
minVersion = "1.3.12.47",
itemIds = {
401490
},
bOpenSuit = true,
suitId = 319
},
[11109] = {
commodityId = 11109,
commodityName = "小夭",
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1724342399
},
shopTag = v4,
shopSort = 2,
jumpId = 1054,
jumpText = "长相思",
gender = 0,
minVersion = "1.3.12.47",
itemIds = {
401500
},
bOpenSuit = true,
suitId = 320
},
[11110] = {
commodityId = 11110,
commodityName = "风间",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v4,
shopSort = 1,
jumpId = 615,
jumpText = "向日葵小班",
gender = 0,
itemIds = {
401510
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1590,
suitId = 187
},
[11111] = {
commodityId = 11111,
commodityName = "妮妮",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v4,
shopSort = 1,
jumpId = 616,
jumpText = "向日葵小班",
gender = 0,
itemIds = {
401520
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1590,
suitId = 188
},
[11112] = {
commodityId = 11112,
commodityName = "阿呆",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v4,
shopSort = 1,
jumpId = 617,
jumpText = "向日葵小班",
gender = 0,
itemIds = {
401530
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1590,
suitId = 189
},
[11113] = {
commodityId = 11113,
commodityName = "报时者 莫罗斯",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v4,
jumpId = 180,
jumpText = v8,
gender = 0,
itemIds = {
401540
},
bOpenSuit = true,
suitId = 144
},
[11114] = {
commodityId = 11114,
commodityName = "报时者 莫罗斯",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401541
}
},
[11115] = {
commodityId = 11115,
commodityName = "报时者 莫罗斯",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401542
}
},
[11116] = {
commodityId = 11116,
commodityName = "夜之萤 露露安",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v4,
jumpId = 9,
jumpText = "时光通行证",
gender = 0,
itemIds = {
401550
},
bOpenSuit = true,
suitId = 153
},
[11117] = {
commodityId = 11117,
commodityName = "夜之萤 露露安",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401551
}
},
[11118] = {
commodityId = 11118,
commodityName = "夜之萤 露露安",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401552
}
},
[11119] = {
commodityId = 11119,
commodityName = "魔小浣",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v4,
shopSort = 2,
jumpId = 189,
jumpText = v8,
gender = 0,
itemIds = {
401560
},
bOpenSuit = true,
suitId = 146
},
[11120] = {
commodityId = 11120,
commodityName = "魔小浣",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401561
}
},
[11121] = {
commodityId = 11121,
commodityName = "葵贝贝",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "时光通行证",
gender = 0,
itemIds = {
401570
},
bOpenSuit = true,
suitId = 154
},
[11122] = {
commodityId = 11122,
commodityName = "葵贝贝",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401571
}
},
[11123] = {
commodityId = 11123,
commodityName = "娱小编",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "时光通行证",
gender = 0,
itemIds = {
401580
},
bOpenSuit = true,
suitId = 155
},
[11124] = {
commodityId = 11124,
commodityName = "娱小编",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401581
}
},
[11125] = {
commodityId = 11125,
commodityName = "森小野",
beginTime = {
seconds = 1711641600
},
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
401590
},
bOpenSuit = true,
suitId = 174
},
[11126] = {
commodityId = 11126,
commodityName = "森小野",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1711641600
},
gender = 0,
itemIds = {
401591
}
},
[11127] = {
commodityId = 11127,
commodityName = "咖啡师小瑞",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401600
}
},
[11128] = {
commodityId = 11128,
commodityName = "小真",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopTag = v4,
shopSort = 93,
jumpId = 1048,
jumpText = "功夫熊猫返场",
gender = 0,
minVersion = "1.3.7.97",
itemIds = {
401610
},
bOpenSuit = true,
suitId = 162
},
[11129] = {
commodityId = 11129,
commodityName = "小丑",
beginTime = {
seconds = 1740672000
},
endTime = v12,
shopTag = v4,
shopSort = 122,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
itemIds = {
401620
},
canGift = true,
addIntimacy = 160,
giftCoinType = 205,
giftPrice = 180,
bOpenSuit = true,
suitId = 168
},
[11130] = {
commodityId = 11130,
commodityName = "小丑",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401621
}
},
[11131] = {
commodityId = 11131,
commodityName = "小丑",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401622
}
},
[11132] = {
commodityId = 11132,
commodityName = "小肥柴",
beginTime = {
seconds = 1719590400
},
endTime = {
seconds = 1720713599
},
shopTag = v4,
shopSort = 2,
jumpId = 600,
jumpText = "小肥柴祈愿",
gender = 0,
minVersion = "1.3.7.73",
itemIds = {
401630
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 280
},
[11133] = {
commodityId = 11133,
commodityName = "白菜狗",
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1715529599
},
shopTag = v4,
shopSort = 114,
jumpId = 175,
jumpText = "蔬菜精灵祈愿",
gender = 0,
itemIds = {
401640
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 184
},
[11134] = {
commodityId = 11134,
commodityName = "莲藕狐",
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1715529599
},
shopTag = v4,
shopSort = 115,
jumpId = 176,
jumpText = "蔬菜精灵祈愿",
gender = 0,
itemIds = {
401650
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 185
},
[11135] = {
commodityId = 11135,
commodityName = "旅星者",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v4,
shopSort = 2,
jumpId = 189,
jumpText = v8,
gender = 0,
itemIds = {
401660
},
bOpenSuit = true,
suitId = 147
},
[11136] = {
commodityId = 11136,
commodityName = "旅星者",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401661
}
},
[11137] = {
commodityId = 11137,
commodityName = "水瓶星",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1713974400
},
shopTag = v4,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
itemIds = {
401670
},
bOpenSuit = true,
suitId = 175
},
[11138] = {
commodityId = 11138,
commodityName = "水瓶星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401671
}
},
[11139] = {
commodityId = 11139,
commodityName = "时秒秒",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v4,
shopSort = 2,
jumpId = 189,
jumpText = v8,
gender = 0,
itemIds = {
401680
},
bOpenSuit = true,
suitId = 148
},
[11140] = {
commodityId = 11140,
commodityName = "时秒秒",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401681
}
},
[11141] = {
commodityId = 11141,
commodityName = "记忆修复师 赫曼",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v4,
jumpId = 180,
jumpText = v8,
gender = 0,
itemIds = {
401690
},
bOpenSuit = true,
suitId = 145
},
[11142] = {
commodityId = 11142,
commodityName = "记忆修复师 赫曼",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401691
}
},
[11143] = {
commodityId = 11143,
commodityName = "记忆修复师 赫曼",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401692
}
},
[11144] = {
commodityId = 11144,
commodityName = "秘密王牌 奇锋",
beginTime = {
seconds = 1716480000
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 2,
jumpId = 179,
jumpText = "前线装备库",
gender = 0,
minVersion = "1.2.100.65",
itemIds = {
401700
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 240
},
[11145] = {
commodityId = 11145,
commodityName = "秘密王牌 奇锋",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401701
}
},
[11146] = {
commodityId = 11146,
commodityName = "秘密王牌 奇锋",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401702
}
},
[11147] = {
commodityId = 11147,
commodityName = "突突仔",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401710
}
},
[11148] = {
commodityId = 11148,
commodityName = "突突仔",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401711
}
},
[11149] = {
commodityId = 11149,
commodityName = "沙小净",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401720
}
},
[11150] = {
commodityId = 11150,
commodityName = "沙小净",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401721
}
},
[11151] = {
commodityId = 11151,
commodityName = "观星者",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1726761599
},
shopTag = v4,
shopSort = 2,
jumpId = 185,
jumpText = "霜天冰雨",
gender = 0,
itemIds = {
401730
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 172
},
[11152] = {
commodityId = 11152,
commodityName = "观星者",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401731
}
},
[11153] = {
commodityId = 11153,
commodityName = "师傅",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopTag = v4,
shopSort = 94,
jumpId = 1048,
jumpText = "功夫熊猫返场",
gender = 0,
minVersion = "1.3.7.97",
itemIds = {
401740
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2450,
bOpenSuit = true,
suitId = 163
},
[11154] = {
commodityId = 11154,
commodityName = "悟小能",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401750
}
},
[11155] = {
commodityId = 11155,
commodityName = "悟小能",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401751
}
},
[11156] = {
commodityId = 11156,
commodityName = "蓝跳跳",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401760
}
},
[11157] = {
commodityId = 11157,
commodityName = "蓝跳跳",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401761
}
},
[11158] = {
commodityId = 11158,
commodityName = "潜修修",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401770
}
},
[11159] = {
commodityId = 11159,
commodityName = "潜修修",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401771
}
},
[11160] = {
commodityId = 11160,
commodityName = "符禄禄",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401772
}
},
[11161] = {
commodityId = 11161,
commodityName = "符禄禄",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401773
}
},
[11162] = {
commodityId = 11162,
commodityName = "小橘子",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v4,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.2.90.35",
itemIds = {
401780
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1920,
bOpenSuit = true,
suitId = 167
},
[11163] = {
commodityId = 11163,
commodityName = "梵小高",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401790
}
},
[11164] = {
commodityId = 11164,
commodityName = "梵小高",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401791
}
},
[11165] = {
commodityId = 11165,
commodityName = "糖果果",
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1740067199
},
shopTag = v4,
jumpId = 557,
jumpText = "遗落的珍宝",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
401800
},
bOpenSuit = true,
suitId = 173
},
[11166] = {
commodityId = 11166,
commodityName = "糖果果",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401801
}
},
[11167] = {
commodityId = 11167,
commodityName = "星光游侠 艾莉娜",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1726761599
},
shopTag = v4,
shopSort = 119,
jumpId = 185,
jumpText = "霜天冰雨",
gender = 0,
itemIds = {
401810
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 170
},
[11168] = {
commodityId = 11168,
commodityName = "星光游侠 艾莉娜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401811
}
},
[11169] = {
commodityId = 11169,
commodityName = "星光游侠 艾莉娜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401812
}
},
[11170] = {
commodityId = 11170,
commodityName = "皮皮火 泰比",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1726761599
},
shopTag = v4,
shopSort = 120,
jumpId = 185,
jumpText = "霜天冰雨",
gender = 0,
itemIds = {
401820
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 171
},
[11171] = {
commodityId = 11171,
commodityName = "皮皮火 泰比",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401821
}
},
[11172] = {
commodityId = 11172,
commodityName = "皮皮火 泰比",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401822
}
},
[11173] = {
commodityId = 11173,
commodityName = "奶盒盒",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401830
}
},
[11174] = {
commodityId = 11174,
commodityName = "奶盒盒",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401831
}
},
[11175] = {
commodityId = 11175,
commodityName = "辣子鸡",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401832
},
suitId = 343
},
[11176] = {
commodityId = 11176,
commodityName = "辣子鸡",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
401833
}
},
[11177] = {
commodityId = 11177,
commodityName = "兔星星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
401840
},
suitId = 215
},
[11178] = {
commodityId = 11178,
commodityName = "兔星星",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401841
}
},
[11179] = {
commodityId = 11179,
commodityName = "兔星星",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
401842
}
},
[11180] = {
commodityId = 11180,
commodityName = "彩虹旋律 洁西卡",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 2,
jumpId = 180,
jumpText = v8,
gender = 0,
minVersion = v25,
itemIds = {
401850
},
bOpenSuit = true,
suitId = 191
},
[11181] = {
commodityId = 11181,
commodityName = "彩虹旋律 洁西卡",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v25,
itemIds = {
401851
}
},
[11182] = {
commodityId = 11182,
commodityName = "彩虹旋律 洁西卡",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v25,
itemIds = {
401852
}
},
[11183] = {
commodityId = 11183,
commodityName = "乐坛巨星  洁西卡",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 2,
jumpId = 180,
jumpText = v8,
gender = 0,
minVersion = v25,
itemIds = {
401860
},
bOpenSuit = true,
suitId = 192
},
[11184] = {
commodityId = 11184,
commodityName = "热血鼓手  安德鲁",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 104,
jumpId = 180,
jumpText = v8,
gender = 0,
minVersion = v25,
itemIds = {
401870
},
bOpenSuit = true,
suitId = 193
},
[11185] = {
commodityId = 11185,
commodityName = "热血鼓手  安德鲁",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v25,
itemIds = {
401871
}
},
[11186] = {
commodityId = 11186,
commodityName = "热血鼓手  安德鲁",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v25,
itemIds = {
401872
}
},
[11187] = {
commodityId = 11187,
commodityName = "低调键盘手 桃乐丝",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 105,
jumpId = 180,
jumpText = v8,
gender = 0,
minVersion = v25,
itemIds = {
401880
},
bOpenSuit = true,
suitId = 194
},
[11188] = {
commodityId = 11188,
commodityName = "低调键盘手 桃乐丝",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v25,
itemIds = {
401881
}
},
[11189] = {
commodityId = 11189,
commodityName = "低调键盘手 桃乐丝",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v25,
itemIds = {
401882
}
},
[11190] = {
commodityId = 11190,
commodityName = "乐小琴",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 2,
jumpId = 180,
jumpText = v8,
gender = 0,
minVersion = v25,
itemIds = {
401890
},
bOpenSuit = true,
suitId = 195
},
[11191] = {
commodityId = 11191,
commodityName = "乐小琴",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v25,
itemIds = {
401891
}
},
[11192] = {
commodityId = 11192,
commodityName = "乐小笛",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 2,
jumpId = 180,
jumpText = v8,
gender = 0,
minVersion = v25,
itemIds = {
401900
},
bOpenSuit = true,
suitId = 196
},
[11193] = {
commodityId = 11193,
commodityName = "乐小笛",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v25,
itemIds = {
401901
}
},
[11194] = {
commodityId = 11194,
commodityName = "元气练习生",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 2,
jumpId = 180,
jumpText = v8,
gender = 0,
minVersion = v25,
itemIds = {
401910
},
bOpenSuit = true,
suitId = 197
},
[11195] = {
commodityId = 11195,
commodityName = "元气练习生",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v25,
itemIds = {
401911
}
},
[11196] = {
commodityId = 11196,
commodityName = "歌剧少女",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "潮音通行证",
gender = 0,
minVersion = v25,
itemIds = {
401920
},
bOpenSuit = true,
suitId = 198
},
[11197] = {
commodityId = 11197,
commodityName = "歌剧少女",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v25,
itemIds = {
401921
}
},
[11198] = {
commodityId = 11198,
commodityName = "钢琴王子",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "潮音通行证",
gender = 0,
minVersion = v25,
itemIds = {
401930
},
bOpenSuit = true,
suitId = 199
},
[11199] = {
commodityId = 11199,
commodityName = "钢琴王子",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v25,
itemIds = {
401931
}
},
[11200] = {
commodityId = 11200,
commodityName = "双鱼星",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
minVersion = v25,
itemIds = {
401940
},
bOpenSuit = true,
suitId = 190
},
[11201] = {
commodityId = 11201,
commodityName = "双鱼星",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v25,
itemIds = {
401941
}
},
[11206] = {
commodityId = 11206,
commodityName = "柠檬气泡水",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v4,
shopSort = 98,
jumpId = 10602,
jumpText = "小甜豆",
gender = 0,
minVersion = "1.3.18.109",
itemIds = {
401950
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 217
},
[11207] = {
commodityId = 11207,
commodityName = "雨具",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v4,
shopSort = 99,
jumpId = 10603,
jumpText = "小甜豆",
gender = 0,
minVersion = "1.3.18.109",
itemIds = {
401960
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 218
},
[11208] = {
commodityId = 11208,
commodityName = "列车员",
beginTime = {
seconds = 1715270400
},
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
minVersion = v25,
itemIds = {
401970
},
bOpenSuit = true,
suitId = 226
},
[11209] = {
commodityId = 11209,
commodityName = "列车员",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1715270400
},
gender = 0,
minVersion = v25,
itemIds = {
401971
}
},
[11210] = {
commodityId = 11210,
commodityName = "小丸子",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopTag = v4,
shopSort = 100,
jumpId = 187,
jumpText = "小丸子便当屋",
gender = 0,
minVersion = v25,
itemIds = {
401980
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1100,
bOpenSuit = true,
suitId = 205
},
[11211] = {
commodityId = 11211,
commodityName = "小丸子时装",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopTag = v4,
shopSort = 101,
jumpId = 188,
jumpText = "盛装派对",
gender = 0,
minVersion = v25,
itemIds = {
401990
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 206
},
[11212] = {
commodityId = 11212,
commodityName = "花轮",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopTag = v4,
shopSort = 102,
jumpId = 188,
jumpText = "盛装派对",
gender = 0,
minVersion = v25,
itemIds = {
402000
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 207
},
[11213] = {
commodityId = 11213,
commodityName = "小玉",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopTag = v4,
shopSort = 103,
jumpId = 187,
jumpText = "小丸子便当屋",
gender = 0,
minVersion = v25,
itemIds = {
402010
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1100,
bOpenSuit = true,
suitId = 208
},
[11214] = {
commodityId = 11214,
commodityName = "铃铛指挥家 哆啦",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v4,
shopSort = 106,
jumpId = 9,
jumpText = "潮音通行证",
gender = 0,
minVersion = v25,
itemIds = {
402020
},
bOpenSuit = true,
suitId = 204
},
[11215] = {
commodityId = 11215,
commodityName = "铃铛指挥家 哆啦",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v25,
itemIds = {
402021
}
},
[11216] = {
commodityId = 11216,
commodityName = "铃铛指挥家 哆啦",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v25,
itemIds = {
402022
}
},
[11217] = {
commodityId = 11217,
commodityName = "名画少女",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v25,
itemIds = {
402030
}
},
[11218] = {
commodityId = 11218,
commodityName = "名画少女",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v25,
itemIds = {
402031
}
},
[11219] = {
commodityId = 11219,
commodityName = "绮莉莉",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v4,
shopSort = 2,
jumpId = 175,
jumpText = "永恒之誓",
gender = 0,
minVersion = v25,
itemIds = {
402040
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 229
},
[11220] = {
commodityId = 11220,
commodityName = "绮莉莉",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1715875200
},
gender = 0,
minVersion = v25,
itemIds = {
402041
}
},
[11221] = {
commodityId = 11221,
commodityName = "浪漫笛音 笛娜",
beginTime = {
seconds = 1740672000
},
endTime = v12,
shopTag = v4,
shopSort = 2,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
minVersion = v25,
itemIds = {
402050
},
canGift = true,
addIntimacy = 160,
giftCoinType = 205,
giftPrice = 180,
bOpenSuit = true,
suitId = 216
},
[11222] = {
commodityId = 11222,
commodityName = "浪漫笛音 笛娜",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v25,
itemIds = {
402051
}
},
[11223] = {
commodityId = 11223,
commodityName = "浪漫笛音 笛娜",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v25,
itemIds = {
402052
}
},
[11224] = {
commodityId = 11224,
commodityName = "翩翩才子",
coinType = 6,
price = 800,
beginTime = {
seconds = 1719504000
},
shopTag = v4,
gender = 0,
itemIds = {
402060
},
bOpenSuit = true,
suitId = 279
},
[11225] = {
commodityId = 11225,
commodityName = "及第星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402061
}
},
[11226] = {
commodityId = 11226,
commodityName = "萌星空乘",
beginTime = {
seconds = 1718899200
},
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
minVersion = v25,
itemIds = {
402070
},
bOpenSuit = true,
suitId = 274
},
[11227] = {
commodityId = 11227,
commodityName = "萌星空乘",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1718899200
},
gender = 0,
minVersion = v25,
itemIds = {
402071
}
},
[11228] = {
commodityId = 11228,
commodityName = "拳击少女",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402080
}
},
[11229] = {
commodityId = 11229,
commodityName = "拳击少女",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402081
}
},
[11230] = {
commodityId = 11230,
commodityName = "守护骑士 杰斯",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v4,
shopSort = 3,
jumpId = 175,
jumpText = "永恒之誓",
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402090
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
bOpenSuit = true,
suitId = 219
},
[11231] = {
commodityId = 11231,
commodityName = "守护骑士 杰斯",
coinType = 200006,
price = 80,
beginTime = {
seconds = 1715875200
},
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402091
}
},
[11232] = {
commodityId = 11232,
commodityName = "守护骑士 杰斯",
coinType = 200006,
price = 80,
beginTime = {
seconds = 1715875200
},
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402092
}
},
[11233] = {
commodityId = 11233,
commodityName = "花海守护者 莉莉安",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v4,
shopSort = 3,
jumpId = 175,
jumpText = "永恒之誓",
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402100
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
bOpenSuit = true,
suitId = 220
},
[11234] = {
commodityId = 11234,
commodityName = "花海守护者 莉莉安",
coinType = 200006,
price = 80,
beginTime = {
seconds = 1715875200
},
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402101
}
},
[11235] = {
commodityId = 11235,
commodityName = "花海守护者 莉莉安",
coinType = 200006,
price = 80,
beginTime = {
seconds = 1715875200
},
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402102
}
},
[11236] = {
commodityId = 11236,
commodityName = "绛天战甲 铠",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402110
},
suitId = 221
},
[11237] = {
commodityId = 11237,
commodityName = "挚爱之约 孙策",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402120
},
suitId = 222
},
[11238] = {
commodityId = 11238,
commodityName = "音你心动 小乔",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v4,
shopSort = 3,
jumpId = 10674,
jumpText = v9,
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
402130
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 223
},
[11239] = {
commodityId = 11239,
commodityName = "影龙天霄 兰陵王",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v4,
shopSort = 3,
jumpId = 10674,
jumpText = v9,
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
402140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 224
},
[11240] = {
commodityId = 11240,
commodityName = "飞燕仙 轻羽",
beginTime = {
seconds = 1750521600
},
endTime = {
seconds = 1752940799
},
shopTag = v4,
shopSort = 2,
jumpId = 1099,
jumpText = "轻羽仙子",
gender = 0,
minVersion = "1.3.88.155",
itemIds = {
402150
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 225
},
[11241] = {
commodityId = 11241,
commodityName = "飞燕仙 轻羽",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402151
}
},
[11242] = {
commodityId = 11242,
commodityName = "飞燕仙 轻羽",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402152
}
},
[11243] = {
commodityId = 11243,
commodityName = "犬系少年 阿柴",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v4,
shopSort = 2,
jumpId = 175,
jumpText = "永恒之誓",
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402160
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 227
},
[11244] = {
commodityId = 11244,
commodityName = "犬系少年 阿柴",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1715875200
},
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402161
}
},
[11245] = {
commodityId = 11245,
commodityName = "犬系少年 阿柴",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1715875200
},
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402162
}
},
[11246] = {
commodityId = 11246,
commodityName = "猫系少女 喵喵",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v4,
shopSort = 2,
jumpId = 175,
jumpText = "永恒之誓",
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402170
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 228
},
[11247] = {
commodityId = 11247,
commodityName = "猫系少女 喵喵",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1715875200
},
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402171
}
},
[11248] = {
commodityId = 11248,
commodityName = "猫系少女 喵喵",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1715875200
},
gender = 0,
minVersion = "1.2.100.46",
itemIds = {
402172
}
},
[11249] = {
commodityId = 11249,
commodityName = "枪火新星 闪电",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402180
},
suitId = 230
},
[11250] = {
commodityId = 11250,
commodityName = "枪火新星 闪电",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402181
}
},
[11251] = {
commodityId = 11251,
commodityName = "枪火新星 闪电",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402182
}
},
[11252] = {
commodityId = 11252,
commodityName = "云鹤仙  鸿鸣",
beginTime = {
seconds = 1750521600
},
endTime = {
seconds = 1752940799
},
shopTag = v4,
shopSort = 1,
jumpId = 1100,
jumpText = "月华鹤影",
gender = 0,
itemIds = {
402190
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 231
},
[11253] = {
commodityId = 11253,
commodityName = "云鹤仙  鸿鸣",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1750521600
},
gender = 0,
itemIds = {
402191
}
},
[11254] = {
commodityId = 11254,
commodityName = "云鹤仙  鸿鸣",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1750521600
},
gender = 0,
itemIds = {
402192
}
},
[11255] = {
commodityId = 11255,
commodityName = "双面天鹅 娜塔莉(黑）",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744905599
},
shopTag = v4,
shopSort = 2,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = v27,
itemIds = {
402200
},
canGift = true,
addIntimacy = 320,
giftCoinType = 1,
giftPrice = 3600,
bOpenSuit = true,
suitId = 232
},
[11256] = {
commodityId = 11256,
commodityName = "双面天鹅 娜塔莉(黑）",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402201
}
},
[11257] = {
commodityId = 11257,
commodityName = "双面天鹅 娜塔莉(黑）",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402202
}
},
[11258] = {
commodityId = 11258,
commodityName = "双面天鹅 娜塔莉(白)",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402210
}
},
[11259] = {
commodityId = 11259,
commodityName = "双面天鹅 娜塔莉(白)",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402211
}
},
[11260] = {
commodityId = 11260,
commodityName = "双面天鹅 娜塔莉(白)",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402212
}
},
[11261] = {
commodityId = 11261,
commodityName = "齐天大圣",
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1727020799
},
shopTag = v4,
shopSort = 2,
jumpId = 8001,
jumpText = "西行之路祈愿",
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
402220
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 234
},
[11262] = {
commodityId = 11262,
commodityName = "齐天大圣",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
402221
}
},
[11263] = {
commodityId = 11263,
commodityName = "齐天大圣",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
402222
}
},
[11264] = {
commodityId = 11264,
commodityName = "木伊伊",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v4,
shopSort = 99,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.12.70",
itemIds = {
402230
},
bOpenSuit = true,
suitId = 235
},
[11265] = {
commodityId = 11265,
commodityName = "木伊伊",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402231
}
},
[11266] = {
commodityId = 11266,
commodityName = "白羊星",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721232000
},
shopTag = v4,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
itemIds = {
402240
},
bOpenSuit = true,
suitId = 236
},
[11267] = {
commodityId = 11267,
commodityName = "白羊星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402241
}
},
[11268] = {
commodityId = 11268,
commodityName = "假日海滩",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402250
},
suitId = 237
},
[11269] = {
commodityId = 11269,
commodityName = "假日海滩",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402251
}
},
[11270] = {
commodityId = 11270,
commodityName = "茶沫沫",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402260
},
suitId = 243
},
[11271] = {
commodityId = 11271,
commodityName = "茶沫沫",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402261
}
},
[11272] = {
commodityId = 11272,
commodityName = "虎鲸男孩",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
minVersion = v27,
itemIds = {
402270
},
bOpenSuit = true,
suitId = 244
},
[11273] = {
commodityId = 11273,
commodityName = "虎鲸男孩",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v27,
itemIds = {
402271
}
},
[11274] = {
commodityId = 11274,
commodityName = "虎鲸男孩",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v27,
itemIds = {
402272
}
},
[11275] = {
commodityId = 11275,
commodityName = "珍珠少女",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
minVersion = v27,
itemIds = {
402280
},
bOpenSuit = true,
suitId = 245
},
[11276] = {
commodityId = 11276,
commodityName = "珍珠少女",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v27,
itemIds = {
402281
}
},
[11277] = {
commodityId = 11277,
commodityName = "珍珠少女",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v27,
itemIds = {
402282
}
},
[11278] = {
commodityId = 11278,
commodityName = "海螺姑娘",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = v27,
itemIds = {
402290
},
bOpenSuit = true,
suitId = 246
},
[11279] = {
commodityId = 11279,
commodityName = "海螺姑娘",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v27,
itemIds = {
402291
}
},
[11280] = {
commodityId = 11280,
commodityName = "海螺姑娘",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v27,
itemIds = {
402292
}
},
[11281] = {
commodityId = 11281,
commodityName = "海盗男",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
minVersion = v27,
itemIds = {
402300
},
bOpenSuit = true,
suitId = 247
},
[11282] = {
commodityId = 11282,
commodityName = "海盗男",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v27,
itemIds = {
402301
}
},
[11283] = {
commodityId = 11283,
commodityName = "鱼群图案服装",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
minVersion = v27,
itemIds = {
402310
},
bOpenSuit = true,
suitId = 248
},
[11284] = {
commodityId = 11284,
commodityName = "鱼群图案服装",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v27,
itemIds = {
402311
}
},
[11285] = {
commodityId = 11285,
commodityName = "草裙女",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
minVersion = v27,
itemIds = {
402320
},
bOpenSuit = true,
suitId = 249
},
[11286] = {
commodityId = 11286,
commodityName = "草裙女",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v27,
itemIds = {
402321
}
},
[11287] = {
commodityId = 11287,
commodityName = "章鱼娘",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = v27,
itemIds = {
402330
},
bOpenSuit = true,
suitId = 250
},
[11288] = {
commodityId = 11288,
commodityName = "章鱼娘",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v27,
itemIds = {
402331
}
},
[11289] = {
commodityId = 11289,
commodityName = "小丑鱼男装",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = v27,
itemIds = {
402340
},
bOpenSuit = true,
suitId = 251
},
[11290] = {
commodityId = 11290,
commodityName = "小丑鱼男装",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v27,
itemIds = {
402341
}
},
[11291] = {
commodityId = 11291,
commodityName = "星牛仔",
beginTime = {
seconds = 1722528000
},
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
minVersion = v28,
itemIds = {
402350
},
bOpenSuit = true,
suitId = 252
},
[11292] = {
commodityId = 11292,
commodityName = "星牛仔",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1722528000
},
gender = 0,
minVersion = v28,
itemIds = {
402351
}
},
[11293] = {
commodityId = 11293,
commodityName = "星世界地图鉴赏家",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v27,
itemIds = {
402360
},
suitId = 253
},
[11294] = {
commodityId = 11294,
commodityName = "星世界地图鉴赏家",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v27,
itemIds = {
402361
}
},
[11295] = {
commodityId = 11295,
commodityName = "美人鱼",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
minVersion = v27,
itemIds = {
402370
},
bOpenSuit = true,
suitId = 254
},
[11296] = {
commodityId = 11296,
commodityName = "美人鱼",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v27,
itemIds = {
402371
}
},
[11297] = {
commodityId = 11297,
commodityName = "美人鱼",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v27,
itemIds = {
402372
}
},
[11298] = {
commodityId = 11298,
commodityName = "人鱼王子",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v4,
shopSort = 2,
jumpId = 11,
jumpText = v8,
gender = 0,
minVersion = v27,
itemIds = {
402380
},
bOpenSuit = true,
suitId = 255
},
[11299] = {
commodityId = 11299,
commodityName = "鼠朵朵",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v27,
itemIds = {
402390
},
suitId = 256
},
[11300] = {
commodityId = 11300,
commodityName = "布朗熊",
beginTime = {
seconds = 1718294400
},
endTime = {
seconds = 1720108799
},
shopTag = v4,
shopSort = 2,
jumpId = 601,
jumpText = "时光小船",
gender = 0,
minVersion = v27,
itemIds = {
402400
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 257
},
[11301] = {
commodityId = 11301,
commodityName = "可妮兔",
beginTime = {
seconds = 1718294400
},
endTime = {
seconds = 1720108799
},
shopTag = v4,
shopSort = 2,
jumpId = 602,
jumpText = "时光小船",
gender = 0,
minVersion = v27,
itemIds = {
402410
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 258
},
[11302] = {
commodityId = 11302,
commodityName = "Hello Kitty",
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1738425599
},
shopTag = v4,
shopSort = 2,
jumpId = 1073,
jumpText = "三丽鸥家族",
gender = 0,
minVersion = "1.3.7.97",
itemIds = {
402420
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 259
},
[11303] = {
commodityId = 11303,
commodityName = "三丽鸥",
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1738425599
},
shopTag = v4,
shopSort = 2,
jumpId = 1073,
jumpText = "三丽鸥家族",
gender = 0,
minVersion = "1.3.7.97",
itemIds = {
402430
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 260
},
[11304] = {
commodityId = 11304,
commodityName = "庆典小子 嘉嘉",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402440
},
suitId = 261
},
[11305] = {
commodityId = 11305,
commodityName = "庆典小子 嘉嘉",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402441
}
},
[11306] = {
commodityId = 11306,
commodityName = "庆典小子 嘉嘉",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402442
}
},
[11307] = {
commodityId = 11307,
commodityName = "红孩儿",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v4,
shopSort = 100,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
402450
},
suitId = 262
},
[11308] = {
commodityId = 11308,
commodityName = "红孩儿",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402451
}
},
[11309] = {
commodityId = 11309,
commodityName = "耀战神 达达尼亚",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v4,
shopSort = 3,
jumpId = 8888,
jumpText = "天启圣谕",
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
402460
},
bOpenSuit = true,
suitId = 263
},
[11310] = {
commodityId = 11310,
commodityName = "焰战神  波尔托斯",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v4,
shopSort = 3,
jumpId = 8888,
jumpText = "天启圣谕",
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
402470
},
bOpenSuit = true,
suitId = 264
},
[11311] = {
commodityId = 11311,
commodityName = "灭战神 阿多斯",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v4,
shopSort = 3,
jumpId = 8888,
jumpText = "天启圣谕",
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
402480
},
bOpenSuit = true,
suitId = 265
},
[11312] = {
commodityId = 11312,
commodityName = "糖豆骑士",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402490
},
suitId = 266
},
[11313] = {
commodityId = 11313,
commodityName = "薯宝",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402500
},
suitId = 267
},
[11314] = {
commodityId = 11314,
commodityName = "糖豆人",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402510
},
suitId = 268
},
[11315] = {
commodityId = 11315,
commodityName = "巨蟹星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = "1.3.6.1",
itemIds = {
402520
},
suitId = 269
},
[11316] = {
commodityId = 11316,
commodityName = "巨蟹星",
coinType = 200006,
price = 12,
gender = 0,
minVersion = "1.3.6.1",
itemIds = {
402521
}
},
[11317] = {
commodityId = 11317,
commodityName = "清柠柠",
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1734623999
},
shopTag = v4,
jumpId = 800,
jumpText = "战神颂歌",
gender = 0,
minVersion = "1.3.7.53",
itemIds = {
402530
},
bOpenSuit = true,
suitId = 270
},
[11318] = {
commodityId = 11318,
commodityName = "清柠柠",
coinType = 200006,
price = 12,
beginTime = v0,
gender = 0,
minVersion = "1.3.7.53",
itemIds = {
402531
}
},
[11319] = {
commodityId = 11319,
commodityName = "七彩梦羽 妮可",
beginTime = {
seconds = 1730476800
},
endTime = {
seconds = 1730649599
},
shopTag = v4,
jumpId = 629,
jumpText = "周末幸运星",
gender = 0,
itemIds = {
402540
},
suitId = 271
},
[11320] = {
commodityId = 11320,
commodityName = "七彩梦羽 妮可",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402541
}
},
[11321] = {
commodityId = 11321,
commodityName = "七彩梦羽 妮可",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402542
}
},
[11322] = {
commodityId = 11322,
commodityName = "战无双",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1720713599
},
shopTag = v4,
jumpId = 25,
jumpText = "特惠礼包",
gender = 0,
minVersion = "1.3.7.75",
itemIds = {
402550
},
bOpenSuit = true,
suitId = 272
},
[11323] = {
commodityId = 11323,
commodityName = "兽盔盔",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402560
},
suitId = 273
},
[11324] = {
commodityId = 11324,
commodityName = "寻梦冒险家 米萝",
beginTime = {
seconds = 1720108800
},
endTime = {
seconds = 1723737599
},
shopTag = v4,
jumpId = 702,
jumpText = "寻梦之旅",
gender = 0,
minVersion = "1.3.7.91",
itemIds = {
402570
},
bOpenSuit = true,
suitId = 276
},
[11325] = {
commodityId = 11325,
commodityName = "梁小蝶",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402580
},
suitId = 277
},
[11326] = {
commodityId = 11326,
commodityName = "梁小蝶",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402581
}
},
[11327] = {
commodityId = 11327,
commodityName = "祝小蝶",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402590
},
suitId = 278
},
[11328] = {
commodityId = 11328,
commodityName = "祝小蝶",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402591
}
},
[11329] = {
commodityId = 11329,
commodityName = "大气探险家",
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1740067199
},
shopTag = v4,
jumpId = 557,
jumpText = "遗落的珍宝",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
400250
},
bOpenSuit = true,
suitId = 281
},
[11330] = {
commodityId = 11330,
commodityName = "大气探险家",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
400251
}
},
[11331] = {
commodityId = 11331,
commodityName = "橙小星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402600
},
suitId = 282
},
[11332] = {
commodityId = 11332,
commodityName = "小画家耶 ",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402610
},
suitId = 283
},
[11335] = {
commodityId = 11335,
commodityName = "星灿灿",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v4,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
402640
},
bOpenSuit = true,
suitId = 286
},
[11336] = {
commodityId = 11336,
commodityName = "星灿灿",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402641
}
},
[11337] = {
commodityId = 11337,
commodityName = "月缘缘",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v4,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
402650
},
bOpenSuit = true,
suitId = 287
},
[11338] = {
commodityId = 11338,
commodityName = "月缘缘",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402651
}
},
[11333] = {
commodityId = 11333,
commodityName = "太医 温实初",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402620
},
suitId = 284
},
[11334] = {
commodityId = 11334,
commodityName = "明星小电视",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402630
},
suitId = 285
},
[11339] = {
commodityId = 11339,
commodityName = "金牛星",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724860800
},
shopTag = v4,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
itemIds = {
402660
},
bOpenSuit = true,
suitId = 288
},
[11340] = {
commodityId = 11340,
commodityName = "金牛星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402661
}
},
[11341] = {
commodityId = 11341,
commodityName = "狮子星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402670
},
suitId = 289
},
[11342] = {
commodityId = 11342,
commodityName = "狮子星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402671
}
},
[11343] = {
commodityId = 11343,
commodityName = "游园男孩",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v4,
shopSort = 2,
jumpId = 706,
jumpText = v8,
gender = 0,
minVersion = v28,
itemIds = {
402680
},
bOpenSuit = true,
suitId = 290
},
[11344] = {
commodityId = 11344,
commodityName = "游园男孩",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v28,
itemIds = {
402681
}
},
[11345] = {
commodityId = 11345,
commodityName = "游园女孩",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v4,
shopSort = 2,
jumpId = 706,
jumpText = v8,
gender = 0,
minVersion = v28,
itemIds = {
402690
},
bOpenSuit = true,
suitId = 291
},
[11346] = {
commodityId = 11346,
commodityName = "游园女孩",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v28,
itemIds = {
402691
}
},
[11347] = {
commodityId = 11347,
commodityName = "爱邮邮",
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 2,
jumpId = 610,
jumpText = "夏夜绮梦",
gender = 0,
minVersion = v29,
itemIds = {
402700
},
bOpenSuit = true,
suitId = 292
},
[11348] = {
commodityId = 11348,
commodityName = "爱邮邮",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v29,
itemIds = {
402701
}
},
[11349] = {
commodityId = 11349,
commodityName = "小恶魔女孩",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402710
},
suitId = 293
},
[11350] = {
commodityId = 11350,
commodityName = "小恶魔女孩",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402711
}
},
[11351] = {
commodityId = 11351,
commodityName = "马戏团帐篷拟人",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "乐园通行证",
gender = 0,
minVersion = v28,
itemIds = {
402720
},
bOpenSuit = true,
suitId = 294
},
[11352] = {
commodityId = 11352,
commodityName = "马戏团帐篷拟人",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v28,
itemIds = {
402721
}
},
[11353] = {
commodityId = 11353,
commodityName = "熹妃 钮祜禄·甄嬛",
beginTime = {
seconds = 1720713600
},
endTime = {
seconds = 1723737599
},
shopTag = v4,
jumpId = 801,
jumpText = "甄嬛传祈愿",
gender = 0,
itemIds = {
402730
},
bOpenSuit = true,
suitId = 295
},
[11354] = {
commodityId = 11354,
commodityName = "皇帝",
beginTime = {
seconds = 1720713600
},
endTime = {
seconds = 1723737599
},
shopTag = v4,
jumpId = 801,
jumpText = "甄嬛传祈愿",
gender = 0,
itemIds = {
402740
},
bOpenSuit = true,
suitId = 296
},
[11355] = {
commodityId = 11355,
commodityName = "华妃 年世兰",
beginTime = {
seconds = 1720713600
},
endTime = {
seconds = 1723737599
},
shopTag = v4,
jumpId = 801,
jumpText = "甄嬛传祈愿",
gender = 0,
itemIds = {
402750
},
bOpenSuit = true,
suitId = 297
},
[11356] = {
commodityId = 11356,
commodityName = "菠菠冰",
coinType = 6,
price = 1000000,
beginTime = {
seconds = 1702483200
},
endTime = {
seconds = 1703951999
},
shopTag = v4,
shopSort = 1,
gender = 0,
minVersion = "1.3.37.27",
itemIds = {
402760
},
suitId = 298
},
[11357] = {
commodityId = 11357,
commodityName = "菠菠冰",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402761
}
},
[11358] = {
commodityId = 11358,
commodityName = "可可豆",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v4,
shopSort = 1,
jumpId = 503,
jumpText = "冰雪圆舞曲",
gender = 0,
minVersion = "1.3.37.68",
itemIds = {
402770
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
suitId = 299
},
[11359] = {
commodityId = 11359,
commodityName = "可可豆",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402771
}
},
[11360] = {
commodityId = 11360,
commodityName = "壶小小",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v4,
shopSort = 2,
jumpId = 706,
jumpText = v8,
gender = 0,
minVersion = v28,
itemIds = {
402780
},
bOpenSuit = true,
suitId = 300
},
[11361] = {
commodityId = 11361,
commodityName = "壶小小",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v28,
itemIds = {
402781
}
},
[11362] = {
commodityId = 11362,
commodityName = "星票票",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "乐园通行证",
gender = 0,
minVersion = v28,
itemIds = {
402790
},
bOpenSuit = true,
suitId = 301
},
[11363] = {
commodityId = 11363,
commodityName = "星票票",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v28,
itemIds = {
402791
}
},
[11364] = {
commodityId = 11364,
commodityName = "狡狡龙",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v4,
jumpId = 657,
jumpText = "购物有礼",
gender = 0,
minVersion = "1.3.78.72",
itemIds = {
402800
},
suitId = 302
},
[11365] = {
commodityId = 11365,
commodityName = "狡狡龙",
coinType = 200006,
price = 12,
gender = 0,
minVersion = "1.3.78.72",
itemIds = {
402801
}
},
[11366] = {
commodityId = 11366,
commodityName = "糖果马戏班",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402810
},
suitId = 303
},
[11367] = {
commodityId = 11367,
commodityName = "糖果马戏班",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402811
}
},
[11368] = {
commodityId = 11368,
commodityName = "杂技小猴",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
402820
},
suitId = 304
},
[11369] = {
commodityId = 11369,
commodityName = "杂技小猴",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402821
}
},
[11370] = {
commodityId = 11370,
commodityName = "甜心公主 萝茜",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v4,
shopSort = 2,
jumpId = 706,
jumpText = v8,
gender = 0,
minVersion = v28,
itemIds = {
402830
},
bOpenSuit = true,
suitId = 305
},
[11371] = {
commodityId = 11371,
commodityName = "甜心公主 萝茜",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v28,
itemIds = {
402831
}
},
[11372] = {
commodityId = 11372,
commodityName = "甜心公主 萝茜",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v28,
itemIds = {
402832
}
},
[11373] = {
commodityId = 11373,
commodityName = "甜梦公主 莱拉",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v4,
shopSort = 2,
jumpId = 706,
jumpText = v8,
gender = 0,
minVersion = v28,
itemIds = {
402840
},
bOpenSuit = true,
suitId = 306
},
[11374] = {
commodityId = 11374,
commodityName = "悠悠云 克劳德",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v4,
shopSort = 2,
jumpId = 706,
jumpText = v8,
gender = 0,
minVersion = v28,
itemIds = {
402850
},
bOpenSuit = true,
suitId = 307
},
[11375] = {
commodityId = 11375,
commodityName = "悠悠云 克劳德",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v28,
itemIds = {
402851
}
},
[11376] = {
commodityId = 11376,
commodityName = "悠悠云 克劳德",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v28,
itemIds = {
402852
}
},
[11377] = {
commodityId = 11377,
commodityName = "水云仙 雨荷",
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
shopTag = v4,
shopSort = 2,
jumpId = 1084,
jumpText = "雨荷仙子",
gender = 0,
minVersion = "1.3.7.122",
itemIds = {
402860
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 308
},
[11378] = {
commodityId = 11378,
commodityName = "水云仙 雨荷",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v28,
itemIds = {
402861
}
},
[11379] = {
commodityId = 11379,
commodityName = "水云仙 雨荷",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v28,
itemIds = {
402862
}
},
[11380] = {
commodityId = 11380,
commodityName = "chiikawa-吉伊",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v28,
itemIds = {
402870
},
suitId = 309
},
[11381] = {
commodityId = 11381,
commodityName = "chiikawa-小八",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v28,
itemIds = {
402880
},
suitId = 310
},
[11382] = {
commodityId = 11382,
commodityName = "chiikawa-乌萨奇",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v28,
itemIds = {
402890
},
suitId = 311
},
[11383] = {
commodityId = 11383,
commodityName = "魔术兔 拉比娜",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v4,
shopSort = 2,
jumpId = 706,
jumpText = v8,
gender = 0,
minVersion = v28,
itemIds = {
402900
},
bOpenSuit = true,
suitId = 312
},
[11384] = {
commodityId = 11384,
commodityName = "魔术兔 拉比娜",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v28,
itemIds = {
402901
}
},
[11385] = {
commodityId = 11385,
commodityName = "魔术兔 拉比娜",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v28,
itemIds = {
402902
}
},
[11386] = {
commodityId = 11386,
commodityName = "吟游诗人 奥菲斯",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v4,
shopSort = 2,
jumpId = 9,
jumpText = "乐园通行证",
gender = 0,
minVersion = v28,
itemIds = {
402910
},
bOpenSuit = true,
suitId = 313
},
[11387] = {
commodityId = 11387,
commodityName = "吟游诗人 奥菲斯",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v28,
itemIds = {
402911
}
},
[11388] = {
commodityId = 11388,
commodityName = "吟游诗人 奥菲斯",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v28,
itemIds = {
402912
}
},
[11389] = {
commodityId = 11389,
commodityName = "象飞飞",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = v28,
itemIds = {
402920
},
suitId = 314
},
[11390] = {
commodityId = 11390,
commodityName = "粉呱呱",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
jumpId = 5033,
jumpText = "冲段挑战",
gender = 0,
minVersion = v28,
itemIds = {
402930
},
bOpenSuit = true,
suitId = 315
},
[11391] = {
commodityId = 11391,
commodityName = "草莓味 Toby",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v4,
shopSort = 2,
jumpId = 1089,
jumpText = "夏日派对",
gender = 0,
minVersion = "1.3.88.53",
itemIds = {
402940
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 321
},
[11392] = {
commodityId = 11392,
commodityName = "草莓味 Toby",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402941
}
},
[11393] = {
commodityId = 11393,
commodityName = "草莓味 Toby",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402942
}
},
[11394] = {
commodityId = 11394,
commodityName = "小青瓜",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v4,
shopSort = 2,
jumpId = 1090,
jumpText = "夏日派对",
gender = 0,
minVersion = "1.3.88.53",
itemIds = {
402950
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 322
},
[11395] = {
commodityId = 11395,
commodityName = "小青瓜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402951
}
},
[11396] = {
commodityId = 11396,
commodityName = "小青瓜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
402952
}
},
[11401] = {
commodityId = 11401,
commodityName = "西瓜奈奈",
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1740067199
},
shopTag = v4,
jumpId = 557,
jumpText = "遗落的珍宝",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
402980
},
bOpenSuit = true,
suitId = 325
},
[11402] = {
commodityId = 11402,
commodityName = "西瓜奈奈",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
402981
}
},
[11403] = {
commodityId = 11403,
commodityName = "龙胆 赵云",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v4,
shopSort = 1,
jumpId = 10674,
jumpText = v9,
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
402990
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 326
},
[11404] = {
commodityId = 11404,
commodityName = "异界灵契 孙尚香",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403000
},
suitId = 327
},
[11405] = {
commodityId = 11405,
commodityName = "追逃游戏 安琪拉",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v4,
shopSort = 1,
jumpId = 10674,
jumpText = v9,
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
403010
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 328
},
[11406] = {
commodityId = 11406,
commodityName = "全息碎影 孙悟空",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403020
},
suitId = 329
},
[11407] = {
commodityId = 11407,
commodityName = "花朝如约 蔡文姬",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403030
},
suitId = 330
},
[11408] = {
commodityId = 11408,
commodityName = "半神之弓 后羿",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403040
},
suitId = 331
},
[11409] = {
commodityId = 11409,
commodityName = "圣骑之力 亚瑟",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403050
},
suitId = 332
},
[11410] = {
commodityId = 11410,
commodityName = "阳小羽",
coinType = 6,
price = 800,
beginTime = {
seconds = 1723737600
},
shopTag = v4,
gender = 0,
itemIds = {
403060
},
bOpenSuit = true,
suitId = 333
},
[11411] = {
commodityId = 11411,
commodityName = "阳小羽",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1723737600
},
gender = 0,
itemIds = {
403061
}
},
[11412] = {
commodityId = 11412,
commodityName = "倾绝之舞 莎希莉",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 2,
jumpId = 809,
jumpText = "凤求凰祈愿",
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403070
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 334
},
[11413] = {
commodityId = 11413,
commodityName = "倾绝之舞 莎希莉",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403071
}
},
[11414] = {
commodityId = 11414,
commodityName = "倾绝之舞 莎希莉",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403072
}
},
[11415] = {
commodityId = 11415,
commodityName = "星佑之音 巴兰",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 2,
jumpId = 809,
jumpText = "凤求凰祈愿",
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403080
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 335
},
[11416] = {
commodityId = 11416,
commodityName = "星佑之音 巴兰",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403081
}
},
[11417] = {
commodityId = 11417,
commodityName = "星佑之音 巴兰",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403082
}
},
[11418] = {
commodityId = 11418,
commodityName = "虎小妞",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403090
},
suitId = 336
},
[11419] = {
commodityId = 11419,
commodityName = "虎小妞",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403091
}
},
[11420] = {
commodityId = 11420,
commodityName = "凤王 赤羽",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 2,
jumpId = 809,
jumpText = "凤求凰祈愿",
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403100
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
bOpenSuit = true,
suitId = 337
},
[11421] = {
commodityId = 11421,
commodityName = "凤王 赤羽",
coinType = 200006,
price = 80,
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403101
}
},
[11422] = {
commodityId = 11422,
commodityName = "凤王 赤羽",
coinType = 200006,
price = 80,
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403102
}
},
[11423] = {
commodityId = 11423,
commodityName = "凰后 丹翎",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 2,
jumpId = 809,
jumpText = "凤求凰祈愿",
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403110
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
bOpenSuit = true,
suitId = 338
},
[11424] = {
commodityId = 11424,
commodityName = "凰后 丹翎",
coinType = 200006,
price = 80,
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403112
}
},
[11425] = {
commodityId = 11425,
commodityName = "凰后 丹翎",
coinType = 200006,
price = 80,
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403111
}
},
[11426] = {
commodityId = 11426,
commodityName = "盼盼熊猫",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403120
},
suitId = 339
},
[11427] = {
commodityId = 11427,
commodityName = "缘芊芊",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 2,
jumpId = 809,
jumpText = "凤求凰祈愿",
gender = 0,
minVersion = "1.3.12.90",
itemIds = {
403130
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 340
},
[11428] = {
commodityId = 11428,
commodityName = "缘芊芊",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403311
}
},
[11429] = {
commodityId = 11429,
commodityName = "牛魔王",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v4,
shopSort = 100,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
403140
},
suitId = 341
},
[11430] = {
commodityId = 11430,
commodityName = "牛魔王",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403141
}
},
[11431] = {
commodityId = 11431,
commodityName = "铁扇公主",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v4,
shopSort = 100,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
403150
},
suitId = 342
},
[11432] = {
commodityId = 11432,
commodityName = "铁扇公主",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403151
}
},
[11445] = {
commodityId = 11445,
commodityName = "逸书公子",
beginTime = {
seconds = 1729785600
},
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
403220
},
suitId = 350
},
[11446] = {
commodityId = 11446,
commodityName = "逸书公子",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403221
}
},
[11447] = {
commodityId = 11447,
commodityName = "小丰收",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403230
},
suitId = 351
},
[11448] = {
commodityId = 11448,
commodityName = "小丰收",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403231
}
},
[11449] = {
commodityId = 11449,
commodityName = "包租婆",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403240
},
suitId = 352
},
[11450] = {
commodityId = 11450,
commodityName = "包租婆",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403241
}
},
[11451] = {
commodityId = 11451,
commodityName = "梦想家 欧林",
beginTime = {
seconds = 1740672000
},
endTime = v12,
shopTag = v4,
shopSort = 121,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
minVersion = "1.3.18.65",
itemIds = {
403250
},
canGift = true,
addIntimacy = 160,
giftCoinType = 205,
giftPrice = 180,
bOpenSuit = true,
suitId = 353
},
[11452] = {
commodityId = 11452,
commodityName = "梦想家 欧林",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403251
}
},
[11453] = {
commodityId = 11453,
commodityName = "梦想家 欧林",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403252
}
},
[11397] = {
commodityId = 11397,
commodityName = " LULU猪",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v4,
shopSort = 2,
jumpId = 1094,
jumpText = "夏日花园",
gender = 0,
minVersion = "1.3.88.53",
itemIds = {
402960
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 323
},
[11398] = {
commodityId = 11398,
commodityName = "金蝉子 唐三藏",
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1727020799
},
shopTag = v4,
shopSort = 2,
jumpId = 8001,
jumpText = "西行之路祈愿",
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
402970
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 324
},
[11399] = {
commodityId = 11399,
commodityName = "金蝉子 唐三藏",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
402971
}
},
[11400] = {
commodityId = 11400,
commodityName = "金蝉子 唐三藏",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
402972
}
},
[11433] = {
commodityId = 11433,
commodityName = "叶露露",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
403160
},
bOpenSuit = true,
suitId = 344
},
[11434] = {
commodityId = 11434,
commodityName = "叶露露",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403161
}
},
[11435] = {
commodityId = 11435,
commodityName = "晨小枫",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
shopSort = 1,
jumpId = 619,
jumpText = v8,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
403170
},
bOpenSuit = true,
suitId = 345
},
[11436] = {
commodityId = 11436,
commodityName = "晨小枫",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403171
}
},
[11437] = {
commodityId = 11437,
commodityName = "甜心果",
beginTime = {
seconds = 1729699200
},
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
403180
},
bOpenSuit = true,
suitId = 346
},
[11438] = {
commodityId = 11438,
commodityName = "甜心果",
coinType = 200006,
price = 12,
beginTime = {
seconds = 1729699200
},
gender = 0,
itemIds = {
403181
}
},
[11439] = {
commodityId = 11439,
commodityName = "林小泽",
beginTime = {
seconds = 1745856000
},
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
403190
},
bOpenSuit = true,
suitId = 347
},
[11440] = {
commodityId = 11440,
commodityName = "林小泽",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403191
}
},
[11441] = {
commodityId = 11441,
commodityName = "熊小豆",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
jumpId = 5033,
jumpText = "冲段挑战",
gender = 0,
itemIds = {
403200
},
bOpenSuit = true,
suitId = 348
},
[11442] = {
commodityId = 11442,
commodityName = "熊小豆",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403201
}
},
[11443] = {
commodityId = 11443,
commodityName = "菊小雅",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
shopSort = 1,
jumpId = 619,
jumpText = v8,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
403210
},
bOpenSuit = true,
suitId = 349
},
[11444] = {
commodityId = 11444,
commodityName = "菊小雅",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403211
}
},
[11454] = {
commodityId = 11454,
commodityName = "叶蔓蔓",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
shopSort = 1,
jumpId = 619,
jumpText = v8,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
403260
},
bOpenSuit = true,
suitId = 354
},
[11455] = {
commodityId = 11455,
commodityName = "叶蔓蔓",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403261
}
},
[11456] = {
commodityId = 11456,
commodityName = "佑天音",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403270
},
suitId = 355
},
[11457] = {
commodityId = 11457,
commodityName = "佑天音",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403271
}
},
[11458] = {
commodityId = 11458,
commodityName = "树墩墩",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
403280
},
bOpenSuit = true,
suitId = 356
},
[11459] = {
commodityId = 11459,
commodityName = "树墩墩",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403281
}
},
[11460] = {
commodityId = 11460,
commodityName = "睡衣小新",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v4,
shopSort = 1,
jumpId = 613,
jumpText = "百变小新",
gender = 0,
itemIds = {
403290
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 357
},
[11461] = {
commodityId = 11461,
commodityName = "正男",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v4,
shopSort = 1,
jumpId = 614,
jumpText = "向日葵小班",
gender = 0,
itemIds = {
403300
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1590,
bOpenSuit = true,
suitId = 358
},
[11462] = {
commodityId = 11462,
commodityName = "左卫门 小新",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v4,
shopSort = 1,
jumpId = 618,
jumpText = "百变小新",
gender = 0,
itemIds = {
403310
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 359
},
[11463] = {
commodityId = 11463,
commodityName = "天蓬元帅 猪悟能",
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1727020799
},
shopTag = v4,
shopSort = 2,
jumpId = 8001,
jumpText = "西行之路祈愿",
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
403320
},
bOpenSuit = true,
suitId = 360
},
[11464] = {
commodityId = 11464,
commodityName = "天蓬元帅 猪悟能",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
403321
}
},
[11465] = {
commodityId = 11465,
commodityName = "天蓬元帅 猪悟能",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.18.23",
itemIds = {
403322
}
},
[11466] = {
commodityId = 11466,
commodityName = "情绪测量师  欧柯塔",
beginTime = {
seconds = 1709740800
},
endTime = {
seconds = 1743695999
},
shopTag = v4,
shopSort = 2,
jumpId = 1079,
jumpText = "沙海寻踪",
gender = 0,
minVersion = "1.3.68.101",
itemIds = {
403330
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 361
},
[11467] = {
commodityId = 11467,
commodityName = "情绪测量师  欧柯塔",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403331
}
},
[11468] = {
commodityId = 11468,
commodityName = "情绪测量师  欧柯塔",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403332
}
},
[11469] = {
commodityId = 11469,
commodityName = "紫罗兰精灵 薇尔蕾",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
403340
},
bOpenSuit = true,
suitId = 362
},
[11470] = {
commodityId = 11470,
commodityName = "紫罗兰精灵 薇尔蕾",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403341
}
},
[11471] = {
commodityId = 11471,
commodityName = "紫罗兰精灵 薇尔蕾",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403342
}
},
[11472] = {
commodityId = 11472,
commodityName = "青莲剑仙 李白",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403350
},
suitId = 363
},
[11473] = {
commodityId = 11473,
commodityName = "灰灰月",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403360
},
suitId = 364
},
[11474] = {
commodityId = 11474,
commodityName = "灰灰月",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403361
}
},
[11475] = {
commodityId = 11475,
commodityName = "奔波儿灞",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403370
},
suitId = 365
},
[11476] = {
commodityId = 11476,
commodityName = "奔波儿灞",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403371
}
},
[11477] = {
commodityId = 11477,
commodityName = "天秤星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403380
},
suitId = 366
},
[11478] = {
commodityId = 11478,
commodityName = "天秤星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403381
}
},
[11479] = {
commodityId = 11479,
commodityName = "蔷薇精灵 路易斯",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
shopSort = 1,
jumpId = 619,
jumpText = v8,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
403390
},
bOpenSuit = true,
suitId = 367
},
[11480] = {
commodityId = 11480,
commodityName = "蔷薇精灵 路易斯",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403391
}
},
[11481] = {
commodityId = 11481,
commodityName = "蔷薇精灵 路易斯",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403392
}
},
[11482] = {
commodityId = 11482,
commodityName = "蓝百合精灵 莉莉",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
shopSort = 1,
jumpId = 619,
jumpText = v8,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
403400
},
bOpenSuit = true,
suitId = 368
},
[11483] = {
commodityId = 11483,
commodityName = "蓝百合精灵 莉莉",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403401
}
},
[11484] = {
commodityId = 11484,
commodityName = "蓝百合精灵 莉莉",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403402
}
},
[11485] = {
commodityId = 11485,
commodityName = "双子星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403410
},
suitId = 369
},
[11486] = {
commodityId = 11486,
commodityName = "双子星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403411
}
},
[11487] = {
commodityId = 11487,
commodityName = "鹿呦呦",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
minVersion = "1.3.6.1",
itemIds = {
403420
},
suitId = 370
},
[11488] = {
commodityId = 11488,
commodityName = "乐园奇幻夜 钟馗",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403430
},
suitId = 371
},
[11489] = {
commodityId = 11489,
commodityName = "花之语  伊笠丝",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
shopSort = 1,
jumpId = 619,
jumpText = v8,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
403440
},
bOpenSuit = true,
suitId = 372
},
[11490] = {
commodityId = 11490,
commodityName = "花之语  伊笠丝",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
403441
}
},
[11491] = {
commodityId = 11491,
commodityName = "花之语  伊笠丝",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
403442
}
},
[11492] = {
commodityId = 11492,
commodityName = "森之神 西尔维",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v4,
shopSort = 1,
jumpId = 619,
jumpText = v8,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
403450
},
bOpenSuit = true,
suitId = 373
},
[11493] = {
commodityId = 11493,
commodityName = "广寒仙子 嫦娥",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 363,
jumpText = "桂月清平",
gender = 0,
minVersion = "1.3.18.37",
itemIds = {
403460
},
canGift = true,
addIntimacy = 500,
giftCoinType = 203,
giftPrice = 320,
bOpenSuit = true,
suitId = 374
},
[11494] = {
commodityId = 11494,
commodityName = "广寒仙子 嫦娥",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
403461
}
},
[11495] = {
commodityId = 11495,
commodityName = "广寒仙子 嫦娥",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
403462
}
},
[11496] = {
commodityId = 11496,
commodityName = "落笔云烟 丹青",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403470
},
suitId = 375
},
[11497] = {
commodityId = 11497,
commodityName = "落笔云烟 丹青",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403471
}
},
[11498] = {
commodityId = 11498,
commodityName = "落笔云烟 丹青",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403472
}
},
[11499] = {
commodityId = 11499,
commodityName = "燎原之心 云缨",
beginTime = {
seconds = 1727798400
},
endTime = {
seconds = 1729612799
},
shopTag = v4,
shopSort = 1,
jumpId = 10685,
jumpText = "燎原之心",
gender = 0,
itemIds = {
403480
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 980,
suitId = 376
},
[11500] = {
commodityId = 11500,
commodityName = "舞龙少年 凌霄",
beginTime = {
seconds = 1735920000
},
endTime = {
seconds = 1737215999
},
shopTag = v4,
jumpId = 410,
jumpText = "拼团享好礼",
gender = 0,
itemIds = {
403490
},
bOpenSuit = true,
suitId = 377
},
[11501] = {
commodityId = 11501,
commodityName = "舞龙少年 凌霄",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1735920000
},
gender = 0,
itemIds = {
403491
}
},
[11502] = {
commodityId = 11502,
commodityName = "舞龙少年 凌霄",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1735920000
},
gender = 0,
itemIds = {
403492
}
},
[11503] = {
commodityId = 11503,
commodityName = "黄金·鼠鼠",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403500
},
suitId = 378
},
[11504] = {
commodityId = 11504,
commodityName = "桂月儿",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403510
},
suitId = 379
},
[11505] = {
commodityId = 11505,
commodityName = "桂月儿",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403511
}
},
[11506] = {
commodityId = 11506,
commodityName = "石烁烁",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403520
},
suitId = 380
},
[11507] = {
commodityId = 11507,
commodityName = "石烁烁",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403521
}
},
[11508] = {
commodityId = 11508,
commodityName = "凤果果",
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
jumpId = 8006,
jumpText = "千都三彩",
gender = 0,
minVersion = "1.3.18.72",
itemIds = {
403530
},
bOpenSuit = true,
suitId = 381
},
[11509] = {
commodityId = 11509,
commodityName = "凤果果",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403531
}
},
[11510] = {
commodityId = 11510,
commodityName = "汉堡贝贝",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403540
},
suitId = 382
},
[11511] = {
commodityId = 11511,
commodityName = "汉堡贝贝",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403541
}
},
[11512] = {
commodityId = 11512,
commodityName = "猎小鹰",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 363,
jumpText = "桂月清平",
gender = 0,
minVersion = "1.3.18.37",
itemIds = {
403550
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 383
},
[11513] = {
commodityId = 11513,
commodityName = "猎小鹰",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403551
}
},
[11514] = {
commodityId = 11514,
commodityName = "幻舞玲珑  公孙离",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403560
},
suitId = 384
},
[11515] = {
commodityId = 11515,
commodityName = "皓月使者 丹桂",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
jumpId = 363,
jumpText = "桂月清平",
gender = 0,
minVersion = "1.3.18.37",
itemIds = {
403570
},
bOpenSuit = true,
suitId = 385
},
[11516] = {
commodityId = 11516,
commodityName = "皓月使者 丹桂",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403571
}
},
[11517] = {
commodityId = 11517,
commodityName = "皓月使者 丹桂",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403572
}
},
[11518] = {
commodityId = 11518,
commodityName = "月宫灵兔 锦儿",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 363,
jumpText = "桂月清平",
gender = 0,
minVersion = "1.3.18.37",
itemIds = {
403580
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 386
},
[11519] = {
commodityId = 11519,
commodityName = "月宫灵兔 锦儿",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403581
}
},
[11520] = {
commodityId = 11520,
commodityName = "月宫灵兔 锦儿",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403582
}
},
[11521] = {
commodityId = 11521,
commodityName = "玻璃精灵 格莉丝",
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1740067199
},
shopTag = v4,
jumpId = 557,
jumpText = "遗落的珍宝",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
403590
},
bOpenSuit = true,
suitId = 387
},
[11522] = {
commodityId = 11522,
commodityName = "玻璃精灵 格莉丝",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403591
}
},
[11523] = {
commodityId = 11523,
commodityName = "玻璃精灵 格莉丝",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403592
}
},
[11524] = {
commodityId = 11524,
commodityName = "晨曦玫瑰 罗瑟琳",
beginTime = {
seconds = 1728662400
},
endTime = {
seconds = 1730476799
},
shopTag = v4,
shopSort = 2,
jumpId = 622,
jumpText = "晨曦玫瑰",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
403600
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 388
},
[11525] = {
commodityId = 11525,
commodityName = "晨曦玫瑰 罗瑟琳",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403601
}
},
[11526] = {
commodityId = 11526,
commodityName = "晨曦玫瑰 罗瑟琳",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403602
}
},
[11527] = {
commodityId = 11527,
commodityName = "黄金·小小象",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403610
},
suitId = 389
},
[11528] = {
commodityId = 11528,
commodityName = "企鹅小甜豆",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v4,
shopSort = 1,
jumpId = 10601,
jumpText = "小甜豆",
gender = 0,
minVersion = "1.3.18.109",
itemIds = {
403620
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 390
},
[11529] = {
commodityId = 11529,
commodityName = "绵羊小甜豆",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v4,
shopSort = 1,
jumpId = 10600,
jumpText = "小甜豆",
gender = 0,
minVersion = "1.3.18.109",
itemIds = {
403630
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 391
},
[11530] = {
commodityId = 11530,
commodityName = "偶像歌手 王昭君",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v4,
shopSort = 1,
jumpId = 10674,
jumpText = v9,
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
403640
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 395
},
[11531] = {
commodityId = 11531,
commodityName = "记忆之芯 公孙离",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v4,
shopSort = 1,
jumpId = 10674,
jumpText = v9,
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
403650
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 396
},
[11532] = {
commodityId = 11532,
commodityName = "三彩逸士 青云",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744905599
},
shopTag = v4,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.18.72",
itemIds = {
403660
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 10440,
bOpenSuit = true,
suitId = 397
},
[11533] = {
commodityId = 11533,
commodityName = "三彩逸士 青云",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
403661
}
},
[11534] = {
commodityId = 11534,
commodityName = "三彩逸士 青云",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
403662
}
},
[11535] = {
commodityId = 11535,
commodityName = "梦安安",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 623,
jumpText = v8,
gender = 0,
minVersion = "1.3.26.1",
itemIds = {
403670
},
bOpenSuit = true,
suitId = 398
},
[11536] = {
commodityId = 11536,
commodityName = "梦安安",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403671
}
},
[11537] = {
commodityId = 11537,
commodityName = "萌熊信使",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 623,
jumpText = v8,
gender = 0,
minVersion = "1.3.26.1",
itemIds = {
403680
},
bOpenSuit = true,
suitId = 399
},
[11538] = {
commodityId = 11538,
commodityName = "萌熊信使",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403681
}
},
[11539] = {
commodityId = 11539,
commodityName = "探乐乐",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.26.1",
itemIds = {
403690
},
bOpenSuit = true,
suitId = 500
},
[11540] = {
commodityId = 11540,
commodityName = "探乐乐",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403691
}
},
[11541] = {
commodityId = 11541,
commodityName = "南瓜苒苒",
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403700
},
suitId = 502
},
[11542] = {
commodityId = 11542,
commodityName = "南瓜苒苒",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403701
}
},
[11543] = {
commodityId = 11543,
commodityName = "安眠仔",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403710
},
suitId = 504
},
[11544] = {
commodityId = 11544,
commodityName = "安眠仔",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403711
}
},
[11545] = {
commodityId = 11545,
commodityName = "夜小寐",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403720
},
suitId = 506
},
[11546] = {
commodityId = 11546,
commodityName = "夜小寐",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403721
}
},
[11547] = {
commodityId = 11547,
commodityName = "天蝎星",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732723200
},
shopTag = v4,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
itemIds = {
403730
},
bOpenSuit = true,
suitId = 508
},
[11548] = {
commodityId = 11548,
commodityName = "天蝎星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403731
}
},
[11549] = {
commodityId = 11549,
commodityName = "筑梦师",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 623,
jumpText = v8,
gender = 0,
minVersion = "1.3.26.1",
itemIds = {
403740
},
bOpenSuit = true,
suitId = 510
},
[11550] = {
commodityId = 11550,
commodityName = "筑梦师",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403741
}
},
[11551] = {
commodityId = 11551,
commodityName = "星际秘盗",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.26.1",
itemIds = {
403750
},
bOpenSuit = true,
suitId = 512
},
[11552] = {
commodityId = 11552,
commodityName = "星际秘盗",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403751
}
},
[11553] = {
commodityId = 11553,
commodityName = "妮闪闪",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403760
},
suitId = 514
},
[11554] = {
commodityId = 11554,
commodityName = "妮闪闪",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403761
}
},
[11555] = {
commodityId = 11555,
commodityName = "食梦兽 芭库",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.26.1",
itemIds = {
403770
},
bOpenSuit = true,
suitId = 516
},
[11556] = {
commodityId = 11556,
commodityName = "食梦兽 芭库",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403771
}
},
[11557] = {
commodityId = 11557,
commodityName = "食梦兽 芭库",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403772
}
},
[11558] = {
commodityId = 11558,
commodityName = "冰雪之华  王昭君",
beginTime = {
seconds = 1750694400
},
endTime = {
seconds = 1752595199
},
shopTag = v4,
jumpId = 10713,
jumpText = "峡谷女明星",
gender = 0,
itemIds = {
403780
},
bOpenSuit = true,
suitId = 518
},
[11559] = {
commodityId = 11559,
commodityName = "吾皇猫",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v4,
shopSort = 2,
jumpId = 1092,
jumpText = "吾皇猫",
gender = 0,
minVersion = "1.3.88.53",
itemIds = {
403790
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 520
},
[11560] = {
commodityId = 11560,
commodityName = "巴扎黑",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v4,
shopSort = 2,
jumpId = 1093,
jumpText = "巴扎黑",
gender = 0,
minVersion = "1.3.88.53",
itemIds = {
403800
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 522
},
[11561] = {
commodityId = 11561,
commodityName = "竹韵隐侠 萌萌",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744905599
},
shopTag = v4,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.18.72",
itemIds = {
403810
},
bOpenSuit = true,
suitId = 524
},
[11562] = {
commodityId = 11562,
commodityName = "竹韵隐侠 萌萌",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403811
}
},
[11563] = {
commodityId = 11563,
commodityName = "竹韵隐侠 萌萌",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403812
}
},
[11564] = {
commodityId = 11564,
commodityName = "露水精灵 嘟嘟",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744905599
},
shopTag = v4,
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.18.72",
itemIds = {
403820
},
bOpenSuit = true,
suitId = 526
},
[11565] = {
commodityId = 11565,
commodityName = "露水精灵 嘟嘟",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403821
}
},
[11566] = {
commodityId = 11566,
commodityName = "露水精灵 嘟嘟",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403822
}
},
[11567] = {
commodityId = 11567,
commodityName = "蒸汽可可",
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1740067199
},
shopTag = v4,
jumpId = 557,
jumpText = "遗落的珍宝",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
403830
},
bOpenSuit = true,
suitId = 528
},
[11568] = {
commodityId = 11568,
commodityName = "蒸汽可可",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403831
}
},
[11569] = {
commodityId = 11569,
commodityName = "挚爱之约 孙策",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
402120
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 222
},
[11570] = {
commodityId = 11570,
commodityName = "音你心动 小乔",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 2,
jumpId = 10677,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
402130
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 223
},
[11571] = {
commodityId = 11571,
commodityName = "影龙天霄 兰陵王",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 2,
jumpId = 10677,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
402140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 224
},
[11572] = {
commodityId = 11572,
commodityName = "龙胆 赵云",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
402990
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 326
},
[11573] = {
commodityId = 11573,
commodityName = "异界灵契 孙尚香",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
403000
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 327
},
[11574] = {
commodityId = 11574,
commodityName = "追逃游戏 安琪拉",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
403010
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 328
},
[11575] = {
commodityId = 11575,
commodityName = "偶像歌手 王昭君",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
403640
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 395
},
[11576] = {
commodityId = 11576,
commodityName = "记忆之芯 公孙离",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v4,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
403650
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 396
},
[11577] = {
commodityId = 11577,
commodityName = "炼星术师 尼古拉斯",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 623,
jumpText = v8,
gender = 0,
minVersion = "1.3.26.1",
itemIds = {
403840
},
bOpenSuit = true,
suitId = 532
},
[11578] = {
commodityId = 11578,
commodityName = "炼星术师 尼古拉斯",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403841
}
},
[11579] = {
commodityId = 11579,
commodityName = "炼星术师 尼古拉斯",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403842
}
},
[11580] = {
commodityId = 11580,
commodityName = "万事屋屋主 怀特妮",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 623,
jumpText = v8,
gender = 0,
minVersion = "1.3.26.1",
itemIds = {
403850
},
bOpenSuit = true,
suitId = 534
},
[11581] = {
commodityId = 11581,
commodityName = "万事屋屋主 怀特妮",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403851
}
},
[11582] = {
commodityId = 11582,
commodityName = "万事屋屋主 怀特妮",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
403852
}
},
[11583] = {
commodityId = 11583,
commodityName = "大收藏家 柯莱荻",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 623,
jumpText = v8,
gender = 0,
minVersion = "1.3.26.1",
itemIds = {
403860
},
bOpenSuit = true,
suitId = 536
},
[11584] = {
commodityId = 11584,
commodityName = "大收藏家 柯莱荻",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
403861
}
},
[11585] = {
commodityId = 11585,
commodityName = "大收藏家 柯莱荻",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
403862
}
},
[11586] = {
commodityId = 11586,
commodityName = "星河织梦者 诗寇蒂",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v4,
shopSort = 1,
jumpId = 623,
jumpText = v8,
gender = 0,
minVersion = "1.3.26.1",
itemIds = {
403870
},
bOpenSuit = true,
suitId = 538
},
[11587] = {
commodityId = 11587,
commodityName = "处女星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403880
},
suitId = 540
},
[11588] = {
commodityId = 11588,
commodityName = "处女星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403881
}
},
[11589] = {
commodityId = 11589,
commodityName = "金属风暴 墨子",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403890
},
suitId = 542
},
[11590] = {
commodityId = 11590,
commodityName = "和平守望 墨子",
beginTime = {
seconds = 1729872000
},
endTime = {
seconds = 1731168000
},
shopTag = v4,
shopSort = 1,
jumpId = 10697,
jumpText = "和平守望",
gender = 0,
itemIds = {
403900
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 980,
suitId = 544
},
[11591] = {
commodityId = 11591,
commodityName = "魅力之狐 妲己",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403910
},
suitId = 546
},
[11592] = {
commodityId = 11592,
commodityName = "田小野",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403920
},
suitId = 548
},
[11593] = {
commodityId = 11593,
commodityName = "田小野",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403921
}
},
[11594] = {
commodityId = 11594,
commodityName = "小芒狗",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
403930
},
suitId = 550
},
[11595] = {
commodityId = 11595,
commodityName = "小芒狗",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
403931
}
},
[11596] = {
commodityId = 11596,
commodityName = "丁丁",
beginTime = {
seconds = 1750435200
},
endTime = {
seconds = 1752249599
},
shopTag = v4,
jumpId = 1098,
jumpText = "天线宝宝",
gender = 0,
minVersion = "1.3.88.155",
itemIds = {
403940
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1200,
suitId = 552
},
[11597] = {
commodityId = 11597,
commodityName = "迪西",
beginTime = {
seconds = 1750435200
},
endTime = {
seconds = 1752249599
},
shopTag = v4,
jumpId = 1098,
jumpText = "天线宝宝",
gender = 0,
minVersion = "1.3.88.155",
itemIds = {
403950
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1200,
suitId = 554
},
[11598] = {
commodityId = 11598,
commodityName = "拉拉",
beginTime = {
seconds = 1750435200
},
endTime = {
seconds = 1752249599
},
shopTag = v4,
jumpId = 1098,
jumpText = "天线宝宝",
gender = 0,
minVersion = "1.3.88.155",
itemIds = {
403960
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1200,
suitId = 556
},
[11599] = {
commodityId = 11599,
commodityName = "小波",
beginTime = {
seconds = 1750435200
},
endTime = {
seconds = 1752249599
},
shopTag = v4,
jumpId = 1098,
jumpText = "天线宝宝",
gender = 0,
minVersion = "1.3.88.155",
itemIds = {
403970
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1200,
suitId = 558
},
[11600] = {
commodityId = 11600,
commodityName = "开心超人",
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1732204799
},
shopTag = v4,
shopSort = 1,
jumpId = 381,
jumpText = "开心超人联盟",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
403980
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1590,
bOpenSuit = true,
suitId = 560
},
[11601] = {
commodityId = 11601,
commodityName = "甜心超人",
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1732204799
},
shopTag = v4,
shopSort = 1,
jumpId = 382,
jumpText = "开心超人联盟",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
403990
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1590,
bOpenSuit = true,
suitId = 562
},
[11602] = {
commodityId = 11602,
commodityName = "小心超人",
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1732204799
},
shopTag = v4,
shopSort = 1,
jumpId = 383,
jumpText = "开心超人联盟",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
404000
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1590,
bOpenSuit = true,
suitId = 564
},
[11603] = {
commodityId = 11603,
commodityName = "巴啦啦 游乐",
beginTime = {
seconds = 1731686400
},
endTime = {
seconds = 1734278399
},
shopTag = v4,
shopSort = 1,
jumpId = 8012,
jumpText = "星缘奇境",
gender = 0,
minVersion = "1.3.26.93",
itemIds = {
404010
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 566
},
[11604] = {
commodityId = 11604,
commodityName = "巴啦啦 小蓝",
beginTime = {
seconds = 1731686400
},
endTime = {
seconds = 1734278399
},
shopTag = v4,
shopSort = 1,
jumpId = 8011,
jumpText = "星缘奇境",
gender = 0,
minVersion = "1.3.26.93",
itemIds = {
404020
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 568
},
[11605] = {
commodityId = 11605,
commodityName = "星光乐师 阿栗雅",
beginTime = {
seconds = 1732291200
},
endTime = {
seconds = 1734019199
},
shopTag = v4,
jumpId = 458,
jumpText = "月半夜曲",
gender = 0,
minVersion = "1.3.26.93",
itemIds = {
404030
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 570
},
[11606] = {
commodityId = 11606,
commodityName = "星光乐师 阿栗雅",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1732291200
},
gender = 0,
minVersion = "1.3.26.93",
itemIds = {
404031
}
},
[11607] = {
commodityId = 11607,
commodityName = "星光乐师 阿栗雅",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1732291200
},
gender = 0,
minVersion = "1.3.26.93",
itemIds = {
404032
}
},
[11608] = {
commodityId = 11608,
commodityName = "小南瓜 卡芭莎",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v4,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404040
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 572
},
[11609] = {
commodityId = 11609,
commodityName = "小南瓜 卡芭莎",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404041
}
},
[11610] = {
commodityId = 11610,
commodityName = "小南瓜 卡芭莎",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404042
}
},
[11611] = {
commodityId = 11611,
commodityName = "园艺精灵 嘉丹",
beginTime = {
seconds = 1736524800
},
endTime = {
seconds = 1737820799
},
shopTag = v4,
jumpId = 535,
jumpText = "春日精灵",
gender = 0,
itemIds = {
404050
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 574
},
[11612] = {
commodityId = 11612,
commodityName = "园艺精灵 嘉丹",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1736524800
},
gender = 0,
itemIds = {
404051
}
},
[11613] = {
commodityId = 11613,
commodityName = "园艺精灵 嘉丹",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1736524800
},
gender = 0,
itemIds = {
404052
}
},
[11614] = {
commodityId = 11614,
commodityName = "糖果女巫 艾露温",
beginTime = {
seconds = 1740672000
},
endTime = v12,
shopTag = v4,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
minVersion = "1.3.26.34",
itemIds = {
404060
},
canGift = true,
addIntimacy = 160,
giftCoinType = 205,
giftPrice = 180,
bOpenSuit = true,
suitId = 576
},
[11615] = {
commodityId = 11615,
commodityName = "糖果女巫 艾露温",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1729872000
},
gender = 0,
itemIds = {
404061
}
},
[11616] = {
commodityId = 11616,
commodityName = "糖果女巫 艾露温",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1729872000
},
gender = 0,
itemIds = {
404062
}
},
[11617] = {
commodityId = 11617,
commodityName = "益点点",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404070
},
suitId = 578
},
[11618] = {
commodityId = 11618,
commodityName = "律小宝",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404080
},
suitId = 580
},
[11619] = {
commodityId = 11619,
commodityName = "律小宝",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404081
}
},
[11620] = {
commodityId = 11620,
commodityName = "小雪梨",
beginTime = {
seconds = 1734019200
},
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
404090
},
bOpenSuit = true,
suitId = 582
},
[11621] = {
commodityId = 11621,
commodityName = "小雪梨",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404091
}
},
[11622] = {
commodityId = 11622,
commodityName = "蒙娜丽莎",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404100
},
suitId = 584
},
[11623] = {
commodityId = 11623,
commodityName = "蒙娜丽莎",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404101
}
},
[11624] = {
commodityId = 11624,
commodityName = "永昼男爵 索林",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v4,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404110
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
bOpenSuit = true,
suitId = 586
},
[11625] = {
commodityId = 11625,
commodityName = "永昼男爵 索林",
coinType = 200006,
price = 80,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404111
}
},
[11626] = {
commodityId = 11626,
commodityName = "永昼男爵 索林",
coinType = 200006,
price = 80,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404112
}
},
[11627] = {
commodityId = 11627,
commodityName = "暮色皇女 伊美尔",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v4,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404120
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
bOpenSuit = true,
suitId = 588
},
[11628] = {
commodityId = 11628,
commodityName = "暮色皇女 伊美尔",
coinType = 200006,
price = 80,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404121
}
},
[11629] = {
commodityId = 11629,
commodityName = "暮色皇女 伊美尔",
coinType = 200006,
price = 80,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404122
}
},
[11630] = {
commodityId = 11630,
commodityName = "小幽灵 凡托姆",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v4,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404130
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 590
},
[11631] = {
commodityId = 11631,
commodityName = "小幽灵 凡托姆",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404131
}
},
[11632] = {
commodityId = 11632,
commodityName = "小幽灵 凡托姆",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404132
}
},
[11633] = {
commodityId = 11633,
commodityName = "全息碎影 孙悟空",
beginTime = v3,
endTime = {
seconds = 1732377599
},
shopTag = v4,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = v29,
itemIds = {
403020
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 329
},
[11634] = {
commodityId = 11634,
commodityName = "超时空战士 狄仁杰",
beginTime = v3,
endTime = {
seconds = 1732377599
},
shopTag = v4,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = v29,
itemIds = {
404140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 592
},
[11635] = {
commodityId = 11635,
commodityName = "挚爱之约 孙策",
beginTime = v3,
endTime = {
seconds = 1732377599
},
shopTag = v4,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = v29,
itemIds = {
402120
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 222
},
[11636] = {
commodityId = 11636,
commodityName = "音你心动 小乔",
beginTime = v3,
endTime = {
seconds = 1732377599
},
shopTag = v4,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = v29,
itemIds = {
402130
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 223
},
[11637] = {
commodityId = 11637,
commodityName = "影龙天霄 兰陵王",
beginTime = v3,
endTime = {
seconds = 1732377599
},
shopTag = v4,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = v29,
itemIds = {
402140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 224
},
[11638] = {
commodityId = 11638,
commodityName = "龙胆 赵云",
beginTime = v3,
endTime = {
seconds = 1732377599
},
shopTag = v4,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = v29,
itemIds = {
402990
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 326
},
[11639] = {
commodityId = 11639,
commodityName = "异界灵契 孙尚香",
beginTime = v3,
endTime = {
seconds = 1732377599
},
shopTag = v4,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = v29,
itemIds = {
403000
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 327
},
[11640] = {
commodityId = 11640,
commodityName = "追逃游戏 安琪拉",
beginTime = v3,
endTime = {
seconds = 1732377599
},
shopTag = v4,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = v29,
itemIds = {
403010
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 328
},
[11641] = {
commodityId = 11641,
commodityName = "偶像歌手 王昭君",
beginTime = v3,
endTime = {
seconds = 1732377599
},
shopTag = v4,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = v29,
itemIds = {
403640
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 395
},
[11642] = {
commodityId = 11642,
commodityName = "记忆之芯 公孙离",
beginTime = v3,
endTime = {
seconds = 1732377599
},
shopTag = v4,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = v29,
itemIds = {
403650
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 396
},
[11643] = {
commodityId = 11643,
commodityName = "超时空战士  狄仁杰",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404140
},
suitId = 592
},
[11644] = {
commodityId = 11644,
commodityName = "莓果果",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v4,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
gender = 0,
minVersion = "1.3.26.71",
itemIds = {
404150
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 594
},
[11645] = {
commodityId = 11645,
commodityName = "莓果果",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404151
}
},
[11646] = {
commodityId = 11646,
commodityName = "纸盒小怪兽",
beginTime = {
seconds = 1731081600
},
endTime = {
seconds = 1732291199
},
shopTag = v4,
jumpId = 1065,
jumpText = "超值礼包",
gender = 0,
minVersion = "1.3.26.81",
itemIds = {
404160
},
bOpenSuit = true,
suitId = 596
},
[11647] = {
commodityId = 11647,
commodityName = "纸盒小怪兽",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404161
}
},
[11648] = {
commodityId = 11648,
commodityName = "暗天使 尤利西斯",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v4,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
404170
},
canGift = true,
addIntimacy = 300,
giftCoinType = 213,
giftPrice = 1,
bOpenSuit = true,
suitId = 598
},
[11649] = {
commodityId = 11649,
commodityName = "暗天使 尤利西斯",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
404171
}
},
[11650] = {
commodityId = 11650,
commodityName = "暗天使 尤利西斯",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
404172
}
},
[11651] = {
commodityId = 11651,
commodityName = "雪果果",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404180
},
suitId = 600
},
[11652] = {
commodityId = 11652,
commodityName = "雪果果",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404181
}
},
[11653] = {
commodityId = 11653,
commodityName = "梦想改造家",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404190
},
suitId = 602
},
[11654] = {
commodityId = 11654,
commodityName = "梦想改造家",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404191
}
},
[11655] = {
commodityId = 11655,
commodityName = "烈焰先锋",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404200
},
suitId = 604
},
[11656] = {
commodityId = 11656,
commodityName = "东方曜",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404210
},
suitId = 606
},
[11657] = {
commodityId = 11657,
commodityName = "元包包",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404220
},
suitId = 608
},
[11658] = {
commodityId = 11658,
commodityName = "元包包",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404221
}
},
[11659] = {
commodityId = 11659,
commodityName = "姜饼小子",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 1,
jumpId = 630,
jumpText = v8,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
404230
},
bOpenSuit = true,
suitId = 610
},
[11660] = {
commodityId = 11660,
commodityName = "姜饼小子",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404231
}
},
[11661] = {
commodityId = 11661,
commodityName = "芦小甜",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 1,
jumpId = 630,
jumpText = v8,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
404240
},
bOpenSuit = true,
suitId = 612
},
[11662] = {
commodityId = 11662,
commodityName = "芦小甜",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404241
}
},
[11663] = {
commodityId = 11663,
commodityName = "花菜崽",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
404250
},
bOpenSuit = true,
suitId = 614
},
[11664] = {
commodityId = 11664,
commodityName = "花菜崽",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404251
}
},
[11665] = {
commodityId = 11665,
commodityName = "布小丁",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
404260
},
bOpenSuit = true,
suitId = 616
},
[11666] = {
commodityId = 11666,
commodityName = "布小丁",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404261
}
},
[11667] = {
commodityId = 11667,
commodityName = "玉暖暖",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 1,
jumpId = 630,
jumpText = v8,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
404270
},
bOpenSuit = true,
suitId = 618
},
[11668] = {
commodityId = 11668,
commodityName = "玉暖暖",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404271
}
},
[11669] = {
commodityId = 11669,
commodityName = "晴小岚",
beginTime = {
seconds = 1738252800
},
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
404280
},
bOpenSuit = true,
suitId = 620
},
[11670] = {
commodityId = 11670,
commodityName = "晴小岚",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404281
}
},
[11671] = {
commodityId = 11671,
commodityName = "ISTJ（物流师）",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404290
},
suitId = 622
},
[11672] = {
commodityId = 11672,
commodityName = "ISTJ（物流师）",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404291
}
},
[11673] = {
commodityId = 11673,
commodityName = "射手星",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043200
},
shopTag = v4,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
itemIds = {
404300
},
bOpenSuit = true,
suitId = 624
},
[11674] = {
commodityId = 11674,
commodityName = "射手星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404301
}
},
[11675] = {
commodityId = 11675,
commodityName = "考懒懒",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404310
},
suitId = 626
},
[11676] = {
commodityId = 11676,
commodityName = " 冰雪精灵 妮薇",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v4,
shopSort = 1,
jumpId = 503,
jumpText = "冰雪圆舞曲",
gender = 0,
minVersion = "1.3.37.68",
itemIds = {
404320
},
canGift = true,
addIntimacy = 500,
giftCoinType = 203,
giftPrice = 320,
bOpenSuit = true,
suitId = 628
},
[11677] = {
commodityId = 11677,
commodityName = " 冰雪精灵 妮薇",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
404321
}
},
[11678] = {
commodityId = 11678,
commodityName = " 冰雪精灵 妮薇",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
404322
}
},
[11679] = {
commodityId = 11679,
commodityName = "袋袋狗",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1739980799
},
shopTag = v4,
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
404330
},
bOpenSuit = true,
suitId = 630
},
[11680] = {
commodityId = 11680,
commodityName = "袋袋狗",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404331
}
},
[11681] = {
commodityId = 11681,
commodityName = "戏院小乖",
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738425599
},
shopTag = v4,
shopSort = 1,
jumpId = 628,
jumpText = "星之恋空",
gender = 0,
itemIds = {
404340
},
bOpenSuit = true,
suitId = 632
},
[11682] = {
commodityId = 11682,
commodityName = "戏院小乖",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404341
}
},
[11683] = {
commodityId = 11683,
commodityName = "江户川柯南",
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 2,
jumpId = 1068,
jumpText = "柯南联动时装",
gender = 0,
minVersion = "1.3.37.37",
itemIds = {
404350
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 634
},
[11684] = {
commodityId = 11684,
commodityName = "毛利兰",
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 2,
jumpId = 1068,
jumpText = "柯南联动时装",
gender = 0,
minVersion = "1.3.37.37",
itemIds = {
404360
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 636
},
[11685] = {
commodityId = 11685,
commodityName = "灰原哀",
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 2,
jumpId = 1068,
jumpText = "柯南联动时装",
gender = 0,
minVersion = "1.3.37.37",
itemIds = {
404370
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 638
},
[11686] = {
commodityId = 11686,
commodityName = "萝萝山-萝福来",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404380
},
suitId = 640
},
[11687] = {
commodityId = 11687,
commodityName = "火爆大厨 蜀焰飞",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
404390
},
bOpenSuit = true,
suitId = 642
},
[11688] = {
commodityId = 11688,
commodityName = "火爆大厨 蜀焰飞",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404391
}
},
[11689] = {
commodityId = 11689,
commodityName = "火爆大厨 蜀焰飞",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404392
}
},
[11690] = {
commodityId = 11690,
commodityName = "茶点师 安娜",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 1,
jumpId = 630,
jumpText = v8,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
404400
},
bOpenSuit = true,
suitId = 644
},
[11691] = {
commodityId = 11691,
commodityName = "茶点师 安娜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404401
}
},
[11692] = {
commodityId = 11692,
commodityName = "茶点师 安娜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404402
}
},
[11693] = {
commodityId = 11693,
commodityName = "拉面大师 秦风",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 1,
jumpId = 630,
jumpText = v8,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
404410
},
bOpenSuit = true,
suitId = 646
},
[11694] = {
commodityId = 11694,
commodityName = "拉面大师 秦风",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404411
}
},
[11695] = {
commodityId = 11695,
commodityName = "拉面大师 秦风",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404412
}
},
[11696] = {
commodityId = 11696,
commodityName = "甜心烘焙师 芙芙",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v4,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
404420
},
canGift = true,
addIntimacy = 300,
giftCoinType = 213,
giftPrice = 1,
bOpenSuit = true,
suitId = 648
},
[11697] = {
commodityId = 11697,
commodityName = "甜心烘焙师 芙芙",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
404421
}
},
[11698] = {
commodityId = 11698,
commodityName = "甜心烘焙师 芙芙",
coinType = 200006,
price = 20,
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
404422
}
},
[11699] = {
commodityId = 11699,
commodityName = "美食主播 可可",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 1,
jumpId = 630,
jumpText = v8,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
404430
},
bOpenSuit = true,
suitId = 650
},
[11700] = {
commodityId = 11700,
commodityName = "美食主播 可可",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
404431
}
},
[11701] = {
commodityId = 11701,
commodityName = "美食主播 可可",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
404432
}
},
[11702] = {
commodityId = 11702,
commodityName = "美食品鉴官 红丝绒",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v4,
shopSort = 1,
jumpId = 630,
jumpText = v8,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
404440
},
bOpenSuit = true,
suitId = 652
},
[11704] = {
commodityId = 11704,
commodityName = "乘风破浪 夏侯惇",
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1735833599
},
shopTag = v4,
jumpId = 10699,
jumpText = "扬帆起航",
gender = 0,
itemIds = {
404450
},
bOpenSuit = true,
suitId = 654
},
[11705] = {
commodityId = 11705,
commodityName = "缤纷绘卷 张良",
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1735833599
},
shopTag = v4,
jumpId = 10700,
jumpText = "扬帆起航",
gender = 0,
itemIds = {
404460
},
bOpenSuit = true,
suitId = 656
},
[11706] = {
commodityId = 11706,
commodityName = "塔莉亚",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404470
},
suitId = 658
},
[11707] = {
commodityId = 11707,
commodityName = "塔莉亚",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404471
}
},
[11708] = {
commodityId = 11708,
commodityName = "猴赛雷",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404480
},
suitId = 660
},
[11710] = {
commodityId = 11710,
commodityName = "远游之枪  马可波罗",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404490
},
suitId = 664
},
[11709] = {
commodityId = 11709,
commodityName = "黄金·胖胖达",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404500
},
suitId = 666
},
[11711] = {
commodityId = 11711,
commodityName = "侦探社社长 夏洛克",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404510
},
suitId = 668
},
[11712] = {
commodityId = 11712,
commodityName = "侦探社社长 夏洛克",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404511
}
},
[11713] = {
commodityId = 11713,
commodityName = "侦探社社长 夏洛克",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404512
}
},
[11714] = {
commodityId = 11714,
commodityName = "大雪怪 伊拉",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v4,
shopSort = 1,
jumpId = 503,
jumpText = "冰雪圆舞曲",
gender = 0,
minVersion = "1.3.37.68",
itemIds = {
404520
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 670
},
[11715] = {
commodityId = 11715,
commodityName = "大雪怪 伊拉",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404521
}
},
[11716] = {
commodityId = 11716,
commodityName = "大雪怪 伊拉",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404522
}
},
[11717] = {
commodityId = 11717,
commodityName = "冰霜者 海沃",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v4,
shopSort = 1,
jumpId = 503,
jumpText = "冰雪圆舞曲",
gender = 0,
minVersion = "1.3.37.68",
itemIds = {
404530
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 672
},
[11718] = {
commodityId = 11718,
commodityName = "冰霜者 海沃",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404531
}
},
[11719] = {
commodityId = 11719,
commodityName = "冰霜者 海沃",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404532
}
},
[11720] = {
commodityId = 11720,
commodityName = "夜天使 莎莉娅",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v4,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
404540
},
bOpenSuit = true,
suitId = 674
},
[11721] = {
commodityId = 11721,
commodityName = "昼天使 卢兹",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v4,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
404550
},
bOpenSuit = true,
suitId = 676
},
[11722] = {
commodityId = 11722,
commodityName = "永恒天使 艾薇",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v4,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
404560
},
bOpenSuit = true,
suitId = 678
},
[11723] = {
commodityId = 11723,
commodityName = "龙果儿",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404570
},
suitId = 680
},
[11724] = {
commodityId = 11724,
commodityName = "龙果儿",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404571
}
},
[11725] = {
commodityId = 11725,
commodityName = "席香香",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404580
},
suitId = 682
},
[11726] = {
commodityId = 11726,
commodityName = "席香香",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404581
}
},
[11727] = {
commodityId = 11727,
commodityName = "生日星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404590
},
suitId = 684
},
[11728] = {
commodityId = 11728,
commodityName = "生日星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404591
}
},
[11729] = {
commodityId = 11729,
commodityName = "穿裙子的丁丁",
beginTime = {
seconds = 1733500800
},
jumpId = 1098,
jumpText = "天线宝宝",
gender = 0,
itemIds = {
403941
}
},
[11730] = {
commodityId = 11730,
commodityName = "穿裙子的迪西",
beginTime = {
seconds = 1733500800
},
jumpId = 1098,
jumpText = "天线宝宝",
gender = 0,
itemIds = {
403951
}
},
[11731] = {
commodityId = 11731,
commodityName = "穿裙子的拉拉",
beginTime = {
seconds = 1733500800
},
jumpId = 1098,
jumpText = "天线宝宝",
gender = 0,
itemIds = {
403961
}
},
[11732] = {
commodityId = 11732,
commodityName = "穿裙子的小波",
beginTime = {
seconds = 1733500800
},
jumpId = 1098,
jumpText = "天线宝宝",
gender = 0,
itemIds = {
403971
}
},
[11733] = {
commodityId = 11733,
commodityName = "夜莺之声 艾娜",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404600
},
suitId = 686
},
[11734] = {
commodityId = 11734,
commodityName = "夜莺之声 艾娜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404601
}
},
[11735] = {
commodityId = 11735,
commodityName = "夜莺之声 艾娜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404602
}
},
[11736] = {
commodityId = 11736,
commodityName = "派对甜心",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1739980799
},
shopTag = v4,
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
404610
},
bOpenSuit = true,
suitId = 688
},
[11737] = {
commodityId = 11737,
commodityName = "派对甜心",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404611
}
},
[11738] = {
commodityId = 11738,
commodityName = "剑圣  宫本武藏",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
404620
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 690
},
[11739] = {
commodityId = 11739,
commodityName = "东方曜",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
404210
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 606
},
[11740] = {
commodityId = 11740,
commodityName = "全息碎影 孙悟空",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
403020
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 329
},
[11741] = {
commodityId = 11741,
commodityName = "超时空战士 狄仁杰",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
404140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 592
},
[11742] = {
commodityId = 11742,
commodityName = "挚爱之约 孙策",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
402120
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 222
},
[11743] = {
commodityId = 11743,
commodityName = "音你心动 小乔",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
402130
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 223
},
[11744] = {
commodityId = 11744,
commodityName = "影龙天霄 兰陵王",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
402140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 224
},
[11745] = {
commodityId = 11745,
commodityName = "龙胆 赵云",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
402990
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 326
},
[11746] = {
commodityId = 11746,
commodityName = "异界灵契 孙尚香",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
403000
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 327
},
[11747] = {
commodityId = 11747,
commodityName = "追逃游戏 安琪拉",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
403010
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 328
},
[11748] = {
commodityId = 11748,
commodityName = "偶像歌手 王昭君",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
403640
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 395
},
[11749] = {
commodityId = 11749,
commodityName = "记忆之芯 公孙离",
beginTime = v3,
endTime = {
seconds = 1736697599
},
shopTag = v4,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
gender = 0,
minVersion = v29,
itemIds = {
403650
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 396
},
[11750] = {
commodityId = 11750,
commodityName = "顽皮天使 兰卡",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1750953599
},
shopTag = v4,
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
gender = 0,
minVersion = "1.3.37.87",
itemIds = {
404630
},
canGift = true,
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
addIntimacy = 160,
giftCoinType = 224,
giftPrice = 150,
bOpenSuit = true,
suitId = 692
},
[11751] = {
commodityId = 11751,
commodityName = "顽皮天使 兰卡",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404631
}
},
[11752] = {
commodityId = 11752,
commodityName = "顽皮天使 兰卡",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404632
}
},
[11753] = {
commodityId = 11753,
commodityName = "绝世舞姬 貂蝉",
beginTime = {
seconds = 1750694400
},
endTime = {
seconds = 1752595199
},
shopTag = v4,
jumpId = 10712,
jumpText = "峡谷女明星",
gender = 0,
itemIds = {
404640
},
bOpenSuit = true,
suitId = 694
},
[11754] = {
commodityId = 11754,
commodityName = "墨小轩",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v4,
shopSort = 1,
jumpId = 636,
jumpText = v8,
gender = 0,
minVersion = "1.3.68.1",
itemIds = {
404650
},
bOpenSuit = true,
suitId = 696
},
[11755] = {
commodityId = 11755,
commodityName = "墨小轩",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404651
}
},
[11756] = {
commodityId = 11756,
commodityName = "异域客",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.68.1",
itemIds = {
404660
},
bOpenSuit = true,
suitId = 698
},
[11757] = {
commodityId = 11757,
commodityName = "异域客",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404661
}
},
[11758] = {
commodityId = 11758,
commodityName = "小福狮",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.68.1",
itemIds = {
404670
},
bOpenSuit = true,
suitId = 700
},
[11759] = {
commodityId = 11759,
commodityName = "小福狮",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404671
}
},
[11760] = {
commodityId = 11760,
commodityName = "昭小容",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v4,
shopSort = 1,
jumpId = 636,
jumpText = v8,
gender = 0,
minVersion = "1.3.68.1",
itemIds = {
404680
},
bOpenSuit = true,
suitId = 702
},
[11761] = {
commodityId = 11761,
commodityName = "昭小容",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404681
}
},
[11762] = {
commodityId = 11762,
commodityName = "琪露露",
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1753977599
},
shopTag = v4,
shopSort = 1,
jumpId = 8899,
jumpText = "蝶舞花间",
gender = 0,
minVersion = "1.3.88.98",
itemIds = {
404690
},
canGift = true,
addIntimacy = 80,
giftCoinType = 3950,
giftPrice = 40,
bOpenSuit = true,
suitId = 704
},
[11763] = {
commodityId = 11763,
commodityName = "琪露露",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404691
}
},
[11764] = {
commodityId = 11764,
commodityName = "小年宝",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404700
},
suitId = 706
},
[11765] = {
commodityId = 11765,
commodityName = "小年宝",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404701
}
},
[11766] = {
commodityId = 11766,
commodityName = "ESTP企业家",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741795200
},
shopTag = v4,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
itemIds = {
404710
},
bOpenSuit = true,
suitId = 707
},
[11767] = {
commodityId = 11767,
commodityName = "ESTP企业家",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404711
}
},
[11768] = {
commodityId = 11768,
commodityName = "惊鸿舞姬 若凝",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v4,
shopSort = 1,
jumpId = 636,
jumpText = v8,
gender = 0,
minVersion = "1.3.68.1",
itemIds = {
404720
},
bOpenSuit = true,
suitId = 710
},
[11769] = {
commodityId = 11769,
commodityName = "惊鸿舞姬 若凝",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
404721
}
},
[11770] = {
commodityId = 11770,
commodityName = "惊鸿舞姬 若凝",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
404722
}
},
[11771] = {
commodityId = 11771,
commodityName = "牡丹贵妃 洛安",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v4,
shopSort = 1,
jumpId = 636,
jumpText = v8,
gender = 0,
minVersion = "1.3.68.1",
itemIds = {
404730
},
bOpenSuit = true,
suitId = 712
},
[11772] = {
commodityId = 11772,
commodityName = "Human小金人",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404740
}
},
[11773] = {
commodityId = 11773,
commodityName = "香薰师 紫烟",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v4,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.68.1",
itemIds = {
404750
},
bOpenSuit = true,
suitId = 714
},
[11774] = {
commodityId = 11774,
commodityName = "香薰师 紫烟",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404751
}
},
[11775] = {
commodityId = 11775,
commodityName = "香薰师 紫烟",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404752
}
},
[11776] = {
commodityId = 11776,
commodityName = "无双之魔 吕布",
beginTime = {
seconds = 1735920000
},
endTime = {
seconds = 1739462399
},
shopTag = v4,
jumpId = 10706,
jumpText = "峡谷战神",
gender = 0,
itemIds = {
404760
},
bOpenSuit = true,
suitId = 716
},
[11777] = {
commodityId = 11777,
commodityName = "水晶猎龙者 花木兰",
beginTime = {
seconds = 1735920000
},
endTime = {
seconds = 1739462399
},
shopTag = v4,
jumpId = 10707,
jumpText = "峡谷战神",
gender = 0,
itemIds = {
404770
},
bOpenSuit = true,
suitId = 718
},
[11778] = {
commodityId = 11778,
commodityName = "鹿星星",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1748275199
},
shopTag = v4,
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
gender = 0,
minVersion = "1.3.37.87",
itemIds = {
404780
},
canGift = true,
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
addIntimacy = 160,
giftCoinType = 224,
giftPrice = 50,
bOpenSuit = true,
suitId = 720
},
[11779] = {
commodityId = 11779,
commodityName = "鹿星星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404781
}
},
[11780] = {
commodityId = 11780,
commodityName = "鹿梦梦",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1750953599
},
shopTag = v4,
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
gender = 0,
minVersion = "1.3.37.87",
itemIds = {
404790
},
canGift = true,
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
addIntimacy = 160,
giftCoinType = 224,
giftPrice = 50,
bOpenSuit = true,
suitId = 722
},
[11781] = {
commodityId = 11781,
commodityName = "鹿梦梦",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404791
}
},
[11782] = {
commodityId = 11782,
commodityName = "聂小倩",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404800
},
suitId = 724
},
[11783] = {
commodityId = 11783,
commodityName = "聂小倩",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404801
}
},
[11784] = {
commodityId = 11784,
commodityName = "松小雪",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404810
},
suitId = 726
},
[11785] = {
commodityId = 11785,
commodityName = "松小雪",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404811
}
},
[11786] = {
commodityId = 11786,
commodityName = "年梦梦",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404820
},
suitId = 728
},
[11787] = {
commodityId = 11787,
commodityName = "年梦梦",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404821
}
},
[11788] = {
commodityId = 11788,
commodityName = "乐小汐",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v4,
jumpId = 8888,
jumpText = "天启圣谕",
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
404830
},
bOpenSuit = true,
suitId = 730
},
[11789] = {
commodityId = 11789,
commodityName = "乐小汐",
coinType = 200006,
price = 12,
gender = 0,
minVersion = "1.3.68.33",
itemIds = {
404831
}
},
[11790] = {
commodityId = 11790,
commodityName = "黄啾啾",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404840
},
suitId = 732
},
[11791] = {
commodityId = 11791,
commodityName = "黄啾啾",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404841
}
},
[11792] = {
commodityId = 11792,
commodityName = "牛壮壮",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404850
},
suitId = 734
},
[11793] = {
commodityId = 11793,
commodityName = "牛壮壮",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404851
}
},
[11794] = {
commodityId = 11794,
commodityName = "古咚咚",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v4,
shopSort = 1,
jumpId = 636,
jumpText = v8,
gender = 0,
minVersion = "1.3.68.1",
itemIds = {
404860
},
bOpenSuit = true,
suitId = 736
},
[11795] = {
commodityId = 11795,
commodityName = "古咚咚",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404861
}
},
[11796] = {
commodityId = 11796,
commodityName = "简妙妙",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404870
},
suitId = 738
},
[11797] = {
commodityId = 11797,
commodityName = "简妙妙",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404871
}
},
[11798] = {
commodityId = 11798,
commodityName = "ISFJ守卫者",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404880
},
suitId = 740
},
[11799] = {
commodityId = 11799,
commodityName = "ISFJ守卫者",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404881
}
},
[11800] = {
commodityId = 11800,
commodityName = "小金蛇",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404890
},
suitId = 742
},
[11801] = {
commodityId = 11801,
commodityName = "弥桃桃",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404900
},
suitId = 744
},
[11802] = {
commodityId = 11802,
commodityName = " 电竞之星 依可",
beginTime = {
seconds = 1744646400
},
endTime = {
seconds = 1746115199
},
shopTag = v4,
shopSort = 2,
jumpId = 1069,
jumpText = "电竞少女",
gender = 0,
minVersion = "1.3.78.90",
itemIds = {
404910
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 746
},
[11803] = {
commodityId = 11803,
commodityName = " 电竞之星 依可",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404911
}
},
[11804] = {
commodityId = 11804,
commodityName = " 电竞之星 依可",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404912
}
},
[11805] = {
commodityId = 11805,
commodityName = "幻梦追光 费伊 ",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404920
},
suitId = 748
},
[11806] = {
commodityId = 11806,
commodityName = "幻梦追光 费伊 ",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404921
}
},
[11807] = {
commodityId = 11807,
commodityName = "幻梦追光 费伊 ",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404922
}
},
[11808] = {
commodityId = 11808,
commodityName = "幻光破界 菲朵",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404930
},
suitId = 750
},
[11809] = {
commodityId = 11809,
commodityName = "幻光破界 菲朵",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404931
}
},
[11810] = {
commodityId = 11810,
commodityName = "幻光破界 菲朵",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404932
}
},
[11811] = {
commodityId = 11811,
commodityName = "荆棘鸟 菲奥娜",
beginTime = {
seconds = 1740672000
},
endTime = v12,
shopTag = v4,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
itemIds = {
404940
},
canGift = true,
addIntimacy = 160,
giftCoinType = 205,
giftPrice = 180,
bOpenSuit = true,
suitId = 752
},
[11812] = {
commodityId = 11812,
commodityName = "荆棘鸟 菲奥娜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404941
}
},
[11813] = {
commodityId = 11813,
commodityName = "荆棘鸟 菲奥娜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404942
}
},
[11814] = {
commodityId = 11814,
commodityName = "鲨鱼猫",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404950
},
suitId = 754
},
[11815] = {
commodityId = 11815,
commodityName = "企鹅猫",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404960
},
suitId = 756
},
[11816] = {
commodityId = 11816,
commodityName = "梨园新蕊 锦翎",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404970
},
suitId = 758
},
[11817] = {
commodityId = 11817,
commodityName = "梨园新蕊 锦翎",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404971
}
},
[11818] = {
commodityId = 11818,
commodityName = "梨园新蕊 锦翎",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404972
}
},
[11819] = {
commodityId = 11819,
commodityName = "网球甜心",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
404980
},
suitId = 760
},
[11820] = {
commodityId = 11820,
commodityName = "网球甜心",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
404981
}
},
[11821] = {
commodityId = 11821,
commodityName = "猫探员 怀义",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v4,
shopSort = 1,
jumpId = 636,
jumpText = v8,
gender = 0,
minVersion = "1.3.68.1",
itemIds = {
404990
},
bOpenSuit = true,
suitId = 762
},
[11822] = {
commodityId = 11822,
commodityName = "猫探员 怀义",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404991
}
},
[11823] = {
commodityId = 11823,
commodityName = "猫探员 怀义",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
404992
}
},
[11824] = {
commodityId = 11824,
commodityName = "飞鸟衔花 渺渺",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v4,
shopSort = 1,
jumpId = 636,
jumpText = v8,
gender = 0,
minVersion = "1.3.68.1",
itemIds = {
410030
},
bOpenSuit = true,
suitId = 764
},
[11825] = {
commodityId = 11825,
commodityName = "飞鸟衔花 渺渺",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410031
}
},
[11826] = {
commodityId = 11826,
commodityName = "飞鸟衔花 渺渺",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410032
}
},
[11827] = {
commodityId = 11827,
commodityName = "晴天娃娃",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410010
},
suitId = 766
},
[11828] = {
commodityId = 11828,
commodityName = "晴天娃娃",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410011
}
},
[11829] = {
commodityId = 11829,
commodityName = "早八女孩",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410020
},
suitId = 768
},
[11830] = {
commodityId = 11830,
commodityName = "早八女孩",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410021
}
},
[11831] = {
commodityId = 11831,
commodityName = "玲珑狐妖 绯璃",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v4,
shopSort = 1,
jumpId = 8018,
jumpText = "桃源万千",
gender = 0,
minVersion = v30,
itemIds = {
410040
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 9000,
bOpenSuit = true,
suitId = 770
},
[11832] = {
commodityId = 11832,
commodityName = "玲珑狐妖 绯璃",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v30,
itemIds = {
410041
}
},
[11833] = {
commodityId = 11833,
commodityName = "玲珑狐妖 绯璃",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v30,
itemIds = {
410042
}
},
[11834] = {
commodityId = 11834,
commodityName = "愿力狐仙  绯璃",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v4,
shopSort = 1,
jumpId = 8018,
jumpText = "桃源万千",
gender = 0,
minVersion = v30,
itemIds = {
410050
},
canGift = true,
addIntimacy = 1000,
giftCoinType = 1,
giftPrice = 14500,
bOpenSuit = true,
suitId = 772
},
[11836] = {
commodityId = 11836,
commodityName = "华莱士财神",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410070
},
suitId = 776
},
[11835] = {
commodityId = 11835,
commodityName = "节奏热浪 阿轲",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
410060
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 774
},
[11837] = {
commodityId = 11837,
commodityName = "蓝屏警告 典韦",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
410080
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 778
},
[11838] = {
commodityId = 11838,
commodityName = "剑圣  宫本武藏",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
404620
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 690
},
[11839] = {
commodityId = 11839,
commodityName = "东方曜",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
404210
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 606
},
[11840] = {
commodityId = 11840,
commodityName = "全息碎影 孙悟空",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
403020
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 329
},
[11841] = {
commodityId = 11841,
commodityName = "超时空战士 狄仁杰",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
404140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 592
},
[11842] = {
commodityId = 11842,
commodityName = "挚爱之约 孙策",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
402120
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 222
},
[11843] = {
commodityId = 11843,
commodityName = "音你心动 小乔",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
402130
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 223
},
[11844] = {
commodityId = 11844,
commodityName = "影龙天霄 兰陵王",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
402140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 224
},
[11845] = {
commodityId = 11845,
commodityName = "龙胆 赵云",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
402990
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 326
},
[11846] = {
commodityId = 11846,
commodityName = "异界灵契 孙尚香",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
403000
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 327
},
[11847] = {
commodityId = 11847,
commodityName = "追逃游戏 安琪拉",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v4,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
gender = 0,
minVersion = "1.3.37.97",
itemIds = {
403010
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 328
},
[11848] = {
commodityId = 11848,
commodityName = "偶像歌手 王昭君",
beginTime = {
seconds = 1738166400
},
endTime = {
seconds = 1769788799
},
shopTag = v4,
shopSort = 1,
jumpId = 10711,
jumpText = "峡谷祈愿",
gender = 0,
minVersion = "1.3.68.37",
itemIds = {
403640
},
bOpenSuit = true,
suitId = 395
},
[11849] = {
commodityId = 11849,
commodityName = "记忆之芯 公孙离",
beginTime = {
seconds = 1738166400
},
endTime = {
seconds = 1769788799
},
shopTag = v4,
shopSort = 1,
jumpId = 10711,
jumpText = "峡谷祈愿",
gender = 0,
minVersion = "1.3.68.37",
itemIds = {
403650
},
bOpenSuit = true,
suitId = 396
},
[11850] = {
commodityId = 11850,
commodityName = "碧水芙蓉 青颜",
beginTime = {
seconds = 1738857600
},
endTime = {
seconds = 1740671999
},
shopTag = v4,
shopSort = 1,
jumpId = 556,
jumpText = "千年烟雨",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
410090
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 780
},
[11851] = {
commodityId = 11851,
commodityName = "碧水芙蓉 青颜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410091
}
},
[11852] = {
commodityId = 11852,
commodityName = "碧水芙蓉 青颜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410092
}
},
[11853] = {
commodityId = 11853,
commodityName = "素雪千年 白落",
beginTime = {
seconds = 1738857600
},
endTime = {
seconds = 1740671999
},
shopTag = v4,
shopSort = 1,
jumpId = 556,
jumpText = "千年烟雨",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
410100
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 782
},
[11854] = {
commodityId = 11854,
commodityName = "素雪千年 白落",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410101
}
},
[11855] = {
commodityId = 11855,
commodityName = "素雪千年 白落",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410102
}
},
[11856] = {
commodityId = 11856,
commodityName = "小福星 长乐",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410110
},
suitId = 784
},
[11857] = {
commodityId = 11857,
commodityName = "小福星 长乐",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410111
}
},
[11858] = {
commodityId = 11858,
commodityName = "小福星 长乐",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410112
}
},
[11859] = {
commodityId = 11859,
commodityName = "小福星 长乐",
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739462399
},
shopTag = v4,
jumpId = 502,
jumpText = "冰雪赐福",
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
410113
},
bOpenSuit = true,
suitId = 786
},
[11860] = {
commodityId = 11860,
commodityName = "大耳狗",
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1738425599
},
shopTag = v4,
shopSort = 2,
jumpId = 1073,
jumpText = "三丽鸥家族",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
410120
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 788
},
[11861] = {
commodityId = 11861,
commodityName = "美乐蒂",
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1738425599
},
shopTag = v4,
shopSort = 2,
jumpId = 1073,
jumpText = "三丽鸥家族",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
410130
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 790
},
[11862] = {
commodityId = 11862,
commodityName = "太华 伽罗",
beginTime = {
seconds = 1738166400
},
endTime = {
seconds = 1769788799
},
shopTag = v4,
jumpId = 10711,
jumpText = "峡谷祈愿",
gender = 0,
minVersion = "1.3.68.37",
itemIds = {
410140
},
bOpenSuit = true,
suitId = 792
},
[11863] = {
commodityId = 11863,
commodityName = "画中仙 翎钗",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v4,
jumpId = 8018,
jumpText = "桃源万千",
gender = 0,
minVersion = v30,
itemIds = {
410150
},
bOpenSuit = true,
suitId = 794
},
[11864] = {
commodityId = 11864,
commodityName = "画中仙 翎钗",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v30,
itemIds = {
410151
}
},
[11865] = {
commodityId = 11865,
commodityName = "画中仙 翎钗",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v30,
itemIds = {
410152
}
},
[11866] = {
commodityId = 11866,
commodityName = "水墨圣手 问心",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v4,
jumpId = 8018,
jumpText = "桃源万千",
gender = 0,
minVersion = v30,
itemIds = {
410160
},
bOpenSuit = true,
suitId = 796
},
[11867] = {
commodityId = 11867,
commodityName = "水墨圣手 问心",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v30,
itemIds = {
410161
}
},
[11868] = {
commodityId = 11868,
commodityName = "水墨圣手 问心",
coinType = 200006,
price = 20,
gender = 0,
minVersion = v30,
itemIds = {
410162
}
},
[11869] = {
commodityId = 11869,
commodityName = "时尚绅士 莱恩特",
beginTime = {
seconds = 1740153600
},
endTime = {
seconds = 1743091199
},
shopTag = v4,
jumpId = 568,
jumpText = "莱恩特咖啡屋",
gender = 0,
itemIds = {
410170
},
bOpenSuit = true,
suitId = 798
},
[11870] = {
commodityId = 11870,
commodityName = "时尚绅士 莱恩特",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410171
}
},
[11871] = {
commodityId = 11871,
commodityName = "时尚绅士 莱恩特",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410172
}
},
[11872] = {
commodityId = 11872,
commodityName = "阿福掌柜",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v4,
jumpId = 8018,
jumpText = "桃源万千",
gender = 0,
minVersion = v30,
itemIds = {
410180
},
bOpenSuit = true,
suitId = 800
},
[11873] = {
commodityId = 11873,
commodityName = "阿福掌柜",
coinType = 200006,
price = 12,
gender = 0,
minVersion = v30,
itemIds = {
410181
}
},
[11874] = {
commodityId = 11874,
commodityName = "番茄朵朵",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopTag = v4,
jumpId = 88888,
jumpText = "幻彩调律",
gender = 0,
itemIds = {
410190
},
bOpenSuit = true,
suitId = 802
},
[11875] = {
commodityId = 11875,
commodityName = "番茄朵朵",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410191
}
},
[11876] = {
commodityId = 11876,
commodityName = "蛋小龙",
beginTime = {
seconds = 1743091200
},
shopTag = v4,
jumpId = 705,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
410200
},
bOpenSuit = true,
suitId = 804
},
[11877] = {
commodityId = 11877,
commodityName = "蛋小龙",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410201
}
},
[11878] = {
commodityId = 11878,
commodityName = "酥角角",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410210
},
suitId = 806
},
[11879] = {
commodityId = 11879,
commodityName = "酥角角",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410211
}
},
[11880] = {
commodityId = 11880,
commodityName = "净净管家",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410220
},
suitId = 808
},
[11881] = {
commodityId = 11881,
commodityName = "净净管家",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410221
}
},
[11882] = {
commodityId = 11882,
commodityName = "雪茸茸",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1746719999
},
shopTag = v4,
shopSort = 1,
jumpId = 638,
jumpText = "岚汀之约",
gender = 0,
minVersion = "1.3.68.87",
itemIds = {
410230
},
bOpenSuit = true,
suitId = 814
},
[11883] = {
commodityId = 11883,
commodityName = "雪茸茸",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410231
}
},
[11884] = {
commodityId = 11884,
commodityName = "乐酷星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410240
},
suitId = 816
},
[11885] = {
commodityId = 11885,
commodityName = "乐酷星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410241
}
},
[11886] = {
commodityId = 11886,
commodityName = "漠花花",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410250
},
suitId = 818
},
[11887] = {
commodityId = 11887,
commodityName = "漠花花",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410251
}
},
[11888] = {
commodityId = 11888,
commodityName = "桃心怪盗",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410260
},
suitId = 820
},
[11889] = {
commodityId = 11889,
commodityName = "桃心怪盗",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410261
}
},
[11890] = {
commodityId = 11890,
commodityName = "夜月猎影 凯茜",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410270
},
suitId = 822
},
[11891] = {
commodityId = 11891,
commodityName = "夜月猎影 凯茜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410271
}
},
[11892] = {
commodityId = 11892,
commodityName = "夜月猎影 凯茜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410272
}
},
[11893] = {
commodityId = 11893,
commodityName = "真言骑士 蕾亚",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410280
},
suitId = 824
},
[11894] = {
commodityId = 11894,
commodityName = "真言骑士 蕾亚",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410281
}
},
[11895] = {
commodityId = 11895,
commodityName = "真言骑士 蕾亚",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410282
}
},
[11896] = {
commodityId = 11896,
commodityName = "愿力狐仙  绯璃",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v30,
itemIds = {
410051
}
},
[11897] = {
commodityId = 11897,
commodityName = "愿力狐仙  绯璃",
coinType = 200006,
price = 80,
gender = 0,
minVersion = v30,
itemIds = {
410052
}
},
[11898] = {
commodityId = 11898,
commodityName = "萌犬管家 艾尔弗斯",
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
shopTag = v4,
jumpId = 641,
jumpText = "玩偶之家",
gender = 0,
minVersion = "1.3.78.72",
itemIds = {
410290
},
bOpenSuit = true,
suitId = 826
},
[11899] = {
commodityId = 11899,
commodityName = "萌犬管家 艾尔弗斯",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410291
}
},
[11900] = {
commodityId = 11900,
commodityName = "萌犬管家 艾尔弗斯",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410292
}
},
[11901] = {
commodityId = 11901,
commodityName = "甜心女仆 莉迪亚",
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
shopTag = v4,
jumpId = 641,
jumpText = "玩偶之家",
gender = 0,
minVersion = "1.3.78.72",
itemIds = {
410300
},
bOpenSuit = true,
suitId = 828
},
[11902] = {
commodityId = 11902,
commodityName = "甜心女仆 莉迪亚",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410301
}
},
[11903] = {
commodityId = 11903,
commodityName = "甜心女仆 莉迪亚",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410302
}
},
[11904] = {
commodityId = 11904,
commodityName = "乐桃桃",
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
shopTag = v4,
jumpId = 641,
jumpText = "玩偶之家",
gender = 0,
minVersion = "1.3.78.72",
itemIds = {
410310
},
bOpenSuit = true,
suitId = 830
},
[11905] = {
commodityId = 11905,
commodityName = "乐桃桃",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410311
}
},
[11906] = {
commodityId = 11906,
commodityName = "霓裳风华 杨玉环",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
410320
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 832
},
[11907] = {
commodityId = 11907,
commodityName = "冰雪圆舞曲 甄姬",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
410330
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 834
},
[11908] = {
commodityId = 11908,
commodityName = "执念清除师 离焰",
beginTime = {
seconds = 1742486400
},
endTime = v12,
shopTag = v4,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
itemIds = {
410340
},
canGift = true,
addIntimacy = 160,
giftCoinType = 205,
giftPrice = 180,
bOpenSuit = true,
suitId = 836
},
[11909] = {
commodityId = 11909,
commodityName = "执念清除师 离焰",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410341
}
},
[11910] = {
commodityId = 11910,
commodityName = "执念清除师 离焰",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410342
}
},
[11911] = {
commodityId = 11911,
commodityName = "玩偶修复师 多莉",
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
shopTag = v4,
shopSort = 1,
jumpId = 641,
jumpText = "玩偶之家",
gender = 0,
minVersion = "1.3.78.72",
itemIds = {
410350
},
canGift = true,
addIntimacy = 500,
giftCoinType = 203,
giftPrice = 320,
bOpenSuit = true,
suitId = 838
},
[11912] = {
commodityId = 11912,
commodityName = "玩偶修复师 多莉",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
410351
}
},
[11913] = {
commodityId = 11913,
commodityName = "玩偶修复师 多莉",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
410352
}
},
[11914] = {
commodityId = 11914,
commodityName = "惊喜礼盒 奇科",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410360
},
suitId = 840
},
[11915] = {
commodityId = 11915,
commodityName = "惊喜礼盒 奇科",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410361
}
},
[11916] = {
commodityId = 11916,
commodityName = "惊喜礼盒 奇科",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410362
}
},
[11917] = {
commodityId = 11917,
commodityName = "喵小萌",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410370
},
suitId = 842
},
[11918] = {
commodityId = 11918,
commodityName = "喵小萌",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410371
}
},
[11919] = {
commodityId = 11919,
commodityName = "节奏热浪 阿轲",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
410060
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 774
},
[11920] = {
commodityId = 11920,
commodityName = "蓝屏警告 典韦",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
410080
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 778
},
[11921] = {
commodityId = 11921,
commodityName = "剑圣  宫本武藏",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
404620
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 690
},
[11922] = {
commodityId = 11922,
commodityName = "东方曜",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
404210
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 606
},
[11923] = {
commodityId = 11923,
commodityName = "全息碎影 孙悟空",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
403020
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 329
},
[11924] = {
commodityId = 11924,
commodityName = "超时空战士 狄仁杰",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
404140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 592
},
[11925] = {
commodityId = 11925,
commodityName = "挚爱之约 孙策",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
402120
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 222
},
[11926] = {
commodityId = 11926,
commodityName = "音你心动 小乔",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
402130
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 223
},
[11927] = {
commodityId = 11927,
commodityName = "影龙天霄 兰陵王",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
402140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 224
},
[11928] = {
commodityId = 11928,
commodityName = "龙胆 赵云",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
402990
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 326
},
[11929] = {
commodityId = 11929,
commodityName = "异界灵契 孙尚香",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
403000
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 327
},
[11930] = {
commodityId = 11930,
commodityName = "追逃游戏 安琪拉",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
403010
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 328
},
[11931] = {
commodityId = 11931,
commodityName = "热烈之斧 程咬金",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410380
},
suitId = 844
},
[11932] = {
commodityId = 11932,
commodityName = "预言家 西比尔",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v4,
shopSort = 1,
jumpId = 640,
jumpText = v8,
gender = 0,
minVersion = "1.3.78.1",
itemIds = {
410390
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
bOpenSuit = true,
suitId = 846
},
[11933] = {
commodityId = 11933,
commodityName = "预言家 西比尔",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
410391
}
},
[11934] = {
commodityId = 11934,
commodityName = "预言家 西比尔",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
410392
}
},
[11935] = {
commodityId = 11935,
commodityName = "圣光预言家 西比尔",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v4,
shopSort = 1,
jumpId = 640,
jumpText = v8,
gender = 0,
minVersion = "1.3.78.1",
itemIds = {
410400
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
bOpenSuit = true,
suitId = 848
},
[11936] = {
commodityId = 11936,
commodityName = "潜行狼王 雅各布",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v4,
jumpId = 640,
jumpText = v8,
gender = 0,
minVersion = "1.3.78.1",
itemIds = {
410410
},
bOpenSuit = true,
suitId = 850
},
[11937] = {
commodityId = 11937,
commodityName = "潜行狼王 雅各布",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410411
}
},
[11938] = {
commodityId = 11938,
commodityName = "潜行狼王 雅各布",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410412
}
},
[11939] = {
commodityId = 11939,
commodityName = "精灵猎人 米兰达",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v4,
jumpId = 640,
jumpText = v8,
gender = 0,
minVersion = "1.3.78.1",
itemIds = {
410420
},
bOpenSuit = true,
suitId = 852
},
[11940] = {
commodityId = 11940,
commodityName = "精灵猎人 米兰达",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410421
}
},
[11941] = {
commodityId = 11941,
commodityName = "精灵猎人 米兰达",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410422
}
},
[11942] = {
commodityId = 11942,
commodityName = "异瞳女巫 赛丝",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410430
},
suitId = 854
},
[11943] = {
commodityId = 11943,
commodityName = "异瞳女巫 赛丝",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410431
}
},
[11944] = {
commodityId = 11944,
commodityName = "异瞳女巫 赛丝",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410432
}
},
[11945] = {
commodityId = 11945,
commodityName = "依芙琳",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v4,
jumpId = 640,
jumpText = v8,
gender = 0,
minVersion = "1.3.78.1",
itemIds = {
410440
},
bOpenSuit = true,
suitId = 856
},
[11946] = {
commodityId = 11946,
commodityName = "依芙琳",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410441
}
},
[11947] = {
commodityId = 11947,
commodityName = "星梦捕手",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v4,
jumpId = 640,
jumpText = v8,
gender = 0,
minVersion = "1.3.78.1",
itemIds = {
410450
},
bOpenSuit = true,
suitId = 858
},
[11948] = {
commodityId = 11948,
commodityName = "星梦捕手",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410451
}
},
[11949] = {
commodityId = 11949,
commodityName = "雅力士",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v4,
jumpId = 640,
jumpText = v8,
gender = 0,
minVersion = "1.3.78.1",
itemIds = {
410460
},
bOpenSuit = true,
suitId = 860
},
[11950] = {
commodityId = 11950,
commodityName = "雅力士",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410461
}
},
[11951] = {
commodityId = 11951,
commodityName = "亚当当",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410470
},
suitId = 862
},
[11952] = {
commodityId = 11952,
commodityName = "亚当当",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410471
}
},
[11953] = {
commodityId = 11953,
commodityName = "格拉底",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410480
},
suitId = 864
},
[11954] = {
commodityId = 11954,
commodityName = "格拉底",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410481
}
},
[11955] = {
commodityId = 11955,
commodityName = "莉莉娅",
beginTime = {
seconds = 1741838400
},
endTime = {
seconds = 1755187199
},
shopTag = v4,
jumpId = 583,
jumpText = "猫猫造物台",
gender = 0,
minVersion = "1.3.78.1",
itemIds = {
410490
},
suitId = 866
},
[11956] = {
commodityId = 11956,
commodityName = "莉莉娅",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410491
}
},
[11957] = {
commodityId = 11957,
commodityName = "花菲菲",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410500
},
suitId = 868
},
[11958] = {
commodityId = 11958,
commodityName = "花菲菲",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410501
}
},
[11959] = {
commodityId = 11959,
commodityName = "暖小熙",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410510
},
suitId = 870
},
[11960] = {
commodityId = 11960,
commodityName = "暖小熙",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410511
}
},
[11961] = {
commodityId = 11961,
commodityName = "阿力力",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410520
},
suitId = 872
},
[11962] = {
commodityId = 11962,
commodityName = "阿力力",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410521
}
},
[11963] = {
commodityId = 11963,
commodityName = "杯面仔",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410530
},
suitId = 874
},
[11964] = {
commodityId = 11964,
commodityName = "杯面仔",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410531
}
},
[11965] = {
commodityId = 11965,
commodityName = "淘乐星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410540
},
suitId = 876
},
[11966] = {
commodityId = 11966,
commodityName = "淘乐星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410541
}
},
[11967] = {
commodityId = 11967,
commodityName = "嗅小宝",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410550
},
suitId = 878
},
[11968] = {
commodityId = 11968,
commodityName = "嗅小宝",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410551
}
},
[11969] = {
commodityId = 11969,
commodityName = "焰狸狸",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410560
},
suitId = 880
},
[11970] = {
commodityId = 11970,
commodityName = "焰狸狸",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410561
}
},
[11971] = {
commodityId = 11971,
commodityName = "茸茸兔",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410570
},
suitId = 882
},
[11972] = {
commodityId = 11972,
commodityName = "茸茸兔",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410571
}
},
[11973] = {
commodityId = 11973,
commodityName = "INFP小蝴蝶",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
shopTag = v4,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
itemIds = {
410580
},
bOpenSuit = true,
suitId = 884
},
[11974] = {
commodityId = 11974,
commodityName = "INFP小蝴蝶",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410581
}
},
[11975] = {
commodityId = 11975,
commodityName = "小金毛",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410590
},
suitId = 886
},
[11976] = {
commodityId = 11976,
commodityName = "百合信使 真白",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028800
},
shopTag = v4,
shopSort = 1,
jumpId = 1078,
jumpText = "百合信使",
gender = 0,
minVersion = "1.3.68.101",
itemIds = {
410600
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 888
},
[11977] = {
commodityId = 11977,
commodityName = "百合信使 真白",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410601
}
},
[11978] = {
commodityId = 11978,
commodityName = "百合信使 真白",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410602
}
},
[11979] = {
commodityId = 11979,
commodityName = "洛可可",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410620
},
suitId = 962
},
[11980] = {
commodityId = 11980,
commodityName = "洛可可",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410621
}
},
[11981] = {
commodityId = 11981,
commodityName = "雷蒙蒙",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410630
},
suitId = 964
},
[11982] = {
commodityId = 11982,
commodityName = "雷蒙蒙",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410631
}
},
[11983] = {
commodityId = 11983,
commodityName = "狮威威",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410610
},
suitId = 960
},
[11984] = {
commodityId = 11984,
commodityName = "星闪流萤 埃莉诺",
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1753977599
},
shopTag = v4,
shopSort = 1,
jumpId = 8899,
jumpText = "蝶舞花间",
gender = 0,
minVersion = "1.3.88.98",
itemIds = {
410640
},
canGift = true,
addIntimacy = 160,
giftCoinType = 3950,
giftPrice = 80,
bOpenSuit = true,
suitId = 966
},
[11985] = {
commodityId = 11985,
commodityName = "星闪流萤 埃莉诺",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410641
}
},
[11986] = {
commodityId = 11986,
commodityName = "星闪流萤 埃莉诺",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410642
}
},
[11987] = {
commodityId = 11987,
commodityName = "乘风破浪 夏侯惇",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
404450
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 654
},
[11988] = {
commodityId = 11988,
commodityName = "缤纷绘卷 张良",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v4,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
gender = 0,
minVersion = v30,
itemIds = {
404460
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 656
},
[11989] = {
commodityId = 11989,
commodityName = "怪奇菌学家 奇奇奥",
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1753977599
},
shopTag = v4,
shopSort = 1,
jumpId = 8899,
jumpText = "蝶舞花间",
gender = 0,
minVersion = "1.3.88.98",
itemIds = {
410650
},
canGift = true,
addIntimacy = 160,
giftCoinType = 3950,
giftPrice = 80,
bOpenSuit = true,
suitId = 968
},
[11990] = {
commodityId = 11990,
commodityName = "怪奇菌学家 奇奇奥",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410651
}
},
[11991] = {
commodityId = 11991,
commodityName = "怪奇菌学家 奇奇奥",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410652
}
},
[11992] = {
commodityId = 11992,
commodityName = "自由之蝶 凡妮莎",
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1753977599
},
shopTag = v4,
shopSort = 1,
jumpId = 8899,
jumpText = "蝶舞花间",
gender = 0,
minVersion = "1.3.88.98",
itemIds = {
410660
},
canGift = true,
addIntimacy = 600,
giftCoinType = 3950,
giftPrice = 320,
bOpenSuit = true,
suitId = 970
},
[11993] = {
commodityId = 11993,
commodityName = "自由之蝶 凡妮莎",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
410661
}
},
[11994] = {
commodityId = 11994,
commodityName = "自由之蝶 凡妮莎",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
410662
}
},
[11995] = {
commodityId = 11995,
commodityName = "咕咕鹰",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410690
},
suitId = 976
},
[11996] = {
commodityId = 11996,
commodityName = "天眼萌仔",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410700
},
suitId = 978
},
[11997] = {
commodityId = 11997,
commodityName = "天眼萌仔",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410701
}
},
[11998] = {
commodityId = 11998,
commodityName = "春小莹",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410710
},
suitId = 980
},
[11999] = {
commodityId = 11999,
commodityName = "春小莹",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410711
}
},
[12000] = {
commodityId = 12000,
commodityName = "绝代智谋 诸葛亮",
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1746806399
},
shopTag = v4,
jumpId = 10715,
jumpText = "峡谷英豪",
gender = 0,
minVersion = "1.3.78.29",
itemIds = {
410760
},
bOpenSuit = true,
suitId = 990
},
[12001] = {
commodityId = 12001,
commodityName = "仁德义枪 刘备",
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1746806399
},
shopTag = v4,
jumpId = 10716,
jumpText = "峡谷英豪",
gender = 0,
minVersion = "1.3.78.29",
itemIds = {
410770
},
bOpenSuit = true,
suitId = 992
},
[12002] = {
commodityId = 12002,
commodityName = "兔兔阿卓",
beginTime = {
seconds = 1748620800
},
endTime = {
seconds = 1750435199
},
shopTag = v4,
shopSort = 1,
jumpId = 1097,
jumpText = "卓大王",
gender = 0,
minVersion = "1.3.88.116",
itemIds = {
410680
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 974
},
[12003] = {
commodityId = 12003,
commodityName = "咖啡师阿卓",
beginTime = {
seconds = 1748620800
},
endTime = {
seconds = 1750435199
},
shopTag = v4,
shopSort = 1,
jumpId = 1096,
jumpText = "卓大王",
gender = 0,
minVersion = "1.3.88.116",
itemIds = {
410790
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 996
},
[12004] = {
commodityId = 12004,
commodityName = "菠萝小甜豆",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v4,
shopSort = 1,
jumpId = 10599,
jumpText = "小甜豆",
gender = 0,
minVersion = "1.3.78.73",
itemIds = {
410780
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 994
},
[12005] = {
commodityId = 12005,
commodityName = "木马小甜豆",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v4,
jumpId = 106041,
jumpText = "小甜豆收集有礼",
gender = 0,
minVersion = "1.3.78.73",
itemIds = {
410720
},
bOpenSuit = true,
suitId = 982
},
[12006] = {
commodityId = 12006,
commodityName = "小鸡小甜豆",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v4,
shopSort = 1,
jumpId = 10598,
jumpText = "小甜豆",
gender = 0,
minVersion = "1.3.78.73",
itemIds = {
410670
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 2240,
bOpenSuit = true,
suitId = 972
},
[12007] = {
commodityId = 12007,
commodityName = "执掌千灯 钦元",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410800
},
suitId = 998
},
[12008] = {
commodityId = 12008,
commodityName = "执掌千灯 钦元",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410801
}
},
[12009] = {
commodityId = 12009,
commodityName = "执掌千灯 钦元",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410802
}
},
[12010] = {
commodityId = 12010,
commodityName = "温小雪",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410810
},
suitId = 1000
},
[12011] = {
commodityId = 12011,
commodityName = "温小雪",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410811
}
},
[12012] = {
commodityId = 12012,
commodityName = "牡丹丹",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410820
},
suitId = 1002
},
[12013] = {
commodityId = 12013,
commodityName = "牡丹丹",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410821
}
},
[12014] = {
commodityId = 12014,
commodityName = "星波波",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410830
},
suitId = 1004
},
[12015] = {
commodityId = 12015,
commodityName = "星波波",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410831
}
},
[12016] = {
commodityId = 12016,
commodityName = "南嘉嘉",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410850
},
suitId = 1008
},
[12017] = {
commodityId = 12017,
commodityName = "南嘉嘉",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410851
}
},
[12018] = {
commodityId = 12018,
commodityName = "草莓猫",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410860
},
suitId = 1010
},
[12019] = {
commodityId = 12019,
commodityName = "祈雨师 墨瑜",
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
shopTag = v4,
shopSort = 1,
jumpId = 1083,
jumpText = "墨影流光",
gender = 0,
minVersion = "1.3.78.99",
itemIds = {
410870
},
canGift = true,
addIntimacy = 160,
giftCoinType = 1,
giftPrice = 1260,
bOpenSuit = true,
suitId = 1012
},
[12020] = {
commodityId = 12020,
commodityName = "祈雨师 墨瑜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410871
}
},
[12021] = {
commodityId = 12021,
commodityName = "祈雨师 墨瑜",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410872
}
},
[12022] = {
commodityId = 12022,
commodityName = "甜小朵",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
shopTag = v4,
jumpId = 694,
jumpText = "幻音喵境",
gender = 0,
itemIds = {
410880
},
bOpenSuit = true,
suitId = 1014
},
[12023] = {
commodityId = 12023,
commodityName = "甜小朵",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410881
}
},
[12024] = {
commodityId = 12024,
commodityName = "墨小棠",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410890
},
suitId = 1016
},
[12025] = {
commodityId = 12025,
commodityName = "墨小棠",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410891
}
},
[12026] = {
commodityId = 12026,
commodityName = "余小怪",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410900
},
suitId = 1018
},
[12027] = {
commodityId = 12027,
commodityName = "余小怪",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410901
}
},
[12028] = {
commodityId = 12028,
commodityName = "胡图图",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410910
},
suitId = 1020
},
[12029] = {
commodityId = 12029,
commodityName = "胡图图",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410911
}
},
[12030] = {
commodityId = 12030,
commodityName = "拉弥娅",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410920
},
suitId = 1022
},
[12031] = {
commodityId = 12031,
commodityName = "拉弥娅",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
410921
}
},
[12032] = {
commodityId = 12032,
commodityName = "浮梦罗烟 海月",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
410930
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 1024
},
[12033] = {
commodityId = 12033,
commodityName = "沧海之曜 大乔",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
410940
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 1026
},
[12034] = {
commodityId = 12034,
commodityName = "学生会长",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410950
},
suitId = 1028
},
[12035] = {
commodityId = 12035,
commodityName = "学生会长",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410951
}
},
[12036] = {
commodityId = 12036,
commodityName = "学生会长",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410952
}
},
[12037] = {
commodityId = 12037,
commodityName = "变形术师",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
410960
},
suitId = 1030
},
[12038] = {
commodityId = 12038,
commodityName = "变形术师",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410961
}
},
[12039] = {
commodityId = 12039,
commodityName = "变形术师",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
410962
}
},
[12040] = {
commodityId = 12040,
commodityName = "自由学园导师",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
shopSort = 1,
gender = 0,
itemIds = {
410970
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
suitId = 1032
},
[12041] = {
commodityId = 12041,
commodityName = "自由学园导师",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
410971
}
},
[12042] = {
commodityId = 12042,
commodityName = "自由学园导师",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
410972
}
},
[12043] = {
commodityId = 12043,
commodityName = "秩序学园导师",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
shopSort = 1,
gender = 0,
itemIds = {
410980
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
suitId = 1034
},
[12044] = {
commodityId = 12044,
commodityName = "蜜桃猫",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v4,
jumpId = 1088,
jumpText = "蜜桃猫",
gender = 0,
itemIds = {
410990
},
suitId = 1036
},
[12045] = {
commodityId = 12045,
commodityName = "项羽",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411000
},
suitId = 1038
},
[12046] = {
commodityId = 12046,
commodityName = "司小礼",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411010
},
suitId = 1040
},
[12047] = {
commodityId = 12047,
commodityName = "司小礼",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411011
}
},
[12048] = {
commodityId = 12048,
commodityName = "金小豹",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411020
},
suitId = 1042
},
[12049] = {
commodityId = 12049,
commodityName = "金小豹",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411021
}
},
[12050] = {
commodityId = 12050,
commodityName = "艺小萱",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411030
},
suitId = 1044
},
[12051] = {
commodityId = 12051,
commodityName = "艺小萱",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411031
}
},
[12052] = {
commodityId = 12052,
commodityName = "酷乐星",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411040
},
suitId = 1046
},
[12053] = {
commodityId = 12053,
commodityName = "酷乐星",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411041
}
},
[12054] = {
commodityId = 12054,
commodityName = "程小维",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411050
},
suitId = 1048
},
[12055] = {
commodityId = 12055,
commodityName = "程小维",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411051
}
},
[12056] = {
commodityId = 12056,
commodityName = "林小垒",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411060
},
suitId = 1050
},
[12057] = {
commodityId = 12057,
commodityName = "林小垒",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411061
}
},
[12058] = {
commodityId = 12058,
commodityName = "宫小千",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411070
},
suitId = 1052
},
[12059] = {
commodityId = 12059,
commodityName = "宫小千",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411071
}
},
[12060] = {
commodityId = 12060,
commodityName = "杨小帆",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopTag = v4,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
itemIds = {
411080
},
bOpenSuit = true,
suitId = 1054
},
[12061] = {
commodityId = 12061,
commodityName = "杨小帆",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411081
}
},
[12062] = {
commodityId = 12062,
commodityName = "乐小野",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411090
},
suitId = 1056
},
[12063] = {
commodityId = 12063,
commodityName = "乐小野",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411091
}
},
[12064] = {
commodityId = 12064,
commodityName = "柠乐乐",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411100
},
suitId = 1058
},
[12065] = {
commodityId = 12065,
commodityName = "柠乐乐",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411101
}
},
[12066] = {
commodityId = 12066,
commodityName = "糖圈圈",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411110
},
suitId = 1060
},
[12067] = {
commodityId = 12067,
commodityName = "糖圈圈",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411111
}
},
[12068] = {
commodityId = 12068,
commodityName = "墨妙妙",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411120
},
suitId = 1062
},
[12069] = {
commodityId = 12069,
commodityName = "乘风破浪 夏侯惇",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
404450
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 654
},
[12070] = {
commodityId = 12070,
commodityName = "缤纷绘卷 张良",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
404460
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 656
},
[12071] = {
commodityId = 12071,
commodityName = "霓裳风华 杨玉环",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
410320
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 832
},
[12072] = {
commodityId = 12072,
commodityName = "冰雪圆舞曲 甄姬",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
410330
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 834
},
[12073] = {
commodityId = 12073,
commodityName = "节奏热浪 阿轲",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
410060
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 774
},
[12074] = {
commodityId = 12074,
commodityName = "蓝屏警告 典韦",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
410080
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 778
},
[12075] = {
commodityId = 12075,
commodityName = "剑圣  宫本武藏",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
404620
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 690
},
[12076] = {
commodityId = 12076,
commodityName = "东方曜",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
404210
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 606
},
[12077] = {
commodityId = 12077,
commodityName = "全息碎影 孙悟空",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
403020
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 329
},
[12078] = {
commodityId = 12078,
commodityName = "超时空战士 狄仁杰",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
404140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 592
},
[12079] = {
commodityId = 12079,
commodityName = "挚爱之约 孙策",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
402120
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 222
},
[12080] = {
commodityId = 12080,
commodityName = "音你心动 小乔",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
402130
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 223
},
[12081] = {
commodityId = 12081,
commodityName = "影龙天霄 兰陵王",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
402140
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 224
},
[12082] = {
commodityId = 12082,
commodityName = "龙胆 赵云",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
402990
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 326
},
[12083] = {
commodityId = 12083,
commodityName = "异界灵契 孙尚香",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
403000
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 327
},
[12084] = {
commodityId = 12084,
commodityName = "追逃游戏 安琪拉",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v4,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
gender = 0,
minVersion = "1.3.78.80",
itemIds = {
403010
},
canGift = true,
addIntimacy = 160,
giftCoinType = 218,
giftPrice = 520,
bOpenSuit = true,
suitId = 328
},
[12085] = {
commodityId = 12085,
commodityName = "蜜桃猫",
coinType = 200006,
price = 20,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
gender = 0,
itemIds = {
410991
}
},
[12086] = {
commodityId = 12086,
commodityName = "天气之女 晴子",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411130
},
suitId = 1064
},
[12087] = {
commodityId = 12087,
commodityName = "天气之女 晴子",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411131
}
},
[12088] = {
commodityId = 12088,
commodityName = "天气之女 晴子",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411132
}
},
[12089] = {
commodityId = 12089,
commodityName = "初阳金菊 幸",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411140
},
suitId = 1066
},
[12090] = {
commodityId = 12090,
commodityName = "初阳金菊 幸",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411141
}
},
[12091] = {
commodityId = 12091,
commodityName = "初阳金菊 幸",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411142
}
},
[12092] = {
commodityId = 12092,
commodityName = "仙灵教母 奥瑞莉亚",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411150
},
suitId = 1068
},
[12093] = {
commodityId = 12093,
commodityName = "仙灵教母 奥瑞莉亚",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411151
}
},
[12094] = {
commodityId = 12094,
commodityName = "仙灵教母 奥瑞莉亚",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411152
}
},
[12095] = {
commodityId = 12095,
commodityName = "熊魂武师 晴霜",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411160
},
suitId = 1070
},
[12096] = {
commodityId = 12096,
commodityName = "绯寒花灵 樱",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411170
},
suitId = 1072
},
[12097] = {
commodityId = 12097,
commodityName = "绯寒花灵 樱",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411171
}
},
[12098] = {
commodityId = 12098,
commodityName = "绯寒花灵 樱",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411172
}
},
[12099] = {
commodityId = 12099,
commodityName = "调香师 安娜苏",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411180
},
suitId = 1074
},
[12100] = {
commodityId = 12100,
commodityName = "调香师 安娜苏",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411181
}
},
[12101] = {
commodityId = 12101,
commodityName = "调香师 安娜苏",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411182
}
},
[12102] = {
commodityId = 12102,
commodityName = " 梦笔生花 绮",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopTag = v4,
jumpId = 88888,
jumpText = "幻彩调律",
gender = 0,
minVersion = "1.3.88.67",
itemIds = {
411190
},
bOpenSuit = true,
suitId = 1076
},
[12103] = {
commodityId = 12103,
commodityName = "绒绒兔叽",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411200
},
suitId = 1078
},
[12104] = {
commodityId = 12104,
commodityName = "绒绒兔叽",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411201
}
},
[12105] = {
commodityId = 12105,
commodityName = "星小虎",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411210
},
suitId = 1080
},
[12106] = {
commodityId = 12106,
commodityName = "星小虎",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411211
}
},
[12107] = {
commodityId = 12107,
commodityName = "狸小豹",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411220
},
suitId = 1082
},
[12108] = {
commodityId = 12108,
commodityName = "狸小豹",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411221
}
},
[12109] = {
commodityId = 12109,
commodityName = "奥黛特",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411230
},
suitId = 1084
},
[12110] = {
commodityId = 12110,
commodityName = "奥黛特",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411231
}
},
[12111] = {
commodityId = 12111,
commodityName = "啵啵软糖",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411240
},
suitId = 1086
},
[12112] = {
commodityId = 12112,
commodityName = "啵啵软糖",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411241
}
},
[12113] = {
commodityId = 12113,
commodityName = "可可小姐",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411250
},
suitId = 1088
},
[12114] = {
commodityId = 12114,
commodityName = "可可小姐",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411251
}
},
[12115] = {
commodityId = 12115,
commodityName = "黄花少女",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411260
},
suitId = 1090
},
[12116] = {
commodityId = 12116,
commodityName = "黄花少女",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411261
}
},
[12117] = {
commodityId = 12117,
commodityName = "星星海",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411270
},
suitId = 1092
},
[12118] = {
commodityId = 12118,
commodityName = "星星海",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411271
}
},
[12119] = {
commodityId = 12119,
commodityName = "安康叶叶",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411280
},
suitId = 1094
},
[12120] = {
commodityId = 12120,
commodityName = "安康叶叶",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411281
}
},
[12121] = {
commodityId = 12121,
commodityName = "舞翩翩",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411290
},
suitId = 1096
},
[12122] = {
commodityId = 12122,
commodityName = "舞翩翩",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411291
}
},
[12123] = {
commodityId = 12123,
commodityName = "幻彩画匠 绮",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopTag = v4,
shopSort = 1,
jumpId = 88888,
jumpText = "幻彩调律",
gender = 0,
minVersion = "1.3.88.67",
itemIds = {
410840
},
canGift = true,
addIntimacy = 1200,
giftCoinType = 1,
giftPrice = 18600,
bOpenSuit = true,
suitId = 1006
},
[12124] = {
commodityId = 12124,
commodityName = "赤魂守心 龙绛",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411300
},
suitId = 1098
},
[12125] = {
commodityId = 12125,
commodityName = "赤魂守心 龙绛",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411301
}
},
[12126] = {
commodityId = 12126,
commodityName = "赤魂守心 龙绛",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411302
}
},
[12127] = {
commodityId = 12127,
commodityName = "黄金骑士 席德",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411310
},
suitId = 1100
},
[12128] = {
commodityId = 12128,
commodityName = "黄金骑士 席德",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411311
}
},
[12129] = {
commodityId = 12129,
commodityName = "黄金骑士 席德",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411312
}
},
[12130] = {
commodityId = 12130,
commodityName = "海洋画家 安德娅",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411320
},
suitId = 1102
},
[12131] = {
commodityId = 12131,
commodityName = "海洋画家 安德娅",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411321
}
},
[12132] = {
commodityId = 12132,
commodityName = "海洋画家 安德娅",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411322
}
},
[12133] = {
commodityId = 12133,
commodityName = "Human限定款",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411330
},
suitId = 1104
},
[12134] = {
commodityId = 12134,
commodityName = "Human限定款",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411340
},
suitId = 1106
},
[12135] = {
commodityId = 12135,
commodityName = "Human限定款",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411350
},
suitId = 1108
},
[12136] = {
commodityId = 12136,
commodityName = "奇遇舞章 艾琳",
beginTime = {
seconds = 1749830400
},
endTime = {
seconds = 1752508799
},
shopTag = v4,
jumpId = 10719,
jumpText = "奇遇舞章",
gender = 0,
minVersion = "1.3.88.105",
itemIds = {
411360
},
bOpenSuit = true,
suitId = 1110
},
[12137] = {
commodityId = 12137,
commodityName = "萌力欧皇",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411370
},
suitId = 1112
},
[12138] = {
commodityId = 12138,
commodityName = "萌力欧皇",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411371
}
},
[12139] = {
commodityId = 12139,
commodityName = "宝葫涂涂",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411380
},
suitId = 1114
},
[12140] = {
commodityId = 12140,
commodityName = "宝葫涂涂",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411381
}
},
[12141] = {
commodityId = 12141,
commodityName = "羽飞飞",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411390
},
suitId = 1116
},
[12142] = {
commodityId = 12142,
commodityName = "羽飞飞",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411391
}
},
[12143] = {
commodityId = 12143,
commodityName = "黄金·气泡狗",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411400
},
suitId = 1118
},
[12144] = {
commodityId = 12144,
commodityName = "森之风灵 虞姬",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411430
},
suitId = 1124
},
[12145] = {
commodityId = 12145,
commodityName = "管弦指挥家 玛琳",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411440
},
suitId = 1126
},
[12146] = {
commodityId = 12146,
commodityName = "管弦指挥家 玛琳",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411441
}
},
[12147] = {
commodityId = 12147,
commodityName = "管弦指挥家 玛琳",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411442
}
},
[12148] = {
commodityId = 12148,
commodityName = "花栗鼠 珍妮特",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411450
},
suitId = 1128
},
[12149] = {
commodityId = 12149,
commodityName = "花栗鼠 珍妮特",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411451
}
},
[12150] = {
commodityId = 12150,
commodityName = "花栗鼠 珍妮特",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411452
}
},
[12151] = {
commodityId = 12151,
commodityName = "灼翼重明鸟 焰昕",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411460
},
suitId = 1130
},
[12152] = {
commodityId = 12152,
commodityName = "灼翼重明鸟 焰昕",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411461
}
},
[12153] = {
commodityId = 12153,
commodityName = "灼翼重明鸟 焰昕",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411462
}
},
[12154] = {
commodityId = 12154,
commodityName = "金辉白虎 宁岳",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411470
},
suitId = 1132
},
[12155] = {
commodityId = 12155,
commodityName = "金辉白虎 宁岳",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411471
}
},
[12156] = {
commodityId = 12156,
commodityName = "金辉白虎 宁岳",
coinType = 200006,
price = 20,
gender = 0,
itemIds = {
411472
}
},
[12157] = {
commodityId = 12157,
commodityName = "腓腓糯",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411480
},
suitId = 1134
},
[12158] = {
commodityId = 12158,
commodityName = "腓腓糯",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411481
}
},
[12159] = {
commodityId = 12159,
commodityName = "灵芝露露",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411490
},
suitId = 1136
},
[12160] = {
commodityId = 12160,
commodityName = "灵芝露露",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411491
}
},
[12161] = {
commodityId = 12161,
commodityName = "人参娃娃",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411500
},
suitId = 1138
},
[12162] = {
commodityId = 12162,
commodityName = "人参娃娃",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411501
}
},
[12163] = {
commodityId = 12163,
commodityName = "当康穗穗",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411510
},
suitId = 1140
},
[12164] = {
commodityId = 12164,
commodityName = "当康穗穗",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411511
}
},
[12165] = {
commodityId = 12165,
commodityName = "天狗烬",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411520
},
suitId = 1142
},
[12166] = {
commodityId = 12166,
commodityName = "天狗烬",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411521
}
},
[12167] = {
commodityId = 12167,
commodityName = "茶甜甜",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411530
},
suitId = 1144
},
[12168] = {
commodityId = 12168,
commodityName = "茶甜甜",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411531
}
},
[12169] = {
commodityId = 12169,
commodityName = "花间猫猫",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411540
},
suitId = 1146
},
[12170] = {
commodityId = 12170,
commodityName = "花间猫猫",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411541
}
},
[12171] = {
commodityId = 12171,
commodityName = "蜜桃邦妮",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411550
},
suitId = 1148
},
[12172] = {
commodityId = 12172,
commodityName = "蜜桃邦妮",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411551
}
},
[12173] = {
commodityId = 12173,
commodityName = "像素派派",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411560
},
suitId = 1150
},
[12174] = {
commodityId = 12174,
commodityName = "像素派派",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411561
}
},
[12175] = {
commodityId = 12175,
commodityName = "团尾栗栗",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411570
},
suitId = 1152
},
[12176] = {
commodityId = 12176,
commodityName = "团尾栗栗",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411571
}
},
[12177] = {
commodityId = 12177,
commodityName = "金铃小满",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411580
},
suitId = 1154
},
[12178] = {
commodityId = 12178,
commodityName = "金铃小满",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411581
}
},
[12179] = {
commodityId = 12179,
commodityName = "铜锈锈",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411590
},
suitId = 1156
},
[12180] = {
commodityId = 12180,
commodityName = "铜锈锈",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411591
}
},
[12181] = {
commodityId = 12181,
commodityName = "莓莓糖心",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411600
},
suitId = 1158
},
[12182] = {
commodityId = 12182,
commodityName = "莓莓糖心",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411601
}
},
[12183] = {
commodityId = 12183,
commodityName = "魔绒斑比",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411610
},
suitId = 1160
},
[12184] = {
commodityId = 12184,
commodityName = "魔绒斑比",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411611
}
},
[12185] = {
commodityId = 12185,
commodityName = "猫奈奈",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411620
},
suitId = 1162
},
[12186] = {
commodityId = 12186,
commodityName = "猫奈奈",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411621
}
},
[12187] = {
commodityId = 12187,
commodityName = "昆仑守护神 白泽",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
shopSort = 1,
gender = 0,
itemIds = {
411630
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
suitId = 1164
},
[12188] = {
commodityId = 12188,
commodityName = "昆仑守护神 白泽",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
411631
}
},
[12189] = {
commodityId = 12189,
commodityName = "昆仑守护神 白泽",
coinType = 200006,
price = 80,
gender = 0,
itemIds = {
411632
}
},
[12190] = {
commodityId = 12190,
commodityName = "昆仑之心 白泽",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
shopSort = 1,
gender = 0,
itemIds = {
411640
},
canGift = true,
addIntimacy = 500,
giftCoinType = 211,
giftPrice = 320,
suitId = 1166
},
[12191] = {
commodityId = 12191,
commodityName = "翩翩蝶",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411650
},
suitId = 1168
},
[12192] = {
commodityId = 12192,
commodityName = "翩翩蝶",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411651
}
},
[12193] = {
commodityId = 12193,
commodityName = "招财鲤鱼",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411680
},
suitId = 1172
},
[12194] = {
commodityId = 12194,
commodityName = "招财鲤鱼",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411681
}
},
[12195] = {
commodityId = 12195,
commodityName = "小狗宝宝",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411690
},
suitId = 1174
},
[12196] = {
commodityId = 12196,
commodityName = "小狗宝宝",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411691
}
},
[12197] = {
commodityId = 12197,
commodityName = "霜龙断孽 冰夷",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411700
},
suitId = 1176
},
[12198] = {
commodityId = 12198,
commodityName = "银角大王",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411710
},
suitId = 1178
},
[12199] = {
commodityId = 12199,
commodityName = "银角大王",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411711
}
},
[12200] = {
commodityId = 12200,
commodityName = "压龙大仙",
coinType = 6,
price = 1000000,
beginTime = v2,
endTime = v12,
shopTag = v4,
gender = 0,
itemIds = {
411720
},
suitId = 1180
},
[12201] = {
commodityId = 12201,
commodityName = "压龙大仙",
coinType = 200006,
price = 12,
gender = 0,
itemIds = {
411721
}
}
}

local mt = {
mallId = 6,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1706198400
},
canDirectBuy = false,
itemNums = {
1
},
bOpenSuit = false,
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
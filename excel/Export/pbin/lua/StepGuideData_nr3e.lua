--com.tencent.wea.xlsRes.table_StepGuideData => excel/xls/X_新手引导表_nr3e.xlsx: 新引导步骤

local v0 = 1

local v1 = 1011

local v2 = 1001

local v3 = "4"

local v4 = "clicked"

local v5 = 3.0

local v6 = "ON_NR3E1_CloseCurrentGuide"

local v7 = 12

local v8 = 10

local v9 = "UI_InLevel_NR3E1_HideAndSeek_Main"

local v10 = "{X=-60, Y = -230}"

local v11 = 1002

local v12 = "{X=0, Y = 0}"

local v13 = "{X=-17, Y = -17}"

local v14 = 1005

local v15 = "{X=-256, Y = -380}"

local v16 = "UI_SkillButtonProp"

local data = {
[10101] = {
StepID = 10101,
GuideID = 501010,
Comment = "显示游戏提示",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = [[来一场躲猫猫吧！这次，你是一名<Highlight31>搜捕者</>。
<Highlight31>游戏目标：</>搜索场景，抓到那些变身中的伪装者吧。]],
bAnyWhereNext = true,
ButtonMode = v4,
LockCDInSeconds = 3.0
},
[10102] = {
StepID = 10102,
GuideID = 501010,
Comment = "攻击按钮提示",
SubStep = 2,
TypeParams = "ON_NR3E0_ThrowProp_BeginTouch",
LifeTime = 12,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Seeker",
GetWidgetFunc = "GetAttackIcon",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "使用水枪，攻击可疑的物品",
UIOffset_Second = v15
},
[10103] = {
StepID = 10103,
GuideID = 501010,
Comment = "扫描按钮提示",
SubStep = 3,
StepType = 1,
LifeTime = 12,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Seeker",
WidgetName = "UI_Button_Scan",
GetButtonFunc = "GetScanButton",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "扫描身边的可疑物品",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10104] = {
StepID = 10104,
GuideID = 501011,
Comment = "探查按钮提示",
StepType = 1,
LifeTime = 12,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Seeker",
WidgetName = "UI_Button_Detect",
GetButtonFunc = "GetDetectButton",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "侦测一个伪装者的方位",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10105] = {
StepID = 10105,
GuideID = 501012,
Comment = "显示游戏提示",
StepType = 1,
IsForceGuide = true,
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
UIStyle_Frist = 1011,
UIText_Frist = [[来一场躲猫猫吧！现在，你是一名<Highlight31>伪装者</>。
<Highlight31>游戏目标：</>变身成各种物品，在场景中躲藏起来吧！]],
bAnyWhereNext = true,
ButtonMode = v4,
LockCDInSeconds = 3.0
},
[10106] = {
StepID = 10106,
GuideID = 501012,
Comment = "显示出移动摇杆",
SubStep = 2,
StepType = 8,
TypeParams = "{actionId = 5,funcParams = {1,4}}",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
ButtonMode = v4
},
[10107] = {
StepID = 10107,
GuideID = 501012,
Comment = "伪装者移动引导前置",
SubStep = 3,
StepType = 12,
TypeParams = "{delay=0,eventName=\"ON_NR3E0_CheckJoyMoveBegin\"}",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}"
},
[10108] = {
StepID = 10108,
GuideID = 501012,
Comment = "伪装者移动引导",
SubStep = 4,
TypeParams = "ON_NR3E0_CheckJoyMoveEnd",
LifeTime = 12,
WindowName = v9,
WidgetName = "w_Guide_Joystick",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
UIStyle_Frist = 1015,
UIOffset_Frist = "{X=-20, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "推动摇杆，可以进行移动",
UIOffset_Second = "{X=-200, Y = -450}",
IsKeyStep = true,
ButtonMode = v4
},
[10109] = {
StepID = 10109,
GuideID = 501012,
Comment = "伪装者镜头引导",
SubStep = 5,
StepType = 101,
TypeParams = "Newcomer_AdjustCamera_Over",
WindowName = v9,
WidgetName = "w_Button_Jump",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
UIStyle_Frist = 1008,
UIOffset_Frist = "{X=-300, Y = -550}",
UIStyle_Second = 1005,
UIText_Second = "滑动此处调整镜头",
UIOffset_Second = "{X=-350, Y = -700}",
IsKeyStep = true,
ButtonMode = "pressed"
},
[10110] = {
StepID = 10110,
GuideID = 501012,
Comment = "变身按钮提示",
SubStep = 6,
StepType = 1,
LifeTime = 12,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Hider",
WidgetName = "UI_BtnTimeCountPretend",
GetButtonFunc = "GetPretendButton",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "变身成一个随机物品",
UIOffset_Second = v15,
ButtonMode = v4
},
[10111] = {
StepID = 10111,
GuideID = 501012,
Comment = "延迟5秒",
SubStep = 7,
TypeParams = "ON_NR3E0_NONE",
LifeTime = 5,
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
IsKeyStep = true,
ButtonMode = v4
},
[10112] = {
StepID = 10112,
GuideID = 501012,
Comment = "隐形按钮提示",
SubStep = 8,
StepType = 1,
LifeTime = 8,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Hider",
WidgetName = "UI_BtnTimeCountHide",
GetButtonFunc = "GetHideButton",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountXRayVision\"}}",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "关键的时刻，可以隐身",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10113] = {
StepID = 10113,
GuideID = 501012,
Comment = "延迟20秒",
SubStep = 9,
TypeParams = "ON_NR3E0_NONE",
LifeTime = 20,
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountXRayVision\"}}",
IsKeyStep = true,
ButtonMode = v4
},
[10114] = {
StepID = 10114,
GuideID = 501012,
Comment = "伪装者透视引导",
SubStep = 10,
StepType = 1,
LifeTime = 8,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Hider",
WidgetName = "UI_BtnTimeCountXRayVision",
GetButtonFunc = "GetXRayButton",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "透视观察其他玩家",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10115] = {
StepID = 10115,
GuideID = 501013,
Comment = "飞行背包按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "飞行背包：获得短暂的飞行能力",
UIOffset_Second = v15,
IsKeyStep = true
},
[10116] = {
StepID = 10116,
GuideID = 501014,
Comment = "爆破弹按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "爆破弹：摧毁场景物件和伪装者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10117] = {
StepID = 10117,
GuideID = 501015,
Comment = "高爆雷按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "高爆雷：放置地雷，触发后可以淘汰伪装者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10118] = {
StepID = 10118,
GuideID = 501016,
Comment = "雷达按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "雷达：探测附近伪装者的位置",
UIOffset_Second = v15,
IsKeyStep = true
},
[10119] = {
StepID = 10119,
GuideID = 501017,
Comment = "磁铁按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "磁铁：投掷磁铁，持续吸引伪装者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10120] = {
StepID = 10120,
GuideID = 501018,
Comment = "诱捕帽按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "诱捕帽：放置一个虚假的道具，诱捕伪装者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10121] = {
StepID = 10121,
GuideID = 501019,
Comment = "护盾按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "护盾：避免搜捕者的伤害",
UIOffset_Second = v15,
IsKeyStep = true
},
[10122] = {
StepID = 10122,
GuideID = 501020,
Comment = "变身器按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "变身器：随机变身为一个搜捕者的模样",
UIOffset_Second = v15,
IsKeyStep = true
},
[10123] = {
StepID = 10123,
GuideID = 501021,
Comment = "涂鸦手雷按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "涂鸦手雷：向搜捕者的屏幕涂鸦，遮挡其视野",
UIOffset_Second = v15,
IsKeyStep = true
},
[10124] = {
StepID = 10124,
GuideID = 501022,
Comment = "复制人偶按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "复制人偶：复制一个当前伪装物，迷惑搜捕者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10125] = {
StepID = 10125,
GuideID = 501023,
Comment = "传送门按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "传送门：放置一个传送门，可随机传送到其他位置",
UIOffset_Second = v15,
IsKeyStep = true
},
[10126] = {
StepID = 10126,
GuideID = 501024,
Comment = "诱捕帽按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "诱捕帽：放置一个虚假的道具，诱捕搜捕者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10154] = {
StepID = 10154,
GuideID = 501041,
Comment = "雪人炮弹按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "雪人炮弹：变成雪人炮弹，撞上敌人把他们变成雪人！",
UIOffset_Second = v15,
IsKeyStep = true
},
[10155] = {
StepID = 10155,
GuideID = 501042,
Comment = "守护精灵按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "守护精灵：召唤守护精灵，被淘汰时可以重生一次。",
UIOffset_Second = v15,
IsKeyStep = true
},
[10156] = {
StepID = 10156,
GuideID = 501043,
Comment = "云朵按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "云朵：放置一朵可以踩的云朵，最多可以使用三次。",
UIOffset_Second = v15,
IsKeyStep = true
},
[10157] = {
StepID = 10157,
GuideID = 501044,
Comment = "砰砰锤按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "砰砰锤：踩踏地面，将附近大范围内的伪装者振飞。",
UIOffset_Second = v15,
IsKeyStep = true
},
[10127] = {
StepID = 10127,
GuideID = 501025,
Comment = "显示游戏提示PC",
StepType = 1,
IsForceGuide = true,
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
UIStyle_Frist = 1011,
UIText_Frist = [[来一场躲猫猫吧！现在，你是一名<Highlight31>伪装者</>。
<Highlight31>游戏目标：</>变身成各种物品，在场景中躲藏起来吧！]],
bAnyWhereNext = true,
ButtonMode = v4,
LockCDInSeconds = 3.0
},
[10128] = {
StepID = 10128,
GuideID = 501025,
Comment = "显示出移动摇杆",
SubStep = 2,
StepType = 8,
TypeParams = "{actionId = 5,funcParams = {1,4}}",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
ButtonMode = v4
},
[10129] = {
StepID = 10129,
GuideID = 501025,
Comment = "伪装者移动引导前置",
SubStep = 3,
StepType = 12,
TypeParams = "{delay=0,eventName=\"ON_NR3E0_CheckJoyMoveBegin\"}",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}"
},
[10130] = {
StepID = 10130,
GuideID = 501025,
Comment = "伪装者移动引导",
SubStep = 4,
TypeParams = "ON_NR3E0_CheckJoyMoveEnd",
LifeTime = 12,
WindowName = v9,
WidgetName = "w_Guide_JoystickE1",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
UIStyle_Second = 1005,
UIText_Second = "按下WASD，可以进行移动",
UIOffset_Second = "{X=-200, Y = -450}",
IsKeyStep = true,
ButtonMode = v4
},
[10131] = {
StepID = 10131,
GuideID = 501025,
Comment = "伪装者镜头引导",
SubStep = 5,
StepType = 101,
TypeParams = "Newcomer_AdjustCamera_Over",
WindowName = v9,
WidgetName = "w_Button_Jump",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
UIStyle_Second = 1005,
UIText_Second = "滑动鼠标调整镜头",
UIOffset_Second = "{X=-500, Y = -700}",
IsKeyStep = true,
ButtonMode = "pressed"
},
[10132] = {
StepID = 10132,
GuideID = 501025,
Comment = "变身按钮提示",
SubStep = 6,
TypeParams = "ON_NR3E1_CHANGE",
LifeTime = 12,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Hider",
WidgetName = "UI_BtnTimeCountPretend",
GetButtonFunc = "GetPretendButton",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "按下F键，变身成一个随机物品",
UIOffset_Second = v15,
ButtonMode = v4
},
[10133] = {
StepID = 10133,
GuideID = 501025,
Comment = "延迟5秒",
SubStep = 7,
TypeParams = "ON_NR3E0_NONE",
LifeTime = 5,
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountHide\", \"UI_BtnTimeCountXRayVision\"}}",
IsKeyStep = true,
ButtonMode = v4
},
[10134] = {
StepID = 10134,
GuideID = 501025,
Comment = "隐形按钮提示",
SubStep = 8,
TypeParams = "ON_NR3E1_HIDE",
LifeTime = 8,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Hider",
WidgetName = "UI_BtnTimeCountHide",
GetButtonFunc = "GetHideButton",
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountXRayVision\"}}",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "关键的时刻，点击鼠标右键可以隐身",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10135] = {
StepID = 10135,
GuideID = 501025,
Comment = "延迟20秒",
SubStep = 9,
TypeParams = "ON_NR3E0_NONE",
LifeTime = 20,
ShowUIList = "{UI_InLevel_NR3E1_HideAndSeek_Main_Hider = {\"UI_BtnTimeCountXRayVision\"}}",
IsKeyStep = true,
ButtonMode = v4
},
[10136] = {
StepID = 10136,
GuideID = 501025,
Comment = "伪装者透视引导",
SubStep = 10,
TypeParams = "ON_NR3E1_XRAY",
LifeTime = 8,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Hider",
WidgetName = "UI_BtnTimeCountXRayVision",
GetButtonFunc = "GetXRayButton",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "按下R键，透视观察其他玩家",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10137] = {
StepID = 10137,
GuideID = 501026,
Comment = "显示游戏提示PC",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = [[来一场躲猫猫吧！这次，你是一名<Highlight31>搜捕者</>。
<Highlight31>游戏目标：</>搜索场景，抓到那些变身中的伪装者吧。]],
bAnyWhereNext = true,
ButtonMode = v4,
LockCDInSeconds = 3.0
},
[10138] = {
StepID = 10138,
GuideID = 501026,
Comment = "攻击按钮提示",
SubStep = 2,
TypeParams = "ON_NR3E0_ThrowProp_BeginTouch",
LifeTime = 12,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Seeker",
GetWidgetFunc = "GetAttackIcon",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "使用鼠标右键，攻击可疑的物品",
UIOffset_Second = v15
},
[10139] = {
StepID = 10139,
GuideID = 501026,
Comment = "扫描按钮提示",
SubStep = 3,
TypeParams = "ON_NR3E1_SCAN",
LifeTime = 12,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Seeker",
WidgetName = "UI_Button_Scan",
GetButtonFunc = "GetScanButton",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "按键F键，扫描身边的可疑物品",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10140] = {
StepID = 10140,
GuideID = 501027,
Comment = "探查按钮提示PC",
TypeParams = "ON_NR3E1_DETECT",
LifeTime = 12,
WindowName = "UI_InLevel_NR3E1_HideAndSeek_Main_Seeker",
WidgetName = "UI_Button_Detect",
GetButtonFunc = "GetDetectButton",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "按下R键，侦测一个伪装者的方位",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10141] = {
StepID = 10141,
GuideID = 501028,
Comment = "飞行背包按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "飞行背包：获得短暂的飞行能力",
UIOffset_Second = v15,
IsKeyStep = true
},
[10142] = {
StepID = 10142,
GuideID = 501029,
Comment = "爆破弹按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "爆破弹：摧毁场景物件和伪装者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10143] = {
StepID = 10143,
GuideID = 501030,
Comment = "高爆雷按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "高爆雷：放置地雷，触发后可以淘汰伪装者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10144] = {
StepID = 10144,
GuideID = 501031,
Comment = "雷达按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "雷达：探测附近伪装者的位置",
UIOffset_Second = v15,
IsKeyStep = true
},
[10145] = {
StepID = 10145,
GuideID = 501032,
Comment = "磁铁按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "磁铁：投掷磁铁，持续吸引伪装者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10146] = {
StepID = 10146,
GuideID = 501033,
Comment = "诱捕帽按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "诱捕帽：放置一个虚假的道具，诱捕伪装者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10147] = {
StepID = 10147,
GuideID = 501034,
Comment = "护盾按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "护盾：避免搜捕者的伤害",
UIOffset_Second = v15,
IsKeyStep = true
},
[10148] = {
StepID = 10148,
GuideID = 501035,
Comment = "变身器按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "变身器：随机变身为一个搜捕者的模样",
UIOffset_Second = v15,
IsKeyStep = true
},
[10149] = {
StepID = 10149,
GuideID = 501036,
Comment = "涂鸦手雷按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "涂鸦手雷：向搜捕者的屏幕涂鸦，遮挡其视野",
UIOffset_Second = v15,
IsKeyStep = true
},
[10150] = {
StepID = 10150,
GuideID = 501037,
Comment = "复制人偶按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "复制人偶：复制一个当前伪装物，迷惑搜捕者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10151] = {
StepID = 10151,
GuideID = 501038,
Comment = "传送门按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "传送门：放置一个传送门，可随机传送到其他位置",
UIOffset_Second = v15,
IsKeyStep = true
},
[10152] = {
StepID = 10152,
GuideID = 501039,
Comment = "诱捕帽按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "诱捕帽：放置一个虚假的道具，诱捕搜捕者",
UIOffset_Second = v15,
IsKeyStep = true
},
[10158] = {
StepID = 10158,
GuideID = 501045,
Comment = "雪人炮弹按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "雪人炮弹：变成雪人炮弹，撞上敌人把他们变成雪人！",
UIOffset_Second = v15,
IsKeyStep = true
},
[10159] = {
StepID = 10159,
GuideID = 501046,
Comment = "守护精灵按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "守护精灵：召唤守护精灵，被淘汰时可以重生一次。",
UIOffset_Second = v15,
IsKeyStep = true
},
[10160] = {
StepID = 10160,
GuideID = 501047,
Comment = "云朵按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "云朵：放置一朵可以踩的云朵，最多可以使用三次。",
UIOffset_Second = v15,
IsKeyStep = true
},
[10161] = {
StepID = 10161,
GuideID = 501048,
Comment = "砰砰锤按钮提示",
TypeParams = v6,
LifeTime = 12,
WindowName = v9,
WidgetName = v16,
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "砰砰锤：踩踏地面，将附近大范围内的伪装者振飞。",
UIOffset_Second = v15,
IsKeyStep = true
},
[10153] = {
StepID = 10153,
GuideID = 501040,
Comment = "阵营选择引导",
TypeParams = "ON_FactionSelectionClick",
LifeTime = 8,
WindowName = "UI_Model_MatchInfo",
WidgetName = "w_btn_Change",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y =0}",
UIStyle_Second = 1005,
UIText_Second = "点击这里，可以选择喜欢的局内身份哦。",
UIOffset_Second = v15,
IsKeyStep = true
},
[10201] = {
StepID = 10201,
GuideID = 502010,
Comment = "显示游戏提示",
StepType = 1,
IsForceGuide = true,
LifeTime = 12,
UIStyle_Frist = 1004,
UIText_Frist = "<Highlight31>目标：</>寻找并攻击那些星宝吧，小心警卫星宝！",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v4,
LockCDInSeconds = 3.0
},
[10203] = {
StepID = 10203,
GuideID = 502015,
Comment = "攻击按钮提示",
StepType = 1,
LifeTime = 12,
WindowName = "UI_InLevel_NR3E2_ControlPad_Bad",
WidgetName = "UI_E2_ButtonComponentWield",
GetButtonFunc = "GetAttackButton",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "使用棍棒，可以攻击别人",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10205] = {
StepID = 10205,
GuideID = 502011,
Comment = "显示游戏提示",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1004,
UIText_Frist = "<Highlight31>目标：</>保卫星宝，找出卧底并淘汰他们。",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v4,
LockCDInSeconds = 3.0
},
[10207] = {
StepID = 10207,
GuideID = 502012,
Comment = "显示游戏提示",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1004,
UIText_Frist = "<Highlight31>目标：</>努力的存活，小心并逃离卧底的攻击。",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v4,
LockCDInSeconds = 3.0
},
[10208] = {
StepID = 10208,
GuideID = 502013,
Comment = "显示游戏提示",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1004,
UIText_Frist = "寻找并捡起手枪，你就能成为新的警卫星宝。",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v4
},
[10204] = {
StepID = 10204,
GuideID = 502014,
Comment = "陷阱按钮提示",
StepType = 1,
LifeTime = 12,
WindowName = "UI_InLevel_NR3E2_ControlPad_Bad",
WidgetName = "UI_E2_ButtonComponentTrap",
GetButtonFunc = "GetTrapButton",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "布置陷阱，可以困住别人",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10209] = {
StepID = 10209,
GuideID = 502016,
Comment = "攻击按钮提示PC",
TypeParams = "ON_NR3E2_WIELD",
LifeTime = 12,
WindowName = "UI_InLevel_NR3E2_ControlPad_Bad",
WidgetName = "UI_E2_ButtonComponentWield",
GetButtonFunc = "GetAttackButton",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "按下F键，可以攻击别人",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[10210] = {
StepID = 10210,
GuideID = 502017,
Comment = "陷阱按钮提示PC",
TypeParams = "ON_NR3E2_PUTTRAP",
LifeTime = 12,
WindowName = "UI_InLevel_NR3E2_ControlPad_Bad",
WidgetName = "UI_E2_ButtonComponentTrap",
GetButtonFunc = "GetTrapButton",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "按下R键，布置陷阱，可以困住别人",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[20101] = {
StepID = 20101,
GuideID = 503010,
Comment = "平民准备阶段提示",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideHighlight31>目标：</><GuideGrey31>完成个人任务。小心！隐藏在人群中的狼人会攻击你。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20102] = {
StepID = 20102,
GuideID = 503036,
Comment = "平民箭头指引提示",
TypeParams = "ON_NR3E3_TouchTask",
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3",
GetWidgetFunc = "GetTaskBg",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=0, Y = 130}",
UIStyle_Second = 1006,
UIText_Second = "点击切换追踪的任务，箭头指向任务地点",
UIOffset_Second = "{X=-230, Y = 250}",
IsKeyStep = true
},
[20103] = {
StepID = 20103,
GuideID = 503011,
Comment = "平民会议提示",
StepType = 1,
IsForceGuide = true,
LifeTime = 10,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideGrey31>会议开始！一起分析、找出那些狼人，并投票驱逐他们。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20104] = {
StepID = 20104,
GuideID = 503012,
Comment = "平民淘汰提示",
TypeParams = "ON_NR3E3_CloseOutTip",
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = [[<GuideGrey31>出局啦，你现在成为了一个</><GuideHighlight31>幽灵</>。
<GuideGrey31>不过，你仍然可以继续进行任务，帮助活着的平民。</>]],
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20105] = {
StepID = 20105,
GuideID = 503013,
Comment = "狼人准备阶段提示",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideHighlight31>目标：</><GuideGrey31>淘汰掉所有的平民。注意伪装，隐藏自己的身份。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20107] = {
StepID = 20107,
GuideID = 503014,
Comment = "狼人会议提示",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideGrey31>会议开始！注意隐藏自己的身份，避免被投票驱逐。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20108] = {
StepID = 20108,
GuideID = 503015,
Comment = "狼人淘汰提示",
TypeParams = "ON_NR3E3_CloseOutTip",
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = [[<GuideGrey31>出局啦，你现在成为了一个</><GuideHighlight31>幽灵</>。
<GuideGrey31>你可以到处观察其他玩家，等待本局游戏的结果吧。</>]],
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20109] = {
StepID = 20109,
GuideID = 503016,
Comment = "平民首次触碰任务提示",
TypeParams = "ON_NR3E3_UseTouch",
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetUseIcon",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "点击使用，可以进行任务",
UIOffset_Second = v15,
IsKeyStep = true
},
[20110] = {
StepID = 20110,
GuideID = 503017,
Comment = "平民任务完成提示",
TypeParams = "ON_NR3E3_FirstTaskComplete_Over",
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = [[<GuideGrey31>完成了一个任务，太棒啦！</> 
<GuideGrey31>所有平民</><GuideHighlight31>共同累积</><GuideGrey31>任务进度，达到100%时便可获胜了。</>]],
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20111] = {
StepID = 20111,
GuideID = 503018,
Comment = "平民会议结束提示",
TypeParams = "ON_NR3E3_MeetingComplete_Over",
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideGrey31>如果成功驱逐了所有的狼人，平民将获得胜利。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20112] = {
StepID = 20112,
GuideID = 503019,
Comment = "平民会议语音提示",
TypeParams = "ON_NR3E3_VoiceTouch",
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_Meeting",
GetWidgetFunc = "GetVoiceIcon",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "长按可以进行语音发言",
UIOffset_Second = v15,
IsKeyStep = true
},
[20113] = {
StepID = 20113,
GuideID = 503020,
Comment = "平民会议提示",
StepType = 1,
IsForceGuide = true,
LifeTime = 10,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideGrey31>会议开始！一起分析、找出那些狼人，并投票驱逐他们。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20114] = {
StepID = 20114,
GuideID = 503021,
Comment = "狼人会议提示",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideGrey31>会议开始！注意隐藏自己的身份，避免被投票驱逐。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20115] = {
StepID = 20115,
GuideID = 503022,
Comment = "紧急任务提示",
TypeParams = "ON_NR3E3_EmergentTask",
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3",
GetWidgetFunc = "GetEmergentTaskBtn",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-70, Y = 80}",
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1006,
UIText_Second = "发起紧急任务，给平民制造点麻烦",
UIOffset_Second = "{X=-260, Y = 200}",
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20116] = {
StepID = 20116,
GuideID = 503023,
Comment = "举报提示",
TypeParams = "ON_NR3E3_ReportEnd",
LifeTime = 10,
WindowName = "UI_NR3E3_GameDetails",
GetWidgetFunc = "GetReportBtn",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "点击此处可举报玩家",
UIOffset_Second = "{X=-315, Y = -365}",
IsKeyStep = true
},
[20117] = {
StepID = 20117,
GuideID = 503024,
Comment = "狼人攻击提示",
TypeParams = "ON_NR3E3_AttackTouch",
LifeTime = 5,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetAttackIcon",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "攻击平民，可以淘汰他们",
UIOffset_Second = v15,
IsKeyStep = true
},
[20118] = {
StepID = 20118,
GuideID = 503025,
Comment = "新手局狼人首次攻击引导",
TypeParams = "ON_NR3E3_AttackTouch",
WindowName = "UI_InLevel_NR3E3",
GetWidgetFunc = "GetTaskBg",
UIStyle_Frist = 2002,
UIText_Frist = "<GuideGrey29>那边有个落单的平民，悄悄的走过去</><GuideHighlight29>攻击</><GuideGrey29>他吧。</>",
UIOffset_Frist = "{X=-180, Y = -100}",
IsKeyStep = true,
SfxID = 55002
},
[20119] = {
StepID = 20119,
GuideID = 503033,
Comment = "新手局狼人逃离引导",
TypeParams = "ON_NR3E3_NewComer_ArriveLeavePoint",
WindowName = "UI_InLevel_NR3E3",
GetWidgetFunc = "GetTaskBg",
UIStyle_Frist = 2002,
UIText_Frist = "<GuideGrey29>成功淘汰他啦！赶快离开现场，不要被别人发现了。</>",
UIOffset_Frist = "{X=-180, Y = -100}",
IsKeyStep = true,
SfxID = 55003
},
[20120] = {
StepID = 20120,
GuideID = 503026,
Comment = "新手局狼人二次攻击引导",
TypeParams = "ON_NR3E3_AttackTouch",
WindowName = "UI_InLevel_NR3E3",
GetWidgetFunc = "GetTaskBg",
UIStyle_Frist = 2002,
UIText_Frist = "<GuideGrey29>接下来，继续寻找并攻击那些落单的平民玩家吧。</>",
UIOffset_Frist = "{X=-180, Y = -100}",
IsKeyStep = true,
SfxID = 55006
},
[20121] = {
StepID = 20121,
GuideID = 503027,
Comment = "新手局狼人二次进入会议",
TypeParams = "ON_NR3E3_NewComer_SecondMeeting_FinishEnter",
WindowName = "UI_InLevel_NR3E3_Meeting",
GetWidgetFunc = "GetChatCanvas",
UIStyle_Frist = 2002,
UIText_Frist = "<GuideGrey29>这次你好像被发现了呢…</>",
UIOffset_Frist = "{X=-400, Y =-280}",
IsKeyStep = true,
SfxID = 55007
},
[20122] = {
StepID = 20122,
GuideID = 503028,
Comment = "新手局狼人二次进入会议1号玩家发言",
TypeParams = "ON_NR3E3_NewComer_SecondMeeting_FinishNo1Discuss",
WindowName = "UI_InLevel_NR3E3_Meeting",
GetWidgetFunc = "GetChatCanvas",
UIStyle_Frist = 2002,
UIText_Frist = "<GuideGrey29>狼人队友在为你辩解，你也可以发言表示自己不在现场。</>",
UIOffset_Frist = "{X=-400, Y =-280}",
IsKeyStep = true,
SfxID = 55008
},
[20123] = {
StepID = 20123,
GuideID = 503029,
Comment = "新手局狼人二次进入会议投票",
TypeParams = "ON_NR3E3_NewComer_SecondMeeting_FinishEnterVote",
WindowName = "UI_InLevel_NR3E3_Meeting",
GetWidgetFunc = "GetChatCanvas",
UIStyle_Frist = 2002,
UIText_Frist = "<GuideGrey29>看来大家怀疑4号是狼人，一起投票驱逐他吧。</>",
UIOffset_Frist = "{X=-400, Y =-280}",
IsKeyStep = true,
SfxID = 55009
},
[20124] = {
StepID = 20124,
GuideID = 503030,
Comment = "普通局狼人首次进入会议",
TypeParams = "ON_NR3E3_NewComer_FirstMeeting_FinishEnter",
IsForceGuide = true,
LifeTime = 9,
UIStyle_Frist = 1011,
UIText_Frist = [[<GuideGrey31>会议开始！大家会轮流进行发言。</>
<GuideGrey31>作为狼人，注意隐藏伪装自己的身份，避免被投票驱逐。</>]],
bAnyWhereNext = true,
IsKeyStep = true,
SfxID = 55004,
LockCDInSeconds = 3.0
},
[20125] = {
StepID = 20125,
GuideID = 503031,
Comment = "新手局狼人首次进入会议投票",
TypeParams = "ON_NR3E3_NewComer_FirstMeeting_FinishEnterVote",
WindowName = "UI_InLevel_NR3E3_Meeting",
GetWidgetFunc = "GetChatCanvas",
UIStyle_Frist = 2002,
UIText_Frist = "<GuideGrey29>看来大家怀疑3号是狼人，一起投票驱逐他吧。</>",
UIOffset_Frist = "{X=-400, Y =-280}",
IsKeyStep = true,
SfxID = 55005
},
[20126] = {
StepID = 20126,
GuideID = 503032,
Comment = "新手局狼人首次进入",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = [[<GuideGrey31>欢迎来到《谁是狼人》！现在，你是一名</><GuideHighlight31>狼人</><GuideGrey31>。</>
<GuideHighlight31>游戏目标：</><GuideGrey31>淘汰掉所有的平民。注意伪装隐藏自己的身份。</>]],
bAnyWhereNext = true,
IsKeyStep = true,
SfxID = 55001,
LockCDInSeconds = 3.0
},
[20127] = {
StepID = 20127,
GuideID = 503032,
Comment = "移动提示",
SubStep = 2,
TypeParams = "ON_NR3E3_Newcomer_Move_Finish",
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetJoystickBtn",
UIStyle_Frist = 1015,
UIOffset_Frist = "{X=-20, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "<GuideGrey29>推动摇杆，可进行移动</>",
UIOffset_Second = "{X=-200, Y = -450}",
IsKeyStep = true,
ButtonMode = v4
},
[20128] = {
StepID = 20128,
GuideID = 503032,
Comment = "调整镜头",
SubStep = 3,
TypeParams = "ON_NR3E3_Newcomer_AdjustCamera_Over",
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetJumpBtn",
UIStyle_Frist = 1008,
UIOffset_Frist = "{X=-400, Y = -450}",
UIStyle_Second = 1005,
UIText_Second = "<GuideGrey29>滑动此处调整镜头</>",
UIOffset_Second = "{X=-450, Y = -600}",
IsKeyStep = true,
ButtonMode = "pressed"
},
[20129] = {
StepID = 20129,
GuideID = 503034,
Comment = "点击选择地图",
StepType = 1,
WindowName = "UI_Preparations_View",
WidgetName = "w_btn_ModelSelect",
ButtonName = "w_btn_ModelSelect",
UIStyle_Frist = 1014,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = -160}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=-0, Y =0}",
UIStyle_Second = 1005,
UIText_Second = "点击选择地图",
UIOffset_Second = "{X=-270, Y = -360}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v4
},
[20130] = {
StepID = 20130,
GuideID = 503035,
Comment = "点击预选身份",
SubStep = 2,
TypeParams = "ON_NR3E3_IdentitySelectFinish",
WindowName = "UI_Preparations_View",
GetWidgetFunc = "GetIdentitySelectGuidItem",
ButtonName = "w_btn_ModelSelect1",
UIStyle_Frist = 1014,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = -160}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=-0, Y =0}",
UIStyle_Second = 1005,
UIText_Second = "点击预选身份",
UIOffset_Second = "{X=-270, Y = -360}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v4
},
[20131] = {
StepID = 20131,
GuideID = 503037,
Comment = "会议点击其他玩家",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_Meeting",
GetWidgetFunc = "GetOtherItem",
GetButtonFunc = "GetGuideItemBtn",
UIStyle_Frist = 1001,
UIText_Frist = "6",
UIOffset_Frist = "{X=300, Y = -80}",
UIStyle_Third = 2004,
UIOffset_Third = "{X=0, Y =0}",
UIStyle_Second = 1005,
UIText_Second = "试试互动道具吧",
UIOffset_Second = "{X=420, Y = -100}",
IsKeyStep = true,
ButtonMode = v4
},
[20132] = {
StepID = 20132,
GuideID = 503038,
Comment = "会议点击互动按钮",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_MeetingAction",
WidgetName = "UI_CommonBtn_ThrowProps",
ButtonName = "w_btn_CommonBtn",
UIStyle_Frist = 1001,
UIText_Frist = "6",
UIOffset_Frist = "{X=180, Y = -60}",
UIStyle_Second = 1005,
UIText_Second = "开始互动吧",
UIOffset_Second = "{X=260, Y = -80}",
IsKeyStep = true,
ButtonMode = v4
},
[20133] = {
StepID = 20133,
GuideID = 503039,
Comment = "会议点击自己",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_Meeting",
GetWidgetFunc = "GetSelfItem",
GetButtonFunc = "GetGuideItemBtn",
UIStyle_Frist = 1001,
UIText_Frist = "6",
UIOffset_Frist = "{X=300, Y = -80}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=120, Y =0}",
UIStyle_Second = 1005,
UIText_Second = "试试会议表情吧",
UIOffset_Second = "{X=420, Y = -100}",
IsKeyStep = true,
ButtonMode = v4
},
[20134] = {
StepID = 20134,
GuideID = 503040,
Comment = "点击选择地图1",
TypeParams = "ON_NR3E3_ClickModelSelect",
WindowName = "UI_Preparations_View",
GetWidgetFunc = "GetMapSelectGuidItem",
UIStyle_Frist = 1014,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = -160}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=-0, Y =0}",
UIStyle_Second = 1005,
UIText_Second = "点击更换模式",
UIOffset_Second = "{X=-270, Y = -360}",
IsKeyStep = true
},
[20135] = {
StepID = 20135,
GuideID = 503041,
Comment = "双人规则提示引导",
StepType = 1,
WindowName = "UI_InLevel_NR3E3",
GetWidgetFunc = "GetSetRuleBtn",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-60, Y =70}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=-0, Y =0}",
UIStyle_Second = 1005,
UIText_Second = "来了解下双人模式的规则吧",
UIOffset_Second = "{X=-270, Y = 160}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v4
},
[20137] = {
StepID = 20137,
GuideID = 503042,
Comment = "新手局狼人首次进入pc用",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = [[<GuideGrey31>欢迎来到《谁是狼人》！现在，你是一名</><GuideHighlight31>狼人</><GuideGrey31>。</>
<GuideHighlight31>游戏目标：</><GuideGrey31>淘汰掉所有的平民。注意伪装隐藏自己的身份。</>]],
bAnyWhereNext = true,
IsKeyStep = true,
SfxID = 55001,
LockCDInSeconds = 3.0
},
[20138] = {
StepID = 20138,
GuideID = 503043,
Comment = "狼人准备阶段提示",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideHighlight31>目标：</><GuideGrey31>淘汰掉所有的平民。注意伪装，隐藏自己的身份。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20139] = {
StepID = 20139,
GuideID = 503044,
Comment = "紧急任务提示",
TypeParams = "ON_NR3E3_EmergentTask",
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3",
GetWidgetFunc = "GetEmergentTaskBtn",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-70, Y = 80}",
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1006,
UIText_Second = "按下N键发起紧急任务，给平民制造点麻烦",
UIOffset_Second = "{X=-260, Y = 200}",
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20140] = {
StepID = 20140,
GuideID = 503045,
Comment = "狼人攻击提示",
TypeParams = "ON_NR3E3_AttackTouch",
LifeTime = 5,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetAttackIcon",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "使用鼠标右键可以攻击平民，淘汰他们",
UIOffset_Second = v15,
IsKeyStep = true
},
[20141] = {
StepID = 20141,
GuideID = 503046,
Comment = "平民首次触碰任务提示",
TypeParams = "ON_NR3E3_UseTouch",
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetUseIcon",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v13,
UIStyle_Second = 1005,
UIText_Second = "按下F键，可以进行任务",
UIOffset_Second = v15,
IsKeyStep = true
},
[20142] = {
StepID = 20142,
GuideID = 503042,
Comment = "移动提示",
SubStep = 2,
TypeParams = "ON_NR3E3_Newcomer_Move_Finish",
LifeTime = 5,
WindowName = "UI_InLevel_NR3E3",
GetWidgetFunc = "GetTaskArrowBtn",
UIStyle_Second = 1005,
UIText_Second = "<GuideGrey29>按键WASD，可进行移动</>",
UIOffset_Second = "{X=-50, Y = 50}",
IsKeyStep = true
},
[20143] = {
StepID = 20143,
GuideID = 503042,
Comment = "调整镜头",
SubStep = 3,
TypeParams = "ON_NR3E3_Newcomer_AdjustCamera_Over",
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetJumpBtn",
UIStyle_Frist = 1008,
UIOffset_Frist = "{X=-400, Y = -450}",
UIStyle_Second = 1005,
UIText_Second = "<GuideGrey29>滑动鼠标调整镜头</>",
UIOffset_Second = "{X=-450, Y = -600}",
IsKeyStep = true,
ButtonMode = "pressed"
},
[20144] = {
StepID = 20144,
GuideID = 503047,
Comment = "狼人会议提示PC",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideGrey31>会议开始！注意隐藏自己的身份，避免被投票驱逐。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20145] = {
StepID = 20145,
GuideID = 503048,
Comment = "平民会议提示PC",
StepType = 1,
IsForceGuide = true,
LifeTime = 10,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideGrey31>会议开始！一起分析、找出那些狼人，并投票驱逐他们。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20146] = {
StepID = 20146,
GuideID = 503049,
Comment = "平民会议语音提示PC",
TypeParams = "ON_NR3E3_VoiceTouch",
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_Meeting",
GetWidgetFunc = "GetVoiceIcon",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "长按T键，可以进行语音发言",
UIOffset_Second = v15,
IsKeyStep = true
},
[20147] = {
StepID = 20147,
GuideID = 503050,
Comment = "备战界面引导返回大厅按钮",
StepType = 1,
WindowName = "UI_Preparations_View",
WidgetName = "w_btn_HidePreparations",
UIStyle_Frist = 1001,
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Second = 1005,
UIText_Second = "点击收起备战界面",
UIOffset_Second = "{X=-250, Y = 160}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v4,
bOtherButtonComplete = true
},
[20148] = {
StepID = 20148,
GuideID = 503051,
Comment = "狼人管道提示PC",
StepType = 1,
LifeTime = 10,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideGrey31>太棒了！进入密道隐藏起来，可以左右切换与其相连的所有密道哦。</>",
bAnyWhereNext = true,
IsKeyStep = true,
LockCDInSeconds = 3.0
},
[20149] = {
StepID = 20149,
GuideID = 503052,
Comment = "狼人秘境寻宝(平民阵营)开局开局引导",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideGrey31>这里是海岛遗迹，埋藏着各种宝物，让我们开始搜寻吧~</>",
bAnyWhereNext = true,
IsKeyStep = true
},
[20150] = {
StepID = 20150,
GuideID = 503053,
Comment = "狼人秘境寻宝(平民阵营)开局挖宝引导",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetUseBtn",
ButtonName = "w_btn_vocationClick",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "点击挖宝，可以挖出宝物",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[20151] = {
StepID = 20151,
GuideID = 503054,
Comment = "狼人秘境寻宝(平民阵营)开局搬起引导",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetUseBtn",
ButtonName = "w_btn_vocationClick",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "点击搬起，可以搬运宝物",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[20152] = {
StepID = 20152,
GuideID = 503055,
Comment = "狼人秘境寻宝(平民阵营)开局上交引导-查看地图",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3",
GetWidgetFunc = "GetOpenMapBtn",
ButtonName = "w_btn_OpenMap",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-80, Y =100}",
UIStyle_Second = 1005,
UIText_Second = "宝物需要交给吴小宝",
UIOffset_Second = "{X=-380, Y = 210}",
IsKeyStep = true,
ButtonMode = v4
},
[20153] = {
StepID = 20153,
GuideID = 503056,
Comment = "狼人秘境寻宝(平民阵营)开局上交引导-查看NPC",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_Map",
GetWidgetFunc = "GetVendorIcon",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "吴小宝在营地等我们",
UIOffset_Second = v15,
bAnyWhereNext = true,
IsKeyStep = true
},
[20154] = {
StepID = 20154,
GuideID = 503057,
Comment = "狼人秘境寻宝(平民阵营)开局上交宝物引导",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = [[<GuideGrey31>上交了一个宝物，太棒啦！</>
<GuideGrey31>所有人</><GuideHighlight31>共同累积</><GuideGrey31>宝物进度，达到100%时平民便可获胜了。</>]],
bAnyWhereNext = true,
IsKeyStep = true
},
[20155] = {
StepID = 20155,
GuideID = 503058,
Comment = "狼人秘境寻宝(平民阵营)开局查看小店引导",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetTreasureShop",
ButtonName = "w_btn_vocationClick",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "点击小店，可以购买道具",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[20156] = {
StepID = 20156,
GuideID = 503059,
Comment = "狼人秘境寻宝(平民阵营)开局查看道具引导",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetVenderSkill",
ButtonName = "w_btn_vocationClick",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = "{X=-70, Y = -260}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=-22.0, Y = -22.0}",
UIStyle_Second = 1005,
UIText_Second = "点击道具，可以查看道具",
UIOffset_Second = "{X=-300, Y =-400}",
IsKeyStep = true,
ButtonMode = v4
},
[20157] = {
StepID = 20157,
GuideID = 503060,
Comment = "狼人秘境寻宝(狼人阵营)开局开局引导",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "<GuideGrey31>这里是海岛遗迹，埋藏着各种宝物，让我们开始搜寻吧~</>",
bAnyWhereNext = true,
IsKeyStep = true
},
[20158] = {
StepID = 20158,
GuideID = 503061,
Comment = "狼人秘境寻宝(狼人阵营)开局挖宝引导",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetUseBtn",
ButtonName = "w_btn_vocationClick",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "点击挖宝，可以挖出宝物",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[20159] = {
StepID = 20159,
GuideID = 503062,
Comment = "狼人秘境寻宝(狼人阵营)搬起引导",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetUseBtn",
ButtonName = "w_btn_vocationClick",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "点击搬起，可以搬运宝物",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[20160] = {
StepID = 20160,
GuideID = 503063,
Comment = "狼人秘境寻宝(狼人阵营)上交引导-查看地图",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3",
GetWidgetFunc = "GetOpenMapBtn",
ButtonName = "w_btn_OpenMap",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-80, Y =100}",
UIStyle_Second = 1005,
UIText_Second = "宝物需要交给吴小宝",
UIOffset_Second = "{X=-380, Y = 210}",
IsKeyStep = true,
ButtonMode = v4
},
[20161] = {
StepID = 20161,
GuideID = 503064,
Comment = "狼人秘境寻宝(狼人阵营)上交引导-查看NPC",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_Map",
GetWidgetFunc = "GetVendorIcon",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "吴小宝在营地等我们",
UIOffset_Second = v15,
bAnyWhereNext = true,
IsKeyStep = true
},
[20162] = {
StepID = 20162,
GuideID = 503065,
Comment = "狼人秘境寻宝(狼人阵营)上交宝物引导",
StepType = 1,
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = [[<GuideGrey31>上交了一个宝物，太棒啦！</>
<GuideGrey31>所有人</><GuideHighlight31>共同累积</><GuideGrey31>宝物进度，达到100%时平民便可获胜了。</>]],
bAnyWhereNext = true,
IsKeyStep = true
},
[20163] = {
StepID = 20163,
GuideID = 503066,
Comment = "狼人秘境寻宝(狼人阵营)查看小店引导",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetTreasureShop",
ButtonName = "w_btn_vocationClick",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = v10,
UIStyle_Third = 1002,
UIOffset_Third = v12,
UIStyle_Second = 1005,
UIText_Second = "点击小店，可以购买道具",
UIOffset_Second = v15,
IsKeyStep = true,
ButtonMode = v4
},
[20164] = {
StepID = 20164,
GuideID = 503067,
Comment = "狼人秘境寻宝(狼人阵营)查看道具引导",
StepType = 1,
LifeTime = 10,
WindowName = "UI_InLevel_NR3E3_ControlPad_Vocation",
GetWidgetFunc = "GetVenderSkill",
ButtonName = "w_btn_vocationClick",
UIStyle_Frist = 1001,
UIText_Frist = v3,
UIOffset_Frist = "{X=-70, Y = -260}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=-22.0, Y = -22.0}",
UIStyle_Second = 1005,
UIText_Second = "点击道具，可以查看道具",
UIOffset_Second = "{X=-300, Y =-400}",
IsKeyStep = true,
ButtonMode = v4
}
}

local mt = {
SubStep = 1,
StepType = 6,
IsForceGuide = false,
bAnyWhereNext = false,
bOtherButtonExit = false,
bMask = false,
bShowSkipButton = false,
IsKeyStep = false,
bOtherButtonComplete = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
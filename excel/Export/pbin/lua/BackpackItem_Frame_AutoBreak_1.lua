--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 头像框

local v0 = 3

local v1 = 0

local v2 = {
{
itemId = 13,
itemNum = 100
}
}

local data = {
[840114] = {
id = 840114,
quality = 3,
name = "找搭子",
desc = "闪耀盛夏活动获得",
icon = "CDN:T_HeadFrame_114",
isDynamic = 0,
beginTime = {
seconds = 1718294400
}
},
[840115] = {
id = 840115,
lowVer = "1.3.7.1",
name = "逐浪之歌",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_115",
beginTime = {
seconds = 1717689600
}
},
[840116] = {
id = 840116,
lowVer = "1.3.7.1",
name = "缤纷夏日",
desc = "缤纷夏日赛季限时活动获得",
icon = "D_HeadFrame_116",
beginTime = {
seconds = 1717689600
}
},
[840109] = {
id = 840109,
name = "布朗熊",
desc = "时光小船祈愿获得",
icon = "CDN:T_HeadFrame_109",
isDynamic = 0,
beginTime = {
seconds = 1717171200
}
},
[840123] = {
id = 840123,
quality = 3,
name = "【限时】卧底高手",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_HeadFrame_123",
isDynamic = 0,
beginTime = {
seconds = 1718899200
}
},
[840117] = {
id = 840117,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 6
}
},
name = "闪耀之冕",
desc = "参加《闪电赛》活动，积累闪电奖章获得",
icon = "D_HeadFrame_117",
beginTime = {
seconds = 1717689600
}
},
[840118] = {
id = 840118,
lowVer = "1.3.7.1",
quality = 3,
name = "夏日海歌",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_118",
isDynamic = 0,
beginTime = {
seconds = 1717689600
}
},
[840119] = {
id = 840119,
lowVer = "1.3.7.1",
quality = 4,
name = "沙滩造梦",
desc = "缤纷夏日通行证获得",
icon = "CDN:T_HeadFrame_119",
isDynamic = 0,
beginTime = {
seconds = 1717689600
}
},
[840120] = {
id = 840120,
lowVer = "1.3.7.1",
quality = 3,
name = "珊瑚游弋",
desc = "缤纷夏日通行证获得",
icon = "CDN:T_HeadFrame_120",
isDynamic = 0,
beginTime = {
seconds = 1717689600
}
},
[840501] = {
id = 840501,
exceedReplaceItem = v2,
name = "秘术狼",
desc = "在【谁是狼人】玩法的兑换商店中获得",
icon = "D_HeadFrame_501",
showInView = 0
},
[840502] = {
id = 840502,
exceedReplaceItem = v2,
name = "小丑",
desc = "在【谁是狼人】玩法的大师之路中获得",
icon = "D_HeadFrame_502",
showInView = 0
},
[840503] = {
id = 840503,
exceedReplaceItem = v2,
name = "法医",
icon = "D_HeadFrame_503",
showInView = 0
},
[840504] = {
id = 840504,
exceedReplaceItem = v2,
name = "赌徒",
desc = "第6赛季狼人通行证中获得",
icon = "D_HeadFrame_504",
showInView = 0
},
[840505] = {
id = 840505,
exceedReplaceItem = v2,
name = "占星师",
icon = "D_HeadFrame_505",
showInView = 0
},
[840506] = {
id = 840506,
exceedReplaceItem = v2,
name = "天使",
desc = "限时祈愿获得",
icon = "D_HeadFrame_506",
showInView = 0
},
[840507] = {
id = 840507,
exceedReplaceItem = v2,
name = "警长",
desc = "在【谁是狼人】玩法的大师之路中获得",
icon = "D_HeadFrame_507",
showInView = 0
},
[840508] = {
id = 840508,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "刺客狼",
desc = "限时祈愿获得",
icon = "D_HeadFrame_508",
showInView = 0
},
[840509] = {
id = 840509,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "流浪汉",
desc = "礼包获得",
icon = "D_HeadFrame_509",
showInView = 0
},
[840510] = {
id = 840510,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "法官",
desc = "第8赛季狼人通行证中获得",
icon = "D_HeadFrame_510",
showInView = 0
},
[840511] = {
id = 840511,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "千里眼",
icon = "D_HeadFrame_511",
showInView = 0
},
[840512] = {
id = 840512,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "通灵师",
desc = "第7赛季狼人通行证中获得",
icon = "D_HeadFrame_512",
showInView = 0
},
[840513] = {
id = 840513,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "监听员",
icon = "D_HeadFrame_513",
showInView = 0
},
[840514] = {
id = 840514,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "黑客",
desc = "第12赛季狼人通行证中获得",
icon = "D_HeadFrame_514",
showInView = 0
},
[840515] = {
id = 840515,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "侦探",
desc = "第9赛季狼人通行证中获得",
icon = "D_HeadFrame_515",
showInView = 0
},
[840516] = {
id = 840516,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "伪装狼",
desc = "限时祈愿获得",
icon = "D_HeadFrame_516",
showInView = 0
},
[840517] = {
id = 840517,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "炸弹狼",
desc = "限时祈愿获得",
icon = "D_HeadFrame_517",
showInView = 0
},
[840518] = {
id = 840518,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "潜行狼",
desc = "限时祈愿获得",
icon = "D_HeadFrame_518",
showInView = 0
},
[840519] = {
id = 840519,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "独狼",
desc = "第11赛季狼人通行证中获得",
icon = "D_HeadFrame_519",
showInView = 0
},
[840520] = {
id = 840520,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "大力狼",
desc = "限时祈愿获得",
icon = "D_HeadFrame_520",
showInView = 0
},
[840521] = {
id = 840521,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "魔爆狼",
desc = "限时祈愿获得",
icon = "D_HeadFrame_521",
showInView = 0
},
[840522] = {
id = 840522,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "赏金猎人",
desc = "第10赛季狼人通行证中获得",
icon = "D_HeadFrame_522",
showInView = 0
},
[840523] = {
id = 840523,
exceedReplaceItem = v2,
lowVer = "1.3.78.1",
name = "臭鼬",
desc = "限时祈愿获得",
icon = "D_HeadFrame_523",
showInView = 0
},
[840531] = {
id = 840531,
exceedReplaceItem = v2,
lowVer = "1.3.78.1",
name = "学者",
desc = "第13赛季狼人通行证中获得",
icon = "D_HeadFrame_531",
showInView = 0
},
[840533] = {
id = 840533,
exceedReplaceItem = v2,
lowVer = "1.3.78.1",
name = "大明星",
icon = "D_HeadFrame_533",
showInView = 0
},
[840536] = {
id = 840536,
exceedReplaceItem = v2,
lowVer = "1.3.78.1",
name = "幽灵狼",
icon = "D_HeadFrame_536",
showInView = 0
},
[840544] = {
id = 840544,
exceedReplaceItem = v2,
lowVer = "1.3.78.1",
name = "捕梦者",
icon = "D_HeadFrame_544",
showInView = 0
},
[840126] = {
id = 840126,
lowVer = "1.3.7.1",
quality = 3,
name = "夏日冰柠",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_126",
isDynamic = 0,
beginTime = {
seconds = 1717689600
}
},
[840127] = {
id = 840127,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 6
}
},
lowVer = "1.3.7.47",
name = "辉耀之光",
desc = "战神颂歌祈愿活动获得",
icon = "D_HeadFrame_130",
beginTime = {
seconds = 1718899200
}
},
[840128] = {
id = 840128,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 6
}
},
lowVer = "1.3.7.47",
name = "烈焰之心",
desc = "战神颂歌祈愿活动获得",
icon = "D_HeadFrame_129",
beginTime = {
seconds = 1718899200
}
},
[840129] = {
id = 840129,
lowVer = "1.3.7.47",
name = "剑启黎明",
desc = "战神颂歌祈愿活动获得",
icon = "D_HeadFrame_128",
beginTime = {
seconds = 1718899200
}
},
[840130] = {
id = 840130,
name = "Hello Kitty的爱",
desc = "寻梦嘉年华活动获得",
icon = "CDN:T_HeadFrame_124",
isDynamic = 0
},
[840131] = {
id = 840131,
quality = 4,
name = "校园新星",
desc = "高校创作大赛获取  ",
icon = "CDN:T_HeadFrame_131",
isDynamic = 0
},
[840132] = {
id = 840132,
quality = 3,
name = "校园明星",
desc = "高校创作大赛获取  ",
icon = "CDN:T_HeadFrame_132",
isDynamic = 0
},
[840133] = {
id = 840133,
quality = 3,
name = "星世界鉴赏家",
desc = "星世界鉴赏家活动获得",
icon = "CDN:T_HeadFrame_108",
isDynamic = 0
},
[840121] = {
id = 840121,
quality = 3,
name = "星宝守护者",
desc = "星宝守护行动获得",
icon = "CDN:T_HeadFrame_121",
isDynamic = 0,
beginTime = {
seconds = 1717689600
}
},
[840122] = {
id = 840122,
quality = 3,
name = "星宝守护者",
desc = "星宝守护行动获得",
icon = "CDN:T_HeadFrame_122",
isDynamic = 0,
beginTime = {
seconds = 1717689600
}
},
[840134] = {
id = 840134,
lowVer = "1.3.7.75",
quality = 3,
name = "小肥柴",
desc = "新品来献礼活动获得",
icon = "CDN:T_HeadFrame_133",
isDynamic = 0,
beginTime = {
seconds = 1719590400
}
},
[840135] = {
id = 840135,
lowVer = "1.3.7.75",
name = "三丽鸥",
desc = "限时活动获得",
icon = "D_HeadFrame_125",
beginTime = {
seconds = 1720108800
}
},
[840136] = {
id = 840136,
quality = 3,
name = "星宝半岁礼",
desc = "你的声音我听得见活动获得",
icon = "CDN:T_HeadFrame_134",
isDynamic = 0
},
[840137] = {
id = 840137,
quality = 3,
name = "中轴守护者",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_136",
isDynamic = 0
},
[840138] = {
id = 840138,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.7.90",
name = "心动启程",
desc = "浪漫旅程祈愿获得",
icon = "D_HeadFrame_137",
beginTime = {
seconds = 1720195200
}
},
[840139] = {
id = 840139,
quality = 3,
name = "铠-光束和鸣",
desc = "峡谷竞技活动获得",
icon = "CDN:T_Arena_HeadFrame_001",
isDynamic = 0
},
[840140] = {
id = 840140,
lowVer = "1.3.7.97",
name = "半周年庆",
desc = "限时活动获得",
icon = "D_HeadFrame_135",
beginTime = {
seconds = 1719936000
}
},
[840141] = {
id = 840141,
quality = 3,
name = "星杯",
desc = "冠军，当然与众不同",
icon = "CDN:T_HeadFrame_139",
isDynamic = 0
},
[840142] = {
id = 840142,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
name = "甄嬛传",
desc = "甄嬛传祈愿活动获得",
icon = "CDN:T_HeadFrame_138",
isDynamic = 0,
beginTime = {
seconds = 1720713600
}
},
[840143] = {
id = 840143,
lowVer = "1.3.12.1",
quality = 4,
name = "乐乐",
desc = "甜心乐园通行证获得",
icon = "CDN:T_HeadFrame_143",
isDynamic = 0,
beginTime = {
seconds = 1721318400
}
},
[840144] = {
id = 840144,
lowVer = "1.3.12.1",
quality = 3,
name = "美美",
desc = "甜心乐园通行证获得",
icon = "CDN:T_HeadFrame_144",
isDynamic = 0,
beginTime = {
seconds = 1721318400
}
},
[840145] = {
id = 840145,
lowVer = "1.3.12.1",
name = "甜心乐园",
desc = "甜心乐园赛季限时活动获得",
icon = "D_HeadFrame_140",
beginTime = {
seconds = 1721318400
}
},
[840146] = {
id = 840146,
lowVer = "1.3.12.1",
name = "心愿摩天轮",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_142",
beginTime = {
seconds = 1721318400
}
},
[840147] = {
id = 840147,
lowVer = "1.3.12.1",
quality = 3,
name = "全糖梦境",
desc = "赛季冲段任务获得",
icon = "CDN:T_HeadFrame_141",
isDynamic = 0,
beginTime = {
seconds = 1721318400
}
},
[840148] = {
id = 840148,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.12.1",
quality = 3,
name = "勇者之冠",
desc = "王冠商铺兑换中获得",
icon = "CDN:T_HeadFrame_146",
isDynamic = 0
},
[840149] = {
id = 840149,
quality = 3,
name = "蔡文姬-落花朝",
desc = "峡谷相逢通行证获得",
icon = "CDN:T_Arena_HeadFrame_002",
showInView = 0,
isDynamic = 0
},
[840151] = {
id = 840151,
quality = 3,
name = "TOBY",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_145",
isDynamic = 0,
beginTime = {
seconds = 1722528000
}
},
[840152] = {
id = 840152,
quality = 3,
name = "星贵妃",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_149",
isDynamic = 0
},
[840153] = {
id = 840153,
lowVer = "1.3.12.36",
name = "长相思",
desc = "限时活动获得",
icon = "D_HeadFrame_148",
beginTime = {
seconds = 1721836800
}
},
[840154] = {
id = 840154,
quality = 3,
name = "【限时】泡泡大王",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_HeadFrame_152",
isDynamic = 0,
beginTime = {
seconds = 1722528000
}
},
[840155] = {
id = 840155,
quality = 3,
name = "夺金时刻",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_147",
isDynamic = 0,
beginTime = {
seconds = 1722528000
}
},
[840156] = {
id = 840156,
lowVer = "1.3.12.118",
name = "凤凰于飞",
desc = "夏夜绮梦祈愿活动获得",
icon = "D_HeadFrame_155"
},
[840157] = {
id = 840157,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.12.92",
name = "凤求凰",
desc = "凤求凰祈愿活动获得",
icon = "D_HeadFrame_151"
},
[840158] = {
id = 840158,
quality = 3,
name = "星宝守护者",
desc = "星宝守护行动获得",
icon = "CDN:T_HeadFrame_153",
isDynamic = 0
},
[840160] = {
id = 840160,
quality = 3,
name = "造梦手艺人",
desc = "星世界创作周报获得",
icon = "CDN:T_HeadFrame_083",
isDynamic = 0
},
[840161] = {
id = 840161,
quality = 3,
name = "造梦小蜜蜂",
desc = "星世界创作周报获得",
icon = "CDN:T_HeadFrame_085",
isDynamic = 0
},
[840159] = {
id = 840159,
quality = 3,
name = "【限时】峡谷3V3",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_Arena_HeadFrame_999",
isDynamic = 0,
beginTime = {
seconds = 1722528000
}
},
[840162] = {
id = 840162,
lowVer = "1.3.7.75",
name = "洋葱牛奶",
desc = "洋葱牛奶祈愿中获得",
icon = "D_HeadFrame_156",
beginTime = {
seconds = 1725033600
}
},
[840163] = {
id = 840163,
lowVer = "1.3.18.1",
quality = 4,
name = "盆景之花",
desc = "精灵之森通行证获得",
icon = "CDN:T_HeadFrame_160",
isDynamic = 0,
beginTime = {
seconds = 1724947200
}
},
[840164] = {
id = 840164,
lowVer = "1.3.18.1",
quality = 3,
name = "树墩上的故事",
desc = "精灵之森通行证获得",
icon = "CDN:T_HeadFrame_159",
isDynamic = 0,
beginTime = {
seconds = 1724947200
}
},
[840165] = {
id = 840165,
lowVer = "1.3.12.1",
quality = 3,
name = "冒险之旅",
desc = "百变小新祈愿获得",
icon = "CDN:T_HeadFrame_158",
isDynamic = 0,
beginTime = {
seconds = 1724342400
}
},
[840166] = {
id = 840166,
lowVer = "1.3.12.1",
quality = 3,
name = "金桂飘香",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_157",
isDynamic = 0,
beginTime = {
seconds = 1724947200
}
},
[840167] = {
id = 840167,
lowVer = "1.3.18.1",
name = "精灵之森",
desc = "精灵之森赛季限时活动获得",
icon = "D_HeadFrame_161",
beginTime = {
seconds = 1724947200
}
},
[840168] = {
id = 840168,
lowVer = "1.3.18.1",
name = "小太阳头像框",
desc = "王冠商铺兑换中获得",
icon = "CDN:T_HeadFrame_165",
isDynamic = 0,
beginTime = {
seconds = 1724947200
}
},
[840169] = {
id = 840169,
lowVer = "1.3.18.1",
name = "鲜花盛开",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_163",
beginTime = {
seconds = 1724947200
}
},
[840170] = {
id = 840170,
lowVer = "1.3.18.1",
quality = 3,
name = "花束精灵",
desc = "赛季冲段任务获得",
icon = "CDN:T_HeadFrame_162",
isDynamic = 0,
beginTime = {
seconds = 1724947200
}
},
[840171] = {
id = 840171,
lowVer = "1.3.18.37",
quality = 3,
name = "浪漫清晨",
desc = "星光剧场祈愿获得",
icon = "CDN:T_HeadFrame_164",
isDynamic = 0,
beginTime = {
seconds = 4091702400
}
},
[840172] = {
id = 840172,
name = "齐天大圣",
desc = "西行之路祈愿活动获得",
icon = "D_HeadFrame_168"
},
[840173] = {
id = 840173,
name = "金蝉子",
desc = "西行之路祈愿活动获得",
icon = "D_HeadFrame_167"
},
[840174] = {
id = 840174,
quality = 3,
name = "龙腾舞跃",
desc = "拼团享好礼限时活动获得",
icon = "CDN:T_HeadFrame_166",
isDynamic = 0,
beginTime = {
seconds = 1735920000
}
},
[840175] = {
id = 840175,
lowVer = "1.3.18.37",
name = "皎月吹纱",
desc = "桂月清平祈愿获得",
icon = "D_HeadFrame_169",
beginTime = {
seconds = 1726156800
}
},
[840176] = {
id = 840176,
lowVer = "1.3.18.37",
name = "猫猫已老实",
desc = "限时活动获得",
icon = "D_HeadFrame_171",
beginTime = {
seconds = 1726761600
}
},
[840177] = {
id = 840177,
lowVer = "1.3.18.69",
quality = 3,
name = "风华鼓舞",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_173",
isDynamic = 0,
beginTime = {
seconds = 1727625600
}
},
[840178] = {
id = 840178,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.18.71",
name = "青龙戏珠",
desc = "青霄龙吟祈愿获得",
icon = "D_HeadFrame_172",
beginTime = {
seconds = 1727625600
}
},
[840179] = {
id = 840179,
name = "泡泡玛特",
desc = "泡泡玛特祈愿获得",
icon = "CDN:T_HeadFrame_170",
isDynamic = 0,
beginTime = {
seconds = 1726156800
}
},
[840180] = {
id = 840180,
lowVer = "1.3.18.72",
name = "三彩逸士",
desc = "千都三彩祈愿活动获得",
icon = "D_HeadFrame_174"
},
[840181] = {
id = 840181,
lowVer = "1.3.18.69",
quality = 3,
name = "吾皇猫",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_175",
isDynamic = 0,
beginTime = {
seconds = 1724947200
}
},
[840182] = {
id = 840182,
lowVer = "1.3.26.1",
quality = 4,
name = "星际怪盗",
desc = "星语星愿通行证获得",
icon = "CDN:T_HeadFrame_177",
isDynamic = 0,
beginTime = {
seconds = 1729180800
}
},
[840183] = {
id = 840183,
lowVer = "1.3.26.1",
quality = 3,
name = "星辰探险",
desc = "星语星愿通行证获得",
icon = "CDN:T_HeadFrame_178",
isDynamic = 0,
beginTime = {
seconds = 1729180800
}
},
[840184] = {
id = 840184,
lowVer = "1.3.26.1",
name = "星汉灿烂",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_180",
beginTime = {
seconds = 1729180800
}
},
[840185] = {
id = 840185,
lowVer = "1.3.26.1",
name = "星语星愿",
desc = "星语星愿赛季限时活动获得",
icon = "D_HeadFrame_179",
beginTime = {
seconds = 1729180800
}
},
[840186] = {
id = 840186,
lowVer = "1.3.26.1",
quality = 3,
name = "星星头像框",
desc = "王冠商铺兑换中获得",
icon = "CDN:T_HeadFrame_183",
isDynamic = 0,
beginTime = {
seconds = 1729180800
}
},
[840187] = {
id = 840187,
lowVer = "1.3.26.1",
quality = 3,
name = "披星戴月",
desc = "赛季冲段任务获得",
icon = "CDN:T_HeadFrame_181",
isDynamic = 0,
beginTime = {
seconds = 1729180800
}
},
[840188] = {
id = 840188,
lowVer = "1.3.26.1",
quality = 3,
name = "冬日暖茶",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_182",
isDynamic = 0,
beginTime = {
seconds = 1729180800
}
}
}

local mt = {
type = "ItemType_Frame",
maxNum = 1,
quality = 2,
showInView = 1,
isDynamic = 1
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
--com.tencent.wea.xlsRes.table_MatchTypeData => excel/xls/COC/W_玩法模式_COC.xlsx: 玩法

local data = {
[1201] = {
id = 1201,
modeID = 3,
desc = "星宝部落",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 13,
thumbImage = "T_UI_COC_ModelSelect_Img_Type_01",
image = "CDN:T_UI_COC_ModelSelectLarge_Img_Type_01",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "91",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 808,
TeamMatchGame = true,
battlePlayerNum = 1,
dropId = 22,
mmrAggrType = "MSAT_WeightedAvg",
isShowBattleRecord = true,
descShort = "建造家园，养成防御，与其他星宝趣味互动！",
battleRecordStyle = "UI_OMD_PlayerInfo_ModRecord_TitleBattleResult",
outImage = "T_Lobby_Start_OMD",
pakGroup = 20083,
recordType = 506,
buttonDesc = "单人",
gameTypeId = 11502,
layoutID = 11,
isShowEmotionEntrance = 2,
linkID = 5200,
isHiddenWhenClose = true,
isOpenGrayScale = true,
openPercent = 100,
UseDefaultChampionDisplayScene = true,
AutoDownloadPakGroup = {
20083
},
AutoDeletePakGroup = {
20083
},
PakPlayID = 1001,
playName = "COC"
}
}

local mt = {
isPermanent = false,
TeamMatchGame = false,
isShowBattleRecord = false,
isHiddenWhenClose = false,
isOpenGrayScale = false,
UseDefaultChampionDisplayScene = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
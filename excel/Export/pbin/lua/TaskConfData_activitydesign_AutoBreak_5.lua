--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/H_活动中心配置_策划专用.xlsx: 活动任务

local data = {
[440991] = {
id = 440991,
name = "答题-星宝小课堂",
desc = "本周累计在自习室答对20道题",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110010
}
}
}
}
}
}
},
jumpId = 726,
taskGroupId = 46023
},
[440992] = {
id = 440992,
name = "快闪-快闪小明星",
desc = "抱1次穿着chikawa皮肤的星宝",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110010
}
}
}
}
}
}
},
jumpId = 726,
taskGroupId = 46023
},
[440993] = {
id = 440993,
name = "娱乐-欢乐小游戏",
desc = "体验任意1局欢乐小游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110010
}
}
}
}
}
}
},
jumpId = 726,
taskGroupId = 46023
},
[440994] = {
id = 440994,
name = "UGC-星世界探索",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110010
}
}
}
}
}
}
},
jumpId = 726,
taskGroupId = 46023
},
[440995] = {
id = 440995,
name = "奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 328,
value = 1,
subConditionList = {
{
type = 1002,
value = {
440927,
440928,
440929,
440930,
440931
}
}
}
}
}
}
},
reward = {
itemIdList = {
820053
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46024
},
[440996] = {
id = 440996,
name = "奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 328,
value = 2,
subConditionList = {
{
type = 1002,
value = {
440927,
440928,
440929,
440930,
440931
}
}
}
}
}
}
},
reward = {
itemIdList = {
820053
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46024
},
[440997] = {
id = 440997,
name = "奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 328,
value = 3,
subConditionList = {
{
type = 1002,
value = {
440927,
440928,
440929,
440930,
440931
}
}
}
}
}
}
},
reward = {
itemIdList = {
820053
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46024
},
[440998] = {
id = 440998,
name = "奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 328,
value = 4,
subConditionList = {
{
type = 1002,
value = {
440927,
440928,
440929,
440930,
440931
}
}
}
}
}
}
},
reward = {
itemIdList = {
820053
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46024
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
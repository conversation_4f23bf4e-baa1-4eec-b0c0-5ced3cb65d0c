--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_礼包_特色玩法运营.xlsx: 固定礼包

local v0 = 3

local data = {
[310666] = {
id = 310666,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷券*15",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218
},
itemNums = {
15
}
}
},
[310667] = {
id = 310667,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷券*20",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218
},
itemNums = {
20
}
}
},
[310668] = {
id = 310668,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷券*25",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218
},
itemNums = {
25
}
}
},
[310669] = {
id = 310669,
effect = true,
quality = 3,
name = "COC资源礼包测试1",
desc = "打开和活动源露*10000，星币*10000",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
242001,
242002
},
itemNums = {
10000,
10000
}
}
},
[310670] = {
id = 310670,
effect = true,
quality = 2,
name = "COC资源礼包测试2",
desc = "打开和活动源露*100000，星币*100000",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
242001,
242002
},
itemNums = {
100000,
100000
}
}
},
[310671] = {
id = 310671,
effect = true,
name = "COC资源礼包测试3",
desc = "打开和活动源露*500000，星币*500000",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
242001,
242002
},
itemNums = {
500000,
500000
}
}
},
[310672] = {
id = 310672,
effect = true,
name = "丰收兔礼盒",
desc = "打开后获得丰收兔、沙沙渔获",
icon = "CDN:T_Common_Item_System_Bag_HarvestRabbit",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218107,
218110
},
itemNums = {
1,
1
}
}
},
[310680] = {
id = 310680,
effect = true,
name = "竞技冲刺宝箱",
desc = "打开后获得高级排位升星券、峡谷排位升星券",
icon = "CDN:T_Common_Item_System_BagBig_011",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
203002,
203012
},
itemNums = {
1,
1
}
}
},
[310681] = {
id = 310681,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷券*10",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218
},
itemNums = {
10
}
}
},
[310682] = {
id = 310682,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷券*15",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218
},
itemNums = {
15
}
}
},
[310683] = {
id = 310683,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷券*20",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218
},
itemNums = {
20
}
}
},
[310684] = {
id = 310684,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷券*25",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218
},
itemNums = {
25
}
}
},
[310685] = {
id = 310685,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷橙色动态高光播报，峡谷券*30",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
270006,
218
},
itemNums = {
1,
30
}
}
},
[310686] = {
id = 310686,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷券*50",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218
},
itemNums = {
50
}
}
},
[310690] = {
id = 310690,
effect = true,
quality = 3,
name = "节日英雄限免礼包",
desc = "7天内免费体验7、8、9月上新的全部峡谷英雄！(获取后自动使用，不会进入背包）",
icon = "CDN:T_Common_Item_System_MobaBox",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
301514,
301511,
301513,
301512,
301510,
301509,
301508,
301507,
301506,
301505,
301504,
301503,
301502,
301501
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1
}
}
},
[310700] = {
id = 310700,
effect = true,
quality = 3,
name = "奇趣派对礼包",
desc = "打开后获得阵营卡*1、狼人币*10",
icon = "CDN:T_E3_Prepare_Icon_FactionCard_001",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
200101,
13
},
itemNums = {
1,
10
},
expireDays = {
0,
0
}
}
},
[310701] = {
id = 310701,
effect = true,
quality = 3,
name = "奇趣派对礼包",
desc = "打开后获得身份卡*1、狼人币*10",
icon = "CDN:T_E3_Prepare_Icon_IdentityCard_001",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
200102,
13
},
itemNums = {
1,
10
},
expireDays = {
0,
0
}
}
},
[310702] = {
id = 310702,
effect = true,
quality = 3,
name = "奇趣派对礼包",
desc = "打开后获得贴符*1、狼人币*10",
icon = "CDN:T_E3_PropsIcon_017",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240417,
13
},
itemNums = {
1,
10
},
expireDays = {
0,
0
}
}
},
[310703] = {
id = 310703,
effect = true,
quality = 3,
name = "奇趣派对礼包",
desc = "打开后获得祝福*1、狼人币*10",
icon = "CDN:T_E3_PropsIcon_019",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240419,
13
},
itemNums = {
1,
10
},
expireDays = {
0,
0
}
}
},
[310704] = {
id = 310704,
effect = true,
quality = 3,
name = "奇趣派对礼包",
desc = "打开后获得丢蛋糕*3、狼人币*10",
icon = "CDN:T_E3_PropsIcon_020",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240420,
13
},
itemNums = {
3,
10
},
expireDays = {
0,
0
}
}
},
[310705] = {
id = 310705,
effect = true,
quality = 3,
name = "奇趣派对礼包",
desc = "打开后获得疯狂点赞*3、狼人币*10",
icon = "CDN:T_E3_PropsIcon_016",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240416,
13
},
itemNums = {
3,
10
},
expireDays = {
0,
0
}
}
},
[310706] = {
id = 310706,
effect = true,
quality = 3,
name = "奇趣派对礼包",
desc = "打开后获得丢鸡蛋*3、狼人币*10",
icon = "CDN:T_E3_PropsIcon_001",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240401,
13
},
itemNums = {
3,
10
},
expireDays = {
0,
0
}
}
},
[310687] = {
id = 310687,
effect = true,
name = "梦幻告白礼盒",
desc = "打开后获得告白熊梦幻屋、蔷薇花车、彩虹牧场、梦幻海洋屋、云朵奶油时钟。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_Farm2",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218115,
218118,
218119,
218120,
218121
},
itemNums = {
1,
1,
1,
1,
1
}
}
},
[310688] = {
id = 310688,
effect = true,
quality = 2,
name = "仙狐花隐礼盒",
desc = "打开后获得狐仙、花蔓时钟（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_Farm5",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218112,
218122
},
itemNums = {
1,
1
}
}
},
[310689] = {
id = 310689,
effect = true,
quality = 3,
name = "惊喜告白礼盒",
desc = "打开后获得来自告白熊的礼物",
icon = "CDN:T_Common_Item_System_Farm4",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
630340,
861006,
219000
},
itemNums = {
1,
1,
10
}
}
},
[310707] = {
id = 310707,
effect = true,
name = "招财喵礼包",
desc = "打开后获得招财喵、梦幻熊礼盒（重复均转换成云朵币）",
icon = "CDN:T_Common_Item_System_Farm3",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218117,
218130
},
itemNums = {
1,
1
}
}
},
[310708] = {
id = 310708,
effect = true,
name = "蔚海绮梦礼盒",
desc = "打开后获得绮丽海螺城堡、璃海星光果行、海盗宝藏小店、海妖鱼店、深海时钟。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_Farm6",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218123,
218125,
218126,
218127,
218128
},
itemNums = {
1,
1,
1,
1,
1
}
}
},
[310800] = {
id = 310800,
effect = true,
name = "英雄体验卡宝箱",
desc = "打开后获得英雄体验卡：铠（7天）、孙策（7天）、蔡文姬（7天）、兰陵王（7天）、妲己（7天）、王昭君（7天）",
icon = "CDN:T_Common_Item_System_BagBig_011",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
301502,
301501,
301504,
301507,
301505,
301515,
301511
},
itemNums = {
1,
1,
1,
1,
1,
1,
1
}
}
},
[310709] = {
id = 310709,
effect = true,
name = "雪境欢颂礼盒",
desc = "打开后获得冰晶星梦城堡、雪花轻语果行、水晶鹿角小店、雪乡小Q鱼铺、糖果松树时钟。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_Farm10",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218142,
218134,
218135,
218136,
218138
},
itemNums = {
1,
1,
1,
1,
1
}
}
},
[310710] = {
id = 310710,
effect = true,
name = "海狮公主礼包",
desc = "打开后获得海狮公主、泡泡鱼礼盒（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_Farm7",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218124,
218133
},
itemNums = {
1,
1
}
}
},
[310711] = {
id = 310711,
effect = true,
name = "雪球精灵礼包",
desc = "打开后获得雪球小精灵、冬日萌宠屋（重复均转换成云朵币）",
icon = "CDN:T_Common_Item_System_Farm13",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218137,
218139
},
itemNums = {
1,
1
}
}
},
[310715] = {
id = 310715,
effect = true,
name = "峡谷战神礼包",
desc = "打开后获得峡谷橙色动态头像框以及通用动态头像框",
icon = "CDN:T_Common_Item_System_Bag_077",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
260006,
840228
},
itemNums = {
1,
1
}
}
},
[310716] = {
id = 310716,
effect = true,
name = "狼人元旦礼盒",
desc = "打开后获得谁是狼人互动道具：新年快乐*5、身份卡*2、爱心*5",
icon = "CDN:T_Common_Item_System_WerewolfBag_General03",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240422,
200102,
240403
},
itemNums = {
5,
2,
5
}
}
},
[310717] = {
id = 310717,
effect = true,
quality = 2,
name = "麻醉枪礼盒",
desc = "打开后获得谁是狼人互动道具：手表型麻醉枪*5、阵营卡*5、炸弹*10",
icon = "CDN:T_Common_Item_System_WerewolfBag_General02",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240421,
200101,
240405
},
itemNums = {
5,
5,
10
}
}
},
[310718] = {
id = 310718,
effect = true,
name = "浪漫2025",
desc = "打开后获得谁是狼人互动道具：永恒之恋*5、鹊桥相会*5、钻戒*5、祝福*5",
icon = "CDN:T_Common_Item_System_WerewolfBag_General03",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240409,
240407,
240408,
240419
},
itemNums = {
5,
5,
5,
5
}
}
},
[310719] = {
id = 310719,
effect = true,
name = "仙福盈门礼盒",
desc = "打开后获得庆丰年宅院、仙福满满时钟。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_SpringFestival1",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218159,
218156
},
itemNums = {
1,
1
}
}
},
[310722] = {
id = 310722,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得幻梦币*6",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
14
},
itemNums = {
6
}
}
},
[310723] = {
id = 310723,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷高光播报 - 太华",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
270011
},
itemNums = {
1
}
}
},
[310724] = {
id = 310724,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得幻梦币*12",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
14
},
itemNums = {
12
}
}
},
[310725] = {
id = 310725,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷动态头像框 - 太华",
icon = "CDN:T_Common_Item_System_Bag_070",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
260010
},
itemNums = {
1
}
}
},
[310720] = {
id = 310720,
effect = true,
name = "福运琳琅礼盒",
desc = "打开后获得琳琅摘星阁、金玉醒狮果行、金闪闪小铺、鲤跃龙门鱼铺。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_Farm15",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218151,
218152,
218153,
218154
},
itemNums = {
1,
1,
1,
1
}
}
},
[310721] = {
id = 310721,
effect = true,
name = "嘶嘶灵宝礼包",
desc = "打开后获得嘶嘶灵宝、宝莲灯礼盒（重复均转换成云朵币）",
icon = "CDN:T_Common_Item_System_Farm14",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218155,
218157
},
itemNums = {
1,
1
}
}
},
[310726] = {
id = 310726,
effect = true,
quality = 2,
name = "大力狼三件套礼包",
desc = "打开后获得谁是狼人：大力狼头像框、大力狼头像、大力狼昵称框",
icon = "CDN:T_Common_Item_System_WerewolfBag_General03",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
860520,
840520,
820520
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
}
}
},
[310727] = {
id = 310727,
effect = true,
quality = 2,
name = "新年大礼包",
desc = "打开后获得谁是狼人：新年快乐道具*5，身份卡*4，阵营卡*4",
icon = "CDN:T_Common_Item_System_WerewolfBag_General03",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240422,
200102,
200101
},
itemNums = {
5,
4,
4
},
expireDays = {
0,
0,
0
}
}
},
[310728] = {
id = 310728,
effect = true,
name = "新春快乐礼盒",
desc = "打开后获得谁是狼人互动道具：新年快乐*5、红包*5、祝福*5、干杯*5",
icon = "CDN:T_Common_Item_System_WerewolfBag_General03",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240422,
240423,
240412,
240419
},
itemNums = {
5,
5,
5,
5
},
expireDays = {
0,
0,
0,
0
}
}
},
[310729] = {
id = 310729,
effect = true,
name = "情人节快乐礼盒",
desc = "打开后获得谁是狼人互动道具：永恒之恋*5、鹊桥*5、钻戒*5、爱心*5",
icon = "CDN:T_Common_Item_System_WerewolfBag_General03",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240409,
240407,
240408,
240403
},
itemNums = {
5,
5,
5,
5
},
expireDays = {
0,
0,
0,
0
}
}
},
[310730] = {
id = 310730,
effect = true,
exceedReplaceItem = {
{
itemId = 13,
itemNum = 100
},
{
itemId = 13,
itemNum = 100
}
},
name = "狼人新春身份礼包",
desc = "打开后获得谁是狼人身份：鸵鸟、墨鱼（重复均转换成狼人币）",
icon = "CDN:T_Common_Item_System_BagBig_009",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240819,
240833
},
itemNums = {
1,
1
}
}
},
[310731] = {
id = 310731,
effect = true,
name = "峡谷女明星礼包",
desc = "打开后获得峡谷橙色动态头像框以及通用动态头像框",
icon = "CDN:T_Common_Item_System_Bag_104",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
260011,
840250
},
itemNums = {
1,
1
}
}
},
[310732] = {
id = 310732,
effect = true,
name = "桃坞问春礼盒",
desc = "打开后获得烟雨小筑、江南果行、芙蓉商行、观鱼小铺、悠悠亭、日晷。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox01",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218161,
218162,
218163,
218164,
218166,
218167
},
itemNums = {
1,
1,
1,
1,
1,
1
}
}
},
[310733] = {
id = 310733,
effect = true,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得峡谷橙色动态高光播报，峡谷券*30",
icon = "CDN:T_Common_Item_System_Bag_104",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
270012,
218
},
itemNums = {
1,
30
}
}
},
[310734] = {
id = 310734,
effect = true,
quality = 2,
name = "丢炸弹礼盒",
desc = "打开后获得狼人炸弹互动道具*5",
icon = "CDN:T_Common_Item_System_WerewolfBag_13",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240405
},
itemNums = {
5
}
}
},
[310735] = {
id = 310735,
effect = true,
quality = 2,
name = "丰收派对礼包",
desc = "打开后可获得幽灵巫师、收获日（重复均转换成云朵币）",
icon = "CDN:T_Common_Item_System_Box_CyanWhiteSnake",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
219302,
219300
},
itemNums = {
1,
1
},
expireDays = {
0,
0
}
}
},
[310736] = {
id = 310736,
effect = true,
quality = 3,
name = "贴纸礼包",
desc = "打开后可获得表情、召唤铃*5",
icon = "CDN:T_Common_Item_System_Box_CyanWhiteSnake",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
711378,
200620
},
itemNums = {
1,
5
},
expireDays = {
0,
0
}
}
},
[310737] = {
id = 310737,
effect = true,
name = "珍馐百味礼盒",
desc = "打开后获得快乐涮涮屋、三明治果行、罐罐茶小铺、豪华寿司鱼店。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox04",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218171,
218172,
218173,
218174
},
itemNums = {
1,
1,
1,
1
}
}
},
[310738] = {
id = 310738,
effect = true,
name = "峡谷英豪礼包",
desc = "打开后获得峡谷橙色动态头像框以及通用动态头像框",
icon = "CDN:T_Common_Item_System_Bag_104",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
260017,
840270
},
itemNums = {
1,
1
}
}
},
[310739] = {
id = 310739,
effect = true,
quality = 2,
name = "狐爷爷礼盒",
desc = "打开后获得狐爷爷、披萨时钟（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox05",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218183,
218178
},
itemNums = {
1,
1
}
}
},
[310740] = {
id = 310740,
effect = true,
name = "蜜糖彩虹礼盒",
desc = "打开后获得蜜糖饼庭院、大力猫爪时钟。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox09",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218170,
218191
},
itemNums = {
1,
1
}
}
},
[310741] = {
id = 310741,
effect = true,
quality = 2,
name = "狼人精品礼包",
desc = "打开后获得谁是狼人：身份卡*2、阵营卡*2、爱心*10、丢鸡蛋*10",
icon = "CDN:T_Common_Item_System_WerewolfBag_General03",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
200102,
200101,
240403,
240401
},
itemNums = {
2,
2,
10,
10
},
expireDays = {
0,
0,
0,
0
}
}
},
[310742] = {
id = 310742,
effect = true,
quality = 2,
name = "狼人优选礼包",
desc = "打开后获得谁是狼人：身份卡*2、阵营卡*2、疯狂点赞*10、扔炸弹*10",
icon = "CDN:T_Common_Item_System_WerewolfBag_General03",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
200102,
200101,
240416,
240405
},
itemNums = {
2,
2,
10,
10
},
expireDays = {
0,
0,
0,
0
}
}
},
[310743] = {
id = 310743,
effect = true,
quality = 2,
name = "狼人互动表情礼包",
desc = "打开后获得“尴尬”和“耶”两个会议新表情",
icon = "CDN:T_Common_Item_System_WerewolfBag_16",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240628,
240629
},
itemNums = {
1,
1
},
expireDays = {
0,
0
}
}
},
[310744] = {
id = 310744,
effect = true,
quality = 2,
name = "臭鼬三件套礼包",
desc = "打开后获得谁是狼人：臭鼬头像框、臭鼬头像、臭鼬昵称框",
icon = "CDN:T_Common_Item_System_WerewolfBag_17",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
840523,
860523,
820523
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
}
}
},
[310745] = {
id = 310745,
effect = true,
name = "翡光仙灵礼包",
desc = "打开后获得翡光仙灵庭、蒸蒸日上小窝（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox07",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218184,
218176
},
itemNums = {
1,
1
}
}
},
[310746] = {
id = 310746,
effect = true,
quality = 2,
name = "诸葛亮英雄礼盒",
desc = "打开后获得诸葛亮体验卡（90天）以及诸葛亮头像",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
301902,
860190
},
itemNums = {
1,
1
}
}
},
[310747] = {
id = 310747,
effect = true,
quality = 2,
name = "刘备英雄礼盒",
desc = "打开后获得刘备体验卡（90天）、刘备头像以及峡谷蓝色高光播报",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
301901,
860191,
270016
},
itemNums = {
1,
1,
1
}
}
},
[310748] = {
id = 310748,
effect = true,
name = "煎饼超人礼包",
desc = "打开后获得煎饼果子超人、猪猪包礼盒（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox08",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218177,
218179
},
itemNums = {
1,
1
}
}
},
[310749] = {
id = 310749,
effect = true,
name = "祈愿进度礼包",
desc = "打开后获得峡谷橙色动态高光播报、峡谷蓝色头像框以及峡谷券*30",
icon = "CDN:T_Common_Item_System_Bag_104",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
270017,
260015,
218
},
itemNums = {
1,
1,
30
}
}
},
[310750] = {
id = 310750,
effect = true,
quality = 2,
name = "满杯蜜桃猫礼盒",
desc = "打开后获得蜜桃猫星星杯、蜜桃猫星礼盒（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox06",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218185,
218186
},
itemNums = {
1,
1
}
}
},
[310751] = {
id = 310751,
effect = true,
quality = 2,
name = "回流英雄限免宝箱",
desc = "打开后获得英雄体验卡：阿珂（7天）、王昭君（7天）、李白（7天）、马可波罗（7天）、虞姬（7天）",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
301605,
301511,
301512,
301608,
301607
},
itemNums = {
1,
1,
1,
1,
1
}
}
},
[310752] = {
id = 310752,
effect = true,
name = "甜梦嘉年华礼盒",
desc = "打开后获得蜜糖喵巡游站、海盐甜筒果行、樱桃蛋糕小铺、海盗星船鱼铺。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox10",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218168,
218188,
218189,
218190
},
itemNums = {
1,
1,
1,
1
}
}
},
[310753] = {
id = 310753,
effect = true,
name = "奶油乐园礼包",
desc = "打开后获得奶油云朵乐园、布丁狗小窝。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox04",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218169,
218193
},
itemNums = {
1,
1
}
}
},
[310754] = {
id = 310754,
effect = true,
quality = 2,
name = "潜行狼装饰礼盒",
desc = "打开后获得潜行狼头像、潜行狼昵称框共2件套",
icon = "CDN:T_Common_Item_System_Bag_102",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
860518,
820518
},
itemNums = {
1,
1
}
}
},
[310755] = {
id = 310755,
effect = true,
quality = 2,
name = "潜行狼头像框礼盒",
desc = "打开后获得潜行狼头像框",
icon = "CDN:T_Common_Item_System_Bag_102",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
840518
},
itemNums = {
1
}
}
},
[310756] = {
id = 310756,
effect = true,
name = "怒海狂鲨礼包",
desc = "打开后获得海盗鱼铺、天空之城时钟（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox13",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218200,
218206
},
itemNums = {
1,
1
}
}
},
[310757] = {
id = 310757,
effect = true,
name = "甜心琪琪礼包",
desc = "打开后获得甜心琪琪、甜甜杯礼盒（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox14",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218194,
218192
},
itemNums = {
1,
1
}
}
},
[310758] = {
id = 310758,
effect = true,
name = "回流身份限免宝箱",
desc = "打开后获得身份体验卡：袋鼠（3天）、法官（3天）、双子（3天）、假面狼（3天）、陷阱狼（3天）、捕梦者（3天）",
icon = "CDN:T_Common_Item_System_WerewolfBag_20",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
240832,
240803,
240831,
240807,
240834,
240814
},
itemNums = {
1,
1,
1,
1,
1,
1
},
expireDays = {
3,
3,
3,
3,
3,
3
}
}
},
[310759] = {
id = 310759,
effect = true,
name = "炽光海岸礼包",
desc = "打开后获得热浪岛屿、石纹部落（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox12",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218195,
218196
},
itemNums = {
1,
1
}
}
},
[310760] = {
id = 310760,
effect = true,
name = "天穹圣域礼盒",
desc = "打开后获得圣灵之庭、星露花台、天穹彩虹小店、灵泉圣庭。（重复转换为云朵币）",
icon = "CDN:T_Common_Item_System_StyleBox10",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
218201,
218210,
218211,
218212
},
itemNums = {
1,
1,
1,
1
}
}
}
}

local mt = {
effect = false,
type = "ItemType_Package",
stackedNum = 999,
quality = 1,
bagId = 1,
useType = "IUTO_GiftPackage"
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
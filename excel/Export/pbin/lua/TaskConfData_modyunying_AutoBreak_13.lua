--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/H_活动中心配置_特色玩法运营.xlsx: 活动任务

local v0 = {
itemIdList = {
317160
},
numList = {
200
},
validPeriodList = {
0
}
}

local data = {
[651485] = {
id = 651485,
name = "完成1局峡谷5v5",
desc = "完成1局峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
3512
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1001506
},
[651486] = {
id = 651486,
name = "完成1局谁是狼人",
desc = "完成1局谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
3512
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1001506
},
[651487] = {
id = 651487,
name = "完成1局天天晋级赛",
desc = "完成1局天天晋级赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6,
7,
8,
9
}
}
}
}
}
}
},
reward = {
itemIdList = {
3512
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1001506
},
[651488] = {
id = 651488,
name = "完成1局大王别抓我",
desc = "完成1局大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
reward = {
itemIdList = {
3512
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1001506
},
[651489] = {
id = 651489,
name = "在好友农场祈福5次",
desc = "在好友农场祈福5次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 5
}
}
}
},
reward = {
itemIdList = {
3512
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1001506
},
[651490] = {
id = 651490,
name = "兑换",
desc = "娱乐排位升星券*1",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
203001
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001507
},
[651491] = {
id = 651491,
name = "兑换",
desc = "狼人bp经验*200",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
300
},
validPeriodList = {
0
}
},
taskGroupId = 1001507
},
[651492] = {
id = 651492,
name = "兑换",
desc = "峡谷bp经验*200",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
1014
},
numList = {
200
},
validPeriodList = {
0
}
},
taskGroupId = 1001507
},
[651493] = {
id = 651493,
name = "兑换",
desc = "晋级赛BP经验*300",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
1015
},
numList = {
200
},
validPeriodList = {
0
}
},
taskGroupId = 1001507
},
[651494] = {
id = 651494,
name = "兑换",
desc = "娱乐排位升星券*1",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 50,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
13
},
numList = {
100
},
validPeriodList = {
0
}
},
taskGroupId = 1001507
},
[651495] = {
id = 651495,
name = "兑换",
desc = "磷虾*4",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 60,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
300102
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001507
},
[651496] = {
id = 651496,
name = "兑换",
desc = "绿装-校园传说",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 70,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1001507
},
[651497] = {
id = 651497,
name = "特色咨询-每日奖励",
desc = "特色咨询-每日奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
200018
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001508
},
[651617] = {
id = 651617,
name = "天天领升至4星",
desc = "天天领升至4星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 616,
value = 1
}
}
}
},
reward = {
itemIdList = {
301745
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1005302
},
[651651] = {
id = 651651,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023015
},
[651652] = {
id = 651652,
name = "第1天",
desc = "第1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023016
},
[651653] = {
id = 651653,
name = "第2天",
desc = "第2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 2,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
3
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023016
},
[651654] = {
id = 651654,
name = "第3天",
desc = "第3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 3,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023016
},
[651655] = {
id = 651655,
name = "第4天",
desc = "第4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 4,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023016
},
[651656] = {
id = 651656,
name = "第5天",
desc = "第5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 5,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
200006
},
numList = {
3
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023016
},
[651657] = {
id = 651657,
name = "第6天",
desc = "第6天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 6,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
15
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023016
},
[651658] = {
id = 651658,
name = "第7天",
desc = "第7天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 7,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
620889
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023016
},
[651659] = {
id = 651659,
name = "第一天",
desc = "第一天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
jumpId = 1154,
taskGroupId = 1023017
},
[659402] = {
id = 659402,
name = "【每日】收获10次农作物",
desc = "【每日】收获10次农作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 10,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317151
},
numList = {
2
}
},
jumpId = 5100,
taskGroupId = 1022140
},
[659403] = {
id = 659403,
name = "【每日】在好友农场祈福5次",
desc = "【每日】在好友农场祈福5次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 5
}
}
}
},
reward = {
itemIdList = {
317151
},
numList = {
2
}
},
jumpId = 5102,
taskGroupId = 1022140
},
[659404] = {
id = 659404,
name = "【每周】在星宝农场餐厅营业2次",
desc = "【每周】在星宝农场餐厅营业2次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 904,
value = 2
}
}
}
},
reward = {
itemIdList = {
317151
},
numList = {
3
}
},
jumpId = 5104,
taskGroupId = 1022141
},
[659405] = {
id = 659405,
name = "【每周】游玩1次【鸡同鸭抢】轻松一下",
desc = "【每周】游玩1次【鸡同鸭抢】轻松一下",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425199145208
}
}
}
}
}
}
},
reward = {
itemIdList = {
317151
},
numList = {
3
}
},
jumpId = 83,
taskGroupId = 1022141
},
[659406] = {
id = 659406,
name = "【累计】拜访5个星宝的农场",
desc = "【累计】拜访5个星宝的农场",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 175,
value = 5
}
}
}
},
reward = {
itemIdList = {
317151
},
numList = {
6
}
},
jumpId = 5102,
taskGroupId = 1022142
},
[659407] = {
id = 659407,
name = "【累计】通过泡温泉获得5次增益",
desc = "【累计】通过泡温泉获得5次增益",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 900,
value = 5,
subConditionList = {
{
type = 600,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317151
},
numList = {
6
}
},
jumpId = 5100,
taskGroupId = 1022142
},
[659408] = {
id = 659408,
name = "在星宝农场播种100次",
desc = "在星宝农场播种100次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 155,
value = 100,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022143
},
[659409] = {
id = 659409,
name = "拜访10个星宝的小屋",
desc = "拜访10个星宝的小屋",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 243,
value = 10
}
}
}
},
reward = v0,
jumpId = 5103,
taskGroupId = 1022143
},
[659410] = {
id = 659410,
name = "在星宝农场餐厅累计接待15个贵宾",
desc = "在星宝农场餐厅累计接待15个贵宾",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 917,
value = 15
}
}
}
},
reward = v0,
jumpId = 5104,
taskGroupId = 1022143
},
[659411] = {
id = 659411,
name = "在钓鱼时触发10次幸运钩",
desc = "在钓鱼时触发10次幸运钩",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 215,
value = 10
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022143
},
[659412] = {
id = 659412,
name = "在星宝农场收获50次动物产品",
desc = "在星宝农场收获50次动物产品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 50,
subConditionList = {
{
type = 159,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
300
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022144
},
[659413] = {
id = 659413,
name = "在好友农场捕鱼成功3次",
desc = "在好友农场捕鱼成功3次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 211,
value = 3
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
300
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022144
},
[659414] = {
id = 659414,
name = "获得5条S及以上品质鱼",
desc = "获得5条S及以上品质鱼",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 214,
value = 5,
subConditionList = {
{
type = 173,
value = {
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022144
},
[659415] = {
id = 659415,
name = "通过泡温泉获得3次增益",
desc = "通过泡温泉获得3次增益",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 900,
value = 3,
subConditionList = {
{
type = 600,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022144
},
[659416] = {
id = 659416,
name = "在星宝农场收获100次农作物",
desc = "在星宝农场收获100次农作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 100,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022145
},
[659417] = {
id = 659417,
name = "在好友农场祈福40次",
desc = "在好友农场祈福40次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 40
}
}
}
},
reward = v0,
jumpId = 5102,
taskGroupId = 1022145
},
[659418] = {
id = 659418,
name = "在星宝农场加工20次",
desc = "在星宝农场加工20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 238,
value = 20
}
}
}
},
reward = v0,
jumpId = 5103,
taskGroupId = 1022145
},
[659419] = {
id = 659419,
name = "通过泡温泉获得3次增益",
desc = "通过泡温泉获得3次增益",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 900,
value = 3,
subConditionList = {
{
type = 600,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022145
},
[659420] = {
id = 659420,
name = "在星宝农场浇水100次",
desc = "在星宝农场浇水100次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 156,
value = 100,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022146
},
[659421] = {
id = 659421,
name = "在好友农场拿取40次作物",
desc = "在好友农场拿取40次作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 173,
value = 40,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 5102,
taskGroupId = 1022146
},
[659422] = {
id = 659422,
name = "在星宝农场获得30次农作物的丰收",
desc = "在星宝农场获得30次农作物的丰收",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 30,
subConditionList = {
{
type = 157,
value = {
3
}
},
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022146
},
[659423] = {
id = 659423,
name = "在星宝农场给好友赠礼3次",
desc = "在星宝农场给好友赠礼3次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 257,
value = 10
}
}
}
},
reward = v0,
jumpId = 5102,
taskGroupId = 1022146
},
[659424] = {
id = 659424,
name = "在星宝农场播种100次",
desc = "在星宝农场播种100次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 155,
value = 100,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022147
},
[659425] = {
id = 659425,
name = "拜访10个星宝的小屋",
desc = "拜访10个星宝的小屋",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 243,
value = 10
}
}
}
},
reward = v0,
jumpId = 5103,
taskGroupId = 1022147
},
[659426] = {
id = 659426,
name = "在星宝农场餐厅累计接待15个贵宾",
desc = "在星宝农场餐厅累计接待15个贵宾",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 917,
value = 15
}
}
}
},
reward = v0,
jumpId = 5104,
taskGroupId = 1022147
},
[659427] = {
id = 659427,
name = "在钓鱼时触发10次幸运钩",
desc = "在钓鱼时触发10次幸运钩",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 215,
value = 10
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022147
},
[659428] = {
id = 659428,
name = "在星宝农场收获50次动物产品",
desc = "在星宝农场收获50次动物产品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 50,
subConditionList = {
{
type = 159,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
300
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022148
},
[659429] = {
id = 659429,
name = "在好友农场捕鱼成功3次",
desc = "在好友农场捕鱼成功3次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 211,
value = 3
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
300
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022148
},
[659430] = {
id = 659430,
name = "获得5条S及以上品质鱼",
desc = "获得5条S及以上品质鱼",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 214,
value = 5,
subConditionList = {
{
type = 173,
value = {
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022148
},
[659431] = {
id = 659431,
name = "通过泡温泉获得3次增益",
desc = "通过泡温泉获得3次增益",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 900,
value = 3,
subConditionList = {
{
type = 600,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022148
},
[659432] = {
id = 659432,
name = "在星宝农场收获500次农作物",
desc = "在星宝农场收获500次农作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 500,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 5100,
taskGroupId = 1022149
},
[659433] = {
id = 659433,
name = "在好友农场祈福150次",
desc = "在好友农场祈福150次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 150
}
}
}
},
reward = v0,
jumpId = 5102,
taskGroupId = 1022149
},
[659434] = {
id = 659434,
name = "在好友农场累计拿取300次作物",
desc = "在好友农场累计拿取300次作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 173,
value = 300,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
300
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022149
},
[659435] = {
id = 659435,
name = "在星宝农场累计钓鱼600次",
desc = "在星宝农场累计钓鱼600次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 209,
value = 600
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
300
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022149
},
[780739] = {
id = 780739,
name = "小档_白银峡谷星III",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
2,
3
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
150
},
validPeriodList = {
0
}
},
taskGroupId = 1082001
},
[780740] = {
id = 780740,
name = "大档_黄金峡谷星V",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
14
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1082002
},
[780742] = {
id = 780742,
name = "大档_铂金峡谷星V",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
14
},
numList = {
4
},
validPeriodList = {
0
}
},
taskGroupId = 1082002
},
[780743] = {
id = 780743,
name = "小档_铂金峡谷星II",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
4
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
203012
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082001
},
[780745] = {
id = 780745,
name = "小档_钻石峡谷星II",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
4
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
300
},
validPeriodList = {
0
}
},
taskGroupId = 1082001
},
[780746] = {
id = 780746,
name = "大档_至尊峡谷星V",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
6,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
403030
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082002
},
[780748] = {
id = 780748,
name = "大档_最强峡谷星V",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
7,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
861025
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082002
},
[780749] = {
id = 780749,
name = "小档_最强峡谷星III",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
7,
3
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
203012
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1082001
},
[780751] = {
id = 780751,
name = "大档_白银峡谷星V",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
2,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 1082004
},
[780752] = {
id = 780752,
name = "小档_白银峡谷星III",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
2,
3
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
150
},
validPeriodList = {
0
}
},
taskGroupId = 1082003
},
[780754] = {
id = 780754,
name = "小档_黄金峡谷星II",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
4
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
711353
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082003
},
[780755] = {
id = 780755,
name = "大档_铂金峡谷星V",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
14
},
numList = {
4
},
validPeriodList = {
0
}
},
taskGroupId = 1082004
},
[780757] = {
id = 780757,
name = "大档_钻石峡谷星V",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
14
},
numList = {
6
},
validPeriodList = {
0
}
},
taskGroupId = 1082004
},
[780758] = {
id = 780758,
name = "小档_钻石峡谷星II",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
4
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
300
},
validPeriodList = {
0
}
},
taskGroupId = 1082003
},
[780760] = {
id = 780760,
name = "小档_至尊峡谷星II",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
6,
4
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
711370
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082003
},
[780761] = {
id = 780761,
name = "大档_最强峡谷星V",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
7,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
861022
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082004
},
[780763] = {
id = 780763,
name = "大档_无双峡谷星",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
407005
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082004
},
[781001] = {
id = 781001,
name = "完成1局峡谷5v5排位",
desc = "完成1局峡谷5v5排位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329936
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078930
},
[781002] = {
id = 781002,
name = "完成3局峡谷5v5排位",
desc = "完成3局峡谷5v5排位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
300102
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078930
},
[781003] = {
id = 781003,
name = "完成5局峡谷5v5排位",
desc = "完成5局峡谷5v5排位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
722059
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078930
},
[781004] = {
id = 781004,
name = "游玩1次【峡谷擂台赛】",
desc = "游玩1次【峡谷擂台赛】",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425204946721
}
}
}
}
}
}
},
reward = {
itemIdList = {
329936
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 83,
taskGroupId = 1078930
},
[781005] = {
id = 781005,
name = "6月16日起登录游戏送童年背饰！",
desc = "6月16日起登录游戏送童年背饰！",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
620405,
3544
},
numList = {
1,
50
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1078931
},
[651500] = {
id = 651500,
name = "免费体验新身份！去看看→（自动使用）",
desc = "免费体验新身份！去看看→（自动使用）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
310
}
}
}
}
}
}
},
reward = {
itemIdList = {
3642
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001511
},
[651501] = {
id = 651501,
name = "【每日】游玩1次秘境寻宝模式",
desc = "【每日】完成1次谁是狼人对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
3642
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001509
},
[651502] = {
id = 651502,
name = "【每周】游玩1次ugc地图xxxx",
desc = "【每周】完成10次谁是狼人普通任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 53,
value = 10,
subConditionList = {
{
type = 2,
value = {
105,
106,
107,
108,
109,
110,
111,
151,
112,
113
}
},
{
type = 49,
value = {
61
}
}
}
}
}
}
},
reward = {
itemIdList = {
3642
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001510
},
[651503] = {
id = 651503,
name = "【累计】完成5次谁是狼人对局",
desc = "【每周】组队完成3次谁是狼人对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
},
{
type = 50,
value = {
45,
1,
54
}
}
}
}
}
}
},
reward = {
itemIdList = {
3642
},
numList = {
3
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001511
},
[651504] = {
id = 651504,
name = "待定",
desc = "待定",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
},
{
type = 50,
value = {
45,
1,
53
}
}
}
}
}
}
},
reward = {
itemIdList = {
3642
},
numList = {
15
},
validPeriodList = {
0
}
},
jumpId = 310
},
[651505] = {
id = 651505,
name = "【累计】使用咒术狼完成1次对局 或 使用特工完成1次对局",
desc = "【累计】使用咒术狼完成1次对局 或 使用特工完成1次对局",
condition = {
resCompleteConditionGroup = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 46,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
},
{
type = 50,
value = {
45,
1,
54
}
}
},
jumpId = 310,
desc = "【累计】使用咒术狼完成1次对局"
},
{
conditionType = 46,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
},
{
type = 50,
value = {
45,
1,
53
}
}
},
jumpId = 310,
desc = "【累计】使用特工完成1次对局"
}
}
}
},
reward = {
itemIdList = {
3642
},
numList = {
3
},
validPeriodList = {
0
}
},
taskGroupId = 1001511
},
[651506] = {
id = 651506,
name = "分享宝物",
desc = "分享宝物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
651506
}
}
}
}
}
}
},
reward = {
itemIdList = {
3642
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1,
taskGroupId = 1001512
},
[651510] = {
id = 651510,
name = "【每日】登录游戏1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
3641
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1001513
},
[651511] = {
id = 651511,
name = "【每日】游玩任意模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
3641
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001513
},
[651512] = {
id = 651512,
name = "狼人春日行",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
310
}
}
}
}
}
}
},
jumpId = 310,
taskGroupId = 1001514
},
[651513] = {
id = 651513,
name = "赛季末冲刺",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
310
}
}
}
}
}
}
},
jumpId = 310,
taskGroupId = 1001514
},
[651514] = {
id = 651514,
name = "峡谷擂台赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
310
}
}
}
}
}
}
},
jumpId = 310,
taskGroupId = 1001514
},
[651515] = {
id = 651515,
name = "大王福利周",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
310
}
}
}
}
}
}
},
jumpId = 310,
taskGroupId = 1001514
},
[651660] = {
id = 651660,
name = "【每周】登录领取大王排位升星券",
desc = "【每周】登录领取大王排位升星券",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
}
},
taskGroupId = 1023018
},
[651661] = {
id = 651661,
name = "【每周】完成2局大王排位模式",
desc = "【每周】完成2局大王排位模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
352,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
}
},
jumpId = 1154,
taskGroupId = 1023018
},
[651662] = {
id = 651662,
name = "星宝/暗星达到星耀段位",
desc = "星宝/暗星达到星耀段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
7,
8
}
},
{
type = 154,
value = {
70012,
80012
}
},
{
type = 32,
value = {
6,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
411710
},
numList = {
1
}
},
jumpId = 1154,
taskGroupId = 1023019
},
[651663] = {
id = 651663,
name = "星宝/暗星达到钻石段位",
desc = "星宝/暗星达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
7,
8
}
},
{
type = 154,
value = {
70012,
80012
}
},
{
type = 32,
value = {
5,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
720849
},
numList = {
1
}
},
jumpId = 1154,
taskGroupId = 1023019
},
[651664] = {
id = 651664,
name = "星宝/暗星达到铂金段位",
desc = "星宝/暗星达到铂金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
7,
8
}
},
{
type = 154,
value = {
70012,
80012
}
},
{
type = 32,
value = {
4,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
830114
},
numList = {
1
}
},
jumpId = 1154,
taskGroupId = 1023019
},
[651665] = {
id = 651665,
name = "星宝/暗星达到黄金段位",
desc = "星宝/暗星达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
7,
8
}
},
{
type = 154,
value = {
70012,
80012
}
},
{
type = 32,
value = {
3,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
3
}
},
jumpId = 1154,
taskGroupId = 1023019
},
[651666] = {
id = 651666,
name = "星宝/暗星达到白银段位",
desc = "星宝/暗星达到白银段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
7,
8
}
},
{
type = 154,
value = {
70012,
80012
}
},
{
type = 32,
value = {
2,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
}
},
jumpId = 1154,
taskGroupId = 1023019
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
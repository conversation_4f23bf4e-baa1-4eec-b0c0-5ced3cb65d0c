--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_元梦卡牌.xlsx: 卡包

local data = {
[290031] = {
id = 290031,
effect = true,
type = "ItemType_CardBag",
name = "夏日联动吉伊卡哇炫彩包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出1张卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_21",
picture = "CDN:T_Common_Item_CardBag_31",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290031
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_31",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290032] = {
id = 290032,
effect = true,
type = "ItemType_CardBag",
quality = 4,
name = "夏日联动吉伊卡哇炫彩包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出2张卡牌，保底至少1张2星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_22",
picture = "CDN:T_Common_Item_CardBag_32",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290032
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_32",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290033] = {
id = 290033,
effect = true,
type = "ItemType_CardBag",
quality = 3,
name = "夏日联动吉伊卡哇炫彩包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出3张卡牌，保底至少1张3星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_23",
picture = "CDN:T_Common_Item_CardBag_33",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290033
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_33",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290034] = {
id = 290034,
effect = true,
type = "ItemType_CardBag",
quality = 2,
name = "夏日联动吉伊卡哇炫彩包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出4张卡牌，保底至少1张4星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_24",
picture = "CDN:T_Common_Item_CardBag_34",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290034
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_34",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290035] = {
id = 290035,
effect = true,
type = "ItemType_CardBag",
quality = 1,
name = "夏日联动吉伊卡哇炫彩包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出5张卡牌，保底至少1张5星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_25",
picture = "CDN:T_Common_Item_CardBag_35",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290035
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_35",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290041] = {
id = 290041,
effect = true,
type = "ItemType_CardBag",
name = "夏日联动柯南炫彩包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出1张卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_21",
picture = "CDN:T_Common_Item_CardBag_41",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290041
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_41",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290042] = {
id = 290042,
effect = true,
type = "ItemType_CardBag",
quality = 4,
name = "夏日联动柯南稀有包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出2张卡牌，保底至少1张2星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_22",
picture = "CDN:T_Common_Item_CardBag_42",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290042
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_42",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290043] = {
id = 290043,
effect = true,
type = "ItemType_CardBag",
quality = 3,
name = "夏日联动柯南非凡包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出3张卡牌，保底至少1张3星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_23",
picture = "CDN:T_Common_Item_CardBag_43",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290043
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_43",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290044] = {
id = 290044,
effect = true,
type = "ItemType_CardBag",
quality = 2,
name = "夏日联动柯南臻藏包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出4张卡牌，保底至少1张4星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_24",
picture = "CDN:T_Common_Item_CardBag_44",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290044
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_44",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290045] = {
id = 290045,
effect = true,
type = "ItemType_CardBag",
quality = 1,
name = "夏日联动柯南超凡包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出5张卡牌，保底至少1张5星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_25",
picture = "CDN:T_Common_Item_CardBag_45",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290045
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_45",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290051] = {
id = 290051,
effect = true,
type = "ItemType_CardBag",
name = "夏日联动斗罗大陆炫彩包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出1张卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_21",
picture = "CDN:T_Common_Item_CardBag_51",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290051
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_51",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290052] = {
id = 290052,
effect = true,
type = "ItemType_CardBag",
quality = 4,
name = "夏日联动斗罗大陆稀有包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出2张卡牌，保底至少1张2星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_22",
picture = "CDN:T_Common_Item_CardBag_52",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290052
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_52",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290053] = {
id = 290053,
effect = true,
type = "ItemType_CardBag",
quality = 3,
name = "夏日联动斗罗大陆非凡包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出3张卡牌，保底至少1张3星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_23",
picture = "CDN:T_Common_Item_CardBag_53",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290053
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_53",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290054] = {
id = 290054,
effect = true,
type = "ItemType_CardBag",
quality = 2,
name = "夏日联动斗罗大陆臻藏包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出4张卡牌，保底至少1张4星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_24",
picture = "CDN:T_Common_Item_CardBag_54",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290054
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_54",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290055] = {
id = 290055,
effect = true,
type = "ItemType_CardBag",
quality = 1,
name = "夏日联动斗罗大陆超凡包",
desc = "专属【夏日联动】卡册的卡包，可固定抽出5张卡牌，保底至少1张5星卡牌。集齐卡册可获得丰厚奖励哦！",
icon = "CDN:T_Common_Item_CardBag_25",
picture = "CDN:T_Common_Item_CardBag_55",
getWay = "【活动】获得;每日奖杯挑战获得;收集奖杯奖励获得",
jumpId = {
20,
7003,
902
},
useParam = {
290055
},
autoUse = true,
miniIcon = "CDN:T_Common_Item_Small_CardBag_55",
descForActivity = "专属【夏日联动】卡册的卡包，在【卡牌增量活动】期间，可抽出{0}张卡牌，保底至少{1}张{2}星卡牌！集齐卡册可获得丰厚奖励哦！"
},
[290058] = {
id = 290058,
effect = true,
type = "ItemType_WildCard",
quality = 1,
name = "夏日联动万能卡",
desc = "【夏日联动】卡册的专属万能卡牌。获得后可在限时有效期内兑换【夏日联动】卡册中的任意1张卡牌（包括普通卡牌和金色卡牌）。超过有效期后会失效哦！",
icon = "CDN:T_Common_Item_WildCard_02",
picture = "CDN:T_Common_Item_WildCard_02",
getWay = "【活动】获得",
useParam = {
290058
},
autoUse = true
},
[290448] = {
id = 290448,
effect = true,
type = "ItemType_Card",
name = "暗潮之下",
desc = "新目标，会是你吗？",
getWay = "卡包抽取",
useParam = {
1040802
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290449] = {
id = 290449,
effect = true,
type = "ItemType_Card",
name = "尽在掌握",
desc = "推理可没有高下之分",
getWay = "卡包抽取",
useParam = {
1040803
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290450] = {
id = 290450,
effect = true,
type = "ItemType_Card",
name = "宝石窃手",
desc = "这个宝物，我拿走了",
getWay = "卡包抽取",
useParam = {
1040804
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290451] = {
id = 290451,
effect = true,
type = "ItemType_Card",
name = "樱樱春日",
desc = "我等的人，还没有来",
getWay = "卡包抽取",
useParam = {
1040805
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290452] = {
id = 290452,
effect = true,
type = "ItemType_Card",
name = "蓝银之皇",
desc = "它的名字是，蓝银皇",
getWay = "卡包抽取",
useParam = {
1040901
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290453] = {
id = 290453,
effect = true,
type = "ItemType_Card",
name = "史莱克之首",
desc = "史莱克七怪，患难与共",
getWay = "卡包抽取",
useParam = {
1040902
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290454] = {
id = 290454,
effect = true,
type = "ItemType_Card",
name = "蓝银觉醒",
desc = "蓝银草，也可以修炼！",
getWay = "卡包抽取",
useParam = {
1040903
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290455] = {
id = 290455,
effect = true,
type = "ItemType_Card",
name = "海神神装",
desc = "瀚海狂涛！",
getWay = "卡包抽取",
useParam = {
1040904
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290456] = {
id = 290456,
effect = true,
type = "ItemType_Card",
name = "星斗藏踪",
desc = "星斗大森林，我来了！",
getWay = "卡包抽取",
useParam = {
1040905
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290457] = {
id = 290457,
effect = true,
type = "ItemType_Card",
name = "斗罗之怒",
desc = "两位兄长，英灵不远！",
getWay = "卡包抽取",
useParam = {
1040906
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290458] = {
id = 290458,
effect = true,
type = "ItemType_Card",
name = "星斗之怒",
desc = "我以生命起誓！",
getWay = "卡包抽取",
useParam = {
1040907
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290459] = {
id = 290459,
effect = true,
type = "ItemType_Card",
name = "柔骨兔",
desc = "小看我，要付出代价哦",
getWay = "卡包抽取",
useParam = {
1041001
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290460] = {
id = 290460,
effect = true,
type = "ItemType_Card",
name = "兔影迷踪",
desc = "大明、二明，等我",
getWay = "卡包抽取",
useParam = {
1041002
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290461] = {
id = 290461,
effect = true,
type = "ItemType_Card",
name = "武魂真身",
desc = "九天之月，因我而坠",
getWay = "卡包抽取",
useParam = {
1041003
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290462] = {
id = 290462,
effect = true,
type = "ItemType_Card",
name = "五年之约",
desc = "自此之后，再不分离",
getWay = "卡包抽取",
useParam = {
1041004
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290463] = {
id = 290463,
effect = true,
type = "ItemType_Card",
name = "相思断肠",
desc = "我会好好珍惜我的爱！",
getWay = "卡包抽取",
useParam = {
1041005
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290464] = {
id = 290464,
effect = true,
type = "ItemType_Card",
name = "骄阳照我",
desc = "前路艰险，但有你陪伴",
getWay = "卡包抽取",
useParam = {
1041006
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290465] = {
id = 290465,
effect = true,
type = "ItemType_Card",
name = "萤火之谜",
desc = "星斗森林，我的故乡",
getWay = "卡包抽取",
useParam = {
1041007
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
},
[290466] = {
id = 290466,
effect = true,
type = "ItemType_Card",
name = "灵兔复苏",
desc = "哥，我回来了",
getWay = "卡包抽取",
useParam = {
1041008
},
bHideInBag = true,
noShowTipsNum = true,
autoUse = true
}
}

local mt = {
effect = false,
stackedNum = 999999,
maxNum = 999999,
quality = 5,
bagId = 1,
autoUse = false,
bHideInBag = false,
noShowTipsNum = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_农场.xlsx: 农场道具

local v0 = 3

local v1 = 1

local v2 = {
{
itemId = 6,
itemNum = 100
}
}

local v3 = 225

local v4 = "1.3.26.1"

local v5 = "1.3.36.1"

local data = {
[218000] = {
id = 218000,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 3,
name = "农场币",
desc = "农场币",
icon = "T_Farmyard_Icon_Coin_01",
useParam = {
1001
},
bHideInBag = true,
bagType = "IBT_Farm"
},
[218001] = {
id = 218001,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
name = "星宝农场月卡",
desc = "获得后星宝农场月卡有效时间增加30天",
icon = "T_Farmyard_Icon_Coin_Card",
useType = "IUTO_FarmMonthlyPass",
useParam = {
30
}
},
[218002] = {
id = 218002,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
name = "星宝农场月卡",
desc = "获得后星宝农场月卡有效时间增加30天（获得后自动使用，不会进入背包）",
icon = "T_Farmyard_Icon_Coin_Card",
useType = "IUTO_FarmMonthlyPass",
useParam = {
30
}
},
[219000] = {
id = 219000,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "精品磷虾",
desc = "农场钓鱼功能使用，所有水层均可使用的特级鱼饵",
icon = "CDN:Icon_Farm_PA_Bait_005_A_Comm",
picture = "CDN:Icon_Farm_PA_Bait_005_A_Comm",
getWay = "活动",
bagType = "IBT_Farm"
},
[219003] = {
id = 219003,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
name = "浮游层紫卡包",
desc = "包含30张浮游层鱼卡的紫卡包",
icon = "Icon_Farm_Tool_Fishcard_001_A",
bHideInBag = true
},
[219007] = {
id = 219007,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
name = "中浮游层紫卡包",
desc = "包含30张中浮游层鱼卡的紫卡包",
icon = "Icon_Farm_Tool_Fishcard_001_A",
bHideInBag = true
},
[219011] = {
id = 219011,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
name = "下浮游层紫卡包",
desc = "包含30张下浮游层鱼卡的紫卡包",
icon = "Icon_Farm_Tool_Fishcard_001_A",
bHideInBag = true
},
[219015] = {
id = 219015,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
name = "阳光层紫卡包",
desc = "包含30张阳光层鱼卡的紫卡包",
icon = "Icon_Farm_Tool_Fishcard_001_A",
bHideInBag = true
},
[219019] = {
id = 219019,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
name = "中阳光层紫卡包",
desc = "包含30张中阳光层鱼卡的紫卡包",
icon = "Icon_Farm_Tool_Fishcard_001_A",
bHideInBag = true
},
[219023] = {
id = 219023,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
name = "下阳光层紫卡包",
desc = "包含30张下阳光层鱼卡的紫卡包",
icon = "Icon_Farm_Tool_Fishcard_001_A",
bHideInBag = true
},
[219027] = {
id = 219027,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
name = "透光层紫卡包",
desc = "包含30张透光层鱼卡的紫卡包",
icon = "Icon_Farm_Tool_Fishcard_001_A",
bHideInBag = true
},
[218100] = {
id = 218100,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "香草花房",
desc = "（星宝农场小屋装饰）可以提升每日可拿取次数。",
icon = "CDN:Icon_Farm_BuComm_Home_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_001_Comm",
modelType = 1
},
scaleTimes = 12,
bHideInBag = true,
rotateYaw = 225,
buff = "每日可拿取次数",
buffValue = "+5",
buffViewOffset = "100,400"
},
[218101] = {
id = 218101,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "精灵时钟",
desc = "（星宝农场时钟装饰）可以使动物待产时间提前。",
icon = "CDN:Icon_Farm_FU_Clock_001_A_2_Comm",
resourceConf = {
model = "SM_Farm_FU_Clock_001_A_2_Comm",
modelType = 1
},
scaleTimes = 25,
bHideInBag = true,
rotateYaw = 180,
buff = "动物可助产时间提前",
buffValue = "+5%",
buffViewOffset = "0,-280"
},
[218102] = {
id = 218102,
exceedReplaceItem = v2,
name = "稻草人汉克",
desc = "（星宝农场稻草人装饰）可以降低牧场饲料价格。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_MessageBoard_001_A_Comm",
modelType = 1
},
scaleTimes = 39,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-90
},
buff = "饲料价格降低",
buffValue = "-5%",
buffViewOffset = "-50,0"
},
[218103] = {
id = 218103,
exceedReplaceItem = v2,
name = "甜兔屋",
desc = "（星宝农场小屋装饰）可以提升蔬菜摊售价。",
icon = "CDN:Icon_Farm_BuComm_Home_002_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_002_Comm",
modelType = 1
},
scaleTimes = 9,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
0,
-100
},
buff = "蔬菜摊售价提升",
buffValue = "+10%",
buffViewOffset = "-50,100"
},
[218104] = {
id = 218104,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
quality = 1,
name = "小云宝",
desc = "（星宝农场无人机装饰）可以提升无人机的飞行速度。",
icon = "CDN:Icon_Farm_Tool_UAV_004_A_Comm",
resourceConf = {
model = "SM_Farm_ToolComm_UAV_004_Comm",
modelType = 1
},
scaleTimes = 35,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-70
},
buff = "无人机飞行速度",
buffValue = "+100%",
buffViewOffset = "0,0"
},
[218105] = {
id = 218105,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "洋葱头蔬果屋",
desc = "（星宝农场蔬菜摊装饰）可以提升蔬菜摊售价。",
icon = "CDN:Icon_Farm_BuComm_VegetableStall_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_VegetableStall_001_Comm",
modelType = 1
},
scaleTimes = 12,
shareTexts = {
"蔬果飘香，洋葱闪亮"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218105.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218105.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218105.astc",
buff = "蔬菜摊售价提升",
buffValue = "+10%",
buffViewOffset = "100,400"
},
[218106] = {
id = 218106,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "牛牛牧场小店",
desc = "（星宝农场动物小铺装饰）可以提升动物小铺售价。",
icon = "CDN:Icon_Farm_BuComm_XumuA_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_XumuA_001_Comm",
modelType = 1
},
scaleTimes = 12,
shareTexts = {
"牛牛的乐园，美味的源泉"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218106.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218106.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218105.astc",
buff = "动物小铺售价提升",
buffValue = "+10%",
buffViewOffset = "100,400"
},
[218107] = {
id = 218107,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.18.1",
quality = 1,
name = "丰收兔",
desc = "（星宝农场稻草人装饰）可以提高作物大丰收产量。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_002_A_Comm",
resourceConf = {
model = "SK_Farm_OG_001",
modelType = 2,
idleAnim = "SK_Farm_OG_001_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_001_PV/Farm_OG_001_Intact",
scaleTimes = 45,
soundId = {
40651
},
outEnterSequence = "Farm_PV_OG_001",
shareTexts = {
"收获多如山，和兔兔一起狂欢"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218107.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218107.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218107.astc",
buff = "作物大丰收产量倍率",
buffValue = "+3",
buffViewOffset = "0,0"
},
[218108] = {
id = 218108,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.68.1",
quality = 1,
name = "卧龙宝宝",
desc = "（星宝农场无人机装饰）可以帮你超高速地打理农场。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_003_A_Comm",
resourceConf = {
model = "SK_Farm_Tool_DragonUAV_001_B_Comm",
modelType = 2,
idleAnim = "SK_Farm_Tool_DragonUAV_001_B_Idle_Comm"
},
scaleTimes = 45,
shareTexts = {
"迅疾如风，一键收获"
},
shareAnim = "SK_Farm_Tool_DragonUAV_001_B_Pose",
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-5,
-70
},
shareScaleTimes = 32,
shareRotateYaw = 210
},
[218109] = {
id = 218109,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = v4,
quality = 3,
name = "幸运星礼盒",
desc = "（星宝农场装饰）获得后可以改变送礼时礼物盒的样式。",
icon = "CDN:Icon_Farm_Tool_Gift_003_A_Comm",
resourceConf = {
model = "SM_Farm_Tool_Gift_003_Com",
modelType = 2,
idleAnim = "SM_Farm_Tool_Gift_003_Loop_Com"
},
scaleTimes = 45,
bHideInBag = true,
rotateYaw = 225
},
[218110] = {
id = 218110,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "沙沙渔获",
desc = "（星宝农场水产摊装饰）可以提升水产摊售价。",
icon = "CDN:Icon_Farm_BuComm_FishStall_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_FishStall_001_Comm",
modelType = 1
},
scaleTimes = 12,
shareTexts = {
"让你满载而归，玩转水中韵味"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218110.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218110.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218110.astc",
buff = "水产摊售价提升",
buffValue = "+10%",
buffViewOffset = "100,400"
},
[218111] = {
id = 218111,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.68.99",
quality = 3,
name = "丛林守卫",
desc = "（星宝农场稻草人装饰）可以提升蔬菜摊售价。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_004_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_MessageBoard_004_Comm",
modelType = 1
},
scaleTimes = 45,
bHideInBag = true,
rotateYaw = 225,
buff = "蔬菜摊售价提升",
buffValue = "+5%",
buffViewOffset = "-80,-80"
},
[218112] = {
id = 218112,
exceedReplaceItem = v2,
lowVer = v4,
name = "狐仙",
desc = "（星宝农场稻草人装饰）可以提升A鱼出现的概率。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_005_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_MessageBoard_005_A_Comm",
modelType = 1
},
scaleTimes = 42,
shareTexts = {
"狐仙显灵，好运不停"
},
bHideInBag = true,
rotateYaw = 225,
buff = "全水层A鱼出现概率",
buffValue = "+30%",
buffViewOffset = "0,0"
},
[218113] = {
id = 218113,
exceedReplaceItem = v2,
lowVer = v4,
name = "狸猫精灵",
desc = "（星宝农场稻草人装饰）可以提升动物小铺售价。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_006_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_MessageBoard_006_A_Comm",
modelType = 1
},
scaleTimes = 45,
bHideInBag = true,
rotateYaw = 205,
buff = "动物小铺售价提升",
buffValue = "+10%",
buffViewOffset = "0,0"
},
[218114] = {
id = 218114,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.18.1",
quality = 3,
name = "胡萝卜时钟",
desc = "（星宝农场时钟装饰）可以缩短加工时间。",
icon = "CDN:Icon_Farm_FU_CarrotClock_001_A_1_Comm",
resourceConf = {
model = "SK_Farm_FU_CarrotClock_001_Comm",
modelType = 1
},
scaleTimes = 30,
bHideInBag = true,
rotateYaw = 180,
buff = "加工时间缩短",
buffValue = "-2%",
buffViewOffset = "0,100"
},
[218115] = {
id = 218115,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = v4,
quality = 1,
name = "告白熊梦幻屋",
desc = "（星宝农场小屋装饰）提升收获动物时获得的农场经验。",
icon = "CDN:Icon_Farm_BuComm_Home_004_A_Comm",
resourceConf = {
model = "SK_Farm_OG_002",
modelType = 2,
idleAnim = "ASM_Farm_BuComm_Home_002_OpenIdle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_002_PV/Farm_OG_002_Intact",
scaleTimes = 12,
soundId = {
40652
},
outEnterSequence = "Farm_PV_OG_002",
shareTexts = {
"住进爱里，熊抱快乐"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218115.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218115.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218115.astc",
buff = "收获动物获得农场经验",
buffValue = "+10%",
buffViewOffset = "100,400"
},
[218116] = {
id = 218116,
exceedReplaceItem = v2,
lowVer = "1.3.18.1",
name = "仙人掌花屋",
desc = "（星宝农场小屋装饰）可以延长浇水后水分维持时间。",
icon = "CDN:Icon_Farm_BuComm_Home_003_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 12,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-15,
-100
},
buff = "水分维持时间增加",
buffValue = "+20%",
buffViewOffset = "0,300"
},
[218117] = {
id = 218117,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.37.1",
quality = 1,
name = "招财喵",
desc = "（星宝农场稻草人装饰）可以提高作物丰收产量。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_007_A_Comm",
resourceConf = {
model = "SK_Farm_OG_003",
modelType = 2,
idleAnim = "SM_Farm_BuComm_MessageBoard_007_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_003_PV/Farm_OG_003_Intact",
scaleTimes = 45,
soundId = {
40653
},
outEnterSequence = "Farm_PV_OG_003",
shareTexts = {
"举起爪爪，财富到家"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218117.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218117.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218117.astc",
buff = "作物丰收产量倍率",
buffValue = "+1",
buffViewOffset = "0,0"
},
[218118] = {
id = 218118,
exceedReplaceItem = v2,
lowVer = v4,
name = "云朵奶油时钟",
desc = "（星宝农场时钟装饰）可以延长浇水后水分维持时间。",
icon = "CDN:Icon_Farm_FU_CakeClock_001_A_Comm",
resourceConf = {
model = "SK_Farm_FU_CakeClock_001_Comm",
modelType = 1
},
scaleTimes = 25,
shareTexts = {
"让时间跟奶油一起融化"
},
bHideInBag = true,
rotateYaw = 200,
shareOffset = {
-5,
-70
},
buff = "水分维持时间增加",
buffValue = "+10%",
buffViewOffset = "0,100",
shareScaleTimes = 20,
shareRotateYaw = 190
},
[218119] = {
id = 218119,
exceedReplaceItem = v2,
lowVer = v4,
name = "蔷薇花车",
desc = "（星宝农场蔬菜摊装饰）可以提升蔬菜摊售价。",
icon = "CDN:Icon_Farm_BuComm_VegetableStall_003_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_VegetableStall_003_Comm",
modelType = 1
},
scaleTimes = 10,
shareTexts = {
"快来逛一逛，收获花花能量"
},
bHideInBag = true,
rotateYaw = 205,
shareOffset = {
-15,
-85
},
buff = "蔬菜摊售价提升",
buffValue = "+10%",
buffViewOffset = "100,400"
},
[218120] = {
id = 218120,
exceedReplaceItem = v2,
lowVer = v4,
name = "彩虹牧场",
desc = "（星宝农场动物小铺装饰）可以提升动物小铺售价。",
icon = "CDN:Icon_Farm_BuComm_XumuA_003_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_XumuA_003_Comm",
modelType = 1
},
scaleTimes = 10,
shareTexts = {
"我的牧场，每天都有彩虹"
},
bHideInBag = true,
rotateYaw = 205,
shareOffset = {
-15,
-75
},
buff = "动物小铺售价提升",
buffValue = "+10%",
buffViewOffset = "100,400",
shareScaleTimes = 7,
shareRotateYaw = 190
},
[218121] = {
id = 218121,
exceedReplaceItem = v2,
lowVer = v4,
name = "梦幻海洋屋",
desc = "（星宝农场水产摊装饰）可以提升水产摊售价。",
icon = "CDN:Icon_Farm_BuComm_FishStall_003_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_FishStall_003_Comm",
modelType = 1
},
scaleTimes = 10,
shareTexts = {
"小小鲸鱼，蓝色梦想"
},
bHideInBag = true,
rotateYaw = 205,
shareOffset = {
-12,
-80
},
buff = "水产摊售价提升",
buffValue = "+10%",
buffViewOffset = "100,400",
shareScaleTimes = 8,
shareRotateYaw = 190
},
[218122] = {
id = 218122,
exceedReplaceItem = v2,
lowVer = v4,
name = "花蔓时钟",
desc = "（星宝农场时钟装饰）可以延长浇水后水分维持时间。",
icon = "CDN:Icon_Farm_FU_FlowerClock_001_A_1_Comm",
resourceConf = {
model = "SK_Farm_FU_FlowerClock_001_Comm",
modelType = 1
},
scaleTimes = 22,
shareTexts = {
"花开一瞬，时光留存"
},
bHideInBag = true,
rotateYaw = 180,
buff = "水分维持时间增加",
buffValue = "+10%",
buffViewOffset = "0,100"
},
[218123] = {
id = 218123,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = v4,
quality = 1,
name = "绮丽海螺城堡",
desc = "（星宝农场小屋装饰）提升钓鱼时获得的农场经验。",
icon = "CDN:Icon_Farm_BuComm_Home_005_A_Comm",
resourceConf = {
model = "SK_Farm_OG_004",
modelType = 2,
idleAnim = "SK_Farm_OG_004_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_004_PV/Farm_OG_004_Intact",
scaleTimes = 10,
soundId = {
40654
},
outEnterSequence = "Farm_PV_OG_004",
shareTexts = {
"住海洋乐园，看彩虹泡泡"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218123.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218123.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218123.astc",
buff = "钓鱼时获得农场经验",
buffValue = "+20%",
buffViewOffset = "0,300"
},
[218124] = {
id = 218124,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.68.99",
quality = 1,
name = "海狮公主",
desc = "（星宝农场稻草人装饰）可以提高动物大丰收产量。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_008_A_Comm",
resourceConf = {
model = "SK_Farm_OG_005",
modelType = 2,
idleAnim = "SK_Farm_OG_005_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_005_PV/Farm_OG_005_Intact",
scaleTimes = 45,
soundId = {
40655
},
outEnterSequence = "Farm_PV_OG_005",
shareTexts = {
"公主亮相，产量棒棒"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218124.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218124.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218124.astc",
buff = "动物大丰收产量倍率",
buffValue = "+3",
buffViewOffset = "0,0"
},
[218125] = {
id = 218125,
exceedReplaceItem = v2,
lowVer = v4,
name = "璃海星光果行",
desc = "（星宝农场蔬菜摊装饰）可以提升蔬菜摊售价。",
icon = "CDN:Icon_Farm_BuComm_VegetableStall_002_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_VegetableStall_002_Comm",
modelType = 1
},
scaleTimes = 10,
shareTexts = {
"琉璃星光，蔬果闪亮"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
0,
-85
},
buff = "蔬菜摊售价提升",
buffValue = "+10%",
buffViewOffset = "100,100",
shareScaleTimes = 8
},
[218126] = {
id = 218126,
exceedReplaceItem = v2,
lowVer = v4,
name = "海盗宝藏小店",
desc = "（星宝农场动物小铺装饰）可以提升动物小铺售价。",
icon = "CDN:Icon_Farm_BuComm_XumuA_002_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_XumuA_002_Comm",
modelType = 1
},
scaleTimes = 10,
shareTexts = {
"小铺产量好，海盗当做宝"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-20,
-90
},
buff = "动物小铺售价提升",
buffValue = "+10%",
buffViewOffset = "100,100"
},
[218127] = {
id = 218127,
exceedReplaceItem = v2,
lowVer = v4,
name = "海妖鱼店",
desc = "（星宝农场水产摊装饰）可以提升水产摊售价。",
icon = "CDN:Icon_Farm_BuComm_FishStall_002_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_FishStall_002_Comm",
modelType = 1
},
scaleTimes = 10,
shareTexts = {
"尽享新鲜，海妖精选"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-25,
-90
},
buff = "水产摊售价提升",
buffValue = "+10%",
buffViewOffset = "100,100"
},
[218128] = {
id = 218128,
exceedReplaceItem = v2,
lowVer = v4,
name = "深海时钟",
desc = "（星宝农场时钟装饰）可以提升每日被祈福次数。",
icon = "CDN:Icon_Farm_FU_SeaClock_001_A_1_Comm",
resourceConf = {
model = "SK_Farm_FU_SeaClock_001_Comm",
modelType = 1
},
scaleTimes = 25,
shareTexts = {
"时光之浪，在此流淌"
},
bHideInBag = true,
rotateYaw = 270,
shareOffset = {
-10,
-70
},
buff = "每日被祈福次数",
buffValue = "+2",
buffViewOffset = "0,200"
},
[218129] = {
id = 218129,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = v4,
quality = 3,
name = "超会稻稻鹅",
desc = "（星宝农场稻草人装饰）可以提升每日可拿取次数。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_009_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_MessageBoard_009_Comm",
modelType = 1
},
scaleTimes = 45,
bHideInBag = true,
rotateYaw = 225,
buff = "每日可拿取次数",
buffValue = "+3",
buffViewOffset = "0,0"
},
[218130] = {
id = 218130,
exceedReplaceItem = v2,
lowVer = v4,
name = "梦幻熊礼盒",
desc = "（星宝农场装饰）获得后可以改变送礼时礼物盒的样式。",
icon = "CDN:Icon_Farm_Tool_Gift_002_A_Comm",
resourceConf = {
model = "SM_Farm_Tool_Gift_002_Comm",
modelType = 2,
idleAnim = "SM_Farm_Tool_Gift_002_Idle_Comm"
},
scaleTimes = 45,
shareTexts = {
"来个熊抱，礼物送到"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-12,
-75
}
},
[218131] = {
id = 218131,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = v5,
quality = 3,
name = "神农幻境",
desc = "（星宝农场风景装饰）获得后可以改变农场风景。",
icon = "Icon_Farm_Env_002_A",
bHideInBag = true
},
[218132] = {
id = 218132,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = v5,
quality = 3,
name = "桃源仙居",
desc = "（星宝农场院落装饰）获得后可以改变农场院落。",
icon = "Icon_Farm_Env_002_B",
bHideInBag = true
},
[218133] = {
id = 218133,
exceedReplaceItem = v2,
lowVer = "1.3.68.99",
name = "泡泡鱼礼盒",
desc = "（星宝农场装饰）获得后可以改变送礼时礼物盒的样式。",
icon = "CDN:Icon_Farm_Tool_Gift_004_A_Comm",
resourceConf = {
model = "SM_Farm_Tool_Gift_004_Comm",
modelType = 2,
idleAnim = "SM_Farm_Tool_Gift_004_Idle_Comm"
},
scaleTimes = 45,
shareTexts = {
"和祝福“泡”在一起吧"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-7,
-120
},
shareScaleTimes = 50
},
[218134] = {
id = 218134,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "雪花轻语果行",
desc = "（星宝农场蔬菜摊装饰）可以提高作物大丰收产量。",
icon = "CDN:Icon_BP_Farm_BuComm_WinterVegetableStall_001_A_Comm",
resourceConf = {
model = "SK_Farm_BuComm_WinterVegetableStall_001_Comm",
modelType = 2,
idleAnim = "Aim_Farm_BuComm_WinterVegetableStall_001_Comm"
},
scaleTimes = 9,
shareTexts = {
"雪花舞动，蔬果晶莹"
},
bHideInBag = true,
rotateYaw = 220,
shareOffset = {
-12,
-75
},
buff = "作物大丰收产量倍率",
buffValue = "+1",
buffViewOffset = "100,400",
shareScaleTimes = 6
},
[218135] = {
id = 218135,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "水晶鹿角小店",
desc = "（星宝农场动物小铺装饰）可以提高动物大丰收产量。",
icon = "CDN:Icon_BP_Farm_BuComm_WinterXumu_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_WinterXumu_001_Comm",
modelType = 1
},
scaleTimes = 9,
shareTexts = {
"鹿角小店，营养满满"
},
bHideInBag = true,
rotateYaw = 220,
shareOffset = {
-12,
-85
},
buff = "动物大丰收产量倍率",
buffValue = "+1",
buffViewOffset = "100,400",
shareScaleTimes = 8
},
[218136] = {
id = 218136,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "雪乡小Q鱼铺",
desc = "（星宝农场水产摊装饰）可以降低鱼饵价格。",
icon = "CDN:Icon_BP_Farm_BuComm_WinterFishStall_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_WinterFishStall_001_Comm",
modelType = 1
},
scaleTimes = 9,
shareTexts = {
"雪乡风味，鲜意满满"
},
bHideInBag = true,
rotateYaw = 220,
shareOffset = {
-2,
-80
},
buff = "鱼饵价格降低",
buffValue = "-2%",
buffViewOffset = "100,400",
shareScaleTimes = 8
},
[218137] = {
id = 218137,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.37.67",
quality = 1,
name = "雪球小精灵",
desc = "（星宝农场稻草人装饰）可以提升作物产量。",
icon = "CDN:Icon_Farm_BuComm_MessageBoard_010_A_Comm",
resourceConf = {
model = "SK_Farm_OG_007",
modelType = 2,
idleAnim = "SK_Farm_OG_007_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_007_PV/Farm_OG_007_Intact",
scaleTimes = 45,
soundId = {
40657
},
outEnterSequence = "Farm_PV_OG_007",
shareTexts = {
"冬日悄悄，精灵来到"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218137.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218137.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218137.astc",
buff = "作物产量提升",
buffValue = "+10%",
buffViewOffset = "0,0"
},
[218138] = {
id = 218138,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "糖果松树时钟",
desc = "（星宝农场时钟装饰）可以使钓鱼成熟时间缩短。",
icon = "CDN:Icon_Farm_FU_TreeClock_001_A_1_Comm",
resourceConf = {
model = "SK_Farm_FU_TreeClock_001_Comm",
modelType = 1
},
scaleTimes = 30,
shareTexts = {
"岁月流转，甜蜜随行"
},
bHideInBag = true,
rotateYaw = 210,
shareOffset = {
-12,
-75
},
buff = "钓鱼成熟时间缩短",
buffValue = "-2%",
buffViewOffset = "0,0",
shareScaleTimes = 22
},
[218139] = {
id = 218139,
exceedReplaceItem = v2,
lowVer = "1.3.37.67",
name = "冬日萌宠屋",
desc = "（星宝农场宠物屋装饰）可以提升加工作物的售价。",
icon = "CDN:Icon_BP_Farm_BuComm_WinterDogHouse_001_A_1_Comm",
resourceConf = {
model = "SM_Farm_BuComm_WinterDogHouse_001_A_1_Comm",
modelType = 1
},
scaleTimes = 35,
shareTexts = {
"心意满满，只为宠爱"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-2,
-65
},
buff = "加工作物的售价提升",
buffValue = "+30%",
buffViewOffset = "0,0",
shareScaleTimes = 30
},
[218140] = {
id = 218140,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "银风山谷",
desc = "（星宝农场风景装饰）获得后可以改变农场风景。",
icon = "CDN:Icon_Farm_Env_003_A_Comm",
picture = "T_Farmyard_BigIcon_Landscape_1",
bHideInBag = true
},
[218141] = {
id = 218141,
exceedReplaceItem = v2,
lowVer = "1.3.37.55",
name = "冬雪庄园",
desc = "（星宝农场院落装饰）获得后可以改变农场院落。",
icon = "CDN:Icon_Farm_Env_003_B_Comm",
picture = "T_Farmyard_BigIcon_Yard_1",
bHideInBag = true
},
[218142] = {
id = 218142,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.37.55",
quality = 1,
name = "冰晶星梦城堡",
desc = "（星宝农场小屋装饰）提升收获作物时获得的农场经验。",
icon = "CDN:Icon_BP_Farm_BuComm_WinterHome_001_A_Comm",
resourceConf = {
model = "SK_Farm_OG_006",
modelType = 2,
idleAnim = "SK_Farm_OG_006_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_006_PV/Farm_OG_006_Intact",
scaleTimes = 8,
soundId = {
40656
},
outEnterSequence = "Farm_PV_OG_006",
shareTexts = {
"梦幻雪宫，等你加冕"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218142.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218142.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218142.astc",
buff = "收获作物获得农场经验",
buffValue = "+15%",
buffViewOffset = "100,400"
},
[218500] = {
id = 218500,
lowVer = v4,
quality = 4,
name = "帝王蝶",
icon = "Icon_Farm_Col_BiaoBen_001_A",
bHideInBag = true
},
[218501] = {
id = 218501,
lowVer = v4,
quality = 3,
name = "一箱私房钱",
icon = "Icon_Farm_Col_Qian_001_A",
bHideInBag = true
},
[218502] = {
id = 218502,
lowVer = v4,
quality = 3,
name = "黄金蚯蚓",
icon = "Icon_Farm_Col_QiuYin_001_A",
bHideInBag = true
},
[218503] = {
id = 218503,
lowVer = v4,
quality = 4,
name = "妙味松花蛋",
icon = "Icon_Farm_Col_SongHuaDan_001_A",
bHideInBag = true
},
[218504] = {
id = 218504,
lowVer = v4,
quality = 3,
name = "七彩毛团",
icon = "Icon_Farm_Col_MaoTuan_001_A",
bHideInBag = true
},
[218505] = {
id = 218505,
lowVer = v4,
quality = 1,
name = "黄金便便",
icon = "Icon_Farm_Col_BianBian_001_A",
bHideInBag = true
},
[218506] = {
id = 218506,
lowVer = v4,
quality = 4,
name = "止痛药丸",
icon = "Icon_Farm_Col_YaoWan_001_A",
bHideInBag = true
},
[218507] = {
id = 218507,
lowVer = v4,
quality = 4,
name = "蒙面头巾",
icon = "Icon_Farm_Col_TouJin_001_A",
bHideInBag = true
},
[218508] = {
id = 218508,
lowVer = v4,
name = "电子芯片",
icon = "Icon_Farm_Col_XinPian_001_A",
bHideInBag = true
},
[218509] = {
id = 218509,
lowVer = v4,
quality = 4,
name = "人鱼吊坠",
icon = "Icon_Farm_Col_SCPPendant_001_A",
bHideInBag = true
},
[218510] = {
id = 218510,
lowVer = v4,
name = "金斧头",
icon = "Icon_Farm_Col_SCPGoldaxe_001_A",
bHideInBag = true
},
[218511] = {
id = 218511,
lowVer = v4,
quality = 1,
name = "水晶鞋",
icon = "Icon_Farm_Col_SCPGlassshoes_001_A",
bHideInBag = true
},
[218512] = {
id = 218512,
lowVer = v4,
quality = 3,
name = "雨丝阳伞",
icon = "Icon_Farm_Col_SCPUmbrella_001_A",
bHideInBag = true
},
[218513] = {
id = 218513,
lowVer = v4,
name = "雷击面具",
icon = "Icon_Farm_Col_SCPMask_001_A",
bHideInBag = true
},
[218514] = {
id = 218514,
lowVer = v4,
quality = 1,
name = "雷水晶",
icon = "Icon_Farm_Col_SCPCrystal_001_A",
bHideInBag = true
},
[218515] = {
id = 218515,
lowVer = "1.3.37.1",
quality = 3,
name = "巨兽躯干化石",
icon = "Icon_Farm_Col_Dinosaurfossils_002_A",
bHideInBag = true
},
[218516] = {
id = 218516,
lowVer = "1.3.37.1",
quality = 1,
name = "石中剑",
icon = "Icon_Farm_Col_PixelSword_001_A",
bHideInBag = true
},
[218517] = {
id = 218517,
lowVer = "1.3.37.1",
quality = 4,
name = "巨兽尾部化石",
icon = "Icon_Farm_Col_Dinosaurfossils_003_A",
bHideInBag = true
},
[218518] = {
id = 218518,
lowVer = "1.3.37.1",
quality = 3,
name = "独角仙",
icon = "Icon_Farm_Col_UnicornBug_001_A",
bHideInBag = true
},
[218519] = {
id = 218519,
lowVer = "1.3.37.1",
name = "巨兽头部化石",
icon = "Icon_Farm_Col_Dinosaurfossils_001_A",
bHideInBag = true
},
[218520] = {
id = 218520,
lowVer = "1.3.37.1",
quality = 4,
name = "神秘石像",
icon = "Icon_Farm_Col_Statue_001_A",
bHideInBag = true
},
[218521] = {
id = 218521,
lowVer = "1.3.37.1",
quality = 4,
name = "放大镜",
icon = "Icon_Farm_Col_Magnifier_001_A",
bHideInBag = true
},
[218522] = {
id = 218522,
lowVer = "1.3.37.1",
quality = 4,
name = "古董计算机",
icon = "Icon_Farm_Col_Computer_001_A",
bHideInBag = true
},
[218523] = {
id = 218523,
lowVer = v5,
quality = 3,
name = "神秘的短笛",
icon = "ICON_BP_Farm_Col_SCPPiccolo_01_A",
bHideInBag = true
},
[218524] = {
id = 218524,
lowVer = v5,
name = "黄金圣甲虫",
icon = "ICON_BP_Farm_Col_SCPBeetle_01_A",
bHideInBag = true
},
[218525] = {
id = 218525,
lowVer = v5,
quality = 1,
name = "古老的天平",
icon = "ICON_BP_Farm_Col_SCPLibra_01_A",
bHideInBag = true
},
[218528] = {
id = 218528,
lowVer = v5,
name = "独角龙头部化石",
icon = "Icon_Farm_Col_Triceratops_001_A",
bHideInBag = true
},
[218529] = {
id = 218529,
lowVer = v5,
quality = 3,
name = "独角龙躯干化石",
icon = "Icon_Farm_Col_Triceratops_002_A",
bHideInBag = true
},
[218530] = {
id = 218530,
lowVer = v5,
quality = 4,
name = "蓝色豆娘",
icon = "Icon_Farm_Col_DragonFly_001_A",
bHideInBag = true
},
[218531] = {
id = 218531,
lowVer = v5,
quality = 4,
name = "独角龙尾部化石",
icon = "Icon_Farm_Col_Triceratops_003_A",
bHideInBag = true
},
[218532] = {
id = 218532,
lowVer = v5,
quality = 3,
name = "猫头鹰木雕",
icon = "Icon_Farm_Col_WoodenOwl_001_A",
bHideInBag = true
},
[218533] = {
id = 218533,
lowVer = v5,
quality = 1,
name = "金元宝",
icon = "Icon_Farm_Col_YuanBao_001_A",
bHideInBag = true
},
[218534] = {
id = 218534,
lowVer = v5,
name = "精美的蛋",
icon = "Icon_Farm_Col_Aartifact_001_A",
bHideInBag = true
},
[218535] = {
id = 218535,
lowVer = v5,
quality = 3,
name = "复古唱片机",
icon = "Icon_Farm_Col_RetroCD_001_A",
bHideInBag = true
},
[218536] = {
id = 218536,
lowVer = v5,
quality = 4,
name = "古老卷轴",
icon = "Icon_Farm_Col_Decree_001_A",
bHideInBag = true
},
[218537] = {
id = 218537,
lowVer = v5,
quality = 4,
name = "狐狸面罩",
icon = "Icon_Farm_Col_FoxMask_001_A",
bHideInBag = true
},
[218538] = {
id = 218538,
lowVer = v5,
quality = 1,
name = "海神三叉戟",
icon = "Icon_Farm_Col_Trident_001_A",
bHideInBag = true
},
[218539] = {
id = 218539,
lowVer = v5,
quality = 4,
name = "铁船锚",
icon = "Icon_Farm_Col_Anchor_001_A",
bHideInBag = true
},
[218540] = {
id = 218540,
lowVer = v5,
quality = 3,
name = "寻宝图",
icon = "Icon_Farm_Col_TreasureMap_001_A",
bHideInBag = true
},
[218553] = {
id = 218553,
lowVer = v5,
quality = 4,
name = "牛语翻译器",
icon = "Icon_Farm_Col_SCPinterphone_01_A",
bHideInBag = true,
buffValue = "动物加工物售价提升",
buffViewOffset = "0.01"
},
[218554] = {
id = 218554,
lowVer = v5,
quality = 1,
name = "招财牛",
icon = "Icon_Farm_Col_SCPdairycattle_01_A",
bHideInBag = true,
buffValue = "提前助产时间提升",
buffViewOffset = "0.03"
},
[218800] = {
id = 218800,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "冰激凌机",
desc = "星宝农场小屋家具。",
icon = "CDN:ICON_BP_Farm_De_S8_IceCream_01_1",
useParam = {
10112
},
bHideInBag = true,
bagType = "IBT_Farm"
},
[218801] = {
id = 218801,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "爱心小熊沙发",
desc = "星宝农场小屋家具。",
icon = "CDN:ICON_SHOP_Farm_Be_S8_CuteSofa_01_1",
useParam = {
10268
},
bHideInBag = true,
bagType = "IBT_Farm"
},
[219200] = {
id = 219200,
exceedReplaceItem = v2,
quality = 3,
name = "小煤球",
desc = "（星宝农场宠物）可以保护农场产物",
icon = "CDN:Icon_Farm_Keji_002_Comm",
resourceConf = {
model = "SM_Farm_Keji_001_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219201] = {
id = 219201,
exceedReplaceItem = v2,
quality = 3,
name = "小二哈",
desc = "（星宝农场宠物）可以保护农场产物",
icon = "CDN:Icon_Farm_Husky_001_Comm",
resourceConf = {
model = "SK_Farm_Husky_001_Comm",
modelType = 2,
idleAnim = "SK_Farm_Husky_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219202] = {
id = 219202,
exceedReplaceItem = v2,
quality = 3,
name = "小乌云",
desc = "（星宝农场宠物）可以保护农场产物",
icon = "CDN:Icon_Farm_Husky_002_Comm",
resourceConf = {
model = "SK_Farm_Husky_002_Comm",
modelType = 2,
idleAnim = "SK_Farm_Husky_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
}
}

local mt = {
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
quality = 2,
desc = "（星宝农场藏品）",
useType = "IUTO_SendToFarm",
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
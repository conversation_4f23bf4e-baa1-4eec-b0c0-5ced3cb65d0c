--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 头像框

local data = {
[840306] = {
id = 840306,
type = "ItemType_Frame",
maxNum = 1,
lowVer = "1.3.88.1",
quality = 2,
name = "卓大王",
desc = "卓大王祈愿活动获得",
icon = "D_HeadFrame_260",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1747324800
}
},
[840307] = {
id = 840307,
type = "ItemType_Frame",
maxNum = 1,
lowVer = "1.3.88.1",
quality = 2,
name = "动感猫猫",
desc = "幻喵音境祈愿活动获得",
icon = "D_HeadFrame_281",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1749744000
}
},
[840308] = {
id = 840308,
type = "ItemType_Frame",
maxNum = 1,
lowVer = "1.3.88.1",
quality = 2,
name = "霓虹喵喵机",
desc = "限时活动获得",
icon = "D_HeadFrame_280",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1748448000
}
},
[840309] = {
id = 840309,
type = "ItemType_Frame",
maxNum = 1,
lowVer = "1.3.88.1",
quality = 2,
name = "喵喵次元袋",
desc = "限时活动获得",
icon = "D_HeadFrame_282",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1748448000
}
},
[840310] = {
id = 840310,
type = "ItemType_Frame",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.88.1",
quality = 2,
name = "蝶舞花间",
desc = "蝶舞花间祈愿活动获得",
icon = "D_HeadFrame_263",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1749139200
}
},
[840311] = {
id = 840311,
type = "ItemType_Frame",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.88.1",
quality = 3,
name = "奖杯征程占坑头像框蓝",
desc = "蝶舞花间祈愿活动获得",
icon = "D_HeadFrame_263",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1749139200
}
},
[840312] = {
id = 840312,
type = "ItemType_Frame",
maxNum = 1,
quality = 3,
name = "白泽小仙头像框",
desc = "王冠商铺兑换中获得",
icon = "CDN:T_HeadFrame_284",
showInView = 1,
isDynamic = 0
},
[840313] = {
id = 840313,
type = "ItemType_Frame",
maxNum = 1,
lowVer = "1.3.88.1",
quality = 4,
name = "极酷新星",
desc = "S13通行证获得",
icon = "CDN:T_HeadFrame_277",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1746115200
}
},
[840314] = {
id = 840314,
type = "ItemType_Frame",
maxNum = 1,
lowVer = "1.3.88.1",
quality = 3,
name = "礼法有度",
desc = "S13通行证获得",
icon = "CDN:T_HeadFrame_276",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1746115200
}
},
[840315] = {
id = 840315,
type = "ItemType_Frame",
maxNum = 1,
lowVer = "1.3.88.1",
quality = 2,
name = "闪耀魔法",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_275",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1746115200
}
},
[840316] = {
id = 840316,
type = "ItemType_Frame",
maxNum = 1,
lowVer = "1.3.88.1",
quality = 2,
name = "超能学园",
desc = "S13赛季限时活动获得",
icon = "D_HeadFrame_274",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1746115200
}
},
[840317] = {
id = 840317,
type = "ItemType_Frame",
maxNum = 1,
lowVer = "1.3.88.1",
quality = 2,
name = "炽光海岸",
desc = "炽光海岸祈愿中获得",
icon = "CDN:T_HeadFrame_288",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1746028800
}
},
[840318] = {
id = 840318,
type = "ItemType_Frame",
maxNum = 1,
lowVer = "1.3.26.1",
quality = 3,
name = "航海计划头像框",
desc = "航海计划活动获得",
icon = "CDN:T_HeadFrame_185",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1746028800
}
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-面饰

local v0 = {
seconds = 1676390400
}

local v1 = {
seconds = 4074768000
}

local v2 = {
seconds = 4074854400
}

local v3 = {
2,
11
}

local v4 = 1

local v5 = 6

local v6 = 200008

local v7 = 10000

local v8 = 10

local v9 = 5

local v10 = "1.2.100.1"

local v11 = "1.2.67.1"

local data = {
[50001] = {
commodityId = 50001,
commodityName = "猫爪眼镜",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopTag = v3,
shopSort = 1,
jumpId = 16,
jumpText = "赛季兑换",
itemIds = {
610001
},
bOpenSuit = true,
suitId = 50001
},
[50002] = {
commodityId = 50002,
commodityName = "超级玩家眼镜",
beginTime = v0,
shopTag = v3,
shopSort = 2,
jumpId = 705,
jumpText = "印章祈愿",
itemIds = {
610002
},
bOpenSuit = true,
suitId = 50002
},
[50004] = {
commodityId = 50004,
commodityName = "栅格太阳镜",
coinType = 6,
price = 500,
beginTime = v0,
shopTag = v3,
itemIds = {
610004
},
canGift = true,
addIntimacy = 50,
giftCoinType = 1,
giftPrice = 500,
suitId = 50003
},
[50005] = {
commodityId = 50005,
commodityName = "赛博墨镜",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopTag = v3,
shopSort = 3,
jumpId = 45,
jumpText = "娱乐大师礼",
itemIds = {
610005
},
bOpenSuit = true,
suitId = 50004
},
[50006] = {
commodityId = 50006,
commodityName = "流光护目镜",
beginTime = v0,
endTime = {
seconds = 1676476800
},
shopTag = v3,
shopSort = 6,
jumpId = 54,
jumpText = "时装礼包",
itemIds = {
610006
},
bOpenSuit = true,
suitId = 50005
},
[50007] = {
commodityId = 50007,
commodityName = "钱币眼罩",
coinType = 6,
price = 50,
beginTime = v0,
endTime = {
seconds = 1676476800
},
shopTag = v3,
itemIds = {
610007
},
suitId = 50006
},
[50008] = {
commodityId = 50008,
commodityName = "不许看眼罩",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v3,
shopSort = 4,
jumpId = 1063,
jumpText = "守护之翼",
minVersion = "1.3.26.33",
itemIds = {
610008
},
bOpenSuit = true,
suitId = 50007
},
[50009] = {
commodityId = 50009,
commodityName = "潮流眼镜",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = {
seconds = 1676476800
},
shopTag = v3,
itemIds = {
610009
},
suitId = 50008
},
[50010] = {
commodityId = 50010,
commodityName = "剑魂之瞳",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopTag = v3,
shopSort = 1,
jumpId = 11,
jumpText = "赛季祈愿",
itemIds = {
610010
},
bOpenSuit = true,
suitId = 50009
},
[50011] = {
commodityId = 50011,
commodityName = "剑魂之瞳",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1702569600
},
itemIds = {
610011
}
},
[50012] = {
commodityId = 50012,
commodityName = "剑魂之瞳",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1702569600
},
itemIds = {
610012
}
},
[50013] = {
commodityId = 50013,
commodityName = "美梦眼罩",
beginTime = {
seconds = 1709827200
},
endTime = {
seconds = 1710431999
},
shopTag = v3,
shopSort = 2,
jumpId = 22,
jumpText = "云端星梦",
itemIds = {
610013
},
bOpenSuit = true,
suitId = 50010
},
[50014] = {
commodityId = 50014,
commodityName = "美梦眼罩",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704643200
},
itemIds = {
610014
}
},
[50015] = {
commodityId = 50015,
commodityName = "美梦眼罩",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704643200
},
itemIds = {
610015
}
},
[50016] = {
commodityId = 50016,
commodityName = "蝶语幻镜",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopTag = v3,
shopSort = 1,
jumpId = 11,
jumpText = "赛季祈愿",
itemIds = {
610016
},
bOpenSuit = true,
suitId = 50011
},
[50017] = {
commodityId = 50017,
commodityName = "蝶语幻镜",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1702569600
},
itemIds = {
610017
}
},
[50018] = {
commodityId = 50018,
commodityName = "蝶语幻镜",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1702569600
},
itemIds = {
610018
}
},
[50019] = {
commodityId = 50019,
commodityName = "小新红黑墨镜",
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1715875199
},
shopTag = v3,
shopSort = 1,
jumpId = 80,
jumpText = "盛装小新祈愿",
itemIds = {
610019
},
bOpenSuit = true,
suitId = 50012
},
[50020] = {
commodityId = 50020,
commodityName = "小新双色眼镜",
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1715875199
},
shopTag = v3,
shopSort = 1,
jumpId = 78,
jumpText = "盛装小新祈愿",
itemIds = {
610020
},
bOpenSuit = true,
suitId = 50013
},
[50021] = {
commodityId = 50021,
commodityName = "星星眼镜",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopTag = v3,
shopSort = 5,
jumpId = 9,
jumpText = "桃源通行证",
itemIds = {
610021
},
bOpenSuit = true,
suitId = 50014
},
[50022] = {
commodityId = 50022,
commodityName = "星星眼镜",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1702569600
},
itemIds = {
610022
}
},
[50023] = {
commodityId = 50023,
commodityName = "星星眼镜",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1702569600
},
itemIds = {
610023
}
},
[50024] = {
commodityId = 50024,
commodityName = "腮红眼镜",
beginTime = v0,
shopTag = v3,
jumpId = 123,
jumpText = "扫码一起玩",
itemIds = {
610024
},
bOpenSuit = true,
suitId = 50015
},
[50025] = {
commodityId = 50025,
commodityName = "腮红眼镜",
coinType = 200008,
price = 5,
beginTime = v0,
itemIds = {
610025
}
},
[50026] = {
commodityId = 50026,
commodityName = "腮红眼镜",
coinType = 200008,
price = 5,
beginTime = v0,
itemIds = {
610026
}
},
[50027] = {
commodityId = 50027,
commodityName = "古典单片眼镜",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v3,
jumpId = 1062,
jumpText = "月夜歌吟",
minVersion = "1.3.26.33",
itemIds = {
610027
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 50016
},
[50028] = {
commodityId = 50028,
commodityName = "古典单片眼镜",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
610028
}
},
[50029] = {
commodityId = 50029,
commodityName = "古典单片眼镜",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
610029
}
},
[50030] = {
commodityId = 50030,
commodityName = "工匠单片眼镜",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = {
seconds = 1676476800
},
shopTag = v3,
itemIds = {
610030
},
suitId = 50017
},
[50031] = {
commodityId = 50031,
commodityName = "不许看眼罩",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704024000
},
itemIds = {
610031
}
},
[50032] = {
commodityId = 50032,
commodityName = "不许看眼罩",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704024000
},
itemIds = {
610032
}
},
[50033] = {
commodityId = 50033,
commodityName = "金钻世家",
beginTime = v0,
shopTag = v3,
shopSort = 7,
jumpId = 8,
jumpText = "充值福利",
itemIds = {
610033
},
bOpenSuit = true,
suitId = 50018
},
[50034] = {
commodityId = 50034,
commodityName = "金钻世家",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
610034
}
},
[50035] = {
commodityId = 50035,
commodityName = "金钻世家",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
610035
}
},
[50036] = {
commodityId = 50036,
commodityName = "舞会羽毛眼镜",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1726761599
},
shopTag = v3,
shopSort = 1,
jumpId = 185,
jumpText = "霜天冰雨",
itemIds = {
610036
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 50019
},
[50037] = {
commodityId = 50037,
commodityName = "舞会羽毛眼镜",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
610037
}
},
[50038] = {
commodityId = 50038,
commodityName = "舞会羽毛眼镜",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
610038
}
},
[50039] = {
commodityId = 50039,
commodityName = "草莓眼镜",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = {
seconds = 1676476800
},
shopTag = v3,
itemIds = {
610039
},
suitId = 50020
},
[50040] = {
commodityId = 50040,
commodityName = "草莓眼镜",
coinType = 200008,
price = 5,
beginTime = v0,
itemIds = {
610040
}
},
[50041] = {
commodityId = 50041,
commodityName = "草莓眼镜",
coinType = 200008,
price = 5,
beginTime = v0,
itemIds = {
610041
}
},
[50042] = {
commodityId = 50042,
commodityName = "阿童木红框眼镜",
beginTime = {
seconds = 1717171200
},
endTime = {
seconds = 1718899199
},
shopTag = v3,
shopSort = 1,
jumpId = 182,
jumpText = "阿童木祈愿",
minVersion = v10,
itemIds = {
610042
},
bOpenSuit = true,
suitId = 50021
},
[50043] = {
commodityId = 50043,
commodityName = "黑框眼镜",
beginTime = {
seconds = 1729526400
},
endTime = {
seconds = 4080211199
},
shopTag = v3,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
itemIds = {
610043
},
canGift = true,
addIntimacy = 40,
giftCoinType = 205,
giftPrice = 20,
bOpenSuit = true,
suitId = 50022
},
[50044] = {
commodityId = 50044,
commodityName = "月光公主--面饰",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v3,
jumpId = 1062,
jumpText = "月夜歌吟",
minVersion = "1.3.26.33",
itemIds = {
610044
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 50023
},
[50045] = {
commodityId = 50045,
commodityName = "月光公主--面饰",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
610045
}
},
[50046] = {
commodityId = 50046,
commodityName = "月光公主--面饰",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
610046
}
},
[50047] = {
commodityId = 50047,
commodityName = "柠檬猫爪眼镜",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = {
seconds = 1676476800
},
shopTag = v3,
itemIds = {
610047
},
suitId = 50024
},
[51000] = {
commodityId = 51000,
commodityName = "赛罗眼镜",
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1711295999
},
shopTag = v3,
jumpId = 179,
jumpText = "奥特曼祈愿",
minVersion = v11,
itemIds = {
610048
},
bOpenSuit = true,
suitId = 50025
},
[51001] = {
commodityId = 51001,
commodityName = "木芙蓉花面饰",
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = 1713455999
},
shopTag = v3,
jumpId = 181,
jumpText = "绮梦灯",
minVersion = "1.2.80.1",
itemIds = {
610049
},
bOpenSuit = true,
suitId = 50026
},
[51002] = {
commodityId = 51002,
commodityName = "木芙蓉花面饰",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707494400
},
minVersion = "1.2.80.1",
itemIds = {
610050
}
},
[51003] = {
commodityId = 51003,
commodityName = "木芙蓉花面饰",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707494400
},
minVersion = "1.2.80.1",
itemIds = {
610051
}
},
[51004] = {
commodityId = 51004,
commodityName = "一支玫瑰面饰",
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1710086399
},
shopTag = v3,
shopSort = 1,
jumpId = 183,
jumpText = "冰雪玫瑰",
minVersion = "1.2.80.1",
itemIds = {
610052
},
bOpenSuit = true,
suitId = 50027
},
[51005] = {
commodityId = 51005,
commodityName = "一支玫瑰面饰",
coinType = 200008,
price = 5,
beginTime = v0,
endTime = v2,
minVersion = v11,
itemIds = {
610053
}
},
[51006] = {
commodityId = 51006,
commodityName = "一支玫瑰面饰",
coinType = 200008,
price = 5,
beginTime = v0,
endTime = v2,
minVersion = v11,
itemIds = {
610054
}
},
[51007] = {
commodityId = 51007,
commodityName = "泡泡糖",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v11,
itemIds = {
610055
},
suitId = 50028
},
[51008] = {
commodityId = 51008,
commodityName = "奶嘴",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v11,
itemIds = {
610056
},
suitId = 50029
},
[51009] = {
commodityId = 51009,
commodityName = "大侠柳叶",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v11,
itemIds = {
610057
},
suitId = 50030
},
[51010] = {
commodityId = 51010,
commodityName = "柯南眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v11,
itemIds = {
610058
},
suitId = 50031
},
[51011] = {
commodityId = 51011,
commodityName = "绷带井字纱布",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v11,
itemIds = {
610059
},
suitId = 50032
},
[51012] = {
commodityId = 51012,
commodityName = "红包眼镜",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v3,
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.26.61",
itemIds = {
610060
},
bOpenSuit = true,
suitId = 50033
},
[51013] = {
commodityId = 51013,
commodityName = "雪花眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v11,
itemIds = {
610061
},
suitId = 50034
},
[51014] = {
commodityId = 51014,
commodityName = "雪豹眼镜",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
shopTag = v3,
jumpId = 189,
jumpText = "印章祈愿",
minVersion = v11,
itemIds = {
610062
},
bOpenSuit = true,
suitId = 50035
},
[51015] = {
commodityId = 51015,
commodityName = "老虎面具",
endTime = {
seconds = 1710431999
},
shopTag = v3,
shopSort = 1,
jumpId = 175,
jumpText = "赛季祈愿",
minVersion = v11,
itemIds = {
610063
},
bOpenSuit = true,
suitId = 50036
},
[51016] = {
commodityId = 51016,
commodityName = "老虎面具",
coinType = 200008,
price = 5,
minVersion = v11,
itemIds = {
610064
}
},
[51017] = {
commodityId = 51017,
commodityName = "老虎面具",
coinType = 200008,
price = 5,
minVersion = v11,
itemIds = {
610065
}
},
[51018] = {
commodityId = 51018,
commodityName = "梅花面纱",
endTime = {
seconds = 1710431999
},
shopTag = v3,
shopSort = 1,
jumpId = 9,
jumpText = "山海通行证",
minVersion = v11,
itemIds = {
610066
},
bOpenSuit = true,
suitId = 50037
},
[51019] = {
commodityId = 51019,
commodityName = "梅花面纱",
coinType = 200008,
price = 5,
minVersion = v11,
itemIds = {
610067
}
},
[51020] = {
commodityId = 51020,
commodityName = "梅花面纱",
coinType = 200008,
price = 5,
minVersion = v11,
itemIds = {
610068
}
},
[51021] = {
commodityId = 51021,
commodityName = "财神面具",
coinType = 200008,
price = 5,
beginTime = v0,
minVersion = v11,
itemIds = {
610069
}
},
[51022] = {
commodityId = 51022,
commodityName = "财神面具",
coinType = 200008,
price = 5,
beginTime = v0,
minVersion = v11,
itemIds = {
610070
}
},
[51023] = {
commodityId = 51023,
commodityName = "财神面具",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v11,
itemIds = {
610007
},
suitId = 50006
},
[51024] = {
commodityId = 51024,
commodityName = "回溯时刻",
beginTime = {
seconds = 1709827200
},
endTime = {
seconds = 1711641599
},
shopTag = v3,
jumpId = 188,
jumpText = "幸运祈愿",
minVersion = v11,
itemIds = {
610071
},
bOpenSuit = true,
suitId = 50038
},
[51025] = {
commodityId = 51025,
commodityName = "回溯时刻",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1709827200
},
minVersion = v11,
itemIds = {
610072
}
},
[51026] = {
commodityId = 51026,
commodityName = "回溯时刻",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1709827200
},
minVersion = v11,
itemIds = {
610073
}
},
[51027] = {
commodityId = 51027,
commodityName = "人鱼泪",
endTime = {
seconds = 1710431999
},
shopTag = v3,
shopSort = 1,
jumpId = 175,
jumpText = "赛季祈愿",
minVersion = v11,
itemIds = {
610074
},
bOpenSuit = true,
suitId = 50039
},
[51028] = {
commodityId = 51028,
commodityName = "人鱼泪",
coinType = 200008,
price = 10,
minVersion = v11,
itemIds = {
610075
}
},
[51029] = {
commodityId = 51029,
commodityName = "人鱼泪",
coinType = 200008,
price = 10,
minVersion = v11,
itemIds = {
610076
}
},
[51030] = {
commodityId = 51030,
commodityName = "马赛克眼镜",
beginTime = {
seconds = 1729526400
},
endTime = {
seconds = 4080211199
},
shopTag = v3,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.2.80.1",
itemIds = {
610077
},
canGift = true,
addIntimacy = 40,
giftCoinType = 205,
giftPrice = 20,
bOpenSuit = true,
suitId = 50040
},
[51031] = {
commodityId = 51031,
commodityName = "金钱眼镜",
beginTime = {
seconds = 1729526400
},
endTime = {
seconds = 4080211199
},
shopTag = v3,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.2.80.1",
itemIds = {
610078
},
canGift = true,
addIntimacy = 40,
giftCoinType = 205,
giftPrice = 20,
bOpenSuit = true,
suitId = 50041
},
[51101] = {
commodityId = 51101,
commodityName = "豆柴眼罩",
beginTime = {
seconds = 1719590400
},
endTime = {
seconds = 1720713599
},
shopTag = v3,
shopSort = 1,
jumpId = 600,
jumpText = "小肥柴祈愿",
minVersion = "1.3.7.73",
itemIds = {
610079
},
bOpenSuit = true,
suitId = 50042
},
[51102] = {
commodityId = 51102,
commodityName = "睿智之眼",
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1719503999
},
shopTag = v3,
shopSort = 1,
jumpId = 186,
jumpText = "春水溯游",
minVersion = v10,
itemIds = {
610080
},
bOpenSuit = true,
suitId = 50043
},
[51103] = {
commodityId = 51103,
commodityName = "睿智之眼",
coinType = 200008,
price = 5,
itemIds = {
610081
}
},
[51104] = {
commodityId = 51104,
commodityName = "睿智之眼",
coinType = 200008,
price = 5,
itemIds = {
610082
}
},
[51105] = {
commodityId = 51105,
commodityName = "流星之眼",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v3,
shopSort = 1,
jumpId = 189,
jumpText = "赛季祈愿",
itemIds = {
610083
},
bOpenSuit = true,
suitId = 50044
},
[51106] = {
commodityId = 51106,
commodityName = "流星之眼",
coinType = 200008,
price = 10,
itemIds = {
610084
}
},
[51107] = {
commodityId = 51107,
commodityName = "流星之眼",
coinType = 200008,
price = 10,
itemIds = {
610085
}
},
[51108] = {
commodityId = 51108,
commodityName = "宗师眼镜",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopTag = v3,
jumpId = 1048,
jumpText = "功夫熊猫返场",
minVersion = "1.3.7.97",
itemIds = {
610086
},
bOpenSuit = true,
suitId = 50045
},
[51109] = {
commodityId = 51109,
commodityName = "潮宝风尚",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v3,
shopSort = 1,
jumpId = 189,
jumpText = "赛季祈愿",
itemIds = {
610087
},
bOpenSuit = true,
suitId = 50046
},
[51110] = {
commodityId = 51110,
commodityName = "潮宝风尚",
coinType = 200008,
price = 5,
itemIds = {
610088
}
},
[51111] = {
commodityId = 51111,
commodityName = "潮宝风尚",
coinType = 200008,
price = 5,
itemIds = {
610089
}
},
[51112] = {
commodityId = 51112,
commodityName = "绅士礼仪",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v3,
jumpId = 1082,
jumpText = "遗落珍宝",
itemIds = {
610090
},
suitId = 50047
},
[51113] = {
commodityId = 51113,
commodityName = "菠菠萝眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610091
},
suitId = 50048
},
[51114] = {
commodityId = 51114,
commodityName = "江湖过客",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610092
},
suitId = 50049
},
[51115] = {
commodityId = 51115,
commodityName = "不等式眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610093
},
suitId = 50050
},
[51116] = {
commodityId = 51116,
commodityName = "学霸之力",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v3,
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.26.61",
itemIds = {
610094
},
bOpenSuit = true,
suitId = 50051
},
[51117] = {
commodityId = 51117,
commodityName = "猎豹眼镜",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
shopTag = v3,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
610095
},
bOpenSuit = true,
suitId = 50052
},
[51118] = {
commodityId = 51118,
commodityName = "夜猫子眼镜",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1726761599
},
shopTag = v3,
shopSort = 1,
jumpId = 185,
jumpText = "霜天冰雨",
itemIds = {
610096
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 50053
},
[51119] = {
commodityId = 51119,
commodityName = "彩虹眼镜",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v3,
shopSort = 1,
jumpId = 9,
jumpText = "时光通行证",
itemIds = {
610097
},
bOpenSuit = true,
suitId = 50054
},
[51120] = {
commodityId = 51120,
commodityName = "绒绒草",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v3,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.68.52",
itemIds = {
610098
},
bOpenSuit = true,
suitId = 50055
},
[51121] = {
commodityId = 51121,
commodityName = "夜猫子眼镜",
coinType = 200008,
price = 5,
itemIds = {
610099
}
},
[51122] = {
commodityId = 51122,
commodityName = "夜猫子眼镜",
coinType = 200008,
price = 5,
itemIds = {
610100
}
},
[51123] = {
commodityId = 51123,
commodityName = "彩虹眼镜",
coinType = 200008,
price = 5,
itemIds = {
610101
}
},
[51124] = {
commodityId = 51124,
commodityName = "彩虹眼镜",
coinType = 200008,
price = 5,
itemIds = {
610102
}
},
[51125] = {
commodityId = 51125,
commodityName = "小清新面膜",
beginTime = {
seconds = 1713542400
},
endTime = {
seconds = 1719503999
},
shopTag = v3,
shopSort = 1,
jumpId = 177,
jumpText = "暗夜冰羽",
minVersion = v10,
itemIds = {
610103
},
bOpenSuit = true,
suitId = 50056
},
[51126] = {
commodityId = 51126,
commodityName = "小清新面膜",
coinType = 200008,
price = 5,
itemIds = {
610104
}
},
[51127] = {
commodityId = 51127,
commodityName = "小清新面膜",
coinType = 200008,
price = 5,
itemIds = {
610105
}
},
[51128] = {
commodityId = 51128,
commodityName = "心动飞驰镜",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1713455999
},
shopTag = v3,
jumpId = 158,
jumpText = "星愿之旅",
itemIds = {
610108
},
bOpenSuit = true,
suitId = 50059
},
[51129] = {
commodityId = 51129,
commodityName = "猪猪贴",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v10,
itemIds = {
610109
},
suitId = 50060
},
[51130] = {
commodityId = 51130,
commodityName = "甜甜圈眼镜",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v3,
shopSort = 1,
jumpId = 9,
jumpText = "潮音通行证",
minVersion = v10,
itemIds = {
610110
},
bOpenSuit = true,
suitId = 50061
},
[51131] = {
commodityId = 51131,
commodityName = "甜甜圈眼镜",
coinType = 200008,
price = 5,
minVersion = v10,
itemIds = {
610111
}
},
[51132] = {
commodityId = 51132,
commodityName = "甜甜圈眼镜",
coinType = 200008,
price = 5,
minVersion = v10,
itemIds = {
610112
}
},
[51133] = {
commodityId = 51133,
commodityName = "白眉毛",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v10,
itemIds = {
610113
},
suitId = 50062
},
[51134] = {
commodityId = 51134,
commodityName = "白八字胡",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v3,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = v10,
itemIds = {
610114
},
bOpenSuit = true,
suitId = 50063
},
[51135] = {
commodityId = 51135,
commodityName = "外星人眼镜A",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v10,
itemIds = {
610115
},
suitId = 50064
},
[51136] = {
commodityId = 51136,
commodityName = "外星人眼镜B",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v10,
itemIds = {
610116
},
suitId = 50065
},
[51137] = {
commodityId = 51137,
commodityName = "外星人眼镜C",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v10,
itemIds = {
610117
},
suitId = 50066
},
[51138] = {
commodityId = 51138,
commodityName = "亲嘴鱼眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v10,
itemIds = {
610118
},
suitId = 50067
},
[51139] = {
commodityId = 51139,
commodityName = "口哨",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v10,
itemIds = {
610119
},
suitId = 50068
},
[51140] = {
commodityId = 51140,
commodityName = "白胡子",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v10,
itemIds = {
610120
},
suitId = 50069
},
[51141] = {
commodityId = 51141,
commodityName = "干杯眼镜",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v3,
shopSort = 1,
jumpId = 180,
jumpText = "赛季祈愿",
minVersion = v10,
itemIds = {
610121
},
bOpenSuit = true,
suitId = 50070
},
[51142] = {
commodityId = 51142,
commodityName = "干杯眼镜",
coinType = 200008,
price = 5,
minVersion = v10,
itemIds = {
610122
}
},
[51143] = {
commodityId = 51143,
commodityName = "干杯眼镜",
coinType = 200008,
price = 5,
minVersion = v10,
itemIds = {
610123
}
},
[51144] = {
commodityId = 51144,
commodityName = "青蛙眼罩",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v10,
itemIds = {
610124
},
suitId = 50071
},
[51145] = {
commodityId = 51145,
commodityName = "三角墨镜",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v3,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = v10,
itemIds = {
610125
},
suitId = 50072
},
[51146] = {
commodityId = 51146,
commodityName = "麦克风",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = v10,
itemIds = {
610126
},
suitId = 50073
},
[51147] = {
commodityId = 51147,
commodityName = "音符单片镜",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v3,
shopSort = 1,
jumpId = 180,
jumpText = "赛季祈愿",
minVersion = v10,
itemIds = {
610127
},
giftCoinType = 1,
bOpenSuit = true,
suitId = 50074
},
[51148] = {
commodityId = 51148,
commodityName = "音符单片镜",
coinType = 200008,
price = 10,
minVersion = v10,
itemIds = {
610128
}
},
[51149] = {
commodityId = 51149,
commodityName = "音符单片镜",
coinType = 200008,
price = 10,
minVersion = v10,
itemIds = {
610129
}
},
[51150] = {
commodityId = 51150,
commodityName = "爱心沙滩镜",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopTag = v3,
shopSort = 1,
jumpId = 188,
jumpText = "盛装派对",
minVersion = v10,
itemIds = {
610130
},
bOpenSuit = true,
suitId = 50075
},
[51151] = {
commodityId = 51151,
commodityName = "舞会假面",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopTag = v3,
shopSort = 1,
jumpId = 188,
jumpText = "盛装派对",
minVersion = v10,
itemIds = {
610131
},
bOpenSuit = true,
suitId = 50076
},
[51152] = {
commodityId = 51152,
commodityName = "舞会假面",
coinType = 200008,
price = 5,
minVersion = v10,
itemIds = {
610132
}
},
[51153] = {
commodityId = 51153,
commodityName = "舞会假面",
coinType = 200008,
price = 5,
minVersion = v10,
itemIds = {
610133
}
},
[51154] = {
commodityId = 51154,
commodityName = "爱心炫彩镜",
coinType = 200008,
price = 5,
itemIds = {
610134
}
},
[51155] = {
commodityId = 51155,
commodityName = "爱心炫彩镜",
coinType = 200008,
price = 5,
itemIds = {
610135
}
},
[51156] = {
commodityId = 51156,
commodityName = "电子目镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610136
},
suitId = 50077
},
[51157] = {
commodityId = 51157,
commodityName = "柠檬眼镜",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v3,
jumpId = 10602,
jumpText = "小甜豆",
minVersion = "1.3.18.109",
itemIds = {
610137
},
bOpenSuit = true,
suitId = 50078
},
[51158] = {
commodityId = 51158,
commodityName = "咕咕镜",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v3,
jumpId = 10603,
jumpText = "小甜豆",
minVersion = "1.3.18.109",
itemIds = {
610138
},
bOpenSuit = true,
suitId = 50079
},
[51159] = {
commodityId = 51159,
commodityName = "时尚魔头镜",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v3,
shopSort = 1,
jumpId = 175,
jumpText = "永恒之誓",
minVersion = "1.2.100.46",
itemIds = {
610139
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 50080
},
[51160] = {
commodityId = 51160,
commodityName = "时尚魔头镜",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
610140
}
},
[51161] = {
commodityId = 51161,
commodityName = "时尚魔头镜",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
610141
}
},
[51162] = {
commodityId = 51162,
commodityName = "神威墨镜",
beginTime = {
seconds = 1716566400
},
endTime = {
seconds = 1798732799
},
shopTag = v3,
jumpId = 176,
jumpText = "星梦使者",
itemIds = {
610142
},
bOpenSuit = true,
suitId = 50081
},
[51163] = {
commodityId = 51163,
commodityName = "神威墨镜",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1716566400
},
itemIds = {
610143
}
},
[51164] = {
commodityId = 51164,
commodityName = "神威墨镜",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1716566400
},
itemIds = {
610144
}
},
[51165] = {
commodityId = 51165,
commodityName = "可妮眼罩",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = "1.3.6.1",
itemIds = {
610145
},
suitId = 50082
},
[51166] = {
commodityId = 51166,
commodityName = "西瓜眼镜",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v3,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.6.1",
itemIds = {
610146
},
bOpenSuit = true,
suitId = 50083
},
[51167] = {
commodityId = 51167,
commodityName = "西瓜眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.6.1",
itemIds = {
610147
}
},
[51168] = {
commodityId = 51168,
commodityName = "西瓜眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.6.1",
itemIds = {
610148
}
},
[51169] = {
commodityId = 51169,
commodityName = "清甜视线",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopTag = v3,
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
610149
},
bOpenSuit = true,
suitId = 50084
},
[51170] = {
commodityId = 51170,
commodityName = "清甜视线",
coinType = 200008,
price = 5,
minVersion = "1.3.7.94",
itemIds = {
610150
}
},
[51171] = {
commodityId = 51171,
commodityName = "清甜视线",
coinType = 200008,
price = 5,
minVersion = "1.3.7.94",
itemIds = {
610151
}
},
[51172] = {
commodityId = 51172,
commodityName = "开屏眼罩",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v3,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.6.1",
itemIds = {
610152
},
bOpenSuit = true,
suitId = 50085
},
[51173] = {
commodityId = 51173,
commodityName = "开屏眼罩",
coinType = 200008,
price = 5,
minVersion = "1.3.6.1",
itemIds = {
610153
}
},
[51174] = {
commodityId = 51174,
commodityName = "开屏眼罩",
coinType = 200008,
price = 5,
minVersion = "1.3.6.1",
itemIds = {
610154
}
},
[51175] = {
commodityId = 51175,
commodityName = "四色眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = "1.3.6.1",
itemIds = {
610155
},
suitId = 50086
},
[51176] = {
commodityId = 51176,
commodityName = "四色眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.6.1",
itemIds = {
610156
}
},
[51177] = {
commodityId = 51177,
commodityName = "四色眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.6.1",
itemIds = {
610157
}
},
[51178] = {
commodityId = 51178,
commodityName = "胶卷眼镜",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v3,
shopSort = 1,
jumpId = 706,
jumpText = "赛季祈愿",
minVersion = "1.3.12.1",
itemIds = {
610158
},
bOpenSuit = true,
suitId = 50087
},
[51179] = {
commodityId = 51179,
commodityName = "胶卷眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.12.1",
itemIds = {
610159
}
},
[51180] = {
commodityId = 51180,
commodityName = "胶卷眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.12.1",
itemIds = {
610160
}
},
[51181] = {
commodityId = 51181,
commodityName = "潜水镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = "1.3.6.1",
itemIds = {
610161
},
suitId = 50088
},
[51182] = {
commodityId = 51182,
commodityName = "海洋之眼",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v3,
shopSort = 1,
jumpId = 11,
jumpText = "赛季祈愿",
minVersion = "1.3.6.1",
itemIds = {
610162
},
bOpenSuit = true,
suitId = 50089
},
[51183] = {
commodityId = 51183,
commodityName = "海洋之眼",
coinType = 200008,
price = 10,
minVersion = "1.3.6.1",
itemIds = {
610163
}
},
[51184] = {
commodityId = 51184,
commodityName = "海洋之眼",
coinType = 200008,
price = 10,
minVersion = "1.3.6.1",
itemIds = {
610164
}
},
[51185] = {
commodityId = 51185,
commodityName = "冰泽面具",
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730390399
},
shopTag = v3,
shopSort = 1,
jumpId = 610,
jumpText = "夏夜绮梦",
minVersion = "1.3.12.118",
itemIds = {
610165
},
bOpenSuit = true,
suitId = 50090
},
[51186] = {
commodityId = 51186,
commodityName = "冰泽面具",
coinType = 200008,
price = 10,
minVersion = "1.3.12.118",
itemIds = {
610166
}
},
[51187] = {
commodityId = 51187,
commodityName = "冰泽面具",
coinType = 200008,
price = 10,
minVersion = "1.3.12.118",
itemIds = {
610167
}
},
[51188] = {
commodityId = 51188,
commodityName = "贝壳眼镜",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v3,
shopSort = 1,
jumpId = 11,
jumpText = "赛季祈愿",
minVersion = "1.3.6.1",
itemIds = {
610168
},
bOpenSuit = true,
suitId = 50091
},
[51189] = {
commodityId = 51189,
commodityName = "贝壳眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.6.1",
itemIds = {
610169
}
},
[51190] = {
commodityId = 51190,
commodityName = "贝壳眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.6.1",
itemIds = {
610170
}
},
[51191] = {
commodityId = 51191,
commodityName = "搞怪眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = "1.3.6.1",
itemIds = {
610171
},
suitId = 50092
},
[51192] = {
commodityId = 51192,
commodityName = "樱桃派",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v3,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.7.31",
itemIds = {
610172
},
bOpenSuit = true,
suitId = 50093
},
[51193] = {
commodityId = 51193,
commodityName = "光之面甲",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v3,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.33",
itemIds = {
610173
},
canGift = true,
addIntimacy = 300,
giftCoinType = 213,
giftPrice = 1,
bOpenSuit = true,
suitId = 50094
},
[51194] = {
commodityId = 51194,
commodityName = "光之面甲",
coinType = 200008,
price = 10,
beginTime = v0,
minVersion = "1.3.68.33",
itemIds = {
610174
}
},
[51195] = {
commodityId = 51195,
commodityName = "光之面甲",
coinType = 200008,
price = 10,
beginTime = v0,
minVersion = "1.3.68.33",
itemIds = {
610175
}
},
[51196] = {
commodityId = 51196,
commodityName = "我不说话",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610176
},
suitId = 50095
},
[51197] = {
commodityId = 51197,
commodityName = "蝶翼单照",
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730390399
},
shopTag = v3,
shopSort = 1,
jumpId = 610,
jumpText = "夏夜绮梦",
minVersion = "1.3.12.118",
itemIds = {
610177
},
bOpenSuit = true,
suitId = 50096
},
[51198] = {
commodityId = 51198,
commodityName = "蝶翼单照",
coinType = 200008,
price = 5,
minVersion = "1.3.12.118",
itemIds = {
610178
}
},
[51199] = {
commodityId = 51199,
commodityName = "蝶翼单照",
coinType = 200008,
price = 5,
minVersion = "1.3.12.118",
itemIds = {
610179
}
},
[51200] = {
commodityId = 51200,
commodityName = "萌萌护眼",
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1738425599
},
shopTag = v3,
jumpId = 1074,
jumpText = "三丽鸥家族",
minVersion = "1.3.7.97",
itemIds = {
610180
},
bOpenSuit = true,
suitId = 50097
},
[51201] = {
commodityId = 51201,
commodityName = "羽毛单片眼镜",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v3,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.18.37",
itemIds = {
610181
},
bOpenSuit = true,
suitId = 50098
},
[51202] = {
commodityId = 51202,
commodityName = "羽毛单片眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.12.1",
itemIds = {
610182
}
},
[51203] = {
commodityId = 51203,
commodityName = "羽毛单片眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.12.1",
itemIds = {
610183
}
},
[51204] = {
commodityId = 51204,
commodityName = "爆米花眼镜",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v3,
shopSort = 1,
jumpId = 9,
jumpText = "乐园通行证",
minVersion = "1.3.12.1",
itemIds = {
610184
},
bOpenSuit = true,
suitId = 50099
},
[51205] = {
commodityId = 51205,
commodityName = "爆米花眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.12.1",
itemIds = {
610185
}
},
[51206] = {
commodityId = 51206,
commodityName = "爆米花眼镜",
coinType = 200008,
price = 5,
minVersion = "1.3.12.1",
itemIds = {
610186
}
},
[51207] = {
commodityId = 51207,
commodityName = "3D眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = "1.3.12.1",
itemIds = {
610187
},
suitId = 50100
},
[51208] = {
commodityId = 51208,
commodityName = "白色VR眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = "1.3.12.1",
itemIds = {
610188
},
suitId = 50101
},
[51209] = {
commodityId = 51209,
commodityName = "黑色VR眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = "1.3.12.1",
itemIds = {
610189
},
suitId = 50102
},
[51210] = {
commodityId = 51210,
commodityName = "炫彩VR眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
minVersion = "1.3.12.1",
itemIds = {
610190
},
suitId = 50103
},
[51211] = {
commodityId = 51211,
commodityName = "煤球面具",
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1740067199
},
shopTag = v3,
jumpId = 557,
jumpText = "遗落的珍宝",
minVersion = "1.3.68.52",
itemIds = {
610191
},
bOpenSuit = true,
suitId = 50104
},
[51212] = {
commodityId = 51212,
commodityName = "古云流镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610192
},
suitId = 50105
},
[51213] = {
commodityId = 51213,
commodityName = "古云流镜",
coinType = 200008,
price = 5,
itemIds = {
610193
}
},
[51214] = {
commodityId = 51214,
commodityName = "古云流镜",
coinType = 200008,
price = 5,
itemIds = {
610194
}
},
[51215] = {
commodityId = 51215,
commodityName = "哞哞眼罩",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v3,
shopSort = 1,
jumpId = 613,
jumpText = "百变小新",
itemIds = {
610195
},
bOpenSuit = true,
suitId = 50106
},
[51216] = {
commodityId = 51216,
commodityName = "哞哞眼罩",
coinType = 200008,
price = 5,
itemIds = {
610196
}
},
[51217] = {
commodityId = 51217,
commodityName = "哞哞眼罩",
coinType = 200008,
price = 5,
itemIds = {
610197
}
},
[51218] = {
commodityId = 51218,
commodityName = "煎蛋眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610198
},
suitId = 50107
},
[51219] = {
commodityId = 51219,
commodityName = "煎蛋眼镜",
coinType = 200008,
price = 5,
itemIds = {
610199
}
},
[51220] = {
commodityId = 51220,
commodityName = "煎蛋眼镜",
coinType = 200008,
price = 5,
itemIds = {
610200
}
},
[51221] = {
commodityId = 51221,
commodityName = "火力全开",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v3,
shopSort = 1,
jumpId = 809,
jumpText = "凤求凰祈愿",
minVersion = "1.3.12.90",
itemIds = {
610201
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 50108
},
[51222] = {
commodityId = 51222,
commodityName = "火力全开",
coinType = 200008,
price = 5,
minVersion = "1.3.12.90",
itemIds = {
610202
}
},
[51223] = {
commodityId = 51223,
commodityName = "火力全开",
coinType = 200008,
price = 5,
minVersion = "1.3.12.90",
itemIds = {
610203
}
},
[51224] = {
commodityId = 51224,
commodityName = "凤羽之舞",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v3,
shopSort = 1,
jumpId = 809,
jumpText = "凤求凰祈愿",
minVersion = "1.3.12.90",
itemIds = {
610204
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 50109
},
[51225] = {
commodityId = 51225,
commodityName = "凤羽之舞",
coinType = 200008,
price = 10,
minVersion = "1.3.12.90",
itemIds = {
610205
}
},
[51226] = {
commodityId = 51226,
commodityName = "凤羽之舞",
coinType = 200008,
price = 10,
minVersion = "1.3.12.90",
itemIds = {
610206
}
},
[51227] = {
commodityId = 51227,
commodityName = "爆燃勇士",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v3,
shopSort = 1,
jumpId = 503,
jumpText = "冰雪圆舞曲",
minVersion = "1.3.37.68",
itemIds = {
610207
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 50110
},
[51228] = {
commodityId = 51228,
commodityName = "爆燃勇士",
coinType = 200008,
price = 5,
itemIds = {
610208
}
},
[51229] = {
commodityId = 51229,
commodityName = "爆燃勇士",
coinType = 200008,
price = 5,
itemIds = {
610209
}
},
[51230] = {
commodityId = 51230,
commodityName = "猫猫面罩",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610210
},
suitId = 50111
},
[51231] = {
commodityId = 51231,
commodityName = "猫猫面罩",
coinType = 200008,
price = 5,
itemIds = {
610211
}
},
[51232] = {
commodityId = 51232,
commodityName = "猫猫面罩",
coinType = 200008,
price = 5,
itemIds = {
610212
}
},
[51233] = {
commodityId = 51233,
commodityName = "爱心创可贴",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610213
},
suitId = 50112
},
[51234] = {
commodityId = 51234,
commodityName = "大眼萌萌",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v3,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.78.58",
itemIds = {
610214
},
bOpenSuit = true,
suitId = 50113
},
[51235] = {
commodityId = 51235,
commodityName = "热力追踪",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610215
},
suitId = 50114
},
[51236] = {
commodityId = 51236,
commodityName = "还我小北鼻",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610216
},
suitId = 50115
},
[51237] = {
commodityId = 51237,
commodityName = "自然之灵",
beginTime = {
seconds = 1699113600
},
endTime = {
seconds = 1699718399
},
shopTag = v3,
minVersion = "1.3.26.61",
itemIds = {
610217
},
suitId = 50116
},
[51238] = {
commodityId = 51238,
commodityName = "自然之灵",
coinType = 200008,
price = 5,
itemIds = {
610218
}
},
[51239] = {
commodityId = 51239,
commodityName = "自然之灵",
coinType = 200008,
price = 5,
itemIds = {
610219
}
},
[51240] = {
commodityId = 51240,
commodityName = "郁金流年",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v3,
shopSort = 1,
jumpId = 619,
jumpText = "赛季祈愿",
minVersion = "1.3.18.1",
itemIds = {
610220
},
bOpenSuit = true,
suitId = 50117
},
[51241] = {
commodityId = 51241,
commodityName = "郁金流年",
coinType = 200008,
price = 5,
itemIds = {
610221
}
},
[51242] = {
commodityId = 51242,
commodityName = "郁金流年",
coinType = 200008,
price = 5,
itemIds = {
610222
}
},
[51243] = {
commodityId = 51243,
commodityName = "鸭趣横生",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610223
},
suitId = 50118
},
[51244] = {
commodityId = 51244,
commodityName = "月夜潜行者",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610224
},
suitId = 50119
},
[51245] = {
commodityId = 51245,
commodityName = "碧意轻扬",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v3,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.18.1",
itemIds = {
610225
},
bOpenSuit = true,
suitId = 50120
},
[51246] = {
commodityId = 51246,
commodityName = "碧意轻扬",
coinType = 200008,
price = 5,
itemIds = {
610226
}
},
[51247] = {
commodityId = 51247,
commodityName = "碧意轻扬",
coinType = 200008,
price = 5,
itemIds = {
610227
}
},
[51248] = {
commodityId = 51248,
commodityName = "轻语鸢梦",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v3,
shopSort = 1,
jumpId = 619,
jumpText = "赛季祈愿",
minVersion = "1.3.18.1",
itemIds = {
610228
},
bOpenSuit = true,
suitId = 50121
},
[51249] = {
commodityId = 51249,
commodityName = "轻语鸢梦",
coinType = 200008,
price = 10,
itemIds = {
610229
}
},
[51250] = {
commodityId = 51250,
commodityName = "轻语鸢梦",
coinType = 200008,
price = 10,
itemIds = {
610230
}
},
[51251] = {
commodityId = 51251,
commodityName = "游侠树叶",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610231
},
suitId = 50122
},
[51252] = {
commodityId = 51252,
commodityName = "玉叶琼枝",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
shopTag = v3,
shopSort = 1,
jumpId = 363,
jumpText = "桂月清平",
minVersion = "1.3.18.37",
itemIds = {
610232
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 50123
},
[51253] = {
commodityId = 51253,
commodityName = "玉叶琼枝",
coinType = 200008,
price = 5,
itemIds = {
610233
}
},
[51254] = {
commodityId = 51254,
commodityName = "玉叶琼枝",
coinType = 200008,
price = 5,
itemIds = {
610234
}
},
[51255] = {
commodityId = 51255,
commodityName = "诚实之枝",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610235
},
suitId = 50124
},
[51256] = {
commodityId = 51256,
commodityName = "飞翼视界",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopTag = v3,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
610236
},
bOpenSuit = true,
suitId = 50125
},
[51257] = {
commodityId = 51257,
commodityName = "飞翼视界",
coinType = 200008,
price = 5,
itemIds = {
610237
}
},
[51258] = {
commodityId = 51258,
commodityName = "飞翼视界",
coinType = 200008,
price = 5,
itemIds = {
610238
}
},
[51259] = {
commodityId = 51259,
commodityName = "酷彩视界",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610239
},
suitId = 50126
},
[51260] = {
commodityId = 51260,
commodityName = "毛线球眼镜",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v3,
jumpId = 10600,
jumpText = "小甜豆",
minVersion = "1.3.18.109",
itemIds = {
610240
},
bOpenSuit = true,
suitId = 50127
},
[51261] = {
commodityId = 51261,
commodityName = "毛线球眼镜",
coinType = 200008,
price = 5,
itemIds = {
610241
}
},
[51262] = {
commodityId = 51262,
commodityName = "毛线球眼镜",
coinType = 200008,
price = 5,
itemIds = {
610242
}
},
[51263] = {
commodityId = 51263,
commodityName = "吾皇眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610243
},
suitId = 50128
},
[51264] = {
commodityId = 51264,
commodityName = "云羽轻纱",
beginTime = {
seconds = 1750521600
},
endTime = {
seconds = 1752940799
},
shopTag = v3,
jumpId = 1100,
jumpText = "月华鹤影",
itemIds = {
610244
},
bOpenSuit = true,
suitId = 50129
},
[51265] = {
commodityId = 51265,
commodityName = "外星探视",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v3,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.26.1",
itemIds = {
610245
},
bOpenSuit = true,
suitId = 50130
},
[51266] = {
commodityId = 51266,
commodityName = "外星探视",
coinType = 200008,
price = 5,
itemIds = {
610246
}
},
[51267] = {
commodityId = 51267,
commodityName = "外星探视",
coinType = 200008,
price = 5,
itemIds = {
610247
}
},
[51268] = {
commodityId = 51268,
commodityName = "野性之息",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610248
},
suitId = 50131
},
[51269] = {
commodityId = 51269,
commodityName = "粉豹魅影",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610249
},
suitId = 50132
},
[51270] = {
commodityId = 51270,
commodityName = "萝萝鼻",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610250
},
suitId = 50133
},
[51271] = {
commodityId = 51271,
commodityName = "飘雪之镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610251
},
suitId = 50134
},
[51272] = {
commodityId = 51272,
commodityName = "蜜糖时刻",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610252
},
suitId = 50135
},
[51273] = {
commodityId = 51273,
commodityName = "暗影凝视",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610253
},
suitId = 50136
},
[51274] = {
commodityId = 51274,
commodityName = "星辰之眼",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v3,
shopSort = 1,
jumpId = 623,
jumpText = "赛季祈愿",
minVersion = "1.3.26.1",
itemIds = {
610254
},
bOpenSuit = true,
suitId = 50137
},
[51275] = {
commodityId = 51275,
commodityName = "星辰之眼",
coinType = 200008,
price = 10,
itemIds = {
610255
}
},
[51276] = {
commodityId = 51276,
commodityName = "星辰之眼",
coinType = 200008,
price = 10,
itemIds = {
610256
}
},
[51277] = {
commodityId = 51277,
commodityName = "古怪南瓜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610257
},
suitId = 50138
},
[51278] = {
commodityId = 51278,
commodityName = "香脆吐司",
beginTime = {
seconds = 1730217600
},
endTime = {
seconds = 1731427199
},
shopTag = v3,
jumpId = 380,
jumpText = "星辰日晷",
minVersion = "1.3.26.29",
itemIds = {
610258
},
bOpenSuit = true,
suitId = 50139
},
[51279] = {
commodityId = 51279,
commodityName = "香脆吐司",
coinType = 200008,
price = 5,
itemIds = {
610259
}
},
[51280] = {
commodityId = 51280,
commodityName = "香脆吐司",
coinType = 200008,
price = 5,
itemIds = {
610260
}
},
[51281] = {
commodityId = 51281,
commodityName = "隐夜之面",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610261
},
suitId = 50140
},
[51282] = {
commodityId = 51282,
commodityName = "隐夜之面",
coinType = 200008,
price = 5,
itemIds = {
610262
}
},
[51283] = {
commodityId = 51283,
commodityName = "隐夜之面",
coinType = 200008,
price = 5,
itemIds = {
610263
}
},
[51284] = {
commodityId = 51284,
commodityName = "赤焰之舞",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v3,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
minVersion = "1.3.26.71",
itemIds = {
610264
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 50141
},
[51285] = {
commodityId = 51285,
commodityName = "赤焰之舞",
coinType = 200008,
price = 10,
minVersion = "1.3.26.71",
itemIds = {
610265
}
},
[51286] = {
commodityId = 51286,
commodityName = "赤焰之舞",
coinType = 200008,
price = 10,
minVersion = "1.3.26.71",
itemIds = {
610266
}
},
[51287] = {
commodityId = 51287,
commodityName = "大眼萌萌",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v3,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
minVersion = "1.3.26.71",
itemIds = {
610267
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 50142
},
[51288] = {
commodityId = 51288,
commodityName = "大眼萌萌",
coinType = 200008,
price = 5,
minVersion = "1.3.26.71",
itemIds = {
610268
}
},
[51289] = {
commodityId = 51289,
commodityName = "大眼萌萌",
coinType = 200008,
price = 5,
minVersion = "1.3.26.71",
itemIds = {
610269
}
},
[51290] = {
commodityId = 51290,
commodityName = "熊猫眼",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v3,
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.26.61",
itemIds = {
610106
},
bOpenSuit = true,
suitId = 50057
},
[51291] = {
commodityId = 51291,
commodityName = "翅膀眼镜A",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610270
},
suitId = 50143
},
[51292] = {
commodityId = 51292,
commodityName = "翅膀眼镜B",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610271
},
suitId = 50144
},
[51293] = {
commodityId = 51293,
commodityName = "蟹钳眼镜",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v3,
shopSort = 1,
jumpId = 630,
jumpText = "赛季祈愿",
minVersion = "1.3.37.1",
itemIds = {
610272
},
bOpenSuit = true,
suitId = 50145
},
[51294] = {
commodityId = 51294,
commodityName = "蟹钳眼镜",
coinType = 200008,
price = 5,
itemIds = {
610273
}
},
[51295] = {
commodityId = 51295,
commodityName = "蟹钳眼镜",
coinType = 200008,
price = 5,
itemIds = {
610274
}
},
[51296] = {
commodityId = 51296,
commodityName = "炸鸡眼镜",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v3,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.37.1",
itemIds = {
610275
},
bOpenSuit = true,
suitId = 50146
},
[51297] = {
commodityId = 51297,
commodityName = "炸鸡眼镜",
coinType = 200008,
price = 5,
itemIds = {
610276
}
},
[51298] = {
commodityId = 51298,
commodityName = "炸鸡眼镜",
coinType = 200008,
price = 5,
itemIds = {
610277
}
},
[51299] = {
commodityId = 51299,
commodityName = "香肠派对",
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738425599
},
shopTag = v3,
shopSort = 1,
jumpId = 628,
jumpText = "星之恋空",
itemIds = {
610278
},
bOpenSuit = true,
suitId = 50147
},
[51300] = {
commodityId = 51300,
commodityName = "香肠派对",
coinType = 200008,
price = 5,
itemIds = {
610279
}
},
[51301] = {
commodityId = 51301,
commodityName = "香肠派对",
coinType = 200008,
price = 5,
itemIds = {
610280
}
},
[51302] = {
commodityId = 51302,
commodityName = "宝宝眼镜",
beginTime = {
seconds = 1750435200
},
endTime = {
seconds = 1752249599
},
shopTag = v3,
jumpId = 1098,
jumpText = "天线宝宝",
minVersion = "1.3.88.155",
itemIds = {
610281
},
bOpenSuit = true,
suitId = 50148
},
[51303] = {
commodityId = 51303,
commodityName = "宝宝眼镜",
coinType = 200008,
price = 5,
itemIds = {
610282
}
},
[51304] = {
commodityId = 51304,
commodityName = "宝宝眼镜",
coinType = 200008,
price = 5,
itemIds = {
610283
}
},
[51305] = {
commodityId = 51305,
commodityName = "迷雾之纱",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v3,
shopSort = 1,
jumpId = 503,
jumpText = "冰雪圆舞曲",
minVersion = "1.3.37.68",
itemIds = {
610284
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 50149
},
[51306] = {
commodityId = 51306,
commodityName = "迷雾之纱",
coinType = 200008,
price = 10,
itemIds = {
610285
}
},
[51307] = {
commodityId = 51307,
commodityName = "迷雾之纱",
coinType = 200008,
price = 10,
itemIds = {
610286
}
},
[51308] = {
commodityId = 51308,
commodityName = "兔牙永存",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610287
},
suitId = 50150
},
[51309] = {
commodityId = 51309,
commodityName = "红唇亲亲",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610288
},
suitId = 50151
},
[51310] = {
commodityId = 51310,
commodityName = "麻萤之眼",
beginTime = {
seconds = 1732680000
},
endTime = {
seconds = 1741881599
},
shopTag = v3,
jumpId = 456,
jumpText = "猫猫搜城记",
minVersion = "1.3.37.1",
itemIds = {
610289
},
bOpenSuit = true,
suitId = 50152
},
[51311] = {
commodityId = 51311,
commodityName = "小黑的凝视",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610290
},
suitId = 50153
},
[51312] = {
commodityId = 51312,
commodityName = "几何视界",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610291
},
suitId = 50154
},
[51313] = {
commodityId = 51313,
commodityName = "几何视界",
coinType = 200008,
price = 5,
itemIds = {
610292
}
},
[51314] = {
commodityId = 51314,
commodityName = "几何视界",
coinType = 200008,
price = 5,
itemIds = {
610293
}
},
[51315] = {
commodityId = 51315,
commodityName = "圣诞树眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610294
},
suitId = 50155
},
[51316] = {
commodityId = 51316,
commodityName = "圣诞树眼镜",
coinType = 200008,
price = 5,
itemIds = {
610295
}
},
[51317] = {
commodityId = 51317,
commodityName = "圣诞树眼镜",
coinType = 200008,
price = 5,
itemIds = {
610296
}
},
[51318] = {
commodityId = 51318,
commodityName = "双生之翼",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v3,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.33",
itemIds = {
610297
},
canGift = true,
addIntimacy = 300,
giftCoinType = 213,
giftPrice = 1,
bOpenSuit = true,
suitId = 50156
},
[51319] = {
commodityId = 51319,
commodityName = "双生之翼",
coinType = 200008,
price = 10,
beginTime = v0,
minVersion = "1.3.68.33",
itemIds = {
610298
}
},
[51320] = {
commodityId = 51320,
commodityName = "双生之翼",
coinType = 200008,
price = 10,
beginTime = v0,
minVersion = "1.3.68.33",
itemIds = {
610299
}
},
[51321] = {
commodityId = 51321,
commodityName = "琳琅点翠",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610300
},
suitId = 50157
},
[51322] = {
commodityId = 51322,
commodityName = "琳琅点翠",
coinType = 200008,
price = 5,
itemIds = {
610301
}
},
[51323] = {
commodityId = 51323,
commodityName = "琳琅点翠",
coinType = 200008,
price = 5,
itemIds = {
610302
}
},
[51324] = {
commodityId = 51324,
commodityName = "多彩纽扣镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610303
},
suitId = 50158
},
[51325] = {
commodityId = 51325,
commodityName = "多彩纽扣镜",
coinType = 200008,
price = 5,
itemIds = {
610304
}
},
[51326] = {
commodityId = 51326,
commodityName = "多彩纽扣镜",
coinType = 200008,
price = 5,
itemIds = {
610305
}
},
[51327] = {
commodityId = 51327,
commodityName = "风车视界",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610306
},
suitId = 50159
},
[51328] = {
commodityId = 51328,
commodityName = "风车视界",
coinType = 200008,
price = 5,
itemIds = {
610307
}
},
[51329] = {
commodityId = 51329,
commodityName = "风车视界",
coinType = 200008,
price = 5,
itemIds = {
610308
}
},
[51330] = {
commodityId = 51330,
commodityName = "鸿运祥云",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v3,
shopSort = 1,
jumpId = 636,
jumpText = "赛季祈愿",
minVersion = "1.3.68.1",
itemIds = {
610309
},
bOpenSuit = true,
suitId = 50160
},
[51331] = {
commodityId = 51331,
commodityName = "鸿运祥云",
coinType = 200008,
price = 10,
itemIds = {
610310
}
},
[51332] = {
commodityId = 51332,
commodityName = "鸿运祥云",
coinType = 200008,
price = 10,
itemIds = {
610311
}
},
[51333] = {
commodityId = 51333,
commodityName = "热狗梦境",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610312
},
suitId = 50161
},
[51334] = {
commodityId = 51334,
commodityName = "章鱼护目镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610313
},
suitId = 50162
},
[51335] = {
commodityId = 51335,
commodityName = "大耳狗眼罩",
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1738425599
},
shopTag = v3,
jumpId = 1073,
jumpText = "三丽鸥家族",
minVersion = "1.3.68.52",
itemIds = {
610314
},
suitId = 50163
},
[51336] = {
commodityId = 51336,
commodityName = "心动时刻",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610315
},
suitId = 50164
},
[51337] = {
commodityId = 51337,
commodityName = "醒狮舞面",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610316
},
suitId = 50165
},
[51338] = {
commodityId = 51338,
commodityName = "醒狮舞面",
coinType = 200008,
price = 5,
itemIds = {
610317
}
},
[51339] = {
commodityId = 51339,
commodityName = "醒狮舞面",
coinType = 200008,
price = 5,
itemIds = {
610318
}
},
[51340] = {
commodityId = 51340,
commodityName = "狐影倾城",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v3,
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
610319
},
bOpenSuit = true,
suitId = 50166
},
[51341] = {
commodityId = 51341,
commodityName = "狐影倾城",
coinType = 200008,
price = 10,
minVersion = "1.3.68.100",
itemIds = {
610320
}
},
[51342] = {
commodityId = 51342,
commodityName = "狐影倾城",
coinType = 200008,
price = 10,
minVersion = "1.3.68.100",
itemIds = {
610321
}
},
[51343] = {
commodityId = 51343,
commodityName = "梦幻泡影",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1746719999
},
shopTag = v3,
shopSort = 1,
jumpId = 638,
jumpText = "岚汀之约",
itemIds = {
610322
},
bOpenSuit = true,
suitId = 50167
},
[51344] = {
commodityId = 51344,
commodityName = "梦幻泡影",
coinType = 200008,
price = 10,
itemIds = {
610323
}
},
[51345] = {
commodityId = 51345,
commodityName = "梦幻泡影",
coinType = 200008,
price = 10,
itemIds = {
610324
}
},
[51346] = {
commodityId = 51346,
commodityName = "编织睛彩",
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
shopTag = v3,
jumpId = 641,
jumpText = "玩偶之家",
minVersion = "1.3.78.72",
itemIds = {
610325
},
bOpenSuit = true,
suitId = 50168
},
[51347] = {
commodityId = 51347,
commodityName = "编织睛彩",
coinType = 200008,
price = 10,
itemIds = {
610326
}
},
[51348] = {
commodityId = 51348,
commodityName = "编织睛彩",
coinType = 200008,
price = 10,
itemIds = {
610327
}
},
[51349] = {
commodityId = 51349,
commodityName = "爱心十字绷",
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
shopTag = v3,
jumpId = 641,
jumpText = "玩偶之家",
minVersion = "1.3.78.72",
itemIds = {
610328
},
bOpenSuit = true,
suitId = 50169
},
[51350] = {
commodityId = 51350,
commodityName = "爱心十字绷",
coinType = 200008,
price = 5,
itemIds = {
610329
}
},
[51351] = {
commodityId = 51351,
commodityName = "爱心十字绷",
coinType = 200008,
price = 5,
itemIds = {
610330
}
},
[51352] = {
commodityId = 51352,
commodityName = "暗影黑喵",
beginTime = {
seconds = 1741838400
},
endTime = {
seconds = 1755187199
},
shopTag = v3,
jumpId = 583,
jumpText = "猫猫造物台",
minVersion = "1.3.78.1",
itemIds = {
610331
},
suitId = 50170
},
[51353] = {
commodityId = 51353,
commodityName = "星辰之纱",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v3,
jumpId = 640,
jumpText = "赛季祈愿",
minVersion = "1.3.78.1",
itemIds = {
610332
},
bOpenSuit = true,
suitId = 50171
},
[51354] = {
commodityId = 51354,
commodityName = "星辰之纱",
coinType = 200008,
price = 10,
itemIds = {
610333
}
},
[51355] = {
commodityId = 51355,
commodityName = "星辰之纱",
coinType = 200008,
price = 10,
itemIds = {
610334
}
},
[51356] = {
commodityId = 51356,
commodityName = "小羊咩咩",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v3,
jumpId = 640,
jumpText = "赛季祈愿",
minVersion = "1.3.78.1",
itemIds = {
610335
},
bOpenSuit = true,
suitId = 50172
},
[51357] = {
commodityId = 51357,
commodityName = "小羊咩咩",
coinType = 200008,
price = 5,
itemIds = {
610336
}
},
[51358] = {
commodityId = 51358,
commodityName = "小羊咩咩",
coinType = 200008,
price = 5,
itemIds = {
610337
}
},
[51359] = {
commodityId = 51359,
commodityName = "寻宝嗅嗅鼻",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610338
},
suitId = 50173
},
[51360] = {
commodityId = 51360,
commodityName = "寻宝嗅嗅鼻",
coinType = 200008,
price = 5,
itemIds = {
610339
}
},
[51361] = {
commodityId = 51361,
commodityName = "寻宝嗅嗅鼻",
coinType = 200008,
price = 5,
itemIds = {
610340
}
},
[51362] = {
commodityId = 51362,
commodityName = "奶猫小爪",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610341
},
suitId = 50174
},
[51363] = {
commodityId = 51363,
commodityName = "奶猫小爪",
coinType = 200008,
price = 5,
itemIds = {
610342
}
},
[51364] = {
commodityId = 51364,
commodityName = "奶猫小爪",
coinType = 200008,
price = 5,
itemIds = {
610343
}
},
[51365] = {
commodityId = 51365,
commodityName = "红尘铜障",
beginTime = {
seconds = 1742486400
},
endTime = v2,
shopTag = v3,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
itemIds = {
610344
},
canGift = true,
addIntimacy = 160,
giftCoinType = 205,
giftPrice = 180,
bOpenSuit = true,
suitId = 50175
},
[51366] = {
commodityId = 51366,
commodityName = "红尘铜障",
coinType = 200008,
price = 10,
itemIds = {
610345
}
},
[51367] = {
commodityId = 51367,
commodityName = "红尘铜障",
coinType = 200008,
price = 10,
itemIds = {
610346
}
},
[51368] = {
commodityId = 51368,
commodityName = "向左看齐",
beginTime = {
seconds = 1743091200
},
shopTag = v3,
jumpId = 705,
jumpText = "印章祈愿",
itemIds = {
610347
},
bOpenSuit = true,
suitId = 50176
},
[51369] = {
commodityId = 51369,
commodityName = "向右看齐",
beginTime = {
seconds = 1743091200
},
shopTag = v3,
jumpId = 705,
jumpText = "印章祈愿",
itemIds = {
610348
},
bOpenSuit = true,
suitId = 50177
},
[51370] = {
commodityId = 51370,
commodityName = "怪兽眼罩",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610349
},
suitId = 50178
},
[51371] = {
commodityId = 51371,
commodityName = "蝶梦之面",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610350
},
suitId = 50179
},
[51372] = {
commodityId = 51372,
commodityName = "蝶梦之面",
coinType = 200008,
price = 5,
itemIds = {
610351
}
},
[51373] = {
commodityId = 51373,
commodityName = "蝶梦之面",
coinType = 200008,
price = 5,
itemIds = {
610352
}
},
[51374] = {
commodityId = 51374,
commodityName = "晶蝶幻面",
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1753977599
},
shopTag = v3,
shopSort = 1,
jumpId = 8899,
jumpText = "蝶舞花间",
minVersion = "1.3.88.98",
itemIds = {
610353
},
canGift = true,
addIntimacy = 160,
giftCoinType = 3950,
giftPrice = 80,
bOpenSuit = true,
suitId = 50180
},
[51375] = {
commodityId = 51375,
commodityName = "晶蝶幻面",
coinType = 200008,
price = 10,
itemIds = {
610354
}
},
[51376] = {
commodityId = 51376,
commodityName = "晶蝶幻面",
coinType = 200008,
price = 10,
itemIds = {
610355
}
},
[51377] = {
commodityId = 51377,
commodityName = "弥生花语",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610356
},
suitId = 50181
},
[51378] = {
commodityId = 51378,
commodityName = "弥生花语",
coinType = 200008,
price = 10,
itemIds = {
610357
}
},
[51379] = {
commodityId = 51379,
commodityName = "弥生花语",
coinType = 200008,
price = 10,
itemIds = {
610358
}
},
[51380] = {
commodityId = 51380,
commodityName = "音浪视界",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopTag = v3,
jumpId = 88888,
jumpText = "幻彩调律",
itemIds = {
610359
},
bOpenSuit = true,
suitId = 50182
},
[51381] = {
commodityId = 51381,
commodityName = "音浪视界",
coinType = 200008,
price = 5,
itemIds = {
610360
}
},
[51382] = {
commodityId = 51382,
commodityName = "音浪视界",
coinType = 200008,
price = 5,
itemIds = {
610361
}
},
[51383] = {
commodityId = 51383,
commodityName = "迷糊泡泡",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610362
},
suitId = 50183
},
[51384] = {
commodityId = 51384,
commodityName = "迷糊泡泡",
coinType = 200008,
price = 10,
itemIds = {
610363
}
},
[51385] = {
commodityId = 51385,
commodityName = "迷糊泡泡",
coinType = 200008,
price = 10,
itemIds = {
610364
}
},
[51386] = {
commodityId = 51386,
commodityName = "苹果视界",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v3,
jumpId = 25,
jumpText = "小甜豆",
minVersion = "1.3.78.73",
itemIds = {
610365
},
bOpenSuit = true,
suitId = 50184
},
[51387] = {
commodityId = 51387,
commodityName = "破碎视界",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610366
},
suitId = 50185
},
[51388] = {
commodityId = 51388,
commodityName = "破碎视界",
coinType = 200008,
price = 5,
itemIds = {
610367
}
},
[51389] = {
commodityId = 51389,
commodityName = "破碎视界",
coinType = 200008,
price = 5,
itemIds = {
610368
}
},
[51390] = {
commodityId = 51390,
commodityName = "颠倒之视",
beginTime = {
seconds = 1743782400
},
endTime = {
seconds = 1746374400
},
shopTag = v3,
jumpId = 598,
jumpText = "翡光仙灵",
minVersion = "1.3.78.72",
itemIds = {
610369
},
bOpenSuit = true,
suitId = 50186
},
[51391] = {
commodityId = 51391,
commodityName = "颠倒之视",
coinType = 200008,
price = 5,
itemIds = {
610370
}
},
[51392] = {
commodityId = 51392,
commodityName = "颠倒之视",
coinType = 200008,
price = 5,
itemIds = {
610371
}
},
[51393] = {
commodityId = 51393,
commodityName = "霓虹之面",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
shopTag = v3,
jumpId = 694,
jumpText = "幻音喵境",
itemIds = {
610372
},
bOpenSuit = true,
suitId = 50187
},
[51394] = {
commodityId = 51394,
commodityName = "霓虹之面",
coinType = 200008,
price = 5,
itemIds = {
610373
}
},
[51395] = {
commodityId = 51395,
commodityName = "霓虹之面",
coinType = 200008,
price = 5,
itemIds = {
610374
}
},
[51396] = {
commodityId = 51396,
commodityName = "红扑嘟嘟",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610375
},
suitId = 50188
},
[51397] = {
commodityId = 51397,
commodityName = "无尽耀光",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
shopTag = v3,
jumpId = 694,
jumpText = "幻猫音境",
itemIds = {
610376
},
bOpenSuit = true,
suitId = 50189
},
[51398] = {
commodityId = 51398,
commodityName = "无尽耀光",
coinType = 200008,
price = 10,
itemIds = {
610377
}
},
[51399] = {
commodityId = 51399,
commodityName = "无尽耀光",
coinType = 200008,
price = 10,
itemIds = {
610378
}
},
[51400] = {
commodityId = 51400,
commodityName = "雷霆视界",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610379
},
suitId = 50190
},
[51401] = {
commodityId = 51401,
commodityName = "雷霆视界",
coinType = 200008,
price = 5,
itemIds = {
610380
}
},
[51402] = {
commodityId = 51402,
commodityName = "雷霆视界",
coinType = 200008,
price = 5,
itemIds = {
610381
}
},
[51403] = {
commodityId = 51403,
commodityName = "靓仔标配",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610382
},
suitId = 50191
},
[51404] = {
commodityId = 51404,
commodityName = "靓仔标配",
coinType = 200008,
price = 5,
itemIds = {
610383
}
},
[51405] = {
commodityId = 51405,
commodityName = "靓仔标配",
coinType = 200008,
price = 5,
itemIds = {
610384
}
},
[51406] = {
commodityId = 51406,
commodityName = "曲奇回忆",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610385
},
suitId = 50192
},
[51407] = {
commodityId = 51407,
commodityName = "熊宝嗅嗅",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610386
},
suitId = 50193
},
[51408] = {
commodityId = 51408,
commodityName = "胡言乱语",
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1751212799
},
shopTag = v3,
jumpId = 692,
jumpText = "狼人世界杯",
minVersion = "1.3.88.50",
itemIds = {
610387
},
suitId = 50194
},
[51409] = {
commodityId = 51409,
commodityName = "甜兔眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610388
},
suitId = 50195
},
[51410] = {
commodityId = 51410,
commodityName = "小丑鼻",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610389
},
suitId = 50196
},
[51411] = {
commodityId = 51411,
commodityName = "小丑鼻",
coinType = 200008,
price = 5,
itemIds = {
610390
}
},
[51412] = {
commodityId = 51412,
commodityName = "小丑鼻",
coinType = 200008,
price = 5,
itemIds = {
610391
}
},
[51413] = {
commodityId = 51413,
commodityName = "柠沫微霞",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopTag = v3,
jumpId = 88888,
jumpText = "幻彩调律",
itemIds = {
610392
},
bOpenSuit = true,
suitId = 50197
},
[51414] = {
commodityId = 51414,
commodityName = "柠沫微霞",
coinType = 200008,
price = 10,
itemIds = {
610393
}
},
[51415] = {
commodityId = 51415,
commodityName = "柠沫微霞",
coinType = 200008,
price = 10,
itemIds = {
610394
}
},
[51416] = {
commodityId = 51416,
commodityName = "落日眼镜",
beginTime = {
seconds = 1745856000
},
shopTag = v3,
jumpId = 705,
jumpText = "印章祈愿",
itemIds = {
610395
},
bOpenSuit = true,
suitId = 50198
},
[51417] = {
commodityId = 51417,
commodityName = "霓虹眼镜",
beginTime = {
seconds = 1745856000
},
shopTag = v3,
jumpId = 705,
jumpText = "印章祈愿",
itemIds = {
610396
},
bOpenSuit = true,
suitId = 50199
},
[51418] = {
commodityId = 51418,
commodityName = "初日眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610397
},
suitId = 50200
},
[51419] = {
commodityId = 51419,
commodityName = "小丑面具",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610398
},
suitId = 50201
},
[51420] = {
commodityId = 51420,
commodityName = "钳钳视界",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610399
},
suitId = 50202
},
[51421] = {
commodityId = 51421,
commodityName = "无忧眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610400
},
suitId = 50203
},
[51422] = {
commodityId = 51422,
commodityName = "无忧眼镜",
coinType = 200008,
price = 5,
itemIds = {
610401
}
},
[51423] = {
commodityId = 51423,
commodityName = "无忧眼镜",
coinType = 200008,
price = 5,
itemIds = {
610402
}
},
[51424] = {
commodityId = 51424,
commodityName = "松树之境",
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1753977599
},
shopTag = v3,
shopSort = 1,
jumpId = 8899,
jumpText = "蝶舞花间",
minVersion = "1.3.88.98",
itemIds = {
610403
},
canGift = true,
addIntimacy = 80,
giftCoinType = 3950,
giftPrice = 40,
bOpenSuit = true,
suitId = 50204
},
[51425] = {
commodityId = 51425,
commodityName = "松树之境",
coinType = 200008,
price = 5,
itemIds = {
610404
}
},
[51426] = {
commodityId = 51426,
commodityName = "松树之境",
coinType = 200008,
price = 5,
itemIds = {
610405
}
},
[51427] = {
commodityId = 51427,
commodityName = "咕咕面具",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610406
},
suitId = 50205
},
[51428] = {
commodityId = 51428,
commodityName = "咕咕面具",
coinType = 200008,
price = 5,
itemIds = {
610407
}
},
[51429] = {
commodityId = 51429,
commodityName = "咕咕面具",
coinType = 200008,
price = 5,
itemIds = {
610408
}
},
[51430] = {
commodityId = 51430,
commodityName = "惩恶符",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610409
},
suitId = 50206
},
[51431] = {
commodityId = 51431,
commodityName = "飞雪衔珠",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610410
},
suitId = 50207
},
[51432] = {
commodityId = 51432,
commodityName = "飞雪衔珠",
coinType = 200008,
price = 10,
itemIds = {
610411
}
},
[51433] = {
commodityId = 51433,
commodityName = "飞雪衔珠",
coinType = 200008,
price = 10,
itemIds = {
610412
}
},
[51434] = {
commodityId = 51434,
commodityName = "竹镜节节",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610413
},
suitId = 50208
},
[51435] = {
commodityId = 51435,
commodityName = "竹镜节节",
coinType = 200008,
price = 5,
itemIds = {
610414
}
},
[51436] = {
commodityId = 51436,
commodityName = "竹镜节节",
coinType = 200008,
price = 5,
itemIds = {
610415
}
},
[51437] = {
commodityId = 51437,
commodityName = "微糖视界",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610416
},
suitId = 50209
},
[51438] = {
commodityId = 51438,
commodityName = "微糖视界",
coinType = 200008,
price = 5,
itemIds = {
610417
}
},
[51439] = {
commodityId = 51439,
commodityName = "微糖视界",
coinType = 200008,
price = 5,
itemIds = {
610418
}
},
[51440] = {
commodityId = 51440,
commodityName = "甜力之眼",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610419
},
suitId = 50210
},
[51441] = {
commodityId = 51441,
commodityName = "甜力之眼",
coinType = 200008,
price = 5,
itemIds = {
610420
}
},
[51442] = {
commodityId = 51442,
commodityName = "甜力之眼",
coinType = 200008,
price = 5,
itemIds = {
610421
}
},
[51443] = {
commodityId = 51443,
commodityName = "心动防护罩",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610422
},
suitId = 50211
},
[51444] = {
commodityId = 51444,
commodityName = "心动防护罩",
coinType = 200008,
price = 5,
itemIds = {
610423
}
},
[51445] = {
commodityId = 51445,
commodityName = "心动防护罩",
coinType = 200008,
price = 5,
itemIds = {
610424
}
},
[51446] = {
commodityId = 51446,
commodityName = "恶魔创口贴",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610425
},
suitId = 50212
},
[51447] = {
commodityId = 51447,
commodityName = "波频眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610426
},
suitId = 50213
},
[51448] = {
commodityId = 51448,
commodityName = "彩波眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610427
},
suitId = 50214
},
[51449] = {
commodityId = 51449,
commodityName = "学究小镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610428
},
suitId = 50215
},
[51450] = {
commodityId = 51450,
commodityName = "侠客眼镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610429
},
suitId = 50216
},
[51451] = {
commodityId = 51451,
commodityName = "多巴胺镜",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610430
},
suitId = 50217
},
[51452] = {
commodityId = 51452,
commodityName = "星漩吹梦",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610431
},
suitId = 50218
},
[51453] = {
commodityId = 51453,
commodityName = "星漩吹梦",
coinType = 200008,
price = 10,
itemIds = {
610432
}
},
[51454] = {
commodityId = 51454,
commodityName = "星漩吹梦",
coinType = 200008,
price = 10,
itemIds = {
610433
}
},
[51455] = {
commodityId = 51455,
commodityName = "狼人之噬",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v2,
shopTag = v3,
itemIds = {
610434
},
suitId = 50219
}
}

local mt = {
mallId = 10,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1706198400
},
gender = 0,
canDirectBuy = false,
itemNums = {
1
},
bOpenSuit = false,
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
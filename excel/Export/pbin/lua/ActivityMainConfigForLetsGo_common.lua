--com.tencent.wea.xlsRes.table_ActivityMainConfig => excel/xls/H_活动中心配置.xlsx: 活动配置

local v0 = "ATGuide"

local v1 = "ATTaskAndShop"

local v2 = "ATLinearRedeem"

local v3 = 4

local v4 = 10

local v5 = 9

local v6 = 7

local v7 = 11

local v8 = 6

local v9 = 99

local v10 = 8

local v11 = 3

local v12 = 1

local v13 = 2

local v14 = 5

local v15 = "UI_CommonJumpActivity_View"

local v16 = "UI_CommonTask_SingleTaskView"

local v17 = "UI_CommonTask_MulTaskView"

local v18 = "UI_Activity_Exchange_LinearView"

local v19 = "UI_Activity_Exchange_KingView"

local v20 = "ANTGuide"

local v21 = "ANTSingleLogin"

local v22 = "ANTLinearRedeem"

local v23 = "ANTNormalExchange"

local v24 = {
"奖励商城",
"兑换任务"
}

local v25 = 0

local v26 = {
0
}

local v27 = {
25
}

local v28 = {
24
}

local v29 = "1.2.67.1"

local v30 = "1.2.80.1"

local v31 = "1.2.100.53"

local v32 = "AG_Farm"

local data = {
[2] = {
id = 2,
activityType = "ATPlatShareActive",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1697817599
},
showEndTime = {
seconds = 1697817599
},
showBeginTime = {
seconds = **********
}
},
labelId = 4,
activityName = "实时干预",
activityParam = {
4
},
rewardInfo = {
{
itemId = 1,
itemNum = 10
}
},
mailId = 1,
activityUIDetail = "UI_RealTimeIntervene_Main",
tagId = 1,
showInCenter = true,
activityNameType = "ANTPlatShareActive",
clientParams = {
"2:5",
"4:100"
},
titleType = 0,
platforms = {
1,
2,
3,
5
}
},
[3] = {
id = 3,
activityType = "ATLuckyMoney",
timeInfo = {
beginTime = {
seconds = 1702310400
},
endTime = {
seconds = 1704470399
},
showEndTime = {
seconds = 1704470399
},
showBeginTime = {
seconds = 1702310400
}
},
labelId = 10,
backgroundUrl = {
"T_Activity_SRPacket_N_A.astc"
},
activityName = "福袋满琳琅",
activityUIDetail = "UI_SRPacket_Main",
activityRuleId = 59,
tagId = 1,
activityTaskGroup = {
50200
},
showInCenter = true,
activityNameType = "ANTLuckyMoney",
titleType = 0
},
[4] = {
id = 4,
activityType = "ATDreamNewStar",
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 9,
activityName = "新星招募",
activityParam = {
3
},
activityDesc = "新星招募测试副标题副标题",
activityUIDetail = "UI_Recruit_Main",
tagId = 1,
activityTaskGroup = {
50300
},
showInCenter = true,
activityNameType = "ANTDreamNewStar",
titleType = 0,
platforms = v26
},
[5] = {
id = 5,
activityType = "ATFriendSquad",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 7,
activityName = "友情小队",
activityParam = {
1
},
activityUIDetail = "UI_FSA_Main",
tagId = 1,
showInCenter = true,
activityNameType = "ANTFriendSquad",
isHideMainBackground = true,
titleType = 0,
platforms = v26
},
[6] = {
id = 6,
activityType = "ATWealthBank",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 13,
activityName = "七日签到",
activityUIDetail = "UI_ManagementBank_Main",
tagId = 4,
showInCenter = true,
activityNameType = "ANTWealthBank",
titleType = 1,
activitySubName = "每天登录有礼",
platforms = v26
},
[7] = {
id = 7,
activityType = v0,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 9,
backgroundUrl = {
"T_Activity_DATIAOBg_01_N_A.astc"
},
jumpId = 14,
activityName = "引导跳大厅",
activityUIDetail = v15,
tagId = 1,
showInCenter = true,
activityNameType = v20,
isHideMainBackground = true,
titleType = 0,
platforms = v26
},
[9] = {
id = 9,
activityType = v1,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 11,
backgroundUrl = {
"T_Activity_TestTitle_5.astc",
"T_Activity_TestTitle_2.astc",
"T_LinearExchange_Img_Hero.astc"
},
activityName = "大奖常规兑换",
activityParam = {
25,
2006
},
activityDesc = "大奖常规兑换的描述",
activityUIDetail = "UI_Activity_Exchange_BigReward_View",
activityRuleId = 2,
tagId = 1,
activityTaskGroup = {
50900,
50901
},
showInCenter = true,
activityNameType = "ANTBigExchange",
activityShopType = v27,
titleType = 0,
activityBeginCleanCoin = {
2006
},
platforms = v26
},
[11] = {
id = 11,
activityType = "ATDailyColorPaint",
timeInfo = {
beginTime = {
seconds = 1731254400
},
endTime = {
seconds = 1731859199
},
showEndTime = {
seconds = 1731859199
},
showBeginTime = {
seconds = 1731254400
}
},
labelId = 13,
backgroundUrl = {
"T_ColorDaily_Bg_Base_N_A.astc"
},
activityName = "星宝手绘本",
activityParam = {
500612
},
activityDesc = "快和冰糕糕一起涂色完成绘本吧~",
activityUIDetail = "UI_Activity_DailyDrawView",
tagId = 1,
activityTaskGroup = {
50600,
50601
},
showInCenter = true,
activityNameType = "ANTDailyColorPaint",
titleType = 0,
activityBeginCleanCoin = {
3421
},
platforms = v26
},
[12] = {
id = 12,
activityType = "ATChapterTask",
timeInfo = {
beginTime = {
seconds = 1688140800
},
endTime = {
seconds = 1695398398
},
showEndTime = {
seconds = 1695398398
},
showBeginTime = {
seconds = 1688140800
}
},
labelId = 98,
backgroundUrl = {
"T_Activity_TestTitle_5.astc"
},
activityName = "章节任务",
activityParam = v28,
activityDesc = "章节任务的描述",
activityUIDetail = "UI_Activity_ChapterTaskView",
tagId = 1,
showInCenter = true,
activityNameType = "ANTChapterInPlan",
activityShopType = v28,
titleType = 0,
platforms = v26
},
[13] = {
id = 13,
activityType = "ATCheckInPlan",
timeInfo = {
beginTime = {
seconds = 1688140800
},
endTime = {
seconds = 1747929599
},
showEndTime = {
seconds = 1747929599
},
showBeginTime = {
seconds = 1688140800
}
},
labelId = 15,
backgroundUrl = {
"T_Activity_CheckIn_N_A.astc"
},
activityName = "星梦贴纸簿",
activityDesc = "打卡计划的描述",
activityUIDetail = "UI_CheckIn_MainView",
tagId = 1,
showInCenter = true,
activityNameType = "ANTCheckInPlan",
titleType = 0,
platforms = v26
},
[14] = {
id = 14,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 6,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "单条测试登录",
activityUIDetail = v16,
tagId = 1,
activityTaskGroup = {
51601
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[15] = {
id = 15,
timeInfo = {
beginTime = {
seconds = 1686326400
},
endTime = {
seconds = 1686412799
},
showEndTime = {
seconds = 1686412799
},
showBeginTime = {
seconds = 1686326400
}
},
labelId = 99,
backgroundUrl = {
"T_Activity_Bg_GADUOT1_N_A.astc"
},
activityName = "天天跑局活动备用",
activityUIDetail = v17,
tagId = 1,
activityTaskGroup = {
51602,
51603,
51604
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[17] = {
id = 17,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 8,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "多条测试跑局",
activityUIDetail = v17,
tagId = 1,
activityTaskGroup = {
51605,
51606,
51607,
51608,
51609,
51610
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[19] = {
id = 19,
activityType = v0,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1686412799
},
showEndTime = {
seconds = 1686412799
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
backgroundUrl = {
"T_Activity_DATIAOBg_01_N_A.astc"
},
jumpId = 14,
activityName = "引导跳大厅",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
titleType = 0,
platforms = v26
},
[20] = {
id = 20,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1686412799
},
showEndTime = {
seconds = 1686412799
},
showBeginTime = {
seconds = **********
}
},
labelId = 1,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
jumpId = 0,
activityName = "单条测试登录活动1",
activityUIDetail = v16,
tagId = 6,
activityTaskGroup = {
51612
},
showInCenter = true,
activityNameType = v21,
jumpType = 1,
titleType = 0,
platforms = v26
},
[21] = {
id = 21,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1686412799
},
showEndTime = {
seconds = 1686412799
},
showBeginTime = {
seconds = **********
}
},
labelId = 2,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
jumpId = 1,
activityName = "多条累登跑局活动2",
activityUIDetail = v17,
tagId = 6,
activityTaskGroup = {
51613,
51614,
51615
},
showInCenter = true,
jumpType = 1,
titleType = 0,
platforms = v26
},
[22] = {
id = 22,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 10,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "多条累登",
activityUIDetail = v17,
tagId = 1,
activityTaskGroup = {
51616,
51617,
51618
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[23] = {
id = 23,
timeInfo = {
beginTime = {
seconds = 1682870400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1682870400
}
},
labelId = 97,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "测试组多任务展示",
activityUIDetail = v17,
tagId = 1,
activityTaskGroup = {
51619,
51620
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[24] = {
id = 24,
timeInfo = {
beginTime = {
seconds = 1682870400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1682870400
}
},
labelId = 12,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "测试组单任务展示",
activityUIDetail = v16,
tagId = 1,
activityTaskGroup = {
51621
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[25] = {
id = 25,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1686412799
},
showEndTime = {
seconds = 1686412799
},
showBeginTime = {
seconds = **********
}
},
labelId = 4,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
jumpId = 2,
activityName = "每日多次跑局活动3",
activityUIDetail = v16,
tagId = 6,
activityTaskGroup = {
51622
},
showInCenter = true,
activityNameType = v21,
jumpType = 1,
titleType = 0,
platforms = v26
},
[26] = {
id = 26,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 13,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "0519新增任务类型1",
activityUIDetail = v17,
tagId = 1,
activityTaskGroup = {
51623,
51624,
51625
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[27] = {
id = 27,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 14,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "0519新增任务类型2",
activityUIDetail = v17,
tagId = 1,
activityTaskGroup = {
51626,
51627,
51628
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[28] = {
id = 28,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 15,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "0519新增任务类型3",
activityUIDetail = v17,
tagId = 1,
activityTaskGroup = {
51629,
51630,
51631
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[29] = {
id = 29,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 16,
backgroundUrl = {
"T_Activity_Bg_DUO3_N_A.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "0522多条花式跑局",
activityUIDetail = v17,
activityRuleId = 18,
tagId = 1,
activityTaskGroup = {
51632,
51633,
51634,
51635,
51636,
51637,
51639,
51640
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[30] = {
id = 30,
timeInfo = {
beginTime = {
seconds = 1682870400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1682870400
}
},
labelId = 17,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "0606测试组单任务展示",
activityUIDetail = v16,
tagId = 1,
activityTaskGroup = {
51641
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[40] = {
id = 40,
timeInfo = {
beginTime = {
seconds = 1688140800
},
endTime = {
seconds = 1692287999
},
showEndTime = {
seconds = 1692287999
},
showBeginTime = {
seconds = 1688140800
}
},
labelId = 2,
backgroundUrl = {
"T_Activity_Bg_GGADUO5_N_A.astc"
},
activityName = "累登七日礼",
activityUIDetail = v17,
tagId = 1,
activityTaskGroup = {
51659
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[43] = {
id = 43,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1688140800
},
endTime = {
seconds = 1699027199
},
showEndTime = {
seconds = 1699027199
},
showBeginTime = {
seconds = 1688140800
}
},
labelId = 11,
backgroundUrl = {
"T_LinearExchange_Img_Background_N_A.astc",
"T_LinearExchange_Img_Title1.astc",
"T_LinearExchange_Img_Girl.astc"
},
activityName = "限时福利赏",
activityParam = {
24,
2004,
500708,
500711
},
activityDesc = "福利值拉满，限时兑换米花娘爆爆",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
50702,
50700,
50701
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2004
},
platforms = v26
},
[44] = {
id = 44,
timeInfo = {
beginTime = {
seconds = 1688140800
},
endTime = {
seconds = 1691423999
},
showEndTime = {
seconds = 1691423999
},
showBeginTime = {
seconds = 1688140800
}
},
labelId = 6,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "单条10点开启",
activityUIDetail = v16,
tagId = 1,
activityTaskGroup = {
51663
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[45] = {
id = 45,
timeInfo = {
beginTime = {
seconds = 1688140800
},
endTime = {
seconds = 1691423999
},
showEndTime = {
seconds = 1691423999
},
showBeginTime = {
seconds = 1688140800
}
},
labelId = 9,
backgroundUrl = {
"T_Activity_Bg_DUO3_N_A.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "多条10点开启",
activityUIDetail = v17,
tagId = 1,
activityTaskGroup = {
51664
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[46] = {
id = 46,
timeInfo = {
beginTime = {
seconds = 1688140800
},
endTime = {
seconds = 1691423999
},
showEndTime = {
seconds = 1691423999
},
showBeginTime = {
seconds = 1688140800
}
},
labelId = 8,
backgroundUrl = {
"T_Activity_Bg_0605.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "单条已开启",
activityUIDetail = v16,
tagId = 1,
activityTaskGroup = {
51665
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[47] = {
id = 47,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1692460799
},
showEndTime = {
seconds = 1692460799
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 3,
backgroundUrl = {
"T_TaskCenter_Gift_Bg.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "幸运兑换",
activityParam = {
25,
2006
},
activityDesc = "快来星宝市收集幸运四叶草吧~",
activityUIDetail = v19,
tagId = 1,
activityTaskGroup = {
51666
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = {
"兑换商店",
"幸运任务"
},
titleType = 0,
activityBeginCleanCoin = {
2006
},
platforms = v26
},
[48] = {
id = 48,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1699372800
},
endTime = {
seconds = 1699545599
},
showEndTime = {
seconds = 1699545599
},
showBeginTime = {
seconds = 1699372800
}
},
labelId = 12,
backgroundUrl = {
"T_MonthView_Img_1Background_N_A.astc"
},
activityName = "小雪接星运",
activityParam = {
7000,
2008,
150
},
activityDesc = "稀有配饰限时领，小雪节气星运连连！",
activityUIDetail = "UI_Activity_MonthView",
tagId = 1,
activityTaskGroup = {
51668
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
7000
},
clientParams = {
"小雪接星运",
"经典模式、娱乐模式概率掉落",
"<MonthViewYellow26>15次</>抽取内必得<MonthViewYellow26>星小递外观奖励</>"
},
titleType = 0,
activityBeginCleanCoin = {
2008
},
platforms = v26
},
[49] = {
id = 49,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1691942400
},
endTime = {
seconds = 1738425599
},
showEndTime = {
seconds = 1738425599
},
showBeginTime = {
seconds = 1691942400
}
},
labelId = 95,
backgroundUrl = {
"T_TaskCenter_Gift_Bg.astc",
"T_TaskCenter_Avatar_Img2.astc"
},
activityName = "幸运兑换",
activityParam = {
25,
2006
},
activityDesc = "快来星宝市收集幸运四叶草吧~",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51669,
51670
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = {
"兑换商店",
"幸运任务"
},
titleType = 0,
activityBeginCleanCoin = {
2006
},
platforms = v26
},
[50] = {
id = 50,
timeInfo = {
beginTime = {
seconds = 1742227200
},
endTime = {
seconds = 1743523199
},
showEndTime = {
seconds = 1743523199
},
showBeginTime = {
seconds = 1742227200
}
},
labelId = 1,
activityName = "庆典签到",
activityUIDetail = "UI_FestivalCheckIn_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51671
},
showInCenter = true,
slapFace = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[51] = {
id = 51,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 2,
backgroundUrl = {
"NActivity_BgDUO8_mh_N_A.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "开局有好礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51672,
51673,
51674
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[52] = {
id = 52,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 7,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "探索星宝市",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51675
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[53] = {
id = 53,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 12,
backgroundUrl = {
"NActivity_BgDUOO2_mh_N_A.astc"
},
activityName = "定点补给站",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51676
},
showInCenter = true,
titleType = 0,
activitySubName = "限时免费抽",
platforms = v26
},
[54] = {
id = 54,
activityType = v1,
labelId = 10,
backgroundUrl = {
"T_Lmg_RegularExchange_Bag_Z_02_N_A.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "随心挑战礼",
activityParam = {
23,
2005
},
activityDesc = "集随心之花，得非凡套装",
activityUIDetail = "UI_Activity_Exchange_NormalView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51660
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
23
},
titleType = 1,
activityBeginCleanCoin = {
2005
},
platforms = v26
},
[57] = {
id = 57,
timeInfo = {
beginTime = {
seconds = 1688140800
},
endTime = {
seconds = 1698335999
},
showEndTime = {
seconds = 1698335999
},
showBeginTime = {
seconds = 1688140800
}
},
labelId = 2,
backgroundUrl = {
"chuangzuoBannerr.jpg"
},
activityName = "创作大赛",
activityUIDetail = v16,
tagId = 6,
activityTaskGroup = {
51679
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[58] = {
id = 58,
activityType = "ATSquad",
timeInfo = {
beginTime = {
seconds = 1704384000
},
endTime = {
seconds = 1704988799
},
showEndTime = {
seconds = 1704988799
},
showBeginTime = {
seconds = 1704384000
}
},
labelId = 10,
activityName = "赶海小队",
activityParam = {
1
},
activityUIDetail = "UI_Activity_MultiplayerSquad_View",
activityRuleId = 64,
tagId = 1,
activityTaskGroup = {
51848,
51849
},
showInCenter = true,
activityNameType = "ANTSquad",
isHideMainBackground = true,
titleType = 0,
platforms = v26
},
[59] = {
id = 59,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1688140800
},
endTime = {
seconds = 1762012799
},
showEndTime = {
seconds = 1762012799
},
showBeginTime = {
seconds = 1688140800
}
},
labelId = 1,
backgroundUrl = {
"UGC_1026datuTZ.jpg"
},
jumpId = 11,
activityName = "创作大赛",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[60] = {
id = 60,
activityType = "ATTakeaway",
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1700927999
},
showEndTime = {
seconds = 1700927999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 1,
backgroundUrl = {
"chuangzuoBannerr.jpg"
},
activityName = "棉棉外卖",
activityParam = {
1
},
activityUIDetail = "UI_BoxDelivery_MainView",
tagId = 1,
activityTaskGroup = {
51682,
51683
},
showInCenter = true,
activityNameType = "ANTTakeaway",
activityShopType = {
32
},
titleType = 0,
activityBeginCleanCoin = {
2009,
2010
},
platforms = v26
},
[61] = {
id = 61,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 7,
backgroundUrl = {
"NActivity_BgDUO5_mh.astc"
},
activityName = "分享测试",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51684
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[62] = {
id = 62,
activityType = "ATChapterTask",
labelId = 98,
backgroundUrl = {
"chaptertask_bg_N_A.astc"
},
activityName = "满月回馈礼",
activityParam = v28,
activityUIDetail = "UI_Activity_ChapterTaskView",
tagId = 1,
showInCenter = true,
activityNameType = "ANTChapterInPlan",
activityShopType = v28,
titleType = 0,
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
2
}
}
},
platforms = v26
},
[63] = {
id = 63,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 2,
backgroundUrl = {
"NActivity_BgDUO5_mh.astc"
},
activityName = "华为渠道可见",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51691,
51692
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[64] = {
id = 64,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 2,
backgroundUrl = {
"NActivity_BgDUO5_mh.astc"
},
activityName = "微信QQ不可见",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51693,
51694
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[65] = {
id = 65,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 2,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "仅手Q可见",
activityUIDetail = v16,
tagId = 1,
activityTaskGroup = {
51695
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[66] = {
id = 66,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 2,
backgroundUrl = {
"NActivity_BgDUO5_mh.astc"
},
activityName = "仅微信可见",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51696,
51697
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[67] = {
id = 67,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "Abtest1",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51698
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
2
}
}
},
platforms = v26
},
[68] = {
id = 68,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "Abtest2",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51699
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[69] = {
id = 69,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "Abtest3",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51700
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[70] = {
id = 70,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "Abtest4",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51701
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[71] = {
id = 71,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "Abtest5",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51702
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[72] = {
id = 72,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "Abtest6",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51703
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[73] = {
id = 73,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "Abtest7",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51704
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[74] = {
id = 74,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "Abtest8",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51705
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[75] = {
id = 75,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "Abtest9",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51706
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[76] = {
id = 76,
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1699199999
},
showEndTime = {
seconds = 1699199999
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "Abtest10",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51707
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[77] = {
id = 77,
timeInfo = {
beginTime = {
seconds = 1688140800
},
endTime = {
seconds = 1700582399
},
showEndTime = {
seconds = 1700582399
},
showBeginTime = {
seconds = 1688140800
}
},
labelId = 2,
backgroundUrl = {
"UGC_FAbuTZ.jpg"
},
jumpId = 11,
activityName = "发布作品",
activityUIDetail = v16,
isInBottom = 1,
tagId = 6,
activityTaskGroup = {
51708
},
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[79] = {
id = 79,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1699113600
},
endTime = {
seconds = 1699372799
},
showEndTime = {
seconds = 1699372799
},
showBeginTime = {
seconds = 1699113600
}
},
labelId = 11,
backgroundUrl = {
"T_LinearExchange_Img_Background_N_A.astc",
"T_LinearExchange_Img_Title1.astc",
"T_LinearExchange_Img_Girl.astc"
},
activityName = "限时福利赏",
activityParam = {
24,
2004,
515207,
515210
},
activityDesc = "福利值拉满，限时兑换米花娘爆爆",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51712,
51710,
51711
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2004
},
platforms = v26
},
[80] = {
id = 80,
labelId = 99,
activityName = "小版本签到",
activityUIDetail = "UI_SignIn_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51713
},
showInCenter = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[83] = {
id = 83,
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "前往任务类型测试",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51720
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[84] = {
id = 84,
timeInfo = {
beginTime = {
seconds = 1699372800
},
endTime = {
seconds = 1702051199
},
showEndTime = {
seconds = 1702051199
},
showBeginTime = {
seconds = 1699372800
}
},
labelId = 1,
activityName = "庆典签到",
activityUIDetail = "UI_FestivalCheckIn_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51721
},
showInCenter = true,
slapFace = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[85] = {
id = 85,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1699372800
},
endTime = {
seconds = 1700495999
},
showEndTime = {
seconds = 1700495999
},
showBeginTime = {
seconds = 1699372800
}
},
labelId = 11,
backgroundUrl = {
"T_LinearExchange_Img_Background3_N_A.astc",
"T_LinearExchange_Img_Title1.astc",
"T_LinearExchange_Img_3Girl.astc"
},
activityName = "限时福利赏",
activityParam = {
24,
2004,
515250,
515253
},
activityDesc = "福利值拉满，限时兑换甜橙喵桑妮",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51724,
51722,
51723
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2004
},
platforms = v26
},
[86] = {
id = 86,
timeInfo = {
beginTime = {
seconds = 1699372800
},
endTime = {
seconds = 1701964799
},
showEndTime = {
seconds = 1701964799
},
showBeginTime = {
seconds = 1699372800
}
},
labelId = 12,
backgroundUrl = {
"NActivity_BgDUO8_mh_N_A.astc"
},
activityName = "定点补给站",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51725
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[87] = {
id = 87,
timeInfo = {
beginTime = {
seconds = 1699372800
},
endTime = {
seconds = 1701359999
},
showEndTime = {
seconds = 1701359999
},
showBeginTime = {
seconds = 1699372800
}
},
labelId = 7,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "星梦广场",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51726
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[88] = {
id = 88,
timeInfo = {
beginTime = {
seconds = 1699372800
},
endTime = {
seconds = 1701964799
},
showEndTime = {
seconds = 1701964799
},
showBeginTime = {
seconds = 1699372800
}
},
labelId = 2,
backgroundUrl = {
"NActivity_BgDUOO2_mh_N_A.astc"
},
activityName = "为你星动",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51727
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[89] = {
id = 89,
timeInfo = {
beginTime = {
seconds = 1699372800
},
endTime = {
seconds = 1700927999
},
showEndTime = {
seconds = 1700927999
},
showBeginTime = {
seconds = 1699372800
}
},
labelId = 4,
backgroundUrl = {
"NActivity_BgDUO5_mh.astc"
},
activityName = [[公主王子
请上线]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51728
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[90] = {
id = 90,
timeInfo = {
beginTime = {
seconds = 1699372800
},
endTime = {
seconds = 1701532799
},
showEndTime = {
seconds = 1701532799
},
showBeginTime = {
seconds = 1699372800
}
},
labelId = 4,
backgroundUrl = {
"NActivity_BgDUO5_mh.astc"
},
activityName = [[公主王子
请上线]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51729
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[91] = {
id = 91,
timeInfo = {
beginTime = {
seconds = 1699372800
},
endTime = {
seconds = 1701964799
},
showEndTime = {
seconds = 1701964799
},
showBeginTime = {
seconds = 1699372800
}
},
labelId = 4,
backgroundUrl = {
"NActivity_BgDUO5_mh.astc"
},
activityName = [[公主王子
请上线]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51730
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[92] = {
id = 92,
timeInfo = {
beginTime = {
seconds = 1699372800
},
endTime = {
seconds = 1701964799
},
showEndTime = {
seconds = 1701964799
},
showBeginTime = {
seconds = 1699372800
}
},
labelId = 3,
backgroundUrl = {
"NActivity_BgDUO4_mh.astc"
},
activityName = "段位冲刺",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51731
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[93] = {
id = 93,
labelId = 3,
backgroundUrl = {
"NActivity_BgDUO3_mh.astc"
},
activityName = "专属开局礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51732
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[94] = {
id = 94,
labelId = 3,
backgroundUrl = {
"NActivity_BgDUO3_mh.astc"
},
activityName = "专属开局礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51733
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[95] = {
id = 95,
labelId = 3,
backgroundUrl = {
"NActivity_BgDUO3_mh.astc"
},
activityName = "专属开局礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51734
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[96] = {
id = 96,
activityType = v1,
labelId = 4,
backgroundUrl = {
"T_TaskCenter_Gift_Bg.astc",
"T_TaskCenter_Avatar_Img2.astc"
},
activityName = "糕糕来袭",
activityParam = {
25,
2006
},
activityDesc = "收集四叶草，和糕糕一起玩耍吧~",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51735,
51736
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = {
"糕糕兑换",
"惊喜任务"
},
titleType = 0,
activityBeginCleanCoin = {
2006
},
platforms = v26
},
[98] = {
id = 98,
activityType = v0,
labelId = 1,
backgroundUrl = {
"UGC_FAbuTZ2.jpg"
},
jumpId = 11,
activityName = [[星动测试
抢先赛]],
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[99] = {
id = 99,
activityType = v1,
labelId = 4,
backgroundUrl = {
"T_TaskCenter_Gift_Bgxiaoxin1_N_A.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "小新幸运礼",
activityParam = {
25,
2006
},
activityDesc = "和小新一起玩耍吧~",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51737,
51738
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
2006
},
platforms = v26
},
[100] = {
id = 100,
labelId = 8,
backgroundUrl = {
"NActivity_BgDan1_mh_N_A.astc"
},
activityName = "前往官网",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51739
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = {
1,
2,
3,
4
}
},
[101] = {
id = 101,
timeInfo = {
beginTime = {
seconds = 1742227200
},
endTime = {
seconds = 1743523199
},
showEndTime = {
seconds = 1743523199
},
showBeginTime = {
seconds = 1742227200
}
},
labelId = 99,
activityName = "元旦签到礼",
activityDesc = "元旦签到礼元旦签到礼",
activityUIDetail = "UI_SignIn_NewaYear_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51740
},
showInCenter = true,
activityNameType = "ANTSevenSign",
titleType = 1
},
[102] = {
id = 102,
timeInfo = {
beginTime = {
seconds = 1742227200
},
endTime = {
seconds = 1743523199
},
showEndTime = {
seconds = 1743523199
},
showBeginTime = {
seconds = 1742227200
}
},
labelId = 99,
activityName = "满月签到礼",
activityDesc = "满月签到礼满月签到礼",
activityUIDetail = "UI_SignIn_Month_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51741
},
showInCenter = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[104] = {
id = 104,
timeInfo = {
beginTime = {
seconds = 1701100800
},
endTime = {
seconds = 1701446399
},
showEndTime = {
seconds = 1701446399
},
showBeginTime = {
seconds = 1701100800
}
},
labelId = 98,
backgroundUrl = {
"T_Activity_Bg_DUO8.astc"
},
activityName = "娱乐专属礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51746
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[105] = {
id = 105,
timeInfo = {
beginTime = {
seconds = 1701187200
},
endTime = {
seconds = 1701705599
},
showEndTime = {
seconds = 1701705599
},
showBeginTime = {
seconds = 1701187200
}
},
labelId = 98,
backgroundUrl = {
"T_Activity_Bg_DUO8.astc"
},
activityName = "任务限时开启测试",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51747,
51748,
51749
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[106] = {
id = 106,
timeInfo = {
beginTime = {
seconds = 1701187200
},
endTime = {
seconds = 1701705599
},
showEndTime = {
seconds = 1701705599
},
showBeginTime = {
seconds = 1701187200
}
},
labelId = 98,
backgroundUrl = {
"T_Activity_Bg_DUO8.astc"
},
activityName = "跳转地图测试",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51750
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[107] = {
id = 107,
timeInfo = {
beginTime = {
seconds = 1701187200
},
endTime = {
seconds = 1701705599
},
showEndTime = {
seconds = 1701705599
},
showBeginTime = {
seconds = 1701187200
}
},
labelId = 98,
backgroundUrl = {
"T_Activity_Bg_DUO8.astc"
},
activityName = "指定日期跳地图",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51751,
51752,
51753
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[108] = {
id = 108,
labelId = 98,
backgroundUrl = {
"T_Activity_Bg_DUO8.astc"
},
activityName = "分享测试",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51754,
51755
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[109] = {
id = 109,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1701100800
},
endTime = {
seconds = 1701446399
},
showEndTime = {
seconds = 1701446399
},
showBeginTime = {
seconds = 1701100800
}
},
labelId = 99,
backgroundUrl = {
"T_LinearExchange_Img_Background5_N_A.astc",
"T_Activity_TestTitle_5.astc",
"T_LinearExchange_Img_5Girl.astc"
},
activityName = "娱乐福利赏",
activityParam = {
24,
2011,
515301,
515318
},
activityDesc = "娱乐模式福利来啦~限时兑换专属称号",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51742,
51743,
51744,
51745
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2011
},
platforms = v26
},
[111] = {
id = 111,
timeInfo = {
beginTime = {
seconds = 1708876800
},
endTime = {
seconds = 1735660799
},
showEndTime = {
seconds = 1735660799
},
showBeginTime = {
seconds = 1708876800
}
},
labelId = 16,
backgroundUrl = {
"NActivity_BgDUO5_mh.astc"
},
activityName = "小小红人",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51762,
51763
},
showInCenter = true,
titleType = 0,
activitySubName = "开播看播领奖",
platforms = {
1,
2,
3,
4,
5,
6
}
},
[112] = {
id = 112,
labelId = 100,
backgroundUrl = {
"NActivity_BgDUO5_mh.astc"
},
activityName = "小小红人礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51764,
51765,
51766
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[113] = {
id = 113,
activityType = v1,
labelId = 4,
backgroundUrl = {
"T_MonthView_Img_1Background_N_A.astc"
},
activityName = "小乔送星运",
activityParam = {
7000,
2008,
150
},
activityDesc = "星运连连~王者荣耀小乔联名饰品限时领！",
activityUIDetail = "UI_Activity_MonthView",
tagId = 1,
activityTaskGroup = {
51767
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
7000
},
clientParams = {
"小乔送星运",
"经典模式、娱乐模式概率掉落",
"<MonthViewYellow26>15次</>抽取内必得<MonthViewYellow26>王者荣耀小乔联名饰品</>"
},
titleType = 0,
activityBeginCleanCoin = {
2008
},
platforms = v26
},
[114] = {
id = 114,
timeInfo = {
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1702569600
}
},
labelId = 10,
activityName = "庆典签到",
activityUIDetail = "UI_FestivalCheckIn_MainView",
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51768
},
showInCenter = true,
slapFace = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[115] = {
id = 115,
timeInfo = {
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1703174399
},
showEndTime = {
seconds = 1703174399
},
showBeginTime = {
seconds = 1702569600
}
},
labelId = 2,
backgroundUrl = {
"NActivity_BgDUO3_mh.astc"
},
activityName = "开服开局礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51769
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[116] = {
id = 116,
timeInfo = {
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1702569600
}
},
labelId = 9,
backgroundUrl = {
"T_Activity_Bg_GGADUO6.astc"
},
activityName = "星宝来啦",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51770
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[117] = {
id = 117,
timeInfo = {
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1702569600
}
},
labelId = 9,
backgroundUrl = {
"T_Activity_Bg_GGADUO6.astc"
},
activityName = "星宝来啦",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51771
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[118] = {
id = 118,
timeInfo = {
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1702569600
}
},
labelId = 15,
backgroundUrl = {
"NActivity_BgDUO4_mh.astc"
},
activityName = "潮玩星方向",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51772
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[119] = {
id = 119,
timeInfo = {
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1702569600
}
},
labelId = 5,
backgroundUrl = {
"xinzeng02.astc"
},
activityName = "定点补给站",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51773
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[120] = {
id = 120,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1702569600
}
},
labelId = 6,
backgroundUrl = {
"TNTpicture.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "初星代言人",
activityParam = {
24,
2013,
515435
},
activityDesc = "时代少年团专属赠礼 限时兑换中",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51774,
51775
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2013
},
platforms = v26
},
[121] = {
id = 121,
timeInfo = {
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1703174399
},
showEndTime = {
seconds = 1703174399
},
showBeginTime = {
seconds = 1702569600
}
},
labelId = 1,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = [[公主王子请
上线]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51776
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[122] = {
id = 122,
activityType = v0,
labelId = 7,
backgroundUrl = {
"T_Activity_DATIAOBg_01_N_A.astc"
},
jumpId = 90,
activityName = "赢代言海报",
activityUIDetail = v15,
tagId = 1,
showInCenter = true,
activityNameType = v20,
isHideMainBackground = true,
titleType = 0,
platforms = v26
},
[123] = {
id = 123,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1704383999
},
showEndTime = {
seconds = 1704383999
},
showBeginTime = {
seconds = 1702569600
}
},
labelId = 16,
backgroundUrl = {
"T_MonthView_Img_1Background_N_A.astc"
},
activityName = "小乔送星运",
activityParam = {
7000,
2008,
150
},
activityDesc = "星运连连~王者荣耀小乔联名饰品限时领！",
activityUIDetail = "UI_Activity_MonthView",
tagId = 1,
activityTaskGroup = {
51777
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
7000
},
clientParams = {
"小乔送星运",
"经典模式、娱乐模式概率掉落",
"<MonthViewYellow26>15次</>抽取内必得<MonthViewYellow26>王者荣耀小乔联名饰品</>"
},
titleType = 0,
activityBeginCleanCoin = {
2008
},
platforms = v26
},
[124] = {
id = 124,
activityType = v1,
labelId = 4,
backgroundUrl = {
"T_TaskCenter_Gift_Bg.astc",
"T_TaskCenter_Avatar_Img2.astc"
},
activityName = "找梦奇玩耍",
activityParam = {
25,
2006
},
activityDesc = "收集四叶草，和糕糕一起玩耍吧~",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51778,
51779
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = {
"糕糕兑换",
"惊喜任务"
},
titleType = 0,
activityBeginCleanCoin = {
2006
},
platforms = v26
},
[125] = {
id = 125,
activityType = v1,
labelId = 7,
backgroundUrl = {
"T_TaskCenter_Gift_Bg.astc",
"T_TaskCenter_Avatar_Img3.astc"
},
activityName = "找梦奇玩耍",
activityParam = {
25,
2006
},
activityDesc = "收集四叶草，和梦奇玩耍吧~",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51780,
51781
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = {
"兑换商城",
"星运任务"
},
titleType = 0,
activityBeginCleanCoin = {
2006
},
platforms = v26
},
[126] = {
id = 126,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1702396800
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1702396800
}
},
labelId = 3,
backgroundUrl = {
"1UGCsaijiS1.astc"
},
jumpId = 11,
activityName = "造梦计划",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[127] = {
id = 127,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1702396800
},
endTime = {
seconds = 1704383999
},
showEndTime = {
seconds = 1704383999
},
showBeginTime = {
seconds = 1702396800
}
},
labelId = 2,
backgroundUrl = {
"1UGC_Wangzhe4.astc"
},
jumpId = 12,
activityName = "王者星创作",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[128] = {
id = 128,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1704384000
},
endTime = {
seconds = 1704988799
},
showEndTime = {
seconds = 1704988799
},
showBeginTime = {
seconds = 1704384000
}
},
labelId = 2,
backgroundUrl = {
"1UGC_Wangzhe5.astc"
},
jumpId = 12,
activityName = "王者星创作",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[129] = {
id = 129,
activityType = v0,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 2,
backgroundUrl = {
"1UGC_Wangzhe6.astc"
},
jumpId = 12,
activityName = "王者星创作",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[130] = {
id = 130,
activityType = v0,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1704643199
},
showEndTime = {
seconds = 1704643199
},
showBeginTime = {
seconds = **********
}
},
labelId = 4,
backgroundUrl = {
"1UGC_meituan1.astc"
},
jumpId = 13,
activityName = "星骑士创作",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[131] = {
id = 131,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1704643200
},
endTime = {
seconds = 1704902399
},
showEndTime = {
seconds = 1704902399
},
showBeginTime = {
seconds = 1704643200
}
},
labelId = 4,
backgroundUrl = {
"1UGC_meituan2.astc"
},
jumpId = 13,
activityName = "星骑士创作",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[132] = {
id = 132,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1704902400
},
endTime = {
seconds = 1705507199
},
showEndTime = {
seconds = 1705507199
},
showBeginTime = {
seconds = 1704902400
}
},
labelId = 4,
backgroundUrl = {
"1UGC_meituan3.astc"
},
jumpId = 13,
activityName = "星骑士创作",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[135] = {
id = 135,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1703174400
},
endTime = {
seconds = 1704988799
},
showEndTime = {
seconds = 1704988799
},
showBeginTime = {
seconds = 1703174400
}
},
labelId = 19,
backgroundUrl = {
"xianxing_baola.astc",
"T_Activity_TestTitle_5.astc",
"T_LinearExchange_Img_Girl9.astc"
},
activityName = "舞动青春秀",
activityParam = {
24,
2004,
515514
},
activityDesc = "星动票选C位出道~限时兑换非凡时装",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51789,
51791,
51792,
51793,
51794
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2004
},
platforms = v26
},
[136] = {
id = 136,
activityType = v1,
labelId = 3,
backgroundUrl = {
"T_Lmg_RegularExchange_Bag_Z_02_N_A.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "梦想星之队",
activityParam = {
23,
2005
},
activityDesc = "集组队花花，得限时称号！",
activityUIDetail = "UI_Activity_Exchange_NormalView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51795
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
23
},
titleType = 0,
activityBeginCleanCoin = {
2005
},
platforms = v26
},
[137] = {
id = 137,
timeInfo = {
beginTime = {
seconds = 1703174400
},
endTime = {
seconds = 1703433599
},
showEndTime = {
seconds = 1703433599
},
showBeginTime = {
seconds = 1703174400
}
},
labelId = 99,
backgroundUrl = {
"xinzeng01.astc"
},
activityName = "敬业气氛组",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51796
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[138] = {
id = 138,
timeInfo = {
beginTime = {
seconds = 1703174400
},
endTime = {
seconds = 1703779199
},
showEndTime = {
seconds = 1703779199
},
showBeginTime = {
seconds = 1703174400
}
},
labelId = 99,
backgroundUrl = {
"NActivity_BgDUO2_mh.astc"
},
activityName = "梦想星之队",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51797,
51798
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[139] = {
id = 139,
timeInfo = {
beginTime = {
seconds = 1703260800
},
endTime = {
seconds = 1703865599
},
showEndTime = {
seconds = 1703865599
},
showBeginTime = {
seconds = 1703260800
}
},
labelId = 3,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = "星愿免费送",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51799
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[140] = {
id = 140,
timeInfo = {
beginTime = {
seconds = 1703779200
},
endTime = {
seconds = 1704643199
},
showEndTime = {
seconds = 1704643199
},
showBeginTime = {
seconds = 1703779200
}
},
labelId = 8,
activityName = "元旦签到礼",
activityDesc = "小新和你一起过元旦！",
activityUIDetail = "UI_SignIn_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51800
},
showInCenter = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[141] = {
id = 141,
timeInfo = {
beginTime = {
seconds = 1703779200
},
endTime = {
seconds = 1704124799
},
showEndTime = {
seconds = 1704124799
},
showBeginTime = {
seconds = 1703779200
}
},
labelId = 11,
backgroundUrl = {
"yanhuaxiu1227.astc"
},
activityName = "跨年烟花秀",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51801,
51802,
51820
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[142] = {
id = 142,
timeInfo = {
beginTime = {
seconds = 1703865600
},
endTime = {
seconds = 1704211199
},
showEndTime = {
seconds = 1704211199
},
showBeginTime = {
seconds = 1703865600
}
},
labelId = 10,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = [[每日组队礼
星愿免费送]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51803,
51819
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[143] = {
id = 143,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1703779200
},
endTime = {
seconds = 1704643199
},
showEndTime = {
seconds = 1704643199
},
showBeginTime = {
seconds = 1703779200
}
},
labelId = 12,
backgroundUrl = {
"T_TaskCenter_Gift_Bgxiaoxin1_N_A.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "小新幸运站",
activityParam = {
23,
2014
},
activityDesc = "小新表情&动作限时兑换~",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51804,
51805,
51806,
51807,
51808,
51809,
51818
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
23
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
2014
},
platforms = v26
},
[144] = {
id = 144,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1703779200
},
endTime = {
seconds = 1705075199
},
showEndTime = {
seconds = 1705075199
},
showBeginTime = {
seconds = 1703779200
}
},
labelId = 14,
backgroundUrl = {
"manyueyure.astc"
},
activityName = "期待满月见",
activityParam = {
32,
2015
},
activityDesc = "1月12日满月相见吧~",
activityUIDetail = v19,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51810,
51811,
51812,
51813,
51814
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
32
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
2015
},
platforms = v26
},
[145] = {
id = 145,
timeInfo = {
beginTime = {
seconds = 1703779200
},
endTime = {
seconds = 1704383999
},
showEndTime = {
seconds = 1704383999
},
showBeginTime = {
seconds = 1703779200
}
},
labelId = 15,
backgroundUrl = {
"chongfengjingji.astc"
},
activityName = "绝对鹰眼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51817,
51823
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[147] = {
id = 147,
timeInfo = {
beginTime = {
seconds = 1704211200
},
endTime = {
seconds = 1704470399
},
showEndTime = {
seconds = 1704470399
},
showBeginTime = {
seconds = 1704211200
}
},
labelId = 10,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = [[每日组队礼
星愿免费送]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51821
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[148] = {
id = 148,
timeInfo = {
beginTime = {
seconds = 1703779200
},
endTime = {
seconds = 1703951999
},
showEndTime = {
seconds = 1703951999
},
showBeginTime = {
seconds = 1703779200
}
},
labelId = 14,
backgroundUrl = {
"UGCxingheyue.astc"
},
activityName = "千万星合约",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51822
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[150] = {
id = 150,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1703779200
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1703779200
}
},
labelId = 1,
backgroundUrl = {
"UGCgufeng01.astc"
},
jumpId = 14,
activityName = "古风剧王",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[151] = {
id = 151,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1705593600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1705593600
}
},
labelId = 1,
backgroundUrl = {
"UGCgufeng02.astc"
},
jumpId = 14,
activityName = "古风剧王",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[152] = {
id = 152,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1706803199
},
showEndTime = {
seconds = 1706803199
},
showBeginTime = {
seconds = 1706198400
}
},
labelId = 1,
backgroundUrl = {
"UGCgufeng03.astc"
},
jumpId = 14,
activityName = "古风剧王",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[153] = {
id = 153,
timeInfo = {
beginTime = {
seconds = 1703865600
},
endTime = {
seconds = 1705075199
},
showEndTime = {
seconds = 1705075199
},
showBeginTime = {
seconds = 1703865600
}
},
labelId = 15,
backgroundUrl = {
"shituanbiaoqingbao.astc"
},
activityName = "TNT表情包",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51825
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[154] = {
id = 154,
timeInfo = {
beginTime = {
seconds = 1703865600
},
endTime = {
seconds = 1704124799
},
showEndTime = {
seconds = 1704124799
},
showBeginTime = {
seconds = 1703865600
}
},
labelId = 16,
backgroundUrl = {
"tuzikuanian1230.astc"
},
activityName = "福启新岁",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51826
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[155] = {
id = 155,
activityType = "ATDailyColorPaint",
timeInfo = {
beginTime = {
seconds = 1704384000
},
endTime = {
seconds = 1704988799
},
showEndTime = {
seconds = 1704988799
},
showBeginTime = {
seconds = 1704384000
}
},
labelId = 8,
backgroundUrl = {
"T_ColorDaily_Bg_Base_N_A.astc"
},
activityName = "1月12日见",
activityDesc = "冰糕糕时装将于1.12登场【印章祈愿】哦~",
activityUIDetail = "UI_Activity_DailyDrawView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51827,
51850
},
showInCenter = true,
activityNameType = "ANTDailyColorPaint",
titleType = 0,
platforms = v26
},
[156] = {
id = 156,
timeInfo = {
beginTime = {
seconds = 1704384000
},
endTime = {
seconds = 1704988799
},
showEndTime = {
seconds = 1704988799
},
showBeginTime = {
seconds = 1704384000
}
},
labelId = 9,
backgroundUrl = {
"NActivity_BgDUO3_mh.astc"
},
activityName = "躲藏追击",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51829,
51830
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[157] = {
id = 157,
timeInfo = {
beginTime = {
seconds = 1704470400
},
endTime = {
seconds = 1704643199
},
showEndTime = {
seconds = 1704643199
},
showBeginTime = {
seconds = 1704470400
}
},
labelId = 10,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = [[每日组队礼
星愿免费送]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51831
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[158] = {
id = 158,
timeInfo = {
beginTime = {
seconds = 1704643200
},
endTime = {
seconds = 1704988799
},
showEndTime = {
seconds = 1704988799
},
showBeginTime = {
seconds = 1704643200
}
},
labelId = 10,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = [[每日组队礼
星愿免费送]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51832
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[159] = {
id = 159,
labelId = 4,
backgroundUrl = {
"UGCxingheyue.astc"
},
activityName = "世界妙妙屋",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51833
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[160] = {
id = 160,
labelId = 4,
backgroundUrl = {
"UGCxingheyue.astc"
},
activityName = "世界妙妙屋",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51834
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[161] = {
id = 161,
labelId = 4,
backgroundUrl = {
"UGCxingheyue.astc"
},
activityName = "世界妙妙屋",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51835
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[162] = {
id = 162,
labelId = 4,
backgroundUrl = {
"UGCxingheyue.astc"
},
activityName = "世界妙妙屋",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51836
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[163] = {
id = 163,
labelId = 4,
backgroundUrl = {
"UGCxingheyue.astc"
},
activityName = "世界妙妙屋",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51837
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[164] = {
id = 164,
labelId = 4,
backgroundUrl = {
"UGCxingheyue.astc"
},
activityName = "世界妙妙屋",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51838
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[165] = {
id = 165,
labelId = 4,
backgroundUrl = {
"UGCxingheyue.astc"
},
activityName = "世界妙妙屋",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51839
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[166] = {
id = 166,
timeInfo = {
beginTime = {
seconds = 1704384000
},
endTime = {
seconds = 1704988799
},
showEndTime = {
seconds = 1704988799
},
showBeginTime = {
seconds = 1704384000
}
},
labelId = 8,
backgroundUrl = {
"happyred.astc"
},
activityName = "满月线索礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51840,
51841,
51842,
51843,
51844,
51845,
51846
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[167] = {
id = 167,
activityType = "ATCheckInPlan",
timeInfo = {
beginTime = {
seconds = 1704643200
},
endTime = {
seconds = 1707062399
},
showEndTime = {
seconds = 1707062399
},
showBeginTime = {
seconds = 1704643200
}
},
labelId = 12,
backgroundUrl = {
"T_Activity_CheckIn_N_A.astc"
},
activityName = "Toby贴纸簿",
activityUIDetail = "UI_CheckIn_MainView",
activityRuleId = 63,
tagId = 1,
showInCenter = true,
activityNameType = "ANTCheckInPlan",
titleType = 0,
platforms = v26
},
[168] = {
id = 168,
timeInfo = {
beginTime = {
seconds = 1704384000
},
endTime = {
seconds = 1704988799
},
showEndTime = {
seconds = 1704988799
},
showBeginTime = {
seconds = 1704384000
}
},
labelId = 11,
backgroundUrl = {
"seagirl.astc"
},
activityName = "新品献礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51847
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[169] = {
id = 169,
labelId = 6,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = "环游星世界",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51851,
51852,
51853,
51854,
51855,
51856,
51857,
51858,
51859
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[170] = {
id = 170,
labelId = 7,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = "每日闯关",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51860,
51861
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[171] = {
id = 171,
timeInfo = {
beginTime = {
seconds = 1704384000
},
endTime = {
seconds = 1704988799
},
showEndTime = {
seconds = 1704988799
},
showBeginTime = {
seconds = 1704384000
}
},
labelId = 12,
backgroundUrl = {
"xinhuanyouxingshijie1_N_A.astc"
},
activityName = "环游星世界",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51862,
51863,
51864,
51865,
51866,
51867,
51868,
51869,
51870
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[172] = {
id = 172,
timeInfo = {
beginTime = {
seconds = 1704384000
},
endTime = {
seconds = 1704988799
},
showEndTime = {
seconds = 1704988799
},
showBeginTime = {
seconds = 1704384000
}
},
labelId = 13,
backgroundUrl = {
"xinxinghaixunyou1_N_A.astc"
},
activityName = "每日闯关",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51871,
51872
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[173] = {
id = 173,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1704729600
},
endTime = {
seconds = 1705334399
},
showEndTime = {
seconds = 1705334399
},
showBeginTime = {
seconds = 1704729600
}
},
labelId = 5,
backgroundUrl = {
"dayiaoaonuan.astc",
"T_Activity_TestTitle_5.astc"
},
activityName = "大衣袄袄暖",
activityParam = {
24,
2016,
515680
},
activityDesc = "一起穿大衣和袄袄，看冬季风光吧~",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51873,
51874
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2016
},
platforms = v26
},
[174] = {
id = 174,
activityType = "ATChapterTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1706803199
},
showEndTime = {
seconds = 1706803199
},
showBeginTime = {
seconds = **********
}
},
labelId = 6,
backgroundUrl = {
"chaptertask_bg_N_A.astc"
},
activityName = "满月星动礼",
activityParam = v28,
activityUIDetail = "UI_Activity_ChapterTaskView",
tagId = 1,
showInCenter = true,
activityNameType = "ANTChapterInPlan",
activityShopType = v28,
titleType = 0,
platforms = v26
},
[175] = {
id = 175,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1743523199
},
showEndTime = {
seconds = 1743523199
},
showBeginTime = {
seconds = **********
}
},
labelId = 5,
activityName = "满月签到礼",
activityDesc = "快来一起参加满月庆典吧！",
activityUIDetail = "UI_SignIn_Month_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51882
},
showInCenter = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[176] = {
id = 176,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 10,
backgroundUrl = {
"xinpinxianlitoby.astc"
},
activityName = "新品献礼",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51883
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[177] = {
id = 177,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 11,
backgroundUrl = {
"chongfengjingjiyindao.astc"
},
activityName = "冲锋大师",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51884,
51885
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[178] = {
id = 178,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 12,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = [[每日组队礼
星愿免费送]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51886
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[179] = {
id = 179,
labelId = 8,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = [[每日组队礼
星愿免费送]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51887
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[180] = {
id = 180,
labelId = 8,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = [[每日组队礼
星愿免费送]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51888
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[181] = {
id = 181,
activityType = v1,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1705852799
},
showEndTime = {
seconds = 1705852799
},
showBeginTime = {
seconds = **********
}
},
labelId = 8,
backgroundUrl = {
"liugenghongxintu.astc"
},
activityName = "元气星动力",
activityParam = {
25,
2018
},
activityDesc = "刘畊宏星动力第一弹来啦！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51889
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
2018
},
platforms = v26
},
[182] = {
id = 182,
timeInfo = {
beginTime = {
seconds = 1705075200
},
endTime = {
seconds = 1705247999
},
showEndTime = {
seconds = 1705247999
},
showBeginTime = {
seconds = 1705075200
}
},
labelId = 9,
backgroundUrl = {
"tongxingzhengchongci.astc"
},
activityName = "通行证冲刺",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51911,
51912,
51913,
51914
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[183] = {
id = 183,
activityType = "ATNNWelfare",
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1744732799
},
showEndTime = {
seconds = 1744732799
},
showBeginTime = {
seconds = 1706198400
}
},
labelId = 10,
lowVersion = v29,
activityName = "公益满天星",
activityParam = {
1,
2019
},
activityUIDetail = "UI_PublicWelfare",
isInBottom = 1,
activityRuleId = 66,
tagId = 4,
activityTaskGroup = {
52000,
52001,
52002,
52003,
52004,
52005,
52006,
52007,
52008,
52009,
52010,
52011
},
showInCenter = true,
activityNameType = "ANTWelfareAerospaceTechEd",
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
2019
},
platforms = v26
},
[184] = {
id = 184,
timeInfo = {
beginTime = {
seconds = 1704816000
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1704816000
}
},
labelId = 7,
backgroundUrl = {
"NActivity_BgDUO2_mh.astc"
},
activityName = "冲段不停歇",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51908
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[185] = {
id = 185,
timeInfo = {
beginTime = {
seconds = 1704816000
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1704816000
}
},
labelId = 7,
backgroundUrl = {
"xinzeng01.astc"
},
activityName = "段位冲刺周",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51909,
51910
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[186] = {
id = 186,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 9,
backgroundUrl = {
"xinhuanyouxingshijie1_N_A.astc"
},
activityName = "环游星世界",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51915,
51916,
51917,
51918
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[187] = {
id = 187,
timeInfo = {
beginTime = {
seconds = 1705075200
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1705075200
}
},
labelId = 9,
backgroundUrl = {
"wangzhebeijingtu.jpg"
},
activityName = "长安探案礼",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51919
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[188] = {
id = 188,
timeInfo = {
beginTime = {
seconds = 1705593600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1705593600
}
},
labelId = 8,
backgroundUrl = {
"xingmengzhexinxin.astc"
},
activityName = "新品献礼",
activityUIDetail = v16,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51920
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[189] = {
id = 189,
timeInfo = {
beginTime = {
seconds = 1705680000
},
endTime = {
seconds = 1706284799
},
showEndTime = {
seconds = 1706284799
},
showBeginTime = {
seconds = 1705680000
}
},
labelId = 11,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = [[每日组队礼
星愿免费送]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51921
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[190] = {
id = 190,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1705852800
},
endTime = {
seconds = 1706716799
},
showEndTime = {
seconds = 1706716799
},
showBeginTime = {
seconds = 1705852800
}
},
labelId = 5,
backgroundUrl = {
"liugenghongxintu.astc"
},
activityName = "元气不停歇",
activityParam = {
23,
2020
},
activityDesc = "刘畊宏星动力第二弹来啦！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51923,
51922
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
23
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
2020
},
platforms = v26
},
[191] = {
id = 191,
timeInfo = {
beginTime = {
seconds = 1705593600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1705593600
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDUO3_mh.astc"
},
activityName = "放松一下",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51924
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[192] = {
id = 192,
timeInfo = {
beginTime = {
seconds = 1705593600
},
endTime = {
seconds = 1706284799
},
showEndTime = {
seconds = 1706284799
},
showBeginTime = {
seconds = 1705593600
}
},
labelId = 13,
backgroundUrl = {
"xinsaijiyingjie.astc"
},
lowVersion = v29,
activityName = "迎接新赛季",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51925,
51926,
51927,
51928
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[193] = {
id = 193,
timeInfo = {
beginTime = {
seconds = 1705593600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1705593600
}
},
labelId = 7,
backgroundUrl = {
"xinhuanyouxingshijie1_N_A.astc"
},
activityName = "环游星世界",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
51929,
51933,
51930,
51931
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[194] = {
id = 194,
timeInfo = {
beginTime = {
seconds = 1705593600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1705593600
}
},
labelId = 9,
backgroundUrl = {
"fanhua.astc"
},
activityName = "共赏繁花",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51932
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[195] = {
id = 195,
timeInfo = {
beginTime = {
seconds = 1705593600
},
endTime = {
seconds = 1706198399
},
showEndTime = {
seconds = 1706198399
},
showBeginTime = {
seconds = 1705593600
}
},
labelId = 7,
backgroundUrl = {
"NActivity_BgDUO2_mh.astc"
},
activityName = "登录就送",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
51934,
51935
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[196] = {
id = 196,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
showEndTime = {
seconds = 1710431999
},
showBeginTime = {
seconds = 1706198400
}
},
labelId = 9,
backgroundUrl = {
"s2zaomengjihua0312.astc"
},
jumpId = 11,
activityName = "造梦计划",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[197] = {
id = 197,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1706630400
},
endTime = {
seconds = 1708099199
},
showEndTime = {
seconds = 1708099199
},
showBeginTime = {
seconds = 1706630400
}
},
labelId = 99,
activityName = "星舞龙游",
tagId = 1,
activityTaskGroup = {
51936,
51937
},
activityNameType = v22,
titleType = 0,
activityBeginCleanCoin = {
9
},
platforms = v26
},
[198] = {
id = 198,
activityType = "ATLuckyBalloon",
timeInfo = {
beginTime = {
seconds = 1672502400
},
endTime = {
seconds = 1674748799
},
showEndTime = {
seconds = 1674748799
},
showBeginTime = {
seconds = 1672502400
}
},
labelId = 99,
activityName = "星灯许愿",
activityParam = {
1
},
activityDesc = "幸运气球",
activityUIDetail = "UI_LuckyBall_MainView",
isInBottom = 1,
activityRuleId = 92,
tagId = 1,
activityTaskGroup = {
51939,
51940
},
showInCenter = true,
activityNameType = "ANTLuckyBalloon",
titleType = 0,
activityBeginCleanCoin = {
10
},
platforms = v26
},
[199] = {
id = 199,
activityType = "ATTimeLimitedCheckIn",
timeInfo = {
beginTime = {
seconds = 1709222400
},
endTime = {
seconds = 1710518399
},
showEndTime = {
seconds = 1710518399
},
showBeginTime = {
seconds = 1709222400
}
},
labelId = 99,
activityName = "开学返利",
tagId = 1,
activityNameType = "ANTimeLimitedCheckIn",
titleType = 0,
platforms = v26
},
[200] = {
id = 200,
activityType = "ATSquad",
timeInfo = {
beginTime = {
seconds = 1705766400
},
endTime = {
seconds = 1705939199
},
showEndTime = {
seconds = 1705939199
},
showBeginTime = {
seconds = 1705766400
}
},
labelId = 5,
lowVersion = v29,
activityName = "赶海小队",
activityParam = {
2
},
activityUIDetail = "UI_Activity_MultiplayerSquad_View",
activityRuleId = 64,
tagId = 1,
activityTaskGroup = {
52012,
52013
},
showInCenter = true,
activityNameType = "ANTSquad",
isHideMainBackground = true,
titleType = 0,
platforms = v26
},
[201] = {
id = 201,
activityType = "ATSingleTask",
timeInfo = {
beginTime = {
seconds = 1705939200
},
endTime = {
seconds = 1705939200
},
showEndTime = {
seconds = 1705939200
},
showBeginTime = {
seconds = 1705939200
}
},
labelId = 3,
lowVersion = v29,
activityName = "星动推荐官",
activityUIDetail = "UI_Activity_Recommend_View",
tagId = 4,
activityTaskGroup = {
52014
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[202] = {
id = 202,
timeInfo = {
beginTime = {
seconds = 1705939200
},
endTime = {
seconds = 1706543999
},
showEndTime = {
seconds = 1706543999
},
showBeginTime = {
seconds = 1705939200
}
},
labelId = 5,
backgroundUrl = {
"xiaojuzi_N_A.astc"
},
lowVersion = v29,
activityName = [[迎版本更新
庆正版联动]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
52015,
52016
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[203] = {
id = 203,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1706803199
},
showEndTime = {
seconds = 1706803199
},
showBeginTime = {
seconds = 1706198400
}
},
labelId = 2,
backgroundUrl = {
"fengkuangxingqishi_N_A.astc"
},
lowVersion = v29,
activityName = "疯狂星骑士",
activityUIDetail = v16,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52017
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[204] = {
id = 204,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1706457599
},
showEndTime = {
seconds = 1706457599
},
showBeginTime = {
seconds = 1706198400
}
},
labelId = 3,
backgroundUrl = {
"xunzhaoxingdazi_N_A.astc"
},
lowVersion = v29,
activityName = "搭子专属礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
52018,
52019
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[205] = {
id = 205,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1735142399
},
showEndTime = {
seconds = 1735142399
},
showBeginTime = {
seconds = 1706198400
},
validDay = 15
},
labelId = 12,
backgroundUrl = {
"TNTpicture.astc"
},
lowVersion = v29,
activityName = "初星代言人",
activityParam = {
24,
2013,
550010
},
activityDesc = "时代少年团专属赠礼 限时兑换中",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52020,
52021
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2013
},
platforms = v26
},
[206] = {
id = 206,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1730995199
},
showEndTime = {
seconds = 1785427199
},
showBeginTime = {
seconds = 1706198400
},
validDay = 15,
endWithValidDay = true
},
labelId = 10,
lowVersion = v29,
activityName = "庆典签到",
activityUIDetail = "UI_FestivalCheckIn_MainView",
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52022
},
showInCenter = true,
slapFace = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[207] = {
id = 207,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1730995199
},
showEndTime = {
seconds = 1785427199
},
showBeginTime = {
seconds = 1706198400
},
validDay = 7,
endWithValidDay = true
},
labelId = 11,
backgroundUrl = {
"T_Activity_Bg_GGADUO6.astc"
},
lowVersion = v29,
activityName = "星宝来啦",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52023
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[208] = {
id = 208,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1730995199
},
showEndTime = {
seconds = 1785427199
},
showBeginTime = {
seconds = 1706198400
},
validDay = 7,
endWithValidDay = true
},
labelId = 11,
backgroundUrl = {
"T_Activity_Bg_GGADUO6.astc"
},
lowVersion = v29,
activityName = "星宝来啦",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52024
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[209] = {
id = 209,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1707062399
},
showEndTime = {
seconds = 1707062399
},
showBeginTime = {
seconds = 1706198400
}
},
labelId = 4,
backgroundUrl = {
"xindeshangxinzhong.astc",
"T_Activity_TestTitle_5.astc"
},
lowVersion = v29,
activityName = "高频上新中",
activityParam = {
24,
2021,
550035
},
activityDesc = "惊喜不停歇，一起领全新时装吧！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
52025,
52026,
52027
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2021
},
platforms = v26
},
[210] = {
id = 210,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1707407999
},
showEndTime = {
seconds = 1707407999
},
showBeginTime = {
seconds = 1706198400
}
},
labelId = 2,
backgroundUrl = {
"bainianxingbaoxiu.astc"
},
lowVersion = v29,
activityName = "百变星宝秀",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
52028,
52029
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[211] = {
id = 211,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1706803199
},
showEndTime = {
seconds = 1706803199
},
showBeginTime = {
seconds = 1706198400
}
},
labelId = 4,
backgroundUrl = {
"chuangguantiaozhanxinban.astc"
},
lowVersion = v29,
activityName = "星座闯关",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52030,
52031,
52032
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[212] = {
id = 212,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1706151600
},
endTime = {
seconds = 1707753599
},
showEndTime = {
seconds = 1707753599
},
showBeginTime = {
seconds = 1706151600
}
},
labelId = 1,
backgroundUrl = {
"xinchun01.astc"
},
jumpId = 15,
activityName = "画龙点星",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[213] = {
id = 213,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1707753600
},
endTime = {
seconds = 1708963199
},
showEndTime = {
seconds = 1708963199
},
showBeginTime = {
seconds = 1707753600
}
},
labelId = 1,
backgroundUrl = {
"xinchun02.astc"
},
jumpId = 15,
activityName = "画龙点星",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[214] = {
id = 214,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1708963200
},
endTime = {
seconds = 1709481599
},
showEndTime = {
seconds = 1709481599
},
showBeginTime = {
seconds = 1708963200
}
},
labelId = 1,
backgroundUrl = {
"xinchun03.astc"
},
jumpId = 15,
activityName = "画龙点星",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[215] = {
id = 215,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1709481600
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1709481600
}
},
labelId = 1,
backgroundUrl = {
"xinchun04.astc"
},
jumpId = 15,
activityName = "画龙点星",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[216] = {
id = 216,
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1706284799
},
showEndTime = {
seconds = 1706284799
},
showBeginTime = {
seconds = 1706198400
}
},
labelId = 1,
backgroundUrl = {
"caihonghongbao.astc"
},
lowVersion = v29,
activityName = "星钻亿起分",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
52033
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[217] = {
id = 217,
activityType = "ATSquad",
timeInfo = {
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1706803199
},
showEndTime = {
seconds = 1706803199
},
showBeginTime = {
seconds = 1706198400
}
},
labelId = 5,
lowVersion = v29,
activityName = "赶海小队",
activityParam = {
2
},
activityUIDetail = "UI_Activity_MultiplayerSquad_View",
activityRuleId = 64,
tagId = 1,
activityTaskGroup = {
52034,
52035
},
showInCenter = true,
activityNameType = "ANTSquad",
isHideMainBackground = true,
titleType = 0,
platforms = v26
},
[218] = {
id = 218,
activityType = "ATSpringRedPacket",
timeInfo = {
beginTime = {
seconds = 1731340800
},
endTime = {
seconds = 1732636799
},
showEndTime = {
seconds = 1732636799
},
showBeginTime = {
seconds = 1707235200
}
},
labelId = 8,
lowVersion = v30,
activityName = [[红包道具
大派送]],
activityDesc = "春节红包",
activityUIDetail = "UI_SpringRedPacket_Main",
activityRuleId = 106,
tagId = 4,
showInCenter = true,
activityNameType = "ANTSpringRedPacket",
titleType = 0,
platforms = v26
},
[219] = {
id = 219,
activityType = "ATSprintBlessingCollection",
timeInfo = {
beginTime = {
seconds = 1707235200
},
endTime = {
seconds = 1707926399
},
showEndTime = {
seconds = 1707926399
},
showBeginTime = {
seconds = 1707235200
}
},
labelId = 7,
lowVersion = v30,
activityName = "春节集福",
activityUIDetail = "UI_CollectionFu_Main",
activityRuleId = 107,
tagId = 4,
showInCenter = true,
activityNameType = "ANTSpringBlessingCollection",
platforms = v26
},
[667] = {
id = 667,
activityType = "ATTeamRank",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1708012799
},
showEndTime = {
seconds = 1708012799
},
showBeginTime = {
seconds = **********
}
},
labelId = 99,
lowVersion = v30,
activityName = "排位双重礼",
activityDesc = "打排位不掉星及额外加分活动",
activityUIDetail = "UI_TeamActi_MainView",
tagId = 1,
activityTaskGroup = {
52040
},
showInCenter = true,
activityNameType = "ANTTeamRank",
titleType = 0,
platforms = v26
},
[240] = {
id = 240,
activityType = "ATSpringPray",
timeInfo = {
beginTime = {
seconds = 1707839999
},
endTime = {
seconds = 1708185600
},
showEndTime = {
seconds = 1708185600
},
showBeginTime = {
seconds = 1707839999
}
},
activityName = "春节财神祈福",
activityNameType = "ANTSpringPray",
platforms = v26
},
[250] = {
id = 250,
timeInfo = {
beginTime = {
seconds = 1706803200
},
endTime = {
seconds = 1743523199
},
showEndTime = {
seconds = 1743523199
},
showBeginTime = {
seconds = 1706803200
}
},
labelId = 17,
lowVersion = v29,
activityName = "迎迪迦签到",
activityDesc = "快来和奥特曼一起玩耍吧！",
activityUIDetail = "UI_SignIn_NewaYear_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
52242
},
showInCenter = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[251] = {
id = 251,
timeInfo = {
beginTime = {
seconds = 1706803200
},
endTime = {
seconds = 1707407999
},
showEndTime = {
seconds = 1707407999
},
showBeginTime = {
seconds = 1706803200
}
},
labelId = 11,
backgroundUrl = {
"newruixing.astc"
},
lowVersion = v29,
activityName = "恰逢小年礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
52236,
52252,
52253
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[252] = {
id = 252,
timeInfo = {
beginTime = {
seconds = 1706889600
},
endTime = {
seconds = 1707494399
},
showEndTime = {
seconds = 1707494399
},
showBeginTime = {
seconds = 1706889600
}
},
labelId = 12,
backgroundUrl = {
"baobaolongnailong.astc"
},
lowVersion = v29,
activityName = "奶龙新品礼",
activityUIDetail = v16,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52237
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[253] = {
id = 253,
timeInfo = {
beginTime = {
seconds = 1706803200
},
endTime = {
seconds = 1707407999
},
showEndTime = {
seconds = 1707407999
},
showBeginTime = {
seconds = 1706803200
}
},
labelId = 11,
backgroundUrl = {
"xianyiren.astc"
},
lowVersion = v29,
activityName = "推理大师赏",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52238,
52239
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[254] = {
id = 254,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1706976000
},
endTime = {
seconds = 1707839999
},
showEndTime = {
seconds = 1707839999
},
showBeginTime = {
seconds = 1706976000
}
},
labelId = 5,
backgroundUrl = {
"yaboxin.astc"
},
lowVersion = v29,
activityName = "新年爆发财",
activityParam = {
25,
2022
},
activityDesc = "联名发财套装来袭！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52240,
52243
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
2022
},
platforms = v26
},
[255] = {
id = 255,
timeInfo = {
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1748447999
},
showEndTime = {
seconds = 1748447999
},
showBeginTime = {
seconds = 1747324800
}
},
labelId = 4,
activityName = [[多图倒计时
测试]],
activityParam = {
6
},
activityDesc = "完成下列任务即可领取奖励",
activityUIDetail = "UI_Countdown_Main",
tagId = 1,
activityTaskGroup = {
52244,
52245,
52246,
52247,
52248,
52249,
52250,
52251
},
showInCenter = true,
platforms = v26
},
[256] = {
id = 256,
timeInfo = {
beginTime = {
seconds = 1672502400
},
endTime = {
seconds = 1674748799
},
showEndTime = {
seconds = 1674748799
},
showBeginTime = {
seconds = 1672502400
}
},
labelId = 2,
backgroundUrl = {
"0129.astc"
},
lowVersion = v29,
activityName = [[星家园礼
创作送]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52241
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[257] = {
id = 257,
timeInfo = {
beginTime = {
seconds = 1706803200
},
endTime = {
seconds = 1707407999
},
showEndTime = {
seconds = 1707407999
},
showBeginTime = {
seconds = 1706803200
}
},
labelId = 4,
backgroundUrl = {
"chuangguantiaozhanxinban.astc"
},
lowVersion = v29,
activityName = "星座闯关",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52254,
52255,
52256
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[258] = {
id = 258,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1707062400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1707062400
}
},
labelId = 1,
backgroundUrl = {
"eelaile1.astc"
},
jumpId = 16,
activityName = "鹅鹅来了",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[259] = {
id = 259,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1709827199
},
showEndTime = {
seconds = 1709827199
},
showBeginTime = {
seconds = 1708617600
}
},
labelId = 1,
backgroundUrl = {
"eelaile2.astc"
},
jumpId = 16,
activityName = "鹅鹅来了",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[260] = {
id = 260,
activityType = v0,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1710431999
},
showEndTime = {
seconds = 1710431999
},
showBeginTime = {
seconds = **********
}
},
labelId = 1,
backgroundUrl = {
"eelaile3.astc"
},
jumpId = 16,
activityName = "鹅鹅来了",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[261] = {
id = 261,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1711036799
},
showEndTime = {
seconds = 1711036799
},
showBeginTime = {
seconds = 1710432000
}
},
labelId = 1,
backgroundUrl = {
"eelaile4.astc"
},
jumpId = 16,
activityName = "鹅鹅来了",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[262] = {
id = 262,
timeInfo = {
beginTime = {
seconds = 1706716800
},
endTime = {
seconds = 1706889599
},
showEndTime = {
seconds = 1706889599
},
showBeginTime = {
seconds = 1706716800
}
},
labelId = 6,
backgroundUrl = {
"xbxxg001_N_A.astc"
},
lowVersion = v29,
activityName = "星宝相信光",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
52257
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[263] = {
id = 263,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1706889600
},
endTime = {
seconds = 1707580799
},
showEndTime = {
seconds = 1707580799
},
showBeginTime = {
seconds = 1706889600
}
},
labelId = 12,
backgroundUrl = {
"newdragonbao.astc"
},
jumpId = 92,
activityName = [[龙年限定
登录全部领]],
activityUIDetail = v15,
tagId = 1,
showInCenter = true,
activityNameType = v20,
isHideMainBackground = true,
titleType = 0,
platforms = v26
},
[264] = {
id = 264,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1706889600
},
endTime = {
seconds = 1707580799
},
showEndTime = {
seconds = 1707580799
},
showBeginTime = {
seconds = 1706889600
}
},
labelId = 12,
backgroundUrl = {
"newdragonbao.astc"
},
jumpId = 93,
activityName = [[龙年限定
登录全部领]],
activityUIDetail = v15,
tagId = 1,
showInCenter = true,
activityNameType = v20,
isHideMainBackground = true,
titleType = 0,
platforms = v26
},
[265] = {
id = 265,
timeInfo = {
beginTime = {
seconds = 1707062400
},
endTime = {
seconds = 1707580799
},
showEndTime = {
seconds = 1707580799
},
showBeginTime = {
seconds = 1707062400
}
},
labelId = 6,
backgroundUrl = {
"banbengengxin0205.astc"
},
lowVersion = v30,
activityName = "版本更新礼",
activityUIDetail = v16,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52258
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[266] = {
id = 266,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1707148800
},
endTime = {
seconds = 1707753599
},
showEndTime = {
seconds = 1707753599
},
showBeginTime = {
seconds = 1707148800
}
},
labelId = 1,
backgroundUrl = {
"songyuqi.astc",
"T_Activity_TestTitle_5.astc"
},
lowVersion = v30,
activityName = "雨琦试衣间",
activityParam = {
24,
2023,
552108
},
activityDesc = "新赛季新时装，雨琦元梦等你",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52259,
52260,
52261
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2023
},
platforms = v26
},
[267] = {
id = 267,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1707235200
},
endTime = {
seconds = 1707839999
},
showEndTime = {
seconds = 1707839999
},
showBeginTime = {
seconds = 1707235200
}
},
labelId = 2,
backgroundUrl = {
"fanchengcheng.astc",
"T_Activity_TestTitle_5.astc"
},
lowVersion = v30,
activityName = "丞丞直播间",
activityParam = {
24,
2024,
552118
},
activityDesc = "丞丞元梦就位，星搭子马上发车",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52262,
52263,
52264
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2024
},
platforms = v26
},
[268] = {
id = 268,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1707321600
},
endTime = {
seconds = 1707926399
},
showEndTime = {
seconds = 1707926399
},
showBeginTime = {
seconds = 1707321600
}
},
labelId = 3,
backgroundUrl = {
"weidaxun.astc",
"T_Activity_TestTitle_5.astc"
},
lowVersion = v30,
activityName = "大勋玉米地",
activityParam = {
24,
2025,
552128
},
activityDesc = "经营大勋玉米地，新年来串门",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52265,
52266,
52267
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2025
},
platforms = v26
},
[269] = {
id = 269,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1707148800
},
endTime = {
seconds = 1707839999
},
showEndTime = {
seconds = 1707839999
},
showBeginTime = {
seconds = 1707148800
}
},
labelId = 9,
backgroundUrl = {
"huanxinzhuang.astc"
},
lowVersion = v30,
activityName = "迎春大焕新",
activityParam = {
23,
2026
},
activityDesc = "热门动作免费领取啦~",
activityUIDetail = v19,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
52268,
52269,
52270,
52271
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
23
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
2026
},
platforms = v26
},
[270] = {
id = 270,
activityType = "ATUseItemShare",
timeInfo = {
beginTime = {
seconds = 1748311200
},
endTime = {
seconds = 1748447999
},
showEndTime = {
seconds = 1748447999
},
showBeginTime = {
seconds = 1748311200
}
},
labelId = 0,
backgroundUrl = {
"Newseagirl.astc"
},
lowVersion = v30,
activityName = "星运红包",
activityParam = {
200034,
50
},
rewardInfo = {
{
itemId = 200035,
itemNum = 1
},
{
itemId = 200036,
itemNum = 1
},
{
itemId = 200037,
itemNum = 1
},
{
itemId = 2,
itemNum = 12
},
{
itemId = 200038,
itemNum = 1
},
{
itemId = 200039,
itemNum = 1
},
{
itemId = 200040,
itemNum = 1
},
{
itemId = 200041,
itemNum = 1
}
},
activityUIDetail = "UI_StarLuck_View",
isInBottom = 1,
activityRuleId = 108,
tagId = 0,
activityTaskGroup = {
52231,
56047,
56048,
56049,
56050,
56051,
56052,
56053
},
slapFace = true,
activityNameType = "ANTStarLucky",
titleType = 0,
platforms = v26
},
[271] = {
id = 271,
timeInfo = {
beginTime = {
seconds = 1707148800
},
endTime = {
seconds = 1707407999
},
showEndTime = {
seconds = 1707407999
},
showBeginTime = {
seconds = 1707148800
}
},
labelId = 13,
backgroundUrl = {
"zuiqiangdiliugan.astc"
},
lowVersion = v30,
activityName = "最强第六感",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
52232,
52233,
52234,
52235
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[272] = {
id = 272,
activityType = "ATLuckyStar",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 9,
lowVersion = v30,
activityName = "福星手账簿",
activityParam = {
1,
2040,
1
},
activityUIDetail = "UI_HandAccount_EntryView",
tagId = 1,
activityTaskGroup = {
56054
},
showInCenter = true,
clientParams = {
"401080",
"1",
"6",
"30",
"320022",
"1",
"620108",
"1"
},
platforms = v26
},
[273] = {
id = 273,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 1,
lowVersion = v30,
activityName = "回归签到",
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
56055
},
activityNameType = "ANTReturningTask",
titleType = 0,
platforms = v26
},
[2731] = {
id = 2731,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 1,
lowVersion = v30,
activityName = "回归签到",
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
560551
},
activityNameType = "ANTReturningTask",
titleType = 0,
platforms = v26
},
[274] = {
id = 274,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 2,
lowVersion = v30,
activityName = "回归特权",
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
56056
},
activityNameType = "ANTReturningTask",
titleType = 0,
platforms = v26
},
[275] = {
id = 275,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
lowVersion = v30,
activityName = "回归任务",
activityParam = {
2041
},
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
56057,
56058,
56059
},
activityNameType = "ANTReturningTask",
clientParams = {
"2,18",
"3,10",
"4,10000"
},
titleType = 0,
activityBeginCleanCoin = {
2041
},
platforms = v26
},
[2751] = {
id = 2751,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
lowVersion = v30,
activityName = "回归任务",
activityParam = {
2041
},
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
560571,
560581,
560591
},
activityNameType = "ANTReturningTask",
clientParams = {
"2,18",
"3,10",
"4,10000"
},
titleType = 0,
activityBeginCleanCoin = {
2041
},
platforms = v26
},
[2752] = {
id = 2752,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
lowVersion = v30,
activityName = "回归任务",
activityParam = {
2041
},
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
560572,
560582,
560592
},
activityNameType = "ANTReturningTask",
clientParams = {
"2,18",
"3,10",
"4,10000"
},
titleType = 0,
activityBeginCleanCoin = {
2041
},
platforms = v26
},
[2753] = {
id = 2753,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
lowVersion = v30,
activityName = "回归任务",
activityParam = {
2041
},
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
560573,
560583,
560593
},
activityNameType = "ANTReturningTask",
clientParams = {
"2,18",
"3,10",
"4,10000"
},
titleType = 0,
activityBeginCleanCoin = {
2041
},
platforms = v26
},
[2754] = {
id = 2754,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
lowVersion = v30,
activityName = "回归任务",
activityParam = {
2041
},
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
560574,
560584,
560594
},
activityNameType = "ANTReturningTask",
clientParams = {
"2,18",
"3,10",
"4,10000"
},
titleType = 0,
activityBeginCleanCoin = {
2041
},
platforms = v26
},
[2755] = {
id = 2755,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
lowVersion = v30,
activityName = "回归任务三期1",
activityParam = {
2041
},
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
560575,
560585,
560595
},
activityNameType = "ANTReturningTask",
clientParams = {
"2,18",
"3,10",
"4,10000"
},
titleType = 0,
activityBeginCleanCoin = {
2041
},
platforms = v26
},
[2756] = {
id = 2756,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
lowVersion = v30,
activityName = "回归任务三期2",
activityParam = {
2041
},
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
560576,
560586,
560596
},
activityNameType = "ANTReturningTask",
clientParams = {
"2,18",
"3,10",
"4,10000"
},
titleType = 0,
activityBeginCleanCoin = {
2041
},
platforms = v26
},
[2757] = {
id = 2757,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
lowVersion = v30,
activityName = "回归任务三期3",
activityParam = {
2041
},
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
560577,
560587,
560597
},
activityNameType = "ANTReturningTask",
clientParams = {
"2,18",
"3,10",
"4,10000"
},
titleType = 0,
activityBeginCleanCoin = {
2041
},
platforms = v26
},
[2758] = {
id = 2758,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
lowVersion = v30,
activityName = "回归任务四期",
activityParam = {
3542,
3542
},
activityRuleId = 109,
tagId = 1,
activityTaskGroup = {
560578,
560588,
560598
},
activityNameType = "ANTReturningTask",
clientParams = {
"2,18",
"3,10",
"4,10000"
},
titleType = 0,
activityBeginCleanCoin = {
3542
},
platforms = v26
},
[276] = {
id = 276,
timeInfo = {
beginTime = {
seconds = 1707321600
},
endTime = {
seconds = 1707407999
},
showEndTime = {
seconds = 1707407999
},
showBeginTime = {
seconds = 1707321600
}
},
labelId = 5,
backgroundUrl = {
"dijiurensheng.astc"
},
lowVersion = v30,
activityName = [[游玩地图
第九人生]],
activityUIDetail = v16,
tagId = 4,
activityTaskGroup = {
56060
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[277] = {
id = 277,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1708012799
},
showEndTime = {
seconds = 1708012799
},
showBeginTime = {
seconds = **********
}
},
labelId = 16,
backgroundUrl = {
"hualitaoz.astc"
},
lowVersion = v30,
activityName = "表演时间到",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56061,
56062
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[278] = {
id = 278,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1708012799
},
showEndTime = {
seconds = 1708012799
},
showBeginTime = {
seconds = **********
}
},
labelId = 11,
backgroundUrl = {
"guaiealv.astc"
},
lowVersion = v30,
activityName = [[乖鹅阿绿
终相聚]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56063,
56064,
56065,
56066,
56067
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[279] = {
id = 279,
timeInfo = {
beginTime = {
seconds = 1706716800
},
endTime = {
seconds = 1706889599
},
showEndTime = {
seconds = 1706889599
},
showBeginTime = {
seconds = 1706716800
}
},
labelId = 4,
backgroundUrl = {
"guaiealv.astc"
},
lowVersion = v30,
activityName = [[乖鹅阿绿
终相聚]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56068,
56069,
56070,
56071,
56072
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[280] = {
id = 280,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 13,
backgroundUrl = {
"bainianxingbaoxiu.astc"
},
lowVersion = v30,
activityName = "百变星宝秀",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56073,
56074
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[281] = {
id = 281,
timeInfo = {
beginTime = {
seconds = 1707840000
},
endTime = {
seconds = 1708444799
},
showEndTime = {
seconds = 1708444799
},
showBeginTime = {
seconds = 1707840000
}
},
labelId = 10,
backgroundUrl = {
"guofeng.astc"
},
lowVersion = v30,
activityName = "最好星搭子",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56075
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[282] = {
id = 282,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1708012799
},
showEndTime = {
seconds = 1708012799
},
showBeginTime = {
seconds = **********
}
},
labelId = 13,
backgroundUrl = {
"chuangguantiaozhanxinban.astc"
},
lowVersion = v30,
activityName = "星座闯关",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56076,
56077,
56092
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[283] = {
id = 283,
timeInfo = {
beginTime = {
seconds = 1742227200
},
endTime = {
seconds = 1743523199
},
showEndTime = {
seconds = 1743523199
},
showBeginTime = {
seconds = 1742227200
}
},
labelId = 8,
lowVersion = v30,
activityName = "新春签到",
activityDesc = "喜迎新春，开心过大年！",
activityUIDetail = "UI_SpringCheckIn_MainView",
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56078
},
showInCenter = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[284] = {
id = 284,
activityType = v2,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1707839999
},
showEndTime = {
seconds = 1707839999
},
showBeginTime = {
seconds = **********
}
},
labelId = 12,
backgroundUrl = {
"yuanmengwutai1.astc",
"T_Activity_TestTitle_5.astc"
},
lowVersion = v30,
activityName = "元梦大舞台",
activityParam = {
24,
2027,
556192
},
activityDesc = "新春时装后续将上架云朵币商城~",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56079,
56080
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2027
},
platforms = v26
},
[285] = {
id = 285,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1707840000
},
endTime = {
seconds = 1708271999
},
showEndTime = {
seconds = 1708271999
},
showBeginTime = {
seconds = 1707840000
}
},
labelId = 12,
backgroundUrl = {
"yuanmengwutai2.astc",
"T_Activity_TestTitle_5.astc"
},
lowVersion = v30,
activityName = "元梦大舞台",
activityParam = {
24,
2028,
556203
},
activityDesc = "新春时装后续将上架云朵币商城~",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56081,
56082,
56083
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2028
},
platforms = v26
},
[286] = {
id = 286,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1708272000
},
endTime = {
seconds = 1708703999
},
showEndTime = {
seconds = 1708703999
},
showBeginTime = {
seconds = 1708272000
}
},
labelId = 12,
backgroundUrl = {
"yuanmengwutai3.astc",
"T_Activity_TestTitle_5.astc"
},
lowVersion = v30,
activityName = "元梦大舞台",
activityParam = {
24,
2029,
556213
},
activityDesc = "新春时装后续将上架云朵币商城~",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56084,
56085,
56086
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 1,
activityBeginCleanCoin = {
2029
},
platforms = v26
},
[287] = {
id = 287,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1707580799
},
showEndTime = {
seconds = 1707580799
},
showBeginTime = {
seconds = **********
}
},
labelId = 14,
backgroundUrl = {
"mainianhuo.astc"
},
lowVersion = v30,
activityName = "过个元梦年",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56093,
56087
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[288] = {
id = 288,
timeInfo = {
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1707494400
}
},
labelId = 15,
backgroundUrl = {
"baohongbao.astc"
},
lowVersion = v30,
activityName = "龙年行大运",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56088,
56089,
56090,
56091
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[289] = {
id = 289,
activityType = "ATSpringFestivalCeremony",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 1,
lowVersion = v30,
activityName = "新春庆典",
activityUIDetail = "UI_NewNavigation_New",
tagId = 1,
showInCenter = true,
activityNameType = "ANTSpringFestivalCeremony",
platforms = v26
},
[290] = {
id = 290,
activityType = "ATScratchOffTickets",
timeInfo = {
beginTime = {
seconds = 1707840000
},
endTime = {
seconds = 1709049599
},
showEndTime = {
seconds = 1709049599
},
showBeginTime = {
seconds = 1707840000
}
},
labelId = 99,
lowVersion = v30,
activityName = "福运刮一刮",
activityParam = {
50001,
50002,
1
},
activityRuleId = 111,
tagId = 1,
activityNameType = "ANTScratchOffTickets",
titleType = 1,
platforms = v26
},
[291] = {
id = 291,
activityType = v0,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1707494399
},
showEndTime = {
seconds = 1707494399
},
showBeginTime = {
seconds = **********
}
},
labelId = 1,
backgroundUrl = {
"1newdijiurensheng.astc"
},
jumpId = 17,
lowVersion = v30,
activityName = [[观看视频
第九人生]],
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[292] = {
id = 292,
timeInfo = {
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = 1714492799
},
showEndTime = {
seconds = 1714492799
},
showBeginTime = {
seconds = 1707494400
},
validDay = 7
},
labelId = 1,
backgroundUrl = {
"newruixing.astc"
},
lowVersion = v30,
activityName = "幸运新人礼",
activityUIDetail = v16,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56094
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[294] = {
id = 294,
timeInfo = {
beginTime = {
seconds = 1708012800
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1708012800
}
},
labelId = 1,
backgroundUrl = {
"xinhuanyouxingshijie1_N_A.astc"
},
lowVersion = v30,
activityName = "环游星世界",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56118,
56119,
56120,
56121
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[295] = {
id = 295,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1708012800
},
endTime = {
seconds = 1708876799
},
showEndTime = {
seconds = 1708876799
},
showBeginTime = {
seconds = 1708012800
}
},
labelId = 9,
backgroundUrl = {
"aoteman0212.astc"
},
lowVersion = v30,
activityName = "英雄集结礼",
activityParam = {
32,
2030
},
activityDesc = "赛罗泽塔，前来报到！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56100,
56101,
56102
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
32
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
2030
},
platforms = v26
},
[296] = {
id = 296,
activityType = "ATDailyColorPaint",
timeInfo = {
beginTime = {
seconds = 1708012800
},
endTime = {
seconds = 1708876799
},
showEndTime = {
seconds = 1708876799
},
showBeginTime = {
seconds = 1708012800
}
},
labelId = 10,
backgroundUrl = {
"nnnewtuzi.astc"
},
lowVersion = v30,
activityName = "胜利队集结",
activityDesc = "世界的和平，就由大家一起守护！",
activityUIDetail = "UI_Activity_DailyDrawView_VictoryTeam",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56110,
56111
},
showInCenter = true,
activityNameType = "ANTDailyColorPaint",
titleType = 0,
platforms = v26
},
[297] = {
id = 297,
timeInfo = {
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1743523199
},
showEndTime = {
seconds = 1743523199
},
showBeginTime = {
seconds = 1708617600
}
},
labelId = 10,
lowVersion = v30,
activityName = "英雄签到礼",
activityDesc = "快来和奥特曼一起玩耍吧！",
activityUIDetail = "UI_SignIn_NewaYear_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56112
},
showInCenter = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[298] = {
id = 298,
timeInfo = {
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1709827199
},
showEndTime = {
seconds = 1709827199
},
showBeginTime = {
seconds = 1708617600
}
},
labelId = 7,
backgroundUrl = {
"bainianxingbaoxiu.astc"
},
lowVersion = v30,
activityName = "百变星宝秀",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56113,
56114
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[300] = {
id = 300,
activityType = "ATUltramanTheme",
timeInfo = {
beginTime = {
seconds = 1708653600
},
endTime = {
seconds = 1710691199
},
showEndTime = {
seconds = 1710691199
},
showBeginTime = {
seconds = 1708653600
}
},
activityName = "光之复苏",
activityParam = {
1012,
2001
},
slapFace = true,
activityNameType = "ANTUltramanTheme",
platforms = v26
},
[301] = {
id = 301,
timeInfo = {
beginTime = {
seconds = 1708653600
},
endTime = {
seconds = 1710691199
},
showEndTime = {
seconds = 1710691199
},
showBeginTime = {
seconds = 1708653600
}
},
labelId = 99,
activityName = "光之任务",
activityUIDetail = "UI_Activity_RevivalOfLight_Task",
tagId = 1,
activityTaskGroup = {
56130,
56131
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[302] = {
id = 302,
activityType = "ATUltramanThemeTeam",
timeInfo = {
beginTime = {
seconds = 1709258400
},
endTime = {
seconds = 1709827199
},
showEndTime = {
seconds = 1709827199
},
showBeginTime = {
seconds = 1709258400
}
},
labelId = 99,
activityName = "奥特曼集结",
activityParam = {
1013,
30
},
activityUIDetail = "UI_Ultraman_Main",
tagId = 1,
activityTaskGroup = {
56132,
56133
},
showInCenter = true,
platforms = v26
},
[310] = {
id = 310,
timeInfo = {
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1708790399
},
showEndTime = {
seconds = 1708790399
},
showBeginTime = {
seconds = 1708444800
}
},
labelId = 1,
backgroundUrl = {
"yuanxiaobg.astc"
},
lowVersion = v30,
activityName = [[元宵节登录
领稀有时装]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56150
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[311] = {
id = 311,
timeInfo = {
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1708876799
},
showEndTime = {
seconds = 1708876799
},
showBeginTime = {
seconds = 1708704000
}
},
labelId = 2,
backgroundUrl = {
"wanhuibg_1.astc"
},
lowVersion = v30,
activityName = [[圆圆满满
喜闹元宵]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56151,
56152,
56153,
56162,
56163
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[312] = {
id = 312,
timeInfo = {
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1709222399
},
showEndTime = {
seconds = 1709222399
},
showBeginTime = {
seconds = 1708617600
}
},
labelId = 6,
backgroundUrl = {
"shangjin.astc"
},
lowVersion = v30,
activityName = "畅游玩耍礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56154,
56155,
56156,
56157
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[313] = {
id = 313,
timeInfo = {
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1709222399
},
showEndTime = {
seconds = 1709222399
},
showBeginTime = {
seconds = 1708617600
}
},
labelId = 1,
backgroundUrl = {
"xinhuanyouxingshijie1_N_A.astc"
},
lowVersion = v30,
activityName = "环游星世界",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56158,
56159,
56160,
56161
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[314] = {
id = 314,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1708531200
},
endTime = {
seconds = 1709827199
},
showEndTime = {
seconds = 1709827199
},
showBeginTime = {
seconds = 1708531200
}
},
labelId = 1,
backgroundUrl = {
"Xiaohs01.astc"
},
jumpId = 18,
lowVersion = v30,
activityName = "星有所薯",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[315] = {
id = 315,
activityType = v0,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1711036799
},
showEndTime = {
seconds = 1711036799
},
showBeginTime = {
seconds = **********
}
},
labelId = 1,
backgroundUrl = {
"Xiaohs02.astc"
},
jumpId = 18,
lowVersion = v30,
activityName = "星有所薯",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[316] = {
id = 316,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1711036800
},
endTime = {
seconds = 1711641599
},
showEndTime = {
seconds = 1711641599
},
showBeginTime = {
seconds = 1711036800
}
},
labelId = 1,
backgroundUrl = {
"Xiaohs03.astc"
},
jumpId = 18,
lowVersion = v30,
activityName = "星有所薯",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[317] = {
id = 317,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1712246399
},
showEndTime = {
seconds = 1712246399
},
showBeginTime = {
seconds = 1711641600
}
},
labelId = 1,
backgroundUrl = {
"Xiaohs04.astc"
},
jumpId = 18,
lowVersion = v30,
activityName = "星有所薯",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[318] = {
id = 318,
timeInfo = {
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1709308799
},
showEndTime = {
seconds = 1709308799
},
showBeginTime = {
seconds = 1708704000
}
},
labelId = 11,
backgroundUrl = {
"aoteman0212.astc"
},
lowVersion = v30,
activityName = "奥特曼赠礼",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56164
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[319] = {
id = 319,
timeInfo = {
beginTime = {
seconds = 1709481600
},
endTime = {
seconds = 1709567999
},
showEndTime = {
seconds = 1709567999
},
showBeginTime = {
seconds = 1709222400
}
},
labelId = 1,
backgroundUrl = {
"wulong.astc"
},
lowVersion = v30,
activityName = [[周一登录
点石成金]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56165
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[320] = {
id = 320,
timeInfo = {
beginTime = {
seconds = 1709222400
},
endTime = {
seconds = 1709827199
},
showEndTime = {
seconds = 1709827199
},
showBeginTime = {
seconds = 1709222400
}
},
labelId = 4,
backgroundUrl = {
"fabuhui.astc"
},
lowVersion = v30,
activityName = "3月激战",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56166,
56167,
56168
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[321] = {
id = 321,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1709222400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1709222400
}
},
labelId = 3,
backgroundUrl = {
"youyaxingshike.astc",
"T_Activity_TestTitle_5.astc"
},
lowVersion = v30,
activityName = "优雅星时刻",
activityParam = {
24,
3111,
556489
},
activityDesc = "绅士风度时装带你领略优雅时刻",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56170,
56169
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3111
},
platforms = v26
},
[322] = {
id = 322,
timeInfo = {
beginTime = {
seconds = 1709222400
},
endTime = {
seconds = 1709827199
},
showEndTime = {
seconds = 1709827199
},
showBeginTime = {
seconds = 1709222400
}
},
labelId = 2,
backgroundUrl = {
"xinhuanyouxingshijie1_N_A.astc"
},
lowVersion = v30,
activityName = "环游星世界",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56171,
56172
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[323] = {
id = 323,
timeInfo = {
beginTime = {
seconds = 1709222400
},
endTime = {
seconds = 1709827199
},
showEndTime = {
seconds = 1709827199
},
showBeginTime = {
seconds = 1709222400
}
},
labelId = 1,
backgroundUrl = {
"guofeng.astc"
},
lowVersion = "1.2.80.47",
activityName = "星友滴滴",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56173,
56174
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[325] = {
id = 325,
activityType = "ATAccumulateBlessings",
timeInfo = {
beginTime = {
seconds = 1709222400
},
endTime = {
seconds = 1715788799
},
showEndTime = {
seconds = 1755273599
},
showBeginTime = {
seconds = 1709222400
}
},
labelId = 1,
activityName = "月初攒福气",
activityParam = {
1,
36,
8100,
8101
},
activityUIDetail = "UI_Activity_AssistBless",
isInBottom = 1,
activityRuleId = 121,
tagId = 4,
showInCenter = true,
activityNameType = "ANTAccumulateBlessings",
platforms = v26
},
[326] = {
id = 326,
timeInfo = {
beginTime = {
seconds = 1709222400
},
endTime = {
seconds = 1709827199
},
showEndTime = {
seconds = 1709827199
},
showBeginTime = {
seconds = 1709222400
}
},
labelId = 2,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
activityName = "组队有福利",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56175
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[327] = {
id = 327,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1710431999
},
showEndTime = {
seconds = 1710431999
},
showBeginTime = {
seconds = **********
}
},
labelId = 8,
backgroundUrl = {
"NActivity_BgDan2_mh.astc"
},
lowVersion = v30,
activityName = "组队有福利",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56176
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[328] = {
id = 328,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1710518399
},
showEndTime = {
seconds = 1710518399
},
showBeginTime = {
seconds = **********
}
},
labelId = 10,
backgroundUrl = {
"xuanzhuanmuma.astc"
},
lowVersion = v30,
activityName = [[每日新搭配
迎接新赛季]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56177,
56178,
56179,
56180,
56181
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[329] = {
id = 329,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1710172799
},
showEndTime = {
seconds = 1710172799
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
backgroundUrl = {
"conglin.astc"
},
lowVersion = v30,
activityName = [[周一登录
舞动青春]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56182
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[330] = {
id = 330,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1710431999
},
showEndTime = {
seconds = 1710431999
},
showBeginTime = {
seconds = **********
}
},
labelId = 9,
backgroundUrl = {
"nnnnnewwuqidashi.astc"
},
lowVersion = v30,
activityName = "放松一下吧",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56183,
56184,
56185,
56186,
56187
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[331] = {
id = 331,
activityType = v2,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1711123199
},
showEndTime = {
seconds = 1711123199
},
showBeginTime = {
seconds = **********
}
},
labelId = 7,
backgroundUrl = {
"youyangsanguangmang.astc"
},
lowVersion = v30,
activityName = "悠扬散光芒",
activityParam = {
24,
3112,
556521
},
activityDesc = "充满自信，去绽放属于我们的光芒！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56190,
56188,
56189
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3112
},
platforms = v26
},
[332] = {
id = 332,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1710431999
},
showEndTime = {
seconds = 1710431999
},
showBeginTime = {
seconds = **********
}
},
labelId = 2,
backgroundUrl = {
"xinhuanyouxingshijie1_N_A.astc"
},
lowVersion = v30,
activityName = "环游星世界",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56191,
56192
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[333] = {
id = 333,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1710431999
},
showEndTime = {
seconds = 1710431999
},
showBeginTime = {
seconds = **********
}
},
labelId = 2,
backgroundUrl = {
"guofeng.astc"
},
lowVersion = "1.2.80.47",
activityName = "星友滴滴",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56193,
56194
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[334] = {
id = 334,
activityType = v0,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1710777599
},
showEndTime = {
seconds = 1710777599
},
showBeginTime = {
seconds = **********
}
},
labelId = 9,
backgroundUrl = {
"boomugc_0304.astc"
},
jumpId = 19,
lowVersion = v30,
activityName = "皮肤共创大赛",
activityUIDetail = v15,
tagId = 6,
showInCenter = true,
activityNameType = v20,
jumpType = 1,
titleType = 0,
platforms = v26
},
[335] = {
id = 335,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 11,
backgroundUrl = {
"guimixinpinli.astc"
},
lowVersion = v30,
activityName = "闺蜜新品礼",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56195
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[336] = {
id = 336,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 7,
backgroundUrl = {
"tongxingchongci01.astc"
},
lowVersion = v30,
activityName = "通行证冲刺",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56196,
56197
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[337] = {
id = 337,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
backgroundUrl = {
"yuanmengkouding.astc"
},
lowVersion = v30,
activityName = "元梦扣叮",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56198
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[338] = {
id = 338,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 1,
lowVersion = "1.2.90.1",
activityName = "回归一元抽奖",
tagId = 1,
activityTaskGroup = {
56199
},
activityNameType = "ANTReturningTask",
titleType = 0,
activityBeginCleanCoin = {
2043
},
platforms = v26
},
[339] = {
id = 339,
activityType = "ATReturningDiffers",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = 1733846399
},
showEndTime = {
seconds = 1733846399
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 1,
backgroundUrl = {
"T_LinearExchange_Img_Background_N_A.astc",
"T_LinearExchange_Img_Title1_N_A.astc",
"T_LinearExchange_Img_Girl_N_A.astc"
},
lowVersion = "1.3.37.70",
activityName = "集友谊之火",
activityParam = {
2800001,
401390
},
activityDesc = "友谊之火",
activityUIDetail = "UI_Return_Activity_Exchange",
activityRuleId = 126,
tagId = 4,
activityTaskGroup = {
60590,
60591
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
40
},
titleType = 1,
activitySubName = "兑小栗子时装",
platforms = v26
},
[350] = {
id = 350,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1711900799
},
showEndTime = {
seconds = 1711900799
},
showBeginTime = {
seconds = 1710432000
}
},
labelId = 3,
backgroundUrl = {
"ruixing0312.astc"
},
lowVersion = "1.2.90.1",
activityName = "咖咖上星记",
activityParam = {
24,
3114,
556606
},
activityDesc = "瑞幸联动背饰&专属称号即将登场~",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56200,
56201,
56202
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3114
},
platforms = v26
},
[351] = {
id = 351,
timeInfo = {
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1711036799
},
showEndTime = {
seconds = 1711036799
},
showBeginTime = {
seconds = 1710432000
}
},
labelId = 2,
backgroundUrl = {
"jisufeiche.astc"
},
lowVersion = "1.2.90.1",
activityName = "焕新来畅玩",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56203,
56204,
56205,
56206
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[352] = {
id = 352,
timeInfo = {
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1711036799
},
showEndTime = {
seconds = 1711036799
},
showBeginTime = {
seconds = 1710432000
}
},
labelId = 5,
backgroundUrl = {
"lahuochewenan.astc"
},
lowVersion = v30,
activityName = [[赛季新关卡
组队有福利]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56207
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[353] = {
id = 353,
timeInfo = {
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1710777599
},
showEndTime = {
seconds = 1710777599
},
showBeginTime = {
seconds = 1710432000
}
},
labelId = 4,
backgroundUrl = {
"kuayuezhangai.astc"
},
lowVersion = v30,
activityName = [[周一来登录
初春花束礼]],
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56208
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[354] = {
id = 354,
timeInfo = {
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1711036799
},
showEndTime = {
seconds = 1711036799
},
showBeginTime = {
seconds = 1710432000
}
},
labelId = 6,
backgroundUrl = {
"yinghuochong.astc"
},
lowVersion = "1.2.90.1",
activityName = "新品体验礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56209
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[355] = {
id = 355,
activityType = "ATRecruitOrder",
timeInfo = {
beginTime = {
seconds = 1710518400
},
endTime = {
seconds = 1717171199
},
showEndTime = {
seconds = 1717171199
},
showBeginTime = {
seconds = 1710518400
}
},
labelId = 6,
backgroundUrl = {
"T_Convened_Img_Background.astc"
},
activityName = "召集星搭子",
activityUIDetail = "UI_Activity_Convened_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56210,
56211
},
showInCenter = true,
activityNameType = v22,
titleType = 0,
activityBeginCleanCoin = {
2033
},
platforms = v26
},
[356] = {
id = 356,
activityType = "ATKungFuPanda",
timeInfo = {
beginTime = {
seconds = 1711036800
},
endTime = {
seconds = 1711814399
},
showEndTime = {
seconds = 1711900799
},
showBeginTime = {
seconds = 1711051200
}
},
labelId = 99,
activityName = "阿宝送面挑战",
activityParam = {
10,
11,
2002,
2,
10006,
20,
10
},
activityUIDetail = "UI_KongFuPanda_MainView",
tagId = 1,
activityTaskGroup = {
56226,
56227,
56228
},
showInCenter = true,
slapFace = true,
activityNameType = "ANKungFuPanda",
titleType = 0,
platforms = v26
},
[360] = {
id = 360,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1711036800
},
endTime = {
seconds = 1712332799
},
showEndTime = {
seconds = 1712332799
},
showBeginTime = {
seconds = 1711036800
}
},
labelId = 2,
backgroundUrl = {
"2ruixingdatu.astc"
},
lowVersion = "1.2.90.1",
activityName = [[咖咖上星记
第二期来袭]],
activityParam = {
24,
3115,
556634
},
activityDesc = "瑞幸联动背饰&专属称号现已登场~",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56212,
56213,
56214
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3115
},
platforms = v26
},
[363] = {
id = 363,
timeInfo = {
beginTime = {
seconds = 1711036800
},
endTime = {
seconds = 1711641599
},
showEndTime = {
seconds = 1711641599
},
showBeginTime = {
seconds = 1711036800
}
},
labelId = 4,
backgroundUrl = {
"lahuochewenan.astc"
},
lowVersion = "1.2.90.1",
activityName = "组队有福利",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56219
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[364] = {
id = 364,
timeInfo = {
beginTime = {
seconds = 1711036800
},
endTime = {
seconds = 1711641599
},
showEndTime = {
seconds = 1711641599
},
showBeginTime = {
seconds = 1711036800
}
},
labelId = 3,
backgroundUrl = {
"guaiealv.astc"
},
lowVersion = "1.2.90.1",
activityName = [[登录领时装
周一送好礼]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56220,
56221
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[365] = {
id = 365,
timeInfo = {
beginTime = {
seconds = 1711036800
},
endTime = {
seconds = 1711468799
},
showEndTime = {
seconds = 1711468799
},
showBeginTime = {
seconds = 1711036800
}
},
labelId = 2,
backgroundUrl = {
"hualitaoz.astc"
},
lowVersion = "1.2.90.1",
activityName = "畅玩娱乐赛",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56222,
56223
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[366] = {
id = 366,
timeInfo = {
beginTime = {
seconds = 1711468800
},
endTime = {
seconds = 1711900799
},
showEndTime = {
seconds = 1711900799
},
showBeginTime = {
seconds = 1711036800
}
},
labelId = 4,
backgroundUrl = {
"hualitaoz.astc"
},
lowVersion = "1.2.90.1",
activityName = "畅玩娱乐赛",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56224,
56225
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[367] = {
id = 367,
timeInfo = {
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1712159999
},
showEndTime = {
seconds = 1712159999
},
showBeginTime = {
seconds = 1711641600
}
},
labelId = 1,
backgroundUrl = {
"huaxiangyixiaobanma.astc"
},
lowVersion = "1.2.90.1",
activityName = "领星愿币",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56238,
56229,
56230,
56231,
56232
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[368] = {
id = 368,
timeInfo = {
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1712159999
},
showEndTime = {
seconds = 1712159999
},
showBeginTime = {
seconds = 1711641600
}
},
labelId = 5,
backgroundUrl = {
"jisufeiche.astc"
},
lowVersion = "1.2.90.1",
activityName = "畅玩娱乐赛",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56233,
56234
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[369] = {
id = 369,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1712505599
},
showEndTime = {
seconds = 1712505599
},
showBeginTime = {
seconds = 1711641600
}
},
labelId = 3,
backgroundUrl = {
"gongfuxiongmaoduihuan.astc"
},
lowVersion = "1.2.90.1",
activityName = "乐园开趴中",
activityParam = {
25,
3116
},
activityDesc = "神龙大侠邀你乐园练武！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56236,
56237
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3116
},
platforms = v26
},
[370] = {
id = 370,
activityType = "ATLotteryDraw",
timeInfo = {
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1741708799
},
showEndTime = {
seconds = 1741708799
},
showBeginTime = {
seconds = 1711641600
}
},
labelId = 7,
activityName = [[正版联动
现已开启]],
activityUIDetail = "UI_QQSpeed_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56239
},
showInCenter = true,
activityNameType = "ANLotteryDraw",
titleType = 0,
activityBeginCleanCoin = {
3117
},
platforms = v26
},
[371] = {
id = 371,
timeInfo = {
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1712851199
},
showEndTime = {
seconds = 1712851199
},
showBeginTime = {
seconds = 1711641600
}
},
labelId = 2,
backgroundUrl = {
"jisufeichexiaojuzi.astc"
},
lowVersion = "1.2.90.51",
activityName = "小橘子有礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56240
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[390] = {
id = 390,
activityType = "ATKungFuPanda",
timeInfo = {
beginTime = {
seconds = 1711900800
},
endTime = {
seconds = 1712419199
},
showEndTime = {
seconds = 1712505599
},
showBeginTime = {
seconds = 1711900800
}
},
labelId = 4,
activityName = "阿宝终极挑战",
activityParam = {
10,
6,
2003,
3,
10006,
5,
5
},
activityUIDetail = "UI_KongFuPanda_MainView",
tagId = 4,
activityTaskGroup = {
56241,
56242,
56243
},
showInCenter = true,
slapFace = true,
activityNameType = "ANKungFuPanda",
titleType = 0,
platforms = v26
},
[395] = {
id = 395,
timeInfo = {
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1712851199
},
showEndTime = {
seconds = 1712851199
},
showBeginTime = {
seconds = 1712160000
}
},
labelId = 7,
backgroundUrl = {
"mianmianwaimai0117.astc"
},
lowVersion = "1.2.90.66",
activityName = "迎春每日礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56251,
56252,
56253
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[396] = {
id = 396,
timeInfo = {
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1712851199
},
showEndTime = {
seconds = 1712851199
},
showBeginTime = {
seconds = 1712160000
}
},
labelId = 9,
backgroundUrl = {
"tuijianwanfa0329.astc"
},
lowVersion = "1.2.90.66",
activityName = "娱乐情报站",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56254
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[397] = {
id = 397,
activityType = "ATLuckyBalloon",
timeInfo = {
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1713110399
},
showEndTime = {
seconds = 1713110399
},
showBeginTime = {
seconds = 1712160000
}
},
labelId = 2,
lowVersion = "1.2.90.66",
activityName = "星灯许愿",
activityParam = {
1
},
activityDesc = "寻春星灯",
activityUIDetail = "UI_LuckyBall_MainView",
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56255,
56256
},
showInCenter = true,
activityNameType = "ANTLuckyBalloon",
titleType = 0,
activityBeginCleanCoin = {
10
},
platforms = v26
},
[398] = {
id = 398,
activityType = "ATTeamRank",
timeInfo = {
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1712851199
},
showEndTime = {
seconds = 1712851199
},
showBeginTime = {
seconds = 1712160000
}
},
labelId = 10,
lowVersion = "1.2.90.66",
activityName = "排位双重礼",
activityDesc = "打排位不掉星及额外加分活动",
activityUIDetail = "UI_TeamActi_MainView",
tagId = 1,
activityTaskGroup = {
56257
},
showInCenter = true,
activityNameType = "ANTTeamRank",
titleType = 0,
platforms = v26
},
[399] = {
id = 399,
timeInfo = {
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1743523199
},
showEndTime = {
seconds = 1743523199
},
showBeginTime = {
seconds = 1712160000
}
},
labelId = 5,
lowVersion = "1.2.90.66",
activityName = "初春签到礼",
activityDesc = "一起来迎接春天的色彩！",
activityUIDetail = "UI_SignIn_NewaYear_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56258
},
showInCenter = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[400] = {
id = 400,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1713110399
},
showEndTime = {
seconds = 1713110399
},
showBeginTime = {
seconds = 1712160000
}
},
labelId = 6,
backgroundUrl = {
"taqingyingchunji.astc"
},
jumpId = 304,
lowVersion = "1.2.90.66",
activityName = "踏青迎春",
activityUIDetail = v15,
tagId = 1,
showInCenter = true,
activityNameType = v20,
titleType = 0,
platforms = v26
},
[405] = {
id = 405,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1714319999
},
showEndTime = {
seconds = 1714319999
},
showBeginTime = {
seconds = 1712851200
}
},
labelId = 2,
backgroundUrl = {
"caigouliandong02.astc"
},
lowVersion = "1.2.90.66",
activityName = "春日新朋友",
activityParam = {
25,
3121
},
activityDesc = "蔬菜精灵正版联动来袭！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56270,
56271
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3121
},
platforms = v26
},
[406] = {
id = 406,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1714060799
},
showEndTime = {
seconds = 1714060799
},
showBeginTime = {
seconds = 1712851200
}
},
labelId = 4,
backgroundUrl = {
"youxiankababa.astc"
},
lowVersion = "1.2.90.66",
activityName = "悠闲卡巴巴",
activityParam = {
24,
3122,
556731
},
activityDesc = "一起做情绪状态最稳定的星宝吧！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56272,
56273
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3122
},
platforms = v26
},
[407] = {
id = 407,
timeInfo = {
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1713455999
},
showEndTime = {
seconds = 1713455999
},
showBeginTime = {
seconds = 1712851200
}
},
labelId = 7,
backgroundUrl = {
"tuijianwanfa0412.astc"
},
lowVersion = "1.2.90.66",
activityName = "谁是娱乐家",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56274
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[408] = {
id = 408,
timeInfo = {
beginTime = {
seconds = 1713110400
},
endTime = {
seconds = 1713542399
},
showEndTime = {
seconds = 1713542399
},
showBeginTime = {
seconds = 1712851200
}
},
labelId = 6,
backgroundUrl = {
"kuayuezhangai.astc"
},
lowVersion = "1.2.90.66",
activityName = "周初星愿礼",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56275
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[410] = {
id = 410,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1714924799
},
showEndTime = {
seconds = 1714924799
},
showBeginTime = {
seconds = 1713456000
}
},
labelId = 3,
backgroundUrl = {
"xiaoxinhefengjian02.astc"
},
lowVersion = "1.2.90.91",
activityName = "小新开派对",
activityParam = {
32,
3123
},
activityDesc = "蜡笔小新联动第二弹开启！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56280,
56281
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
32
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3123
},
platforms = v26
},
[411] = {
id = 411,
timeInfo = {
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1714060799
},
showEndTime = {
seconds = 1714060799
},
showBeginTime = {
seconds = 1713456000
}
},
labelId = 5,
backgroundUrl = {
"tuijianwanfa0419_2.astc"
},
lowVersion = "1.2.90.91",
activityName = "头号玩家",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56282,
56286
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[412] = {
id = 412,
timeInfo = {
beginTime = {
seconds = 1713715200
},
endTime = {
seconds = 1714147199
},
showEndTime = {
seconds = 1714147199
},
showBeginTime = {
seconds = 1713456000
}
},
labelId = 3,
backgroundUrl = {
"fabuhui.astc"
},
lowVersion = "1.2.90.91",
activityName = "周初星愿礼",
activityUIDetail = v16,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56283
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[413] = {
id = 413,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1713542400
},
endTime = {
seconds = 1714924799
},
showEndTime = {
seconds = 1714924799
},
showBeginTime = {
seconds = 1713542400
}
},
labelId = 4,
backgroundUrl = {
"haidilaoxiaohonghu03.astc"
},
lowVersion = "1.2.90.91",
activityName = "一起嗨翻天",
activityParam = {
24,
3124,
556763
},
activityDesc = "海底捞联动时装限时登场！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56284,
56285
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3124
},
platforms = v26
},
[415] = {
id = 415,
timeInfo = {
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1714492799
},
showEndTime = {
seconds = 1714492799
},
showBeginTime = {
seconds = 1714060800
}
},
labelId = 1,
backgroundUrl = {
"zhongdianzhengduohuangguan.astc"
},
lowVersion = "1.2.100.7",
activityName = [[赛季新气象
元梦新方向]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56287,
56288,
57298
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[416] = {
id = 416,
timeInfo = {
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1714492799
},
showEndTime = {
seconds = 1714492799
},
showBeginTime = {
seconds = 1714060800
}
},
labelId = 2,
backgroundUrl = {
"fanchengcheng0422.astc"
},
lowVersion = "1.2.100.7",
activityName = "丞丞直播礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56289,
56290,
56291
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[417] = {
id = 417,
timeInfo = {
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1714492799
},
showEndTime = {
seconds = 1714492799
},
showBeginTime = {
seconds = 1714060800
}
},
labelId = 4,
backgroundUrl = {
"tuijianwanfa0503.astc"
},
lowVersion = "1.2.100.7",
activityName = "娱乐情报站",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56292,
56298
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[418] = {
id = 418,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1714924799
},
showEndTime = {
seconds = 1714924799
},
showBeginTime = {
seconds = 1714060800
}
},
labelId = 2,
backgroundUrl = {
"jueweiyabo.astc"
},
lowVersion = "1.2.100.7",
activityName = "一个字绝",
activityParam = {
24,
3125
},
activityDesc = "绝味鸭脖联动再开启！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56293,
56294
},
showInCenter = true,
activityNameType = v23,
activityShopType = v28,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3125
},
platforms = v26
},
[419] = {
id = 419,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1714924799
},
showEndTime = {
seconds = 1714924799
},
showBeginTime = {
seconds = 1714060800
}
},
labelId = 1,
backgroundUrl = {
"fanchengchengxianxingduihuan.astc"
},
lowVersion = "1.2.100.7",
activityName = "丞丞衣帽间",
activityParam = {
25,
3126,
556806
},
activityDesc = "丞丞专属配饰免费获得！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56295,
56296,
56297
},
showInCenter = true,
activityNameType = v22,
activityShopType = v27,
titleType = 0,
activityBeginCleanCoin = {
3126
},
platforms = v26
},
[420] = {
id = 420,
activityType = "ATSticker",
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1744473599
},
showEndTime = {
seconds = 1744473599
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 3,
activityName = "小丸子贴纸",
activityUIDetail = "UI_Xiaowanzi_ActivityView",
isInBottom = 1,
activityRuleId = 141,
tagId = 1,
activityTaskGroup = {
56300,
56301,
56302
},
showInCenter = true,
activityNameType = "ANTSticker",
titleType = 0,
activityBeginCleanCoin = {
3128
},
platforms = v26
},
[421] = {
id = 421,
activityType = "ATSuperLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1726156799
},
showEndTime = {
seconds = 1726156799
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 4,
activityName = "假日出游季",
activityParam = {
3129,
10
},
activityUIDetail = "UI_Activity_ChibiMarukoView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56303,
56304
},
showInCenter = true,
activityNameType = "ANTSuperLinearRedeem",
titleType = 0,
activityBeginCleanCoin = {
3129
},
activitySubName = "小标题在测试",
platforms = v26
},
[422] = {
id = 422,
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1743523199
},
showEndTime = {
seconds = 1743523199
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 1,
lowVersion = "1.2.100.18",
activityName = "出游签到礼",
activityDesc = "快乐星奇天出游送好礼！",
activityUIDetail = "UI_SignIn_NewaYear_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56305
},
showInCenter = true,
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[423] = {
id = 423,
timeInfo = {
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1715529599
},
showEndTime = {
seconds = 1715529599
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 5,
backgroundUrl = {
"kunzaiju.astc"
},
lowVersion = "1.2.100.1",
activityName = "快乐寻鲲宝",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56306
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[424] = {
id = 424,
timeInfo = {
beginTime = {
seconds = 1682870400
},
endTime = {
seconds = 1683302399
},
showEndTime = {
seconds = 1683302399
},
showBeginTime = {
seconds = 1682870400
}
},
labelId = 99,
backgroundUrl = {
"fabuhui.astc"
},
lowVersion = "1.2.100.1",
activityName = "超级跳出道",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56307
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[425] = {
id = 425,
activityType = "ATTeamRank",
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1715270399
},
showEndTime = {
seconds = 1715270399
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 9,
lowVersion = "1.2.100.1",
activityName = "排位双重礼",
activityDesc = "打排位不掉星及额外加分！",
activityUIDetail = "UI_TeamActi_MainView",
tagId = 1,
activityTaskGroup = {
56308
},
showInCenter = true,
activityNameType = "ANTTeamRank",
titleType = 0,
platforms = v26
},
[430] = {
id = 430,
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1715270399
},
showEndTime = {
seconds = 1715270399
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 8,
backgroundUrl = {
"tuijianwanfa0430.astc"
},
lowVersion = "1.2.100.1",
activityName = "娱乐情报站",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56309
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[431] = {
id = 431,
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1715270399
},
showEndTime = {
seconds = 1715270399
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 7,
backgroundUrl = {
"sanwang.astc"
},
lowVersion = "1.2.100.1",
activityName = [[星宝快步跑
大王来抓啦]],
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56310
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[432] = {
id = 432,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1715529599
},
showEndTime = {
seconds = 1715529599
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 2,
lowVersion = "1.2.100.1",
activityName = "快乐星奇天",
activityUIDetail = "UI_ActivityNavigation01",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTActivityNavigation",
titleType = 0,
platforms = v26
},
[433] = {
id = 433,
timeInfo = {
beginTime = {
seconds = 1714564800
},
endTime = {
seconds = 1714579199
},
showEndTime = {
seconds = 1714579199
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 10,
backgroundUrl = {
"yinyuehui02.astc"
},
lowVersion = "1.2.100.18",
activityName = "超级跳出道",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56311
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[434] = {
id = 434,
timeInfo = {
beginTime = {
seconds = 1714651200
},
endTime = {
seconds = 1714665599
},
showEndTime = {
seconds = 1714665599
},
showBeginTime = {
seconds = 1714579200
}
},
labelId = 10,
backgroundUrl = {
"yinyuehui02.astc"
},
lowVersion = "1.2.100.18",
activityName = "超级跳出道",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56312
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[435] = {
id = 435,
timeInfo = {
beginTime = {
seconds = 1714737600
},
endTime = {
seconds = 1714751999
},
showEndTime = {
seconds = 1714751999
},
showBeginTime = {
seconds = 1714665600
}
},
labelId = 10,
backgroundUrl = {
"yinyuehui02.astc"
},
lowVersion = "1.2.100.18",
activityName = "超级跳出道",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56313
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[436] = {
id = 436,
timeInfo = {
beginTime = {
seconds = 1714824000
},
endTime = {
seconds = 1714838399
},
showEndTime = {
seconds = 1714838399
},
showBeginTime = {
seconds = 1714752000
}
},
labelId = 10,
backgroundUrl = {
"yinyuehui02.astc"
},
lowVersion = "1.2.100.18",
activityName = "超级跳出道",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56314
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[437] = {
id = 437,
timeInfo = {
beginTime = {
seconds = 1714910400
},
endTime = {
seconds = 1714924799
},
showEndTime = {
seconds = 1714924799
},
showBeginTime = {
seconds = 1714838400
}
},
labelId = 10,
backgroundUrl = {
"yinyuehui02.astc"
},
lowVersion = "1.2.100.18",
activityName = "超级跳出道",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56315
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[438] = {
id = 438,
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1714924799
},
showEndTime = {
seconds = 1714924799
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 6,
backgroundUrl = {
"xingbaonongchang.astc"
},
lowVersion = "1.2.100.18",
activityName = "种菜乐翻天",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56316
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[440] = {
id = 440,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1715270400
},
endTime = {
seconds = 1717603199
},
showEndTime = {
seconds = 1717603199
},
showBeginTime = {
seconds = 1715270400
}
},
labelId = 6,
backgroundUrl = {
"dahuangfeng.astc"
},
lowVersion = "1.2.100.31",
activityName = "大黄蜂报到",
activityParam = {
24,
3130,
556906
},
activityDesc = "极速飞车专属赛车-大黄蜂免费得！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56320,
56321,
56322,
56323
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3130
},
platforms = v26
},
[441] = {
id = 441,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1715270400
},
endTime = {
seconds = 1716739199
},
showEndTime = {
seconds = 1716739199
},
showBeginTime = {
seconds = 1715270400
}
},
labelId = 5,
backgroundUrl = {
"banlangbanniang520.astc"
},
lowVersion = "1.2.100.31",
activityName = "永恒之约",
activityParam = {
25,
3131
},
activityDesc = "连星宝也是爱你的形状！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56324,
56325
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3131
},
platforms = v26
},
[442] = {
id = 442,
timeInfo = {
beginTime = {
seconds = 1715270400
},
endTime = {
seconds = 1715875199
},
showEndTime = {
seconds = 1715875199
},
showBeginTime = {
seconds = 1715270400
}
},
labelId = 4,
backgroundUrl = {
"wanfatuijian0510.astc"
},
lowVersion = "1.2.100.31",
activityName = "娱乐情报站",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56326
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[443] = {
id = 443,
timeInfo = {
beginTime = {
seconds = 1715270400
},
endTime = {
seconds = 1715875199
},
showEndTime = {
seconds = 1715875199
},
showBeginTime = {
seconds = 1715270400
}
},
labelId = 2,
backgroundUrl = {
"senxiaoyenongchang.astc"
},
lowVersion = "1.2.100.31",
activityName = "勤劳小菜农",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56327
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[444] = {
id = 444,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1718726400
},
endTime = {
seconds = 1724947199
},
showEndTime = {
seconds = 1724947199
},
showBeginTime = {
seconds = 1718726400
}
},
labelId = 1,
backgroundUrl = {
"T_ArenaActivity_lmg_Bg02.astc"
},
activityName = "峡谷竞技",
activityParam = {
24,
3540,
590015
},
activityDesc = "完成任务获得闪电币，赢取专属皮肤！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 10,
activityTaskGroup = {
59001,
59002,
59003,
59004
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3540
},
platforms = v26
},
[448] = {
id = 448,
timeInfo = {
beginTime = {
seconds = 1715875200
},
endTime = {
seconds = 1716479999
},
showEndTime = {
seconds = 1716479999
},
showBeginTime = {
seconds = 1715875200
}
},
labelId = 1,
backgroundUrl = {
"lahuochewenan.astc"
},
lowVersion = v31,
activityName = "携手相伴行",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56334,
56335,
56336
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[446] = {
id = 446,
timeInfo = {
beginTime = {
seconds = 1715875200
},
endTime = {
seconds = 1716479999
},
showEndTime = {
seconds = 1716479999
},
showBeginTime = {
seconds = 1715875200
}
},
labelId = 2,
backgroundUrl = {
"tianmichuyouxin520.astc"
},
lowVersion = v31,
activityName = "甜蜜出游记",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56332
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[447] = {
id = 447,
timeInfo = {
beginTime = {
seconds = 1715875200
},
endTime = {
seconds = 1716479999
},
showEndTime = {
seconds = 1716479999
},
showBeginTime = {
seconds = 1715875200
}
},
labelId = 3,
backgroundUrl = {
"hashiqinongchang.astc"
},
lowVersion = v31,
activityName = "种菜星搭子",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56333
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[450] = {
id = 450,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1716480000
},
endTime = {
seconds = 1717343999
},
showEndTime = {
seconds = 1717343999
},
showBeginTime = {
seconds = 1716480000
}
},
labelId = 1,
backgroundUrl = {
"shejiwanfa.astc"
},
lowVersion = v31,
activityName = "刚刚派对",
activityParam = {
24,
3133
},
activityDesc = "火热前线时装免费得！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56340,
56341
},
showInCenter = true,
activityNameType = v23,
activityShopType = v28,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3133
},
platforms = v26
},
[451] = {
id = 451,
timeInfo = {
beginTime = {
seconds = 1716480000
},
endTime = {
seconds = 1719763199
},
showEndTime = {
seconds = 1719763199
},
showBeginTime = {
seconds = 1716480000
}
},
labelId = 4,
backgroundUrl = {
"T_WeChat_Bg_SummerGameplay.astc"
},
lowVersion = v31,
activityName = "种菜星搭子",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56342
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[452] = {
id = 452,
timeInfo = {
beginTime = {
seconds = 1716480000
},
endTime = {
seconds = 1717084799
},
showEndTime = {
seconds = 1717084799
},
showBeginTime = {
seconds = 1716480000
}
},
labelId = 3,
backgroundUrl = {
"nnnnnewwuqidashi.astc"
},
lowVersion = v31,
activityName = "段位冲刺周",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56343,
56344
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[453] = {
id = 453,
activityType = "ATSuperLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1716480000
},
endTime = {
seconds = 1722355199
},
showEndTime = {
seconds = 1722355199
},
showBeginTime = {
seconds = 1716480000
}
},
labelId = 2,
backgroundUrl = {
"T_RegularExchange02_Img_Background.astc"
},
activityName = "火力全开",
activityParam = {
3132,
10
},
activityUIDetail = "UI_Activity_DrawLinear02_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56345
},
showInCenter = true,
activityNameType = "ANTSuperLinearRedeem",
titleType = 0,
activityBeginCleanCoin = {
3132
},
platforms = v26
},
[454] = {
id = 454,
timeInfo = {
beginTime = {
seconds = 1716825600
},
endTime = {
seconds = 1717343999
},
showEndTime = {
seconds = 1717343999
},
showBeginTime = {
seconds = 1716825600
}
},
labelId = 9,
backgroundUrl = {
"youxifabuhui.astc"
},
lowVersion = v31,
activityName = "UP发布会",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56346
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[455] = {
id = 455,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 1,
lowVersion = v31,
activityName = "回归六元礼包",
tagId = 1,
activityTaskGroup = {
56347
},
activityNameType = "ANTReturningTask",
titleType = 0,
platforms = v26
},
[456] = {
id = 456,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "每日福利",
tagId = 1,
activityTaskGroup = {
56348,
56349,
56350,
56351,
56352,
56353,
56354
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4561] = {
id = 4561,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "每日福利",
tagId = 1,
activityTaskGroup = {
563481,
563491,
563501,
563511,
563521,
563531,
563541
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4562] = {
id = 4562,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "每日福利",
tagId = 1,
activityTaskGroup = {
563482,
563492,
563502,
563512,
563522,
563532,
563542
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4563] = {
id = 4563,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "每日福利",
tagId = 1,
activityTaskGroup = {
563483,
563493,
563503,
563513,
563523,
563533,
563543
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4564] = {
id = 4564,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "每日福利",
tagId = 1,
activityTaskGroup = {
563484,
563494,
563504,
563514,
563524,
563534,
563544
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4565] = {
id = 4565,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "每日福利",
tagId = 1,
activityTaskGroup = {
563485,
563495,
563505,
563515,
563525,
563535,
563545
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4601] = {
id = 4601,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "福利1",
tagId = 1,
activityTaskGroup = {
600001
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4602] = {
id = 4602,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "福利2",
tagId = 1,
activityTaskGroup = {
600002
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4603] = {
id = 4603,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "福利3",
tagId = 1,
activityTaskGroup = {
600003
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4604] = {
id = 4604,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "福利4",
tagId = 1,
activityTaskGroup = {
600004
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4605] = {
id = 4605,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "福利5",
tagId = 1,
activityTaskGroup = {
600005
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[4606] = {
id = 4606,
activityType = "ATReturningDailyGroupTask",
timeInfo = {
beginTime = {
seconds = 1710986400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1710986400
}
},
labelId = 3,
lowVersion = v31,
activityName = "福利6",
tagId = 1,
activityTaskGroup = {
600006
},
activityNameType = "ANTReturningDailyGroupTask",
titleType = 0,
platforms = v26
},
[601] = {
id = 601,
activityType = "ATWish",
timeInfo = {
beginTime = {
seconds = 1709222400
},
endTime = {
seconds = 1717084799
},
showEndTime = {
seconds = 1717084799
},
showBeginTime = {
seconds = 1709222400
}
},
labelId = 9,
activityName = "心愿全实现",
activityParam = {
3151,
7001
},
activityUIDetail = "UI_Activity_WishesCameTrue_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
57000,
57001
},
showInCenter = true,
activityNameType = "ANTWish",
titleType = 0,
activityBeginCleanCoin = {
3151
},
platforms = v26
},
[477] = {
id = 477,
activityType = "ATTaskRunOnActivitySvr",
timeInfo = {
beginTime = {
seconds = 1715270400
},
endTime = {
seconds = 1722182399
},
showEndTime = {
seconds = 1722182399
},
showBeginTime = {
seconds = 1715270400
}
},
labelId = 2,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "峡谷英雄传",
activityParam = {
58008,
1015,
50021
},
activityUIDetail = "UI_Activity_LeagueHeroes_Main",
isInBottom = 1,
tagId = 10,
activityTaskGroup = {
58002,
58003,
58004,
58005,
58006,
58007,
58008
},
showInCenter = true,
titleType = 0,
pakGroupId = 50021,
platforms = v26
},
[801] = {
id = 801,
activityType = "ATSuperLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1720799999
},
showEndTime = {
seconds = 1720799999
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 4,
activityName = "漫步中轴",
activityParam = {
3193,
10
},
activityUIDetail = "UI_Activity_CenterAxisView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
61009
},
showInCenter = true,
activityNameType = "ANTSuperLinearRedeem",
titleType = 0,
activityBeginCleanCoin = {
3193
},
activitySubName = "小标题在测试",
platforms = v26
},
[802] = {
id = 802,
activityType = "ATClubChallenge",
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1720799999
},
showEndTime = {
seconds = 1720799999
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 4,
activityName = "社团挑战活动测试",
tagId = 1,
showInCenter = true,
activityNameType = "ANTClubChallenge",
titleType = 0,
platforms = v26
},
[803] = {
id = 803,
timeInfo = {
beginTime = {
seconds = 1716480000
},
endTime = {
seconds = 1720799999
},
showEndTime = {
seconds = 1720799999
},
showBeginTime = {
seconds = 1716480000
}
},
labelId = 3,
activityName = "订阅测试",
activityParam = {
610038,
610039
},
activityUIDetail = "UI_NewVersion_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
61010,
61011
},
showInCenter = true,
activityNameType = v22,
titleType = 0,
platforms = v26
},
[804] = {
id = 804,
activityType = "ATMonopoly",
timeInfo = {
beginTime = {
seconds = 1709222400
},
endTime = {
seconds = 1720799999
},
showEndTime = {
seconds = 1720799999
},
showBeginTime = {
seconds = 1709222400
}
},
labelId = 9,
activityName = "大富翁测试",
activityParam = {
3192
},
activityUIDetail = "UI_Activity_MonopolyView",
isInBottom = 1,
activityRuleId = 141,
tagId = 1,
activityTaskGroup = {
61012,
61013
},
showInCenter = true,
activityNameType = "ANTMonopoly",
titleType = 0,
activitySubName = "小标题在测试",
platforms = v26
},
[805] = {
id = 805,
activityType = "ATTrainingCamp",
timeInfo = {
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1760716799
},
showEndTime = {
seconds = 1760716799
},
showBeginTime = {
seconds = 1714492800
}
},
labelId = 4,
activityName = "运动特训营",
activityParam = {
5,
5,
3171
},
activityUIDetail = "UI_SportsTrainingCamp_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
61008,
61007,
61002,
61003,
61005,
61001,
61006,
61004,
62000,
62001,
61000
},
showInCenter = true,
activityNameType = "ANTTrainingCamp",
titleType = 0,
platforms = v26
},
[806] = {
id = 806,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
showEndTime = {
seconds = 1724947199
},
showBeginTime = {
seconds = 1721318400
}
},
labelId = 1,
activityName = "福利导航",
activityUIDetail = "UI_Activity_FridayCollection_View",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTFridayCollection",
titleType = 0,
platforms = v26
},
[851] = {
id = 851,
activityType = "ATUpdateForesight",
timeInfo = {
beginTime = {
seconds = 1723564800
},
endTime = {
seconds = 1751299199
},
showEndTime = {
seconds = 1751299199
},
showBeginTime = {
seconds = 1723564800
}
},
labelId = 9,
activityName = "新版本预约",
activityParam = {
642002,
642003
},
activityUIDetail = "UI_NewVersion_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
64202,
64201
},
showInCenter = true,
activityNameType = "ANTUpdateForesightV1",
titleType = 0,
platforms = v26
},
[4444] = {
id = 4444,
activityType = "ATLotteryDraw",
timeInfo = {
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1724342399
},
showEndTime = {
seconds = 1724342399
},
showBeginTime = {
seconds = 1723132800
}
},
labelId = 7,
lowVersion = "1.3.12.78",
activityName = "幸运丰收",
activityUIDetail = "UI_FarmActivity_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63003,
63005
},
showInCenter = true,
activityNameType = "ANTFarmActivity",
titleType = 0,
activityBeginCleanCoin = {
2031
},
activityGroup = v32,
platforms = v26
},
[809] = {
id = 809,
activityType = "ATMinesweeper",
timeInfo = {
beginTime = {
seconds = 1718812800
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1718812800
}
},
labelId = 7,
activityName = "扫雷",
activityUIDetail = "UI_TreasureHunt_ActivityView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63004
},
showInCenter = true,
activityNameType = "ANTMinesweeper",
titleType = 0,
activityBeginCleanCoin = {
3117
},
platforms = v26
},
[811] = {
id = 811,
timeInfo = {
beginTime = {
seconds = 1718812800
},
endTime = {
seconds = 1724169599
},
showEndTime = {
seconds = 1724169599
},
showBeginTime = {
seconds = 1718812800
}
},
labelId = 8,
activityName = "峡谷测试123",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
64001
},
showInCenter = true,
titleType = 0,
platforms = v26
},
[460] = {
id = 460,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1717171200
},
endTime = {
seconds = 1718553599
},
showEndTime = {
seconds = 1718553599
},
showBeginTime = {
seconds = 1717171200
}
},
labelId = 8,
backgroundUrl = {
"atongmu.astc"
},
lowVersion = "1.2.100.90",
activityName = "重温阿童木",
activityParam = {
25,
3140
},
activityDesc = "阿童木联动来袭！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56355,
56356,
56357
},
activityNameType = v23,
activityShopType = v27,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3140
},
platforms = v26
},
[461] = {
id = 461,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1717084800
},
endTime = {
seconds = 1717948799
},
showEndTime = {
seconds = 1717948799
},
showBeginTime = {
seconds = 1717084800
}
},
labelId = 9,
backgroundUrl = {
"xyqpx2.astc"
},
lowVersion = "1.2.100.90",
activityName = "星愿起跑线",
activityParam = {
24,
3141,
557011
},
activityDesc = "与元梦一起助力城乡孩子的科创教育",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56358,
56359
},
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3141
},
platforms = v26
},
[462] = {
id = 462,
timeInfo = {
beginTime = {
seconds = 1717084800
},
endTime = {
seconds = 1717689599
},
showEndTime = {
seconds = 1717689599
},
showBeginTime = {
seconds = 1717084800
}
},
labelId = 10,
backgroundUrl = {
"xuanzhuanmumawenan.astc"
},
lowVersion = "1.2.100.90",
activityName = "童话乐园",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56360,
56361
},
titleType = 0,
platforms = v26
},
[463] = {
id = 463,
timeInfo = {
beginTime = {
seconds = 1717084800
},
endTime = {
seconds = 1717689599
},
showEndTime = {
seconds = 1717689599
},
showBeginTime = {
seconds = 1717084800
}
},
labelId = 11,
backgroundUrl = {
"hashiqinongchang.astc"
},
lowVersion = "1.2.100.90",
activityName = "种菜星搭子",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56362
},
titleType = 0,
platforms = v26
},
[464] = {
id = 464,
timeInfo = {
beginTime = {
seconds = 1717084800
},
endTime = {
seconds = 1717689599
},
showEndTime = {
seconds = 1717689599
},
showBeginTime = {
seconds = 1717084800
}
},
labelId = 12,
backgroundUrl = {
"diaoyumei001.astc"
},
lowVersion = "1.2.100.90",
activityName = "娱乐情报站",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56363
},
titleType = 0,
platforms = v26
},
[470] = {
id = 470,
timeInfo = {
beginTime = {
seconds = 1717776000
},
endTime = {
seconds = 1743523199
},
showEndTime = {
seconds = 1743523199
},
showBeginTime = {
seconds = 1717776000
}
},
labelId = 6,
lowVersion = "1.3.6.1",
activityName = "欢夏签到礼",
activityDesc = "稀有头饰粽饱饱免费配送中！",
activityUIDetail = "UI_SignIn_NewaYear_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56370
},
activityNameType = "ANTSevenSign",
titleType = 1,
platforms = v26
},
[471] = {
id = 471,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1717776000
},
endTime = {
seconds = 1718899199
},
showEndTime = {
seconds = 1718899199
},
showBeginTime = {
seconds = 1717776000
}
},
labelId = 1,
backgroundUrl = {
"duanwushuiyitu.astc"
},
lowVersion = "1.3.6.1",
activityName = "粽享星活力",
activityParam = {
24,
3142,
557063
},
activityDesc = "唤醒夏日穿搭力，盛夏清爽假日出击！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56371,
56372,
56373,
56374,
56375
},
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3142
},
platforms = v26
},
[472] = {
id = 472,
timeInfo = {
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1718294399
},
showEndTime = {
seconds = 1718294399
},
showBeginTime = {
seconds = 1717689600
}
},
labelId = 3,
backgroundUrl = {
"xiayizhanweilai.astc"
},
lowVersion = "1.3.6.1",
activityName = "下一站未来",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56376,
56377,
56378,
56379
},
titleType = 0,
platforms = v26
},
[473] = {
id = 473,
activityType = "ATTeamRank",
timeInfo = {
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1718294399
},
showEndTime = {
seconds = 1718294399
},
showBeginTime = {
seconds = 1717689600
}
},
labelId = 4,
lowVersion = "1.3.6.1",
activityName = [[赛季新方向
排位双重礼]],
activityDesc = "打排位不掉星及额外加分！",
activityUIDetail = "UI_TeamActi_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56380
},
activityNameType = "ANTTeamRank",
titleType = 0,
platforms = v26
},
[474] = {
id = 474,
activityType = "ATIntelligenceStation",
timeInfo = {
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1748447999
},
showEndTime = {
seconds = 1748447999
},
showBeginTime = {
seconds = 1747324800
}
},
labelId = 2,
lowVersion = "1.3.6.1",
activityName = "娱乐情报站",
activityParam = {
1,
3135
},
activityUIDetail = "UI_Activity_GameModeRecommend",
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56381
},
showInCenter = true,
activityNameType = "ANTIntelligenceStation",
titleType = 0,
activityBeginCleanCoin = {
3135
},
platforms = v26
},
[475] = {
id = 475,
timeInfo = {
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1719503999
},
showEndTime = {
seconds = 1719503999
},
showBeginTime = {
seconds = 1717689600
}
},
labelId = 3,
backgroundUrl = {
"muchangwuyu.astc"
},
lowVersion = "1.3.6.1",
activityName = "牧场物语",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56382
},
titleType = 0,
platforms = v26
},
[476] = {
id = 476,
timeInfo = {
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1718553599
},
showEndTime = {
seconds = 1718553599
},
showBeginTime = {
seconds = 1717689600
}
},
labelId = 3,
backgroundUrl = {
"baibianlangrenxiu.astc"
},
lowVersion = "1.3.6.1",
activityName = "百变狼人秀",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56383,
56384,
56385,
56386
},
titleType = 0,
platforms = v26
},
[480] = {
id = 480,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1718294400
},
endTime = {
seconds = 1719503999
},
showEndTime = {
seconds = 1719503999
},
showBeginTime = {
seconds = 1718294400
}
},
labelId = 2,
backgroundUrl = {
"hanfutuliandong.astc"
},
lowVersion = "1.3.7.1",
activityName = [[星梦殷商
甲骨探秘]],
activityParam = {
24,
3143,
557106
},
activityDesc = "元梦之星x甲骨文数字焕活联动开启！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56387,
56388,
56389
},
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3143
},
platforms = v26
},
[481] = {
id = 481,
timeInfo = {
beginTime = {
seconds = 1718294400
},
endTime = {
seconds = 1718899199
},
showEndTime = {
seconds = 1718899199
},
showBeginTime = {
seconds = 1718294400
}
},
labelId = 3,
backgroundUrl = {
"haitanduixingxing.astc"
},
lowVersion = "1.3.7.1",
activityName = "梦寻天鹅湖",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56390,
56391
},
titleType = 0,
platforms = v26
},
[482] = {
id = 482,
timeInfo = {
beginTime = {
seconds = 1718294400
},
endTime = {
seconds = 1718899199
},
showEndTime = {
seconds = 1718899199
},
showBeginTime = {
seconds = 1718294400
}
},
labelId = 4,
backgroundUrl = {
"wujinrouge.astc"
},
lowVersion = "1.3.7.1",
activityName = "星宝守卫战",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56392
},
titleType = 0,
platforms = v26
},
[483] = {
id = 483,
timeInfo = {
beginTime = {
seconds = 1718294400
},
endTime = {
seconds = 1718899199
},
showEndTime = {
seconds = 1718899199
},
showBeginTime = {
seconds = 1718294400
}
},
labelId = 2,
backgroundUrl = {
"LINEFRIENDS.astc"
},
lowVersion = "1.3.7.1",
activityName = "迎接星朋友",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56393,
56394
},
titleType = 0,
platforms = v26
},
[485] = {
id = 485,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1720972799
},
showEndTime = {
seconds = 1720972799
},
showBeginTime = {
seconds = 1718899200
}
},
labelId = 7,
backgroundUrl = {
"guoyuan12345.astc"
},
lowVersion = "1.3.7.53",
activityName = "纯果乐一夏",
activityParam = {
24,
3144
},
activityDesc = "纯果缤纷乐一夏！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56400,
56401,
56402
},
activityNameType = v23,
activityShopType = v28,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3144
},
platforms = v26
},
[486] = {
id = 486,
timeInfo = {
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1719503999
},
showEndTime = {
seconds = 1719503999
},
showBeginTime = {
seconds = 1718899200
}
},
labelId = 2,
backgroundUrl = {
"mengxiangqihang.astc"
},
lowVersion = "1.3.7.1",
activityName = "梦想正启航",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56403,
56404
},
titleType = 0,
platforms = v26
},
[487] = {
id = 487,
timeInfo = {
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1719503999
},
showEndTime = {
seconds = 1719503999
},
showBeginTime = {
seconds = 1718899200
}
},
labelId = 3,
backgroundUrl = {
"yuledamanguan.astc"
},
lowVersion = "1.3.7.1",
activityName = "娱乐大满贯",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56405,
56406
},
titleType = 0,
platforms = v26
},
[488] = {
id = 488,
timeInfo = {
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1719503999
},
showEndTime = {
seconds = 1719503999
},
showBeginTime = {
seconds = 1718899200
}
},
labelId = 4,
backgroundUrl = {
"langrenxinshenfenfayi.astc"
},
lowVersion = "1.3.7.1",
activityName = "放松一下吧",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56407
},
titleType = 0,
platforms = v26
},
[600] = {
id = 600,
activityType = "ATDreamNewStar",
timeInfo = {
beginTime = {
seconds = 1689782400
},
endTime = {
seconds = 1730822399
},
showEndTime = {
seconds = 1730822399
},
showBeginTime = {
seconds = 1689782400
}
},
labelId = 99,
backgroundUrl = {
"T_Activity_TestTitle_5.astc"
},
activityName = "摇人有礼",
activityDesc = "关系链手拉手活动",
activityUIDetail = "UI_Recruit_Main",
tagId = 1,
activityTaskGroup = {
58320,
58310
},
activityNameType = "ANTSnsInvite",
titleType = 0,
platforms = v26
},
[490] = {
id = 490,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1720972799
},
showEndTime = {
seconds = 1720972799
},
showBeginTime = {
seconds = 1719504000
}
},
labelId = 2,
backgroundUrl = {
"banzhounianbaobaoku.astc"
},
lowVersion = "1.3.7.1",
activityName = [[庆7月5日
星宝半岁礼]],
activityParam = {
24,
3152,
557166
},
activityDesc = "不管几岁，可爱万岁！守护我的星宝！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56410,
56411,
56412,
56413
},
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3152
},
platforms = v26
},
[491] = {
id = 491,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1719763200
},
endTime = {
seconds = 1720367999
},
showEndTime = {
seconds = 1720367999
},
showBeginTime = {
seconds = 1719763200
}
},
labelId = 5,
backgroundUrl = {
"liangzhuliandong2.astc"
},
lowVersion = "1.3.7.1",
activityName = "跨越千年旅",
activityParam = {
25,
3153
},
activityDesc = "与良渚文明跨越五千年之旅！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56414
},
activityNameType = v23,
activityShopType = v27,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3153
},
platforms = v26
},
[492] = {
id = 492,
timeInfo = {
beginTime = {
seconds = 1719590400
},
endTime = {
seconds = 1720195199
},
showEndTime = {
seconds = 1720195199
},
showBeginTime = {
seconds = 1719590400
}
},
labelId = 8,
backgroundUrl = {
"xiaofeichai.astc"
},
lowVersion = "1.3.7.74",
activityName = "新品来献礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56415
},
titleType = 0,
platforms = v26
},
[495] = {
id = 495,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1721923199
},
showEndTime = {
seconds = 1721923199
},
showBeginTime = {
seconds = 1719504000
}
},
labelId = 10,
backgroundUrl = {
"huanlelangrenju.astc"
},
activityName = "欢乐狼人局",
activityParam = {
24,
3154,
557186
},
activityDesc = "攒灵石，拿福利，候迎通灵师！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56421,
56416,
56417,
56418
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3154
},
platforms = v26
},
[496] = {
id = 496,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1720367999
},
showEndTime = {
seconds = 1720367999
},
showBeginTime = {
seconds = 1719504000
}
},
labelId = 9,
backgroundUrl = {
"shourenliandong.astc"
},
lowVersion = "1.3.7.74",
activityName = "兽人补给站",
activityParam = {
32,
3155
},
activityDesc = "兽人必须死玩法上线！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56419,
56420
},
activityNameType = v23,
activityShopType = {
32
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3155
},
platforms = v26
},
[500] = {
id = 500,
activityType = v2,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 1,
backgroundUrl = {
"nideshengyin.astc"
},
lowVersion = "1.3.7.95",
activityName = [[你的声音
我听得见]],
activityParam = {
24,
3156,
557204
},
activityDesc = "星宝半岁礼，感谢遇见你！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56422,
56423,
56432,
56433
},
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3156
},
platforms = v26
},
[501] = {
id = 501,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 6,
backgroundUrl = {
"xinpinlaixianli.astc"
},
lowVersion = "1.3.7.95",
activityName = "乐园同行礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56424,
56425
},
titleType = 0,
platforms = v26
},
[502] = {
id = 502,
activityType = "ATTeamRank",
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 7,
lowVersion = "1.3.7.74",
activityName = "排位双重礼",
activityDesc = "打排位不掉星及额外加分！",
activityUIDetail = "UI_TeamActi_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56426
},
activityNameType = "ANTTeamRank",
titleType = 0,
platforms = v26
},
[503] = {
id = 503,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
backgroundUrl = {
"dazitongxing.astc"
},
lowVersion = "1.3.7.74",
activityName = "搭子同行",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56427,
56428
},
titleType = 0,
activitySubName = "小标题在测试",
platforms = v26
},
[504] = {
id = 504,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1720972799
},
showEndTime = {
seconds = 1720972799
},
showBeginTime = {
seconds = **********
}
},
labelId = 8,
backgroundUrl = {
"tonglingshi.astc"
},
jumpId = 25,
lowVersion = "1.3.7.94",
activityName = "通灵师降临",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56429,
56430,
56431
},
clientParams = {
"获取更多卡"
},
titleType = 0,
platforms = v26
},
[505] = {
id = 505,
timeInfo = {
beginTime = {
seconds = 1688313600
},
endTime = {
seconds = 1688486400
},
showEndTime = {
seconds = 1688486400
},
showBeginTime = {
seconds = 1688313600
}
},
labelId = 4,
backgroundUrl = {
"chechedengchang2.astc"
},
lowVersion = "1.3.7.103",
activityName = "车车登场",
activityUIDetail = v16,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56434
},
activityNameType = v21,
titleType = 0,
pakGroupId = 20031,
platforms = v26
},
[701] = {
id = 701,
activityType = "ATRecruitOrder",
timeInfo = {
beginTime = {
seconds = 1721354400
},
endTime = {
seconds = 1724342399
},
showEndTime = {
seconds = 1724342399
},
showBeginTime = {
seconds = 1721354400
}
},
labelId = 11,
backgroundUrl = {
"T_Convened_Img_Background.astc"
},
lowVersion = "1.3.12.15",
activityName = "召集星搭子",
activityDesc = "得非凡外观",
activityUIDetail = "UI_Activity_Convened_MainView",
tagId = 1,
activityTaskGroup = {
70002,
70003
},
showInCenter = true,
activityNameType = v22,
titleType = 0,
activityBeginCleanCoin = {
3159
},
activitySubName = "送你稀有时装",
platforms = v26
},
[812] = {
id = 812,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
backgroundUrl = {
"dazitongxing.astc"
},
activityName = "峡谷祈愿礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 10,
activityTaskGroup = {
65001
},
showInCenter = true,
platforms = v26
},
[813] = {
id = 813,
activityType = "ATCallWerewolf",
timeInfo = {
beginTime = {
seconds = 1722441600
},
endTime = {
seconds = 1724342399
},
showEndTime = {
seconds = 1724342399
},
showBeginTime = {
seconds = 1722441600
}
},
labelId = 3,
backgroundUrl = {
"langrenqixi.astc"
},
activityName = "狼人七夕礼",
activityParam = {
2,
3164,
670007
},
activityUIDetail = "UI_Activity_QiXiView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
65007,
65006,
65002,
65003,
65004,
65005
},
showInCenter = true,
activityNameType = "ANTCallWerewolf",
activitySubName = "领保镖新身份",
platforms = v26
},
[814] = {
id = 814,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 4,
backgroundUrl = {
"dazitongxing.astc"
},
lowVersion = "1.3.7.1",
activityName = "峡谷祈愿礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 10,
activityTaskGroup = {
81401
},
showInCenter = true,
platforms = v26
},
[507] = {
id = 507,
activityType = "ATThemeAdventure",
timeInfo = {
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1727711999
},
showEndTime = {
seconds = 1727711999
},
showBeginTime = {
seconds = 1721318400
}
},
labelId = 4,
lowVersion = "1.0.0.0",
activityName = "星界奇遇",
activityDesc = "玩法获得奇遇加成，完成奇遇任务可获得奇遇盲盒哦!",
activityUIDetail = "UI_ThemeAdventure_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
66001,
66002,
66003
},
showInCenter = true,
activityNameType = "ANTThemeAdventure",
activitySubName = "星界奇遇",
activityGroup = v32,
platforms = v26
},
[815] = {
id = 815,
activityType = "ATBookOfFriends",
timeInfo = {
beginTime = {
seconds = 1722470400
},
endTime = {
seconds = 1725119999
},
showEndTime = {
seconds = 1725119999
},
showBeginTime = {
seconds = 1722470400
}
},
labelId = 4,
lowVersion = "1.3.12.91",
activityName = "花房建造礼",
activityUIDetail = "UI_Activity_GreenHouse_MainView",
isInBottom = 1,
activityRuleId = 301,
tagId = 1,
showInCenter = true,
activityNameType = "ANTWish",
activitySubName = "免费农场外观",
activityGroup = v32,
platforms = v26
},
[558] = {
id = 558,
activityType = "ATTeamRank",
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1724947200
}
},
labelId = 7,
lowVersion = "1.3.7.74",
activityName = "排位双重礼",
activityDesc = "打排位不掉星及额外加分！",
activityUIDetail = "UI_TeamActi_MainView",
isInBottom = 1,
tagId = 1,
activityNameType = "ANTTeamRank",
platforms = v26
},
[525] = {
id = 525,
timeInfo = {
beginTime = {
seconds = 1721750400
},
endTime = {
seconds = 1722527999
},
showEndTime = {
seconds = 1722527999
},
showBeginTime = {
seconds = 1721750400
}
},
labelId = 2,
backgroundUrl = {
"koudai2.astc"
},
lowVersion = "1.3.12.21",
activityName = "口袋领到鼓",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56480,
56481,
56482,
56483,
56484
},
showInCenter = true,
titleType = 0,
activitySubName = "白名单测试",
platforms = v26
},
[526] = {
id = 526,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1721750400
},
endTime = {
seconds = 1722787199
},
showEndTime = {
seconds = 1722787199
},
showBeginTime = {
seconds = 1721750400
}
},
labelId = 3,
backgroundUrl = {
"xiangsi.astc"
},
lowVersion = "1.3.12.21",
activityName = "相思时光礼",
activityParam = {
24,
3160
},
activityDesc = "全新语音包免费送！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56485
},
showInCenter = true,
activityNameType = v23,
activityShopType = v28,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3160
},
activitySubName = "送语音包",
platforms = v26
},
[527] = {
id = 527,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1721750400
},
endTime = {
seconds = 1725119999
},
showEndTime = {
seconds = 1725119999
},
showBeginTime = {
seconds = 1721750400
}
},
labelId = 6,
backgroundUrl = {
"zaomengzhongzhou.astc"
},
lowVersion = "1.3.12.21",
activityName = "造梦中轴",
activityParam = {
24,
3161,
557390
},
activityDesc = "送定制称号！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56486,
56487
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3161
},
activitySubName = "送定制称号",
platforms = v26
},
[532] = {
id = 532,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1722787199
},
showEndTime = {
seconds = 1722787199
},
showBeginTime = {
seconds = 1721923200
}
},
labelId = 2,
backgroundUrl = {
"langrenxinhudong3.astc"
},
lowVersion = "1.3.12.21",
activityName = "狼人新互动",
activityParam = {
24,
3162,
680042
},
activityDesc = "做任务，攒积分，领狼人专属头像",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
68009,
68010,
68011,
68012
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3162
},
activitySubName = "领狼人头像",
platforms = v26
},
[528] = {
id = 528,
activityType = "ATTrainingCamp",
timeInfo = {
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1750607999
},
showEndTime = {
seconds = 1750607999
},
showBeginTime = {
seconds = 1721923200
}
},
labelId = 2,
activityName = "运动特训营",
activityParam = {
5,
15,
3171
},
activityUIDetail = "UI_SportsTrainingCamp_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56495,
56488,
56489,
56490,
56491,
56492,
56493,
56494,
56496,
56497
},
showInCenter = true,
activityNameType = "ANTTrainingCamp",
titleType = 0,
activityBeginCleanCoin = {
3172
},
activitySubName = "时团联动时装",
platforms = v26
},
[535] = {
id = 535,
timeInfo = {
beginTime = {
seconds = 1722528000
},
endTime = {
seconds = 1723132799
},
showEndTime = {
seconds = 1723132799
},
showBeginTime = {
seconds = 1722528000
}
},
labelId = 2,
backgroundUrl = {
"koudai3.astc"
},
lowVersion = "1.3.12.21",
activityName = "口袋领到鼓",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56505,
56506,
56507,
56508
},
showInCenter = true,
titleType = 0,
activitySubName = "周周领星愿币",
platforms = v26
},
[536] = {
id = 536,
timeInfo = {
beginTime = {
seconds = 1722528000
},
endTime = {
seconds = 1723132799
},
showEndTime = {
seconds = 1723132799
},
showBeginTime = {
seconds = 1722528000
}
},
labelId = 3,
backgroundUrl = {
"nvmingxing.astc"
},
lowVersion = "1.3.12.21",
activityName = "女明星来鸭",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56509
},
showInCenter = true,
titleType = 0,
activitySubName = "免费Toby头像",
platforms = v26
},
[537] = {
id = 537,
activityType = "ATSuperLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1722528000
},
endTime = {
seconds = 1742054399
},
showEndTime = {
seconds = 1742054399
},
showBeginTime = {
seconds = 1722528000
}
},
labelId = 4,
backgroundUrl = {
"quansuqianjin.astc"
},
lowVersion = "1.2.12.21",
activityName = "全速前进中",
activityParam = {
3163,
10
},
activityUIDetail = "UI_Activity_DrawLinear_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56510,
56511
},
showInCenter = true,
activityNameType = "ANTSuperLinearRedeem",
titleType = 0,
activityBeginCleanCoin = {
3163
},
activitySubName = "领乒乓球头饰",
platforms = v26
},
[8000] = {
id = 8000,
activityType = v2,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1723996799
},
showEndTime = {
seconds = 1723996799
},
showBeginTime = {
seconds = **********
}
},
labelId = 1,
backgroundUrl = {
"nideshengyin.astc"
},
activityName = "线性测试",
activityParam = {
24,
3156,
800006
},
activityDesc = "星宝半岁礼，感谢遇见你！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
80000,
80001
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3156
},
activitySubName = "倩倩专用",
platforms = v26
},
[8001] = {
id = 8001,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1723996799
},
showEndTime = {
seconds = 1723996799
},
showBeginTime = {
seconds = 1719504000
}
},
labelId = 9,
backgroundUrl = {
"shourenliandong.astc"
},
activityName = "兽人补给站",
activityParam = {
32,
3155
},
activityDesc = "兽人必须死玩法上线！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
80004,
80005
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
32
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3155
},
activitySubName = "倩倩专用",
platforms = v26
},
[541] = {
id = 541,
timeInfo = {
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1723737599
},
showEndTime = {
seconds = 1723737599
},
showBeginTime = {
seconds = 1723132800
}
},
labelId = 2,
backgroundUrl = {
"koudai4.astc"
},
lowVersion = "1.3.12.21",
activityName = "口袋领到鼓",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56513,
56514,
56515,
56516
},
showInCenter = true,
titleType = 0,
activitySubName = "周周领星愿币",
platforms = v26
},
[542] = {
id = 542,
activityType = "ATMonopoly",
timeInfo = {
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1743091199
},
showEndTime = {
seconds = 1743091199
},
showBeginTime = {
seconds = 1723132800
}
},
labelId = 3,
lowVersion = "1.3.12.21",
activityName = "奇妙冒险旅",
activityParam = {
3192
},
activityUIDetail = "UI_Activity_MonopolyView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
61016,
61017
},
showInCenter = true,
activityNameType = "ANTMonopoly",
titleType = 0,
activityBeginCleanCoin = {
3192
},
activitySubName = "全新动作试用",
platforms = v26
},
[550] = {
id = 550,
activityType = "ATDreamNewStar",
timeInfo = {
beginTime = {
seconds = 1726110000
},
endTime = {
seconds = 1730390399
},
showEndTime = {
seconds = 1730390399
},
showBeginTime = {
seconds = 1726110000
}
},
labelId = 99,
backgroundUrl = {
"T_Activity_TestTitle_5.astc"
},
activityName = "摇人再有礼",
activityDesc = "关系链手拉手活动",
activityUIDetail = "UI_WXGame_LobbyView_InvitePop",
activityRuleId = 213,
tagId = 1,
activityTaskGroup = {
80301,
80302,
80303,
80304,
80305,
80306
},
activityNameType = "ANTWXInviteFriends",
titleType = 0,
activitySubName = "第二期测试",
platforms = v26
},
[553] = {
id = 553,
activityType = "ATDreamNewStar",
timeInfo = {
beginTime = {
seconds = 1739847600
},
endTime = {
seconds = 1746028799
},
showEndTime = {
seconds = 1746028799
},
showBeginTime = {
seconds = 1739847600
}
},
labelId = 99,
backgroundUrl = {
"T_Activity_TestTitle_5.astc"
},
activityName = "摇人又有礼",
activityDesc = "关系链手拉手活动",
activityUIDetail = "UI_WXGame_LobbyView_InvitePop",
activityRuleId = 335,
tagId = 1,
activityTaskGroup = {
80401,
80402
},
activityNameType = "ANTWXInviteFriends",
titleType = 0,
activitySubName = "第三期测试",
platforms = v26
},
[602] = {
id = 602,
activityType = "ATWish",
timeInfo = {
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1732723199
},
showEndTime = {
seconds = 1732723199
},
showBeginTime = {
seconds = 1723132800
}
},
labelId = 4,
activityName = "心愿全实现",
activityParam = {
3151,
7001
},
activityUIDetail = "UI_Activity_WishesCameTrue_MainView",
isInBottom = 1,
activityRuleId = 213,
tagId = 1,
activityTaskGroup = {
61014,
61015
},
showInCenter = true,
activityNameType = "ANTWish",
titleType = 0,
activityBeginCleanCoin = {
3151
},
activitySubName = "第二期测试",
platforms = v26
},
[545] = {
id = 545,
timeInfo = {
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1724342399
},
showEndTime = {
seconds = 1724342399
},
showBeginTime = {
seconds = 1723737600
}
},
labelId = 2,
backgroundUrl = {
"koudai5.astc"
},
lowVersion = "1.3.12.90",
activityName = "口袋领到鼓",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56520,
56521,
56522
},
showInCenter = true,
titleType = 0,
activitySubName = "周周领星愿币",
platforms = v26
},
[547] = {
id = 547,
timeInfo = {
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1724947199
},
showEndTime = {
seconds = 1724947199
},
showBeginTime = {
seconds = 1724342400
}
},
labelId = 2,
backgroundUrl = {
"koudai6.astc"
},
lowVersion = "1.3.12.138",
activityName = "口袋领到鼓",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56528,
56529
},
showInCenter = true,
titleType = 0,
activitySubName = "周周领星愿币",
platforms = v26
},
[548] = {
id = 548,
activityType = "ATAnimalHandbook",
timeInfo = {
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1725811199
},
showEndTime = {
seconds = 1725811199
},
showBeginTime = {
seconds = 1724342400
}
},
labelId = 1,
lowVersion = "1.3.12.90",
activityName = "牧场物语",
activityUIDetail = "UI_PastureStories_EntryView",
tagId = 1,
activityTaskGroup = {
56530,
56531
},
showInCenter = true,
activityNameType = "ANTAnimalHandbook",
titleType = 1,
activitySubName = "送盼盼熊猫",
platforms = v26
},
[551] = {
id = 551,
timeInfo = {
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1724947199
},
showEndTime = {
seconds = 1724947199
},
showBeginTime = {
seconds = 1724342400
}
},
labelId = 99,
backgroundUrl = {
"koudai5.astc"
},
lowVersion = "1.3.12.90",
activityName = "兑换测试1",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56525,
56526
},
showInCenter = true,
titleType = 0,
activitySubName = "获取兑换物",
platforms = v26
},
[552] = {
id = 552,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1725551999
},
showEndTime = {
seconds = 1725551999
},
showBeginTime = {
seconds = 1724342400
}
},
labelId = 99,
backgroundUrl = {
"shourenliandong.astc"
},
activityName = "兑换测试2",
activityParam = {
24,
3168
},
activityDesc = "兑换测试（去掉开始清空货币）",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56527
},
showInCenter = true,
activityNameType = v23,
activityShopType = v28,
clientParams = v24,
titleType = 0,
activitySubName = "不清空兑换物",
platforms = v26
},
[555] = {
id = 555,
timeInfo = {
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1748447999
},
showEndTime = {
seconds = 1748447999
},
showBeginTime = {
seconds = 1747324800
}
},
labelId = 2,
activityName = "中秋多图分享测试",
activityParam = {
6
},
activityDesc = "完成下列任务即可领取奖励",
activityUIDetail = "UI_Activity_MultiImageSharing_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56539,
56540,
56541,
56542,
56543,
56544,
56545,
56546
},
showInCenter = true,
titleType = 0,
activitySubName = "猜灯谜送头饰",
platforms = v26
},
[556] = {
id = 556,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1725811199
},
showEndTime = {
seconds = 1725811199
},
showBeginTime = {
seconds = 1724947200
}
},
labelId = 1,
backgroundUrl = {
"xiuxianshike.astc"
},
lowVersion = "1.3.7.121",
activityName = "休闲时刻",
activityParam = {
32,
3401,
557696
},
activityDesc = "任务减负，免费领盼盼吐司包！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
56550,
56551,
56552,
56553
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
32
},
titleType = 0,
activityBeginCleanCoin = {
3401
},
activitySubName = "送全新背饰",
platforms = v26
},
[702] = {
id = 702,
activityType = "ATRecruitOrder",
timeInfo = {
beginTime = {
seconds = 1724983200
},
endTime = {
seconds = 1742659199
},
showEndTime = {
seconds = 1742659199
},
showBeginTime = {
seconds = 1724983200
}
},
labelId = 11,
backgroundUrl = {
"T_Convened_Img_Background.astc"
},
lowVersion = "1.3.12.15",
activityName = "召集星搭子",
activityDesc = "得非凡外观",
activityUIDetail = "UI_Activity_Convened_MainView",
tagId = 1,
activityTaskGroup = {
70004,
70005
},
showInCenter = true,
activityNameType = v22,
titleType = 0,
activityBeginCleanCoin = {
3159
},
activitySubName = "送你稀有时装",
platforms = v26
},
[810] = {
id = 810,
activityType = "ATTaskRunOnActivitySvr",
timeInfo = {
beginTime = {
seconds = 1721404800
},
endTime = {
seconds = 1724947199
},
showEndTime = {
seconds = 1724947199
},
showBeginTime = {
seconds = 1721404800
}
},
labelId = 11,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "峡谷英雄帖",
activityParam = {
50021
},
activityUIDetail = "UI_Arena_HerosRoad_Main",
isInBottom = 1,
tagId = 13,
showInCenter = true,
titleType = 0,
platforms = v26
},
[30000] = {
id = 30000,
timeInfo = {
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1724342399
},
showEndTime = {
seconds = 1724342399
},
showBeginTime = {
seconds = 1723737600
}
},
labelId = 2,
backgroundUrl = {
"chijisunshangxiang_2.astc"
},
lowVersion = "1.3.12.117",
activityName = "峡谷又上新",
activityUIDetail = v17,
isInBottom = 1,
tagId = 11,
activityTaskGroup = {
1001984,
1001985
},
showInCenter = true,
titleType = 0,
activitySubName = "新英雄新玩法",
platforms = v26
},
[30001] = {
id = 30001,
timeInfo = {
beginTime = {
seconds = 1724083200
},
endTime = {
seconds = 1724947199
},
showEndTime = {
seconds = 1724947199
},
showBeginTime = {
seconds = 1724083200
}
},
labelId = 1,
backgroundUrl = {
"xiaguhuanmeng2_2.astc"
},
lowVersion = "1.3.12.117",
activityName = "峡谷璀璨秀",
activityUIDetail = v17,
isInBottom = 1,
tagId = 11,
activityTaskGroup = {
1001986,
1001987
},
showInCenter = true,
titleType = 0,
activitySubName = "赠送20抽福利",
platforms = v26
},
[557] = {
id = 557,
timeInfo = {
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1727625599
},
showEndTime = {
seconds = 1727625599
},
showBeginTime = {
seconds = 1724342400
}
},
labelId = 99,
backgroundUrl = {
"koudai2.astc"
},
lowVersion = "1.3.12.90",
activityName = "好好鸭测试",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56554
},
showInCenter = true,
titleType = 0,
activitySubName = "任务测试",
platforms = v26
},
[565] = {
id = 565,
activityType = "ATLanternRiddles",
timeInfo = {
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1726156800
}
},
labelId = 2,
backgroundUrl = {
"koudai6.astc"
},
lowVersion = "1.3.18.20",
activityName = "喜迎中秋礼",
activityDesc = "团圆;幸福;好运;顺意",
activityUIDetail = "UI_Activity_MidAutumnLantern_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56562,
56563,
56564,
56565
},
showInCenter = true,
activityNameType = "ATFLanternRiddles",
titleType = 0,
activitySubName = "送头饰送表情",
platforms = v26
},
[567] = {
id = 567,
timeInfo = {
beginTime = {
seconds = 1726243200
},
endTime = {
seconds = 1726847999
},
showEndTime = {
seconds = 1726847999
},
showBeginTime = {
seconds = 1726243200
}
},
labelId = 6,
backgroundUrl = {
"kuayuezhangai.astc"
},
activityName = "绑定有好礼",
activityUIDetail = v16,
tagId = 1,
activityTaskGroup = {
56568
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
platforms = v26
},
[568] = {
id = 568,
activityType = "ATAnimalHandbook",
timeInfo = {
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1727711999
},
showEndTime = {
seconds = 1727711999
},
showBeginTime = {
seconds = 1724342400
}
},
labelId = 1,
lowVersion = "1.3.12.90",
activityName = "牧场物语",
activityUIDetail = "UI_PastureStories_EntryView",
tagId = 1,
activityTaskGroup = {
56569,
56570
},
showInCenter = true,
activityNameType = "ANTAnimalHandbook",
titleType = 1,
activitySubName = "送盼盼熊猫",
platforms = v26
},
[605] = {
id = 605,
activityType = "ATNationalDayHundredDraws",
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1765987199
},
showEndTime = {
seconds = 1765987199
},
showBeginTime = {
seconds = 1724947200
}
},
labelId = 1,
activityName = "百衣补贴季",
activityParam = {
20000001,
20000001
},
activityDesc = "充能得能量值与外观奖励，充满<NationalDay>必得</>全新非凡时装",
activityUIDetail = "UI_NationalDay_MainView",
tagId = 1,
activityTaskGroup = {
60500,
60501,
60502,
60503,
60504,
60505,
60506
},
showInCenter = true,
activityNameType = "ANTNationalDayHundredDraws",
clientParams = {
"11,10,9"
},
titleType = 0,
activitySubName = "实现换装自由",
currencyCfg = {
2071
},
taskGroupsDrawBase = {
60501,
60500
},
platforms = v26
},
[606] = {
id = 606,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1727366400
},
endTime = {
seconds = 1729439999
},
showEndTime = {
seconds = 1729439999
},
showBeginTime = {
seconds = 1727366400
}
},
labelId = 12,
backgroundUrl = {
"wuhuangmao.astc"
},
activityName = "吾皇猫来袭",
activityParam = {
24,
3408
},
activityDesc = "吾皇猫联动福利派送中！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60507,
60508
},
showInCenter = true,
activityNameType = v23,
activityShopType = v28,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3408
},
activitySubName = "全新联动时装",
platforms = v26
},
[607] = {
id = 607,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1727366400
},
endTime = {
seconds = 1728316799
},
showEndTime = {
seconds = 1728316799
},
showBeginTime = {
seconds = 1727366400
}
},
labelId = 11,
backgroundUrl = {
"baiyi.astc"
},
lowVersion = "1.3.18.69",
activityName = "百衣线索礼",
activityParam = {
32,
3409,
605048
},
activityDesc = "抢先领百衣补贴抽奖券！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60509,
60510,
60511,
60512,
60513,
60514
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
32
},
titleType = 0,
activityBeginCleanCoin = {
3409
},
activitySubName = "前瞻福利来袭",
platforms = v26
},
[609] = {
id = 609,
activityType = "ATAnimalHandbook",
timeInfo = {
beginTime = {
seconds = 1727971200
},
endTime = {
seconds = 1736351999
},
showEndTime = {
seconds = 1736351999
},
showBeginTime = {
seconds = 1727971200
}
},
labelId = 2,
lowVersion = "1.3.18.69",
activityName = "牧场物语",
activityUIDetail = "UI_PastureStories_EntryView",
tagId = 1,
activityTaskGroup = {
60517,
60518
},
showInCenter = true,
activityNameType = "ANTAnimalHandbook",
titleType = 1,
activitySubName = "送虎小妞时装",
platforms = v26
},
[901] = {
id = 901,
activityType = "ATWishingTree",
timeInfo = {
beginTime = {
seconds = 1727625600
},
endTime = {
seconds = 1730908799
},
showEndTime = {
seconds = 1730908799
},
showBeginTime = {
seconds = 1727625600
}
},
labelId = 1,
activityName = "许愿树",
activityUIDetail = "UI_Activity_WishingTreeView",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTWishingTree",
titleType = 0,
activitySubName = "奖励五选二",
platforms = v26
},
[902] = {
id = 902,
activityType = "ATUpdateForesight",
timeInfo = {
beginTime = {
seconds = 1730692800
},
endTime = {
seconds = 1732204799
},
showEndTime = {
seconds = 1732204799
},
showBeginTime = {
seconds = 1730692800
}
},
labelId = 7,
activityName = "隐守测试",
activityParam = {
1000002,
1000003
},
activityUIDetail = "UI_InvisibleGuardian_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
700012,
700011,
700013
},
showInCenter = true,
activityNameType = "ANTUpdateForesightV1",
activitySubName = "送稀有头饰",
platforms = v26
},
[610] = {
id = 610,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1728316800
},
endTime = {
seconds = 1729439999
},
showEndTime = {
seconds = 1729439999
},
showBeginTime = {
seconds = 1728316800
}
},
labelId = 3,
backgroundUrl = {
"xiaotiandou.astc"
},
activityName = "小甜豆驾到",
activityParam = {
25,
3411
},
activityDesc = "免费领小甜豆联动福利！",
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60519,
60520,
60521
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3411
},
activitySubName = "登录领星愿币",
platforms = v26
},
[611] = {
id = 611,
activityType = "ATUpdateForesight",
timeInfo = {
beginTime = {
seconds = 1727402400
},
endTime = {
seconds = 1748707199
},
showEndTime = {
seconds = 1748707199
},
showBeginTime = {
seconds = 1727402400
}
},
labelId = 7,
activityName = "订阅新赛季",
activityParam = {
605059,
605061
},
activityUIDetail = "UI_NewVersion_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60523,
60522
},
showInCenter = true,
activityNameType = "ANTUpdateForesightV1",
activitySubName = "送稀有头饰",
platforms = v26
},
[612] = {
id = 612,
activityType = "ATWishingTree",
timeInfo = {
beginTime = {
seconds = 1729267200
},
endTime = {
seconds = 1729871999
},
showEndTime = {
seconds = 1729871999
},
showBeginTime = {
seconds = 1729267200
}
},
labelId = 3,
lowVersion = "1.3.26.3",
activityName = "许愿树",
activityUIDetail = "UI_Activity_WishingTreeView",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTWishingTree",
titleType = 0,
activitySubName = "奖励五选二",
platforms = v26
},
[613] = {
id = 613,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1730044799
},
showEndTime = {
seconds = 1730044799
},
showBeginTime = {
seconds = 1729180800
}
},
labelId = 2,
backgroundUrl = {
"daloushao.astc"
},
lowVersion = "1.3.26.3",
activityName = "大漏勺来也",
activityDesc = "新品抢先试用，陆续送永久！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60524,
60525,
60526,
60527,
60528
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
3415
},
titleType = 0,
activityBeginCleanCoin = {
32,
3415,
605072
},
activitySubName = "免费炫彩时装",
currencyCfg = {
32
},
platforms = v26
},
[616] = {
id = 616,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1730390399
},
showEndTime = {
seconds = 1730390399
},
showBeginTime = {
seconds = **********
}
},
labelId = 99,
backgroundUrl = {
"tonglingshi.astc"
},
activityName = "订阅测试",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60535,
60536
},
showInCenter = true,
titleType = 0,
activitySubName = "订阅测试",
platforms = v26
},
[910] = {
id = 910,
activityType = "ATTaskShopRaffle",
timeInfo = {
beginTime = {
seconds = 1735228800
},
endTime = {
seconds = 1736697599
},
showEndTime = {
seconds = 1736697599
},
showBeginTime = {
seconds = 1735228800
}
},
labelId = 1,
activityName = "吉星好运树",
activityParam = {
20000002,
20000002
},
activityUIDetail = "UI_Activity_GiftTree_MainView",
isInBottom = 1,
activityRuleId = 299,
tagId = 1,
activityTaskGroup = {
60680,
60681,
60682
},
showInCenter = true,
activityNameType = "ANTExchangeCoinTree",
activityShopType = {
156
},
clientParams = {
"11,10"
},
titleType = 0,
activityBeginCleanCoin = {
3458
},
activitySubName = "领松小雪时装",
currencyCfg = {
3458,
3457
},
taskGroupsDrawBase = {
60682
},
taskGroupsGetMore = {
60680,
60681
},
platforms = v26
},
[614] = {
id = 614,
activityType = "ATMinesweeper",
timeInfo = {
beginTime = {
seconds = 1737734400
},
endTime = {
seconds = 1739462399
},
showEndTime = {
seconds = 1739462399
},
showBeginTime = {
seconds = 1737734400
}
},
labelId = 1,
activityName = "乐园寻宝",
activityUIDetail = "UI_TreasureHunt_ActivityView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60529,
60530,
60531
},
showInCenter = true,
activityNameType = "ANTMinesweeper",
titleType = 0,
activityBeginCleanCoin = {
3417
},
activitySubName = "魔法南瓜来袭",
platforms = v26
},
[618] = {
id = 618,
timeInfo = {
beginTime = {
seconds = 1740585600
},
endTime = {
seconds = 1741622399
},
showEndTime = {
seconds = 1741622399
},
showBeginTime = {
seconds = 1740585600
}
},
labelId = 6,
backgroundUrl = {
"kuayuezhangai.astc"
},
activityName = "绑定有好礼",
activityUIDetail = v16,
tagId = 1,
activityTaskGroup = {
60540
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
activitySubName = "老单条TEST",
platforms = v26
},
[621] = {
id = 621,
activityType = "ATWishingTree",
timeInfo = {
beginTime = {
seconds = 1730476800
},
endTime = {
seconds = 1731081599
},
showEndTime = {
seconds = 1731081599
},
showBeginTime = {
seconds = 1730476800
}
},
labelId = 3,
lowVersion = "1.3.26.40",
activityName = "许愿树",
activityUIDetail = "UI_Activity_WishingTreeView",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTWishingTree",
titleType = 0,
activitySubName = "奖励五选二",
platforms = v26
},
[622] = {
id = 622,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1731859199
},
showEndTime = {
seconds = 1731859199
},
showBeginTime = {
seconds = 1730995200
}
},
labelId = 2,
backgroundUrl = {
"daloushao2.astc"
},
lowVersion = "1.3.26.71",
activityName = "大漏勺来也",
activityDesc = "新品抢先试用，陆续送永久！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60553,
60554,
60555,
60556,
60557
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
3426
},
titleType = 0,
activityBeginCleanCoin = {
32,
3426,
605110
},
activitySubName = "抢先知晓福利",
currencyCfg = {
32
},
platforms = v26
},
[623] = {
id = 623,
timeInfo = {
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1731859199
},
showEndTime = {
seconds = 1731859199
},
showBeginTime = {
seconds = 1730995200
}
},
labelId = 4,
backgroundUrl = {
"guanzhuvx.astc"
},
lowVersion = "1.3.26.71",
activityName = "关注不迷路",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60558,
60559
},
showInCenter = true,
titleType = 0,
activitySubName = "星愿币免费领",
platforms = v26
},
[624] = {
id = 624,
timeInfo = {
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1731859199
},
showEndTime = {
seconds = 1731859199
},
showBeginTime = {
seconds = 1730995200
}
},
labelId = 4,
backgroundUrl = {
"guanzhuqq.astc"
},
lowVersion = "1.3.26.71",
activityName = "关注不迷路",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60560,
60561
},
showInCenter = true,
titleType = 0,
activitySubName = "星愿币免费领",
platforms = v26
},
[625] = {
id = 625,
activityType = "ATWishingTree",
timeInfo = {
beginTime = {
seconds = 1731081600
},
endTime = {
seconds = 1731686399
},
showEndTime = {
seconds = 1731686399
},
showBeginTime = {
seconds = 1731081600
}
},
labelId = 3,
lowVersion = "1.3.26.71",
activityName = "许愿树",
activityUIDetail = "UI_Activity_WishingTreeView",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTWishingTree",
titleType = 0,
activitySubName = "送现金红包",
platforms = v26
},
[626] = {
id = 626,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1732463999
},
showEndTime = {
seconds = 1732463999
},
showBeginTime = {
seconds = 1731600000
}
},
labelId = 2,
backgroundUrl = {
"bianshen241115.astc"
},
lowVersion = "1.3.26.71",
activityName = "梦幻变身季",
activityDesc = "幻想永不褪色，暗夜幻梦登场~",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60562,
60563,
60564,
60565,
60566
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
3427
},
titleType = 0,
activityBeginCleanCoin = {
24,
3427,
605145
},
activitySubName = "全新炫彩时装",
currencyCfg = v28,
platforms = v26
},
[627] = {
id = 627,
timeInfo = {
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1732463999
},
showEndTime = {
seconds = 1732463999
},
showBeginTime = {
seconds = 1731600000
}
},
labelId = 4,
backgroundUrl = {
"dingyuevx.astc"
},
lowVersion = "1.3.26.71",
activityName = "订阅领好礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60567,
60568
},
showInCenter = true,
titleType = 0,
activitySubName = "星愿币送不停",
platforms = {
1,
3
}
},
[628] = {
id = 628,
timeInfo = {
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1732463999
},
showEndTime = {
seconds = 1732463999
},
showBeginTime = {
seconds = 1731600000
}
},
labelId = 4,
backgroundUrl = {
"dingyueqq.astc"
},
lowVersion = "1.3.26.71",
activityName = "订阅领好礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60569,
60570
},
showInCenter = true,
titleType = 0,
activitySubName = "星愿币送不停",
platforms = {
1,
3
}
},
[703] = {
id = 703,
activityType = "ATRecruitOrder",
timeInfo = {
beginTime = {
seconds = 1729216800
},
endTime = {
seconds = 1735574399
},
showEndTime = {
seconds = 1735574399
},
showBeginTime = {
seconds = 1729216800
}
},
labelId = 11,
backgroundUrl = {
"T_Convened_Img_Background.astc"
},
activityName = "召集星搭子",
activityDesc = "得非凡外观",
activityUIDetail = "UI_Activity_Convened_MainView",
tagId = 1,
activityTaskGroup = {
70006,
70007
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
3159
},
titleType = 0,
activitySubName = "送你稀有时装"
},
[1003] = {
id = 1003,
activityType = "ATTraining",
timeInfo = {
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1732982399
},
showEndTime = {
seconds = 1732982399
},
showBeginTime = {
seconds = 1721923200
}
},
labelId = 2,
activityName = "宝宝奶昔铺",
activityParam = {
5,
5,
10,
3506,
1,
3501,
1
},
activityUIDetail = "UI_Teletubbies_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
90001,
90002,
90003,
90004,
90005,
90000
},
showInCenter = true,
activityNameType = "ANTTraining",
titleType = 0,
activitySubName = "天线宝宝测试"
},
[633] = {
id = 633,
activityType = "ATUpdateForesight",
timeInfo = {
beginTime = {
seconds = 1739116800
},
endTime = {
seconds = 1740067199
},
showEndTime = {
seconds = 1740067199
},
showBeginTime = {
seconds = 1739116800
}
},
labelId = 7,
activityName = "订阅新玩法",
activityParam = {
605161,
605162
},
activityUIDetail = "UI_InvisibleGuardian_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60571,
60572,
60573
},
showInCenter = true,
activityNameType = "ANTUpdateForesightV1",
activitySubName = "送稀有头饰",
platforms = v26
},
[634] = {
id = 634,
activityType = "ATUpdateForesight",
timeInfo = {
beginTime = {
seconds = 1732291200
},
endTime = {
seconds = 1732723199
},
showEndTime = {
seconds = 1732723199
},
showBeginTime = {
seconds = 1732291200
}
},
labelId = 7,
activityName = "订阅新玩法",
activityParam = {
605167,
605168
},
activityUIDetail = "UI_InvisibleGuardian_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60574,
60575,
60576
},
showInCenter = true,
activityNameType = "ANTUpdateForesightV1",
activitySubName = "送稀有头饰",
platforms = v26
},
[635] = {
id = 635,
activityType = "ATUpdateForesight",
timeInfo = {
beginTime = {
seconds = 1732291200
},
endTime = {
seconds = 1732723199
},
showEndTime = {
seconds = 1732723199
},
showBeginTime = {
seconds = 1732291200
}
},
labelId = 7,
activityName = "订阅新玩法",
activityParam = {
605173,
605174
},
activityUIDetail = "UI_InvisibleGuardian_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60577,
60578,
60579
},
showInCenter = true,
activityNameType = "ANTUpdateForesightV1",
activitySubName = "送稀有头饰",
platforms = v26
},
[632] = {
id = 632,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1732809599
},
showEndTime = {
seconds = 1732809599
},
showBeginTime = {
seconds = 1732204800
}
},
labelId = 3,
backgroundUrl = {
"faxian_chongduan241114.astc"
},
lowVersion = "*********",
activityName = "冲段进行时",
activityParam = {
25,
3428
},
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60580,
60581,
60582
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
3428
},
clientParams = v24,
titleType = 0,
activitySubName = "领免费时装",
currencyCfg = v27,
platforms = v26
},
[636] = {
id = 636,
activityType = "ATReturningTask",
timeInfo = {
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1732204800
}
},
lowVersion = "1.3.37.1",
activityName = "首局福利",
activityTaskGroup = {
60592
},
activityNameType = "ANTReturningTask",
clientParams = {
"620024",
"620196",
"610107",
"630115"
}
},
[650] = {
id = 650,
activityType = "ATSpringRedPacket",
timeInfo = {
beginTime = {
seconds = 1732593600
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1732593600
}
},
labelId = 8,
lowVersion = "*********",
activityName = "星期五天团",
activityDesc = "春节红包",
activityUIDetail = "UI_SpringRedPacket_Main",
activityRuleId = 106,
tagId = 4,
showInCenter = true,
activityNameType = "ANTSpringRedPacket",
titleType = 0,
activitySubName = "专属红包登场",
activityGroup = v32,
platforms = v26
},
[630] = {
id = 630,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1740585600
},
endTime = {
seconds = 1741622399
},
showEndTime = {
seconds = 1741622399
},
showBeginTime = {
seconds = 1740585600
}
},
labelId = 1,
backgroundUrl = {
"shuoguo.astc"
},
lowVersion = "1.3.37.1",
activityName = "隐形守护者",
activityParam = {
24,
3500,
606006,
3411,
3412
},
activityUIDetail = "UI_Activity_TheInvisibleGuardian_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60600,
60601
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3500
},
activitySubName = "玩法正式上线",
activityGroup = v32,
platforms = v26
},
[640] = {
id = 640,
timeInfo = {
beginTime = {
seconds = 1732636800
},
endTime = {
seconds = 1733673599
},
showEndTime = {
seconds = 1733673599
},
showBeginTime = {
seconds = 1732636800
}
},
labelId = 2,
backgroundUrl = {
"faxian_ganen241121_2.astc"
},
lowVersion = "1.3.26.111",
activityName = "感恩有你",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60610,
60611,
60612
},
showInCenter = true,
titleType = 0,
activitySubName = "免费送磷虾",
platforms = v26
},
[641] = {
id = 641,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1733673599
},
showEndTime = {
seconds = 1733673599
},
showBeginTime = {
seconds = 1732809600
}
},
labelId = 3,
backgroundUrl = {
"faxian_xinsaiji241121.astc"
},
lowVersion = "1.3.37.1",
activityName = "新赛季登场",
activityParam = {
32,
3445,
606022
},
activityDesc = "全新赛季来袭，超多奖励免费领！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60606,
60607,
60608
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
32
},
titleType = 0,
activityBeginCleanCoin = {
3445
},
activitySubName = "免费配饰相送",
platforms = v26
},
[660] = {
id = 660,
activityType = "ATHalfYearWarmUp",
timeInfo = {
beginTime = {
seconds = 1732636800
},
endTime = {
seconds = 1733673599
},
showEndTime = {
seconds = 1733673599
},
showBeginTime = {
seconds = 1732636800
}
},
labelId = 5,
activityName = "欢庆签到站",
activityUIDetail = "UI_Activity_FirstAnniversary_MainView",
tagId = 1,
showInCenter = true,
activityNameType = "ANTHalfYearWarmUp",
titleType = 1,
activitySubName = "微信"
},
[560] = {
id = 560,
timeInfo = {
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1736351999
},
showEndTime = {
seconds = 1736351999
},
showBeginTime = {
seconds = 1725552000
}
},
labelId = 2,
backgroundUrl = {
"koudai240906.astc"
},
activityName = "口袋领到鼓",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56560,
56561
},
showInCenter = true,
titleType = 0,
activitySubName = "链接跳转测试",
platforms = v26
},
[566] = {
id = 566,
activityType = "ATSticker",
timeInfo = {
beginTime = {
seconds = 1726329600
},
endTime = {
seconds = 1735660799
},
showEndTime = {
seconds = 1735660799
},
showBeginTime = {
seconds = 1726329600
}
},
labelId = 3,
activityName = "金秋月饼铺",
activityUIDetail = "UI_Activity_MidAutumnMooncake_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
56566,
56567
},
showInCenter = true,
activityNameType = "ANTSticker",
titleType = 0,
activityBeginCleanCoin = {
3403
},
activitySubName = "卡牌包测试",
platforms = v26
},
[615] = {
id = 615,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1735660799
},
showEndTime = {
seconds = 1735660799
},
showBeginTime = {
seconds = 1729785600
}
},
labelId = 3,
backgroundUrl = {
"tiantianshangfen.astc"
},
activityName = "天天上分礼",
activityParam = {
37,
3418
},
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60532,
60533,
60534
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
37
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3418
},
activitySubName = "卡牌包测试",
platforms = v26
},
[620] = {
id = 620,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1736351999
},
showEndTime = {
seconds = 1736351999
},
showBeginTime = {
seconds = 1730390400
}
},
labelId = 2,
backgroundUrl = {
"shuoguo.astc"
},
activityName = "硕果累累季",
activityDesc = "印章祈愿更新啦~",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60541,
60542,
60543,
60544
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
3422
},
titleType = 0,
activityBeginCleanCoin = {
24,
3422,
605097
},
activitySubName = "卡牌包测试",
currencyCfg = v28,
platforms = v26
},
[662] = {
id = 662,
activityType = "ATMinesweeper",
timeInfo = {
beginTime = {
seconds = 1733414400
},
endTime = {
seconds = 1734883199
},
showEndTime = {
seconds = 1734883199
},
showBeginTime = {
seconds = 1733414400
}
},
labelId = 1,
activityName = "游园寻宝",
activityUIDetail = "UI_TreasureHunt_ActivityView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60620,
60621
},
showInCenter = true,
activityNameType = "ANTMinesweeper",
titleType = 0,
activityBeginCleanCoin = {
3449
},
activitySubName = "送明星小电视",
platforms = v26
},
[663] = {
id = 663,
timeInfo = {
beginTime = {
seconds = 1734710400
},
endTime = {
seconds = 1735747199
},
showEndTime = {
seconds = 1735747199
},
showBeginTime = {
seconds = 1734710400
}
},
labelId = 2,
backgroundUrl = {
"faxian_yizhounian_zhengshi.astc"
},
activityName = "元梦一周年",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60660
},
showInCenter = true,
titleType = 0,
activitySubName = "登录领非凡",
activityGroup = v32,
platforms = v26
},
[669] = {
id = 669,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1733932800
},
endTime = {
seconds = 1734710399
},
showEndTime = {
seconds = 1734710399
},
showBeginTime = {
seconds = 1733932800
}
},
labelId = 3,
backgroundUrl = {
"faxian_yizhounian_zhanshi.astc"
},
activityName = "元梦一周年",
activityUIDetail = v15,
tagId = 1,
showInCenter = true,
activityNameType = v20,
titleType = 0,
activitySubName = "登录领非凡",
activityGroup = v32,
platforms = v26
},
[664] = {
id = 664,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1735228799
},
showEndTime = {
seconds = 1735228799
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 3,
backgroundUrl = {
"faxian_jinji.astc"
},
activityName = "晋级冲冲冲",
activityParam = {
24,
3451
},
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60661,
60662,
60663
},
showInCenter = true,
activityNameType = v23,
activityShopType = v28,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3451
},
activitySubName = "全新充能大招",
platforms = v26
},
[665] = {
id = 665,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 3,
backgroundUrl = {
"Jump_kenan3.astc"
},
jumpId = 493,
activityName = "名侦探柯南",
activityUIDetail = v15,
tagId = 1,
showInCenter = true,
activityNameType = v20,
titleType = 0,
activitySubName = "周年送好礼",
activityGroup = v32,
platforms = v26
},
[674] = {
id = 674,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1733889600
},
endTime = {
seconds = 1736524799
},
showEndTime = {
seconds = 1736524799
},
showBeginTime = {
seconds = 1733889600
}
},
labelId = 2,
backgroundUrl = {
"Jump_douyin.astc"
},
jumpId = 98,
activityName = "绑定抖音号",
activityUIDetail = v15,
tagId = 4,
showInCenter = true,
activityNameType = v20,
titleType = 0,
activitySubName = "即刻领福利",
platforms = v26
},
[670] = {
id = 670,
activityType = "ATWishingTree",
timeInfo = {
beginTime = {
seconds = 1735228800
},
endTime = {
seconds = 1735660799
},
showEndTime = {
seconds = 1735660799
},
showBeginTime = {
seconds = 1735228800
}
},
labelId = 3,
lowVersion = "1.3.26.71",
activityName = "许愿五选二",
activityUIDetail = "UI_Activity_WishingTreeView",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTWishingTree",
titleType = 0,
activitySubName = "福利多随心选",
platforms = v26
},
[671] = {
id = 671,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1735228800
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1735228800
}
},
labelId = 2,
backgroundUrl = {
"faxian_qingdian_wx.astc"
},
activityName = "庆典不停歇",
activityParam = {
24,
3455,
606138
},
activityDesc = "热舞突袭免费领，周年派对赶紧舞起来！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60670,
60672,
60673,
60674
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3455
},
activitySubName = "超多免费福利",
platforms = v26
},
[672] = {
id = 672,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1732636800
},
endTime = {
seconds = 1732723199
},
showEndTime = {
seconds = 1732723199
},
showBeginTime = {
seconds = 1732636800
}
},
labelId = 2,
backgroundUrl = {
"faxian_qingdian_qq.astc"
},
activityName = "庆典不停歇",
activityParam = {
32,
3456,
606147
},
activityDesc = "热舞突袭免费领，周年派对赶紧舞起来！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60671,
60675,
60676,
60677
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
32
},
titleType = 0,
activityBeginCleanCoin = {
3456
},
activitySubName = "超多免费福利",
platforms = v26
},
[673] = {
id = 673,
timeInfo = {
beginTime = {
seconds = 1735488000
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1735488000
}
},
labelId = 2,
backgroundUrl = {
"faxian_25xinnian.astc"
},
activityName = "元梦新年礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60678,
60679
},
showInCenter = true,
titleType = 0,
activitySubName = "领2025头饰",
activityGroup = v32,
platforms = v26
},
[704] = {
id = 704,
activityType = "ATRecruitOrder",
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736438399
},
showEndTime = {
seconds = 1736438399
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 11,
backgroundUrl = {
"T_Convened_Img_Background.astc"
},
activityName = "召集星搭子",
activityDesc = "得非凡外观",
activityUIDetail = "UI_Activity_Convened_MainView",
tagId = 1,
activityTaskGroup = {
70008,
70009
},
showInCenter = true,
activityNameType = v22,
titleType = 0,
activityBeginCleanCoin = {
3159
},
activitySubName = "免费手持物",
activityGroup = v32
},
[800] = {
id = 800,
activityType = "ATSpringFestivalRaffle",
timeInfo = {
beginTime = {
seconds = 1728316800
},
endTime = {
seconds = 1737388799
},
showEndTime = {
seconds = 1737388799
},
showBeginTime = {
seconds = 1728316800
}
},
labelId = 1,
activityName = "新春躺赢节",
activityParam = {
20000003,
20000004,
20000005,
20000006,
20000007
},
activityDesc = "充能得能量值与外观奖励，充满<NationalDay>必得</>全新非凡时装",
activityUIDetail = "UI_NewYearActivities_MainView",
tagId = 1,
showInCenter = true,
activityNameType = "ANTSpringFestivalRaffle",
activityShopType = {
168
},
clientParams = {
"10401",
"10402",
"10403",
"10404",
"10405",
"10406",
"10407",
"10408"
},
titleType = 0,
activitySubName = "霸福节节高",
currencyCfg = {
3407,
2071
},
taskGroupsDrawBase = {
60664
},
platforms = v26
},
[852] = {
id = 852,
activityType = "ATNewYearSign",
timeInfo = {
beginTime = {
seconds = 1736697600
},
endTime = {
seconds = 1740326399
},
showEndTime = {
seconds = 1740326399
},
showBeginTime = {
seconds = 1736697600
}
},
activityName = "新春天天送",
activityParam = {
3602,
3,
234
},
activityUIDetail = "UI_Activity_NewYearSendEveryDay_MainView",
activityTaskGroup = {
85200,
85201,
85202,
85203,
85204,
85205,
85206
},
showInCenter = true,
activityNameType = "ANTNewYearSign",
titleType = 1,
activitySubName = "每日送福",
tagIds = {
1,
5
},
tagEndTimes = {
{
seconds = 1737525600
}
},
labelIds = {
{
tagId = 1,
labelId = 5
},
{
tagId = 5,
labelId = 5
}
}
},
[900] = {
id = 900,
activityType = "ATReturningDiffers",
timeInfo = {
beginTime = {
seconds = 1734883200
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1734883200
}
},
labelId = 1,
backgroundUrl = {
"T_LinearExchange_Img_Background_N_A.astc",
"T_LinearExchange_Img_Title1_N_A.astc",
"T_LinearExchange_Img_Girl_N_A.astc"
},
lowVersion = "1.3.37.55",
activityName = "集友谊之火",
activityParam = {
2800007,
404840,
2800001,
401390
},
activityDesc = "友谊之火",
activityUIDetail = "UI_Return_Activity_Exchange",
activityRuleId = 126,
tagId = 4,
activityTaskGroup = {
60593,
60594
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
40
},
titleType = 1,
activitySubName = "兑小栗子时装",
currencyCfg = {
2042
},
platforms = v26
},
[705] = {
id = 705,
activityType = "ATRecruitOrder",
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1741535999
},
showEndTime = {
seconds = 1741535999
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 11,
backgroundUrl = {
"T_Convened_Img_Background.astc"
},
activityName = "召集星搭子",
activityParam = {
20000008,
20000008
},
activityDesc = "得非凡外观",
activityUIDetail = "UI_Activity_CallUp_MainView",
tagId = 1,
activityTaskGroup = {
70010,
70011
},
showInCenter = true,
activityNameType = v22,
titleType = 0,
activityBeginCleanCoin = {
3159
},
activitySubName = "免费手持物",
taskGroupsDrawBase = {
70011
}
},
[920] = {
id = 920,
activityType = "ATFashionFund",
timeInfo = {
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1742054399
},
showEndTime = {
seconds = 1742054399
},
showBeginTime = {
seconds = 1735660800
}
},
labelId = 11,
activityName = "时装基金",
activityUIDetail = "UI_Couture_MainView",
tagId = 1,
showInCenter = true,
titleType = 0,
activitySubName = "时装基金",
currencyCfg = {
1
},
platforms = v26,
sonActivityIds = {
921,
922
}
},
[921] = {
id = 921,
activityType = "ATFashionFund",
timeInfo = {
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1739635199
},
showEndTime = {
seconds = 1739635199
},
showBeginTime = {
seconds = 1735660800
}
},
labelId = 11,
activityName = "时装基金",
tagId = 1,
titleType = 0,
activitySubName = "12月",
currencyCfg = {
1
},
platforms = v26
},
[922] = {
id = 922,
activityType = "ATFashionFund",
timeInfo = {
beginTime = {
seconds = 1738339200
},
endTime = {
seconds = 1742054399
},
showEndTime = {
seconds = 1742054399
},
showBeginTime = {
seconds = 1738339200
}
},
labelId = 11,
activityName = "时装基金",
tagId = 1,
titleType = 0,
activitySubName = "1月",
currencyCfg = {
1
},
platforms = v26
},
[2551] = {
id = 2551,
timeInfo = {
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1748447999
},
showEndTime = {
seconds = 1748447999
},
showBeginTime = {
seconds = 1747324800
}
},
labelId = 4,
activityName = "新春霸福节",
activityParam = {
6
},
activityDesc = "完成下列任务即可领取奖励",
activityUIDetail = "UI_Countdown03_Main",
tagId = 1,
activityTaskGroup = {
522441,
522451,
522461,
522471,
522481,
522491,
522501,
522511
},
showInCenter = true,
platforms = v26
},
[675] = {
id = 675,
timeInfo = {
beginTime = {
seconds = 1735920000
},
endTime = {
seconds = 1738339199
},
showEndTime = {
seconds = 1738339199
},
showBeginTime = {
seconds = 1735920000
}
},
labelId = 2,
backgroundUrl = {
"faxian_saijimo.astc"
},
activityName = "赛季末冲段",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60690
},
showInCenter = true,
titleType = 0,
activitySubName = "专属伙伴测试",
platforms = v26
},
[676] = {
id = 676,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1736697599
},
showEndTime = {
seconds = 1736697599
},
showBeginTime = {
seconds = 1735833600
}
},
labelId = 2,
backgroundUrl = {
"faxian_tiaowu.astc"
},
activityName = "一起跳舞吧",
activityParam = {
25,
3459,
606210
},
activityDesc = "跳卡皮巴拉舞，快乐”元“来很简单",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60691,
60692,
60693
},
showInCenter = true,
activityNameType = v22,
activityShopType = v27,
titleType = 0,
activityBeginCleanCoin = {
3459
},
activitySubName = "送卡皮巴拉舞",
platforms = v26
},
[677] = {
id = 677,
timeInfo = {
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1736697599
},
showEndTime = {
seconds = 1736697599
},
showBeginTime = {
seconds = 1735833600
}
},
labelId = 2,
backgroundUrl = {
"faxian_dingyue_wx.astc"
},
activityName = "订阅领好礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60694
},
showInCenter = true,
titleType = 0,
activitySubName = "送稀有配饰",
platforms = {
3
}
},
[678] = {
id = 678,
timeInfo = {
beginTime = {
seconds = 1740585600
},
endTime = {
seconds = 1741622399
},
showEndTime = {
seconds = 1741622399
},
showBeginTime = {
seconds = 1740585600
}
},
labelId = 7,
backgroundUrl = {
"faxian_dingyue_qq.astc"
},
activityName = "订阅领好礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60695
},
showInCenter = true,
titleType = 0,
activitySubName = "老多条TEST",
platforms = {
3
}
},
[679] = {
id = 679,
activityType = "ATEaseBurdenTeamTask",
timeInfo = {
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1736697599
},
showEndTime = {
seconds = 1736697599
},
showBeginTime = {
seconds = 1735833600
}
},
labelId = 4,
lowVersion = "**********",
activityName = "热情帮帮队",
activityUIDetail = "UI_EasyTeam_MainView",
isInBottom = 1,
activityRuleId = 5028,
tagId = 1,
activityTaskGroup = {
60696
},
showInCenter = true,
activityNameType = "ANTEaseBurdenTeamTask",
isHideMainBackground = true,
titleType = 1,
activitySubName = "领奖励超简单"
},
[685] = {
id = 685,
activityType = "ATSpringSlip",
timeInfo = {
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1744300799
},
showEndTime = {
seconds = 1744300799
},
showBeginTime = {
seconds = 1730563200
}
},
activityName = "新春贺卡",
activityUIDetail = "UI_NewYearsCard_MainView",
isInBottom = 1,
activityTaskGroup = {
60900,
60901,
60902
},
showInCenter = true,
activityNameType = "ANTSpringSlip",
clientParams = {
"任务",
"邀请"
},
titleType = 0,
activitySubName = "新春贺卡测试",
currencyCfg = {
200750
},
platforms = v26,
tagIds = {
1,
4
},
tagEndTimes = {
{
seconds = 1737525600
}
},
thumbnail = "T_Common_Tip",
labelIds = {
{
tagId = 1,
labelId = 2
},
{
tagId = 4,
labelId = 2
}
}
},
[690] = {
id = 690,
activityType = "ATSpringRedPacket",
timeInfo = {
beginTime = {
seconds = 1732593600
},
endTime = {
seconds = 1739375999
},
showEndTime = {
seconds = 1739375999
},
showBeginTime = {
seconds = 1732593600
}
},
labelId = 8,
lowVersion = "*********",
activityName = "星期五天团",
activityDesc = "春节红包",
activityUIDetail = "UI_SpringRedPacket_Main",
activityRuleId = 106,
tagId = 4,
activityTaskGroup = {
52036
},
showInCenter = true,
activityNameType = "ANTSpringRedPacket",
titleType = 0,
activitySubName = "专属红包登场",
activityGroup = v32,
platforms = v26
},
[700] = {
id = 700,
timeInfo = {
beginTime = {
seconds = 1736438400
},
endTime = {
seconds = 1748620799
},
showEndTime = {
seconds = 1748620799
},
showBeginTime = {
seconds = 1736438400
}
},
labelId = 2,
activityName = "胡闹派对乐新春",
activityParam = {
10000
},
activityUIDetail = "UI_Activity_NewYearParty2025_MainView",
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
60760,
60761
},
showInCenter = true,
titleType = 0,
activitySubName = "小标题测试",
currencyCfg = {
3700
},
platforms = v26
},
[694] = {
id = 694,
activityType = "ATLanternRiddles",
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1742140799
},
showEndTime = {
seconds = 1742140799
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 2,
activityName = "欢乐闹元宵",
activityDesc = "团圆;幸福;好运;顺意",
activityUIDetail = "UI_Activity_LanternFestival03_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60753,
60754,
60755,
60756
},
showInCenter = true,
activityNameType = "ATFLanternRiddles",
titleType = 0,
activitySubName = "送元宵昵称框",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
9
}
}
},
platforms = v26
},
[695] = {
id = 695,
activityType = "ATSticker",
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1742140799
},
showEndTime = {
seconds = 1742140799
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 3,
activityName = "福元滚滚来",
activityUIDetail = "UI_Activity_LanternFestival02_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60757,
60758,
60759
},
showInCenter = true,
activityNameType = "ANTSticker",
titleType = 0,
activityBeginCleanCoin = {
3403
},
activitySubName = "送元宵宝宝",
activityGroup = "AG_Common",
platforms = v26
},
[712] = {
id = 712,
timeInfo = {
beginTime = {
seconds = 1738857600
},
endTime = {
seconds = 1769788799
},
showEndTime = {
seconds = 1769788799
},
showBeginTime = {
seconds = 1738857600
}
},
labelId = 2,
lowVersion = "*********",
activityName = "胡闹派对乐新春2",
activityParam = {
10000
},
activityUIDetail = "UI_Activity_NewYearParty2025_MainView",
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
60762,
60763
},
showInCenter = true,
titleType = 0,
activitySubName = "小标题测试",
currencyCfg = {
3700
},
platforms = v26
},
[706] = {
id = 706,
activityType = "ATRecruitOrder",
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741103999
},
showEndTime = {
seconds = 1741103999
},
showBeginTime = {
seconds = 1737043200
}
},
labelId = 9,
backgroundUrl = {
"T_Convened_Img_Background.astc"
},
lowVersion = "1.3.68.1",
activityName = "携友同行",
activityParam = {
20000008,
20000008
},
activityDesc = "得非凡外观",
activityUIDetail = "UI_Activity_CallUp_MainView",
tagId = 1,
activityTaskGroup = {
70012,
70013
},
showInCenter = true,
activityNameType = v22,
titleType = 0,
activityBeginCleanCoin = {
3159
},
activitySubName = "赢元梦周边",
activityGroup = v32,
taskGroupsDrawBase = {
70013
}
},
[710] = {
id = 710,
timeInfo = {
beginTime = {
seconds = 1738857600
},
endTime = {
seconds = 1750003199
},
showEndTime = {
seconds = 1750003199
},
showBeginTime = {
seconds = 1738857600
}
},
labelId = 3,
backgroundUrl = {
"faxian_tianmi_250213.astc"
},
lowVersion = "1.3.68.84",
activityName = "甜蜜口令码",
activityUIDetail = v17,
isInBottom = 1,
tagId = 7,
activityTaskGroup = {
61771,
61776
},
showInCenter = true,
titleType = 0,
activitySubName = "口令码测试",
platforms = v26
},
[711] = {
id = 711,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
showEndTime = {
seconds = 1751299199
},
showBeginTime = {
seconds = 1747324800
}
},
labelId = 5,
backgroundUrl = {
"faxian_nuanai_250213.astc"
},
lowVersion = "1.3.68.84",
activityName = "暖爱陪伴",
activityParam = {
24,
3478,
607010
},
activityDesc = "永久时装清凉午后，2月21日起累登领！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 7,
activityTaskGroup = {
60773,
61770
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3478
},
activitySubName = "口令码测试",
platforms = v26
},
[688] = {
id = 688,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1748707200
},
endTime = {
seconds = 1749571199
},
showEndTime = {
seconds = 1749571199
},
showBeginTime = {
seconds = 1748707200
}
},
labelId = 3,
backgroundUrl = {
"faxian_sanliou_250123.astc"
},
activityName = "甜蜜咖啡店",
activityParam = {
37,
3467
},
activityUIDetail = v19,
isInBottom = 1,
tagId = 7,
activityTaskGroup = {
61772
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
37
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3467
},
activitySubName = "口令码测试",
platforms = v26
},
[689] = {
id = 689,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1738684799
},
showEndTime = {
seconds = 1738684799
},
showBeginTime = {
seconds = 1737993600
}
},
labelId = 2,
backgroundUrl = {
"faxian_guonian_250123.astc"
},
activityName = "守岁共纳福",
activityParam = {
37,
3468,
606340
},
activityDesc = "每日18点起可发射烟花哦~",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60745,
60746,
60752
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
37
},
titleType = 0,
activityBeginCleanCoin = {
3468
},
activitySubName = "专属拜年动作",
platforms = v26
},
[691] = {
id = 691,
timeInfo = {
beginTime = {
seconds = 1738598400
},
endTime = {
seconds = 1739116799
},
showEndTime = {
seconds = 1739116799
},
showBeginTime = {
seconds = 1738598400
}
},
labelId = 2,
backgroundUrl = {
"faxian_kaimen_250123.astc"
},
activityName = "喜迎开门红",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60747
},
showInCenter = true,
titleType = 0,
activitySubName = "口令码测试",
platforms = v26
},
[692] = {
id = 692,
activityType = "ATAnimalHandbook",
timeInfo = {
beginTime = {
seconds = 1738252800
},
endTime = {
seconds = 1741881599
},
showEndTime = {
seconds = 1741881599
},
showBeginTime = {
seconds = 1738252800
}
},
labelId = 2,
activityName = "牧场物语",
activityUIDetail = "UI_PastureStories_EntryView",
tagId = 1,
activityTaskGroup = {
60748,
60749,
60750
},
showInCenter = true,
activityNameType = "ANTAnimalHandbook",
titleType = 1,
activitySubName = "送小年宝时装",
platforms = v26
},
[855] = {
id = 855,
activityType = "ATTwoPeopleSquad",
timeInfo = {
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1743436799
},
showEndTime = {
seconds = 1743436799
},
showBeginTime = {
seconds = 1739462400
}
},
labelId = 2,
activityName = "双人成团",
activityParam = {
3603
},
activityUIDetail = "UI_DoubleFormationActivity",
tagId = 1,
activityTaskGroup = {
60800,
60801,
60802,
60803,
60804
},
showInCenter = true,
activityNameType = "ANTTwoPeopleSquad",
clientParams = {
"608000",
"608002",
"608004",
"608006",
"608007",
"608009",
"608011",
"608013",
"608014",
"608016",
"608018",
"608020",
"608021"
},
titleType = 1,
activitySubName = "共领好礼"
},
[714] = {
id = 714,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 1741535999
},
showEndTime = {
seconds = 1741535999
},
showBeginTime = {
seconds = 1740672000
}
},
labelId = 1,
backgroundUrl = {
"faxian_nuanchun_250227.astc"
},
lowVersion = "1.3.68.116",
activityName = "暖春赠礼",
activityParam = {
24,
3482
},
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60777
},
showInCenter = true,
activityNameType = v23,
activityShopType = v28,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3482
},
activitySubName = "全新面部表情",
platforms = v26
},
[715] = {
id = 715,
timeInfo = {
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1741276799
},
showEndTime = {
seconds = 1741276799
},
showBeginTime = {
seconds = 1740758400
}
},
labelId = 2,
backgroundUrl = {
"faxian_taoyuan_250227.astc"
},
lowVersion = "1.3.68.116",
activityName = "喜迎萝福来",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60778
},
showInCenter = true,
titleType = 0,
activitySubName = "桃源联动将至",
platforms = v26
},
[716] = {
id = 716,
activityType = "ATMinesweeper",
timeInfo = {
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 1749484799
},
showEndTime = {
seconds = 1749484799
},
showBeginTime = {
seconds = 1740672000
}
},
labelId = 3,
lowVersion = "1.3.68.116",
activityName = "游园寻宝",
activityUIDetail = "UI_TreasureHunt_ActivityView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60790,
60791,
60792,
60793
},
showInCenter = true,
activityNameType = "ANTMinesweeper",
titleType = 0,
activityBeginCleanCoin = {
3483
},
activitySubName = "新时装乐酷星",
platforms = v26
},
[717] = {
id = 717,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1740585600
},
endTime = {
seconds = 1749571199
},
showEndTime = {
seconds = 1749571199
},
showBeginTime = {
seconds = 1740585600
}
},
labelId = 3,
backgroundUrl = {
"xiuxianshike.astc"
},
activityName = "一起跳舞吧",
activityParam = {
32,
3401,
607128
},
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60786,
60787
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
32
},
titleType = 0,
activityBeginCleanCoin = {
3401
},
activitySubName = "新线性TEST",
platforms = v26,
videos = {
3503,
3504,
3506
},
videoColorTemplate = 1
},
[718] = {
id = 718,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1740585600
},
endTime = {
seconds = 1749571199
},
showEndTime = {
seconds = 1749571199
},
showBeginTime = {
seconds = 1740585600
}
},
labelId = 4,
backgroundUrl = {
"faxian_yuqi2_250110.astc"
},
activityName = "雨琦送好礼",
activityParam = {
24,
3482
},
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60785
},
showInCenter = true,
activityNameType = v23,
activityShopType = v28,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3482
},
activitySubName = "新商店TEST",
platforms = v26,
videos = {
3502
},
videoColorTemplate = 2
},
[719] = {
id = 719,
timeInfo = {
beginTime = {
seconds = 1740585600
},
endTime = {
seconds = 1749571199
},
showEndTime = {
seconds = 1749571199
},
showBeginTime = {
seconds = 1740585600
}
},
labelId = 5,
backgroundUrl = {
"faxian_chuxiye_250123.astc"
},
activityName = "守岁除夕夜",
activityUIDetail = "UI_CommonTask_SingleTaskView2",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60784
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
activitySubName = "新单条TEST",
platforms = v26,
videos = {
3501
},
videoColorTemplate = 3
},
[723] = {
id = 723,
timeInfo = {
beginTime = {
seconds = 1740585600
},
endTime = {
seconds = 1749571199
},
showEndTime = {
seconds = 1749571199
},
showBeginTime = {
seconds = 1740585600
}
},
labelId = 6,
backgroundUrl = {
"faxian_saiji_250117.astc"
},
activityName = "新赛季开启",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60783
},
showInCenter = true,
titleType = 0,
activitySubName = "新多条TEST",
platforms = v26,
videos = {
3505
},
videoColorTemplate = 1
},
[720] = {
id = 720,
timeInfo = {
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1742745599
},
showEndTime = {
seconds = 1742745599
},
showBeginTime = {
seconds = 1741881600
}
},
labelId = 2,
backgroundUrl = {
"faxian_xinsaiji_250314.astc"
},
lowVersion = "********",
activityName = "新赛季开启",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60915
},
showInCenter = true,
titleType = 0,
activitySubName = "上分要趁早",
platforms = v26
},
[721] = {
id = 721,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1742140800
},
endTime = {
seconds = 1742745599
},
showEndTime = {
seconds = 1742745599
},
showBeginTime = {
seconds = 1742140800
}
},
labelId = 2,
backgroundUrl = {
"faxian_chunri_250314.astc"
},
lowVersion = "********",
activityName = "悠闲春日季",
activityParam = {
32,
3488
},
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60916,
60917,
60918
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
32
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3488
},
activitySubName = "兑星愿币宝箱",
platforms = v26
},
[722] = {
id = 722,
timeInfo = {
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1742745599
},
showEndTime = {
seconds = 1742745599
},
showBeginTime = {
seconds = 1741881600
}
},
labelId = 2,
backgroundUrl = {
"faxian_kapai_250314.astc"
},
lowVersion = "********",
activityName = "登录集卡牌",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
60919
},
showInCenter = true,
clientParams = {
"页签1",
"页签2",
"页签3"
},
titleType = 0,
activitySubName = "实现卡牌自由",
platforms = v26
},
[725] = {
id = 725,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1743350399
},
showEndTime = {
seconds = 1743350399
},
showBeginTime = {
seconds = 1742486400
}
},
labelId = 5,
backgroundUrl = {
"faxian_yuanqi_250321.astc"
},
lowVersion = "1.3.78.33",
activityName = "元气出游记",
activityParam = {
24,
3492,
630006
},
activityDesc = "免费领取新非凡动作-吹泡泡！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63010,
63011,
63012,
63013
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3492
},
activitySubName = "新动作吹泡泡",
platforms = v26
},
[726] = {
id = 726,
activityType = "ATRecruitOrder",
timeInfo = {
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1747151999
},
showEndTime = {
seconds = 1747151999
},
showBeginTime = {
seconds = 1742745600
}
},
labelId = 11,
backgroundUrl = {
"T_Convened_Img_Background.astc"
},
lowVersion = "1.3.78.33",
activityName = "召集星搭子",
activityDesc = "得非凡外观",
activityUIDetail = "UI_Activity_Convened_MainView",
tagId = 1,
activityTaskGroup = {
63014,
63015
},
showInCenter = true,
activityNameType = v22,
titleType = 0,
activityBeginCleanCoin = {
3159
},
activitySubName = "稀有时装上新",
activityGroup = v32
},
[727] = {
id = 727,
timeInfo = {
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1743350399
},
showEndTime = {
seconds = 1743350399
},
showBeginTime = {
seconds = 1742745600
}
},
labelId = 2,
backgroundUrl = {
"faxian_songzhaoji_250321.astc"
},
lowVersion = "1.3.78.33",
activityName = "送召集能量",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63016
},
showInCenter = true,
titleType = 0,
activitySubName = "加速领时装",
activityGroup = v32,
platforms = v26
},
[740] = {
id = 740,
timeInfo = {
beginTime = {
seconds = 1743350400
},
endTime = {
seconds = 1743955199
},
showEndTime = {
seconds = 1743955199
},
showBeginTime = {
seconds = 1743350400
}
},
labelId = 2,
backgroundUrl = {
"faxian_songzhaoji2_250328.astc"
},
lowVersion = "1.3.78.58",
activityName = "送召集能量",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63020
},
showInCenter = true,
titleType = 0,
activitySubName = "加速领时装",
activityGroup = v32,
platforms = v26,
activityGroupList = {
"AG_Main"
}
},
[741] = {
id = 741,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1743695999
},
showEndTime = {
seconds = 1743695999
},
showBeginTime = {
seconds = 1743091200
}
},
labelId = 1,
backgroundUrl = {
"langrenxinditu.astc"
},
lowVersion = "1.3.78.58",
activityName = "福利抢先知",
activityParam = {
25,
3493,
630113
},
activityDesc = "稀有面饰-不等式眼镜免费送！一起来搞怪！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63021,
63022,
63023,
63024,
63025
},
showInCenter = true,
activityNameType = v22,
activityShopType = v27,
titleType = 0,
activityBeginCleanCoin = {
3493
},
activitySubName = "免费稀有头饰",
platforms = {
3
}
},
[742] = {
id = 742,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1743695999
},
showEndTime = {
seconds = 1743695999
},
showBeginTime = {
seconds = 1743091200
}
},
labelId = 1,
backgroundUrl = {
"langrenxinditu.astc"
},
lowVersion = "1.3.78.58",
activityName = "福利抢先知",
activityParam = {
32,
3494,
630126
},
activityDesc = "稀有面饰-不等式眼镜免费送！一起来搞怪！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63026,
63027,
63028,
63029,
63030
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
32
},
titleType = 0,
activityBeginCleanCoin = {
3494
},
activitySubName = "免费稀有头饰",
platforms = {
3
}
},
[743] = {
id = 743,
timeInfo = {
beginTime = {
seconds = 1743955200
},
endTime = {
seconds = 1744559999
},
showEndTime = {
seconds = 1744559999
},
showBeginTime = {
seconds = 1743955200
}
},
labelId = 2,
backgroundUrl = {
"faxian_songzhaoji_250404.astc"
},
lowVersion = "*********",
activityName = "送召集能量",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63031
},
showInCenter = true,
titleType = 0,
activitySubName = "加速领时装",
activityGroup = v32,
platforms = v26
},
[744] = {
id = 744,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1744905599
},
showEndTime = {
seconds = 1744905599
},
showBeginTime = {
seconds = 1744300800
}
},
labelId = 2,
backgroundUrl = {
"faxian_kuanghuan_250404.astc"
},
lowVersion = "*********",
activityName = "晋级赛狂欢",
activityParam = {
24,
3495,
630149
},
activityDesc = "炫彩时装-拂春语免费领！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63032,
63033,
63034,
63035
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3495
},
activitySubName = "幸运对局上线",
platforms = v26,
activityGroupList = {
"AG_Main"
}
},
[745] = {
id = 745,
timeInfo = {
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1745164799
},
showEndTime = {
seconds = 1745164799
},
showBeginTime = {
seconds = 1744300800
}
},
labelId = 3,
backgroundUrl = {
"faxian_chunri_250404.astc"
},
lowVersion = "*********",
activityName = "春日领好礼",
activityUIDetail = v16,
tagId = 1,
activityTaskGroup = {
63036
},
showInCenter = true,
activityNameType = v21,
titleType = 0,
activitySubName = "登录送磷虾",
platforms = v26
},
[750] = {
id = 750,
timeInfo = {
beginTime = {
seconds = 1742140800
},
endTime = {
seconds = 1751299199
},
showEndTime = {
seconds = 1751299199
},
showBeginTime = {
seconds = 1742140800
}
},
labelId = 2,
backgroundUrl = {
"faxian_ganen241121_2.astc"
},
lowVersion = "1.3.26.111",
activityName = "感恩有你",
activityUIDetail = v17,
isInBottom = 1,
tagId = 7,
activityTaskGroup = {
60820,
60821,
60822
},
showInCenter = true,
titleType = 0,
activitySubName = "多页签之多条",
platforms = v26,
activityTabSplitGroup = {
1,
1,
1
}
},
[751] = {
id = 751,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1742140800
},
endTime = {
seconds = 1751299199
},
showEndTime = {
seconds = 1751299199
},
showBeginTime = {
seconds = 1742140800
}
},
labelId = 2,
backgroundUrl = {
"faxian_qingdian_wx.astc"
},
activityName = "庆典不停歇",
activityParam = {
24,
3455,
606138
},
activityDesc = "热舞突袭免费领，周年派对赶紧舞起来！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 7,
activityTaskGroup = {
60823,
60824,
60825,
60826
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
clientParams = {
"页签1",
"页签2",
"页签3"
},
titleType = 0,
activityBeginCleanCoin = {
3455
},
activitySubName = "多页签之线性",
platforms = v26,
activityTabSplitGroup = {
2,
1,
1
}
},
[752] = {
id = 752,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1742140800
},
endTime = {
seconds = 1751299199
},
showEndTime = {
seconds = 1751299199
},
showBeginTime = {
seconds = 1742140800
}
},
labelId = 2,
backgroundUrl = {
"faxian_chunri_250314.astc"
},
lowVersion = "1.3.98.1",
activityName = "悠闲春日季",
activityParam = {
25,
3491
},
activityDesc = "悠闲春日季，模板活动~",
activityUIDetail = v19,
isInBottom = 1,
tagId = 7,
activityTaskGroup = {
60827,
60828
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = {
"奖励商城",
"任务一"
},
titleType = 0,
activityBeginCleanCoin = {
3491
},
activitySubName = "多页签之兑换",
currencyCfg = {
3491
},
platforms = v26
},
[753] = {
id = 753,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1742140800
},
endTime = {
seconds = 1744905599
},
showEndTime = {
seconds = 1744905599
},
showBeginTime = {
seconds = 1742140800
}
},
labelId = 3,
backgroundUrl = {
"T_Lmg_RegularExchange_Bag_Z_02_N_A.astc",
"T_Activity_TestTitle_5.astc"
},
lowVersion = "1.3.98.1",
activityName = "梦想星之队",
activityParam = {
121,
2031
},
activityDesc = "集组队花花，得限时称号！",
activityUIDetail = "UI_Activity_ExchangeNormalView",
isInBottom = 1,
tagId = 7,
activityTaskGroup = {
60829
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
121
},
titleType = 0,
activityBeginCleanCoin = {
2031
},
activitySubName = "任务兑换",
currencyCfg = {
2031
},
platforms = v26
},
[860] = {
id = 860,
activityType = "ATTwoPeopleSquad",
timeInfo = {
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1746979199
},
showEndTime = {
seconds = 1746979199
},
showBeginTime = {
seconds = 1743436800
}
},
labelId = 2,
activityName = "双人成团",
activityParam = {
3603
},
activityUIDetail = "UI_DoubleFormationActivity",
tagId = 1,
activityTaskGroup = {
60860,
60861,
60862,
60863,
60864
},
showInCenter = true,
activityNameType = "ANTTwoPeopleSquad",
clientParams = {
"608600",
"608602",
"608604",
"608606",
"608607",
"608609",
"608611",
"608613",
"608614",
"608616",
"608618",
"608620",
"608621"
},
titleType = 1,
activityBeginCleanCoin = {
3603
},
activitySubName = "共领好礼",
thumbnail = "0"
},
[760] = {
id = 760,
activityType = "ATTaskRunOnActivitySvr",
timeInfo = {
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1746979199
},
showEndTime = {
seconds = 1746979199
},
showBeginTime = {
seconds = 1745510400
}
},
labelId = 1,
lowVersion = "********08",
activityName = "喵喵来签到",
activityUIDetail = "UI_Meow_Maininterface",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63050,
63051,
63052
},
showInCenter = true,
titleType = 0,
activitySubName = "周周领好礼",
platforms = v26
},
[765] = {
id = 765,
activityType = "ATNNWelfare",
timeInfo = {
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1745510399
},
showEndTime = {
seconds = 1745510399
},
showBeginTime = {
seconds = 1743436800
}
},
labelId = 2,
activityName = "星宝守护者",
activityParam = {
4,
2019
},
activityUIDetail = "UI_PublicWelfare",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63060,
63061,
63062,
63063,
63064
},
showInCenter = true,
activityNameType = "ANTWelfareAerospaceTechEd",
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
2019
},
activitySubName = "领专属背饰",
platforms = v26
},
[767] = {
id = 767,
activityType = "ATWishingTree",
timeInfo = {
beginTime = {
seconds = 1731081600
},
endTime = {
seconds = 1744732799
},
showEndTime = {
seconds = 1744732799
},
showBeginTime = {
seconds = 1731081600
}
},
labelId = 3,
activityName = "许愿树测试",
activityUIDetail = "UI_Activity_WishingTreeView",
isInBottom = 1,
tagId = 7,
showInCenter = true,
activityNameType = "ANTWishingTree",
titleType = 0,
activitySubName = "test",
platforms = v26
},
[769] = {
id = 769,
timeInfo = {
beginTime = {
seconds = 1746633600
},
endTime = {
seconds = 1759247999
},
showEndTime = {
seconds = 1759247999
},
showBeginTime = {
seconds = 1746633600
}
},
labelId = 4,
backgroundUrl = {
"dingyuexyx.astc"
},
activityName = "订阅领好礼",
activityUIDetail = v17,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
70017,
70018
},
showInCenter = true,
titleType = 0,
activitySubName = "星愿币送不停",
platforms = {
4
}
},
[780] = {
id = 780,
activityType = "ATSticker",
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1747929599
},
showEndTime = {
seconds = 1747929599
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 3,
activityName = "萌动蜜桃猫",
activityUIDetail = "UI_Activity_PeachCatBingo_Mainview",
isInBottom = 1,
tagId = 7,
activityTaskGroup = {
63800,
63801,
63802
},
showInCenter = true,
activityNameType = "ANTSticker",
titleType = 0,
activityBeginCleanCoin = {
3546
},
activitySubName = "联动时装头饰",
activityGroup = "AG_Common",
platforms = v26
},
[770] = {
id = 770,
activityType = "ATRecruitOrder",
timeInfo = {
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1750262399
},
showEndTime = {
seconds = 1750262399
},
showBeginTime = {
seconds = 1746028800
}
},
labelId = 9,
backgroundUrl = {
"T_Convened_Img_Background.astc"
},
activityName = "携友同行",
activityParam = {
20000009,
20000009
},
activityDesc = "得紫萝萝痛包",
activityUIDetail = "UI_Activity_SummerCallUp_MainView",
tagId = 1,
activityTaskGroup = {
63070,
63071
},
showInCenter = true,
activityNameType = v22,
titleType = 0,
activityBeginCleanCoin = {
3547
},
activitySubName = "赢元梦周边",
taskGroupsDrawBase = {
63071
}
},
[771] = {
id = 771,
timeInfo = {
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1746979199
},
showEndTime = {
seconds = 1746979199
},
showBeginTime = {
seconds = 1746115200
}
},
labelId = 2,
backgroundUrl = {
"faxian_miaolijuexing_250429.astc"
},
lowVersion = "********",
activityName = "喵力觉醒中",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63072,
63073,
63074,
63075,
63076
},
showInCenter = true,
titleType = 0,
activitySubName = "领喵喵节头饰",
activityGroup = v32,
platforms = v26
},
[772] = {
id = 772,
timeInfo = {
beginTime = {
seconds = 1745942400
},
endTime = {
seconds = 1746719999
},
showEndTime = {
seconds = 1746719999
},
showBeginTime = {
seconds = 1745942400
}
},
labelId = 2,
backgroundUrl = {
"faxian_xinsaiji3_250429.astc"
},
lowVersion = "*********",
activityName = "新赛季来袭",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63077,
63084
},
showInCenter = true,
titleType = 0,
activitySubName = "福利大集结",
activityGroup = v32,
platforms = v26
},
[766] = {
id = 766,
activityType = "ATSpringSlip",
timeInfo = {
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1746979199
},
showEndTime = {
seconds = 1746979199
},
showBeginTime = {
seconds = 1746028800
}
},
labelId = 3,
lowVersion = "********",
activityName = "喵喵相册礼",
activityUIDetail = "UI_ActivityStarBaby_MainView",
isInBottom = 1,
activityRuleId = 357,
tagId = 1,
activityTaskGroup = {
63078,
63079,
63080,
63081,
63082,
63083
},
showInCenter = true,
activityNameType = "ANTSpringSlip",
clientParams = {
"任务",
"邀请"
},
titleType = 0,
activitySubName = "得紫萝萝背包",
currencyCfg = {
200800
},
platforms = v26,
activityGroupList = {
"AG_Main"
}
},
[781] = {
id = 781,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1747929599
},
showEndTime = {
seconds = 1747929599
},
showBeginTime = {
seconds = 1747324800
}
},
labelId = 2,
backgroundUrl = {
"faxian_qingding_250515.astc"
},
lowVersion = "********",
activityName = "情定之时",
activityParam = {
25,
3548
},
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63803,
63804,
63805
},
showInCenter = true,
activityNameType = v23,
activityShopType = v27,
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3548
},
activitySubName = "领520头饰",
platforms = v26
},
[782] = {
id = 782,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1748534399
},
showEndTime = {
seconds = 1748534399
},
showBeginTime = {
seconds = 1747929600
}
},
labelId = 2,
backgroundUrl = {
"faxian_wangwang_250515.astc"
},
lowVersion = "********08",
activityName = "汪汪队长到",
activityParam = {
24,
3549,
630411
},
activityDesc = "完成任务免费领汪星人时装！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63806,
63807,
63808
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3549
},
activitySubName = "送汪星人时装",
platforms = v26,
activityGroupList = {
"AG_Main"
}
},
[790] = {
id = 790,
activityType = "ATLotteryDraw",
timeInfo = {
beginTime = {
seconds = 1747670400
},
endTime = {
seconds = 1749743999
},
showEndTime = {
seconds = 1749743999
},
showBeginTime = {
seconds = 1747670400
}
},
labelId = 4,
activityName = "阿卓来漫游",
activityUIDetail = "UI_Activity_zZoton_Mainview",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
66010,
66011,
66012,
66024
},
showInCenter = true,
activityNameType = "ANLotteryDraw",
titleType = 0,
activityBeginCleanCoin = {
3632
},
activitySubName = "联动炫彩时装",
activityGroup = v32,
currencyCfg = {
3632
},
platforms = v26
},
[791] = {
id = 791,
timeInfo = {
beginTime = {
seconds = 1747670400
},
endTime = {
seconds = 1750435199
},
showEndTime = {
seconds = 1750435199
},
showBeginTime = {
seconds = 1747670400
}
},
labelId = 2,
backgroundUrl = {
"faxian_zongyi1_wx.astc"
},
activityName = "就是粽意你",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63810,
63811,
63812,
63813,
63822
},
showInCenter = true,
titleType = 0,
activitySubName = "登录领星愿币",
platforms = {
3
}
},
[792] = {
id = 792,
timeInfo = {
beginTime = {
seconds = 1747670400
},
endTime = {
seconds = 1750435199
},
showEndTime = {
seconds = 1750435199
},
showBeginTime = {
seconds = 1747670400
}
},
labelId = 2,
backgroundUrl = {
"faxian_zongyi1_qq.astc"
},
activityName = "就是粽意你",
activityUIDetail = v17,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63814,
63815,
63816,
63817,
63823
},
showInCenter = true,
titleType = 0,
activitySubName = "登录领星愿币",
platforms = {
3
}
},
[793] = {
id = 793,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1750348799
},
showEndTime = {
seconds = 1750348799
},
showBeginTime = {
seconds = 1749139200
}
},
labelId = 2,
backgroundUrl = {
"faxian_qingliang1_250529.astc"
},
lowVersion = "********12",
activityName = "清凉夏日礼",
activityParam = {
32,
3633
},
activityUIDetail = v19,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63818,
63819,
63820,
63821
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
32
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3633
},
activitySubName = "送稀有时装",
platforms = v26
},
[794] = {
id = 794,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1750348799
},
showEndTime = {
seconds = 1750348799
},
showBeginTime = {
seconds = 1749744000
}
},
labelId = 2,
backgroundUrl = {
"faxian_qingchun_250612.astc"
},
lowVersion = "********53",
activityName = "青春不散场",
activityParam = {
24,
3635,
630506
},
activityDesc = "与学长学姐一起开启泡泡大战！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63830,
63831,
63832
},
showInCenter = true,
activityNameType = v22,
activityShopType = v28,
titleType = 0,
activityBeginCleanCoin = {
3635
},
activitySubName = "泡泡大战返场",
platforms = v26
},
[795] = {
id = 795,
activityType = "ATUpdateForesight",
timeInfo = {
beginTime = {
seconds = 1750348800
},
endTime = {
seconds = 1750953599
},
showEndTime = {
seconds = 1750953599
},
showBeginTime = {
seconds = 1750348800
}
},
labelId = 2,
lowVersion = "********53",
activityName = "订阅新赛季",
activityParam = {
630513,
630514
},
activityUIDetail = "UI_NewVersion_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63834,
63833
},
showInCenter = true,
activityNameType = "ANTUpdateForesightV1",
activitySubName = "星愿币轻松领",
platforms = v26
},
[796] = {
id = 796,
activityType = v2,
timeInfo = {
beginTime = {
seconds = 1750348800
},
endTime = {
seconds = 1751212799
},
showEndTime = {
seconds = 1751212799
},
showBeginTime = {
seconds = 1750348800
}
},
labelId = 3,
backgroundUrl = {
"faxian_kuaishan_250612.astc"
},
lowVersion = "********53",
activityName = "快闪季将袭",
activityParam = {
37,
3636,
630525
},
activityDesc = "神秘内容抢先知，福利好礼享不停！",
activityUIDetail = v18,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
63835,
63836,
63837,
63838,
63839
},
showInCenter = true,
activityNameType = v22,
activityShopType = {
37
},
titleType = 0,
activityBeginCleanCoin = {
3636
},
activitySubName = "前瞻福利曝光",
platforms = v26
},
[797] = {
id = 797,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1750348800
},
endTime = {
seconds = 1752249599
},
showEndTime = {
seconds = 1752249599
},
showBeginTime = {
seconds = 1750348800
}
},
labelId = 2,
backgroundUrl = {
"faxian_baobao_250612.astc"
},
lowVersion = "********53",
activityName = "宝宝游园记",
activityParam = {
32,
3638
},
activityUIDetail = v19,
isInBottom = 1,
activityRuleId = 374,
tagId = 1,
activityTaskGroup = {
63840,
63841,
63842,
63843
},
showInCenter = true,
activityNameType = v23,
activityShopType = {
32
},
clientParams = v24,
titleType = 0,
activityBeginCleanCoin = {
3638
},
activitySubName = "天线宝宝返场",
platforms = v26
},
[798] = {
id = 798,
activityType = "ATLotteryDraw",
timeInfo = {
beginTime = {
seconds = 1747670400
},
endTime = {
seconds = 1751299199
},
showEndTime = {
seconds = 1751299199
},
showBeginTime = {
seconds = 1747670400
}
},
labelId = 4,
activityName = "chiikawa来漫游",
activityUIDetail = "UI_Chikawa_MainView",
isInBottom = 1,
tagId = 7,
activityTaskGroup = {
63850,
63851,
63852,
63853
},
showInCenter = true,
activityNameType = "ANLotteryDraw",
titleType = 0,
activityBeginCleanCoin = {
3632
},
activityGroup = v32,
currencyCfg = {
3632
},
platforms = v26
}
}

local mt = {
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1701792000
},
endTime = {
seconds = 1701964799
},
showEndTime = {
seconds = 1701964799
},
showBeginTime = {
seconds = 1701792000
}
},
showInCenter = false,
slapFace = false,
activityNameType = "ANTTotalLogin",
isHideMainBackground = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
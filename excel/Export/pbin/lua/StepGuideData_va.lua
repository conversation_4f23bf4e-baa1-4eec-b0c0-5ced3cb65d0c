--com.tencent.wea.xlsRes.table_StepGuideData => excel/xls/X_新手引导表_VA.xlsx: 新引导步骤

local v0 = 1001

local v1 = 1005

local data = {
[800001] = {
StepID = 800001,
GuideID = 800000,
Comment = "弹出欢迎指引",
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "欢迎来到元梦之星",
bAnyWhereNext = true,
bMask = true
},
[800002] = {
StepID = 800002,
GuideID = 800000,
Comment = "点击开始游戏",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_WXGame_LobbyView",
WidgetName = "w_overlay_StartGame",
ButtonName = "w_btn_Start",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "快来开一局吧！",
UIOffset_Second = "{X=-300, Y = -300}",
bMask = true,
IsKeyStep = true
},
[800003] = {
StepID = 800003,
GuideID = 800001,
Comment = "弹出欢迎指引",
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "欢迎来到谁是狼人",
bAnyWhereNext = true,
bMask = true
},
[800004] = {
StepID = 800004,
GuideID = 800001,
Comment = "打开指引UI",
SubStep = 2,
StepType = 8,
TypeParams = "{actionId = 13}",
IsForceGuide = true
},
[800005] = {
StepID = 800005,
GuideID = 800001,
Comment = "等待指引UI关闭",
SubStep = 3,
StepType = 7,
TypeParams = "{windowName=\"UI_WXGame_LobbyView_SkipPop_up\", isWait=true}"
},
[800006] = {
StepID = 800006,
GuideID = 800001,
Comment = "等待一会",
SubStep = 4,
StepType = 3,
TypeParams = "{delay = 0.5, autoEnd =true}",
IsForceGuide = true
},
[800007] = {
StepID = 800007,
GuideID = 800001,
Comment = "点击开始游戏",
SubStep = 5,
IsForceGuide = true,
WindowName = "UI_WXGame_LobbyView",
WidgetName = "w_overlay_StartGame",
ButtonName = "w_btn_Start",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "快来开一局吧！",
UIOffset_Second = "{X=-300, Y = -300}",
bMask = true,
IsKeyStep = true
},
[800010] = {
StepID = 800010,
GuideID = 800002,
Comment = "弹出欢迎指引",
IsForceGuide = true,
UIStyle_Frist = 1011,
UIText_Frist = "欢迎来到星宝农场",
bAnyWhereNext = true,
bMask = true
},
[800014] = {
StepID = 800014,
GuideID = 800002,
Comment = "点击开始游戏",
SubStep = 5,
IsForceGuide = true,
WindowName = "UI_WXGame_LobbyView",
WidgetName = "w_overlay_StartGame",
ButtonName = "w_btn_Goto",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "快来开一局吧！",
UIOffset_Second = "{X=-300, Y = -300}",
bMask = true,
IsKeyStep = true
},
[800015] = {
StepID = 800015,
GuideID = 800003,
Comment = "引导回到大厅",
WindowName = "UI_Farmyard_Mainview",
WidgetName = "w_btn_Quit",
ButtonName = "w_btn_Quit",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-180, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "更多精彩玩法，可以去广场探索一下哦",
UIOffset_Second = "{X=-480, Y =130}",
bOtherButtonExit = true
},
[800020] = {
StepID = 800020,
GuideID = 800004,
Comment = "引导回到农场",
WindowName = "UILobbyView",
WidgetName = "w_btn_Farmyard",
ButtonName = "w_btn_Farmyard",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-150, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击这里，可以返回农场哦",
UIOffset_Second = "{X=-250, Y = -250}",
bAnyWhereNext = true,
IsKeyStep = true,
bOtherButtonComplete = true,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[800021] = {
StepID = 800021,
GuideID = 800005,
Comment = "引导点击设置",
IsForceGuide = true,
WindowName = "UI_PlayerInfo",
WidgetName = "w_btn_ShowConfigTab",
ButtonName = "w_btn_ShowConfigTab",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-250, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击这里可以修改形象",
UIOffset_Second = "{X=-600, Y = 100}",
bMask = true
},
[800022] = {
StepID = 800022,
GuideID = 800005,
Comment = "引导点击修改形象",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_PlayerInfo",
GetWidgetFunc = "GetButtonReChoose",
GetButtonFunc = "GetButtonReChoose",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-250, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击这里可以修改形象",
UIOffset_Second = "{X=-600, Y = 100}",
bMask = true,
IsKeyStep = true
},
[800023] = {
StepID = 800023,
GuideID = 800006,
Comment = "点击模式按钮（弱引导）",
StepType = 3,
TypeParams = "{delay = 0.5}",
WindowName = "UILobbyView",
WidgetName = "w_btn_ModelSelect",
ButtonName = "w_btn_ModelSelect",
UIStyle_Frist = 1001,
UIOffset_Frist = "{X=-40, Y = 80}",
UIStyle_Second = 1005,
UIText_Second = "点击这里查看更多玩法！",
UIOffset_Second = "{X=-226, Y = 172}",
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[800024] = {
StepID = 800024,
GuideID = 800006,
Comment = "引导玩家切换模式",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_Model_MainView",
UIStyle_Frist = 1011,
UIText_Frist = "乐园里项目新奇多变，玩法层出不穷！走过路过，不要错过各种精彩纷呈的玩法！",
bAnyWhereNext = true,
IsKeyStep = true,
bOtherButtonComplete = true
},
[800025] = {
StepID = 800025,
GuideID = 800006,
Comment = "查看副玩法",
SubStep = 3,
StepType = 3,
TypeParams = "{delay = 0}",
IsForceGuide = true,
WindowName = "UI_Model_MainView",
WidgetName = "w_image_guide",
UIStyle_Frist = 1014,
UIOffset_Frist = "{X=-310, Y =-440}",
UIStyle_Second = 1005,
UIText_Second = "海量玩法模式已解锁~",
UIOffset_Second = "{X=-530, Y =-630}",
bAnyWhereNext = true,
bOtherButtonComplete = true
},
[800026] = {
StepID = 800026,
GuideID = 800006,
Comment = "等待主界面打开",
SubStep = 4,
StepType = 10,
TypeParams = "{windowNameList = {\"UI_TeamShow_LobbyView\", \"UILobbyView\"}}",
IsForceGuide = true
},
[800027] = {
StepID = 800027,
GuideID = 800006,
Comment = "点击开始按钮",
SubStep = 5,
WindowName = "UILobbyView",
WidgetName = "w_btn_StartGame",
ButtonName = "w_btn_StartGame",
UIStyle_Frist = 1001,
UIOffset_Frist = "{X=-50, Y = 60}",
UIStyle_Second = 1005,
UIText_Second = "开始你选择的比赛吧！",
UIOffset_Second = "{X=-60, Y = 180}",
IsKeyStep = true,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[800028] = {
StepID = 800028,
GuideID = 800006,
Comment = "等待玩家关卡结束",
SubStep = 6,
StepType = 3,
TypeParams = "{delay = 1,autoEnd = true}",
IsKeyStep = true
},
[800040] = {
StepID = 800040,
GuideID = 800007,
Comment = "永远不完成",
StepType = 3,
TypeParams = "{delay = 0}"
},
[800041] = {
StepID = 800041,
GuideID = 800008,
Comment = "永远不完成",
StepType = 3,
TypeParams = "{delay = 0}"
},
[800042] = {
StepID = 800042,
GuideID = 800009,
Comment = "hud界面，引导玩家进行一场游戏（弱引导1级）",
WindowName = "UI_WXGame_LobbyView",
GetWidgetFunc = "OnGetStartMatchGuideButton",
GetButtonFunc = "OnGetStartMatchGuideButton",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-250, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "快来开一局吧！",
UIOffset_Second = "{X=-565, Y = -300}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800043] = {
StepID = 800043,
GuideID = 800010,
Comment = "引导点击新手任务入口（2级）",
IsForceGuide = true,
WindowName = "UI_WXGame_LobbyView",
WidgetName = "w_Size_NoviceReward",
ButtonName = "w_btn_NoviceReward",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-150, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "点击这里查看新手福利",
UIOffset_Second = "{X=-300, Y = 150}",
bMask = true,
bTriggerOnlyUIShowOnTop = true
},
[800044] = {
StepID = 800044,
GuideID = 800010,
Comment = "介绍新手界面",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_NoviceReward_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "哇，好多新手福利，快来看看吧！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800045] = {
StepID = 800045,
GuideID = 800011,
Comment = "hud界面，引导玩家再开一局（2级）",
WindowName = "UI_WXGame_LobbyView",
GetWidgetFunc = "OnGetStartMatchGuideButton",
GetButtonFunc = "OnGetStartMatchGuideButton",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-250, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "再来开一局吧！",
UIOffset_Second = "{X=-565, Y = -300}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800046] = {
StepID = 800046,
GuideID = 800012,
Comment = "引导收藏功能（2级）（强引导）",
IsForceGuide = true,
WindowName = "UI_WXGame_LobbyView",
WidgetName = "w_overlay_Collect",
ButtonName = "w_btn_Collect",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-150, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "点个收藏不迷路！",
UIOffset_Second = "{X=-300, Y = 150}",
bMask = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800047] = {
StepID = 800047,
GuideID = 800013,
Comment = "引导点击更多玩法（2级）",
WindowName = "UI_WXGame_LobbyView",
GetWidgetFunc = "OnGetSelectNextIndexWidget",
GetButtonFunc = "OnGetSelectNextIndexWidget",
UIStyle_Frist = 1001,
UIText_Frist = "6",
UIOffset_Frist = "{X= 150, Y = -50}",
UIStyle_Second = 1005,
UIText_Second = "来试试其他玩法吧！",
UIOffset_Second = "{X= 200, Y = -50}",
bOtherButtonExit = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800048] = {
StepID = 800048,
GuideID = 800014,
Comment = "hud界面，引导玩家再开一局（2级）",
WindowName = "UI_WXGame_LobbyView",
GetWidgetFunc = "OnGetStartMatchGuideButton",
GetButtonFunc = "OnGetStartMatchGuideButton",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-250, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "再来开一局吧！",
UIOffset_Second = "{X=-565, Y = -300}",
bOtherButtonExit = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800049] = {
StepID = 800049,
GuideID = 800015,
Comment = "引导点击每日奖杯挑战，同时引导奖杯征程（强引导）（3级）",
IsForceGuide = true,
WindowName = "UI_WXGame_LobbyView",
WidgetName = "w_namedSlot_DailyTrophyItem",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-300, Y = -50}",
UIStyle_Second = 1005,
UIText_Second = "参与对局可以获得大量奖杯，记得完成【每日奖杯挑战】，奖励很丰厚呢！",
UIOffset_Second = "{X=-800, Y = -80}",
bAnyWhereNext = true,
bMask = true,
bTriggerOnlyUIShowOnTop = true
},
[800050] = {
StepID = 800050,
GuideID = 800015,
Comment = "引导奖杯征程入口",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_WXGame_LobbyView",
GetWidgetFunc = "OnGetCupButtonWidget",
GetButtonFunc = "OnGetCupButtonWidget",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-150, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "让我们看看【奖杯征程】吧！",
UIOffset_Second = "{X=-350, Y = 150}",
bMask = true,
bTriggerOnlyUIShowOnTop = true
},
[800051] = {
StepID = 800051,
GuideID = 800015,
Comment = "打开奖杯征程时引导介绍",
SubStep = 3,
WindowName = "UI_Cup_Bg",
UIStyle_Frist = 2005,
UIText_Frist = "参与玩法和任务能够获得大量奖杯，达到一定的奖杯数，能够领取丰厚的奖励哦！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800052] = {
StepID = 800052,
GuideID = 800016,
Comment = "引导星世界（4级）",
WindowName = "UI_WXGame_LobbyView",
WidgetName = "OverlayUGC",
ButtonName = "w_Btn_StarWorld",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-150, Y = -150}",
UIStyle_Second = 1005,
UIText_Second = "【星世界】中有丰富的地图玩法，来看看吧！",
UIOffset_Second = "{X=-300, Y = -300}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800053] = {
StepID = 800053,
GuideID = 800017,
Comment = "背包引导（1级以上）-新手-引导商城（弱引导）介绍获取途径",
IsForceGuide = true,
WindowName = "UI_Bag_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "穿上新时装，一下好看了很多！商城里也有许多漂亮的时装哦！去看看吧！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bTriggerOnlyUIShowOnTop = true
},
[800054] = {
StepID = 800054,
GuideID = 800017,
Comment = "背包引导（1级以上）-新手-引导商城（弱引导）点击商城",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_Bag_MainView",
WidgetName = "w_btn_Mall",
ButtonName = "w_btn_Mall",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-120, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "去商城逛逛吧！",
UIOffset_Second = "{X=0, Y = 50}",
bMask = true,
bTriggerOnlyUIShowOnTop = true
},
[800055] = {
StepID = 800055,
GuideID = 800017,
Comment = "背包引导（1级以上）-新手-引导商城（弱引导）介绍商城",
SubStep = 3,
IsForceGuide = true,
WindowName = "UI_NewMall_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "商城里有大量精美的时装，可以通过各种活动获得！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800056] = {
StepID = 800056,
GuideID = 800018,
Comment = "背包引导（5级以上）-老玩家-快速换装",
IsForceGuide = true,
WindowName = "UI_WXGame_LobbyView",
WidgetName = "w_btn_ChangeSuit",
ButtonName = "w_btn_ChangeSuit",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-200, Y = -50}",
UIStyle_Second = 1005,
UIText_Second = "点击便捷入口，可以快速换装哦！",
UIOffset_Second = "{X= -500, Y = 50}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800057] = {
StepID = 800057,
GuideID = 800018,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）-高亮右侧面板",
SubStep = 2,
StepType = 8,
TypeParams = "{actionId = 15}",
IsForceGuide = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800058] = {
StepID = 800058,
GuideID = 800018,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）-高亮右侧面板",
SubStep = 3,
StepType = 10,
TypeParams = "{windowNameList = {\"UI_Bag_ChangeSuitSlotView\"}}",
IsForceGuide = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800059] = {
StepID = 800059,
GuideID = 800018,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）-高亮右侧面板",
SubStep = 4,
IsForceGuide = true,
WindowName = "UI_Bag_ChangeSuitSlotView",
GetWidgetFunc = "GetGuideWidget",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-300, Y = -50}",
UIStyle_Second = 1005,
UIText_Second = "在右侧面板可以快速切换装扮！",
UIOffset_Second = "{X= -600, Y = 50}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800060] = {
StepID = 800060,
GuideID = 800018,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）-切换页签",
SubStep = 5,
StepType = 8,
TypeParams = "{actionId = 16}",
IsForceGuide = true,
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800061] = {
StepID = 800061,
GuideID = 800019,
Comment = "停留在hud界面5秒不操作则引导开一局",
WindowName = "UI_WXGame_LobbyView",
GetWidgetFunc = "OnGetStartMatchGuideButton",
GetButtonFunc = "OnGetStartMatchGuideButton",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-250, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "快来开一局吧！",
UIOffset_Second = "{X=-565, Y = -300}",
bOtherButtonExit = true,
bTriggerOnlyUIShowOnTop = true
},
[800062] = {
StepID = 800062,
GuideID = 800020,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）-介绍",
IsForceGuide = true,
WindowName = "UI_WXGame_LobbyView",
UIStyle_Frist = 2005,
UIText_Frist = "刚才获得了新时装，快去背包里看看吧！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
bTriggerOnlyUIShowOnTop = true
},
[800063] = {
StepID = 800063,
GuideID = 800020,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）-点击背包",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_WXGame_LobbyView",
WidgetName = "w_btn_ChangeSuit",
ButtonName = "w_btn_ChangeSuit",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-200, Y = -50}",
UIStyle_Second = 1005,
UIText_Second = "打开背包，查看新时装",
UIOffset_Second = "{X= -500, Y = 50}",
bMask = true,
bTriggerOnlyUIShowOnTop = true
},
[800064] = {
StepID = 800064,
GuideID = 800020,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）-高亮右侧面板",
SubStep = 3,
StepType = 8,
TypeParams = "{actionId = 15}",
IsForceGuide = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800065] = {
StepID = 800065,
GuideID = 800020,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）-高亮右侧面板",
SubStep = 4,
StepType = 10,
TypeParams = "{windowNameList = {\"UI_Bag_ChangeSuitSlotView\"}}",
IsForceGuide = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800066] = {
StepID = 800066,
GuideID = 800020,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）-高亮右侧面板",
SubStep = 5,
IsForceGuide = true,
WindowName = "UI_Bag_ChangeSuitSlotView",
GetWidgetFunc = "GetGuideWidget",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-300, Y = -50}",
UIStyle_Second = 1005,
UIText_Second = "在右侧面板可以快速切换装扮！",
UIOffset_Second = "{X= -600, Y = 50}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800067] = {
StepID = 800067,
GuideID = 800020,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）-切换页签",
SubStep = 6,
StepType = 8,
TypeParams = "{actionId = 16}",
IsForceGuide = true,
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800070] = {
StepID = 800070,
GuideID = 800021,
Comment = "背包引导（1级以上）-新手-穿戴保存（强引导）",
StepType = 8,
TypeParams = "{actionId = 14}",
IsForceGuide = true,
WindowName = "UI_Bag_MainView",
bMask = true,
bTriggerOnlyUIShowOnTop = true
},
[800071] = {
StepID = 800071,
GuideID = 800021,
Comment = "背包引导（1级以上）-新手-穿戴保存（强引导）",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_Bag_MainView",
GetWidgetFunc = "GetNewItemWidgetByItemType",
GetButtonFunc = "GetNewItemWidgetByItemType",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-150, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "换上新时装",
UIOffset_Second = "{X= -300, Y = 150}",
bMask = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800072] = {
StepID = 800072,
GuideID = 800021,
Comment = "等待装扮加载, 显示保存搭配按钮",
SubStep = 3,
StepType = 13,
TypeParams = "{actionId=3, funcParams={windowName=\"UI_Bag_MainView\", getWidgetFunc=\"GetTabMakeUpItemBtnSave\"}}",
IsForceGuide = true,
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-50, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "点击保存搭配",
UIOffset_Second = "{X=-350, Y = -320}",
bTriggerOnlyUIShowOnTop = true
},
[800073] = {
StepID = 800073,
GuideID = 800021,
Comment = "保存搭配",
SubStep = 4,
IsForceGuide = true,
WindowName = "UI_Bag_MainView",
GetWidgetFunc = "GetTabMakeUpItemBtnSave",
GetButtonFunc = "GetTabMakeUpItemBtnSave",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-50, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "点击保存搭配",
UIOffset_Second = "{X=-350, Y = -320}",
bTriggerOnlyUIShowOnTop = true
},
[800090] = {
StepID = 800090,
GuideID = 800040,
Comment = "新版hud界面，引导玩家点击主推位1",
WindowName = "UI_WXGame_NewLobbyView",
WidgetName = "w_btn_Rec1",
ButtonName = "w_btn_Rec1",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-260, Y =120}",
UIStyle_Third = 1005,
UIText_Third = "快来开一局吧！",
UIOffset_Third = "{X=-700, Y =200}",
UIStyle_Second = 1003,
UIOffset_Second = "{X=0, Y = 0}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800092] = {
StepID = 800092,
GuideID = 800042,
Comment = "引导祈愿入口",
WindowName = "UI_WXGame_NewLobbyView",
GetWidgetFunc = "GetGuideLotteryButton",
GetButtonFunc = "GetGuideLotteryButton",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y =0}",
UIStyle_Second = 1005,
UIText_Second = "第一次十连抽<Highlight31>免费送</>，赶紧来试试！",
UIOffset_Second = "{X=-320, Y =160}",
bOtherButtonExit = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800093] = {
StepID = 800093,
GuideID = 800043,
Comment = "引导快速开始游戏",
StepType = 3,
TypeParams = "{delay = 1}",
WindowName = "UI_WXGame_NewLobbyView",
WidgetName = "w_btn_Start",
ButtonName = "w_btn_Start",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-250, Y = -200}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y =0}",
UIStyle_Second = 1006,
UIText_Second = "这里可以快速开始刚才玩过的游戏，再来一局吧！",
UIOffset_Second = "{X=-565, Y = -300}",
IsKeyStep = true,
bOtherButtonComplete = true,
bTriggerOnlyUIShowOnTop = true
},
[800094] = {
StepID = 800094,
GuideID = 800044,
Comment = "引导新手奖励",
WindowName = "UI_WXGame_NewLobbyView",
WidgetName = "w_btn_NoviceReward",
ButtonName = "w_btn_NoviceReward",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-150, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "点击这里查看新手福利",
UIOffset_Second = "{X=-300, Y = 150}",
bTriggerOnlyUIShowOnTop = true
},
[800095] = {
StepID = 800095,
GuideID = 800044,
Comment = "介绍新手界面",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_NoviceReward_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "哇，好多新手福利，快来看看吧！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800096] = {
StepID = 800096,
GuideID = 800045,
Comment = "引导每日奖杯",
WindowName = "UI_WXGame_NewLobbyView",
WidgetName = "UI_WXGame_Cup_LobbyHud_cloud",
ButtonName = "w_btn_Open",
UIStyle_Frist = 1001,
UIText_Frist = "7",
UIOffset_Frist = "{X=220, Y = 50}",
UIStyle_Second = 1006,
UIText_Second = "你可以通过完成任务和游玩奖杯玩法获得奖杯奖励",
UIOffset_Second = "{X=200, Y =100}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800097] = {
StepID = 800097,
GuideID = 800046,
Comment = "引导玩家切换玩法",
WindowName = "UI_WXGame_NewLobbyView",
WidgetName = "w_btn_ChangeModel",
ButtonName = "w_btn_ChangeModel",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-250, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "最近游玩可以在这里找到",
UIOffset_Second = "{X=-450, Y = -300}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800098] = {
StepID = 800098,
GuideID = 800047,
Comment = "引导玩家查看交友广场",
WindowName = "UI_WXGame_NewLobbyView",
WidgetName = "w_btn_Bottom2",
ButtonName = "w_btn_Bottom2",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "探索广场，结交更多朋友",
UIOffset_Second = "{X=-700, Y =50}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800099] = {
StepID = 800099,
GuideID = 800048,
Comment = "引导玩家查看地图乐园",
WindowName = "UI_WXGame_NewLobbyView",
WidgetName = "w_btn_Bottom3",
ButtonName = "w_btn_Bottom3",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y = 0}",
UIStyle_Second = 1006,
UIText_Second = "探索星地图，发现更多创意玩法",
UIOffset_Second = "{X=-700, Y =50}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800100] = {
StepID = 800100,
GuideID = 800049,
Comment = "引导点击头像",
WindowName = "UI_WXGame_NewLobbyView",
WidgetName = "w_btn_PlayerInfo",
ButtonName = "w_btn_PlayerInfo",
UIStyle_Frist = 1001,
UIText_Frist = "7",
UIOffset_Frist = "{X=50, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "点击头像修改个人信息",
UIOffset_Second = "{X=0, Y =120}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800102] = {
StepID = 800102,
GuideID = 800051,
Comment = "在广场引导返回主页",
StepType = 3,
TypeParams = "{delay = 3}",
WindowName = "UILobbyView",
WidgetName = "w_btn_GotoHUD",
ButtonName = "w_btn_GotoHUD",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-20, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "从这里可以回到小游戏主页",
UIOffset_Second = "{X=-150, Y = 120}",
IsKeyStep = true,
bOtherButtonComplete = true
},
[800104] = {
StepID = 800104,
GuideID = 800054,
Comment = "引导查看任务目标",
WindowName = "UI_WXGame_NewLobbyView",
WidgetName = "w_btn_Open",
ButtonName = "w_btn_Open",
UIStyle_Frist = 1001,
UIText_Frist = "7",
UIOffset_Frist = "{X=200, Y =100}",
UIStyle_Second = 1005,
UIText_Second = "领取奖励，完成下个目标吧！",
UIOffset_Second = "{X=200, Y =200}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800110] = {
StepID = 800110,
GuideID = 800052,
Comment = "跳转到活跃奖池",
StepType = 8,
TypeParams = "{actionId = 3}",
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
bTriggerOnlyUIShowOnTop = true
},
[800111] = {
StepID = 800111,
GuideID = 800052,
Comment = "没送代币到3，送代币到7",
SubStep = 2,
StepType = 5,
TypeParams = "{conditions = {c_isReward = {}}, isTrue = 7 , isFalse = 3}",
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
bTriggerOnlyUIShowOnTop = true
},
[800112] = {
StepID = 800112,
GuideID = 800052,
Comment = "介绍抽奖店",
SubStep = 3,
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "在这里可以获得各种有趣的装扮，真诚许下心愿，说不定就会实现呢！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
bTriggerOnlyUIShowOnTop = true
},
[800113] = {
StepID = 800113,
GuideID = 800052,
Comment = "送代币介绍",
SubStep = 4,
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "乐园里每天都有好事发生！先送你10000星宝印章的见面礼，祝你心想事成！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
bTriggerOnlyUIShowOnTop = true
},
[800114] = {
StepID = 800114,
GuideID = 800052,
Comment = "送代币逻辑",
SubStep = 5,
StepType = 8,
TypeParams = "{actionId = 2}",
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
bMask = true,
bTriggerOnlyUIShowOnTop = true
},
[800115] = {
StepID = 800115,
GuideID = 800052,
Comment = "点击确定",
SubStep = 6,
IsForceGuide = true,
WindowName = "UI_Common_PopRewardView",
GetWidgetFunc = "GetCommonConfirmBtn",
GetButtonFunc = "GetCommonConfirmBtn",
bTriggerOnlyUIShowOnTop = true
},
[800116] = {
StepID = 800116,
GuideID = 800052,
Comment = "点击抽奖按钮",
SubStep = 7,
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
GetWidgetFunc = "GetLotteryBtnWidget_BG",
GetButtonFunc = "GetLotteryBtnWidget",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-80, Y = -230}",
UIStyle_Second = 1005,
UIText_Second = "点击十连抽！",
UIOffset_Second = "{X=-320, Y = -360}",
bMask = true,
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800124] = {
StepID = 800124,
GuideID = 800070,
Comment = "引导点击主玩法/狼人匹配按钮",
WindowName = "UI_Preparations_View",
WidgetName = "UI_CommonBtn_Start",
ButtonName = "w_btn_CommonBtn",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-50, Y = -180}",
UIStyle_Second = 1005,
UIText_Second = "点这里开始匹配~",
UIOffset_Second = "{X=-320, Y = -300}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800125] = {
StepID = 800125,
GuideID = 800071,
Comment = "引导点击大王匹配按钮",
WindowName = "UI_UniversalPreparation_Template1",
WidgetName = "UI_UPComponent_MatchState",
ButtonName = "w_btn_CommonBtn",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-50, Y = -180}",
UIStyle_Second = 1005,
UIText_Second = "点这里开始匹配~",
UIOffset_Second = "{X=-320, Y = -300}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800126] = {
StepID = 800126,
GuideID = 800072,
Comment = "引导点moba匹配按钮",
WindowName = "UI_Arena_Preparations_Main",
WidgetName = "UI_CommonBtn_Start",
ButtonName = "w_btn_CommonBtn",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-50, Y = -180}",
UIStyle_Second = 1005,
UIText_Second = "点这里开始匹配~",
UIOffset_Second = "{X=-320, Y = -300}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
},
[800127] = {
StepID = 800127,
GuideID = 800100,
Comment = "新版hud兜底引导点击快速开始",
StepType = 3,
TypeParams = "{delay = 3}",
WindowName = "UI_WXGame_NewLobbyView",
WidgetName = "w_btn_Start",
ButtonName = "w_btn_Start",
UIStyle_Frist = 1002,
UIOffset_Frist = "{X=0, Y =0}",
IsKeyStep = true,
bTriggerOnlyUIShowOnTop = true
}
}

local mt = {
SubStep = 1,
StepType = 1,
IsForceGuide = false,
bAnyWhereNext = false,
bOtherButtonExit = false,
bMask = false,
bShowSkipButton = false,
IsKeyStep = false,
ButtonMode = "clicked",
bOtherButtonComplete = false,
bTriggerOnlyUIShowOnTop = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
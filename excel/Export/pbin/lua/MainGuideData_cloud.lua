--com.tencent.wea.xlsRes.table_PlayerGuideData => excel/xls/X_新手引导表_云游.xlsx: 新引导总表

local v0 = "{c_grade = {grade = 5}}"

local v1 = "{c_state={state=\"E_STATE_IN_TEAM_OR_TEAM_MATCH\"}}"

local data = {
[900000] = {
GuideID = 900000,
Comment = "通用引导",
Priority = 0,
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = "{ c_grade = {grade = 2}, c_guideId= {guideId = {900001}}, c_conditionMode={mode=\"ANY\"} }",
InterruptConditions = v1
},
[900001] = {
GuideID = 900001,
Comment = "通用切片-谁是狼人",
Priority = 0,
StartConditions = "{c_modeId = {modeId=105}, c_hasNotPlayMatchType={matchType=105}, c_isStartMatchFromCloudEnv={matchType=105}}",
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_TRIGGER_CLOUD_GAME_SLICE_GUIDE",
InterruptConditions = v1
},
[900002] = {
GuideID = 900002,
Comment = "通用切片-星宝农场",
Priority = 0,
StartConditions = "{c_modeId = {modeId=666}, c_hasNotPlayMatchType={matchType=666}, c_isStartMatchFromCloudEnv={matchType=666}}",
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_TRIGGER_CLOUD_GAME_SLICE_GUIDE",
InterruptConditions = v1
},
[900003] = {
GuideID = 900003,
Comment = "农场引导至大厅",
Priority = 1,
StartConditions = "{c_isQQLogin={}}",
TriggerEventType = 1,
TriggerEventParam = "ON_MAINHOUSE_LEVEL_4"
},
[900004] = {
GuideID = 900004,
Comment = "大厅引导至农场",
Priority = 1,
StartConditions = "{c_isQQLogin={}}",
TriggerEventParam = "UILobbyView",
InterruptConditions = "{c_state={state=\"E_STATE_IN_TEAM_OR_TEAM_MATCH\"},c_conditionMode={mode=\"ANY\"}}",
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView"
},
[900005] = {
GuideID = 900005,
Comment = "第一次打开个人信息界面，强引导设置按钮",
Priority = 100,
StartConditions = "{c_cloudGamehasNotChangeDress={}}",
TriggerEventParam = "UI_PlayerInfo"
},
[900006] = {
GuideID = 900006,
Comment = "模式选择-引导点击更多玩法",
Priority = 0,
StartConditions = "{c_grade = {grade = 1},c_isQQLogin}",
TriggerEventParam = "UILobbyView",
CompleteConditions = v0,
InterruptConditions = v1,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView"
},
[900007] = {
GuideID = 900007,
Comment = "新手承接页面对比测试-实验组（仅做判断，永不触发）",
Priority = 999,
StartConditions = "{c_abTest = {testId ={7253726, 7253788, 7253789, 7253790}}}",
TriggerEventType = 1,
TriggerEventParam = "NONE_XXX_NEVER_COMPLETE"
},
[900008] = {
GuideID = 900008,
Comment = "新手承接页面对比测试-对照组（仅做判断，永不触发）",
Priority = 999,
StartConditions = "{c_abTest = {testId ={7253725}}}",
TriggerEventType = 1,
TriggerEventParam = "NONE_XXX_NEVER_COMPLETE"
},
[900009] = {
GuideID = 900009,
Comment = "hud界面，引导玩家进行一场游戏（弱引导1级）",
Priority = 99,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900010] = {
GuideID = 900010,
Comment = "引导点击新手任务入口（2级）",
Priority = 60,
StartConditions = "{c_grade = {grade = 2}}",
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900011] = {
GuideID = 900011,
Comment = "hud界面，引导玩家再开一局（2级）",
Priority = 59,
StartConditions = "{c_grade = {grade = 2}}",
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900012] = {
GuideID = 900012,
Comment = "引导收藏功能（2级）（强引导）",
Priority = 58,
StartConditions = "{c_grade = {grade = 2},c_isWXLogin={}}",
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900013] = {
GuideID = 900013,
Comment = "引导点击更多玩法（2级）",
Priority = 57,
StartConditions = "{c_grade = {grade = 2}}",
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900014] = {
GuideID = 900014,
Comment = "hud界面，引导玩家再开一局（2级）",
Priority = 56,
StartConditions = "{c_grade = {grade = 2}}",
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900015] = {
GuideID = 900015,
Comment = "引导点击每日奖杯挑战，同时引导奖杯征程（3级）（强引导）",
Priority = 0,
StartConditions = "{c_grade = {grade = 3}}",
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = "{c_grade = {grade = 10}}",
InterruptConditions = v1
},
[900016] = {
GuideID = 900016,
Comment = "引导星世界（4级）",
Priority = 54,
StartConditions = "{c_grade = {grade = 4}}",
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = "{c_grade = {grade = 10}}",
InterruptConditions = v1
},
[900017] = {
GuideID = 900017,
Comment = "背包引导（1级以上）-新手-引导商城（弱引导）",
Priority = 60,
StartConditions = "{c_grade = {grade = 1}, c_guideId = {guideId = {900021}}}",
TriggerEventParam = "UI_Bag_MainView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900018] = {
GuideID = 900018,
Comment = "背包引导（5级以上）-老玩家-快速换装",
Priority = 53,
StartConditions = v0,
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = "{c_guideId= {guideId = {900020}}}",
InterruptConditions = v1
},
[900019] = {
GuideID = 900019,
Comment = "停留在hud界面5秒不操作则引导开一局",
Priority = 1,
StartConditions = "{c_isNoTouchOnLobbyView={seconds=5}}",
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = "{c_grade = {grade = 10}}",
InterruptConditions = v1,
CanBeJump = true
},
[900020] = {
GuideID = 900020,
Comment = "背包引导（1级以上）-新手-快速换装（强引导）",
Priority = 61,
StartConditions = "{c_hasNewDressItemType={}}",
TriggerEventParam = "UI_WXGame_LobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900021] = {
GuideID = 900021,
Comment = "背包引导（1级以上）-新手-穿戴保存（强引导）",
Priority = 61,
StartConditions = "{c_hasNewDressItemType={}, c_guideId = {guideId = {900020}}}",
TriggerEventParam = "UI_Bag_MainView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900022] = {
GuideID = 900022,
Comment = "新用户玩法引导-对照组-默认选中天天晋级赛，弱引导开局",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255020}}, c_isWXLogin={}}",
TriggerEventParam = "UI_WXGame_LobbyView",
InterruptConditions = v1
},
[900023] = {
GuideID = 900023,
Comment = "新用户玩法引导-对照组-默认选中moba5v5，弱引导开局",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255021}}, c_isWXLogin={}}",
TriggerEventParam = "UI_WXGame_LobbyView",
InterruptConditions = v1
},
[900024] = {
GuideID = 900024,
Comment = "新用户玩法引导-实验组-默认选中moba5v5，强引导开局",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255022}}, c_isWXLogin={}}",
TriggerEventParam = "UI_WXGame_LobbyView",
InterruptConditions = v1
},
[900025] = {
GuideID = 900025,
Comment = "新用户玩法引导-实验组-默认选中moba5v5，自动开局",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255023}}, c_isWXLogin={}}",
TriggerEventType = 1,
TriggerEventParam = "NONE_XXX_NEVER_COMPLETE",
InterruptConditions = v1
},
[900026] = {
GuideID = 900026,
Comment = "新用户玩法引导-实验组-默认选中天天晋级赛，强引导开局",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255024}}, c_isWXLogin={}}",
TriggerEventParam = "UI_WXGame_LobbyView",
InterruptConditions = v1
},
[900027] = {
GuideID = 900027,
Comment = "新用户玩法引导-实验组-默认选中天天晋级赛，自动开局",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255025}}, c_isWXLogin={}}",
TriggerEventType = 1,
TriggerEventParam = "NONE_XXX_NEVER_COMPLETE",
InterruptConditions = v1
},
[900030] = {
GuideID = 900030,
Comment = "新用户单局实验-直接进入天天晋级赛玩法-单局",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255080}}, c_isWXLogin={}}",
TriggerEventType = 1,
TriggerEventParam = "NONE_XXX_NEVER_COMPLETE",
InterruptConditions = v1
},
[900031] = {
GuideID = 900031,
Comment = "新用户单局实验-直接进入天天晋级赛玩法-两局",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255156}}, c_isWXLogin={}}",
TriggerEventType = 1,
TriggerEventParam = "NONE_XXX_NEVER_COMPLETE",
InterruptConditions = v1
},
[900032] = {
GuideID = 900032,
Comment = "新用户单局实验-直接进入天天晋级赛玩法-新手教学关卡",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255186}}, c_isWXLogin={}}",
TriggerEventType = 1,
TriggerEventParam = "NONE_XXX_NEVER_COMPLETE",
InterruptConditions = v1
},
[900040] = {
GuideID = 900040,
Comment = "新版hud界面，引导玩家点击主推位1",
Priority = 99,
StartConditions = "{c_IsNotStartFromDeeplink={},c_IsNewPlayerForNewHud={}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = "{c_conditionMode = {mode = \"ANY\"},c_guideId= {guideId = {900801}}, c_guideId= {guideId = {900802}}, c_guideId= {guideId = {900803}},c_grade = {grade = 5}}",
InterruptConditions = v1
},
[900041] = {
GuideID = 900041,
Comment = "新版hud界面，dplink引导点击快速开始",
Priority = 99,
StartConditions = "{c_IsStartFromDeeplink={}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900042] = {
GuideID = 900042,
Comment = "引导祈愿入口",
Priority = 98,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900043] = {
GuideID = 900043,
Comment = "引导快速开始游戏",
Priority = 96,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
TriggerFinishEvent = "Newcomer_PushSevenDayCheck",
CompleteConditions = v0,
InterruptConditions = v1
},
[900044] = {
GuideID = 900044,
Comment = "引导新手奖励",
Priority = 95,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900045] = {
GuideID = 900045,
Comment = "引导每日奖杯",
Priority = 94,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900046] = {
GuideID = 900046,
Comment = "引导玩家切换玩法",
Priority = 93,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900059] = {
GuideID = 900059,
Comment = "新用户引导农场",
Priority = 80,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = "{c_guideId= {guideId = {900060}}}",
InterruptConditions = v1
},
[900047] = {
GuideID = 900047,
Comment = "引导玩家查看交友广场",
Priority = 79,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = "{c_guideId= {guideId = {900061}}}",
InterruptConditions = v1
},
[900048] = {
GuideID = 900048,
Comment = "引导玩家查看地图乐园",
Priority = 78,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = "{c_guideId= {guideId = {900062}}}",
InterruptConditions = v1
},
[900049] = {
GuideID = 900049,
Comment = "引导点击头像",
Priority = 77,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = "{c_conditionMode = {mode = \"ANY\"},c_guideId= {guideId = {900005}},c_grade = {grade = 5}}",
InterruptConditions = v1
},
[900051] = {
GuideID = 900051,
Comment = "在广场引导返回主页",
Priority = 1,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UILobbyView",
InterruptConditions = v1
},
[900052] = {
GuideID = 900052,
Comment = "十连抽引导（1级）",
Priority = 70,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_Lottery_MainView",
EndEvent = "ON_CLOSE_UI_UI_Lottery_MainView",
CompleteConditions = v0,
InterruptConditions = "{c_NotCompletedGuideId={guideId = 900042}}"
},
[900054] = {
GuideID = 900054,
Comment = "引导任务目标",
Priority = 97,
StartConditions = "{c_isHasAchievementFromCloudEnv={}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = v0,
InterruptConditions = v1
},
[900060] = {
GuideID = 900060,
Comment = "老用户引导农场",
Priority = 92,
StartConditions = v0,
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = "{c_guideId= {guideId = {900059}}}",
InterruptConditions = v1
},
[900061] = {
GuideID = 900061,
Comment = "老用户引导广场",
Priority = 91,
StartConditions = v0,
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = "{c_guideId= {guideId = {900047}}}",
InterruptConditions = v1
},
[900062] = {
GuideID = 900062,
Comment = "老用户引导星世界",
Priority = 90,
StartConditions = v0,
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = "{c_guideId= {guideId = {900048}}}",
InterruptConditions = v1
},
[900070] = {
GuideID = 900070,
Comment = "引导点击主玩法/狼人匹配按钮",
Priority = 99,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_Preparations_View",
CompleteConditions = v0,
InterruptConditions = v1
},
[900071] = {
GuideID = 900071,
Comment = "引导点击大王匹配按钮",
Priority = 99,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_UniversalPreparation_Template1",
CompleteConditions = v0,
InterruptConditions = v1
},
[900072] = {
GuideID = 900072,
Comment = "引导点moba匹配按钮",
Priority = 99,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_Arena_Preparations_Main",
CompleteConditions = v0,
InterruptConditions = v1
},
[900100] = {
GuideID = 900100,
Comment = "新版hud兜底引导点击快速开始",
Priority = 1,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = "{c_grade = {grade = 10}}",
InterruptConditions = v1
},
[900801] = {
GuideID = 900801,
Comment = "新版hud默认进入新版hud",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255617}}, c_isWXLogin={}}",
TriggerEventParam = "UI_WXGame_NewLobbyView",
CompleteConditions = "{c_conditionMode = {mode = \"ANY\"},c_guideId= {guideId = {900040}}, c_guideId= {guideId = {900802}}, c_guideId= {guideId = {900803}},c_grade = {grade = 5}}",
InterruptConditions = v1
},
[900802] = {
GuideID = 900802,
Comment = "新版hud默认进入广场",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255618}}, c_isWXLogin={}}",
TriggerEventParam = "UILobbyView",
CompleteConditions = "{c_conditionMode = {mode = \"ANY\"},c_guideId= {guideId = {900040}}, c_guideId= {guideId = {900801}}, c_guideId= {guideId = {900803}},c_grade = {grade = 5}}",
InterruptConditions = v1
},
[900803] = {
GuideID = 900803,
Comment = "新版hud默认进入玩法选择页",
Priority = 100,
StartConditions = "{c_abTest = {testId ={7255619}}, c_isWXLogin={}}",
TriggerEventParam = "UI_Model_MainView",
CompleteConditions = "{c_conditionMode = {mode = \"ANY\"},c_guideId= {guideId = {900040}}, c_guideId= {guideId = {900801}}, c_guideId= {guideId = {900802}},c_grade = {grade = 5}}",
InterruptConditions = v1
}
}

local mt = {
TriggerEventType = 2,
FinishEvent = "KeyStepCompleteEvent",
CanBeJump = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
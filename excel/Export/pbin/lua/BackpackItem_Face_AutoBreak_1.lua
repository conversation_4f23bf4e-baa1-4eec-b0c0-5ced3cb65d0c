--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_脸部.xlsx: 脸部

local data = {
[830101] = {
id = 830101,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "认真的",
desc = "双眉微皱，严肃气息自然来",
icon = "CDN:T_Expression_Img_205",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_090_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830102] = {
id = 830102,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "憋出内伤",
desc = "我会憋气直到你理我为止！",
icon = "CDN:T_Expression_Img_200",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_091_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830103] = {
id = 830103,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "阿巴阿巴",
desc = "精神不佳，只会阿巴阿巴",
icon = "CDN:T_Expression_Img_206",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_092_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830104] = {
id = 830104,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "歪嘴战神",
desc = "小嘴一歪，烦恼走开",
icon = "CDN:T_Expression_Img_207",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_093_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830105] = {
id = 830105,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "天生魔种",
desc = "小爷笑一笑，天地吓一跳",
icon = "CDN:T_Expression_Img_208",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_094_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830106] = {
id = 830106,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "灵气外显",
desc = "遇到危险，我会保护你！",
icon = "CDN:T_Expression_Img_209",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_095_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830107] = {
id = 830107,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "咧嘴小太阳",
desc = "张开嘴，笑一笑，没有什么大不了！",
icon = "CDN:T_Expression_Img_210",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_096_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830108] = {
id = 830108,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "成熟的凝视",
desc = "有时候，睿智，是一种感觉",
icon = "CDN:T_Expression_Img_211",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_097_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830109] = {
id = 830109,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "拉弥娅之凝",
desc = "拉弥娅的朋友，都躲在哪里呢？",
icon = "CDN:T_Expression_Img_212",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_098_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830110] = {
id = 830110,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "小猫啵啵",
desc = "啵啵嘟嘟嘟，萌翻全世界！",
icon = "CDN:T_Expression_Img_213",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_099_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830111] = {
id = 830111,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "做饿梦了",
desc = "嘿嘿，饭饭…好香的饭饭…",
icon = "CDN:T_Expression_Img_215",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_102_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830112] = {
id = 830112,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "不嘻嘻",
desc = "拒绝嬉皮笑脸，严肃起来！",
icon = "CDN:T_Expression_Img_216",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_101_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830113] = {
id = 830113,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "满脸惬意",
desc = "一呼一吸间，满是舒适自在",
icon = "CDN:T_Expression_Img_214",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_100_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
},
[830114] = {
id = 830114,
effect = true,
type = "ItemType_Face",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 10
}
},
quality = 3,
name = "银角大王",
desc = "一呼一吸间，满是舒适自在",
icon = "CDN:T_Expression_Img_218",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 10
},
faceEmotionConf = {
textureBaseColor = "T_Body_Face_100_D",
textureNE = "T_Body_Face_001_N_LOD",
textureARM = "T_Body_Face_001_ARM_LOD"
}
}
}

local mt = {
effect = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
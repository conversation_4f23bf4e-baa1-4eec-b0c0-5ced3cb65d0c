--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/R_任务表_闪电赛专用.xlsx: 闪电赛任务

local data = {
[59001] = {
id = 59001,
desc = "累计90奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 90,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
taskGroupId = 9501
},
[59002] = {
id = 59002,
desc = "累计320奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 320,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
reward = {
itemIdList = {
870014
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 870014
},
taskGroupId = 9501
},
[59003] = {
id = 59003,
desc = "累计450奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 450,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
taskGroupId = 9501
},
[59004] = {
id = 59004,
desc = "累计630奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 630,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
taskGroupId = 9501
},
[59005] = {
id = 59005,
desc = "累计750奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 750,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
reward = {
itemIdList = {
840117
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 840117
},
taskGroupId = 9501
},
[59006] = {
id = 59006,
desc = "累计980奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 980,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9501
},
[59007] = {
id = 59007,
desc = "累计1130奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1130,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 9501
},
[59008] = {
id = 59008,
desc = "累计1350奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1350,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9501
},
[59009] = {
id = 59009,
desc = "累计1500奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1500,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
reward = {
itemIdList = {
202001
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 202001
},
taskGroupId = 9501
},
[59010] = {
id = 59010,
desc = "累计1880奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1880,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 9501
},
[59011] = {
id = 59011,
desc = "累计2250奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 2250,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100002
}
}
}
}
}
}
},
reward = {
itemIdList = {
850411
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 850411
},
taskGroupId = 9501
},
[59012] = {
id = 59012,
desc = "累计90奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 90,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
taskGroupId = 9502
},
[59013] = {
id = 59013,
desc = "累计320奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 320,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
reward = {
itemIdList = {
870014
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 870014
},
taskGroupId = 9502
},
[59014] = {
id = 59014,
desc = "累计450奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 450,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
taskGroupId = 9502
},
[59015] = {
id = 59015,
desc = "累计630奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 630,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
taskGroupId = 9502
},
[59016] = {
id = 59016,
desc = "累计750奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 750,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
reward = {
itemIdList = {
840117
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 840117
},
taskGroupId = 9502
},
[59017] = {
id = 59017,
desc = "累计980奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 980,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9502
},
[59018] = {
id = 59018,
desc = "累计1130奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1130,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 9502
},
[59019] = {
id = 59019,
desc = "累计1350奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1350,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9502
},
[59020] = {
id = 59020,
desc = "累计1500奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1500,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
reward = {
itemIdList = {
202002
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 202002
},
taskGroupId = 9502
},
[59021] = {
id = 59021,
desc = "累计1880奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1880,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 9502
},
[59022] = {
id = 59022,
desc = "累计2250奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 2250,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100003
}
}
}
}
}
}
},
reward = {
itemIdList = {
850411
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 850411
},
taskGroupId = 9502
},
[59023] = {
id = 59023,
desc = "累计90奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 90,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
taskGroupId = 9503
},
[59024] = {
id = 59024,
desc = "累计320奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 320,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
reward = {
itemIdList = {
870014
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 870014
},
taskGroupId = 9503
},
[59025] = {
id = 59025,
desc = "累计450奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 450,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
taskGroupId = 9503
},
[59026] = {
id = 59026,
desc = "累计630奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 630,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
taskGroupId = 9503
},
[59027] = {
id = 59027,
desc = "累计750奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 750,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
reward = {
itemIdList = {
840117
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 840117
},
taskGroupId = 9503
},
[59028] = {
id = 59028,
desc = "累计980奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 980,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9503
},
[59029] = {
id = 59029,
desc = "累计1130奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1130,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 9503
},
[59030] = {
id = 59030,
desc = "累计1350奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1350,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9503
},
[59031] = {
id = 59031,
desc = "累计1500奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1500,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
reward = {
itemIdList = {
202003
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 202003
},
taskGroupId = 9503
},
[59032] = {
id = 59032,
desc = "累计1880奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1880,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 9503
},
[59033] = {
id = 59033,
desc = "累计2250奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 2250,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100004
}
}
}
}
}
}
},
reward = {
itemIdList = {
850411
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 850411
},
taskGroupId = 9503
},
[59034] = {
id = 59034,
desc = "累计90奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 90,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
taskGroupId = 9504
},
[59035] = {
id = 59035,
desc = "累计320奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 320,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
reward = {
itemIdList = {
870014
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 870014
},
taskGroupId = 9504
},
[59036] = {
id = 59036,
desc = "累计450奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 450,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
taskGroupId = 9504
},
[59037] = {
id = 59037,
desc = "累计630奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 630,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
taskGroupId = 9504
},
[59038] = {
id = 59038,
desc = "累计750奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 750,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
reward = {
itemIdList = {
840117
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 840117
},
taskGroupId = 9504
},
[59039] = {
id = 59039,
desc = "累计980奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 980,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9504
},
[59040] = {
id = 59040,
desc = "累计1130奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1130,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 9504
},
[59041] = {
id = 59041,
desc = "累计1350奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1350,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9504
},
[59042] = {
id = 59042,
desc = "累计1500奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1500,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
reward = {
itemIdList = {
202004
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 202004
},
taskGroupId = 9504
},
[59043] = {
id = 59043,
desc = "累计1880奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1880,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 9504
},
[59044] = {
id = 59044,
desc = "累计2250奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 2250,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100005
}
}
}
}
}
}
},
reward = {
itemIdList = {
850411
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 850411
},
taskGroupId = 9504
},
[59045] = {
id = 59045,
desc = "累计90奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 90,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
taskGroupId = 9505
},
[59046] = {
id = 59046,
desc = "累计320奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 320,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
reward = {
itemIdList = {
870014
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 870014
},
taskGroupId = 9505
},
[59047] = {
id = 59047,
desc = "累计450奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 450,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
taskGroupId = 9505
},
[59048] = {
id = 59048,
desc = "累计630奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 630,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
taskGroupId = 9505
},
[59049] = {
id = 59049,
desc = "累计750奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 750,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
reward = {
itemIdList = {
840117
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 840117
},
taskGroupId = 9505
},
[59050] = {
id = 59050,
desc = "累计980奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 980,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9505
},
[59051] = {
id = 59051,
desc = "累计1130奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1130,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 9505
},
[59052] = {
id = 59052,
desc = "累计1350奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1350,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9505
},
[59053] = {
id = 59053,
desc = "累计1500奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1500,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
reward = {
itemIdList = {
202005
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 202005
},
taskGroupId = 9505
},
[59054] = {
id = 59054,
desc = "累计1880奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1880,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 9505
},
[59055] = {
id = 59055,
desc = "累计2250奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 2250,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100006
}
}
}
}
}
}
},
reward = {
itemIdList = {
850411
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 850411
},
taskGroupId = 9505
},
[59056] = {
id = 59056,
desc = "累计90奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 90,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
taskGroupId = 9506
},
[59057] = {
id = 59057,
desc = "累计320奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 320,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
870014
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 870014
},
taskGroupId = 9506
},
[59058] = {
id = 59058,
desc = "累计450奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 450,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
taskGroupId = 9506
},
[59059] = {
id = 59059,
desc = "累计630奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 630,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
taskGroupId = 9506
},
[59060] = {
id = 59060,
desc = "累计750奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 750,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
840117
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 840117
},
taskGroupId = 9506
},
[59061] = {
id = 59061,
desc = "累计980奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 980,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9506
},
[59062] = {
id = 59062,
desc = "累计1130奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1130,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 9506
},
[59063] = {
id = 59063,
desc = "累计1350奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1350,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9506
},
[59064] = {
id = 59064,
desc = "累计1500奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1500,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
202006
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 202006
},
taskGroupId = 9506
},
[59065] = {
id = 59065,
desc = "累计1880奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1880,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 9506
},
[59066] = {
id = 59066,
desc = "累计2250奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 2250,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
850411
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 850411
},
taskGroupId = 9506
},
[59067] = {
id = 59067,
desc = "累计90奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 90,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
taskGroupId = 9507
},
[59068] = {
id = 59068,
desc = "累计320奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 320,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
reward = {
itemIdList = {
870014
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 870014
},
taskGroupId = 9507
},
[59069] = {
id = 59069,
desc = "累计450奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 450,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
taskGroupId = 9507
},
[59070] = {
id = 59070,
desc = "累计630奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 630,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
taskGroupId = 9507
},
[59071] = {
id = 59071,
desc = "累计750奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 750,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
reward = {
itemIdList = {
840117
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 840117
},
taskGroupId = 9507
},
[59072] = {
id = 59072,
desc = "累计980奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 980,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9507
},
[59073] = {
id = 59073,
desc = "累计1130奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1130,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 9507
},
[59074] = {
id = 59074,
desc = "累计1350奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1350,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9507
},
[59075] = {
id = 59075,
desc = "累计1500奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1500,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
reward = {
itemIdList = {
202007
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 202007
},
taskGroupId = 9507
},
[59076] = {
id = 59076,
desc = "累计1880奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1880,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 9507
},
[59077] = {
id = 59077,
desc = "累计2250奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 2250,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100008
}
}
}
}
}
}
},
reward = {
itemIdList = {
850411
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 850411
},
taskGroupId = 9507
},
[59078] = {
id = 59078,
desc = "累计90奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 90,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
taskGroupId = 9508
},
[59079] = {
id = 59079,
desc = "累计320奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 320,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
870014
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 870014
},
taskGroupId = 9508
},
[59080] = {
id = 59080,
desc = "累计450奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 450,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
taskGroupId = 9508
},
[59081] = {
id = 59081,
desc = "累计630奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 630,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
taskGroupId = 9508
},
[59082] = {
id = 59082,
desc = "累计750奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 750,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
840117
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 840117
},
taskGroupId = 9508
},
[59083] = {
id = 59083,
desc = "累计980奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 980,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9508
},
[59084] = {
id = 59084,
desc = "累计1130奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1130,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 9508
},
[59085] = {
id = 59085,
desc = "累计1350奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1350,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9508
},
[59086] = {
id = 59086,
desc = "累计1500奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1500,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
202008
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 202008
},
taskGroupId = 9508
},
[59087] = {
id = 59087,
desc = "累计1880奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1880,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 9508
},
[59088] = {
id = 59088,
desc = "累计2250奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 2250,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
850411
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 850411
},
taskGroupId = 9508
},
[59089] = {
id = 59089,
desc = "累计90奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 90,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
taskGroupId = 9509
},
[59090] = {
id = 59090,
desc = "累计320奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 320,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
870014
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 870014
},
taskGroupId = 9509
},
[59091] = {
id = 59091,
desc = "累计450奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 450,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
taskGroupId = 9509
},
[59092] = {
id = 59092,
desc = "累计630奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 630,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
taskGroupId = 9509
},
[59093] = {
id = 59093,
desc = "累计750奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 750,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
840117
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 840117
},
taskGroupId = 9509
},
[59094] = {
id = 59094,
desc = "累计980奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 980,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9509
},
[59095] = {
id = 59095,
desc = "累计1130奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1130,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 9509
},
[59096] = {
id = 59096,
desc = "累计1350奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1350,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1500
},
validPeriodList = {
0
}
},
taskGroupId = 9509
},
[59097] = {
id = 59097,
desc = "累计1500奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1500,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
202008
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 202008
},
taskGroupId = 9509
},
[59098] = {
id = 59098,
desc = "累计1880奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 1880,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 9509
},
[59099] = {
id = 59099,
desc = "累计2250奖章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 2250,
subConditionList = {
{
type = 145,
value = {
10
}
},
{
type = 154,
value = {
100007
}
}
}
}
}
}
},
reward = {
itemIdList = {
850411
},
numList = {
1
},
validPeriodList = {
30
}
},
extraConf = {
rewardItemId = 850411
},
taskGroupId = 9509
}
}

local mt = {
name = "达到指定奖章数",
reward = {
itemIdList = {
4
},
numList = {
800
},
validPeriodList = {
0
}
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_动态图标.xlsx: appIcon

local data = {
[247001] = {
id = 247001,
type = "ItemType_AppIcon",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 1,
name = "吉伊卡哇联动图标",
icon = "CDN:T_Common_Icon_ckw",
showInView = 1,
beginTime = {
seconds = 1750694400
},
endTime = {
seconds = 1755791999
},
isLock = 1,
default = 1,
platformID = 1,
useBeginTime = {
seconds = 1750694400
},
useEndTime = {
seconds = 1755791999
}
},
[247002] = {
id = 247002,
type = "ItemType_AppIcon",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 1,
name = "小红狐图标",
icon = "CDN:T_Common_Icon_xiaohonghu",
showInView = 1,
beginTime = {
seconds = 1749484800
},
endTime = {
seconds = 4082371200
},
isLock = 1,
default = 0,
platformID = 2
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
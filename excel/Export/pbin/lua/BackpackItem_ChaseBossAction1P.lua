--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_ChaseBossAction.xlsx: Sheet1

local data = {
[770000] = {
id = 770000,
type = "ItemType_Action1P",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 35
}
},
quality = 3,
name = "好戏开场",
desc = "德古拉 好戏开场",
icon = "CDN:Icon_Cheer_001",
movementConf = {
action = "AS_MCG_Dracula_Special_Idle_Re_01",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0,
action_3_5 = "AS_MCG_Dracula_Special_Idle_Re_01"
},
actorID = 1013,
actorName = "德古拉",
useInLoca = 0
},
[770001] = {
id = 770001,
type = "ItemType_Action1P",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 35
}
},
quality = 3,
name = "美味追击",
desc = "德古拉 美味追击",
icon = "CDN:Icon_Shive_001",
movementConf = {
action = "AS_MCG_Dracula_Special_Idle_Re_02",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0,
action_3_5 = "AS_MCG_Dracula_Special_Idle_Re_02"
},
actorID = 1013,
actorName = "德古拉",
useInLoca = 0
},
[770002] = {
id = 770002,
type = "ItemType_Action1P",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 35
}
},
quality = 3,
name = "女王登场",
desc = "伊丽莎白 女王登场",
icon = "CDN:Icon_Cheer_001",
movementConf = {
action = "AS_MCG_Liza_Special_Idle_Re_01",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0,
action_3_5 = "AS_MCG_Liza_Special_Idle_Re_01"
},
actorID = 1014,
actorName = "伊丽莎白",
useInLoca = 0
},
[770003] = {
id = 770003,
type = "ItemType_Action1P",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 35
}
},
quality = 3,
name = "月光舞步",
desc = "伊丽莎白 月光舞步",
icon = "CDN:Icon_Shive_001",
movementConf = {
action = "AS_MCG_Liza_Special_Idle_Re_02",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0,
action_3_5 = "AS_MCG_Liza_Special_Idle_Re_02"
},
actorID = 1014,
actorName = "伊丽莎白",
useInLoca = 0
},
[770004] = {
id = 770004,
type = "ItemType_Action1P",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 35
}
},
quality = 3,
name = "为你撑伞",
desc = "拉弥亚 为你撑伞",
icon = "CDN:Icon_Cheer_001",
movementConf = {
action = "AS_MCG_Mia_Special_Idle_01",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0,
action_3_5 = "AS_MCG_Mia_Special_Idle_01"
},
actorID = 1012,
actorName = "拉弥亚",
useInLoca = 0
},
[770005] = {
id = 770005,
type = "ItemType_Action1P",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 35
}
},
quality = 3,
name = "伞舞",
desc = "拉弥亚 伞舞",
icon = "CDN:Icon_Shive_001",
movementConf = {
action = "AS_MCG_Mia_Special_Idle_02",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0,
action_3_5 = "AS_MCG_Mia_Special_Idle_02"
},
actorID = 1012,
actorName = "拉弥亚",
useInLoca = 0
},
[770006] = {
id = 770006,
type = "ItemType_Action1P",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 35
}
},
quality = 3,
name = "美味追击1",
desc = "德古拉 美味追击",
icon = "CDN:Icon_Shive_001",
movementConf = {
action = "AS_MCG_Dracula_Special_Idle_Re_02",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0,
animBlendType = 0,
action_3_5 = "AS_MCG_Dracula_Special_Idle_Re_02"
},
actorID = 1013,
actorName = "德古拉",
useInLoca = 1
},
[770007] = {
id = 770007,
type = "ItemType_Action1P",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 35
}
},
quality = 3,
name = "美味追击2",
desc = "德古拉 美味追击",
icon = "CDN:Icon_Shive_001",
movementConf = {
action = "AS_MCG_Dracula_Special_Idle_Re_02",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0,
animBlendType = 1,
action_3_5 = "AS_MCG_Dracula_Special_Idle_Re_02"
},
actorID = 1013,
actorName = "德古拉",
useInLoca = 1
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
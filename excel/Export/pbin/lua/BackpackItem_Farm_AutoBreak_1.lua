--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_农场.xlsx: 农场道具

local v0 = "ItemType_AutoUse"

local v1 = {
{
itemId = 6,
itemNum = 100
}
}

local v2 = 2

local v3 = 4

local v4 = "星宝农场小屋家具。"

local v5 = 100

local v6 = 225

local v7 = "IBT_Farm"

local data = {
[219300] = {
id = 219300,
exceedReplaceItem = v1,
quality = 2,
name = "收获日",
icon = "CDN:Icon_Farm_Suit_001_Comm",
resourceConf = {
model = "SK_Farm_Suit_001_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-8,
-70
}
},
[219301] = {
id = 219301,
exceedReplaceItem = v1,
quality = 2,
name = "星夜魔法",
icon = "CDN:Icon_Farm_Suit_002_Comm",
resourceConf = {
model = "SK_Farm_Suit_002_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-70
},
shareScaleTimes = 70,
shareRotateYaw = 210
},
[219302] = {
id = 219302,
exceedReplaceItem = v1,
quality = 2,
name = "幽灵巫师",
icon = "CDN:Icon_Farm_Suit_003_Comm",
resourceConf = {
model = "SK_Farm_Suit_003_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-15,
-65
},
shareScaleTimes = 80
},
[219400] = {
id = 219400,
exceedReplaceItem = v1,
quality = 4,
name = "磨牙棒",
icon = "CDN:Icon_Farm_Head_001_Comm",
resourceConf = {
model = "SK_Farm_Head_004_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219401] = {
id = 219401,
exceedReplaceItem = v1,
name = "海盐夏日",
icon = "CDN:Icon_Farm_Head_002_Comm",
resourceConf = {
model = "SK_Farm_Head_002_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219402] = {
id = 219402,
exceedReplaceItem = v1,
name = "小肥啾",
icon = "CDN:Icon_Farm_Head_003_Comm",
resourceConf = {
model = "SK_Farm_Head_003_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219403] = {
id = 219403,
exceedReplaceItem = v1,
name = "桃之夭夭",
icon = "CDN:Icon_Farm_Head_004_Comm",
resourceConf = {
model = "SK_Farm_Head_004_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219404] = {
id = 219404,
exceedReplaceItem = v1,
quality = 4,
name = "活力小苗",
icon = "CDN:Icon_Farm_Head_005_Comm",
resourceConf = {
model = "SK_Farm_Head_008_KEJI_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219405] = {
id = 219405,
exceedReplaceItem = v1,
quality = 4,
name = "大橘为重",
icon = "CDN:Icon_Farm_Head_006_Comm",
resourceConf = {
model = "SK_Farm_Head_009_KEJI_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219406] = {
id = 219406,
exceedReplaceItem = v1,
name = "名流刘海",
icon = "CDN:Icon_Farm_Head_007_Comm",
resourceConf = {
model = "SK_Farm_Head_010_KEJI_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219407] = {
id = 219407,
exceedReplaceItem = v1,
quality = 4,
name = "降温毛巾",
icon = "CDN:Icon_Farm_Head_008_Comm",
resourceConf = {
model = "SK_Farm_Head_011_KEJI_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219408] = {
id = 219408,
exceedReplaceItem = v1,
name = "派对狂欢",
icon = "CDN:Icon_Farm_Head_009_Comm",
resourceConf = {
model = "SK_Farm_Head_012_KEJI_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219409] = {
id = 219409,
exceedReplaceItem = v1,
name = "友谊魔法",
icon = "CDN:Icon_Farm_Head_010_Comm",
resourceConf = {
model = "SK_Farm_Head_010_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219410] = {
id = 219410,
exceedReplaceItem = v1,
name = "童年回忆",
icon = "CDN:Icon_Farm_Head_011_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219411] = {
id = 219411,
exceedReplaceItem = v1,
name = "文雅眼镜",
icon = "CDN:Icon_Farm_Head_012_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219600] = {
id = 219600,
exceedReplaceItem = v1,
quality = 4,
name = "项圈",
icon = "CDN:Icon_Farm_Neck_000_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219601] = {
id = 219601,
exceedReplaceItem = v1,
quality = 4,
name = "波点领结",
icon = "CDN:Icon_Farm_Neck_001_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219602] = {
id = 219602,
exceedReplaceItem = v1,
name = "铃铛项圈",
icon = "CDN:Icon_Farm_Neck_002_Comm",
resourceConf = {
model = "SK_Farm_Neck_002_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219603] = {
id = 219603,
exceedReplaceItem = v1,
name = "职场精英",
icon = "CDN:Icon_Farm_Neck_003_Comm",
resourceConf = {
model = "SK_Farm_Neck_003_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219604] = {
id = 219604,
exceedReplaceItem = v1,
quality = 4,
name = "卷草纹包袱",
icon = "CDN:Icon_Farm_Neck_004_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219605] = {
id = 219605,
exceedReplaceItem = v1,
name = "爱心宝宝巾",
icon = "CDN:Icon_Farm_Neck_005_Comm",
resourceConf = {
model = "SK_Farm_Neck_005_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219606] = {
id = 219606,
exceedReplaceItem = v1,
quality = 4,
name = "条纹围巾",
icon = "CDN:Icon_Farm_Neck_006_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219607] = {
id = 219607,
exceedReplaceItem = v1,
quality = 4,
name = "朋克项圈",
icon = "CDN:Icon_Farm_Neck_007_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219608] = {
id = 219608,
exceedReplaceItem = v1,
name = "优雅态度",
icon = "CDN:Icon_Farm_Neck_008_Comm",
resourceConf = {
model = "SK_Farm_Neck_008_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219609] = {
id = 219609,
exceedReplaceItem = v1,
name = "星火领巾",
icon = "CDN:Icon_Farm_Neck_009_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219610] = {
id = 219610,
exceedReplaceItem = v1,
name = "潮玩耳机",
icon = "CDN:Icon_Farm_Neck_010_Comm",
resourceConf = {
model = "SK_Farm_Neck_010_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219800] = {
id = 219800,
exceedReplaceItem = v1,
quality = 4,
name = "运动背扣",
icon = "CDN:Icon_Farm_Body_001_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219801] = {
id = 219801,
exceedReplaceItem = v1,
name = "遇上彩虹",
icon = "CDN:Icon_Farm_Body_002_Comm",
resourceConf = {
model = "SK_Farm_Body_002_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219802] = {
id = 219802,
exceedReplaceItem = v1,
quality = 4,
name = "骨头小包",
icon = "CDN:Icon_Farm_Body_003_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219803] = {
id = 219803,
exceedReplaceItem = v1,
name = "咔哒咔哒",
icon = "CDN:Icon_Farm_Body_004_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219804] = {
id = 219804,
exceedReplaceItem = v1,
name = "牛仔很忙",
icon = "CDN:Icon_Farm_Body_005_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219805] = {
id = 219805,
exceedReplaceItem = v1,
name = "挚爱赠礼",
icon = "CDN:Icon_Farm_Body_006_Comm",
resourceConf = {
model = "SK_Farm_Body_006_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219806] = {
id = 219806,
exceedReplaceItem = v1,
quality = 4,
name = "星星短裤",
icon = "CDN:Icon_Farm_Body_007_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219807] = {
id = 219807,
exceedReplaceItem = v1,
quality = 4,
name = "镶边毛巾",
icon = "CDN:Icon_Farm_Body_008_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[219808] = {
id = 219808,
exceedReplaceItem = v1,
name = "今日启航",
icon = "CDN:Icon_Farm_Body_009_Comm",
resourceConf = {
model = "SM_Farm_BuComm_Home_003_Comm",
modelType = 1
},
scaleTimes = 100,
bHideInBag = true,
rotateYaw = 225
},
[218802] = {
id = 218802,
type = v0,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "星星树",
desc = v4,
icon = "CDN:ICON_SHOP_Farm_De_S9_CartoonTree_01_1",
useParam = {
10292
},
bHideInBag = true,
bagType = v7
},
[218803] = {
id = 218803,
type = v0,
quality = 4,
name = "人鱼吊坠",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_SCPPendant_01_1",
useParam = {
10271
},
bHideInBag = true,
bagType = v7
},
[218804] = {
id = 218804,
type = v0,
name = "雨丝阳伞",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_SCPUmbrella_01_1",
useParam = {
10272
},
bHideInBag = true,
bagType = v7
},
[218805] = {
id = 218805,
type = v0,
quality = 2,
name = "金斧头",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_SCPGoldaxe_01_1",
useParam = {
10273
},
bHideInBag = true,
bagType = v7
},
[218806] = {
id = 218806,
type = v0,
quality = 2,
name = "雷击面具",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_SCPMask_01_1",
useParam = {
10274
},
bHideInBag = true,
bagType = v7
},
[218807] = {
id = 218807,
type = v0,
quality = 1,
name = "水晶鞋",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_SCPGlassshoes_01_1",
useParam = {
10275
},
bHideInBag = true,
bagType = v7
},
[218808] = {
id = 218808,
type = v0,
quality = 1,
name = "雷水晶",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_SCPCrystal_01_1",
useParam = {
10276
},
bHideInBag = true,
bagType = v7
},
[218809] = {
id = 218809,
type = v0,
quality = 1,
name = "黄金便便",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_GoldenShit_01_1",
useParam = {
10277
},
bHideInBag = true,
bagType = v7
},
[218810] = {
id = 218810,
type = v0,
quality = 4,
name = "妙味松花蛋",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_PiDan_01_1",
useParam = {
10278
},
bHideInBag = true,
bagType = v7
},
[218811] = {
id = 218811,
type = v0,
quality = 4,
name = "止痛药丸",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_Pills_01_1",
useParam = {
10279
},
bHideInBag = true,
bagType = v7
},
[218812] = {
id = 218812,
type = v0,
quality = 4,
name = "帝王蝶",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_Specimen_01_1",
useParam = {
10280
},
bHideInBag = true,
bagType = v7
},
[218813] = {
id = 218813,
type = v0,
name = "一箱私房钱",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_CashBox_01_1",
useParam = {
10281
},
bHideInBag = true,
bagType = v7
},
[218814] = {
id = 218814,
type = v0,
name = "黄金蚯蚓",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_GoldenWorm_01_1",
useParam = {
10282
},
bHideInBag = true,
bagType = v7
},
[218815] = {
id = 218815,
type = v0,
quality = 2,
name = "电子芯片",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_MicroChip_01_1",
useParam = {
10283
},
bHideInBag = true,
bagType = v7
},
[218816] = {
id = 218816,
type = v0,
quality = 4,
name = "蒙面头巾",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_Visor_01_1",
useParam = {
10284
},
bHideInBag = true,
bagType = v7
},
[218817] = {
id = 218817,
type = v0,
name = "七彩毛团",
desc = v4,
icon = "ICON_BP_Farm_Pr_S8_WoolenBall_01_1",
useParam = {
10285
},
bHideInBag = true,
bagType = v7
},
[218818] = {
id = 218818,
type = v0,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "神奇海螺壁炉",
desc = v4,
icon = "CDN:ICON_SHOP_Farm_De_S9_HYHome_01_1",
useParam = {
10357
},
bHideInBag = true,
bagType = v7
},
[218819] = {
id = 218819,
type = v0,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "清新花朵冰箱",
desc = v4,
icon = "CDN:ICON_SHOP_Farm_De_S9_GirlFreezer_02_1",
useParam = {
10366
},
bHideInBag = true,
bagType = v7
},
[219412] = {
id = 219412,
exceedReplaceItem = v1,
name = "生日蛋糕",
icon = "CDN:Icon_Farm_Head_013_Comm",
resourceConf = {
model = "SK_Farm_Head_013_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219413] = {
id = 219413,
exceedReplaceItem = v1,
name = "鹿角发箍",
icon = "CDN:Icon_Farm_Head_014_Comm",
resourceConf = {
model = "SK_Farm_Head_014_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219611] = {
id = 219611,
exceedReplaceItem = v1,
name = "花环围脖",
icon = "CDN:Icon_Farm_Neck_014_Comm",
resourceConf = {
model = "SK_Farm_Neck_014_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219809] = {
id = 219809,
exceedReplaceItem = v1,
name = "礼物口袋",
icon = "CDN:Icon_Farm_Body_014_Comm",
resourceConf = {
model = "SK_Farm_Body_014_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[218148] = {
id = 218148,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.37.67",
name = "树精宝宝",
desc = "（星宝农场稻草人装饰）可以提高动物大丰收产量。",
icon = "CDN:Icon_Farm_Suit_014_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CLMessageBoard_001_Comm",
modelType = 1
},
scaleTimes = 45,
bHideInBag = true,
rotateYaw = 225,
buff = "动物大丰收产量倍率",
buffValue = "+0.5",
buffViewOffset = "0,0"
},
[218149] = {
id = 218149,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.37.67",
name = "绿野时钟",
desc = "（星宝农场时钟装饰）可以提升每日被祈福次数。",
icon = "CDN:Icon_Farm_Head_015_Comm",
resourceConf = {
model = "SK_Farm_FU_CLClock_001_Comm",
modelType = 1
},
scaleTimes = 30,
bHideInBag = true,
rotateYaw = 300,
buff = "每日被祈福次数",
buffValue = "+1",
buffViewOffset = "-50,0"
},
[218150] = {
id = 218150,
exceedReplaceItem = v1,
lowVer = "1.3.37.67",
quality = 2,
name = "胖胖菇时钟",
desc = "（星宝农场时钟装饰）可以提高作物大丰收产量。",
icon = "CDN:Icon_Farm_Neck_015_Comm",
resourceConf = {
model = "SK_Farm_FU_CLClock_002_Comm",
modelType = 1
},
scaleTimes = 30,
shareTexts = {
"菇菇守护，萌趣时光"
},
bHideInBag = true,
rotateYaw = 290,
shareOffset = {
-10,
-70
},
buff = "作物大丰收产量倍率",
buffValue = "+1",
buffViewOffset = "-50,0",
shareScaleTimes = 24,
shareRotateYaw = 280
},
[218151] = {
id = 218151,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.68.26",
quality = 1,
name = "琳琅摘星阁",
desc = "（星宝农场小屋装饰）提升收获动物时获得的农场经验。",
icon = "CDN:Icon_Farm_Body_015_Comm",
resourceConf = {
model = "SK_Farm_OG_008",
modelType = 2,
idleAnim = "SK_Farm_OG_008_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_008_PV/Farm_OG_008_Intact",
scaleTimes = 8,
soundId = {
40659
},
outEnterSequence = "Farm_PV_OG_008",
shareTexts = {
"满目琳琅，抬手摘星"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218151.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218151.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218151.astc",
buff = "收获动物获得农场经验",
buffValue = "+10%",
buffViewOffset = "75,400"
},
[218152] = {
id = 218152,
exceedReplaceItem = v1,
lowVer = "1.3.68.1",
quality = 2,
name = "金玉醒狮果行",
desc = "（星宝农场蔬菜摊装饰）可以提升蔬菜摊售价。",
icon = "CDN:Icon_Farm_Suit_007_Comm",
resourceConf = {
model = "SM_Farm_BuComm_XCVegetableStall_001_Comm",
modelType = 1
},
scaleTimes = 20,
shareTexts = {
"蔬果舞狮，喜迎八方"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-15,
-65
},
buff = "蔬菜摊售价提升",
buffValue = "+10%",
buffViewOffset = "0,0",
shareScaleTimes = 13,
shareRotateYaw = 210
},
[218153] = {
id = 218153,
exceedReplaceItem = v1,
lowVer = "1.3.68.1",
quality = 2,
name = "金闪闪小铺",
desc = "（星宝农场动物小铺装饰）可以提升动物小铺售价。",
icon = "CDN:Icon_Farm_Head_017",
resourceConf = {
model = "SM_Farm_BuComm_XCXumu_001_Comm",
modelType = 1
},
scaleTimes = 20,
shareTexts = {
"元宝闪闪，幸福满满"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-15,
-65
},
buff = "动物小铺售价提升",
buffValue = "+10%",
buffViewOffset = "0,0",
shareScaleTimes = 13,
shareRotateYaw = 210
},
[218154] = {
id = 218154,
exceedReplaceItem = v1,
lowVer = "1.3.68.1",
quality = 2,
name = "鲤跃龙门鱼铺",
desc = "（星宝农场水产摊装饰）可以提升水产摊售价。",
icon = "CDN:Icon_Farm_Neck_011_Comm",
resourceConf = {
model = "SM_Farm_BuComm_XCFishStall_001_Comm",
modelType = 1
},
scaleTimes = 20,
shareTexts = {
"锦鲤跃，鲜味来"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-15,
-65
},
buff = "水产摊售价提升",
buffValue = "+10%",
buffViewOffset = "150,150",
shareScaleTimes = 12,
shareRotateYaw = 210
},
[218155] = {
id = 218155,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.68.26",
quality = 1,
name = "嘶嘶灵宝",
desc = "（星宝农场稻草人装饰）可以提高动物丰收产量。",
icon = "CDN:Icon_Farm_Body_011",
resourceConf = {
model = "SK_Farm_OG_009",
modelType = 2,
idleAnim = "SK_Farm_OG_009_Idle"
},
outEnter = "/Game/FeatureBaseAssets/Farm/Building/Farm_OG_009_PV/Farm_OG_009_Intact",
scaleTimes = 45,
soundId = {
40661
},
outEnterSequence = "Farm_PV_OG_009",
shareTexts = {
"灵宝来，好运到"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218155.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218155.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218155.astc",
buff = "动物丰收产量倍率",
buffValue = "+1",
buffViewOffset = "0,0"
},
[218156] = {
id = 218156,
exceedReplaceItem = v1,
lowVer = "1.3.68.26",
quality = 2,
name = "仙福满满时钟",
desc = "（星宝农场时钟装饰）可以缩短加工时间。",
icon = "CDN:Icon_Farm_Poodle_001_Comm",
resourceConf = {
model = "SK_Farm_FU_XCClock_001_Comm",
modelType = 1
},
scaleTimes = 30,
shareTexts = {
"岁月如歌，福运常伴"
},
bHideInBag = true,
rotateYaw = 280,
shareOffset = {
-15,
-70
},
buff = "加工时间缩短",
buffValue = "-3%",
buffViewOffset = "0,0",
shareScaleTimes = 25,
shareRotateYaw = 270
},
[218157] = {
id = 218157,
exceedReplaceItem = v1,
lowVer = "1.3.68.26",
quality = 2,
name = "宝莲灯礼盒",
desc = "（星宝农场装饰）获得后可以改变送礼时礼物盒的样式。",
icon = "CDN:Icon_Farm_Suit_016_Comm",
resourceConf = {
model = "SM_Farm_Tool_XCGift_001_Comm",
modelType = 2,
idleAnim = "SM_Farm_Tool_XCGift_001_Idle_Comm"
},
scaleTimes = 66,
shareTexts = {
"花开福运，心意翩然"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-5,
-35
},
shareScaleTimes = 45,
shareRotateYaw = 210
},
[218158] = {
id = 218158,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.68.26",
name = "金宝萌宠屋",
desc = "（星宝农场宠物屋装饰）可以提高所有水层金冠鱼的出现概率。",
icon = "CDN:Icon_Farm_Suit_019_Comm",
resourceConf = {
model = "SM_Farm_BuComm_XCDoghouse_001_Comm",
modelType = 1
},
scaleTimes = 38,
bHideInBag = true,
rotateYaw = 120,
buff = "金冠鱼出现率提升",
buffValue = "+10%",
buffViewOffset = "0,0"
},
[218159] = {
id = 218159,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
lowVer = "1.3.68.1",
quality = 1,
name = "庆丰年宅院",
desc = "（星宝农场院落装饰）可以提升鱼卡&钓鱼获取的熟练度20%。",
icon = "CDN:Icon_Farm_Suit_023_Comm",
picture = "T_Farmyard_BigIcon_Yard_2",
bHideInBag = true,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218159.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218159.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218159.astc",
buff = "钓鱼获取熟练度提升",
buffValue = "+20%",
buffViewOffset = "0,0"
},
[218820] = {
id = 218820,
type = v0,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "水晶糖果树",
desc = v4,
icon = "CDN:Icon_Farm_HeadSuit_006_Comm",
useParam = {
10394
},
bHideInBag = true,
bagType = v7
},
[219303] = {
id = 219303,
exceedReplaceItem = v1,
quality = 2,
name = "锦绣狮宝",
icon = "CDN:Icon_Farm_Suit_014_Comm",
resourceConf = {
model = "SK_Farm_Suit_014_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-70
},
shareScaleTimes = 80,
shareRotateYaw = 210
},
[219414] = {
id = 219414,
exceedReplaceItem = v1,
name = "发财帽",
icon = "CDN:Icon_Farm_Head_015_Comm",
resourceConf = {
model = "SK_Farm_Head_015_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219612] = {
id = 219612,
exceedReplaceItem = v1,
name = "红围巾",
icon = "CDN:Icon_Farm_Neck_015_Comm",
resourceConf = {
model = "SK_Farm_Neck_015_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[219810] = {
id = 219810,
exceedReplaceItem = v1,
name = "纳福袋",
icon = "CDN:Icon_Farm_Body_015_Comm",
resourceConf = {
model = "SK_Farm_Body_015_Comm",
modelType = 2,
idleAnim = "SK_Farm_Keji_001_Idle_Comm"
},
scaleTimes = 105,
bHideInBag = true,
rotateYaw = 225
},
[218821] = {
id = 218821,
type = v0,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "金桔盆栽",
desc = v4,
icon = "CDN:Icon_Farm_NeckSuit_012_Comm",
useParam = {
10529
},
bHideInBag = true,
bagType = v7
},
[218822] = {
id = 218822,
type = v0,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "红梅盆栽",
desc = v4,
icon = "CDN:Icon_Farm_BodySuit_006_Comm",
useParam = {
10530
},
bHideInBag = true,
bagType = v7
},
[218823] = {
id = 218823,
type = v0,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "富贵屏风",
desc = v4,
icon = "CDN:Icon_Farm_Body_010_Comm",
useParam = {
10531
},
bHideInBag = true,
bagType = v7
},
[218160] = {
id = 218160,
exceedReplaceItem = v1,
lowVer = "1.3.68.76",
quality = 2,
name = "梦幻萌宠屋",
desc = "（星宝农场宠物屋装饰）可以提升水族箱获取农场币的效率。",
icon = "CDN:Icon_Farm_BodySuit_009_Comm",
resourceConf = {
model = "SM_Farm_Doghouse_002_A_1_Comm",
modelType = 1
},
scaleTimes = 33,
shareTexts = {
"梦幻小窝，宠爱有加"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-60
},
buff = "水族箱获取农场币效率提升",
buffValue = "+5%",
buffViewOffset = "-50,0",
shareScaleTimes = 25,
shareRotateYaw = 210
},
[218161] = {
id = 218161,
exceedReplaceItem = v1,
lowVer = "1.3.68.99",
quality = 2,
name = "烟雨小筑",
desc = "（星宝农场小屋装饰）提升收获加工物时获得的农场经验。",
icon = "CDN:Icon_Farm_BodySuit_012_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CNHome_001_Comm",
modelType = 1
},
scaleTimes = 6,
shareTexts = {
"烟雨江南，画里人家"
},
bHideInBag = true,
rotateYaw = 225,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_218161.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Name_218161.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Prop_Frame_218161.astc",
buff = "收获加工物获得农场经验",
buffValue = "+10%",
buffViewOffset = "0,0"
},
[218162] = {
id = 218162,
exceedReplaceItem = v1,
lowVer = "1.3.68.99",
quality = 2,
name = "江南果行",
desc = "（星宝农场蔬菜摊装饰）可以提升收获作物时获得的作物熟练度。",
icon = "CDN:Icon_Farm_Suit_009_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CNVegetableStall_001_Comm",
modelType = 1
},
scaleTimes = 8,
shareTexts = {
"粉墙黛瓦，果蔬飘香"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-80
},
buff = "作物熟练度获取提升",
buffValue = "+10%",
buffViewOffset = "0,0",
shareScaleTimes = 6,
shareRotateYaw = 210
},
[218163] = {
id = 218163,
exceedReplaceItem = v1,
lowVer = "1.3.68.99",
quality = 2,
name = "芙蓉商行",
desc = "（星宝农场动物小铺装饰）可以提升收获动物时获得的动物熟练度。",
icon = "CDN:Icon_Farm_Golden_001_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CNXumu_004_Comm",
modelType = 1
},
scaleTimes = 8,
shareTexts = {
"春意暖暖，营养满满"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-80
},
buff = "动物熟练度获取提升",
buffValue = "+10%",
buffViewOffset = "0,0",
shareScaleTimes = 6,
shareRotateYaw = 210
},
[218164] = {
id = 218164,
exceedReplaceItem = v1,
lowVer = "1.3.68.99",
quality = 2,
name = "观鱼小铺",
desc = "（星宝农场水产摊装饰）可以降低鱼饵价格。",
icon = "Icon_Farm_Neck_016",
resourceConf = {
model = "SM_Farm_BuComm_CNFishStall_001_Comm",
modelType = 1
},
scaleTimes = 9,
shareTexts = {
"亭畔观鱼，尽享新鲜"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-80
},
buff = "鱼饵价格降低",
buffValue = "-2%",
buffViewOffset = "0,0",
shareScaleTimes = 7,
shareRotateYaw = 210
},
[218166] = {
id = 218166,
exceedReplaceItem = v1,
lowVer = "1.3.68.99",
quality = 2,
name = "悠悠亭",
desc = "（星宝农场宠物屋装饰）可以提升加工动物产品的售价。",
icon = "CDN:Icon_Farm_Suit_010_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CNDogHouse_001_A_1_Comm",
modelType = 1
},
scaleTimes = 33,
shareTexts = {
"无忧愁，乐悠悠"
},
bHideInBag = true,
rotateYaw = 225,
shareOffset = {
-10,
-70
},
buff = "加工动物产品售价提升",
buffValue = "+30%",
buffViewOffset = "0,0",
shareScaleTimes = 22,
shareRotateYaw = 210
},
[218167] = {
id = 218167,
exceedReplaceItem = v1,
lowVer = "1.3.68.99",
quality = 2,
name = "日晷",
desc = "（星宝农场时钟装饰）可以使钓鱼成熟时间缩短。",
icon = "CDN:Icon_Farm_Suit_020_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CNClock_001_Comm",
modelType = 1
},
scaleTimes = 35,
shareTexts = {
"阳光画圈，时光甜甜"
},
bHideInBag = true,
rotateYaw = 200,
shareOffset = {
-10,
-70
},
buff = "钓鱼成熟时间缩短",
buffValue = "-2%",
buffViewOffset = "0,0",
shareScaleTimes = 25,
shareRotateYaw = 190
},
[218824] = {
id = 218824,
type = v0,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "树桩壁炉",
desc = v4,
icon = "CDN:Icon_Farm_HeadSuit_005_Comm",
useParam = {
10452
},
bHideInBag = true,
bagType = v7
},
[218825] = {
id = 218825,
type = v0,
stackedNum = 999999999,
maxNum = 999999999,
quality = 1,
name = "莲池假山",
desc = v4,
icon = "CDN:Icon_Farm_Head_016_Comm",
useParam = {
10534
},
bHideInBag = true,
bagType = v7
},
[218541] = {
id = 218541,
name = "小猎龙尾部化石",
desc = "（星宝农场藏品）",
icon = "CDN:Icon_Farm_NeckSuit_005_Comm",
bHideInBag = true
},
[218542] = {
id = 218542,
quality = 2,
name = "小猎龙头部化石",
desc = "（星宝农场藏品）",
icon = "CDN:Icon_Farm_BodySuit_005_Comm",
bHideInBag = true
},
[218543] = {
id = 218543,
quality = 4,
name = "熊头挂饰",
desc = "（星宝农场藏品）",
icon = "CDN:Icon_Farm_Neck_012_Comm",
bHideInBag = true
},
[218544] = {
id = 218544,
name = "石像猫",
desc = "（星宝农场藏品）",
icon = "CDN:Icon_Farm_Poodle_002_Comm",
bHideInBag = true
},
[218545] = {
id = 218545,
quality = 2,
name = "像素盾",
desc = "（星宝农场藏品）",
bHideInBag = true
},
[218546] = {
id = 218546,
quality = 4,
name = "漂流瓶",
desc = "（星宝农场藏品）",
bHideInBag = true
},
[218547] = {
id = 218547,
name = "钓鱼佬的渔轮",
desc = "（星宝农场藏品）",
bHideInBag = true
},
[218548] = {
id = 218548,
quality = 1,
name = "海洋之心",
desc = "（星宝农场藏品）",
bHideInBag = true
},
[218549] = {
id = 218549,
quality = 4,
name = "许愿雕塑",
desc = "（星宝农场藏品）",
bHideInBag = true
},
[218550] = {
id = 218550,
quality = 4,
name = "天使面罩",
desc = "（星宝农场藏品）",
bHideInBag = true
},
[218551] = {
id = 218551,
name = "监听耳机",
desc = "（星宝农场藏品）",
bHideInBag = true
},
[218552] = {
id = 218552,
quality = 2,
name = "神秘陨石",
desc = "（星宝农场藏品）",
bHideInBag = true
},
[218143] = {
id = 218143,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.88.54",
name = "风悠悠树屋",
desc = "（星宝农场小屋装饰）可以提高动物大丰收产量和水族箱获取农场币的效率。",
icon = "CDN:Icon_Farm_BuComm_CLHome_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CLHome_001_Comm",
modelType = 2,
idleAnim = "SM_Farm_BuComm_CLHome_001_Idle_Comm"
},
scaleTimes = 12,
bHideInBag = true,
rotateYaw = 225,
buff = "动物大丰收产量倍率",
buffValue = "+0.5",
buffViewOffset = "0,0",
buffSecondLevel = -2,
buffSecond = "水族箱获取农场币效率提升",
buffSecondValue = "+2%"
},
[218144] = {
id = 218144,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
lowVer = "1.3.68.114",
name = "萝尖尖果行",
desc = "（星宝农场蔬菜摊装饰）可以提高作物大丰收产量。",
icon = "CDN:Icon_Farm_BuComm_CLVegetableStall_001_A_Comm",
resourceConf = {
model = "SM_Farm_BuComm_CLVegetableStall_001_Comm",
modelType = 1
},
scaleTimes = 20,
bHideInBag = true,
rotateYaw = 225,
buff = "作物大丰收产量倍率",
buffValue = "+0.5",
buffViewOffset = "0,0"
}
}

local mt = {
type = "ItemType_FarmActive",
stackedNum = 1,
maxNum = 1,
quality = 3,
desc = "（星宝农场宠物装饰）可以装扮你的宠物。",
useType = "IUTO_SendToFarm",
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
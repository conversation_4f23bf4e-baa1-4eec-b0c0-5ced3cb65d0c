com.tencent.wea.xlsRes.table_RankingSnapshotRewardConfData
excel/xls/P_排位段位&保分配置_lightning_闪电赛.xlsx sheet:榜单结算奖励
rows {
  id: 2101
  rewardRange {
    priority: 1
    fromIndex: 1
    toIndex: 1
    mailId: 129
    fromStr: "1"
    toStr: "1"
    rewardText: "第1名"
    rewardItems {
      itemId: 850412
      itemNum: 1
      expireDays: 30
    }
    mailParams: "第#名"
  }
  rewardRange {
    priority: 2
    fromIndex: 2
    toIndex: 200
    mailId: 129
    fromStr: "2"
    toStr: "200"
    rewardText: "第2~200名"
    rewardItems {
      itemId: 850413
      itemNum: 1
      expireDays: 30
    }
    mailParams: "第#名"
  }
  rewardRange {
    priority: 3
    fromIndex: 201
    toPercent: 10
    mailId: 129
    describeText: "全服前\n10%"
    fromStr: "201"
    toStr: "10%"
    rewardText: "全服前10%"
    rewardItems {
      itemId: 850414
      itemNum: 1
      expireDays: 30
    }
    mailParams: "全服前10%"
  }
  rewardRange {
    priority: 4
    fromPercent: 10
    toPercent: 60
    mailId: 129
    describeText: "全服前\n60%"
    fromStr: "10%"
    toStr: "60%"
    rewardText: "全服前60%"
    rewardItems {
      itemId: 850415
      itemNum: 1
      expireDays: 30
    }
    mailParams: "全服前60%"
  }
  rewardRange {
    priority: 5
    fromPercent: 60
    mailId: 129
    describeText: "全服\n60%以后"
    fromStr: "60%"
    rewardText: "全服60%以后"
    rewardItems {
      itemId: 850416
      itemNum: 1
      expireDays: 30
    }
    mailParams: "全服60%以后"
  }
  maxSize: 3000000
  expireDays: 31
  rankId: 210
  fromSeason: 100002
  toSeason: 100010
  minSize: 200000
}

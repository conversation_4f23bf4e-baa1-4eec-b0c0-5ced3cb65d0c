com.tencent.wea.xlsRes.table_PlaceableActorConfigData
excel/xls/LetsGoPlaceable/C_场景放置物_逻辑.xlsx sheet:Sheet1
rows {
  typeId: 1105000000000000
  className: "BP_PA_LP_AdjustCamera"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP/BP_PA_LP_AdjustCamera.BP_PA_LP_AdjustCamera_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000001
  className: "BP_PA_LP_Checkpoint"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP/BP_PA_LP_Checkpoint.BP_PA_LP_Checkpoint_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000002
  className: "BP_PA_LP_CheckPoint_UGC"
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000003
  className: "BP_PA_LP_Destination"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP/BP_PA_LP_Destination.BP_PA_LP_Destination_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000004
  className: "BP_PA_LP_Rebirth"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP/BP_PA_LP_Rebirth.BP_PA_LP_Rebirth_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000005
  className: "BP_PA_LP_Rebirth_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP/BP_PA_LP_Rebirth_2.BP_PA_LP_Rebirth_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000006
  className: "BP_PA_LP_Rebirth_Lobby"
  classPath: "/Game/LetsGo/Assets/CommunityAssets/Placeables/LogicPAs/BP/BP_PA_LP_Rebirth_Lobby.BP_PA_LP_Rebirth_Lobby_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000174
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000007
  className: "BP_PA_LP_Venom"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP/BP_PA_LP_Venom.BP_PA_LP_Venom_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000008
  className: "BP_ActorSpawner"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_ActorSpawner.BP_ActorSpawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000009
  className: "BP_AddGameScore"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_AddGameScore.BP_AddGameScore_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000010
  className: "BP_Cask_Broken_01"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_Cask_Broken_01.BP_Cask_Broken_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3011
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000011
  className: "BP_GamePlayScoreNeededtoWin"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP_GamePlayScoreNeededtoWin.BP_GamePlayScoreNeededtoWin_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000012
  className: "BP_KFCCask_Broken_01"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_KFCCask_Broken_01.BP_KFCCask_Broken_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000013
  className: "BP_PropSpawner"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_PropSpawner.BP_PropSpawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000014
  className: "BP_RainTrigger"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_RainTrigger.BP_RainTrigger_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000015
  className: "BP_PA_GE_CrownStage_001_A"
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000016
  className: "BP_PA_GE_GameHidden_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/GameHidden/BP/BP_PA_GE_GameHidden_001_A.BP_PA_GE_GameHidden_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5004
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000017
  className: "BP_PA_LP_ArchiveArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_ArchiveArea.BP_PA_LP_ArchiveArea_C"
  protoName: "ArchiveAreaInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000018
  className: "BP_PA_LP_ArchiveAreaBirthpoint"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_ArchiveAreaBirthpoint.BP_PA_LP_ArchiveAreaBirthpoint_C"
  protoName: "ArchiveAreaBirthPointInfo"
  subTypeID: 5001
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000019
  className: "BP_PA_LP_DestinationArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DestinationArea.BP_PA_LP_DestinationArea_C"
  protoName: "DestinationAreaInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000020
  className: "BP_PA_LP_DestroyArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DestroyArea.BP_PA_LP_DestroyArea_C"
  protoName: "DestroyAreaInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000021
  className: "BP_PA_LP_DisplayPoint"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DisplayPoint.BP_PA_LP_DisplayPoint_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000022
  className: "BP_PA_LP_DisplayPoint_Blue"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DisplayPoint_Blue.BP_PA_LP_DisplayPoint_Blue_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000023
  className: "BP_PA_LP_DisplayPoint_Master"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DisplayPoint_Master.BP_PA_LP_DisplayPoint_Master_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000024
  className: "BP_PA_LP_DisplayTrigger"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DisplayTrigger.BP_PA_LP_DisplayTrigger_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000025
  className: "BP_PA_LP_GodBornPoint"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_GodBornPoint.BP_PA_LP_GodBornPoint_C"
  protoName: "LogicExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000026
  className: "BP_PA_LP_LogicSwitch"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LogicSwitch.BP_PA_LP_LogicSwitch_C"
  protoName: "LogicSwitcherExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000027
  className: "BP_PA_LP_SpawnPoint"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_SpawnPoint.BP_PA_LP_SpawnPoint_C"
  protoName: "SpawnPointInfo"
  subTypeID: 5008
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000028
  className: "BP_PA_LP_Switch"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_Switch.BP_PA_LP_Switch_C"
  protoName: "SwitcherExportInfo"
  subTypeID: 5005
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000029
  className: "BP_PA_LP_SwitcherArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_SwitcherArea.BP_PA_LP_SwitcherArea_C"
  protoName: "SwitcherAreaInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000030
  className: "BP_PA_LP_TriggerArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_TriggerArea.BP_PA_LP_TriggerArea_C"
  protoName: "TriggerAreaInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000031
  className: "BP_PA_LP_MA_Spawn_001"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Modifiers/BP/BP_PA_LP_MA_Spawn_001.BP_PA_LP_MA_Spawn_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1006
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000032
  className: "BP_PA_LP_MA_Spawn_002"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Modifiers/BP/BP_PA_LP_MA_Spawn_002.BP_PA_LP_MA_Spawn_002_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1006
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000033
  className: "BP_PA_LP_MA_SplitGroup"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Modifiers/BP/BP_PA_LP_MA_SplitGroup.BP_PA_LP_MA_SplitGroup_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1006
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000034
  className: "BP_LevelConfig"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Modifiers/BP/Edit/BP_LevelConfig.BP_LevelConfig_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000035
  className: "BP_PA_M_Floor_Edit_008"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/Modifiers/BP/Edit/BP_PA_M_Floor_Edit_008.BP_PA_M_Floor_Edit_008_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000036
  className: "BP_PA_M_Floor_Edit_010"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Modifiers/BP/Edit/BP_PA_M_Floor_Edit_010.BP_PA_M_Floor_Edit_010_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000037
  className: "UI_Title"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Modifiers/BP/Edit/UI_Title.UI_Title_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000038
  className: "BP_PA_LP_GM_ChangeMoveState"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Modifiers/BP/GroupModifiers/BP_PA_LP_GM_ChangeMoveState.BP_PA_LP_GM_ChangeMoveState_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1006
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000039
  className: "BP_PA_LP_GM_DoAction"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Modifiers/BP/GroupModifiers/BP_PA_LP_GM_DoAction.BP_PA_LP_GM_DoAction_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1006
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000040
  className: "BP_PA_LP_GM_SetParam"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Modifiers/BP/GroupModifiers/BP_PA_LP_GM_SetParam.BP_PA_LP_GM_SetParam_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1006
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000041
  className: "BP_PA_LP_Modifier"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Modifiers/BP_PA_LP_Modifier.BP_PA_LP_Modifier_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1006
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000042
  className: "BP_PA_LP_PropBox_UGC"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/PropBox/BP/BP_PA_LP_PropBox_UGC.BP_PA_LP_PropBox_UGC_C"
  protoName: "PropBoxInfo"
  subTypeID: 3067
  IsCombineUGC: 1
  RedirectTypeID: 1105000000000201
}
rows {
  typeId: 1105000000000043
  className: "BP_PA_LP_AISpawner"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Spawners/BP/BP_PA_LP_AISpawner.BP_PA_LP_AISpawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5001
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000044
  className: "SplineWall"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/SplineWall.SplineWall_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000045
  className: "SplineWall_Fence"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/SplineWall_Fence.SplineWall_Fence_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000046
  className: "BP_PA_GE_Trigger_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Trigger/BP/BP_PA_GE_Trigger_001_A.BP_PA_GE_Trigger_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000047
  className: "BP_PA_LP_CountTrigger_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Trigger/BP/BP_PA_LP_CountTrigger_001_A.BP_PA_LP_CountTrigger_001_A_C"
  protoName: "GenericCounterExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000048
  className: "BP_PA_LP_TimeTrigger_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Trigger/BP/BP_PA_LP_TimeTrigger_001_A.BP_PA_LP_TimeTrigger_001_A_C"
  protoName: "TimerTriggerExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000049
  className: "BP_GuideTrigger"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_GuideTrigger.BP_GuideTrigger_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000050
  className: "BP_Debug_Cube"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP/BP_Debug_Cube.BP_Debug_Cube_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000051
  className: "BP_Debug_Sphere"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP/BP_Debug_Sphere.BP_Debug_Sphere_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000052
  className: "BP_PA_LP_DisplayLED_0"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DisplayLED_0.BP_PA_LP_DisplayLED_0_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000053
  className: "BP_PA_LP_DisplayLED_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DisplayLED_1.BP_PA_LP_DisplayLED_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000054
  className: "BP_PA_LP_DisplayLED_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DisplayLED_2.BP_PA_LP_DisplayLED_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000055
  className: "BP_PA_LP_EventTriggerArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_EventTriggerArea.BP_PA_LP_EventTriggerArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000056
  className: "BP_PA_LP_EventDirector"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_EventDirector.BP_PA_LP_EventDirector_C"
  protoName: "EventDirectorExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000057
  className: "BP_PA_LP_TimeDirector"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_TimeDirector.BP_PA_LP_TimeDirector_C"
  protoName: "TimeDirectorExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000058
  className: "BP_PA_LP_PASpawnPoint"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_PASpawnPoint.BP_PA_LP_PASpawnPoint_C"
  protoName: "PASpawnPointExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000059
  className: "BP_GuidePosition"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_GuidePosition.BP_GuidePosition_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000060
  className: "BP_PropSpawner_Guide1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP_PropSpawner_Guide1.BP_PropSpawner_Guide1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000061
  className: "BP_PropSpawner_Guide2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP_PropSpawner_Guide2.BP_PropSpawner_Guide2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000062
  className: "BP_PropSpawner_Guide3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP_PropSpawner_Guide3.BP_PropSpawner_Guide3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000063
  className: "BP_PA_LP_PlayerPoint"
  classPath: "/Game/LetsGo/Assets/CommunityAssets/Placeables/LogicPAs/LogicActor/BP_PA_LP_PlayerPoint.BP_PA_LP_PlayerPoint_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000064
  className: "BP_PA_LP_RandomGenerator"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_RandomGenerator.BP_PA_LP_RandomGenerator_C"
  protoName: "RandomGeneratorExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000065
  className: "BP_MoeObjectGroupMove"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/pool/BP_MoeObjectGroupMove.BP_MoeObjectGroupMove_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000066
  className: "BP_MoeObjectPool"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/pool/BP_MoeObjectPool.BP_MoeObjectPool_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000067
  className: "BP_CrownStageVolume"
  classPath: "/Game/LetsGo/Blueprints/AI/BP_CrownStageVolume.BP_CrownStageVolume_C"
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000068
  className: "BP_PA_LP_DisplayLED_0_Low"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DisplayLED_0_Low.BP_PA_LP_DisplayLED_0_Low_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000069
  className: "BP_PA_LP_SpringSwitch"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_SpringSwitch.BP_PA_LP_SpringSwitch_C"
  protoName: "SwitcherExportInfo"
  subTypeID: 5005
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000070
  className: "BP_3DUI_CountDown"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_3DUI_CountDown.BP_3DUI_CountDown_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000071
  className: "BP_3DUI_Logo"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_3DUI_Logo.BP_3DUI_Logo_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000072
  className: "BP_PA_LP_Boundary_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Boundary/BP/BP_PA_LP_Boundary_001_A.BP_PA_LP_Boundary_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000073
  className: "BP_PA_LP_RaceRankInteractive"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP/BP_PA_LP_RaceRankInteractive.BP_PA_LP_RaceRankInteractive_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000172
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000074
  className: "BP_PA_LP_Airwall"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_Airwall.BP_PA_LP_Airwall_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000273
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000075
  className: "AI_WayPoint"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/AI_WayPoint/AI_WayPoint.AI_WayPoint_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000076
  className: "BP_PA_LP_FootballRebirth"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP/BP_PA_LP_FootballRebirth.BP_PA_LP_FootballRebirth_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000077
  className: "BP_PA_LP_PropBox_FPS_001"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/PropBox/BP/BP_PA_LP_PropBox_FPS_001.BP_PA_LP_PropBox_FPS_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000078
  className: "BP_PA_LP_SpawnPointHome"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_SpawnPointHome.BP_PA_LP_SpawnPointHome_C"
  protoName: "SpawnPointInfo"
  subTypeID: 5008
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000079
  className: "BP_PA_LP_BombArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BombArea/BP/BP_PA_LP_BombArea.BP_PA_LP_BombArea_C"
  protoName: "BombPlacementAreaInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000080
  className: "BP_PA_LP_TeamCounter"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/TeamCounter/BP/BP_PA_LP_TeamCounter.BP_PA_LP_TeamCounter_C"
  protoName: "TeamFightAddScoreInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000081
  className: "BP_PA_LP_AILearningTips"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/LearningAI/BP_PA_LP_AILearningTips.BP_PA_LP_AILearningTips_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3073
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000082
  className: "BP_PA_LP_AILearningWayPoint"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LearningAI/BP_PA_LP_AILearningWayPoint.BP_PA_LP_AILearningWayPoint_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3073
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000083
  className: "BP_PA_LP_DestinationArea_B"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DestinationArea_B.BP_PA_LP_DestinationArea_B_C"
  protoName: "DestinationAreaInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000084
  className: "BP_CharacterSoundTriggerArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_CharacterSoundTriggerArea.BP_CharacterSoundTriggerArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000085
  className: "BP_PA_LP_HitResponseArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/HitResponseArea/BP/BP_PA_LP_HitResponseArea.BP_PA_LP_HitResponseArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000086
  className: "BP_PA_LP_Rebirth_Home"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP/BP_PA_LP_Rebirth_Home.BP_PA_LP_Rebirth_Home_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000087
  className: "BP_PA_LP_TriggerAreaNew"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_TriggerAreaNew.BP_PA_LP_TriggerAreaNew_C"
  protoName: "TriggerAreaInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000088
  className: "BP_PA_LP_AILearningWaitPoint"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/LearningAI/BP_PA_LP_AILearningWaitPoint.BP_PA_LP_AILearningWaitPoint_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3073
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000089
  className: "BP_Cask_Broken_2"
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000090
  className: "BP_PA_GE_GameHidden_NoRep_001"
  classPath: "/Game/LetsGo/Assets/CommunityAssets/Placeables/LogicPAs/GameHidden/BP/BP_PA_GE_GameHidden_NoRep_001.BP_PA_GE_GameHidden_NoRep_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5004
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000091
  className: "BP_PA_LP_DirectorGroup"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DirectorGroup.BP_PA_LP_DirectorGroup_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000092
  className: "BP_PA_LP_DirectorPoint"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DirectorPoint.BP_PA_LP_DirectorPoint_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000093
  className: "BP_PA_LP_LightHouseArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LightHouseArea.BP_PA_LP_LightHouseArea_C"
  protoName: "LightHouseExportInfo"
  subTypeID: 3018
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000094
  className: "BP_PA_LP_FPSAddScoreArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/FPSAddScoreArea/BP/BP_PA_LP_FPSAddScoreArea.BP_PA_LP_FPSAddScoreArea_C"
  protoName: "FPSAddScoreAreaActorInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000095
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000096
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000097
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000099
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000100
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000102
  className: "BP_PA_LP_HurtBoxArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/DamageCollisionArea/BP/BP_PA_LP_HurtBoxArea.BP_PA_LP_HurtBoxArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000103
  className: "BP_PA_LP_PropBox_Infinte_UGC"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/PropBox/BP/BP_PA_LP_PropBox_Infinte_UGC.BP_PA_LP_PropBox_Infinte_UGC_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 1
  RedirectTypeID: 1105000000000202
}
rows {
  typeId: 1105000000000104
  className: "BP_PA_NewComer_Setting"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP/BP_PA_NewComer_Setting.BP_PA_NewComer_Setting_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000105
  className: "BP_PA_LP_GM_SetMonsterParam"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/Modifiers/BP/GroupModifiers/BP_PA_LP_GM_SetMonsterParam.BP_PA_LP_GM_SetMonsterParam_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1006
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000107
  className: "BP_PA_LP_AvatarChangeTrigger"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_AvatarChangeTrigger.BP_PA_LP_AvatarChangeTrigger_C"
  protoName: "TriggerAreaInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000108
  className: "BP_PA_LP_GM_RectangleSpawner_Base"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/Modifiers/BP/Spawner/BP_PA_LP_GM_RectangleSpawner_Base.BP_PA_LP_GM_RectangleSpawner_Base_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1006
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000109
  className: "BP_PA_LP_AIWaypointPlacer"
  classPath: "/Game/Feature/UGC/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_AIWaypointPlacer.BP_PA_LP_AIWaypointPlacer_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000110
  className: "BP_PA_LP_UGCHiddenGrass"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_UGCHiddenGrass.BP_PA_LP_UGCHiddenGrass_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000292
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000111
  className: "BP_PA_LP_UGCAITargetPoint"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_UGCAITargetPoint.BP_PA_LP_UGCAITargetPoint_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000291
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000112
  className: "BP_PA_LP_BehaviorSwitcher"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_BehaviorSwitcher.BP_PA_LP_BehaviorSwitcher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000113
  className: "BP_PA_PC_UGCPreviewCamera"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Camera/BP_PA_PC_UGCPreviewCamera.BP_PA_PC_UGCPreviewCamera_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000271
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000114
  className: "BP_PA_GE_UIControl_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BasicComponents/BP_PA_GE_UIControl_001_A.BP_PA_GE_UIControl_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000115
  className: "BP_PA_GE_TimeTrigger_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BasicComponents/BP_PA_GE_TimeTrigger_001_A.BP_PA_GE_TimeTrigger_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000116
  className: "BP_PA_LP_TeleportBeacon"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_TeleportBeacon.BP_PA_LP_TeleportBeacon_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000129
  className: "BP_PA_LP_ConeBooleanableSharpActor"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BooleanableActor/BP_PA_LP_ConeBooleanableSharpActor.BP_PA_LP_ConeBooleanableSharpActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000130
  className: "BP_PA_LP_CubeBooleanableSharpActor"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BooleanableActor/BP_PA_LP_CubeBooleanableSharpActor.BP_PA_LP_CubeBooleanableSharpActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000131
  className: "BP_PA_LP_FrustumCircleBooleanableSharpActor"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BooleanableActor/BP_PA_LP_FrustumCircleBooleanableSharpActor.BP_PA_LP_FrustumCircleBooleanableSharpActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000132
  className: "BP_PA_LP_SlopeBooleanableSharpActor"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BooleanableActor/BP_PA_LP_SlopeBooleanableSharpActor.BP_PA_LP_SlopeBooleanableSharpActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000133
  className: "BP_PA_LP_TorusBooleanableSharpActor"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BooleanableActor/BP_PA_LP_TorusBooleanableSharpActor.BP_PA_LP_TorusBooleanableSharpActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000134
  className: "BP_PA_LP_TorusSquareBooleanableSharpActor"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BooleanableActor/BP_PA_LP_TorusSquareBooleanableSharpActor.BP_PA_LP_TorusSquareBooleanableSharpActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000135
  className: "BP_PA_LP_ReturnSquareTriggerArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_ReturnSquareTriggerArea.BP_PA_LP_ReturnSquareTriggerArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000136
  className: "BP_PA_PC_UGCActorCameraLockTarget"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Camera/BP_PA_PC_UGCActorCameraLockTarget.BP_PA_PC_UGCActorCameraLockTarget_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000270
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000137
  className: "BP_Cask_Broken_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP_Cask_Broken_2.BP_Cask_Broken_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3011
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000138
  className: "BP_DDPGuideSetLocation"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_DDPGuideSetLocation.BP_DDPGuideSetLocation_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000265
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000139
  className: "BP_DDPGuideTrigger"
  classPath: "/Game/Feature/DDP/Assets/Placeables/LogicPAs/BP_DDPGuideTrigger.BP_DDPGuideTrigger_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000140
  className: "BP_FPSGunPropSpawner"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_FPSGunPropSpawner.BP_FPSGunPropSpawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000141
  className: "BP_JSGuideTrigger"
  classPath: "/Game/Feature/JS/Assets/Placeables/LogicPAs/BP_JSGuideTrigger.BP_JSGuideTrigger_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000142
  className: "BP_PropSpawner_BioChase"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_PropSpawner_BioChase.BP_PropSpawner_BioChase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000143
  className: "BP_PA_LP_DialogueArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DialogueArea.BP_PA_LP_DialogueArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000144
  className: "BP_PA_LP_ServerTimeTrigger"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_ServerTimeTrigger.BP_PA_LP_ServerTimeTrigger_C"
  protoName: "ServerTimeTriggerInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000145
  className: "BP_PA_LP_LiveMoniter"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LiveStream/BP_PA_LP_LiveMoniter.BP_PA_LP_LiveMoniter_C"
  protoName: "LiveMoniterInfo"
  subTypeID: 1000112
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000146
  className: "BP_PA_LP_SkillSwitcher"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_SkillSwitcher.BP_PA_LP_SkillSwitcher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000147
  className: "BP_PA_LP_Rebirth_LobbyUGC"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP/BP_PA_LP_Rebirth_LobbyUGC.BP_PA_LP_Rebirth_LobbyUGC_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000148
  className: "BP_PA_LP_CustomEquipBase_A"
  classPath: "/Game/Feature/UGC/CustomEquip/BP_PA_LP_CustomEquipBase_A.BP_PA_LP_CustomEquipBase_A_C"
  protoName: "UGCCustomEquipInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000151
  className: "BP_PA_PC_CUSTOM_ANIM_Actor"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/CustomAnim/BP_PA_PC_CUSTOM_ANIM_Actor.BP_PA_PC_CUSTOM_ANIM_Actor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000152
  className: "BP_PA_PC_CUSTOM_ANIM_SubBone_Actor"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/CustomAnim/BP_PA_PC_CUSTOM_ANIM_SubBone_Actor.BP_PA_PC_CUSTOM_ANIM_SubBone_Actor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000153
  className: "BP_PA_DefaultCustom_Helmet"
  classPath: "/Game/Feature/UGC/CustomEquip/BP_PA_DefaultCustom_Helmet.BP_PA_DefaultCustom_Helmet_C"
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000154
  className: "BP_PA_DefaultCustom_Armor"
  classPath: "/Game/Feature/UGC/CustomEquip/BP_PA_DefaultCustom_Armor.BP_PA_DefaultCustom_Armor_C"
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000155
  className: "BP_PA_DefaultCustom_LowerGarment"
  classPath: "/Game/Feature/UGC/CustomEquip/BP_PA_DefaultCustom_LowerGarment.BP_PA_DefaultCustom_LowerGarment_C"
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000156
  className: "BP_PA_DefaultCustom_Bracelet"
  classPath: "/Game/Feature/UGC/CustomEquip/BP_PA_DefaultCustom_Bracelet.BP_PA_DefaultCustom_Bracelet_C"
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000157
  className: "BP_PA_DefaultCustom_Shoes"
  classPath: "/Game/Feature/UGC/CustomEquip/BP_PA_DefaultCustom_Shoes.BP_PA_DefaultCustom_Shoes_C"
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000158
  className: "BP_PA_DefaultCustom_Back"
  classPath: "/Game/Feature/UGC/CustomEquip/BP_PA_DefaultCustom_Back.BP_PA_DefaultCustom_Back_C"
  protoName: "PlaceablesExportInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000159
  className: "BP_PA_LP_ChangeAttrArea"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP/BP_PA_LP_ChangeAttrArea.BP_PA_LP_ChangeAttrArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000160
  className: "BP_PA_LP_DestinationArea_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_DestinationArea_2.BP_PA_LP_DestinationArea_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000161
  className: "BP_PA_LP_CloudGameTriggerArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/Customized/BP_PA_LP_CloudGameTriggerArea.BP_PA_LP_CloudGameTriggerArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000162
  className: "BP_PA_LP_LobbyJumpLobbyTrigger"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LobbyJumpLobbyTrigger.BP_PA_LP_LobbyJumpLobbyTrigger_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000163
  className: "BP_PA_LP_LobbyJumpUGCMapTrigger"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LobbyJumpUGCMapTrigger.BP_PA_LP_LobbyJumpUGCMapTrigger_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000164
  className: "BP_PA_LP_LobbySpawnArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LobbySpawnArea.BP_PA_LP_LobbySpawnArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000165
  className: "BP_PA_LP_Rebirth_Shoot"
  classPath: "/Game/Feature/FPS/Assets/Placeables/LogicPAs/BP/BP_PA_LP_Rebirth_Shoot.BP_PA_LP_Rebirth_Shoot_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000174
  className: "BP_PA_LP_CheckpointWithRebirthAreas"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP/BP_PA_LP_CheckpointWithRebirthAreas.BP_PA_LP_CheckpointWithRebirthAreas_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000175
  className: "BP_PropSpawner_VehcileSkill"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_PropSpawner_VehcileSkill.BP_PropSpawner_VehcileSkill_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000176
  className: "BP_PA_LP_Venom_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP/BP_PA_LP_Venom_2.BP_PA_LP_Venom_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000188
  className: "BP_PA_LP_Indicator"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_Indicator.BP_PA_LP_Indicator_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000189
  className: "BP_PropSpawner_CommunityDress"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/Spawner/BP_PropSpawner_CommunityDress.BP_PropSpawner_CommunityDress_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000190
  className: "BP_PA_LP_Collection"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_Collection.BP_PA_LP_Collection_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5005
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000191
  className: "BP_PA_LP_Venom_3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP/BP_PA_LP_Venom_3.BP_PA_LP_Venom_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000192
  className: "BP_PA_LP_FlypointPlacer"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/OmdPath/BP_PA_LP_FlypointPlacer.BP_PA_LP_FlypointPlacer_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000193
  className: "BP_PA_LP_RoadpointPlacer"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/OmdPath/BP_PA_LP_RoadpointPlacer.BP_PA_LP_RoadpointPlacer_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000194
  className: "BP_PA_LP_OMDPathSpawnerBase"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/OmdPath/BP/BP_PA_LP_OMDPathSpawnerBase.BP_PA_LP_OMDPathSpawnerBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000195
  className: "BP_PA_LP_OMDPathSpawnerFly"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/OmdPath/BP/BP_PA_LP_OMDPathSpawnerFly.BP_PA_LP_OMDPathSpawnerFly_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000196
  className: "BP_PA_LP_OMDPathSpawnerGround"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/OmdPath/BP/BP_PA_LP_OMDPathSpawnerGround.BP_PA_LP_OMDPathSpawnerGround_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000197
  className: "BP_PA_LP_SpawnPoint_OMD"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/OMDSpawnPoint/BP_PA_LP_SpawnPoint_OMD.BP_PA_LP_SpawnPoint_OMD_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000198
  className: "BP_PA_GE_TargetPoint_OMD"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/OMDRebirthPoint/BP_PA_GE_TargetPoint_OMD.BP_PA_GE_TargetPoint_OMD_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000199
  className: "BP_PA_LP_SpawnPoint_test"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_SpawnPoint_test.BP_PA_LP_SpawnPoint_test_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5008
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000200
  className: "BP_ShuttlePropSpawner"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP_ShuttlePropSpawner.BP_ShuttlePropSpawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000201
  className: "BP_PA_LP_PropBox_001"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/PropBox/BP/BP_PA_LP_PropBox_001.BP_PA_LP_PropBox_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 2
  RedirectTypeID: 1105000000000042
}
rows {
  typeId: 1105000000000202
  className: "BP_PA_LP_PropBox_Infinte_001"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/PropBox/BP/BP_PA_LP_PropBox_Infinte_001.BP_PA_LP_PropBox_Infinte_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
  IsCombineUGC: 2
  RedirectTypeID: 1105000000000103
}
rows {
  typeId: 1105000000000203
  className: "BP_PA_LP_TrapField"
  classPath: "/Game/Feature/UGC/Assets/Placeables/LogicPAs/Trap/BP_PA_LP_TrapField.BP_PA_LP_TrapField_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000204
  className: "BP_PA_LP_NavMeshBoundsVolume"
  classPath: "/Game/Feature/UGC/Assets/Placeables/LogicPAs/NavMeshBoundsVolume/BP_PA_LP_NavMeshBoundsVolume.BP_PA_LP_NavMeshBoundsVolume_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000205
  className: "BP_GreatBattle_Launcher"
  classPath: "/Game/Feature/UGC/Mod/GreatBattle/BP_GreatBattle_Launcher.BP_GreatBattle_Launcher_C"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1105000000000206
  className: "BP_OperationBrokenBridge"
  classPath: "/Game/Feature/UGC/Mod/OBB/BP_OperationBrokenBridge.BP_OperationBrokenBridge_C"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1105000000000207
  className: "BP_PA_LP_AIAssistantLocater"
  classPath: "/Game/Feature/UGC/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_AIAssistantLocater.BP_PA_LP_AIAssistantLocater_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000208
  className: "BP_PA_GE_DamageCollision_OMD"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/OMDDamageCollisionArea/BP_PA_GE_DamageCollision_OMD.BP_PA_GE_DamageCollision_OMD_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000209
  className: "BP_PA_LP_LevelEventSpawner"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LevelEventSpawner.BP_PA_LP_LevelEventSpawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000210
  className: "BP_PA_LP_UGCLobbyJumpHome"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_UGCLobbyJumpHome.BP_PA_LP_UGCLobbyJumpHome_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000475
}
rows {
  typeId: 1105000000000211
  className: "BP_PA_LP_UGCLobbyJumpFarm"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_UGCLobbyJumpFarm.BP_PA_LP_UGCLobbyJumpFarm_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000212
  className: "BP_PA_LP_LevelEventSpawner_Acceleration"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LevelEventSpawner_Acceleration.BP_PA_LP_LevelEventSpawner_Acceleration_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000213
  className: "BP_PA_LP_LevelEventSpawner_Cream"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LevelEventSpawner_Cream.BP_PA_LP_LevelEventSpawner_Cream_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000214
  className: "BP_PA_LP_LevelEventSpawner_PlasmaBall"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LevelEventSpawner_PlasmaBall.BP_PA_LP_LevelEventSpawner_PlasmaBall_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000215
  className: "BP_PA_LP_LevelEventSpawner_Tornado"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LevelEventSpawner_Tornado.BP_PA_LP_LevelEventSpawner_Tornado_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000216
  className: "BP_PA_LP_LevelEventSpawnerBase"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/LogicActor/BP_PA_LP_LevelEventSpawnerBase.BP_PA_LP_LevelEventSpawnerBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000217
  className: "BP_LA_SequencePlayer_Base"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/SequencePlayer/BP_LA_SequencePlayer_Base.BP_LA_SequencePlayer_Base_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000218
  className: "BP_PA_GE_GameManagerHP"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/BP/BP_PA_GE_GameManagerHP.BP_PA_GE_GameManagerHP_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1105000000000219
  className: "BP_PA_LP_UGCScript"
  classPath: "/Game/Feature/UGC/Assets/Placeables/LogicPAs/UGCScript/BP_PA_LP_UGCScript.BP_PA_LP_UGCScript_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1105000000000220
  className: "BP_OMDMiniMapEditBaseActor"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/OMDMiniMapEditActor/BP_OMDMiniMapEditBaseActor.BP_OMDMiniMapEditBaseActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1105000000000221
  className: "BP_OMDMiniMapEditBoxActor"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/OMDMiniMapEditActor/BP_OMDMiniMapEditBoxActor.BP_OMDMiniMapEditBoxActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1105000000000222
  className: "BP_PA_LP_NavMeshBoundsVolumeUGC"
  classPath: "/Game/Feature/UGC/Assets/Placeables/LogicPAs/NavMeshBoundsVolume/BP_PA_LP_NavMeshBoundsVolumeUGC.BP_PA_LP_NavMeshBoundsVolumeUGC_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000223
  className: "BP_PA_LP_MiniMapDataLevelsEntrance"
  classPath: "/Game/Feature/UGC/Assets/Placeables/LogicPAs/MiniMapData/BP_PA_LP_MiniMapDataLevelsEntrance.BP_PA_LP_MiniMapDataLevelsEntrance_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000224
  className: "BP_PA_LP_MiniMapDataRoad"
  classPath: "/Game/Feature/UGC/Assets/Placeables/LogicPAs/MiniMapData/BP_PA_LP_MiniMapDataRoad.BP_PA_LP_MiniMapDataRoad_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1105000000000226
  className: "BP_PropSpawner_FoxVehcileSkill"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_PropSpawner_FoxVehcileSkill.BP_PropSpawner_FoxVehcileSkill_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1105000000000227
  className: "BP_GuideTriggerForLocalPawn"
  classPath: "/Game/LetsGo/Assets/Placeables/LogicPAs/BP_GuideTriggerForLocalPawn.BP_GuideTriggerForLocalPawn_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1105000000000228
  className: "BP_PA_AIGenActor"
  classPath: "/Game/Feature/UGC/AIGenActor/BP_PA_AIGenActor.BP_PA_AIGenActor_C"
  protoName: "UGCAIGenActorInfo"
  IsCombineUGC: 0
}
rows {
  typeId: 1105000000000229
  className: "BP_PA_LP_CameraRotation"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LogicPAs/CameraRotation/BP_PA_LP_CameraRotation.BP_PA_LP_CameraRotation_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}

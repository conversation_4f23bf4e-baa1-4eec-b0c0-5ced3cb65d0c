com.tencent.wea.xlsRes.table_BattlePassLevelRewardConf
excel/xls/S_暑期BP_经验奖励表.xlsx sheet:等级奖励
rows {
  id: 100
  level: 1
  freeRewards {
    itemId: 4
    itemNum: 200
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 2
  freeRewards {
    itemId: 200006
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 3
  freeRewards {
    itemId: 6
    itemNum: 10
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 4
  freeRewards {
    itemId: 200642
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 5
  freeRewards {
    itemId: 219000
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 6
  freeRewards {
    itemId: 4
    itemNum: 200
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 7
  freeRewards {
    itemId: 200006
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 8
  freeRewards {
    itemId: 6
    itemNum: 10
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 9
  freeRewards {
    itemId: 200642
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 10
  freeRewards {
    itemId: 200201
    itemNum: 1
  }
  exp: 100
  isFlag: true
  hint: "2037:317155"
}
rows {
  id: 100
  level: 11
  freeRewards {
    itemId: 4
    itemNum: 200
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 12
  freeRewards {
    itemId: 200006
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 13
  freeRewards {
    itemId: 6
    itemNum: 10
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 14
  freeRewards {
    itemId: 200642
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 15
  freeRewards {
    itemId: 219000
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 16
  freeRewards {
    itemId: 4
    itemNum: 200
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 17
  freeRewards {
    itemId: 200006
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 18
  freeRewards {
    itemId: 6
    itemNum: 10
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 19
  freeRewards {
    itemId: 200642
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 20
  freeRewards {
    itemId: 860223
    itemNum: 1
  }
  exp: 100
  isFlag: true
  hint: "2040:317156"
}
rows {
  id: 100
  level: 21
  freeRewards {
    itemId: 4
    itemNum: 200
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 22
  freeRewards {
    itemId: 200006
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 23
  freeRewards {
    itemId: 6
    itemNum: 10
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 24
  freeRewards {
    itemId: 200642
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 25
  freeRewards {
    itemId: 219000
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 26
  freeRewards {
    itemId: 4
    itemNum: 200
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 27
  freeRewards {
    itemId: 200006
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 28
  freeRewards {
    itemId: 6
    itemNum: 10
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 29
  freeRewards {
    itemId: 200642
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 30
  freeRewards {
    itemId: 200620
    itemNum: 20
  }
  exp: 100
  isFlag: true
  hint: "2038:317157"
}
rows {
  id: 100
  level: 31
  freeRewards {
    itemId: 4
    itemNum: 200
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 32
  freeRewards {
    itemId: 200006
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 33
  freeRewards {
    itemId: 6
    itemNum: 10
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 34
  freeRewards {
    itemId: 200642
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 35
  freeRewards {
    itemId: 219000
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 36
  freeRewards {
    itemId: 4
    itemNum: 200
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 37
  freeRewards {
    itemId: 200006
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 38
  freeRewards {
    itemId: 6
    itemNum: 10
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 39
  freeRewards {
    itemId: 200642
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 40
  freeRewards {
    itemId: 840318
    itemNum: 1
  }
  exp: 100
  isFlag: true
  hint: "2039:317158"
}
rows {
  id: 100
  level: 41
  freeRewards {
    itemId: 4
    itemNum: 200
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 42
  freeRewards {
    itemId: 200006
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 43
  freeRewards {
    itemId: 6
    itemNum: 10
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 44
  freeRewards {
    itemId: 200642
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 45
  freeRewards {
    itemId: 219000
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 46
  freeRewards {
    itemId: 4
    itemNum: 200
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 47
  freeRewards {
    itemId: 200006
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 48
  freeRewards {
    itemId: 6
    itemNum: 10
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 49
  freeRewards {
    itemId: 200642
    itemNum: 2
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 50
  freeRewards {
    itemId: 218205
    itemNum: 1
  }
  exp: 100
  isFlag: true
  hint: "2041:317159"
}
rows {
  id: 100
  level: 51
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 52
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 53
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 54
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 55
  freeRewards {
    itemId: 200642
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 56
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 57
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 58
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 59
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 60
  freeRewards {
    itemId: 850485
    itemNum: 1
  }
  exp: 100
  isFlag: true
}
rows {
  id: 100
  level: 61
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 62
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 63
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 64
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 65
  freeRewards {
    itemId: 200642
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 66
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 67
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 68
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 69
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 70
  freeRewards {
    itemId: 219608
    itemNum: 1
  }
  exp: 100
  isFlag: true
}
rows {
  id: 100
  level: 71
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 72
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 73
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 74
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 75
  freeRewards {
    itemId: 200642
    itemNum: 1
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 76
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 77
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 78
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 79
  freeRewards {
    itemId: 4
    itemNum: 50
  }
  exp: 100
  isFlag: false
}
rows {
  id: 100
  level: 80
  freeRewards {
    itemId: 711378
    itemNum: 1
  }
  exp: 100
  isFlag: true
}

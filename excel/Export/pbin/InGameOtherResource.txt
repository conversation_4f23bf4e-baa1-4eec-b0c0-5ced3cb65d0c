com.tencent.wea.xlsRes.table_InGameOtherResource
excel/xls/D_道具表_时装.xlsm sheet:局内其他资源
rows {
  id: 510001
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_001_LOD1"
    modelType: 2
  }
}
rows {
  id: 510004
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_004_LOD1"
    modelType: 2
  }
}
rows {
  id: 510005
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_005_LOD1"
    modelType: 2
  }
}
rows {
  id: 510006
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_022_LOD1"
    modelType: 2
  }
}
rows {
  id: 510007
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_023_LOD1"
    modelType: 2
  }
}
rows {
  id: 510008
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_024_LOD1"
    modelType: 2
  }
}
rows {
  id: 510009
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_025_LOD1"
    modelType: 2
  }
}
rows {
  id: 510010
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_026_LOD1"
    modelType: 2
  }
}
rows {
  id: 510011
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_027_LOD1"
    modelType: 2
  }
}
rows {
  id: 510012
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_002_LOD1"
    modelType: 2
  }
}
rows {
  id: 510013
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_007_LOD1"
    modelType: 2
  }
}
rows {
  id: 510014
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_010_LOD1"
    modelType: 2
  }
}
rows {
  id: 510015
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_021_LOD1"
    modelType: 2
  }
}
rows {
  id: 510016
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_019_LOD1"
    modelType: 2
  }
}
rows {
  id: 510017
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_028_LOD1"
    modelType: 2
  }
}
rows {
  id: 510018
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_029_LOD1"
    modelType: 2
  }
}
rows {
  id: 510019
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_014_LOD1"
    modelType: 2
  }
}
rows {
  id: 510020
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_016_LOD1"
    modelType: 2
  }
}
rows {
  id: 510021
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_018_LOD1"
    modelType: 2
  }
}
rows {
  id: 510022
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_003_LOD1"
    modelType: 2
  }
}
rows {
  id: 510023
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_015_LOD1"
    modelType: 2
  }
}
rows {
  id: 510024
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_009_LOD1"
    modelType: 2
  }
}
rows {
  id: 510025
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_011_LOD1"
    modelType: 2
  }
}
rows {
  id: 510026
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_031_LOD1"
    modelType: 2
  }
}
rows {
  id: 510027
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_030_LOD1"
    modelType: 2
  }
}
rows {
  id: 510028
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_038_LOD1"
    modelType: 2
  }
}
rows {
  id: 510029
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_041_LOD1"
    modelType: 2
  }
}
rows {
  id: 510030
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_043_LOD1"
    modelType: 2
  }
}
rows {
  id: 510031
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_045_LOD1"
    modelType: 2
  }
}
rows {
  id: 510032
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_049_LOD1"
    modelType: 2
  }
}
rows {
  id: 510033
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_053_LOD1"
    bottom: "SK_TY_Under_053_LOD1"
    gloves: "SK_TY_Hands_053_LOD1"
    modelType: 2
  }
}
rows {
  id: 510034
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_054_LOD1"
    bottom: "SK_TY_Under_054_LOD1"
    gloves: "SK_TY_Hands_054_LOD1"
    modelType: 2
  }
}
rows {
  id: 510035
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_032_LOD1"
    bottom: "SK_TY_Under_032_LOD1"
    modelType: 2
  }
}
rows {
  id: 510036
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_033_LOD1"
    bottom: "SK_TY_Under_033_LOD1"
    modelType: 2
  }
}
rows {
  id: 510037
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_034_LOD1"
    bottom: "SK_TY_Under_034_LOD1"
    gloves: "SK_TY_Hands_034_LOD1"
    modelType: 2
  }
}
rows {
  id: 510038
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_035_LOD1"
    bottom: "SK_TY_Under_035_LOD1"
    modelType: 2
  }
}
rows {
  id: 510039
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_036_LOD1"
    bottom: "SK_TY_Under_036_LOD1"
    modelType: 2
  }
}
rows {
  id: 510040
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_037_LOD1"
    bottom: "SK_TY_Under_037_LOD1"
    gloves: "SK_TY_Hands_037_LOD1"
    modelType: 2
  }
}
rows {
  id: 510041
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_051_LOD1"
    modelType: 2
  }
}
rows {
  id: 510042
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_052_LOD1"
    modelType: 2
  }
}
rows {
  id: 510043
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_040_LOD1"
    modelType: 2
  }
}
rows {
  id: 510044
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_047_LOD1"
    modelType: 2
  }
}
rows {
  id: 510045
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_050_LOD1"
    modelType: 2
  }
}
rows {
  id: 510046
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_042_LOD1"
    modelType: 2
  }
}
rows {
  id: 510047
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_048_LOD1"
    modelType: 2
  }
}
rows {
  id: 510048
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_039_LOD1"
    modelType: 2
  }
}
rows {
  id: 510049
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_044_LOD1"
    modelType: 2
  }
}
rows {
  id: 510050
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_046_LOD1"
    modelType: 2
  }
}
rows {
  id: 510051
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_061_LOD1"
    modelType: 2
  }
}
rows {
  id: 510052
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_062_LOD1"
    modelType: 2
  }
}
rows {
  id: 510053
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_055_LOD1"
    bottom: "SK_TY_Under_055_LOD1"
    gloves: "SK_TY_Hands_055_LOD1"
    modelType: 2
  }
}
rows {
  id: 510054
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_056_LOD1"
    bottom: "SK_TY_Under_056_LOD1"
    gloves: "SK_TY_Hands_056_LOD1"
    modelType: 2
  }
}
rows {
  id: 510055
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_057_LOD1"
    bottom: "SK_TY_Under_057_LOD1"
    gloves: "SK_TY_Hands_057_LOD1"
    modelType: 2
  }
}
rows {
  id: 510056
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_058_LOD1"
    bottom: "SK_TY_Under_058_LOD1"
    gloves: "SK_TY_Hands_058_LOD1"
    modelType: 2
  }
}
rows {
  id: 510057
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_059_LOD1"
    bottom: "SK_TY_Under_059_LOD1"
    modelType: 2
  }
}
rows {
  id: 510058
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_060_LOD1"
    bottom: "SK_TY_Under_060_LOD1"
    modelType: 2
  }
}
rows {
  id: 510059
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_069_LOD1"
    modelType: 2
  }
}
rows {
  id: 510060
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_070_LOD1"
    modelType: 2
  }
}
rows {
  id: 510061
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_071_LOD1"
    modelType: 2
  }
}
rows {
  id: 510062
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_072_LOD1"
    modelType: 2
  }
}
rows {
  id: 510063
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_066_LOD1"
    bottom: "SK_TY_Under_066_LOD1"
    gloves: "SK_TY_Hands_066_LOD1"
    modelType: 2
  }
}
rows {
  id: 510064
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_063_LOD1"
    modelType: 2
  }
}
rows {
  id: 510065
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_064_LOD1"
    bottom: "SK_TY_Under_064_LOD1"
    gloves: "SK_TY_Hands_064_LOD1"
    modelType: 2
  }
}
rows {
  id: 510066
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_065_LOD1"
    bottom: "SK_TY_Under_065_LOD1"
    gloves: "SK_TY_Hands_065_LOD1"
    modelType: 2
  }
}
rows {
  id: 510067
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_067_LOD1"
    bottom: "SK_TY_Under_067_LOD1"
    gloves: "SK_TY_Hands_067_LOD1"
    modelType: 2
  }
}
rows {
  id: 510068
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_068_LOD1"
    bottom: "SK_TY_Under_068_LOD1"
    gloves: "SK_TY_Hands_068_LOD1"
    modelType: 2
  }
}
rows {
  id: 510069
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_095_LOD1"
    modelType: 2
  }
}
rows {
  id: 510070
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_073_LOD1"
    bottom: "SK_TY_Under_073_LOD1"
    gloves: "SK_TY_Hands_073_LOD1"
    modelType: 2
  }
}
rows {
  id: 510071
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_075_LOD1"
    bottom: "SK_TY_Under_075_LOD1"
    gloves: "SK_TY_Hands_075_LOD1"
    modelType: 2
  }
}
rows {
  id: 510072
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_077_LOD1"
    bottom: "SK_TY_Under_077_LOD1"
    gloves: "SK_TY_Hands_077_LOD1"
    modelType: 2
  }
}
rows {
  id: 510073
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_085_LOD1"
    modelType: 2
  }
}
rows {
  id: 510074
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_087_LOD1"
    modelType: 2
  }
}
rows {
  id: 510075
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_090_LOD1"
    bottom: "SK_TY_Under_090_LOD1"
    gloves: "SK_TY_Hands_090_LOD1"
    modelType: 2
  }
}
rows {
  id: 510076
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_079_LOD1"
    modelType: 2
  }
}
rows {
  id: 510077
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_081_LOD1"
    modelType: 2
  }
}
rows {
  id: 510078
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_082_LOD1"
    bottom: "SK_TY_Under_082_LOD1"
    gloves: "SK_TY_Hands_082_LOD1"
    modelType: 2
  }
}
rows {
  id: 510079
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_083_LOD1"
    bottom: "SK_TY_Under_083_LOD1"
    gloves: "SK_TY_Hands_083_LOD1"
    modelType: 2
  }
}
rows {
  id: 510080
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_084_LOD1"
    modelType: 2
  }
}
rows {
  id: 510081
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_086_LOD1"
    modelType: 2
  }
}
rows {
  id: 510082
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_088_LOD1"
    bottom: "SK_TY_Under_088_LOD1"
    modelType: 2
  }
}
rows {
  id: 510083
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_089_LOD1"
    modelType: 2
  }
}
rows {
  id: 510084
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_094_LOD1"
    modelType: 2
  }
}
rows {
  id: 510085
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_091_LOD1"
    modelType: 2
  }
}
rows {
  id: 510086
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_092_LOD1"
    modelType: 2
  }
}
rows {
  id: 510087
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_093_LOD1"
    modelType: 2
  }
}
rows {
  id: 510088
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_074_LOD1"
    bottom: "SK_TY_Under_074_LOD1"
    gloves: "SK_TY_Hands_074_LOD1"
    modelType: 2
  }
}
rows {
  id: 510089
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_078_LOD1"
    modelType: 2
  }
}
rows {
  id: 510090
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_096_LOD1"
    modelType: 2
  }
}
rows {
  id: 510091
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_097_LOD1"
    bottom: "SK_TY_Under_097_LOD1"
    modelType: 2
  }
}
rows {
  id: 510092
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_109_LOD1"
    modelType: 2
  }
}
rows {
  id: 510093
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_110_LOD1"
    modelType: 2
  }
}
rows {
  id: 510094
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_111_LOD1"
    modelType: 2
  }
}
rows {
  id: 510095
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_116_LOD1"
    modelType: 2
  }
}
rows {
  id: 510096
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_112_LOD1"
    modelType: 2
  }
}
rows {
  id: 510097
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_113_LOD1"
    bottom: "SK_TY_Under_113_LOD1"
    modelType: 2
  }
}
rows {
  id: 510098
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_114_LOD1"
    bottom: "SK_TY_Under_114_LOD1"
    modelType: 2
  }
}
rows {
  id: 510099
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_100_LOD1"
    bottom: "SK_TY_Under_100_LOD1"
    gloves: "SK_TY_Hands_100_LOD1"
    modelType: 2
  }
}
rows {
  id: 510100
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_104_LOD1"
    bottom: "SK_TY_Under_104_LOD1"
    modelType: 2
  }
}
rows {
  id: 510101
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_105_LOD1"
    modelType: 2
  }
}
rows {
  id: 510102
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_098_LOD1"
    bottom: "SK_TY_Under_098_LOD1"
    gloves: "SK_TY_Hands_098_LOD1"
    modelType: 2
  }
}
rows {
  id: 510103
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_115_LOD1"
    modelType: 2
  }
}
rows {
  id: 510104
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_101_LOD1"
    modelType: 2
  }
}
rows {
  id: 510105
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_103_LOD1"
    modelType: 2
  }
}
rows {
  id: 510106
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_117_LOD1"
    modelType: 2
  }
}
rows {
  id: 510107
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_118_LOD1"
    modelType: 2
  }
}
rows {
  id: 510108
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_131_LOD1"
    modelType: 2
  }
}
rows {
  id: 510109
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_132_LOD1"
    modelType: 2
  }
}
rows {
  id: 510110
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_102_LOD1"
    bottom: "SK_TY_Under_102_LOD1"
    modelType: 2
  }
}
rows {
  id: 510111
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_130_LOD1"
    bottom: "SK_TY_Under_130_LOD1"
    modelType: 2
  }
}
rows {
  id: 510112
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_127_LOD1"
    modelType: 2
  }
}
rows {
  id: 510113
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_099_LOD1"
    modelType: 2
  }
}
rows {
  id: 510114
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_125_LOD1"
    modelType: 2
  }
}
rows {
  id: 510115
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_128_LOD1"
    modelType: 2
  }
}
rows {
  id: 510116
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_133_LOD1"
    bottom: "SK_TY_Under_133_LOD1"
    modelType: 2
  }
}
rows {
  id: 510117
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_108_LOD1"
    bottom: "SK_TY_Under_108_LOD1"
    modelType: 2
  }
}
rows {
  id: 510118
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_124_LOD1"
    bottom: "SK_TY_Under_124_LOD1"
    modelType: 2
  }
}
rows {
  id: 510119
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_126_LOD1"
    modelType: 2
  }
}
rows {
  id: 510120
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_135_LOD1"
    modelType: 2
  }
}
rows {
  id: 510121
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_136_LOD1"
    modelType: 2
  }
}
rows {
  id: 510122
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_138_LOD1"
    modelType: 2
  }
}
rows {
  id: 510123
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_119_LOD1"
    bottom: "SK_TY_Under_119_LOD1"
    gloves: "SK_TY_Hands_119_LOD1"
    modelType: 2
  }
}
rows {
  id: 510124
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_122_LOD1"
    modelType: 2
  }
}
rows {
  id: 510125
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_121_LOD1"
    modelType: 2
  }
}
rows {
  id: 510126
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_137_LOD1"
    bottom: "SK_TY_Under_137_LOD1"
    modelType: 2
  }
}
rows {
  id: 510127
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_134_LOD1"
    bottom: "SK_TY_Under_134_LOD1"
    modelType: 2
  }
}
rows {
  id: 510128
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_139_LOD1"
    modelType: 2
  }
}
rows {
  id: 510129
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_140_LOD1"
    modelType: 2
  }
}
rows {
  id: 510130
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_141_LOD1"
    modelType: 2
  }
}
rows {
  id: 510131
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_142_LOD1"
    modelType: 2
  }
}
rows {
  id: 510132
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_143_LOD1"
    bottom: "SK_TY_Under_143_LOD1"
    modelType: 2
  }
}
rows {
  id: 510133
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_151_LOD1"
    modelType: 2
  }
}
rows {
  id: 510134
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_152_LOD1"
    modelType: 2
  }
}
rows {
  id: 510135
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_158_LOD1"
    modelType: 2
  }
}
rows {
  id: 510136
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_146_LOD1"
    modelType: 2
  }
}
rows {
  id: 510137
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_147_LOD1"
    modelType: 2
  }
}
rows {
  id: 510138
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_148_LOD1"
    modelType: 2
  }
}
rows {
  id: 510139
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_149_LOD1"
    modelType: 2
  }
}
rows {
  id: 510140
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_150_LOD1"
    bottom: "SK_TY_Under_150_LOD1"
    modelType: 2
  }
}
rows {
  id: 510141
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_153_LOD1"
    bottom: "SK_TY_Under_153_LOD1"
    modelType: 2
  }
}
rows {
  id: 510142
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_156_LOD1"
    modelType: 2
  }
}
rows {
  id: 510143
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_159_LOD1"
    modelType: 2
  }
}
rows {
  id: 510144
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_160_LOD1"
    modelType: 2
  }
}
rows {
  id: 510145
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_144_LOD1"
    modelType: 2
  }
}
rows {
  id: 510146
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_145_LOD1"
    modelType: 2
  }
}
rows {
  id: 510147
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_154_LOD1"
    modelType: 2
  }
}
rows {
  id: 510148
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_155_LOD1"
    modelType: 2
  }
}
rows {
  id: 510149
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_157_LOD1"
    modelType: 2
  }
}
rows {
  id: 510150
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_161_LOD1"
    bottom: "SK_TY_Under_161_LOD1"
    modelType: 2
  }
}
rows {
  id: 510151
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_162_LOD1"
    modelType: 2
  }
}
rows {
  id: 510152
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_189_LOD1"
    modelType: 2
  }
}
rows {
  id: 510153
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_175_LOD1"
    modelType: 2
  }
}
rows {
  id: 510154
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_176_LOD1"
    modelType: 2
  }
}
rows {
  id: 510155
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_182_LOD1"
    bottom: "SK_TY_Under_182_LOD1"
    gloves: "SK_TY_Hands_182_LOD1"
    modelType: 2
  }
}
rows {
  id: 510156
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_174_LOD1"
    modelType: 2
  }
}
rows {
  id: 510157
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_169_LOD1"
    modelType: 2
  }
}
rows {
  id: 510158
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_172_LOD1"
    modelType: 2
  }
}
rows {
  id: 510159
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_178_LOD1"
    modelType: 2
  }
}
rows {
  id: 510160
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_179_LOD1"
    modelType: 2
  }
}
rows {
  id: 510161
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_180_LOD1"
    modelType: 2
  }
}
rows {
  id: 510162
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_165_LOD1"
    bottom: "SK_TY_Under_165_LOD1"
    modelType: 2
  }
}
rows {
  id: 510163
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_168_LOD1"
    modelType: 2
  }
}
rows {
  id: 510164
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_192_LOD1"
    bottom: "SK_TY_Under_192_LOD1"
    modelType: 2
  }
}
rows {
  id: 510165
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_166_LOD1"
    modelType: 2
  }
}
rows {
  id: 510166
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_167_LOD1"
    modelType: 2
  }
}
rows {
  id: 510167
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_173_LOD1"
    modelType: 2
  }
}
rows {
  id: 510168
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_171_LOD1"
    modelType: 2
  }
}
rows {
  id: 510169
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_177_LOD1"
    bottom: "SK_TY_Under_177_LOD1"
    modelType: 2
  }
}
rows {
  id: 510170
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_181_LOD1"
    modelType: 2
  }
}
rows {
  id: 510171
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_183_LOD1"
    modelType: 2
  }
}
rows {
  id: 510172
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_185_LOD1"
    modelType: 2
  }
}
rows {
  id: 510173
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_184_LOD1"
    bottom: "SK_TY_Under_184_LOD1"
    modelType: 2
  }
}
rows {
  id: 510174
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_202_LOD1"
    modelType: 2
  }
}
rows {
  id: 510175
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_188_LOD1"
    bottom: "SK_TY_Under_188_LOD1"
    modelType: 2
  }
}
rows {
  id: 510176
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_190_LOD1"
    bottom: "SK_TY_Under_190_LOD1"
    modelType: 2
  }
}
rows {
  id: 510177
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_194_LOD1"
    bottom: "SK_TY_Under_194_LOD1"
    gloves: "SK_TY_Hands_194_LOD1"
    modelType: 2
  }
}
rows {
  id: 510178
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_209_LOD1"
    modelType: 2
  }
}
rows {
  id: 510179
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_207_LOD1"
    modelType: 2
  }
}
rows {
  id: 510180
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_208_LOD1"
    modelType: 2
  }
}
rows {
  id: 510181
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_186_LOD1"
    modelType: 2
  }
}
rows {
  id: 510182
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_205_LOD1"
    modelType: 2
  }
}
rows {
  id: 510183
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_206_LOD1"
    modelType: 2
  }
}
rows {
  id: 510184
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_204_LOD1"
    modelType: 2
  }
}
rows {
  id: 510185
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_214_LOD1"
    bottom: "SK_TY_Under_214_LOD1"
    modelType: 2
  }
}
rows {
  id: 510186
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_212_LOD1"
    bottom: "SK_TY_Under_212_LOD1"
    gloves: "SK_TY_Hands_212_LOD1"
    modelType: 2
  }
}
rows {
  id: 510187
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_211_LOD1"
    bottom: "SK_TY_Under_211_LOD1"
    modelType: 2
  }
}
rows {
  id: 510188
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_203_LOD1"
    bottom: "SK_TY_Under_203_LOD1"
    modelType: 2
  }
}
rows {
  id: 510189
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_191_LOD1"
    modelType: 2
  }
}
rows {
  id: 510190
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_187_LOD1"
    modelType: 2
  }
}
rows {
  id: 510191
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_170_LOD1"
    modelType: 2
  }
}
rows {
  id: 510192
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_195_LOD1"
    modelType: 2
  }
}
rows {
  id: 510193
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_196_LOD1"
    modelType: 2
  }
}
rows {
  id: 510194
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_197_LOD1"
    modelType: 2
  }
}
rows {
  id: 510195
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_198_LOD1"
    modelType: 2
  }
}
rows {
  id: 510196
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_199_LOD1"
    modelType: 2
  }
}
rows {
  id: 510197
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_200_LOD1"
    modelType: 2
  }
}
rows {
  id: 510198
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_201_LOD1"
    modelType: 2
  }
}
rows {
  id: 510199
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_213_LOD1"
    modelType: 2
  }
}
rows {
  id: 510200
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_215_LOD1"
    modelType: 2
  }
}
rows {
  id: 510201
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_216_LOD1"
    modelType: 2
  }
}
rows {
  id: 510202
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_217_LOD1"
    modelType: 2
  }
}
rows {
  id: 510203
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_218_LOD1"
    bottom: "SK_TY_Under_218_LOD1"
    modelType: 2
  }
}
rows {
  id: 510204
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_219_LOD1"
    modelType: 2
  }
}
rows {
  id: 510205
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_220_LOD1"
    modelType: 2
  }
}
rows {
  id: 510206
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_193_LOD1"
    modelType: 2
  }
}
rows {
  id: 510207
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_226_LOD1"
    modelType: 2
  }
}
rows {
  id: 510208
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_221_LOD1"
    modelType: 2
  }
}
rows {
  id: 510209
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_222_LOD1"
    modelType: 2
  }
}
rows {
  id: 510210
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_227_LOD1"
    bottom: "SK_TY_Under_227_LOD1"
    modelType: 2
  }
}
rows {
  id: 510211
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_228_LOD1"
    bottom: "SK_TY_Under_228_LOD1"
    modelType: 2
  }
}
rows {
  id: 510212
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_229_LOD1"
    modelType: 2
  }
}
rows {
  id: 510213
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_223_LOD1"
    modelType: 2
  }
}
rows {
  id: 510214
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_235_LOD1"
    modelType: 2
  }
}
rows {
  id: 510215
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_236_LOD1"
    modelType: 2
  }
}
rows {
  id: 510216
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_237_LOD1"
    modelType: 2
  }
}
rows {
  id: 510217
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_239_LOD1"
    bottom: "SK_TY_Under_239_LOD1"
    modelType: 2
  }
}
rows {
  id: 510218
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_240_LOD1"
    modelType: 2
  }
}
rows {
  id: 510219
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_232_LOD1"
    modelType: 2
  }
}
rows {
  id: 510220
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_247_LOD1"
    modelType: 2
  }
}
rows {
  id: 510221
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_248_LOD1"
    modelType: 2
  }
}
rows {
  id: 510222
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_238_LOD1"
    modelType: 2
  }
}
rows {
  id: 510223
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_241_LOD1"
    bottom: "SK_TY_Under_241_LOD1"
    modelType: 2
  }
}
rows {
  id: 510224
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_224_LOD1"
    bottom: "SK_TY_Under_224_LOD1"
    gloves: "SK_TY_Hands_224_LOD1"
    modelType: 2
  }
}
rows {
  id: 510225
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_164_LOD1"
    modelType: 2
  }
}
rows {
  id: 510226
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_242_LOD1"
    bottom: "SK_TY_Under_242_LOD1"
    modelType: 2
  }
}
rows {
  id: 510227
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_243_LOD1"
    modelType: 2
  }
}
rows {
  id: 510228
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_244_LOD1"
    modelType: 2
  }
}
rows {
  id: 510229
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_245_LOD1"
    bottom: "SK_TY_Under_245_LOD1"
    gloves: "SK_TY_Hands_245_LOD1"
    modelType: 2
  }
}
rows {
  id: 510230
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_230_LOD1"
    bottom: "SK_TY_Under_230_LOD1"
    modelType: 2
  }
}
rows {
  id: 510231
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_258_LOD1"
    bottom: "SK_TY_Under_258_LOD1"
    modelType: 2
  }
}
rows {
  id: 510232
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_225_LOD1"
    bottom: "SK_TY_Under_225_LOD1"
    gloves: "SK_TY_Hands_225_LOD1"
    modelType: 2
  }
}
rows {
  id: 510233
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_286_LOD1"
    modelType: 2
  }
}
rows {
  id: 510234
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_252_LOD1"
    bottom: "SK_TY_Under_252_LOD1"
    modelType: 2
  }
}
rows {
  id: 510235
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_254_LOD1"
    bottom: "SK_TY_Under_254_LOD1"
    modelType: 2
  }
}
rows {
  id: 510236
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_255_LOD1"
    modelType: 2
  }
}
rows {
  id: 510237
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_251_LOD1"
    modelType: 2
  }
}
rows {
  id: 510238
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_261_LOD1"
    modelType: 2
  }
}
rows {
  id: 510239
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_263_LOD1"
    modelType: 2
  }
}
rows {
  id: 510240
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_264_LOD1"
    modelType: 2
  }
}
rows {
  id: 510241
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_266_LOD1"
    modelType: 2
  }
}
rows {
  id: 510242
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_274_LOD1"
    modelType: 2
  }
}
rows {
  id: 510243
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_259_LOD1"
    modelType: 2
  }
}
rows {
  id: 510244
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_265_LOD1"
    modelType: 2
  }
}
rows {
  id: 510245
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_260_LOD1"
    bottom: "SK_TY_Under_260_LOD1"
    modelType: 2
  }
}
rows {
  id: 510246
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_233_LOD1"
    modelType: 2
  }
}
rows {
  id: 510247
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_210_LOD1"
    modelType: 2
  }
}
rows {
  id: 510248
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_275_LOD1"
    modelType: 2
  }
}
rows {
  id: 510249
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_276_LOD1"
    modelType: 2
  }
}
rows {
  id: 510250
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_277_LOD1"
    modelType: 2
  }
}
rows {
  id: 510251
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_278_LOD1"
    modelType: 2
  }
}
rows {
  id: 510252
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_256_LOD1"
    bottom: "SK_TY_Under_256_LOD1"
    modelType: 2
  }
}
rows {
  id: 510253
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_253_LOD1"
    bottom: "SK_TY_Under_253_LOD1"
    modelType: 2
  }
}
rows {
  id: 510254
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_268_LOD1"
    modelType: 2
  }
}
rows {
  id: 510255
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_269_LOD1"
    modelType: 2
  }
}
rows {
  id: 510256
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_270_LOD1"
    modelType: 2
  }
}
rows {
  id: 510257
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_273_LOD1"
    modelType: 2
  }
}
rows {
  id: 510258
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_304_LOD1"
    bottom: "SK_TY_Under_304_LOD1"
    modelType: 2
  }
}
rows {
  id: 510259
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_305_LOD1"
    bottom: "SK_TY_Under_305_LOD1"
    modelType: 2
  }
}
rows {
  id: 510260
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_267_LOD1"
    bottom: "SK_TY_Under_267_LOD1"
    modelType: 2
  }
}
rows {
  id: 510261
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_279_LOD1"
    bottom: "SK_TY_Under_279_LOD1"
    modelType: 2
  }
}
rows {
  id: 510262
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_280_LOD1"
    bottom: "SK_TY_Under_280_LOD1"
    modelType: 2
  }
}
rows {
  id: 510263
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_281_LOD1"
    bottom: "SK_TY_Under_281_LOD1"
    modelType: 2
  }
}
rows {
  id: 510264
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_282_LOD1"
    bottom: "SK_TY_Under_282_LOD1"
    modelType: 2
  }
}
rows {
  id: 510265
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_283_LOD1"
    bottom: "SK_TY_Under_283_LOD1"
    modelType: 2
  }
}
rows {
  id: 510266
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_271_LOD1"
    bottom: "SK_TY_Under_271_LOD1"
    modelType: 2
  }
}
rows {
  id: 510267
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_272_LOD1"
    bottom: "SK_TY_Under_272_LOD1"
    modelType: 2
  }
}
rows {
  id: 510268
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_306_LOD1"
    modelType: 2
  }
}
rows {
  id: 510269
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_262_LOD1"
    modelType: 2
  }
}
rows {
  id: 510270
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_291_LOD1"
    modelType: 2
  }
}
rows {
  id: 510271
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_292_LOD1"
    modelType: 2
  }
}
rows {
  id: 510272
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_293_LOD1"
    modelType: 2
  }
}
rows {
  id: 510273
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_294_LOD1"
    modelType: 2
  }
}
rows {
  id: 510274
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_296_LOD1"
    modelType: 2
  }
}
rows {
  id: 510275
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_303_LOD1"
    modelType: 2
  }
}
rows {
  id: 510276
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_295_LOD1"
    bottom: "SK_TY_Under_295_LOD1"
    modelType: 2
  }
}
rows {
  id: 510277
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_297_LOD1"
    modelType: 2
  }
}
rows {
  id: 510278
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_298_LOD1"
    modelType: 2
  }
}
rows {
  id: 510279
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_299_LOD1"
    modelType: 2
  }
}
rows {
  id: 510280
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_301_LOD1"
    modelType: 2
  }
}
rows {
  id: 510281
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_302_LOD1"
    modelType: 2
  }
}
rows {
  id: 510282
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_300_LOD1"
    bottom: "SK_TY_Under_300_LOD1"
    modelType: 2
  }
}
rows {
  id: 510283
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_284_LOD1"
    bottom: "SK_TY_Under_284_LOD1"
    modelType: 2
  }
}
rows {
  id: 510284
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_285_LOD1"
    bottom: "SK_TY_Under_285_LOD1"
    modelType: 2
  }
}
rows {
  id: 510285
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_257_LOD1"
    bottom: "SK_TY_Under_257_LOD1"
    gloves: "SK_TY_Hands_257_LOD1"
    modelType: 2
  }
}
rows {
  id: 510286
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_337_LOD1"
    modelType: 2
  }
}
rows {
  id: 510287
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_318_LOD1"
    bottom: "SK_TY_Under_318_LOD1"
    modelType: 2
  }
}
rows {
  id: 510288
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_319_LOD1"
    bottom: "SK_TY_Under_319_LOD1"
    modelType: 2
  }
}
rows {
  id: 510289
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_287_LOD1"
    bottom: "SK_TY_Under_287_LOD1"
    modelType: 2
  }
}
rows {
  id: 510290
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_288_LOD1"
    bottom: "SK_TY_Under_288_LOD1"
    modelType: 2
  }
}
rows {
  id: 510291
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_289_LOD1"
    bottom: "SK_TY_Under_289_LOD1"
    modelType: 2
  }
}
rows {
  id: 510292
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_290_LOD1"
    bottom: "SK_TY_Under_290_LOD1"
    modelType: 2
  }
}
rows {
  id: 510293
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_349_LOD1"
    bottom: "SK_TY_Under_349_LOD1"
    modelType: 2
  }
}
rows {
  id: 510294
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_322_LOD1"
    modelType: 2
  }
}
rows {
  id: 510295
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_323_LOD1"
    modelType: 2
  }
}
rows {
  id: 510296
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_324_LOD1"
    modelType: 2
  }
}
rows {
  id: 510297
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_328_LOD1"
    modelType: 2
  }
}
rows {
  id: 510298
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_329_LOD1"
    modelType: 2
  }
}
rows {
  id: 510299
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_330_LOD1"
    modelType: 2
  }
}
rows {
  id: 510300
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_331_LOD1"
    modelType: 2
  }
}
rows {
  id: 510301
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_332_LOD1"
    modelType: 2
  }
}
rows {
  id: 510302
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_333_LOD1"
    modelType: 2
  }
}
rows {
  id: 510303
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_325_LOD1"
    bottom: "SK_TY_Under_325_LOD1"
    modelType: 2
  }
}
rows {
  id: 510304
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_326_LOD1"
    bottom: "SK_TY_Under_326_LOD1"
    modelType: 2
  }
}
rows {
  id: 510305
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_308_LOD1"
    modelType: 2
  }
}
rows {
  id: 510306
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_309_LOD1"
    modelType: 2
  }
}
rows {
  id: 510307
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_310_LOD1"
    modelType: 2
  }
}
rows {
  id: 510308
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_311_LOD1"
    bottom: "SK_TY_Under_311_LOD1"
    modelType: 2
  }
}
rows {
  id: 510309
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_307_LOD1"
    modelType: 2
  }
}
rows {
  id: 510310
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_313_LOD1"
    modelType: 2
  }
}
rows {
  id: 510311
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_314_LOD1"
    modelType: 2
  }
}
rows {
  id: 510312
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_316_LOD1"
    modelType: 2
  }
}
rows {
  id: 510313
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_317_LOD1"
    modelType: 2
  }
}
rows {
  id: 510314
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_312_LOD1"
    bottom: "SK_TY_Under_312_LOD1"
    modelType: 2
  }
}
rows {
  id: 510315
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_315_LOD1"
    bottom: "SK_TY_Under_315_LOD1"
    modelType: 2
  }
}
rows {
  id: 510316
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_327_LOD1"
    modelType: 2
  }
}
rows {
  id: 510317
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_334_LOD1"
    modelType: 2
  }
}
rows {
  id: 510318
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_320_LOD1"
    bottom: "SK_TY_Under_320_LOD1"
    modelType: 2
  }
}
rows {
  id: 510319
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_340_LOD1"
    modelType: 2
  }
}
rows {
  id: 510320
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_341_LOD1"
    modelType: 2
  }
}
rows {
  id: 510321
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_336_LOD1"
    bottom: "SK_TY_Under_336_LOD1"
    modelType: 2
  }
}
rows {
  id: 510322
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_321_LOD1"
    modelType: 2
  }
}
rows {
  id: 510323
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_338_LOD1"
    modelType: 2
  }
}
rows {
  id: 510324
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_346_LOD1"
    bottom: "SK_TY_Under_346_LOD1"
    modelType: 2
  }
}
rows {
  id: 510325
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_362_LOD1"
    bottom: "SK_TY_Under_362_LOD1"
    gloves: "SK_TY_Hands_362_LOD1"
    modelType: 2
  }
}
rows {
  id: 510326
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_367_LOD1"
    modelType: 2
  }
}
rows {
  id: 510327
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_355_LOD1"
    bottom: "SK_TY_Under_355_LOD1"
    modelType: 2
  }
}
rows {
  id: 510328
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_347_LOD1"
    bottom: "SK_TY_Under_347_LOD1"
    modelType: 2
  }
}
rows {
  id: 510329
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_358_LOD1"
    bottom: "SK_TY_Under_358_LOD1"
    modelType: 2
  }
}
rows {
  id: 510330
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_359_LOD1"
    bottom: "SK_TY_Under_359_LOD1"
    modelType: 2
  }
}
rows {
  id: 510331
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_351_LOD1"
    modelType: 2
  }
}
rows {
  id: 510332
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_352_LOD1"
    modelType: 2
  }
}
rows {
  id: 510333
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_353_LOD1"
    modelType: 2
  }
}
rows {
  id: 510334
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_354_LOD1"
    modelType: 2
  }
}
rows {
  id: 510335
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_356_LOD1"
    modelType: 2
  }
}
rows {
  id: 510336
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_363_LOD1"
    modelType: 2
  }
}
rows {
  id: 510337
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_364_LOD1"
    modelType: 2
  }
}
rows {
  id: 510338
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_357_LOD1"
    modelType: 2
  }
}
rows {
  id: 510339
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_360_LOD1"
    modelType: 2
  }
}
rows {
  id: 510340
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_361_LOD1"
    modelType: 2
  }
}
rows {
  id: 510341
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_365_LOD1"
    modelType: 2
  }
}
rows {
  id: 510342
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_350_LOD1"
    bottom: "SK_TY_Under_350_LOD1"
    modelType: 2
  }
}
rows {
  id: 510343
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_342_LOD1"
    bottom: "SK_TY_Under_342_LOD1"
    modelType: 2
  }
}
rows {
  id: 510344
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_343_LOD1"
    bottom: "SK_TY_Under_343_LOD1"
    modelType: 2
  }
}
rows {
  id: 510345
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_344_LOD1"
    bottom: "SK_TY_Under_344_LOD1"
    modelType: 2
  }
}
rows {
  id: 510346
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_345_LOD1"
    bottom: "SK_TY_Under_345_LOD1"
    modelType: 2
  }
}
rows {
  id: 510347
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_348_LOD1"
    bottom: "SK_TY_Under_348_LOD1"
    modelType: 2
  }
}
rows {
  id: 510348
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_335_LOD1"
    modelType: 2
  }
}
rows {
  id: 510349
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_339_LOD1"
    modelType: 2
  }
}
rows {
  id: 510350
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_372_LOD1"
    modelType: 2
  }
}
rows {
  id: 510351
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_373_LOD1"
    modelType: 2
  }
}
rows {
  id: 510352
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_370_LOD1"
    modelType: 2
  }
}
rows {
  id: 510353
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_368_LOD1"
    modelType: 2
  }
}
rows {
  id: 510354
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_375_LOD1"
    modelType: 2
  }
}
rows {
  id: 510355
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_376_LOD1"
    modelType: 2
  }
}
rows {
  id: 510356
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_377_LOD1"
    modelType: 2
  }
}
rows {
  id: 510357
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_374_LOD1"
    bottom: "SK_TY_Under_374_LOD1"
    modelType: 2
  }
}
rows {
  id: 510358
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_371_LOD1"
    bottom: "SK_TY_Under_371_LOD1"
    modelType: 2
  }
}
rows {
  id: 510359
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_378_LOD1"
    bottom: "SK_TY_Under_378_LOD1"
    modelType: 2
  }
}
rows {
  id: 510360
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_366_LOD1"
    bottom: "SK_TY_Under_366_LOD1"
    modelType: 2
  }
}
rows {
  id: 510361
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_369_LOD1"
    bottom: "SK_TY_Under_369_LOD1"
    modelType: 2
  }
}
rows {
  id: 510362
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_401_LOD1"
    modelType: 2
  }
}
rows {
  id: 510363
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_394_LOD1"
    modelType: 2
  }
}
rows {
  id: 510364
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_395_LOD1"
    modelType: 2
  }
}
rows {
  id: 510365
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_396_LOD1"
    modelType: 2
  }
}
rows {
  id: 510366
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_397_LOD1"
    modelType: 2
  }
}
rows {
  id: 510367
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_380_LOD1"
    modelType: 2
  }
}
rows {
  id: 510368
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_398_LOD1"
    bottom: "SK_TY_Under_398_LOD1"
    modelType: 2
  }
}
rows {
  id: 510369
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_399_LOD1"
    bottom: "SK_TY_Under_399_LOD1"
    modelType: 2
  }
}
rows {
  id: 510370
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_403_LOD1"
    bottom: "SK_TY_Under_403_LOD1"
    modelType: 2
  }
}
rows {
  id: 510371
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_381_LOD1"
    modelType: 2
  }
}
rows {
  id: 510372
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_382_LOD1"
    modelType: 2
  }
}
rows {
  id: 510373
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_383_LOD1"
    modelType: 2
  }
}
rows {
  id: 510374
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_402_LOD1"
    bottom: "SK_TY_Under_402_LOD1"
    modelType: 2
  }
}
rows {
  id: 510375
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_405_LOD1"
    modelType: 2
  }
}
rows {
  id: 510376
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_406_LOD1"
    modelType: 2
  }
}
rows {
  id: 510377
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_404_LOD1"
    modelType: 2
  }
}
rows {
  id: 510378
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_407_LOD1"
    bottom: "SK_TY_Under_407_LOD1"
    modelType: 2
  }
}
rows {
  id: 510379
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_409_LOD1"
    bottom: "SK_TY_Under_409_LOD1"
    modelType: 2
  }
}
rows {
  id: 510380
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_410_LOD1"
    bottom: "SK_TY_Under_410_LOD1"
    modelType: 2
  }
}
rows {
  id: 510381
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_411_LOD1"
    bottom: "SK_TY_Under_411_LOD1"
    modelType: 2
  }
}
rows {
  id: 510382
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_379_LOD1"
    bottom: "SK_TY_Under_379_LOD1"
    modelType: 2
  }
}
rows {
  id: 510383
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_431_LOD1"
    modelType: 2
  }
}
rows {
  id: 510384
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_432_LOD1"
    modelType: 2
  }
}
rows {
  id: 510385
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_412_LOD1"
    bottom: "SK_TY_Under_412_LOD1"
    modelType: 2
  }
}
rows {
  id: 510386
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_414_LOD1"
    bottom: "SK_TY_Under_414_LOD1"
    modelType: 2
  }
}
rows {
  id: 510387
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_415_LOD1"
    bottom: "SK_TY_Under_415_LOD1"
    modelType: 2
  }
}
rows {
  id: 510388
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_400_LOD1"
    bottom: "SK_TY_Under_400_LOD1"
    modelType: 2
  }
}
rows {
  id: 510389
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_424_LOD1"
    bottom: "SK_TY_Under_424_LOD1"
    modelType: 2
  }
}
rows {
  id: 510390
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_425_LOD1"
    bottom: "SK_TY_Under_425_LOD1"
    modelType: 2
  }
}
rows {
  id: 510391
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_430_LOD1"
    bottom: "SK_TY_Under_430_LOD1"
    modelType: 2
  }
}
rows {
  id: 510392
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_419_LOD1"
    modelType: 2
  }
}
rows {
  id: 510393
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_420_LOD1"
    modelType: 2
  }
}
rows {
  id: 510394
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_421_LOD1"
    modelType: 2
  }
}
rows {
  id: 510395
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_422_LOD1"
    modelType: 2
  }
}
rows {
  id: 510396
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_423_LOD1"
    modelType: 2
  }
}
rows {
  id: 510397
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_416_LOD1"
    modelType: 2
  }
}
rows {
  id: 510398
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_417_LOD1"
    modelType: 2
  }
}
rows {
  id: 510399
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_408_LOD1"
    modelType: 2
  }
}
rows {
  id: 510400
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_413_LOD1"
    modelType: 2
  }
}
rows {
  id: 510401
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_433_LOD1"
    bottom: "SK_TY_Under_433_LOD1"
    modelType: 2
  }
}
rows {
  id: 510402
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Upper_231_LOD1"
    bottom: "SK_TY_Under_231_LOD1"
    modelType: 2
  }
}
rows {
  id: 518001
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "/Game/Feature/Farm/Assets/Cook/NPC/TY_384/Mesh/SK_TY_Upper_384_LOD1.SK_TY_Upper_384_LOD1"
    bottom: "/Game/Feature/Farm/Assets/Cook/NPC/TY_384/Mesh/SK_TY_Under_384_LOD1.SK_TY_Under_384_LOD1"
    modelType: 2
  }
}
rows {
  id: 518002
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "/Game/Feature/Farm/Assets/Cook/NPC/TY_385/Mesh/SK_TY_Upper_385_LOD1.SK_TY_Upper_385_LOD1"
    bottom: "/Game/Feature/Farm/Assets/Cook/NPC/TY_385/Mesh/SK_TY_Under_385_LOD1.SK_TY_Under_385_LOD1"
    modelType: 2
  }
}
rows {
  id: 518003
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "/Game/Feature/Farm/Assets/Cook/NPC/TY_386/Mesh/SK_TY_Upper_386_LOD1.SK_TY_Upper_386_LOD1"
    bottom: "/Game/Feature/Farm/Assets/Cook/NPC/TY_386/Mesh/SK_TY_Under_386_LOD1.SK_TY_Under_386_LOD1"
    modelType: 2
  }
}
rows {
  id: 518004
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "/Game/Feature/Farm/Assets/Cook/NPC/TY_387/Mesh/SK_TY_Upper_387_LOD1.SK_TY_Upper_387_LOD1"
    bottom: "/Game/Feature/Farm/Assets/Cook/NPC/TY_387/Mesh/SK_TY_Under_387_LOD1.SK_TY_Under_387_LOD1"
    modelType: 2
  }
}
rows {
  id: 518005
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "/Game/Feature/Farm/Assets/Cook/NPC/TY_388/Mesh/SK_TY_Upper_388_LOD1.SK_TY_Upper_388_LOD1"
    bottom: "/Game/Feature/Farm/Assets/Cook/NPC/TY_388/Mesh/SK_TY_Under_388_LOD1.SK_TY_Under_388_LOD1"
    modelType: 2
  }
}
rows {
  id: 518006
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "/Game/Feature/Farm/Assets/Cook/NPC/TY_389/Mesh/SK_TY_Upper_389_LOD1.SK_TY_Upper_389_LOD1"
    bottom: "/Game/Feature/Farm/Assets/Cook/NPC/TY_389/Mesh/SK_TY_Under_389_LOD1.SK_TY_Under_389_LOD1"
    modelType: 2
  }
}
rows {
  id: 518007
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "/Game/Feature/Farm/Assets/Cook/NPC/TY_390/Mesh/SK_TY_Upper_390_LOD1.SK_TY_Upper_390_LOD1"
    bottom: "/Game/Feature/Farm/Assets/Cook/NPC/TY_390/Mesh/SK_TY_Under_390_LOD1.SK_TY_Under_390_LOD1"
    modelType: 2
  }
}
rows {
  id: 518008
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "/Game/Feature/Farm/Assets/Cook/NPC/TY_391/Mesh/SK_TY_Upper_391_LOD1.SK_TY_Upper_391_LOD1"
    bottom: "/Game/Feature/Farm/Assets/Cook/NPC/TY_391/Mesh/SK_TY_Under_391_LOD1.SK_TY_Under_391_LOD1"
    modelType: 2
  }
}
rows {
  id: 518009
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "/Game/Feature/Farm/Assets/Cook/NPC/TY_392/Mesh/SK_TY_Upper_392_LOD1.SK_TY_Upper_392_LOD1"
    bottom: "/Game/Feature/Farm/Assets/Cook/NPC/TY_392/Mesh/SK_TY_Under_392_LOD1.SK_TY_Under_392_LOD1"
    modelType: 2
  }
}
rows {
  id: 518010
  type: ItemType_UpperGarment
  quality: 4
  resourceConf {
    model: "/Game/Feature/Farm/Assets/Cook/NPC/TY_393/Mesh/SK_TY_Upper_393_LOD1.SK_TY_Upper_393_LOD1"
    bottom: "/Game/Feature/Farm/Assets/Cook/NPC/TY_393/Mesh/SK_TY_Under_393_LOD1.SK_TY_Under_393_LOD1"
    modelType: 2
  }
}
rows {
  id: 520001
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_001_LOD1"
    modelType: 2
  }
}
rows {
  id: 520004
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_004_LOD1"
    modelType: 2
  }
}
rows {
  id: 520005
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_005_LOD1"
    modelType: 2
  }
}
rows {
  id: 520006
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_022_LOD1"
    modelType: 2
  }
}
rows {
  id: 520007
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_023_LOD1"
    modelType: 2
  }
}
rows {
  id: 520008
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_024_LOD1"
    modelType: 2
  }
}
rows {
  id: 520009
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_002_LOD1"
    modelType: 2
  }
}
rows {
  id: 520010
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_007_LOD1"
    modelType: 2
  }
}
rows {
  id: 520011
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_010_LOD1"
    modelType: 2
  }
}
rows {
  id: 520012
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_021_LOD1"
    modelType: 2
  }
}
rows {
  id: 520013
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_019_LOD1"
    modelType: 2
  }
}
rows {
  id: 520014
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_028_LOD1"
    modelType: 2
  }
}
rows {
  id: 520015
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_029_LOD1"
    modelType: 2
  }
}
rows {
  id: 520016
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_014_LOD1"
    modelType: 2
  }
}
rows {
  id: 520017
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_016_LOD1"
    modelType: 2
  }
}
rows {
  id: 520018
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_017_LOD1"
    modelType: 2
  }
}
rows {
  id: 520019
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_018_LOD1"
    modelType: 2
  }
}
rows {
  id: 520020
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_003_LOD1"
    modelType: 2
  }
}
rows {
  id: 520021
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_015_LOD1"
    modelType: 2
  }
}
rows {
  id: 520022
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_009_LOD1"
    modelType: 2
  }
}
rows {
  id: 520023
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_011_LOD1"
    modelType: 2
  }
}
rows {
  id: 520024
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_031_LOD1"
    modelType: 2
  }
}
rows {
  id: 520025
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_030_LOD1"
    modelType: 2
  }
}
rows {
  id: 520026
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_038_LOD1"
    modelType: 2
  }
}
rows {
  id: 520027
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_041_LOD1"
    modelType: 2
  }
}
rows {
  id: 520028
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_043_LOD1"
    modelType: 2
  }
}
rows {
  id: 520029
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_045_LOD1"
    modelType: 2
  }
}
rows {
  id: 520030
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_049_LOD1"
    modelType: 2
  }
}
rows {
  id: 520031
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_051_LOD1"
    modelType: 2
  }
}
rows {
  id: 520032
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_052_LOD1"
    modelType: 2
  }
}
rows {
  id: 520033
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_040_LOD1"
    modelType: 2
  }
}
rows {
  id: 520034
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_047_LOD1"
    modelType: 2
  }
}
rows {
  id: 520035
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_050_LOD1"
    modelType: 2
  }
}
rows {
  id: 520036
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_042_LOD1"
    modelType: 2
  }
}
rows {
  id: 520037
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_048_LOD1"
    modelType: 2
  }
}
rows {
  id: 520038
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_039_LOD1"
    modelType: 2
  }
}
rows {
  id: 520039
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_044_LOD1"
    modelType: 2
  }
}
rows {
  id: 520040
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_046_LOD1"
    modelType: 2
  }
}
rows {
  id: 520041
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_061_LOD1"
    modelType: 2
  }
}
rows {
  id: 520042
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_062_LOD1"
    modelType: 2
  }
}
rows {
  id: 520043
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_069_LOD1"
    modelType: 2
  }
}
rows {
  id: 520044
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_070_LOD1"
    modelType: 2
  }
}
rows {
  id: 520045
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_071_LOD1"
    modelType: 2
  }
}
rows {
  id: 520046
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_072_LOD1"
    modelType: 2
  }
}
rows {
  id: 520047
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_063_LOD1"
    modelType: 2
  }
}
rows {
  id: 520048
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_085_LOD1"
    modelType: 2
  }
}
rows {
  id: 520049
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_087_LOD1"
    modelType: 2
  }
}
rows {
  id: 520050
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_079_LOD1"
    modelType: 2
  }
}
rows {
  id: 520051
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_081_LOD1"
    modelType: 2
  }
}
rows {
  id: 520052
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_084_LOD1"
    modelType: 2
  }
}
rows {
  id: 520053
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_086_LOD1"
    modelType: 2
  }
}
rows {
  id: 520054
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_089_LOD1"
    modelType: 2
  }
}
rows {
  id: 520055
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_094_LOD1"
    modelType: 2
  }
}
rows {
  id: 520056
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_093_LOD1"
    modelType: 2
  }
}
rows {
  id: 520057
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_091_LOD1"
    modelType: 2
  }
}
rows {
  id: 520058
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_092_LOD1"
    modelType: 2
  }
}
rows {
  id: 520059
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_078_LOD1"
    modelType: 2
  }
}
rows {
  id: 520060
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_096_LOD1"
    modelType: 2
  }
}
rows {
  id: 520061
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_109_LOD1"
    modelType: 2
  }
}
rows {
  id: 520062
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_110_LOD1"
    modelType: 2
  }
}
rows {
  id: 520063
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_111_LOD1"
    modelType: 2
  }
}
rows {
  id: 520064
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_116_LOD1"
    modelType: 2
  }
}
rows {
  id: 520065
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_112_LOD1"
    modelType: 2
  }
}
rows {
  id: 520066
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_101_LOD1"
    modelType: 2
  }
}
rows {
  id: 520067
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_115_LOD1"
    modelType: 2
  }
}
rows {
  id: 520068
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_117_LOD1"
    modelType: 2
  }
}
rows {
  id: 520069
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_118_LOD1"
    modelType: 2
  }
}
rows {
  id: 520070
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_131_LOD1"
    modelType: 2
  }
}
rows {
  id: 520071
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_132_LOD1"
    modelType: 2
  }
}
rows {
  id: 520072
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_127_LOD1"
    modelType: 2
  }
}
rows {
  id: 520073
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_099_LOD1"
    modelType: 2
  }
}
rows {
  id: 520074
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_125_LOD1"
    modelType: 2
  }
}
rows {
  id: 520075
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_105_LOD1"
    modelType: 2
  }
}
rows {
  id: 520076
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_103_LOD1"
    modelType: 2
  }
}
rows {
  id: 520077
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_128_LOD1"
    modelType: 2
  }
}
rows {
  id: 520078
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_126_LOD1"
    modelType: 2
  }
}
rows {
  id: 520079
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_135_LOD1"
    modelType: 2
  }
}
rows {
  id: 520080
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_136_LOD1"
    modelType: 2
  }
}
rows {
  id: 520081
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_138_LOD1"
    modelType: 2
  }
}
rows {
  id: 520082
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_122_LOD1"
    modelType: 2
  }
}
rows {
  id: 520083
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_121_LOD1"
    modelType: 2
  }
}
rows {
  id: 520084
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_139_LOD1"
    modelType: 2
  }
}
rows {
  id: 520085
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_140_LOD1"
    modelType: 2
  }
}
rows {
  id: 520086
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_141_LOD1"
    modelType: 2
  }
}
rows {
  id: 520087
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_142_LOD1"
    modelType: 2
  }
}
rows {
  id: 520088
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_151_LOD1"
    modelType: 2
  }
}
rows {
  id: 520089
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_152_LOD1"
    modelType: 2
  }
}
rows {
  id: 520090
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_158_LOD1"
    modelType: 2
  }
}
rows {
  id: 520091
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_146_LOD1"
    modelType: 2
  }
}
rows {
  id: 520092
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_147_LOD1"
    modelType: 2
  }
}
rows {
  id: 520093
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_148_LOD1"
    modelType: 2
  }
}
rows {
  id: 520094
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_149_LOD1"
    modelType: 2
  }
}
rows {
  id: 520095
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_156_LOD1"
    modelType: 2
  }
}
rows {
  id: 520096
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_159_LOD1"
    modelType: 2
  }
}
rows {
  id: 520097
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_160_LOD1"
    modelType: 2
  }
}
rows {
  id: 520098
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_144_LOD1"
    modelType: 2
  }
}
rows {
  id: 520099
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_145_LOD1"
    modelType: 2
  }
}
rows {
  id: 520100
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_154_LOD1"
    modelType: 2
  }
}
rows {
  id: 520101
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_155_LOD1"
    modelType: 2
  }
}
rows {
  id: 520102
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_157_LOD1"
    modelType: 2
  }
}
rows {
  id: 520103
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_162_LOD1"
    modelType: 2
  }
}
rows {
  id: 520104
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_189_LOD1"
    modelType: 2
  }
}
rows {
  id: 520105
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_175_LOD1"
    modelType: 2
  }
}
rows {
  id: 520106
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_176_LOD1"
    modelType: 2
  }
}
rows {
  id: 520107
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_174_LOD1"
    modelType: 2
  }
}
rows {
  id: 520108
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_169_LOD1"
    modelType: 2
  }
}
rows {
  id: 520109
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_172_LOD1"
    modelType: 2
  }
}
rows {
  id: 520110
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_178_LOD1"
    modelType: 2
  }
}
rows {
  id: 520111
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_179_LOD1"
    modelType: 2
  }
}
rows {
  id: 520112
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_180_LOD1"
    modelType: 2
  }
}
rows {
  id: 520113
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_168_LOD1"
    modelType: 2
  }
}
rows {
  id: 520114
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_166_LOD1"
    modelType: 2
  }
}
rows {
  id: 520115
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_167_LOD1"
    modelType: 2
  }
}
rows {
  id: 520116
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_173_LOD1"
    modelType: 2
  }
}
rows {
  id: 520117
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_171_LOD1"
    modelType: 2
  }
}
rows {
  id: 520118
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_181_LOD1"
    modelType: 2
  }
}
rows {
  id: 520119
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_183_LOD1"
    modelType: 2
  }
}
rows {
  id: 520120
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_185_LOD1"
    modelType: 2
  }
}
rows {
  id: 520121
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_202_LOD1"
    modelType: 2
  }
}
rows {
  id: 520122
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_207_LOD1"
    modelType: 2
  }
}
rows {
  id: 520123
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_208_LOD1"
    modelType: 2
  }
}
rows {
  id: 520124
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_186_LOD1"
    modelType: 2
  }
}
rows {
  id: 520125
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_205_LOD1"
    modelType: 2
  }
}
rows {
  id: 520126
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_206_LOD1"
    modelType: 2
  }
}
rows {
  id: 520127
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_204_LOD1"
    modelType: 2
  }
}
rows {
  id: 520128
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_191_LOD1"
    modelType: 2
  }
}
rows {
  id: 520129
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_187_LOD1"
    modelType: 2
  }
}
rows {
  id: 520130
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_170_LOD1"
    modelType: 2
  }
}
rows {
  id: 520131
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_195_LOD1"
    modelType: 2
  }
}
rows {
  id: 520132
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_196_LOD1"
    modelType: 2
  }
}
rows {
  id: 520133
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_197_LOD1"
    modelType: 2
  }
}
rows {
  id: 520134
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_198_LOD1"
    modelType: 2
  }
}
rows {
  id: 520135
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_199_LOD1"
    modelType: 2
  }
}
rows {
  id: 520136
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_200_LOD1"
    modelType: 2
  }
}
rows {
  id: 520137
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_201_LOD1"
    modelType: 2
  }
}
rows {
  id: 520138
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_213_LOD1"
    modelType: 2
  }
}
rows {
  id: 520139
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_215_LOD1"
    modelType: 2
  }
}
rows {
  id: 520140
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_216_LOD1"
    modelType: 2
  }
}
rows {
  id: 520141
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_217_LOD1"
    modelType: 2
  }
}
rows {
  id: 520142
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_219_LOD1"
    modelType: 2
  }
}
rows {
  id: 520143
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_220_LOD1"
    modelType: 2
  }
}
rows {
  id: 520144
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_209_LOD1"
    modelType: 2
  }
}
rows {
  id: 520145
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_193_LOD1"
    modelType: 2
  }
}
rows {
  id: 520146
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_226_LOD1"
    modelType: 2
  }
}
rows {
  id: 520147
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_221_LOD1"
    modelType: 2
  }
}
rows {
  id: 520148
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_222_LOD1"
    modelType: 2
  }
}
rows {
  id: 520149
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_229_LOD1"
    modelType: 2
  }
}
rows {
  id: 520150
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_223_LOD1"
    modelType: 2
  }
}
rows {
  id: 520151
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_235_LOD1"
    modelType: 2
  }
}
rows {
  id: 520152
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_236_LOD1"
    modelType: 2
  }
}
rows {
  id: 520153
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_237_LOD1"
    modelType: 2
  }
}
rows {
  id: 520154
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_240_LOD1"
    modelType: 2
  }
}
rows {
  id: 520155
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_232_LOD1"
    modelType: 2
  }
}
rows {
  id: 520156
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_247_LOD1"
    modelType: 2
  }
}
rows {
  id: 520157
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_248_LOD1"
    modelType: 2
  }
}
rows {
  id: 520158
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_238_LOD1"
    modelType: 2
  }
}
rows {
  id: 520159
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_164_LOD1"
    modelType: 2
  }
}
rows {
  id: 520160
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_243_LOD1"
    modelType: 2
  }
}
rows {
  id: 520161
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_244_LOD1"
    modelType: 2
  }
}
rows {
  id: 520162
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_286_LOD1"
    modelType: 2
  }
}
rows {
  id: 520163
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_255_LOD1"
    modelType: 2
  }
}
rows {
  id: 520164
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_251_LOD1"
    modelType: 2
  }
}
rows {
  id: 520165
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_261_LOD1"
    modelType: 2
  }
}
rows {
  id: 520166
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_263_LOD1"
    modelType: 2
  }
}
rows {
  id: 520167
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_264_LOD1"
    modelType: 2
  }
}
rows {
  id: 520168
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_266_LOD1"
    modelType: 2
  }
}
rows {
  id: 520169
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_274_LOD1"
    modelType: 2
  }
}
rows {
  id: 520170
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_259_LOD1"
    modelType: 2
  }
}
rows {
  id: 520171
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_265_LOD1"
    modelType: 2
  }
}
rows {
  id: 520172
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_233_LOD1"
    modelType: 2
  }
}
rows {
  id: 520173
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_210_LOD1"
    modelType: 2
  }
}
rows {
  id: 520174
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_275_LOD1"
    modelType: 2
  }
}
rows {
  id: 520175
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_276_LOD1"
    modelType: 2
  }
}
rows {
  id: 520176
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_277_LOD1"
    modelType: 2
  }
}
rows {
  id: 520177
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_278_LOD1"
    modelType: 2
  }
}
rows {
  id: 520178
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_268_LOD1"
    modelType: 2
  }
}
rows {
  id: 520179
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_269_LOD1"
    modelType: 2
  }
}
rows {
  id: 520180
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_270_LOD1"
    modelType: 2
  }
}
rows {
  id: 520181
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_273_LOD1"
    modelType: 2
  }
}
rows {
  id: 520182
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_306_LOD1"
    modelType: 2
  }
}
rows {
  id: 520183
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_262_LOD1"
    modelType: 2
  }
}
rows {
  id: 520184
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_291_LOD1"
    modelType: 2
  }
}
rows {
  id: 520185
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_292_LOD1"
    modelType: 2
  }
}
rows {
  id: 520186
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_293_LOD1"
    modelType: 2
  }
}
rows {
  id: 520187
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_294_LOD1"
    modelType: 2
  }
}
rows {
  id: 520188
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_296_LOD1"
    modelType: 2
  }
}
rows {
  id: 520189
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_303_LOD1"
    modelType: 2
  }
}
rows {
  id: 520190
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_297_LOD1"
    modelType: 2
  }
}
rows {
  id: 520191
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_298_LOD1"
    modelType: 2
  }
}
rows {
  id: 520192
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_299_LOD1"
    modelType: 2
  }
}
rows {
  id: 520193
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_301_LOD1"
    modelType: 2
  }
}
rows {
  id: 520194
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_302_LOD1"
    modelType: 2
  }
}
rows {
  id: 520195
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_337_LOD1"
    modelType: 2
  }
}
rows {
  id: 520196
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_322_LOD1"
    modelType: 2
  }
}
rows {
  id: 520197
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_323_LOD1"
    modelType: 2
  }
}
rows {
  id: 520198
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_324_LOD1"
    modelType: 2
  }
}
rows {
  id: 520199
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_328_LOD1"
    modelType: 2
  }
}
rows {
  id: 520200
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_329_LOD1"
    modelType: 2
  }
}
rows {
  id: 520201
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_330_LOD1"
    modelType: 2
  }
}
rows {
  id: 520202
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_331_LOD1"
    modelType: 2
  }
}
rows {
  id: 520203
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_332_LOD1"
    modelType: 2
  }
}
rows {
  id: 520204
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_333_LOD1"
    modelType: 2
  }
}
rows {
  id: 520205
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_308_LOD1"
    modelType: 2
  }
}
rows {
  id: 520206
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_309_LOD1"
    modelType: 2
  }
}
rows {
  id: 520207
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_310_LOD1"
    modelType: 2
  }
}
rows {
  id: 520208
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_307_LOD1"
    modelType: 2
  }
}
rows {
  id: 520209
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_313_LOD1"
    modelType: 2
  }
}
rows {
  id: 520210
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_314_LOD1"
    modelType: 2
  }
}
rows {
  id: 520211
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_316_LOD1"
    modelType: 2
  }
}
rows {
  id: 520212
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_317_LOD1"
    modelType: 2
  }
}
rows {
  id: 520213
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_327_LOD1"
    modelType: 2
  }
}
rows {
  id: 520214
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_334_LOD1"
    modelType: 2
  }
}
rows {
  id: 520215
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_340_LOD1"
    modelType: 2
  }
}
rows {
  id: 520216
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_341_LOD1"
    modelType: 2
  }
}
rows {
  id: 520217
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_321_LOD1"
    modelType: 2
  }
}
rows {
  id: 520218
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_338_LOD1"
    modelType: 2
  }
}
rows {
  id: 520219
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_367_LOD1"
    modelType: 2
  }
}
rows {
  id: 520220
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_351_LOD1"
    modelType: 2
  }
}
rows {
  id: 520221
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_352_LOD1"
    modelType: 2
  }
}
rows {
  id: 520222
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_353_LOD1"
    modelType: 2
  }
}
rows {
  id: 520223
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_354_LOD1"
    modelType: 2
  }
}
rows {
  id: 520224
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_356_LOD1"
    modelType: 2
  }
}
rows {
  id: 520225
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_363_LOD1"
    modelType: 2
  }
}
rows {
  id: 520226
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_364_LOD1"
    modelType: 2
  }
}
rows {
  id: 520227
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_357_LOD1"
    modelType: 2
  }
}
rows {
  id: 520228
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_360_LOD1"
    modelType: 2
  }
}
rows {
  id: 520229
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_361_LOD1"
    modelType: 2
  }
}
rows {
  id: 520230
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_365_LOD1"
    modelType: 2
  }
}
rows {
  id: 520231
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_335_LOD1"
    modelType: 2
  }
}
rows {
  id: 520232
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_339_LOD1"
    modelType: 2
  }
}
rows {
  id: 520233
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_372_LOD1"
    modelType: 2
  }
}
rows {
  id: 520234
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_373_LOD1"
    modelType: 2
  }
}
rows {
  id: 520235
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_370_LOD1"
    modelType: 2
  }
}
rows {
  id: 520236
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_368_LOD1"
    modelType: 2
  }
}
rows {
  id: 520237
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_375_LOD1"
    modelType: 2
  }
}
rows {
  id: 520238
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_376_LOD1"
    modelType: 2
  }
}
rows {
  id: 520239
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_377_LOD1"
    modelType: 2
  }
}
rows {
  id: 520240
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_401_LOD1"
    modelType: 2
  }
}
rows {
  id: 520241
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_394_LOD1"
    modelType: 2
  }
}
rows {
  id: 520242
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_395_LOD1"
    modelType: 2
  }
}
rows {
  id: 520243
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_396_LOD1"
    modelType: 2
  }
}
rows {
  id: 520244
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_397_LOD1"
    modelType: 2
  }
}
rows {
  id: 520245
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_380_LOD1"
    modelType: 2
  }
}
rows {
  id: 520246
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_381_LOD1"
    modelType: 2
  }
}
rows {
  id: 520247
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_382_LOD1"
    modelType: 2
  }
}
rows {
  id: 520248
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_383_LOD1"
    modelType: 2
  }
}
rows {
  id: 520249
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_405_LOD1"
    modelType: 2
  }
}
rows {
  id: 520250
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_406_LOD1"
    modelType: 2
  }
}
rows {
  id: 520251
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_404_LOD1"
    modelType: 2
  }
}
rows {
  id: 520252
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_432_LOD1"
    modelType: 2
  }
}
rows {
  id: 520253
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_419_LOD1"
    modelType: 2
  }
}
rows {
  id: 520254
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_420_LOD1"
    modelType: 2
  }
}
rows {
  id: 520255
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_421_LOD1"
    modelType: 2
  }
}
rows {
  id: 520256
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_422_LOD1"
    modelType: 2
  }
}
rows {
  id: 520257
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_423_LOD1"
    modelType: 2
  }
}
rows {
  id: 520258
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_416_LOD1"
    modelType: 2
  }
}
rows {
  id: 520259
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_417_LOD1"
    modelType: 2
  }
}
rows {
  id: 520260
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_408_LOD1"
    modelType: 2
  }
}
rows {
  id: 520261
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_413_LOD1"
    modelType: 2
  }
}
rows {
  id: 520262
  type: ItemType_LowerGarment
  quality: 4
  resourceConf {
    model: "SK_TY_Under_431_LOD1"
    modelType: 2
  }
}
rows {
  id: 530001
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_001_LOD1"
    modelType: 2
  }
}
rows {
  id: 530002
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_002_LOD1"
    modelType: 2
  }
}
rows {
  id: 530003
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_019_LOD1"
    modelType: 2
  }
}
rows {
  id: 530004
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_011_LOD1"
    modelType: 2
  }
}
rows {
  id: 530005
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_003_LOD1"
    modelType: 2
  }
}
rows {
  id: 530006
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_004_LOD1"
    modelType: 2
  }
}
rows {
  id: 530007
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_005_LOD1"
    modelType: 2
  }
}
rows {
  id: 530008
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_007_LOD1"
    modelType: 2
  }
}
rows {
  id: 530009
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_009_LOD1"
    modelType: 2
  }
}
rows {
  id: 530010
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_014_LOD1"
    modelType: 2
  }
}
rows {
  id: 530011
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_015_LOD1"
    modelType: 2
  }
}
rows {
  id: 530012
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_016_LOD1"
    modelType: 2
  }
}
rows {
  id: 530013
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_018_LOD1"
    modelType: 2
  }
}
rows {
  id: 530014
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_021_LOD1"
    modelType: 2
  }
}
rows {
  id: 530015
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_024_LOD1"
    modelType: 2
  }
}
rows {
  id: 530016
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_028_LOD1"
    modelType: 2
  }
}
rows {
  id: 530017
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_029_LOD1"
    modelType: 2
  }
}
rows {
  id: 530018
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_030_LOD1"
    modelType: 2
  }
}
rows {
  id: 530019
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_031_LOD1"
    modelType: 2
  }
}
rows {
  id: 530020
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_061_LOD1"
    modelType: 2
  }
}
rows {
  id: 530021
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_062_LOD1"
    modelType: 2
  }
}
rows {
  id: 530022
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_069_LOD1"
    modelType: 2
  }
}
rows {
  id: 530023
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_070_LOD1"
    modelType: 2
  }
}
rows {
  id: 530024
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_071_LOD1"
    modelType: 2
  }
}
rows {
  id: 530025
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_072_LOD1"
    modelType: 2
  }
}
rows {
  id: 530026
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_063_LOD1"
    modelType: 2
  }
}
rows {
  id: 530027
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_085_LOD1"
    modelType: 2
  }
}
rows {
  id: 530028
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_087_LOD1"
    modelType: 2
  }
}
rows {
  id: 530029
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_079_LOD1"
    modelType: 2
  }
}
rows {
  id: 530030
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_081_LOD1"
    modelType: 2
  }
}
rows {
  id: 530031
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_084_LOD1"
    modelType: 2
  }
}
rows {
  id: 530032
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_086_LOD1"
    modelType: 2
  }
}
rows {
  id: 530033
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_089_LOD1"
    modelType: 2
  }
}
rows {
  id: 530034
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_094_LOD1"
    modelType: 2
  }
}
rows {
  id: 530035
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_093_LOD1"
    modelType: 2
  }
}
rows {
  id: 530036
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_091_LOD1"
    modelType: 2
  }
}
rows {
  id: 530037
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_092_LOD1"
    modelType: 2
  }
}
rows {
  id: 530038
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_078_LOD1"
    modelType: 2
  }
}
rows {
  id: 530039
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_096_LOD1"
    modelType: 2
  }
}
rows {
  id: 530040
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_088_LOD1"
    modelType: 2
  }
}
rows {
  id: 530041
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_109_LOD1"
    modelType: 2
  }
}
rows {
  id: 530042
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_110_LOD1"
    modelType: 2
  }
}
rows {
  id: 530043
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_111_LOD1"
    modelType: 2
  }
}
rows {
  id: 530044
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_116_LOD1"
    modelType: 2
  }
}
rows {
  id: 530045
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_112_LOD1"
    modelType: 2
  }
}
rows {
  id: 530046
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_101_LOD1"
    modelType: 2
  }
}
rows {
  id: 530047
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_115_LOD1"
    modelType: 2
  }
}
rows {
  id: 530048
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_117_LOD1"
    modelType: 2
  }
}
rows {
  id: 530049
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_118_LOD1"
    modelType: 2
  }
}
rows {
  id: 530050
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_131_LOD1"
    modelType: 2
  }
}
rows {
  id: 530051
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_132_LOD1"
    modelType: 2
  }
}
rows {
  id: 530052
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_127_LOD1"
    modelType: 2
  }
}
rows {
  id: 530053
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_099_LOD1"
    modelType: 2
  }
}
rows {
  id: 530054
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_125_LOD1"
    modelType: 2
  }
}
rows {
  id: 530055
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_128_LOD1"
    modelType: 2
  }
}
rows {
  id: 530056
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_126_LOD1"
    modelType: 2
  }
}
rows {
  id: 530057
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_135_LOD1"
    modelType: 2
  }
}
rows {
  id: 530058
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_136_LOD1"
    modelType: 2
  }
}
rows {
  id: 530059
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_138_LOD1"
    modelType: 2
  }
}
rows {
  id: 530060
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_122_LOD1"
    modelType: 2
  }
}
rows {
  id: 530061
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_121_LOD1"
    modelType: 2
  }
}
rows {
  id: 530062
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_139_LOD1"
    modelType: 2
  }
}
rows {
  id: 530063
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_140_LOD1"
    modelType: 2
  }
}
rows {
  id: 530064
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_141_LOD1"
    modelType: 2
  }
}
rows {
  id: 530065
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_142_LOD1"
    modelType: 2
  }
}
rows {
  id: 530066
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_151_LOD1"
    modelType: 2
  }
}
rows {
  id: 530067
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_152_LOD1"
    modelType: 2
  }
}
rows {
  id: 530068
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_158_LOD1"
    modelType: 2
  }
}
rows {
  id: 530069
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_146_LOD1"
    modelType: 2
  }
}
rows {
  id: 530070
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_147_LOD1"
    modelType: 2
  }
}
rows {
  id: 530071
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_148_LOD1"
    modelType: 2
  }
}
rows {
  id: 530072
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_149_LOD1"
    modelType: 2
  }
}
rows {
  id: 530073
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_156_LOD1"
    modelType: 2
  }
}
rows {
  id: 530074
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_144_LOD1"
    modelType: 2
  }
}
rows {
  id: 530075
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_145_LOD1"
    modelType: 2
  }
}
rows {
  id: 530076
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_154_LOD1"
    modelType: 2
  }
}
rows {
  id: 530077
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_155_LOD1"
    modelType: 2
  }
}
rows {
  id: 530078
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_157_LOD1"
    modelType: 2
  }
}
rows {
  id: 530079
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_162_LOD1"
    modelType: 2
  }
}
rows {
  id: 530080
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_161_LOD1"
    modelType: 2
  }
}
rows {
  id: 530081
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_189_LOD1"
    modelType: 2
  }
}
rows {
  id: 530082
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_175_LOD1"
    modelType: 2
  }
}
rows {
  id: 530083
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_176_LOD1"
    modelType: 2
  }
}
rows {
  id: 530084
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_174_LOD1"
    modelType: 2
  }
}
rows {
  id: 530085
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_169_LOD1"
    modelType: 2
  }
}
rows {
  id: 530086
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_172_LOD1"
    modelType: 2
  }
}
rows {
  id: 530087
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_178_LOD1"
    modelType: 2
  }
}
rows {
  id: 530088
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_179_LOD1"
    modelType: 2
  }
}
rows {
  id: 530089
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_180_LOD1"
    modelType: 2
  }
}
rows {
  id: 530090
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_168_LOD1"
    modelType: 2
  }
}
rows {
  id: 530091
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_166_LOD1"
    modelType: 2
  }
}
rows {
  id: 530092
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_167_LOD1"
    modelType: 2
  }
}
rows {
  id: 530093
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_173_LOD1"
    modelType: 2
  }
}
rows {
  id: 530094
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_171_LOD1"
    modelType: 2
  }
}
rows {
  id: 530095
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_181_LOD1"
    modelType: 2
  }
}
rows {
  id: 530096
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_183_LOD1"
    modelType: 2
  }
}
rows {
  id: 530097
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_185_LOD1"
    modelType: 2
  }
}
rows {
  id: 530098
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_202_LOD1"
    modelType: 2
  }
}
rows {
  id: 530099
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_207_LOD1"
    modelType: 2
  }
}
rows {
  id: 530100
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_208_LOD1"
    modelType: 2
  }
}
rows {
  id: 530101
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_186_LOD1"
    modelType: 2
  }
}
rows {
  id: 530102
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_205_LOD1"
    modelType: 2
  }
}
rows {
  id: 530103
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_206_LOD1"
    modelType: 2
  }
}
rows {
  id: 530104
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_204_LOD1"
    modelType: 2
  }
}
rows {
  id: 530105
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_191_LOD1"
    modelType: 2
  }
}
rows {
  id: 530106
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_187_LOD1"
    modelType: 2
  }
}
rows {
  id: 530107
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_170_LOD1"
    modelType: 2
  }
}
rows {
  id: 530108
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_195_LOD1"
    modelType: 2
  }
}
rows {
  id: 530109
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_196_LOD1"
    modelType: 2
  }
}
rows {
  id: 530110
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_197_LOD1"
    modelType: 2
  }
}
rows {
  id: 530111
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_198_LOD1"
    modelType: 2
  }
}
rows {
  id: 530112
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_199_LOD1"
    modelType: 2
  }
}
rows {
  id: 530113
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_200_LOD1"
    modelType: 2
  }
}
rows {
  id: 530114
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_201_LOD1"
    modelType: 2
  }
}
rows {
  id: 530115
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_213_LOD1"
    modelType: 2
  }
}
rows {
  id: 530116
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_215_LOD1"
    modelType: 2
  }
}
rows {
  id: 530117
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_216_LOD1"
    modelType: 2
  }
}
rows {
  id: 530118
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_217_LOD1"
    modelType: 2
  }
}
rows {
  id: 530119
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_219_LOD1"
    modelType: 2
  }
}
rows {
  id: 530120
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_220_LOD1"
    modelType: 2
  }
}
rows {
  id: 530121
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_209_LOD1"
    modelType: 2
  }
}
rows {
  id: 530122
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_193_LOD1"
    modelType: 2
  }
}
rows {
  id: 530123
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_226_LOD1"
    modelType: 2
  }
}
rows {
  id: 530124
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_221_LOD1"
    modelType: 2
  }
}
rows {
  id: 530125
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_222_LOD1"
    modelType: 2
  }
}
rows {
  id: 530126
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_223_LOD1"
    modelType: 2
  }
}
rows {
  id: 530127
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_235_LOD1"
    modelType: 2
  }
}
rows {
  id: 530128
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_236_LOD1"
    modelType: 2
  }
}
rows {
  id: 530129
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_237_LOD1"
    modelType: 2
  }
}
rows {
  id: 530130
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_240_LOD1"
    modelType: 2
  }
}
rows {
  id: 530131
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_232_LOD1"
    modelType: 2
  }
}
rows {
  id: 530132
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_247_LOD1"
    modelType: 2
  }
}
rows {
  id: 530133
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_248_LOD1"
    modelType: 2
  }
}
rows {
  id: 530134
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_238_LOD1"
    modelType: 2
  }
}
rows {
  id: 530135
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_164_LOD1"
    modelType: 2
  }
}
rows {
  id: 530136
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_243_LOD1"
    modelType: 2
  }
}
rows {
  id: 530137
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_244_LOD1"
    modelType: 2
  }
}
rows {
  id: 530138
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_286_LOD1"
    modelType: 2
  }
}
rows {
  id: 530139
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_255_LOD1"
    modelType: 2
  }
}
rows {
  id: 530140
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_251_LOD1"
    modelType: 2
  }
}
rows {
  id: 530141
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_261_LOD1"
    modelType: 2
  }
}
rows {
  id: 530142
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_263_LOD1"
    modelType: 2
  }
}
rows {
  id: 530143
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_264_LOD1"
    modelType: 2
  }
}
rows {
  id: 530144
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_266_LOD1"
    modelType: 2
  }
}
rows {
  id: 530145
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_274_LOD1"
    modelType: 2
  }
}
rows {
  id: 530146
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_259_LOD1"
    modelType: 2
  }
}
rows {
  id: 530147
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_265_LOD1"
    modelType: 2
  }
}
rows {
  id: 530148
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_233_LOD1"
    modelType: 2
  }
}
rows {
  id: 530149
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_210_LOD1"
    modelType: 2
  }
}
rows {
  id: 530150
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_275_LOD1"
    modelType: 2
  }
}
rows {
  id: 530151
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_276_LOD1"
    modelType: 2
  }
}
rows {
  id: 530152
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_277_LOD1"
    modelType: 2
  }
}
rows {
  id: 530153
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_278_LOD1"
    modelType: 2
  }
}
rows {
  id: 530154
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_268_LOD1"
    modelType: 2
  }
}
rows {
  id: 530155
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_269_LOD1"
    modelType: 2
  }
}
rows {
  id: 530156
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_270_LOD1"
    modelType: 2
  }
}
rows {
  id: 530157
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_273_LOD1"
    modelType: 2
  }
}
rows {
  id: 530158
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_306_LOD1"
    modelType: 2
  }
}
rows {
  id: 530159
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_262_LOD1"
    modelType: 2
  }
}
rows {
  id: 530160
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_291_LOD1"
    modelType: 2
  }
}
rows {
  id: 530161
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_292_LOD1"
    modelType: 2
  }
}
rows {
  id: 530162
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_293_LOD1"
    modelType: 2
  }
}
rows {
  id: 530163
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_294_LOD1"
    modelType: 2
  }
}
rows {
  id: 530164
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_296_LOD1"
    modelType: 2
  }
}
rows {
  id: 530165
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_303_LOD1"
    modelType: 2
  }
}
rows {
  id: 530166
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_297_LOD1"
    modelType: 2
  }
}
rows {
  id: 530167
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_298_LOD1"
    modelType: 2
  }
}
rows {
  id: 530168
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_299_LOD1"
    modelType: 2
  }
}
rows {
  id: 530169
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_301_LOD1"
    modelType: 2
  }
}
rows {
  id: 530170
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_302_LOD1"
    modelType: 2
  }
}
rows {
  id: 530171
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_337_LOD1"
    modelType: 2
  }
}
rows {
  id: 530172
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_322_LOD1"
    modelType: 2
  }
}
rows {
  id: 530173
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_323_LOD1"
    modelType: 2
  }
}
rows {
  id: 530174
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_324_LOD1"
    modelType: 2
  }
}
rows {
  id: 530175
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_328_LOD1"
    modelType: 2
  }
}
rows {
  id: 530176
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_329_LOD1"
    modelType: 2
  }
}
rows {
  id: 530177
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_330_LOD1"
    modelType: 2
  }
}
rows {
  id: 530178
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_331_LOD1"
    modelType: 2
  }
}
rows {
  id: 530179
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_332_LOD1"
    modelType: 2
  }
}
rows {
  id: 530180
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_333_LOD1"
    modelType: 2
  }
}
rows {
  id: 530181
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_308_LOD1"
    modelType: 2
  }
}
rows {
  id: 530182
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_309_LOD1"
    modelType: 2
  }
}
rows {
  id: 530183
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_310_LOD1"
    modelType: 2
  }
}
rows {
  id: 530184
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_307_LOD1"
    modelType: 2
  }
}
rows {
  id: 530185
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_313_LOD1"
    modelType: 2
  }
}
rows {
  id: 530186
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_314_LOD1"
    modelType: 2
  }
}
rows {
  id: 530187
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_316_LOD1"
    modelType: 2
  }
}
rows {
  id: 530188
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_317_LOD1"
    modelType: 2
  }
}
rows {
  id: 530189
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_327_LOD1"
    modelType: 2
  }
}
rows {
  id: 530190
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_334_LOD1"
    modelType: 2
  }
}
rows {
  id: 530191
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_340_LOD1"
    modelType: 2
  }
}
rows {
  id: 530192
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_341_LOD1"
    modelType: 2
  }
}
rows {
  id: 530193
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_321_LOD1"
    modelType: 2
  }
}
rows {
  id: 530194
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_338_LOD1"
    modelType: 2
  }
}
rows {
  id: 530195
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_367_LOD1"
    modelType: 2
  }
}
rows {
  id: 530196
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_351_LOD1"
    modelType: 2
  }
}
rows {
  id: 530197
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_352_LOD1"
    modelType: 2
  }
}
rows {
  id: 530198
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_353_LOD1"
    modelType: 2
  }
}
rows {
  id: 530199
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_354_LOD1"
    modelType: 2
  }
}
rows {
  id: 530200
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_356_LOD1"
    modelType: 2
  }
}
rows {
  id: 530201
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_363_LOD1"
    modelType: 2
  }
}
rows {
  id: 530202
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_364_LOD1"
    modelType: 2
  }
}
rows {
  id: 530203
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_357_LOD1"
    modelType: 2
  }
}
rows {
  id: 530204
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_360_LOD1"
    modelType: 2
  }
}
rows {
  id: 530205
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_361_LOD1"
    modelType: 2
  }
}
rows {
  id: 530206
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_365_LOD1"
    modelType: 2
  }
}
rows {
  id: 530207
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_335_LOD1"
    modelType: 2
  }
}
rows {
  id: 530208
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_339_LOD1"
    modelType: 2
  }
}
rows {
  id: 530209
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_372_LOD1"
    modelType: 2
  }
}
rows {
  id: 530210
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_373_LOD1"
    modelType: 2
  }
}
rows {
  id: 530211
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_370_LOD1"
    modelType: 2
  }
}
rows {
  id: 530212
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_368_LOD1"
    modelType: 2
  }
}
rows {
  id: 530213
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_375_LOD1"
    modelType: 2
  }
}
rows {
  id: 530214
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_376_LOD1"
    modelType: 2
  }
}
rows {
  id: 530215
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_377_LOD1"
    modelType: 2
  }
}
rows {
  id: 530216
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_401_LOD1"
    modelType: 2
  }
}
rows {
  id: 530217
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_394_LOD1"
    modelType: 2
  }
}
rows {
  id: 530218
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_395_LOD1"
    modelType: 2
  }
}
rows {
  id: 530219
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_396_LOD1"
    modelType: 2
  }
}
rows {
  id: 530220
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_397_LOD1"
    modelType: 2
  }
}
rows {
  id: 530221
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_380_LOD1"
    modelType: 2
  }
}
rows {
  id: 530222
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_381_LOD1"
    modelType: 2
  }
}
rows {
  id: 530223
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_382_LOD1"
    modelType: 2
  }
}
rows {
  id: 530224
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_383_LOD1"
    modelType: 2
  }
}
rows {
  id: 530225
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_405_LOD1"
    modelType: 2
  }
}
rows {
  id: 530226
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_406_LOD1"
    modelType: 2
  }
}
rows {
  id: 530227
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_404_LOD1"
    modelType: 2
  }
}
rows {
  id: 530228
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_432_LOD1"
    modelType: 2
  }
}
rows {
  id: 530229
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_419_LOD1"
    modelType: 2
  }
}
rows {
  id: 530230
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_420_LOD1"
    modelType: 2
  }
}
rows {
  id: 530231
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_421_LOD1"
    modelType: 2
  }
}
rows {
  id: 530232
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_422_LOD1"
    modelType: 2
  }
}
rows {
  id: 530233
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_423_LOD1"
    modelType: 2
  }
}
rows {
  id: 530234
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_416_LOD1"
    modelType: 2
  }
}
rows {
  id: 530235
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_417_LOD1"
    modelType: 2
  }
}
rows {
  id: 530236
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_408_LOD1"
    modelType: 2
  }
}
rows {
  id: 530237
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_413_LOD1"
    modelType: 2
  }
}
rows {
  id: 530242
  type: ItemType_Gloves
  quality: 4
  resourceConf {
    model: "SK_TY_Hands_431_LOD1"
    modelType: 2
  }
}
rows {
  id: 610001
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_004_LOD1"
    modelType: 1
  }
}
rows {
  id: 610002
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_001_LOD1"
    modelType: 1
  }
}
rows {
  id: 610004
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_003_LOD1"
    modelType: 1
  }
}
rows {
  id: 610005
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_009_LOD1"
    modelType: 1
  }
}
rows {
  id: 610006
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_005_LOD1"
    modelType: 1
  }
}
rows {
  id: 610007
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_006_LOD1"
    modelType: 1
  }
}
rows {
  id: 610008
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_007_LOD1"
    modelType: 1
  }
}
rows {
  id: 610009
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_008_LOD1"
    modelType: 1
  }
}
rows {
  id: 610010
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_011_LOD1"
    physics: "SK_Mask_011_Physics"
    modelType: 2
    idleAnim: "AS_Mask_011_idle_001"
  }
}
rows {
  id: 610011
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_011_LOD1"
    material: "MI_Mask_011_HP01_LOD1;MI_Mask_011_HP01_LOD2"
    physics: "SK_Mask_011_Physics"
    modelType: 2
    idleAnim: "AS_Mask_011_idle_001_HP01"
  }
}
rows {
  id: 610012
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_011_LOD1"
    material: "MI_Mask_011_HP02_LOD1;MI_Mask_011_HP02_LOD2"
    physics: "SK_Mask_011_Physics"
    modelType: 2
    idleAnim: "AS_Mask_011_idle_001_HP02"
  }
}
rows {
  id: 610013
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_010_LOD1"
    modelType: 1
  }
}
rows {
  id: 610014
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_010_LOD1"
    material: "MI_Mask_010_HP01_LOD1;MI_Mask_010_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610015
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_010_LOD1"
    material: "MI_Mask_010_HP02_LOD1;MI_Mask_010_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610016
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_012_LOD1"
    modelType: 1
  }
}
rows {
  id: 610017
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_012_LOD1"
    material: "MI_Mask_012_HP01_LOD1;MI_Mask_012_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610018
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_012_LOD1"
    material: "MI_Mask_012_HP02_LOD1;MI_Mask_012_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610019
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_022_LOD1"
    modelType: 1
  }
}
rows {
  id: 610020
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_023_LOD1"
    modelType: 1
  }
}
rows {
  id: 610021
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_014_LOD1"
    modelType: 1
  }
}
rows {
  id: 610022
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_014_LOD1"
    material: "MI_Mask_014_HP01_LOD1;MI_Mask_014_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610023
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_014_LOD1"
    material: "MI_Mask_014_HP02_LOD1;MI_Mask_014_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610024
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_015_LOD1"
    modelType: 1
  }
}
rows {
  id: 610025
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_015_LOD1"
    material: "MI_Mask_015_HP01_LOD1;MI_Mask_015_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610026
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_015_LOD1"
    material: "MI_Mask_015_HP02_LOD1;MI_Mask_015_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610027
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_016_LOD1"
    modelType: 1
  }
}
rows {
  id: 610028
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_016_LOD1"
    material: "MI_Mask_016_HP01_LOD1;MI_Mask_016_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610029
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_016_LOD1"
    material: "MI_Mask_016_HP02_LOD1;MI_Mask_016_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610030
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_002_LOD1"
    modelType: 1
  }
}
rows {
  id: 610031
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_007_LOD1"
    material: "MI_Mask_007_HP01_LOD1;MI_Mask_007_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610032
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_007_LOD1"
    material: "MI_Mask_007_HP02_LOD1;MI_Mask_007_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610033
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_019_LOD1"
    physics: "SK_Mask_019_Physics"
    modelType: 2
    idleAnim: "AS_Mask_019_idle_001"
  }
}
rows {
  id: 610034
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_019_LOD1"
    material: "Mask_LOD1:MI_Mask_019_1_HP01_LOD1;Mask_LOD2:MI_Mask_019_1_HP01_LOD2;Mask_Translucent_LOD1:MI_Mask_019_2_HP01_LOD1;Mask_Translucent_LOD2:MI_Mask_019_2_HP01_LOD2"
    physics: "SK_Mask_019_Physics"
    modelType: 2
    idleAnim: "AS_Mask_019_idle_001_HP01"
  }
}
rows {
  id: 610035
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_019_LOD1"
    material: "Mask_LOD1:MI_Mask_019_1_HP02_LOD1;Mask_LOD2:MI_Mask_019_1_HP02_LOD2;Mask_Translucent_LOD1:MI_Mask_019_2_HP02_LOD1;Mask_Translucent_LOD2:MI_Mask_019_2_HP02_LOD2"
    physics: "SK_Mask_019_Physics"
    modelType: 2
    idleAnim: "AS_Mask_019_idle_001_HP02"
  }
}
rows {
  id: 610036
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_017_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_017_idle_001"
  }
}
rows {
  id: 610037
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_017_LOD1"
    material: "MI_Mask_017_HP01_LOD1;MI_Mask_017_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_017_idle_001_HP01"
  }
}
rows {
  id: 610038
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_017_LOD1"
    material: "MI_Mask_017_HP02_LOD1;MI_Mask_017_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_017_idle_001_HP02"
  }
}
rows {
  id: 610039
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_024_LOD1"
    modelType: 1
  }
}
rows {
  id: 610040
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_024_LOD1"
    material: "MI_Mask_024_HP01_LOD1;MI_Mask_024_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610041
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_024_LOD1"
    material: "MI_Mask_024_HP02_LOD1;MI_Mask_024_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610042
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_018_LOD1"
    modelType: 1
  }
}
rows {
  id: 610043
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_020_LOD1"
    modelType: 1
  }
}
rows {
  id: 610044
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_013_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_013_idle_001"
  }
}
rows {
  id: 610045
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_013_LOD1"
    material: "Mask_LOD1:MI_Mask_013_1_HP01_LOD1;Mask_Translucent_LOD1:MI_Mask_013_2_HP01_LOD1;Mask_LOD2:MI_Mask_013_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_013_idle_001_HP01"
  }
}
rows {
  id: 610046
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_013_LOD1"
    material: "Mask_LOD1:MI_Mask_013_1_HP02_LOD1;Mask_Translucent_LOD1:MI_Mask_013_2_HP02_LOD1;Mask_LOD2:MI_Mask_013_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_013_idle_001_HP02"
  }
}
rows {
  id: 610047
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_021_LOD1"
    modelType: 1
  }
}
rows {
  id: 610048
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_026_LOD1"
    modelType: 1
  }
}
rows {
  id: 610049
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_027_LOD1"
    modelType: 1
  }
}
rows {
  id: 610050
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_027_LOD1"
    material: "MI_Mask_027_HP01_LOD1;MI_Mask_027_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610051
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_027_LOD1"
    material: "MI_Mask_027_HP02_LOD1;MI_Mask_027_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610052
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_028_LOD1"
    modelType: 1
  }
}
rows {
  id: 610053
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_028_LOD1"
    material: "MI_Mask_028_HP01_LOD1;MI_Mask_028_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610054
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_028_LOD1"
    material: "MI_Mask_028_HP02_LOD1;MI_Mask_028_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610055
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_033_LOD1"
    modelType: 1
  }
}
rows {
  id: 610056
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_034_LOD1"
    modelType: 1
  }
}
rows {
  id: 610057
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_035_LOD1"
    modelType: 1
  }
}
rows {
  id: 610058
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_036_LOD1"
    modelType: 1
  }
}
rows {
  id: 610059
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_037_LOD1"
    modelType: 1
  }
}
rows {
  id: 610060
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_039_LOD1"
    modelType: 1
  }
}
rows {
  id: 610061
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_025_LOD1"
    modelType: 1
  }
}
rows {
  id: 610062
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_038_LOD1"
    modelType: 1
  }
}
rows {
  id: 610063
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_030_LOD1"
    modelType: 1
  }
}
rows {
  id: 610064
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_030_LOD1"
    material: "MI_Mask_030_HP01_LOD1;MI_Mask_030_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610065
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_030_LOD1"
    material: "MI_Mask_030_HP02_LOD1;MI_Mask_030_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610066
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_031_LOD1"
    modelType: 1
  }
}
rows {
  id: 610067
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_031_LOD1"
    material: "MI_Mask_031_HP01_LOD1;MI_Mask_031_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610068
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_031_LOD1"
    material: "MI_Mask_031_HP02_LOD1;MI_Mask_031_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610069
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_006_LOD1"
    material: "MI_Mask_006_HP01_LOD1;MI_Mask_006_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610070
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_006_LOD1"
    material: "MI_Mask_006_HP02_LOD1;MI_Mask_006_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610071
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_032_LOD1"
    modelType: 1
  }
}
rows {
  id: 610072
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_032_LOD1"
    material: "MI_Mask_032_HP01_LOD1;MI_Mask_032_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610073
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_032_LOD1"
    material: "MI_Mask_032_HP02_LOD1;MI_Mask_032_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610074
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_029_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_029_idle_001"
  }
}
rows {
  id: 610075
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_029_LOD1"
    material: "Mask_LOD1:MI_Mask_029_1_HP01_LOD1;Mask_Translucent_LOD1:MI_Mask_029_2_HP01_LOD1;Mask_LOD2:MI_Mask_029_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_029_idle_001_HP01"
  }
}
rows {
  id: 610076
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_029_LOD1"
    material: "Mask_LOD1:MI_Mask_029_1_HP02_LOD1;Mask_Translucent_LOD1:MI_Mask_029_2_HP02_LOD1;Mask_LOD2:MI_Mask_029_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_029_idle_001_HP02"
  }
}
rows {
  id: 610077
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_040_LOD1"
    modelType: 1
  }
}
rows {
  id: 610078
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_057_LOD1"
    modelType: 1
  }
}
rows {
  id: 610079
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_043_LOD1"
    modelType: 1
  }
}
rows {
  id: 610080
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_050_LOD1"
    modelType: 1
  }
}
rows {
  id: 610081
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_050_LOD1"
    material: "MI_Mask_050_HP01_LOD1;MI_Mask_050_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610082
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_050_LOD1"
    material: "MI_Mask_050_HP02_LOD1;MI_Mask_050_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610083
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_042_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_042_idle_001"
  }
}
rows {
  id: 610084
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_042_LOD1"
    material: "Mask_LOD1:MI_Mask_042_1_HP01_LOD1;Mask_Translucent_LOD1:MI_Mask_042_2_HP01_LOD1;Mask_LOD2:MI_Mask_042_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_042_idle_001_HP01"
  }
}
rows {
  id: 610085
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_042_LOD1"
    material: "Mask_LOD1:MI_Mask_042_1_HP02_LOD1;Mask_Translucent_LOD1:MI_Mask_042_2_HP02_LOD1;Mask_LOD2:MI_Mask_042_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_042_idle_001_HP02"
  }
}
rows {
  id: 610086
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_048_LOD1"
    modelType: 1
  }
}
rows {
  id: 610087
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_051_LOD1"
    modelType: 1
  }
}
rows {
  id: 610088
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_051_LOD1"
    material: "MI_Mask_051_HP01_LOD1;MI_Mask_051_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610089
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_051_LOD1"
    material: "MI_Mask_051_HP02_LOD1;MI_Mask_051_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610090
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_041_LOD1"
    modelType: 1
  }
}
rows {
  id: 610091
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_053_LOD1"
    modelType: 1
  }
}
rows {
  id: 610092
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_058_LOD1"
    modelType: 1
  }
}
rows {
  id: 610093
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_059_LOD1"
    modelType: 1
  }
}
rows {
  id: 610094
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_056_LOD1"
    modelType: 1
  }
}
rows {
  id: 610095
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_060_LOD1"
    modelType: 1
  }
}
rows {
  id: 610096
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_044_LOD1"
    modelType: 1
  }
}
rows {
  id: 610097
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_045_LOD1"
    modelType: 1
  }
}
rows {
  id: 610098
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_052_LOD1"
    modelType: 1
  }
}
rows {
  id: 610099
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_044_LOD1"
    material: "MI_Mask_044_HP01_LOD1;MI_Mask_044_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610100
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_044_LOD1"
    material: "MI_Mask_044_HP02_LOD1;MI_Mask_044_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610101
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_045_LOD1"
    material: "MI_Mask_045_HP01_LOD1;MI_Mask_045_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610102
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_045_LOD1"
    material: "MI_Mask_045_HP02_LOD1;MI_Mask_045_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610103
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_049_LOD1"
    modelType: 1
  }
}
rows {
  id: 610104
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_049_LOD1"
    material: "MI_Mask_049_HP01_LOD1;MI_Mask_049_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610105
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_049_LOD1"
    material: "MI_Mask_049_HP02_LOD1;MI_Mask_049_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610106
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_054_LOD1"
    modelType: 1
  }
}
rows {
  id: 610107
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_055_LOD1"
    modelType: 1
  }
}
rows {
  id: 610108
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_066_LOD1"
    modelType: 1
  }
}
rows {
  id: 610109
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_061_LOD1"
    modelType: 1
  }
}
rows {
  id: 610110
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_065_LOD1"
    modelType: 1
  }
}
rows {
  id: 610111
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_065_LOD1"
    material: "MI_Mask_065_HP01_LOD1;MI_Mask_065_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610112
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_065_LOD1"
    material: "MI_Mask_065_HP02_LOD1;MI_Mask_065_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610113
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_067_LOD1"
    modelType: 1
  }
}
rows {
  id: 610114
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_069_LOD1"
    modelType: 1
  }
}
rows {
  id: 610115
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_062_LOD1"
    modelType: 1
  }
}
rows {
  id: 610116
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_063_LOD1"
    modelType: 1
  }
}
rows {
  id: 610117
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_064_LOD1"
    modelType: 1
  }
}
rows {
  id: 610118
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_070_LOD1"
    modelType: 1
  }
}
rows {
  id: 610119
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_072_LOD1"
    modelType: 1
  }
}
rows {
  id: 610120
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_068_LOD1"
    modelType: 1
  }
}
rows {
  id: 610121
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_075_LOD1"
    modelType: 1
  }
}
rows {
  id: 610122
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_075_LOD1"
    material: "MI_Mask_075_HP01_LOD1;MI_Mask_075_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610123
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_075_LOD1"
    material: "MI_Mask_075_HP02_LOD1;MI_Mask_075_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610124
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_071_LOD1"
    modelType: 1
  }
}
rows {
  id: 610125
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_074_LOD1"
    modelType: 1
  }
}
rows {
  id: 610126
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_073_LOD1"
    modelType: 1
  }
}
rows {
  id: 610127
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_085_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_085_idle_001"
  }
}
rows {
  id: 610128
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_085_LOD1"
    material: "MI_Mask_085_HP01_LOD1;MI_Mask_085_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_085_idle_001_HP01"
  }
}
rows {
  id: 610129
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_085_LOD1"
    material: "MI_Mask_085_HP02_LOD1;MI_Mask_085_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_085_idle_001_HP02"
  }
}
rows {
  id: 610130
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_083_LOD1"
    modelType: 1
  }
}
rows {
  id: 610131
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_084_LOD1"
    modelType: 1
  }
}
rows {
  id: 610132
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_084_LOD1"
    material: "MI_Mask_084_HP01_LOD1;MI_Mask_084_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610133
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_084_LOD1"
    material: "MI_Mask_084_HP02_LOD1;MI_Mask_084_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610134
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_083_LOD1"
    material: "MI_Mask_083_HP01_LOD1;MI_Mask_083_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610135
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_083_LOD1"
    material: "MI_Mask_083_HP02_LOD1;MI_Mask_083_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610136
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_089_LOD1"
    modelType: 1
  }
}
rows {
  id: 610137
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_081_LOD1"
    modelType: 1
  }
}
rows {
  id: 610138
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_082_LOD1"
    modelType: 1
  }
}
rows {
  id: 610139
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_076_LOD1"
    modelType: 1
  }
}
rows {
  id: 610140
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_076_LOD1"
    material: "MI_Mask_076_HP01_LOD1;MI_Mask_076_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610141
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_076_LOD1"
    material: "MI_Mask_076_HP02_LOD1;MI_Mask_076_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610142
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_077_LOD1"
    modelType: 1
  }
}
rows {
  id: 610143
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_077_LOD1"
    material: "MI_Mask_077_HP01_LOD1;MI_Mask_077_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610144
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_077_LOD1"
    material: "MI_Mask_077_HP02_LOD1;MI_Mask_077_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610145
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_078_LOD1"
    modelType: 1
  }
}
rows {
  id: 610146
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_087_LOD1"
    modelType: 1
  }
}
rows {
  id: 610147
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_087_LOD1"
    material: "MI_Mask_087_HP01_LOD1;MI_Mask_087_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610148
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_087_LOD1"
    material: "MI_Mask_087_HP02_LOD1;MI_Mask_087_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610149
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_080_LOD1"
    modelType: 1
  }
}
rows {
  id: 610150
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_080_LOD1"
    material: "MI_Mask_080_HP01_LOD1;MI_Mask_080_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610151
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_080_LOD1"
    material: "MI_Mask_080_HP02_LOD1;MI_Mask_080_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610152
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_079_LOD1"
    modelType: 1
  }
}
rows {
  id: 610153
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_079_LOD1"
    material: "MI_Mask_079_HP01_LOD1;MI_Mask_079_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610154
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_079_LOD1"
    material: "MI_Mask_079_HP02_LOD1;MI_Mask_079_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610155
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_093_LOD1"
    modelType: 1
  }
}
rows {
  id: 610156
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_093_LOD1"
    material: "MI_Mask_093_HP01_LOD1;MI_Mask_093_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610157
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_093_LOD1"
    material: "MI_Mask_093_HP02_LOD1;MI_Mask_093_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610158
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_094_LOD1"
    modelType: 1
  }
}
rows {
  id: 610159
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_094_LOD1"
    material: "MI_Mask_094_HP01_LOD1;MI_Mask_094_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610160
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_094_LOD1"
    material: "MI_Mask_094_HP02_LOD1;MI_Mask_094_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610161
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_092_LOD1"
    modelType: 1
  }
}
rows {
  id: 610162
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_096_LOD1"
    physics: "SK_Mask_096_Physics"
    modelType: 2
    idleAnim: "AS_Mask_096_idle_001"
  }
}
rows {
  id: 610163
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_096_LOD1"
    material: "Mask_LOD1:MI_Mask_096_1_HP01_LOD1;Mask_Translucent_LOD1:MI_Mask_096_2_HP01_LOD1;Mask_LOD2:MI_Mask_096_1_HP01_LOD2;Mask_Translucent_LOD2:MI_Mask_096_2_HP01_LOD2"
    physics: "SK_Mask_096_Physics"
    modelType: 2
    idleAnim: "AS_Mask_096_idle_001_HP01"
  }
}
rows {
  id: 610164
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_096_LOD1"
    material: "Mask_LOD1:MI_Mask_096_1_HP02_LOD1;Mask_Translucent_LOD1:MI_Mask_096_2_HP02_LOD1;Mask_LOD2:MI_Mask_096_1_HP02_LOD2;Mask_Translucent_LOD2:MI_Mask_096_2_HP02_LOD2"
    physics: "SK_Mask_096_Physics"
    modelType: 2
    idleAnim: "AS_Mask_096_idle_001_HP02"
  }
}
rows {
  id: 610165
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_097_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_097_idle_001"
  }
}
rows {
  id: 610166
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_097_LOD1"
    material: "Mask_LOD1:MI_Mask_097_1_HP01_LOD1;Mask_Translucent_LOD1:MI_Mask_097_2_HP01_LOD1;Mask_LOD2:MI_Mask_097_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_097_idle_001_HP01"
  }
}
rows {
  id: 610167
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_097_LOD1"
    material: "Mask_LOD1:MI_Mask_097_1_HP02_LOD1;Mask_Translucent_LOD1:MI_Mask_097_2_HP02_LOD1;Mask_LOD2:MI_Mask_097_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_097_idle_001_HP02"
  }
}
rows {
  id: 610168
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_090_LOD1"
    modelType: 1
  }
}
rows {
  id: 610169
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_090_LOD1"
    material: "MI_Mask_090_HP01_LOD1;MI_Mask_090_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610170
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_090_LOD1"
    material: "MI_Mask_090_HP02_LOD1;MI_Mask_090_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610171
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_095_LOD1"
    modelType: 1
  }
}
rows {
  id: 610172
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_091_LOD1"
    modelType: 1
  }
}
rows {
  id: 610173
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_100_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_100_idle_001"
  }
}
rows {
  id: 610174
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_100_LOD1"
    material: "MI_Mask_100_HP01_LOD1;MI_Mask_100_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_100_idle_001_HP01"
  }
}
rows {
  id: 610175
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_100_LOD1"
    material: "MI_Mask_100_HP02_LOD1;MI_Mask_100_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_100_idle_001_HP02"
  }
}
rows {
  id: 610176
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_098_LOD1"
    modelType: 1
  }
}
rows {
  id: 610177
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_102_LOD1"
    modelType: 1
  }
}
rows {
  id: 610178
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_102_LOD1"
    material: "Mask_LOD1:MI_Mask_102_1_HP01_LOD1;Mask_Translucent_LOD1:MI_Mask_102_2_HP01_LOD1;Mask_LOD2:MI_Mask_102_HP01_LOD2;Mask_Translucent_LOD2:MI_Mask_102_2_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610179
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_102_LOD1"
    material: "Mask_LOD1:MI_Mask_102_1_HP02_LOD1;Mask_Translucent_LOD1:MI_Mask_102_2_HP02_LOD1;Mask_LOD2:MI_Mask_102_HP02_LOD2;Mask_Translucent_LOD2:MI_Mask_102_2_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610180
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_099_LOD1"
    modelType: 1
  }
}
rows {
  id: 610181
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_103_LOD1"
    modelType: 1
  }
}
rows {
  id: 610182
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_103_LOD1"
    material: "Mask_LOD1:MI_Mask_103_HP01_LOD1;Mask_LOD2:MI_Mask_103_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610183
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_103_LOD1"
    material: "Mask_LOD1:MI_Mask_103_HP02_LOD1;Mask_LOD2:MI_Mask_103_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610184
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_104_LOD1"
    modelType: 1
  }
}
rows {
  id: 610185
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_104_LOD1"
    material: "Mask_LOD1:MI_Mask_104_HP01_LOD1;Mask_LOD2:MI_Mask_104_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610186
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_104_LOD1"
    material: "Mask_LOD1:MI_Mask_104_HP02_LOD1;Mask_LOD2:MI_Mask_104_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610187
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_106_LOD1"
    modelType: 1
  }
}
rows {
  id: 610188
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_107_LOD1"
    modelType: 1
  }
}
rows {
  id: 610189
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_108_LOD1"
    modelType: 1
  }
}
rows {
  id: 610190
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_109_LOD1"
    modelType: 1
  }
}
rows {
  id: 610191
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_105_LOD1"
    modelType: 1
  }
}
rows {
  id: 610192
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_086_LOD1"
    modelType: 1
  }
}
rows {
  id: 610193
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_086_LOD1"
    material: "Mask_LOD1:MI_Mask_086_HP01_LOD1;Mask_LOD2:MI_Mask_086_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610194
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_086_LOD1"
    material: "Mask_LOD1:MI_Mask_086_HP02_LOD1;Mask_LOD2:MI_Mask_086_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610195
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_110_LOD1"
    modelType: 1
  }
}
rows {
  id: 610196
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_110_LOD1"
    material: "Mask_LOD1:MI_Mask_110_HP01_LOD1;Mask_LOD2:MI_Mask_110_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610197
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_110_LOD1"
    material: "Mask_LOD1:MI_Mask_110_HP02_LOD1;Mask_LOD2:MI_Mask_110_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610198
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_112_LOD1"
    modelType: 1
  }
}
rows {
  id: 610199
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_112_LOD1"
    material: "Mask_LOD1:MI_Mask_112_HP01_LOD1;Mask_LOD2:MI_Mask_112_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610200
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_112_LOD1"
    material: "Mask_LOD1:MI_Mask_112_HP02_LOD1;Mask_LOD2:MI_Mask_112_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610201
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_101_LOD1"
    modelType: 1
  }
}
rows {
  id: 610202
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_101_LOD1"
    material: "Mask_LOD1:MI_Mask_101_HP01_LOD1;Mask_LOD2:MI_Mask_101_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610203
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_101_LOD1"
    material: "Mask_LOD1:MI_Mask_101_HP02_LOD1;Mask_LOD2:MI_Mask_101_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610204
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_114_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_114_idle_001"
  }
}
rows {
  id: 610205
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_114_LOD1"
    material: "Mask_1_LOD1:MI_Mask_114_1_HP01_LOD1;Mask_2_LOD1:MI_Mask_114_2_HP01_LOD1;Mask_LOD2:MI_Mask_114_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_114_idle_001_HP01"
  }
}
rows {
  id: 610206
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_114_LOD1"
    material: "Mask_1_LOD1:MI_Mask_114_1_HP02_LOD1;Mask_2_LOD1:MI_Mask_114_2_HP02_LOD1;Mask_LOD2:MI_Mask_114_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_114_idle_001_HP02"
  }
}
rows {
  id: 610207
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_116_LOD1"
    modelType: 1
  }
}
rows {
  id: 610208
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_116_LOD1"
    material: "Mask_LOD1:MI_Mask_116_HP01_LOD1;Mask_LOD2:MI_Mask_116_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610209
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_116_LOD1"
    material: "Mask_LOD1:MI_Mask_116_HP02_LOD1;Mask_LOD2:MI_Mask_116_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610210
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_111_LOD1"
    modelType: 1
  }
}
rows {
  id: 610211
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_111_LOD1"
    material: "Mask_LOD1:MI_Mask_111_HP01_LOD1;Mask_LOD2:MI_Mask_111_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610212
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_111_LOD1"
    material: "Mask_LOD1:MI_Mask_111_HP02_LOD1;Mask_LOD2:MI_Mask_111_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610213
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_113_LOD1"
    modelType: 1
  }
}
rows {
  id: 610214
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_119_LOD1"
    modelType: 1
  }
}
rows {
  id: 610215
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_120_LOD1"
    modelType: 1
  }
}
rows {
  id: 610216
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_126_LOD1"
    modelType: 1
  }
}
rows {
  id: 610217
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_115_LOD1"
    modelType: 1
  }
}
rows {
  id: 610218
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_115_LOD1"
    material: "Mask_LOD1:MI_Mask_115_HP01_LOD1;Mask_LOD2:MI_Mask_115_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610219
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_115_LOD1"
    material: "Mask_LOD1:MI_Mask_115_HP02_LOD1;Mask_LOD2:MI_Mask_115_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610220
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_121_LOD1"
    modelType: 1
  }
}
rows {
  id: 610221
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_121_LOD1"
    material: "Mask_LOD1:MI_Mask_121_1_HP01_LOD1;Mask_Translucent_LOD1:MI_Mask_121_2_HP01_LOD1;Mask_LOD2:MI_Mask_121_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610222
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_121_LOD1"
    material: "Mask_LOD1:MI_Mask_121_1_HP02_LOD1;Mask_Translucent_LOD1:MI_Mask_121_2_HP02_LOD1;Mask_LOD2:MI_Mask_121_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610223
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_118_LOD1"
    modelType: 1
  }
}
rows {
  id: 610224
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_128_LOD1"
    modelType: 1
  }
}
rows {
  id: 610225
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_123_LOD1"
    modelType: 1
  }
}
rows {
  id: 610226
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_123_LOD1"
    material: "Mask_LOD1:MI_Mask_123_HP01_LOD1;Mask_LOD2:MI_Mask_123_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610227
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_123_LOD1"
    material: "Mask_LOD1:MI_Mask_123_HP02_LOD1;Mask_LOD2:MI_Mask_123_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610228
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_117_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_idle_117_001"
  }
}
rows {
  id: 610229
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_117_LOD1"
    material: "Mask_LOD1:MI_Mask_117_HP01_LOD1;Mask_Translucent_LOD1:MI_Mask_117_1_HP01_LOD1;Mask_LOD2:MI_Mask_117_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_idle_117_001_HP01"
  }
}
rows {
  id: 610230
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_117_LOD1"
    material: "Mask_LOD1:MI_Mask_117_HP02_LOD1;Mask_Translucent_LOD1:MI_Mask_117_1_HP02_LOD1;Mask_LOD2:MI_Mask_117_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_idle_117_001_HP02"
  }
}
rows {
  id: 610231
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_140_LOD1"
    modelType: 1
  }
}
rows {
  id: 610232
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_122_LOD1"
    modelType: 1
  }
}
rows {
  id: 610233
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_122_LOD1"
    material: "Mask_LOD1:MI_Mask_122_HP01_LOD1;Mask_LOD2:MI_Mask_122_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610234
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_122_LOD1"
    material: "Mask_LOD1:MI_Mask_122_HP02_LOD1;Mask_LOD2:MI_Mask_122_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610235
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_125_LOD1"
    modelType: 1
  }
}
rows {
  id: 610236
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_124_LOD1"
    modelType: 1
  }
}
rows {
  id: 610237
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_124_LOD1"
    material: "Mask_LOD1:MI_Mask_124_1_HP01_LOD1;Mask_Translucent_LOD1:MI_Mask_124_2_HP01_LOD1;Mask_LOD2:MI_Mask_124_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610238
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_124_LOD1"
    material: "Mask_LOD1:MI_Mask_124_1_HP02_LOD1;Mask_Translucent_LOD1:MI_Mask_124_2_HP02_LOD1;Mask_LOD2:MI_Mask_124_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610239
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_134_LOD1"
    modelType: 1
  }
}
rows {
  id: 610240
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_133_LOD1"
    modelType: 1
  }
}
rows {
  id: 610241
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_133_LOD1"
    material: "Mask_LOD1:MI_Mask_133_HP01_LOD1;Mask_LOD2:MI_Mask_133_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610242
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_133_LOD1"
    material: "Mask_LOD1:MI_Mask_133_HP02_LOD1;Mask_LOD2:MI_Mask_133_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610243
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_141_LOD1"
    modelType: 1
  }
}
rows {
  id: 610244
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_127_LOD1"
    modelType: 1
  }
}
rows {
  id: 610245
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_138_LOD1"
    modelType: 1
  }
}
rows {
  id: 610246
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_138_LOD1"
    material: "Mask_LOD1:MI_Mask_138_HP01_LOD1;Mask_LOD2:MI_Mask_138_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610247
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_138_LOD1"
    material: "Mask_LOD1:MI_Mask_138_HP02_LOD1;Mask_LOD2:MI_Mask_138_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610248
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_136_LOD1"
    modelType: 1
  }
}
rows {
  id: 610249
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_137_LOD1"
    modelType: 1
  }
}
rows {
  id: 610250
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_130_LOD1"
    modelType: 1
  }
}
rows {
  id: 610251
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_131_LOD1"
    modelType: 1
  }
}
rows {
  id: 610252
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_132_LOD1"
    modelType: 1
  }
}
rows {
  id: 610253
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_139_LOD1"
    modelType: 1
  }
}
rows {
  id: 610254
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_135_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_135_idle_001"
  }
}
rows {
  id: 610255
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_135_LOD1"
    material: "Mask_1_LOD1:MI_Mask_135_1_HP01_LOD1;Mask_2_LOD1:MI_Mask_135_2_HP01_LOD1;Mask_LOD2:MI_Mask_135_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_135_idle_001_HP01"
  }
}
rows {
  id: 610256
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_135_LOD1"
    material: "Mask_1_LOD1:MI_Mask_135_1_HP02_LOD1;Mask_2_LOD1:MI_Mask_135_2_HP02_LOD1;Mask_LOD2:MI_Mask_135_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_135_idle_001_HP02"
  }
}
rows {
  id: 610257
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_144_LOD1"
    modelType: 1
  }
}
rows {
  id: 610258
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_129_LOD1"
    modelType: 1
  }
}
rows {
  id: 610259
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_129_LOD1"
    material: "Mask_LOD1:MI_Mask_129_HP01_LOD1;Mask_LOD2:MI_Mask_129_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610260
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_129_LOD1"
    material: "Mask_LOD1:MI_Mask_129_HP02_LOD1;Mask_LOD2:MI_Mask_129_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610261
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_154_LOD1"
    modelType: 1
  }
}
rows {
  id: 610262
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_154_LOD1"
    material: "Mask_LOD1:MI_Mask_154_HP01_LOD1;Mask_LOD2:MI_Mask_154_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610263
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_154_LOD1"
    material: "Mask_LOD1:MI_Mask_154_HP02_LOD1;Mask_LOD2:MI_Mask_154_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610264
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_143_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_143_idle_001"
  }
}
rows {
  id: 610265
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_143_LOD1"
    material: "MI_Mask_143_HP01_LOD1;MI_Mask_143_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_143_idle_001_HP01"
  }
}
rows {
  id: 610266
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_143_LOD1"
    material: "MI_Mask_143_HP02_LOD1;MI_Mask_143_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_143_idle_001_HP02"
  }
}
rows {
  id: 610267
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_142_LOD1"
    modelType: 1
  }
}
rows {
  id: 610268
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_142_LOD1"
    material: "Mask_LOD1:MI_Mask_142_HP01_LOD1;Mask_LOD2:MI_Mask_142_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610269
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_142_LOD1"
    material: "Mask_LOD1:MI_Mask_142_HP02_LOD1;Mask_LOD2:MI_Mask_142_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610270
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_147_LOD1"
    modelType: 1
  }
}
rows {
  id: 610271
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_148_LOD1"
    modelType: 1
  }
}
rows {
  id: 610272
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_145_LOD1"
    modelType: 1
  }
}
rows {
  id: 610273
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_145_LOD1"
    material: "Mask_LOD1:MI_Mask_145_HP01_LOD1;Mask_LOD2:MI_Mask_145_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610274
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_145_LOD1"
    material: "Mask_LOD1:MI_Mask_145_HP02_LOD1;Mask_LOD2:MI_Mask_145_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610275
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_149_LOD1"
    modelType: 1
  }
}
rows {
  id: 610276
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_149_LOD1"
    material: "Mask_LOD1:MI_Mask_149_HP01_LOD1;Mask_LOD2:MI_Mask_149_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610277
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_149_LOD1"
    material: "Mask_LOD1:MI_Mask_149_HP02_LOD1;Mask_LOD2:MI_Mask_149_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610278
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_152_LOD1"
    modelType: 1
  }
}
rows {
  id: 610279
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_152_LOD1"
    material: "Mask_LOD1:MI_Mask_152_HP01_LOD1;Mask_LOD2:MI_Mask_152_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610280
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_152_LOD1"
    material: "Mask_LOD1:MI_Mask_152_HP02_LOD1;Mask_LOD2:MI_Mask_152_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610281
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_146_LOD1"
    modelType: 1
  }
}
rows {
  id: 610282
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_146_LOD1"
    material: "Mask_LOD1:MI_Mask_146_HP01_LOD1;Mask_LOD2:MI_Mask_146_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610283
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_146_LOD1"
    material: "Mask_LOD1:MI_Mask_146_HP02_LOD1;Mask_LOD2:MI_Mask_146_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610284
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_153_LOD1"
    emitter: "FX_CH_Decorate_Mask_153"
    modelType: 1
  }
}
rows {
  id: 610285
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_153_LOD1"
    material: "Mask_1_LOD1:MI_Mask_153_1_HP01_LOD1;Mask_2_LOD1:MI_Mask_153_2_HP01_LOD1;Mask_LOD2:MI_Mask_153_HP01_LOD2"
    emitter: "FX_CH_Decorate_Mask_153_HP01"
    modelType: 1
  }
}
rows {
  id: 610286
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_153_LOD1"
    material: "Mask_1_LOD1:MI_Mask_153_1_HP02_LOD1;Mask_2_LOD1:MI_Mask_153_2_HP02_LOD1;Mask_LOD2:MI_Mask_153_HP02_LOD2"
    emitter: "FX_CH_Decorate_Mask_153_HP02"
    modelType: 1
  }
}
rows {
  id: 610287
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_161_LOD1"
    modelType: 1
  }
}
rows {
  id: 610288
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_160_LOD1"
    modelType: 1
  }
}
rows {
  id: 610289
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_163_LOD1"
    modelType: 1
  }
}
rows {
  id: 610290
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_159_LOD1"
    modelType: 1
  }
}
rows {
  id: 610291
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_156_LOD1"
    modelType: 1
  }
}
rows {
  id: 610292
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_156_LOD1"
    material: "Mask_LOD1:MI_Mask_156_HP01_LOD1;Mask_LOD2:MI_Mask_156_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610293
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_156_LOD1"
    material: "Mask_LOD1:MI_Mask_156_HP02_LOD1;Mask_LOD2:MI_Mask_156_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610294
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_155_LOD1"
    modelType: 1
  }
}
rows {
  id: 610295
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_155_LOD1"
    material: "Mask_LOD1:MI_Mask_155_HP01_LOD1;Mask_LOD2:MI_Mask_155_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610296
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_155_LOD1"
    material: "Mask_LOD1:MI_Mask_155_HP02_LOD1;Mask_LOD2:MI_Mask_155_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610297
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_158_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_158_idle_001"
  }
}
rows {
  id: 610298
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_158_LOD1"
    material: "Mask_1_LOD1:MI_Mask_158_1_HP01_LOD1;Mask_2_LOD1:MI_Mask_158_2_HP01_LOD1;Mask_3_LOD1:MI_Mask_158_3_HP01_LOD1;Mask_LOD2:MI_Mask_158_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_158_idle_001_HP01"
  }
}
rows {
  id: 610299
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_158_LOD1"
    material: "Mask_1_LOD1:MI_Mask_158_1_HP02_LOD1;Mask_2_LOD1:MI_Mask_158_2_HP02_LOD1;Mask_3_LOD1:MI_Mask_158_3_HP02_LOD1;Mask_LOD2:MI_Mask_158_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_158_idle_001_HP02"
  }
}
rows {
  id: 610300
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_167_LOD1"
    modelType: 1
  }
}
rows {
  id: 610301
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_167_LOD1"
    material: "Mask_LOD1:MI_Mask_167_HP01_LOD1;Mask_LOD2:MI_Mask_167_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610302
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_167_LOD1"
    material: "Mask_LOD1:MI_Mask_167_HP02_LOD1;Mask_LOD2:MI_Mask_167_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610303
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_169_LOD1"
    modelType: 1
  }
}
rows {
  id: 610304
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_169_LOD1"
    material: "Mask_LOD1:MI_Mask_169_HP01_LOD1;Mask_LOD2:MI_Mask_169_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610305
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_169_LOD1"
    material: "Mask_LOD1:MI_Mask_169_HP02_LOD1;Mask_LOD2:MI_Mask_169_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610306
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_168_LOD1"
    modelType: 1
  }
}
rows {
  id: 610307
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_168_LOD1"
    material: "Mask_LOD1:MI_Mask_168_HP01_LOD1;Mask_LOD2:MI_Mask_168_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610308
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_168_LOD1"
    material: "Mask_LOD1:MI_Mask_168_HP02_LOD1;Mask_LOD2:MI_Mask_168_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610309
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_166_LOD1"
    emitter: "FX_CH_Decorate_Mask_166_001"
    modelType: 1
  }
}
rows {
  id: 610310
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_166_LOD1"
    material: "Mask_LOD1:MI_Mask_166_HP01_LOD1;Mask_LOD2:MI_Mask_166_HP01_LOD2"
    emitter: "FX_CH_Decorate_Mask_166_001_HP01"
    modelType: 1
  }
}
rows {
  id: 610311
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_166_LOD1"
    material: "Mask_LOD1:MI_Mask_166_HP02_LOD1;Mask_LOD2:MI_Mask_166_HP02_LOD2"
    emitter: "FX_CH_Decorate_Mask_166_001_HP02"
    modelType: 1
  }
}
rows {
  id: 610312
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_151_LOD1"
    modelType: 1
  }
}
rows {
  id: 610313
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_150_LOD1"
    modelType: 1
  }
}
rows {
  id: 610314
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_162_LOD1"
    modelType: 1
  }
}
rows {
  id: 610315
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_157_LOD1"
    modelType: 1
  }
}
rows {
  id: 610316
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_165_LOD1"
    modelType: 1
  }
}
rows {
  id: 610317
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_165_LOD1"
    material: "Mask_LOD1:MI_Mask_165_HP01_LOD1;Mask_LOD2:MI_Mask_165_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610318
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_165_LOD1"
    material: "Mask_LOD1:MI_Mask_165_HP02_LOD1;Mask_LOD2:MI_Mask_165_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610319
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_170_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_170_idle_001"
  }
}
rows {
  id: 610320
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_170_LOD1"
    material: "Mask_LOD1:MI_Mask_170_HP01_LOD1;Mask_LOD2:MI_Mask_170_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_170_idle_001_HP01"
  }
}
rows {
  id: 610321
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_170_LOD1"
    material: "Mask_LOD1:MI_Mask_170_HP02_LOD1;Mask_LOD2:MI_Mask_170_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_170_idle_001_HP02"
  }
}
rows {
  id: 610322
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_174_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_174_idle_001"
  }
}
rows {
  id: 610323
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_174_LOD1"
    material: "Mask_LOD1:MI_Mask_174_HP01_LOD1;Mask_LOD2:MI_Mask_174_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_174_idle_001_HP01"
  }
}
rows {
  id: 610324
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_174_LOD1"
    material: "Mask_LOD1:MI_Mask_174_HP02_LOD1;Mask_LOD2:MI_Mask_174_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_174_idle_001_HP02"
  }
}
rows {
  id: 610325
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_178_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_178_idle_001"
  }
}
rows {
  id: 610326
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_178_LOD1"
    material: "Mask_LOD1:MI_Mask_178_HP01_LOD1;Mask_LOD2:MI_Mask_178_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_178_idle_001_HP01"
  }
}
rows {
  id: 610327
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_178_LOD1"
    material: "Mask_LOD1:MI_Mask_178_HP02_LOD1;Mask_LOD2:MI_Mask_178_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_178_idle_001_HP02"
  }
}
rows {
  id: 610328
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_164_LOD1"
    modelType: 1
  }
}
rows {
  id: 610329
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_164_LOD1"
    material: "Mask_LOD1:MI_Mask_164_HP01_LOD1;Mask_LOD2:MI_Mask_164_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610330
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_164_LOD1"
    material: "Mask_LOD1:MI_Mask_164_HP02_LOD1;Mask_LOD2:MI_Mask_164_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610331
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_171_LOD1"
    modelType: 1
  }
}
rows {
  id: 610332
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_179_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_179_idle_001"
  }
}
rows {
  id: 610333
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_179_LOD1"
    material: "Mask_1_LOD1:MI_Mask_179_1_HP01_LOD1;Mask_2_LOD1:MI_Mask_179_2_HP01_LOD1;Mask_3_LOD1:MI_Mask_179_3_HP01_LOD1;Mask_LOD2:MI_Mask_179_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_179_idle_001_HP01"
  }
}
rows {
  id: 610334
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_179_LOD1"
    material: "Mask_1_LOD1:MI_Mask_179_1_HP02_LOD1;Mask_2_LOD1:MI_Mask_179_2_HP02_LOD1;Mask_3_LOD1:MI_Mask_179_3_HP02_LOD1;Mask_LOD2:MI_Mask_179_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_179_idle_001_HP02"
  }
}
rows {
  id: 610335
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_185_LOD1"
    modelType: 1
  }
}
rows {
  id: 610336
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_185_LOD1"
    material: "Mask_LOD1:MI_Mask_185_HP01_LOD1;Mask_LOD2:MI_Mask_185_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610337
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_185_LOD1"
    material: "Mask_LOD1:MI_Mask_185_HP02_LOD1;Mask_LOD2:MI_Mask_185_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610338
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_186_LOD1"
    modelType: 1
  }
}
rows {
  id: 610339
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_186_LOD1"
    material: "Mask_LOD1:MI_Mask_186_HP01_LOD1;Mask_LOD2:MI_Mask_186_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610340
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_186_LOD1"
    material: "Mask_LOD1:MI_Mask_186_HP02_LOD1;Mask_LOD2:MI_Mask_186_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610341
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_187_LOD1"
    modelType: 1
  }
}
rows {
  id: 610342
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_187_LOD1"
    material: "Mask_LOD1:MI_Mask_187_HP01_LOD1;Mask_LOD2:MI_Mask_187_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610343
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_187_LOD1"
    material: "Mask_LOD1:MI_Mask_187_HP02_LOD1;Mask_LOD2:MI_Mask_187_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610344
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_184_LOD1"
    emitter: "FX_CH_Decorate_Mask_184"
    modelType: 1
  }
}
rows {
  id: 610345
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_184_LOD1"
    material: "Mask_LOD1:MI_Mask_184_HP01_LOD1;Mask_LOD2:MI_Mask_184_HP01_LOD2"
    emitter: "FX_CH_Decorate_Mask_184_HP01"
    modelType: 1
  }
}
rows {
  id: 610346
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_184_LOD1"
    material: "Mask_LOD1:MI_Mask_184_HP02_LOD1;Mask_LOD2:MI_Mask_184_HP02_LOD2"
    emitter: "FX_CH_Decorate_Mask_184_HP02"
    modelType: 1
  }
}
rows {
  id: 610347
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_175_LOD1"
    modelType: 1
  }
}
rows {
  id: 610348
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_176_LOD1"
    modelType: 1
  }
}
rows {
  id: 610349
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_172_LOD1"
    modelType: 1
  }
}
rows {
  id: 610350
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_173_LOD1"
    modelType: 1
  }
}
rows {
  id: 610351
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_173_LOD1"
    material: "Mask_LOD1:MI_Mask_173_HP01_LOD1;Mask_LOD2:MI_Mask_173_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610352
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_173_LOD1"
    material: "Mask_LOD1:MI_Mask_173_HP02_LOD1;Mask_LOD2:MI_Mask_173_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610353
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_188_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_188_idle_001"
  }
}
rows {
  id: 610354
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_188_LOD1"
    material: "Mask_1_LOD1:MI_Mask_188_1_HP01_LOD1;Mask_2_LOD1:MI_Mask_188_2_HP01_LOD1;Mask_LOD2:MI_Mask_188_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_188_idle_001_HP01"
  }
}
rows {
  id: 610355
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_188_LOD1"
    material: "Mask_1_LOD1:MI_Mask_188_1_HP02_LOD1;Mask_2_LOD1:MI_Mask_188_2_HP02_LOD1;Mask_LOD2:MI_Mask_188_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_188_idle_001_HP02"
  }
}
rows {
  id: 610356
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_189_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_189_idle_001"
  }
}
rows {
  id: 610357
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_189_LOD1"
    material: "Mask_1_LOD1:MI_Mask_189_1_HP01_LOD1;Mask_2_LOD1:MI_Mask_189_2_HP01_LOD1;Mask_3_LOD1:MI_Mask_189_3_HP01_LOD1;Mask_LOD2:MI_Mask_189_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_189_idle_001_HP01"
  }
}
rows {
  id: 610358
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_189_LOD1"
    material: "Mask_1_LOD1:MI_Mask_189_1_HP02_LOD1;Mask_2_LOD1:MI_Mask_189_2_HP02_LOD1;Mask_3_LOD1:MI_Mask_189_3_HP02_LOD1;Mask_LOD2:MI_Mask_189_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_189_idle_001_HP02"
  }
}
rows {
  id: 610359
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_181_LOD1"
    modelType: 1
  }
}
rows {
  id: 610360
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_181_LOD1"
    material: "Mask_LOD1:MI_Mask_181_HP01_LOD1;Mask_LOD2:MI_Mask_181_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610361
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_181_LOD1"
    material: "Mask_LOD1:MI_Mask_181_HP02_LOD1;Mask_LOD2:MI_Mask_181_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610362
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_190_LOD1"
    modelType: 1
  }
}
rows {
  id: 610363
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_190_LOD1"
    material: "Mask_LOD1:MI_Mask_190_HP01_LOD1;Mask_LOD2:MI_Mask_190_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610364
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_190_LOD1"
    material: "Mask_LOD1:MI_Mask_190_HP02_LOD1;Mask_LOD2:MI_Mask_190_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610365
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_180_LOD1"
    modelType: 1
  }
}
rows {
  id: 610366
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_182_LOD1"
    modelType: 1
  }
}
rows {
  id: 610367
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_182_LOD1"
    material: "Mask_LOD1:MI_Mask_182_HP01_LOD1;Mask_LOD2:MI_Mask_182_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610368
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_182_LOD1"
    material: "Mask_LOD1:MI_Mask_182_HP02_LOD1;Mask_LOD2:MI_Mask_182_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610369
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_177_LOD1"
    modelType: 1
  }
}
rows {
  id: 610370
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_177_LOD1"
    material: "Mask_LOD1:MI_Mask_177_HP01_LOD1;Mask_LOD2:MI_Mask_177_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610371
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_177_LOD1"
    material: "Mask_LOD1:MI_Mask_177_HP02_LOD1;Mask_LOD2:MI_Mask_177_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610372
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_183_LOD1"
    modelType: 1
  }
}
rows {
  id: 610373
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_183_LOD1"
    material: "Mask_LOD1:MI_Mask_183_HP01_LOD1;Mask_LOD2:MI_Mask_183_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610374
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_183_LOD1"
    material: "Mask_LOD1:MI_Mask_183_HP02_LOD1;Mask_LOD2:MI_Mask_183_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610375
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_201_LOD1"
    modelType: 1
  }
}
rows {
  id: 610376
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_191_LOD1"
    emitter: "FX_CH_Decorate_Mask_191_001"
    modelType: 1
  }
}
rows {
  id: 610377
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_191_LOD1"
    material: "Mask_LOD1:MI_Mask_191_HP01_LOD1;Mask_LOD2:MI_Mask_191_HP01_LOD2"
    emitter: "FX_CH_Decorate_Mask_191_001_HP01"
    modelType: 1
  }
}
rows {
  id: 610378
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_191_LOD1"
    material: "Mask_LOD1:MI_Mask_191_HP02_LOD1;Mask_LOD2:MI_Mask_191_HP02_LOD2"
    emitter: "FX_CH_Decorate_Mask_191_001_HP02"
    modelType: 1
  }
}
rows {
  id: 610379
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_193_LOD1"
    modelType: 1
  }
}
rows {
  id: 610380
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_193_LOD1"
    material: "Mask_LOD1:MI_Mask_193_HP01_LOD1;Mask_LOD2:MI_Mask_193_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610381
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_193_LOD1"
    material: "Mask_LOD1:MI_Mask_193_HP02_LOD1;Mask_LOD2:MI_Mask_193_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610382
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_194_LOD1"
    modelType: 1
  }
}
rows {
  id: 610383
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_194_LOD1"
    material: "Mask_LOD1:MI_Mask_194_HP01_LOD1;Mask_LOD2:MI_Mask_194_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610384
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_194_LOD1"
    material: "Mask_LOD1:MI_Mask_194_HP02_LOD1;Mask_LOD2:MI_Mask_194_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610385
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_200_LOD1"
    modelType: 1
  }
}
rows {
  id: 610386
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_192_LOD1"
    modelType: 1
  }
}
rows {
  id: 610387
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_207_LOD1"
    modelType: 1
  }
}
rows {
  id: 610388
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_204_LOD1"
    modelType: 1
  }
}
rows {
  id: 610389
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_202_LOD1"
    modelType: 1
  }
}
rows {
  id: 610390
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_202_LOD1"
    material: "Mask_LOD1:MI_Mask_202_HP01_LOD1;Mask_LOD2:MI_Mask_202_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610391
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_202_LOD1"
    material: "Mask_LOD1:MI_Mask_202_HP02_LOD1;Mask_LOD2:MI_Mask_202_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610392
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_209_LOD1"
    emitter: "FX_CH_Decorate_Mask_209_001"
    modelType: 1
  }
}
rows {
  id: 610393
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_209_LOD1"
    material: "Mask:MI_Mask_209_HP01"
    emitter: "FX_CH_Decorate_Mask_209_001_HP01"
    modelType: 1
  }
}
rows {
  id: 610394
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SM_Mask_209_LOD1"
    material: "Mask:MI_Mask_209_HP02"
    emitter: "FX_CH_Decorate_Mask_209_001_HP02"
    modelType: 1
  }
}
rows {
  id: 610395
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_195_LOD1"
    modelType: 1
  }
}
rows {
  id: 610396
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_196_LOD1"
    modelType: 1
  }
}
rows {
  id: 610397
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_197_LOD1"
    modelType: 1
  }
}
rows {
  id: 610398
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_208_LOD1"
    modelType: 1
  }
}
rows {
  id: 610399
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_205_LOD1"
    modelType: 1
  }
}
rows {
  id: 610400
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_199_LOD1"
    modelType: 1
  }
}
rows {
  id: 610401
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_199_LOD1"
    material: "Mask_LOD1:MI_Mask_199_HP01_LOD1;Mask_LOD2:MI_Mask_199_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610402
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_199_LOD1"
    material: "Mask_LOD1:MI_Mask_199_HP02_LOD1;Mask_LOD2:MI_Mask_199_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610403
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_198_LOD1"
    modelType: 1
  }
}
rows {
  id: 610404
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_198_LOD1"
    material: "Mask_LOD1:MI_Mask_198_HP01_LOD1;Mask_LOD2:MI_Mask_198_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610405
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_198_LOD1"
    material: "Mask_LOD1:MI_Mask_198_HP02_LOD1;Mask_LOD2:MI_Mask_198_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610406
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_203_LOD1"
    modelType: 1
  }
}
rows {
  id: 610407
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_203_LOD1"
    material: "Mask_LOD1:MI_Mask_203_HP01_LOD1;Mask_LOD2:MI_Mask_203_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610408
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_203_LOD1"
    material: "Mask_LOD1:MI_Mask_203_HP02_LOD1;Mask_LOD2:MI_Mask_203_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610409
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_206_LOD1"
    modelType: 1
  }
}
rows {
  id: 610410
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_215_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_215_idle_001"
  }
}
rows {
  id: 610411
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_215_LOD1"
    material: "Mask_LOD1:MI_Mask_215_HP01_LOD1;Mask_LOD2:MI_Mask_215_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_215_idle_001_HP01"
  }
}
rows {
  id: 610412
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_215_LOD1"
    material: "Mask_LOD1:MI_Mask_215_HP02_LOD1;Mask_LOD2:MI_Mask_215_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_215_idle_001_HP02"
  }
}
rows {
  id: 610413
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_219_LOD1"
    modelType: 1
  }
}
rows {
  id: 610414
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_219_LOD1"
    material: "Mask_LOD1:MI_Mask_219_HP01_LOD1;Mask_LOD2:MI_Mask_219_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610415
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_219_LOD1"
    material: "Mask_LOD1:MI_Mask_219_HP02_LOD1;Mask_LOD2:MI_Mask_219_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610416
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_212_LOD1"
    modelType: 1
  }
}
rows {
  id: 610417
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_212_LOD1"
    material: "Mask_LOD1:MI_Mask_212_HP01_LOD1;Mask_LOD2:MI_Mask_212_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610418
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_212_LOD1"
    material: "Mask_LOD1:MI_Mask_212_HP02_LOD1;Mask_LOD2:MI_Mask_212_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610419
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_213_LOD1"
    modelType: 1
  }
}
rows {
  id: 610420
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_213_LOD1"
    material: "Mask_LOD1:MI_Mask_213_HP01_LOD1;Mask_LOD2:MI_Mask_213_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610421
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_213_LOD1"
    material: "Mask_LOD1:MI_Mask_213_HP02_LOD1;Mask_LOD2:MI_Mask_213_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610422
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_220_LOD1"
    modelType: 1
  }
}
rows {
  id: 610423
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_220_LOD1"
    material: "Mask_LOD1:MI_Mask_220_HP01_LOD1;Mask_LOD2:MI_Mask_220_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 610424
  type: ItemType_FaceOrnament
  quality: 2
  resourceConf {
    model: "SM_Mask_220_LOD1"
    material: "Mask_LOD1:MI_Mask_220_HP02_LOD1;Mask_LOD2:MI_Mask_220_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 610425
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_223_LOD1"
    modelType: 1
  }
}
rows {
  id: 610426
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_217_LOD1"
    modelType: 1
  }
}
rows {
  id: 610427
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_218_LOD1"
    modelType: 1
  }
}
rows {
  id: 610428
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_214_LOD1"
    modelType: 1
  }
}
rows {
  id: 610429
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_221_LOD1"
    modelType: 1
  }
}
rows {
  id: 610430
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_226_LOD1"
    modelType: 1
  }
}
rows {
  id: 610431
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_216_LOD1"
    modelType: 2
    idleAnim: "AS_Mask_216_idle_001"
  }
}
rows {
  id: 610432
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_216_LOD1"
    material: "Mask_LOD1:MI_Mask_216_HP01_LOD1;Mask_LOD2:MI_Mask_216_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_216_idle_001_HP01"
  }
}
rows {
  id: 610433
  type: ItemType_FaceOrnament
  quality: 1
  resourceConf {
    model: "SK_Mask_216_LOD1"
    material: "Mask_LOD1:MI_Mask_216_HP02_LOD1;Mask_LOD2:MI_Mask_216_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Mask_216_idle_001_HP02"
  }
}
rows {
  id: 610434
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_Mask_229_LOD1"
    modelType: 1
  }
}
rows {
  id: 618001
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_MAY_DemonMask_01_LOD1"
    modelType: 1
  }
}
rows {
  id: 618002
  type: ItemType_FaceOrnament
  quality: 3
  resourceConf {
    model: "SM_MAY_DemonMask_02_LOD1"
    modelType: 1
  }
}
rows {
  id: 620001
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_004_LOD1"
    modelType: 1
  }
}
rows {
  id: 620002
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_003_LOD1"
    modelType: 1
  }
}
rows {
  id: 620003
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_001_LOD1"
    modelType: 1
  }
}
rows {
  id: 620004
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_002_LOD1"
    modelType: 1
  }
}
rows {
  id: 620005
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_005_LOD1"
    modelType: 1
  }
}
rows {
  id: 620008
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_008_LOD1"
    modelType: 1
  }
}
rows {
  id: 620011
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_022_LOD1"
    modelType: 1
  }
}
rows {
  id: 620012
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_012_LOD1"
    modelType: 1
  }
}
rows {
  id: 620013
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_019_LOD1"
    emitter: "FX_CH_Decorate_Wing_019"
    modelType: 1
  }
}
rows {
  id: 620014
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_028_LOD1"
    emitter: "FX_CH_Decorate_Wing_028"
    modelType: 1
  }
}
rows {
  id: 620015
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_013_LOD1"
    modelType: 1
  }
}
rows {
  id: 620016
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_020_LOD1"
    modelType: 1
  }
}
rows {
  id: 620017
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_007_LOD1"
    modelType: 1
  }
}
rows {
  id: 620018
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_024_LOD1"
    emitter: "FX_CH_Decorate_Wing_024"
    modelType: 1
  }
}
rows {
  id: 620019
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_021_LOD1"
    emitter: "FX_CH_Decorate_Wing_021"
    modelType: 1
  }
}
rows {
  id: 620020
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_009_LOD1"
    emitter: "FX_CH_Decorate_Wing_009"
    modelType: 1
  }
}
rows {
  id: 620021
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_025_LOD1"
    modelType: 1
  }
}
rows {
  id: 620022
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_011_LOD1"
    modelType: 1
  }
}
rows {
  id: 620023
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_016_LOD1"
    modelType: 1
  }
}
rows {
  id: 620024
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_023_LOD1"
    modelType: 1
  }
}
rows {
  id: 620025
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_006_LOD1"
    modelType: 1
  }
}
rows {
  id: 620026
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_014_LOD1"
    modelType: 1
  }
}
rows {
  id: 620027
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_017_LOD1"
    modelType: 1
  }
}
rows {
  id: 620028
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_015_LOD1"
    modelType: 1
  }
}
rows {
  id: 620029
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_026_LOD1"
    modelType: 1
  }
}
rows {
  id: 620031
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_029_LOD1"
    modelType: 1
  }
}
rows {
  id: 620032
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_032_LOD1"
    modelType: 1
  }
}
rows {
  id: 620033
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_032_LOD1"
    material: "MI_Wing_032_HP01_LOD1;MI_Wing_032_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620034
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_032_LOD1"
    material: "MI_Wing_032_HP02_LOD1;MI_Wing_032_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620035
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_033_LOD1"
    modelType: 1
  }
}
rows {
  id: 620036
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_033_LOD1"
    material: "MI_Wing_033_HP01_LOD1;MI_Wing_033_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620037
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_033_LOD1"
    material: "MI_Wing_033_HP02_LOD1;MI_Wing_033_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620038
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_035_LOD1"
    physics: "SK_Wing_035_Physics"
    emitter: "FX_CH_Decorate_Wing_035"
    modelType: 2
  }
}
rows {
  id: 620039
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_035_LOD1"
    material: "MI_Wing_035_HP01_LOD1;MI_Wing_035_HP01_LOD2"
    physics: "SK_Wing_035_Physics"
    emitter: "FX_CH_Decorate_Wing_035_HP01"
    modelType: 2
  }
}
rows {
  id: 620040
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_035_LOD1"
    material: "MI_Wing_035_HP02_LOD1;MI_Wing_035_HP02_LOD2"
    physics: "SK_Wing_035_Physics"
    emitter: "FX_CH_Decorate_Wing_035_HP02"
    modelType: 2
  }
}
rows {
  id: 620041
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_037_LOD1"
    physics: "SK_Wing_037_Physics"
    modelType: 2
    idleAnim: "AS_Idle_001_Wing_037"
  }
}
rows {
  id: 620042
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_037_LOD1"
    material: "MI_Wing_037_1_HP01_LOD1;MI_Wing_037_1_HP01_LOD2;MI_Wing_037_2_HP01_LOD1"
    physics: "SK_Wing_037_Physics"
    modelType: 2
    idleAnim: "AS_Idle_001_Wing_037_HP01"
  }
}
rows {
  id: 620043
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_037_LOD1"
    material: "MI_Wing_037_1_HP02_LOD1;MI_Wing_037_1_HP02_LOD2;MI_Wing_037_2_HP02_LOD1"
    physics: "SK_Wing_037_Physics"
    modelType: 2
    idleAnim: "AS_Idle_001_Wing_037_HP02"
  }
}
rows {
  id: 620044
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_027_LOD1"
    modelType: 1
  }
}
rows {
  id: 620045
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_027_LOD1"
    material: "MI_Wing_027_HP01_LOD1;MI_Wing_027_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620046
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_027_LOD1"
    material: "MI_Wing_027_HP02_LOD1;MI_Wing_027_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620047
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_030_LOD1"
    modelType: 1
  }
}
rows {
  id: 620048
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_009_LOD1"
    material: "MI_Wing_009_HP01_LOD1;MI_Wing_009_HP01_LOD2"
    emitter: "FX_CH_Decorate_Wing_009_HP01"
    modelType: 1
  }
}
rows {
  id: 620049
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_009_LOD1"
    material: "MI_Wing_009_HP02_LOD1;MI_Wing_009_HP02_LOD2"
    emitter: "FX_CH_Decorate_Wing_009_HP02"
    modelType: 1
  }
}
rows {
  id: 620050
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_028_LOD1"
    material: "MI_Wing_028_HP01_LOD1;MI_Wing_028_HP01_LOD2"
    emitter: "FX_CH_Decorate_Wing_028_HP01"
    modelType: 1
  }
}
rows {
  id: 620051
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_028_LOD1"
    material: "MI_Wing_028_HP02_LOD1;MI_Wing_028_HP02_LOD2"
    emitter: "FX_CH_Decorate_Wing_028_HP02"
    modelType: 1
  }
}
rows {
  id: 620052
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_021_LOD1"
    material: "MI_Wing_021_HP01_LOD1;MI_Wing_021_HP01_LOD2"
    emitter: "FX_CH_Decorate_Wing_021_HP01"
    modelType: 1
  }
}
rows {
  id: 620053
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_021_LOD1"
    material: "MI_Wing_021_HP02_LOD1;MI_Wing_021_HP01_LOD2"
    emitter: "FX_CH_Decorate_Wing_021_HP02"
    modelType: 1
  }
}
rows {
  id: 620054
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_019_LOD1"
    material: "MI_Wing_019_HP01_LOD1;MI_Wing_019_HP01_LOD2"
    emitter: "FX_CH_Decorate_Wing_019_HP01"
    modelType: 1
  }
}
rows {
  id: 620055
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_019_LOD1"
    material: "MI_Wing_019_HP02_LOD1;MI_Wing_019_HP02_LOD2"
    emitter: "FX_CH_Decorate_Wing_019_HP02"
    modelType: 1
  }
}
rows {
  id: 620056
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_038_LOD1"
    modelType: 1
  }
}
rows {
  id: 620057
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_038_LOD1"
    material: "MI_Wing_038_HP01_LOD1;MI_Wing_038_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620058
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_038_LOD1"
    material: "MI_Wing_038_HP02_LOD1;MI_Wing_038_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620059
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_031_LOD1"
    modelType: 1
  }
}
rows {
  id: 620060
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_031_LOD1"
    material: "MI_Wing_031_HP01_LOD1;MI_Wing_031_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620061
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_031_LOD1"
    material: "MI_Wing_031_HP02_LOD1;MI_Wing_031_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620062
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_034_LOD1"
    modelType: 1
  }
}
rows {
  id: 620063
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_034_LOD1"
    material: "MI_Wing_034_HP01_LOD1;MI_Wing_034_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620064
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_034_LOD1"
    material: "MI_Wing_034_HP02_LOD1;MI_Wing_034_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620065
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_045_LOD1"
    modelType: 1
  }
}
rows {
  id: 620066
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_045_LOD1"
    material: "MI_Wing_045_HP01_LOD1;MI_Wing_045_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620067
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_045_LOD1"
    material: "MI_Wing_045_HP02_LOD1;MI_Wing_045_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620068
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_043_LOD1"
    modelType: 1
  }
}
rows {
  id: 620069
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_043_LOD1"
    material: "Wing_LOD1:MI_Wing_043_HP01_1_LOD1;Wing_LOD1_Translucent:MI_Wing_043_HP01_2_LOD1;Wing_LOD2:MI_Wing_043_HP01_1_LOD2;Wing_LOD2_Translucent:MI_Wing_043_HP01_2_LOD2"
    modelType: 1
  }
}
rows {
  id: 620070
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_043_LOD1"
    material: "Wing_LOD1:MI_Wing_043_HP02_1_LOD1;Wing_LOD1_Translucent:MI_Wing_043_HP02_2_LOD1;Wing_LOD2:MI_Wing_043_HP02_1_LOD2;Wing_LOD2_Translucent:MI_Wing_043_HP02_2_LOD2"
    modelType: 1
  }
}
rows {
  id: 620071
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_051_LOD1"
    modelType: 1
  }
}
rows {
  id: 620072
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_052_LOD1"
    modelType: 1
  }
}
rows {
  id: 620073
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_048_LOD1"
    modelType: 1
  }
}
rows {
  id: 620074
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_048_LOD1"
    material: "MI_Wing_048_HP01_LOD1;MI_Wing_048_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620075
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_048_LOD1"
    material: "MI_Wing_048_HP02_LOD1;MI_Wing_048_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620076
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_042_LOD1"
    modelType: 1
  }
}
rows {
  id: 620077
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_050_LOD1"
    modelType: 1
  }
}
rows {
  id: 620078
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_050_LOD1"
    material: "MI_Wing_050_HP01_LOD1;MI_Wing_050_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620079
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_050_LOD1"
    material: "MI_Wing_050_HP02_LOD1;MI_Wing_050_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620080
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_044_LOD1"
    modelType: 1
  }
}
rows {
  id: 620081
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_047_LOD1"
    modelType: 1
  }
}
rows {
  id: 620082
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_046_LOD1"
    modelType: 1
  }
}
rows {
  id: 620083
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_053_LOD1"
    modelType: 1
  }
}
rows {
  id: 620084
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_053_LOD1"
    material: "MI_Wing_053_HP01_LOD1;MI_Wing_053_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620085
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_053_LOD1"
    material: "MI_Wing_053_HP02_LOD1;MI_Wing_053_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620086
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_026_LOD1"
    material: "MI_Wing_026_HP01_LOD1;MI_Wing_026_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620087
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_026_LOD1"
    material: "MI_Wing_026_HP02_LOD1;MI_Wing_026_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620088
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_041_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_041_Idle_001"
  }
}
rows {
  id: 620089
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_041_LOD1"
    material: "MI_Wing_041_HP01_LOD1;MI_Wing_041_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_041_Idle_001_HP01"
  }
}
rows {
  id: 620090
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_041_LOD1"
    material: "MI_Wing_041_HP02_LOD1;MI_Wing_041_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_041_Idle_001_HP02"
  }
}
rows {
  id: 620091
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_049_LOD1"
    modelType: 1
  }
}
rows {
  id: 620092
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_049_LOD1"
    material: "MI_Wing_049_HP01_LOD1;MI_Wing_049_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620093
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_049_LOD1"
    material: "MI_Wing_049_HP02_LOD1;MI_Wing_049_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620094
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_039_LOD1"
    modelType: 1
  }
}
rows {
  id: 620095
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_040_LOD1"
    modelType: 1
  }
}
rows {
  id: 620096
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_054_LOD1"
    modelType: 1
  }
}
rows {
  id: 620097
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_055_LOD1"
    modelType: 1
  }
}
rows {
  id: 620098
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_055_LOD1"
    material: "MI_Wing_055_HP01_LOD1;MI_Wing_055_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620099
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_078_LOD1"
    modelType: 1
  }
}
rows {
  id: 620100
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_061_LOD1"
    modelType: 1
  }
}
rows {
  id: 620101
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_079_LOD1"
    modelType: 1
  }
}
rows {
  id: 620102
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_057_LOD1"
    modelType: 1
  }
}
rows {
  id: 620103
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_058_LOD1"
    modelType: 1
  }
}
rows {
  id: 620104
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_056_LOD1"
    modelType: 1
  }
}
rows {
  id: 620105
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_056_LOD1"
    material: "MI_Wing_056_HP01_LOD1;MI_Wing_056_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620106
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_056_LOD1"
    material: "MI_Wing_056_HP02_LOD1;MI_Wing_056_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620107
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_076_LOD1"
    modelType: 1
  }
}
rows {
  id: 620108
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_059_LOD1"
    modelType: 1
  }
}
rows {
  id: 620109
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_071_LOD1"
    modelType: 1
  }
}
rows {
  id: 620110
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_072_LOD1"
    modelType: 1
  }
}
rows {
  id: 620111
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_073_LOD1"
    modelType: 1
  }
}
rows {
  id: 620112
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_088_LOD1"
    modelType: 1
  }
}
rows {
  id: 620113
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_083_LOD1"
    modelType: 1
  }
}
rows {
  id: 620114
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_084_LOD1"
    modelType: 1
  }
}
rows {
  id: 620115
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_077_LOD1"
    modelType: 1
  }
}
rows {
  id: 620116
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_069_LOD1"
    modelType: 1
  }
}
rows {
  id: 620117
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_069_LOD1"
    material: "MI_Wing_069_HP01_LOD1;MI_Wing_069_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620118
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_069_LOD1"
    material: "MI_Wing_069_HP02_LOD1;MI_Wing_069_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620119
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_080_LOD1"
    modelType: 1
  }
}
rows {
  id: 620120
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_080_LOD1"
    material: "MI_Wing_080_HP01_LOD1;MI_Wing_080_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620121
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_080_LOD1"
    material: "MI_Wing_080_HP02_LOD1;MI_Wing_080_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620122
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_081_LOD1"
    modelType: 1
  }
}
rows {
  id: 620123
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_081_LOD1"
    material: "MI_Wing_081_HP01_LOD1;MI_Wing_081_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620124
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_081_LOD1"
    material: "MI_Wing_081_HP02_LOD1;MI_Wing_081_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620125
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_065_LOD1"
    modelType: 1
  }
}
rows {
  id: 620126
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_065_LOD1"
    material: "MI_Wing_065_HP01_LOD1;MI_Wing_065_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620127
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_065_LOD1"
    material: "MI_Wing_065_HP02_LOD1;MI_Wing_065_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620128
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_066_LOD1"
    modelType: 1
  }
}
rows {
  id: 620129
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_066_LOD1"
    material: "MI_Wing_066_HP01_LOD1;MI_Wing_066_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620130
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_066_LOD1"
    material: "MI_Wing_066_HP02_LOD1;MI_Wing_066_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620131
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_075_LOD1"
    modelType: 1
  }
}
rows {
  id: 620132
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_075_LOD1"
    material: "MI_Wing_075_HP01_LOD1;MI_Wing_075_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620133
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_075_LOD1"
    material: "MI_Wing_075_HP02_LOD1;MI_Wing_075_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620134
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_062_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_062_Idle_001"
  }
}
rows {
  id: 620135
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_062_LOD1"
    material: "Wing_LOD1:MI_Wing_062_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_062_2_HP01_LOD1;Wing_LOD2:MI_Wing_062_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_062_Idle_001_HP01"
  }
}
rows {
  id: 620136
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_062_LOD1"
    material: "Wing_LOD1:MI_Wing_062_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_062_2_HP02_LOD1;Wing_LOD2:MI_Wing_062_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_062_Idle_001_HP02"
  }
}
rows {
  id: 620137
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_074_LOD1"
    modelType: 1
  }
}
rows {
  id: 620138
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_074_LOD1"
    material: "MI_Wing_074_HP01_LOD1;MI_Wing_074_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620139
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_074_LOD1"
    material: "MI_Wing_074_HP02_LOD1;MI_Wing_074_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620140
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_085_LOD1"
    modelType: 1
  }
}
rows {
  id: 620141
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_064_LOD1"
    modelType: 1
  }
}
rows {
  id: 620142
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_064_LOD1"
    material: "MI_Wing_064_HP01_LOD1;MI_Wing_064_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620143
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_064_LOD1"
    material: "MI_Wing_064_HP02_LOD1;MI_Wing_064_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620144
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_070_LOD1"
    modelType: 1
  }
}
rows {
  id: 620145
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_070_LOD1"
    material: "MI_Wing_070_HP01_LOD1;MI_Wing_070_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620146
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_070_LOD1"
    material: "MI_Wing_070_HP02_LOD1;MI_Wing_070_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620147
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_082_LOD1"
    modelType: 1
  }
}
rows {
  id: 620148
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_089_LOD1"
    modelType: 1
  }
}
rows {
  id: 620149
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_060_LOD1"
    physics: "SK_Wing_060_Physics"
    modelType: 2
    idleAnim: "AS_Wing_060_Idle_001"
  }
}
rows {
  id: 620150
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_060_LOD1"
    material: "Wing_LOD1:MI_Wing_060_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_060_2_HP01_LOD1;Wing_LOD2:MI_Wing_060_HP01_LOD2"
    physics: "SK_Wing_060_Physics"
    modelType: 2
    idleAnim: "AS_Wing_060_Idle_001_HP01"
  }
}
rows {
  id: 620151
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_060_LOD1"
    material: "Wing_LOD1:MI_Wing_060_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_060_2_HP02_LOD1;Wing_LOD2:MI_Wing_060_HP02_LOD2"
    physics: "SK_Wing_060_Physics"
    modelType: 2
    idleAnim: "AS_Wing_060_Idle_001_HP02"
  }
}
rows {
  id: 620152
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_063_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_063_Idle_001"
  }
}
rows {
  id: 620153
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_063_LOD1"
    material: "MI_Wing_063_HP01_LOD1;MI_Wing_063_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_063_Idle_001_HP01"
  }
}
rows {
  id: 620154
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_063_LOD1"
    material: "MI_Wing_063_HP02_LOD1;MI_Wing_063_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_063_Idle_001_HP02"
  }
}
rows {
  id: 620155
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_067_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_067_Idle_001"
  }
}
rows {
  id: 620156
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_067_LOD1"
    material: "Wing_LOD1:MI_Wing_067_HP01_1_LOD1;Wing_Translucent_LOD1:MI_Wing_067_HP01_2_LOD1;Wing_LOD2:MI_Wing_067_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_067_Idle_001_HP01"
  }
}
rows {
  id: 620157
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_067_LOD1"
    material: "Wing_LOD1:MI_Wing_067_HP02_1_LOD1;Wing_Translucent_LOD1:MI_Wing_067_HP02_2_LOD1;Wing_LOD2:MI_Wing_067_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_067_Idle_001_HP02"
  }
}
rows {
  id: 620158
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_068_LOD1"
    modelType: 1
  }
}
rows {
  id: 620159
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_068_LOD1"
    material: "MI_Wing_068_HP01_LOD1;MI_Wing_068_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620160
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_068_LOD1"
    material: "MI_Wing_068_HP02_LOD1;MI_Wing_068_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620161
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_085_LOD1"
    material: "MI_Wing_085_HP01_LOD1;MI_Wing_085_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620162
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_085_LOD1"
    material: "MI_Wing_085_HP02_LOD1;MI_Wing_085_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620164
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_098_LOD1"
    modelType: 1
  }
}
rows {
  id: 620165
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_141_LOD1"
    modelType: 1
  }
}
rows {
  id: 620166
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_142_LOD1"
    modelType: 1
  }
}
rows {
  id: 620167
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_100_LOD1"
    modelType: 1
  }
}
rows {
  id: 620168
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_097_LOD1"
    modelType: 1
  }
}
rows {
  id: 620169
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_112_LOD1"
    modelType: 1
  }
}
rows {
  id: 620170
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_112_LOD1"
    material: "MI_Wing_112_HP01_LOD1;MI_Wing_112_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620171
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_112_LOD1"
    material: "MI_Wing_112_HP02_LOD1;MI_Wing_112_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620172
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_091_LOD1"
    modelType: 1
  }
}
rows {
  id: 620173
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_092_LOD1"
    modelType: 1
  }
}
rows {
  id: 620174
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_093_LOD1"
    modelType: 1
  }
}
rows {
  id: 620175
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_094_LOD1"
    modelType: 1
  }
}
rows {
  id: 620176
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_096_LOD1"
    modelType: 1
  }
}
rows {
  id: 620177
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_096_LOD1"
    material: "MI_Wing_096_HP01_LOD1;MI_Wing_096_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620178
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_096_LOD1"
    material: "MI_Wing_096_HP02_LOD1;MI_Wing_096_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620179
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_095_LOD1"
    modelType: 1
  }
}
rows {
  id: 620180
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_095_LOD1"
    material: "MI_Wing_095_HP01_LOD1;MI_Wing_095_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620181
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_095_LOD1"
    material: "MI_Wing_095_HP02_LOD1;MI_Wing_095_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620182
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_103_LOD1"
    modelType: 1
  }
}
rows {
  id: 620183
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_101_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_101_Idle_001"
  }
}
rows {
  id: 620184
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_101_LOD1"
    material: "Wing_LOD1:MI_Wing_101_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_101_2_HP01_LOD1;Wing_LOD2:MI_Wing_101_1_HP01_LOD2;Wing_Translucent_LOD2:MI_Wing_101_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_101_Idle_001_HP01"
  }
}
rows {
  id: 620185
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_101_LOD1"
    material: "Wing_LOD1:MI_Wing_101_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_101_2_HP02_LOD1;Wing_LOD2:MI_Wing_101_1_HP02_LOD2;Wing_Translucent_LOD2:MI_Wing_101_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_101_Idle_001_HP02"
  }
}
rows {
  id: 620186
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_102_LOD1"
    modelType: 1
  }
}
rows {
  id: 620187
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_109_LOD1"
    modelType: 1
  }
}
rows {
  id: 620188
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_086_LOD1"
    modelType: 1
  }
}
rows {
  id: 620189
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_086_LOD1"
    material: "MI_Wing_086_HP01_LOD1;MI_Wing_086_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620190
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_086_LOD1"
    material: "MI_Wing_086_HP02_LOD1;MI_Wing_086_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620191
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_099_LOD1"
    modelType: 1
  }
}
rows {
  id: 620192
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_087_LOD1"
    modelType: 1
  }
}
rows {
  id: 620193
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_113_LOD1"
    modelType: 1
  }
}
rows {
  id: 620194
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_113_LOD1"
    material: "MI_Wing_113_HP01_LOD1;MI_Wing_113_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620195
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_113_LOD1"
    material: "MI_Wing_113_HP02_LOD1;MI_Wing_113_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620196
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_115_LOD1"
    modelType: 1
  }
}
rows {
  id: 620197
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_122_LOD1"
    modelType: 1
  }
}
rows {
  id: 620198
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_129_LOD1"
    modelType: 1
  }
}
rows {
  id: 620199
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_145_LOD1"
    modelType: 1
  }
}
rows {
  id: 620200
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_146_LOD1"
    modelType: 1
  }
}
rows {
  id: 620201
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_144_LOD1"
    modelType: 1
  }
}
rows {
  id: 620202
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_104_LOD1"
    modelType: 1
  }
}
rows {
  id: 620203
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_104_LOD1"
    material: "MI_Wing_104_HP01_LOD1;MI_Wing_104_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620204
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_104_LOD1"
    material: "MI_Wing_104_HP02_LOD1;MI_Wing_104_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620205
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_131_LOD1"
    modelType: 1
  }
}
rows {
  id: 620206
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_111_LOD1"
    modelType: 1
  }
}
rows {
  id: 620207
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_111_LOD1"
    material: "MI_Wing_111_1_HP01_LOD1;MI_Wing_111_2_HP01_LOD1"
    modelType: 1
  }
}
rows {
  id: 620208
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_111_LOD1"
    material: "MI_Wing_111_1_HP02_LOD1;MI_Wing_111_2_HP02_LOD1"
    modelType: 1
  }
}
rows {
  id: 620209
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_120_LOD1"
    modelType: 1
  }
}
rows {
  id: 620210
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_123_LOD1"
    modelType: 1
  }
}
rows {
  id: 620211
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_124_LOD1"
    modelType: 1
  }
}
rows {
  id: 620212
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_128_LOD1"
    modelType: 1
  }
}
rows {
  id: 620213
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_136_LOD1"
    modelType: 1
  }
}
rows {
  id: 620214
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_137_LOD1"
    modelType: 1
  }
}
rows {
  id: 620215
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_138_LOD1"
    modelType: 1
  }
}
rows {
  id: 620216
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_114_LOD1"
    modelType: 1
  }
}
rows {
  id: 620217
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_114_LOD1"
    material: "MI_Wing_114_HP01_LOD1;MI_Wing_114_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620218
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_114_LOD1"
    material: "MI_Wing_114_HP02_LOD1;MI_Wing_114_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620219
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_125_LOD1"
    modelType: 1
  }
}
rows {
  id: 620220
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_110_LOD1"
    modelType: 1
  }
}
rows {
  id: 620221
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_117_LOD1"
    modelType: 1
  }
}
rows {
  id: 620222
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_117_LOD1"
    material: "Wing_LOD1:MI_Wing_117_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_117_2_HP01_LOD1;Wing_LOD2:MI_Wing_117_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620223
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_117_LOD1"
    material: "Wing_LOD1:MI_Wing_117_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_117_2_HP02_LOD1;Wing_LOD2:MI_Wing_117_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620224
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_134_LOD1"
    modelType: 1
  }
}
rows {
  id: 620225
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_148_LOD1"
    modelType: 1
  }
}
rows {
  id: 620226
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_151_LOD1"
    modelType: 1
  }
}
rows {
  id: 620227
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_151_LOD1"
    material: "MI_Wing_151_HP01_LOD1;MI_Wing_151_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620228
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_151_LOD1"
    material: "MI_Wing_151_HP02_LOD1;MI_Wing_151_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620229
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_149_LOD1"
    modelType: 1
  }
}
rows {
  id: 620230
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_149_LOD1"
    material: "MI_Wing_149_HP01_LOD1;MI_Wing_149_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620231
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_149_LOD1"
    material: "MI_Wing_149_HP02_LOD1;MI_Wing_149_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620232
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_105_LOD1"
    modelType: 1
  }
}
rows {
  id: 620233
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_105_LOD1"
    material: "MI_Wing_105_HP01_LOD1;MI_Wing_105_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620234
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_105_LOD1"
    material: "MI_Wing_105_HP02_LOD1;MI_Wing_105_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620235
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_116_LOD1"
    modelType: 1
  }
}
rows {
  id: 620236
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_116_LOD1"
    material: "MI_Wing_116_HP01_LOD1;MI_Wing_116_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620237
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_116_LOD1"
    material: "MI_Wing_116_HP02_LOD1;MI_Wing_116_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620238
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_118_LOD1"
    modelType: 1
  }
}
rows {
  id: 620239
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_118_LOD1"
    material: "MI_Wing_118_HP01_LOD1;MI_Wing_118_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620240
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_118_LOD1"
    material: "MI_Wing_118_HP02_LOD1;MI_Wing_118_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620241
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_132_LOD1"
    modelType: 1
  }
}
rows {
  id: 620242
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_133_LOD1"
    modelType: 1
  }
}
rows {
  id: 620243
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_143_LOD1"
    modelType: 1
  }
}
rows {
  id: 620244
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_143_LOD1"
    material: "Wing_LOD1:MI_Wing_143_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_143_2_HP01_LOD1;Wing_LOD2:MI_Wing_143_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620245
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_143_LOD1"
    material: "Wing_LOD1:MI_Wing_143_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_143_2_HP02_LOD1;Wing_LOD2:MI_Wing_143_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620246
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_155_LOD1"
    physics: "SK_Wing_155_Physics"
    modelType: 2
    idleAnim: "AS_Wing_155_Idle_001"
  }
}
rows {
  id: 620247
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_155_LOD1"
    material: "Wing_LOD1:MI_Wing_155_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_155_2_HP01_LOD1;Wing_LOD2:MI_Wing_155_HP01_LOD2"
    physics: "SK_Wing_155_Physics"
    modelType: 2
    idleAnim: "AS_Wing_155_Idle_001_HP01"
  }
}
rows {
  id: 620248
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_155_LOD1"
    material: "Wing_LOD1:MI_Wing_155_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_155_2_HP02_LOD1;Wing_LOD2:MI_Wing_155_HP02_LOD2"
    physics: "SK_Wing_155_Physics"
    modelType: 2
    idleAnim: "AS_Wing_155_Idle_001_HP02"
  }
}
rows {
  id: 620249
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_150_LOD1"
    modelType: 1
  }
}
rows {
  id: 620250
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_154_LOD1"
    modelType: 1
  }
}
rows {
  id: 620251
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_090_LOD1"
    modelType: 1
  }
}
rows {
  id: 620252
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_152_LOD1"
    modelType: 1
  }
}
rows {
  id: 620253
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_152_LOD1"
    material: "MI_Wing_152_HP01_LOD1;MI_Wing_152_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620254
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_152_LOD1"
    material: "MI_Wing_152_HP02_LOD1;MI_Wing_152_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620255
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_130_LOD1"
    modelType: 1
  }
}
rows {
  id: 620256
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_130_LOD1"
    material: "MI_Wing_130_HP01_LOD1;MI_Wing_130_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620257
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_130_LOD1"
    material: "MI_Wing_130_HP02_LOD1;MI_Wing_130_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620258
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_161_LOD1"
    modelType: 1
  }
}
rows {
  id: 620259
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_163_LOD1"
    modelType: 1
  }
}
rows {
  id: 620260
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_169_LOD1"
    modelType: 1
  }
}
rows {
  id: 620261
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_169_LOD1"
    material: "MI_Wing_169_HP01_LOD1;MI_Wing_169_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620262
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_169_LOD1"
    material: "MI_Wing_169_HP02_LOD1;MI_Wing_169_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620263
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_164_LOD1"
    modelType: 1
  }
}
rows {
  id: 620264
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_164_LOD1"
    material: "MI_Wing_164_HP01_LOD1;MI_Wing_164_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620265
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_164_LOD1"
    material: "MI_Wing_164_HP02_LOD1;MI_Wing_164_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620266
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_156_LOD1"
    modelType: 1
  }
}
rows {
  id: 620267
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_157_LOD1"
    modelType: 1
  }
}
rows {
  id: 620268
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_178_LOD1"
    modelType: 1
  }
}
rows {
  id: 620269
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_182_LOD1"
    modelType: 1
  }
}
rows {
  id: 620270
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_166_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_166_Idle_001"
  }
}
rows {
  id: 620271
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_166_LOD1"
    material: "Wing_LOD1:MI_Wing_166_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_166_2_HP01_LOD1;Wing_LOD2:MI_Wing_166_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_166_Idle_001_HP01"
  }
}
rows {
  id: 620272
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_166_LOD1"
    material: "Wing_LOD1:MI_Wing_166_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_166_2_HP02_LOD1;Wing_LOD2:MI_Wing_166_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_166_Idle_001_HP02"
  }
}
rows {
  id: 620273
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_158_LOD1"
    modelType: 1
  }
}
rows {
  id: 620274
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_183_LOD1"
    modelType: 1
  }
}
rows {
  id: 620275
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_177_LOD1"
    modelType: 1
  }
}
rows {
  id: 620276
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_189_LOD1"
    modelType: 1
  }
}
rows {
  id: 620277
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_181_LOD1"
    modelType: 1
  }
}
rows {
  id: 620278
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_172_LOD1"
    modelType: 1
  }
}
rows {
  id: 620279
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_180_LOD1"
    modelType: 1
  }
}
rows {
  id: 620280
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_173_LOD1"
    modelType: 1
  }
}
rows {
  id: 620281
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_174_LOD1"
    modelType: 1
  }
}
rows {
  id: 620282
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_175_LOD1"
    modelType: 1
  }
}
rows {
  id: 620283
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_176_LOD1"
    modelType: 1
  }
}
rows {
  id: 620284
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_176_LOD1"
    material: "MI_Wing_176_HP01_LOD1;MI_Wing_176_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620285
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_176_LOD1"
    material: "MI_Wing_176_HP02_LOD1;MI_Wing_176_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620286
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_159_LOD1"
    modelType: 1
  }
}
rows {
  id: 620287
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_159_LOD1"
    material: "MI_Wing_159_HP01_LOD1;MI_Wing_159_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620288
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_159_LOD1"
    material: "MI_Wing_159_HP02_LOD1;MI_Wing_159_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620289
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_160_LOD1"
    modelType: 1
  }
}
rows {
  id: 620290
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_160_LOD1"
    material: "MI_Wing_160_HP01_LOD1;MI_Wing_160_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620291
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_160_LOD1"
    material: "MI_Wing_160_HP02_LOD1;MI_Wing_160_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620292
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_184_LOD1"
    modelType: 1
  }
}
rows {
  id: 620293
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_184_LOD1"
    material: "MI_Wing_184_HP01_LOD1;MI_Wing_184_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620294
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_184_LOD1"
    material: "MI_Wing_184_HP02_LOD1;MI_Wing_184_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620295
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_185_LOD1"
    modelType: 1
  }
}
rows {
  id: 620296
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_186_LOD1"
    modelType: 1
  }
}
rows {
  id: 620297
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_202_LOD1"
    modelType: 1
  }
}
rows {
  id: 620298
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_190_LOD1"
    physics: "SK_Wing_190_Physics"
    modelType: 2
    idleAnim: "AS_Wing_190_Idle_001"
  }
}
rows {
  id: 620299
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_190_LOD1"
    material: "Wing_LOD1:MI_Wing_190_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_190_2_HP01_LOD1;Wing_LOD2:MI_Wing_190_1_HP01_LOD2;Wing_Translucent_LOD2:MI_Wing_190_2_HP01_LOD2"
    physics: "SK_Wing_190_Physics"
    modelType: 2
    idleAnim: "AS_Wing_190_Idle_001_HP01"
  }
}
rows {
  id: 620300
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_190_LOD1"
    material: "Wing_LOD1:MI_Wing_190_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_190_2_HP02_LOD1;Wing_LOD2:MI_Wing_190_1_HP02_LOD2;Wing_Translucent_LOD2:MI_Wing_190_2_HP02_LOD2"
    physics: "SK_Wing_190_Physics"
    modelType: 2
    idleAnim: "AS_Wing_190_Idle_001_HP02"
  }
}
rows {
  id: 620301
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_191_LOD1"
    physics: "SK_Wing_191_Physics"
    modelType: 2
    idleAnim: "AS_Wing_191_Idle_001"
  }
}
rows {
  id: 620302
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_191_LOD1"
    material: "Wing_LOD1:MI_Wing_191_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_191_2_HP01_LOD1;Wing_LOD2:MI_Wing_191_1_HP01_LOD2;Wing_Translucent_LOD2:MI_Wing_191_2_HP01_LOD2"
    physics: "SK_Wing_191_Physics"
    modelType: 2
    idleAnim: "AS_Wing_191_Idle_001_HP01"
  }
}
rows {
  id: 620303
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_191_LOD1"
    material: "Wing_LOD1:MI_Wing_191_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_191_2_HP02_LOD1;Wing_LOD2:MI_Wing_191_1_HP02_LOD2;Wing_Translucent_LOD2:MI_Wing_191_2_HP02_LOD2"
    physics: "SK_Wing_191_Physics"
    modelType: 2
    idleAnim: "AS_Wing_191_Idle_001_HP02"
  }
}
rows {
  id: 620304
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_170_LOD1"
    modelType: 1
  }
}
rows {
  id: 620305
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_171_LOD1"
    modelType: 1
  }
}
rows {
  id: 620306
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_188_LOD1"
    modelType: 1
  }
}
rows {
  id: 620307
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_188_LOD1"
    material: "MI_Wing_188_HP01_LOD1;MI_Wing_188_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620308
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_188_LOD1"
    material: "MI_Wing_188_HP02_LOD1;MI_Wing_188_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620309
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_187_LOD1"
    modelType: 1
  }
}
rows {
  id: 620310
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_187_LOD1"
    material: "MI_Wing_187_HP01_LOD1;MI_Wing_187_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620311
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_187_LOD1"
    material: "MI_Wing_187_HP02_LOD1;MI_Wing_187_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620312
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_153_LOD1"
    modelType: 1
  }
}
rows {
  id: 620313
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_153_LOD1"
    material: "MI_Wing_153_HP01_LOD1;MI_Wing_153_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620314
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_153_LOD1"
    material: "MI_Wing_153_HP02_LOD1;MI_Wing_153_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620315
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_193_LOD1"
    modelType: 1
  }
}
rows {
  id: 620316
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_193_LOD1"
    material: "MI_Wing_193_HP01_LOD1;MI_Wing_193_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620317
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_193_LOD1"
    material: "MI_Wing_193_HP02_LOD1;MI_Wing_193_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620318
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_204_LOD1"
    modelType: 1
  }
}
rows {
  id: 620319
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_203_LOD1"
    modelType: 1
  }
}
rows {
  id: 620320
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_199_LOD1"
    modelType: 1
  }
}
rows {
  id: 620321
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_199_LOD1"
    material: "MI_Wing_199_HP01_LOD1;MI_Wing_199_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620322
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_199_LOD1"
    material: "MI_Wing_199_HP02_LOD1;MI_Wing_199_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620323
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_210_LOD1"
    modelType: 1
  }
}
rows {
  id: 620324
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_210_LOD1"
    material: "MI_Wing_210_HP01_LOD1;MI_Wing_210_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620325
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_210_LOD1"
    material: "MI_Wing_210_HP02_LOD1;MI_Wing_210_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620326
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_200_LOD1"
    modelType: 1
  }
}
rows {
  id: 620327
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_200_LOD1"
    material: "MI_Wing_200_HP01_LOD1;MI_Wing_200_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620328
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_200_LOD1"
    material: "MI_Wing_200_HP02_LOD1;MI_Wing_200_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620329
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_194_LOD1"
    modelType: 1
  }
}
rows {
  id: 620330
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_195_LOD1"
    modelType: 1
  }
}
rows {
  id: 620331
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_198_LOD1"
    modelType: 1
  }
}
rows {
  id: 620332
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_196_LOD1"
    modelType: 1
  }
}
rows {
  id: 620333
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_207_LOD1"
    modelType: 1
  }
}
rows {
  id: 620334
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_209_LOD1"
    modelType: 1
  }
}
rows {
  id: 620335
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_205_LOD1"
    modelType: 1
  }
}
rows {
  id: 620336
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_205_LOD1"
    material: "MI_Wing_205_HP01_LOD1;MI_Wing_205_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620337
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_205_LOD1"
    material: "MI_Wing_205_HP02_LOD1;MI_Wing_205_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620338
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_212_LOD1"
    modelType: 1
  }
}
rows {
  id: 620339
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_212_LOD1"
    material: "MI_Wing_212_HP01_LOD1;MI_Wing_212_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620340
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_212_LOD1"
    material: "MI_Wing_212_HP02_LOD1;MI_Wing_212_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620341
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_211_LOD1"
    modelType: 1
  }
}
rows {
  id: 620342
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_211_LOD1"
    material: "MI_Wing_211_HP01_LOD1;MI_Wing_211_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620343
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_211_LOD1"
    material: "MI_Wing_211_HP02_LOD1;MI_Wing_211_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620344
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_208_LOD1"
    modelType: 1
  }
}
rows {
  id: 620345
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_218_LOD1"
    modelType: 1
  }
}
rows {
  id: 620346
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_214_LOD1"
    modelType: 1
  }
}
rows {
  id: 620347
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_168_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_168_Idle_001"
  }
}
rows {
  id: 620348
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_168_LOD1"
    material: "Wing_LOD1:MI_Wing_168_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_168_2_HP01_LOD1;Wing_LOD2:MI_Wing_168_HP01_LOD2;"
    modelType: 2
    idleAnim: "AS_Wing_168_Idle_001_HP01"
  }
}
rows {
  id: 620349
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_168_LOD1"
    material: "Wing_LOD1:MI_Wing_168_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_168_2_HP02_LOD1;Wing_LOD2:MI_Wing_168_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_168_Idle_001_HP02"
  }
}
rows {
  id: 620350
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_213_LOD1"
    modelType: 1
  }
}
rows {
  id: 620351
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_213_LOD1"
    material: "MI_Wing_213_HP01_LOD1;MI_Wing_213_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620352
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_213_LOD1"
    material: "MI_Wing_213_HP02_LOD1;MI_Wing_213_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620353
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_201_LOD1"
    modelType: 1
  }
}
rows {
  id: 620354
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_201_LOD1"
    material: "MI_Wing_201_HP01_LOD1;MI_Wing_201_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620355
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_201_LOD1"
    material: "MI_Wing_201_HP02_LOD1;MI_Wing_201_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620356
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_223_LOD1"
    physics: "SK_Wing_223_Physics"
    modelType: 2
    idleAnim: "AS_Wing_223_Idle_001"
  }
}
rows {
  id: 620357
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_223_LOD1"
    material: "Wing_LOD1:MI_Wing_223_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_223_2_HP01_LOD1;Wing_LOD2:MI_Wing_223_HP01_LOD2"
    physics: "SK_Wing_223_Physics"
    modelType: 2
    idleAnim: "AS_Wing_223_Idle_001_HP01"
  }
}
rows {
  id: 620358
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_223_LOD1"
    material: "Wing_LOD1:MI_Wing_223_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_223_2_HP02_LOD1;Wing_LOD2:MI_Wing_223_HP02_LOD2"
    physics: "SK_Wing_223_Physics"
    modelType: 2
    idleAnim: "AS_Wing_223_Idle_001_HP02"
  }
}
rows {
  id: 620359
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_167_LOD1"
    physics: "SK_Wing_167_Physics"
    modelType: 2
    idleAnim: "AS_Wing_167_Idle_001"
  }
}
rows {
  id: 620360
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_167_LOD1"
    material: "Wing_LOD1:MI_Wing_167_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_167_2_HP01_LOD1;Wing_LOD2:MI_Wing_167_1_HP01_LOD2;Wing_Translucent_LOD2:MI_Wing_167_2_HP01_LOD2"
    physics: "SK_Wing_167_Physics"
    modelType: 2
    idleAnim: "AS_Wing_167_Idle_001_HP01"
  }
}
rows {
  id: 620361
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_167_LOD1"
    material: "Wing_LOD1:MI_Wing_167_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_167_2_HP02_LOD1;Wing_LOD2:MI_Wing_167_1_HP02_LOD2;Wing_Translucent_LOD2:MI_Wing_167_2_HP02_LOD2"
    physics: "SK_Wing_167_Physics"
    modelType: 2
    idleAnim: "AS_Wing_167_Idle_001_HP02"
  }
}
rows {
  id: 620362
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_226_LOD1"
    modelType: 1
  }
}
rows {
  id: 620363
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_226_LOD1"
    material: "MI_Wing_226_HP01_LOD1;MI_Wing_226_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620364
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_226_LOD1"
    material: "MI_Wing_226_HP02_LOD1;MI_Wing_226_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620365
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_206_LOD1"
    modelType: 1
  }
}
rows {
  id: 620366
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_197_LOD1"
    modelType: 1
  }
}
rows {
  id: 620367
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_217_LOD1"
    modelType: 1
  }
}
rows {
  id: 620368
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_192_LOD1"
    physics: "SK_Wing_192_Physics"
    modelType: 2
    idleAnim: "AS_Wing_192_Idle_001"
  }
}
rows {
  id: 620369
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_192_LOD1"
    material: "Wing_LOD1:MI_Wing_192_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_192_2_HP01_LOD1;Wing_LOD2:MI_Wing_192_HP01_LOD2"
    physics: "SK_Wing_192_Physics"
    modelType: 2
    idleAnim: "AS_Wing_192_Idle_001_HP01"
  }
}
rows {
  id: 620370
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_192_LOD1"
    material: "Wing_LOD1:MI_Wing_192_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_192_2_HP02_LOD1;Wing_LOD2:MI_Wing_192_HP02_LOD2"
    physics: "SK_Wing_192_Physics"
    modelType: 2
    idleAnim: "AS_Wing_192_Idle_001_HP02"
  }
}
rows {
  id: 620371
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_228_LOD1"
    modelType: 1
  }
}
rows {
  id: 620372
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_220_LOD1"
    modelType: 1
  }
}
rows {
  id: 620373
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_222_LOD1"
    modelType: 1
  }
}
rows {
  id: 620374
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_139_LOD1"
    modelType: 1
  }
}
rows {
  id: 620375
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_140_LOD1"
    modelType: 1
  }
}
rows {
  id: 620376
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_219_LOD1"
    modelType: 1
  }
}
rows {
  id: 620377
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_221_LOD1"
    modelType: 1
  }
}
rows {
  id: 620378
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_215_LOD1"
    modelType: 1
  }
}
rows {
  id: 620379
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_215_LOD1"
    material: "MI_Wing_215_HP01_LOD1;MI_Wing_215_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620380
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_215_LOD1"
    material: "MI_Wing_215_HP02_LOD1;MI_Wing_215_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620381
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_216_LOD1"
    modelType: 1
  }
}
rows {
  id: 620382
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_216_LOD1"
    material: "MI_Wing_216_HP01_LOD1;MI_Wing_216_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620383
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_216_LOD1"
    material: "MI_Wing_216_HP02_LOD1;MI_Wing_216_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620384
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_224_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_224_Idle_001"
  }
}
rows {
  id: 620385
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_224_LOD1"
    material: "Wing_1_LOD1:MI_Wing_224_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_224_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_224_3_HP01_LOD1;Wing_1_LOD2:MI_Wing_224_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_224_2_HP01_LOD2;Wing_3_LOD2:MI_Wing_224_3_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_224_Idle_001_HP01"
  }
}
rows {
  id: 620386
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_224_LOD1"
    material: "Wing_1_LOD1:MI_Wing_224_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_224_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_224_3_HP02_LOD1;Wing_1_LOD2:MI_Wing_224_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_224_2_HP02_LOD2;Wing_3_LOD2:MI_Wing_224_3_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_224_Idle_001_HP02"
  }
}
rows {
  id: 620387
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_225_LOD1"
    modelType: 1
  }
}
rows {
  id: 620388
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_225_LOD1"
    material: "MI_Wing_225_HP01_LOD1;MI_Wing_225_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620389
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_225_LOD1"
    material: "MI_Wing_225_HP02_LOD1;MI_Wing_225_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620390
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_234_LOD1"
    modelType: 1
  }
}
rows {
  id: 620391
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_234_LOD1"
    material: "MI_Wing_234_HP01_LOD1;MI_Wing_234_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620392
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_234_LOD1"
    material: "MI_Wing_234_HP02_LOD1;MI_Wing_234_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620393
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_235_LOD1"
    modelType: 1
  }
}
rows {
  id: 620394
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_236_LOD1"
    modelType: 1
  }
}
rows {
  id: 620395
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_238_LOD1"
    modelType: 1
  }
}
rows {
  id: 620396
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_237_LOD1"
    modelType: 1
  }
}
rows {
  id: 620397
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_240_LOD1"
    modelType: 1
  }
}
rows {
  id: 620398
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_229_LOD1"
    modelType: 1
  }
}
rows {
  id: 620399
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_229_LOD1"
    material: "MI_Wing_229_HP01_LOD1;MI_Wing_229_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620400
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_229_LOD1"
    material: "MI_Wing_229_HP02_LOD1;MI_Wing_229_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620401
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_230_LOD1"
    modelType: 1
  }
}
rows {
  id: 620402
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_227_LOD1"
    physics: "SK_Wing_227_Physics"
    modelType: 2
    idleAnim: "AS_Wing_227_Idle_001"
  }
}
rows {
  id: 620403
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_227_LOD1"
    material: "Wing_1_LOD1:MI_Wing_227_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_227_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_227_3_HP01_LOD1;Wing_4_LOD1:MI_Wing_227_4_HP01_LOD1;Wing_1_LOD2:MI_Wing_227_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_227_2_HP01_LOD2"
    physics: "SK_Wing_227_Physics"
    modelType: 2
    idleAnim: "AS_Wing_227_Idle_001_HP01"
  }
}
rows {
  id: 620404
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_227_LOD1"
    material: "Wing_1_LOD1:MI_Wing_227_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_227_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_227_3_HP02_LOD1;Wing_4_LOD1:MI_Wing_227_4_HP02_LOD1;Wing_1_LOD2:MI_Wing_227_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_227_2_HP02_LOD2"
    physics: "SK_Wing_227_Physics"
    modelType: 2
    idleAnim: "AS_Wing_227_Idle_001_HP02"
  }
}
rows {
  id: 620405
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_232_LOD1"
    modelType: 1
  }
}
rows {
  id: 620406
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_233_LOD1"
    modelType: 1
  }
}
rows {
  id: 620407
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_239_LOD1"
    modelType: 1
  }
}
rows {
  id: 620408
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_257_LOD1"
    modelType: 1
  }
}
rows {
  id: 620409
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_242_LOD1"
    modelType: 1
  }
}
rows {
  id: 620410
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_231_LOD1"
    modelType: 1
  }
}
rows {
  id: 620411
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_231_LOD1"
    material: "MI_Wing_231_HP01_LOD1;MI_Wing_231_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620412
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_231_LOD1"
    material: "MI_Wing_231_HP02_LOD1;MI_Wing_231_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620413
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_253_LOD1"
    modelType: 1
  }
}
rows {
  id: 620414
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_274_LOD1"
    modelType: 1
  }
}
rows {
  id: 620415
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_241_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_241_Idle_001"
  }
}
rows {
  id: 620416
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_241_LOD1"
    material: "Wing_1_LOD1:MI_Wing_241_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_241_2_HP01_LOD1;Wing_LOD2:MI_Wing_241_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_241_Idle_001_HP01"
  }
}
rows {
  id: 620417
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_241_LOD1"
    material: "Wing_1_LOD1:MI_Wing_241_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_241_2_HP02_LOD1;Wing_LOD2:MI_Wing_241_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_241_Idle_001_HP02"
  }
}
rows {
  id: 620418
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_243_LOD1"
    modelType: 1
  }
}
rows {
  id: 620419
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_243_LOD1"
    material: "MI_Wing_243_HP01_LOD1;MI_Wing_243_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620420
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_243_LOD1"
    material: "MI_Wing_243_HP02_LOD1;MI_Wing_243_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620421
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_245_LOD1"
    modelType: 1
  }
}
rows {
  id: 620422
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_245_LOD1"
    material: "MI_Wing_245_HP01_LOD1;MI_Wing_245_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620423
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_245_LOD1"
    material: "MI_Wing_245_HP02_LOD1;MI_Wing_245_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620424
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_247_LOD1"
    modelType: 1
  }
}
rows {
  id: 620425
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_247_LOD1"
    material: "MI_Wing_247_HP01_LOD1;MI_Wing_247_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620426
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_247_LOD1"
    material: "MI_Wing_247_HP02_LOD1;MI_Wing_247_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620427
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_268_LOD1"
    modelType: 1
  }
}
rows {
  id: 620428
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_258_LOD1"
    modelType: 1
  }
}
rows {
  id: 620429
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_255_LOD1"
    modelType: 1
  }
}
rows {
  id: 620430
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_255_LOD1"
    material: "MI_Wing_255_HP01_LOD1;MI_Wing_255_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620431
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_255_LOD1"
    material: "MI_Wing_255_HP02_LOD1;MI_Wing_255_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620432
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_240_LOD1"
    material: "MI_Wing_240_HP01_LOD1;MI_Wing_240_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620433
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_240_LOD1"
    material: "MI_Wing_240_HP02_LOD1;MI_Wing_240_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620434
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_248_LOD1"
    modelType: 1
  }
}
rows {
  id: 620435
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_248_LOD1"
    material: "MI_Wing_248_HP01_LOD1;MI_Wing_248_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620436
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_248_LOD1"
    material: "MI_Wing_248_HP02_LOD1;MI_Wing_248_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620437
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_249_LOD1"
    modelType: 1
  }
}
rows {
  id: 620438
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_249_LOD1"
    material: "Wing_1_LOD1:MI_Wing_249_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_249_2_HP01_LOD1;Wing_LOD2:MI_Wing_249_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620439
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_249_LOD1"
    material: "Wing_1_LOD1:MI_Wing_249_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_249_2_HP02_LOD1;Wing_LOD2:MI_Wing_249_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620440
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_250_LOD1"
    modelType: 1
  }
}
rows {
  id: 620441
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_250_LOD1"
    material: "MI_Wing_250_HP01_LOD1;MI_Wing_250_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620442
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_250_LOD1"
    material: "MI_Wing_250_HP02_LOD1;MI_Wing_250_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620443
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_244_LOD1"
    modelType: 1
  }
}
rows {
  id: 620444
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_244_LOD1"
    material: "Wing_1_LOD1:MI_Wing_244_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_244_2_HP01_LOD1;Wing_LOD2:MI_Wing_244_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620445
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_244_LOD1"
    material: "Wing_1_LOD1:MI_Wing_244_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_244_2_HP02_LOD1;Wing_LOD2:MI_Wing_244_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620446
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_254_LOD1"
    modelType: 1
  }
}
rows {
  id: 620447
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_251_LOD1"
    modelType: 1
  }
}
rows {
  id: 620448
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_246_LOD1"
    modelType: 1
  }
}
rows {
  id: 620449
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_259_LOD1"
    modelType: 1
  }
}
rows {
  id: 620450
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_256_LOD1"
    modelType: 1
  }
}
rows {
  id: 620451
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_281_LOD1"
    modelType: 1
  }
}
rows {
  id: 620452
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_261_LOD1"
    modelType: 1
  }
}
rows {
  id: 620453
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_260_LOD1"
    modelType: 1
  }
}
rows {
  id: 620454
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_269_LOD1"
    modelType: 1
  }
}
rows {
  id: 620455
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_291_LOD1"
    modelType: 1
  }
}
rows {
  id: 620456
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_291_LOD1"
    material: "Wing_LOD1:MI_Wing_291_HP01_LOD1;Wing_Opaque_LOD1:MI_Wing_291_1_HP01_LOD1;Wing_LOD2:MI_Wing_291_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620457
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_291_LOD1"
    material: "Wing_LOD1:MI_Wing_291_HP02_LOD1;Wing_Opaque_LOD1:MI_Wing_291_1_HP02_LOD1;Wing_LOD2:MI_Wing_291_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620458
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_264_LOD1"
    modelType: 1
  }
}
rows {
  id: 620459
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_265_LOD1"
    modelType: 1
  }
}
rows {
  id: 620460
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_266_LOD1"
    modelType: 1
  }
}
rows {
  id: 620461
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_267_LOD1"
    modelType: 1
  }
}
rows {
  id: 620462
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_263_LOD1"
    modelType: 1
  }
}
rows {
  id: 620463
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_263_LOD1"
    material: "MI_Wing_263_HP01_LOD1;MI_Wing_263_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620464
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_263_LOD1"
    material: "MI_Wing_263_HP02_LOD1;MI_Wing_263_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620465
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_252_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_252_Idle_001"
  }
}
rows {
  id: 620466
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_252_LOD1"
    material: "Wing_1_LOD1:MI_Wing_252_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_252_2_HP01_LOD1;Wing_LOD2:MI_Wing_252_1_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_252_Idle_001_HP01"
  }
}
rows {
  id: 620467
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_252_LOD1"
    material: "Wing_1_LOD1:MI_Wing_252_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_252_2_HP02_LOD1;Wing_LOD2:MI_Wing_252_1_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_252_Idle_001_HP02"
  }
}
rows {
  id: 620468
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_290_LOD1"
    modelType: 1
  }
}
rows {
  id: 620469
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_301_LOD1"
    modelType: 1
  }
}
rows {
  id: 620470
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_302_LOD1"
    modelType: 1
  }
}
rows {
  id: 620471
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_282_LOD1"
    modelType: 1
  }
}
rows {
  id: 620472
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_309_LOD1"
    modelType: 1
  }
}
rows {
  id: 620473
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_309_LOD1"
    material: "MI_Wing_309_HP01_LOD1;MI_Wing_309_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620474
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_309_LOD1"
    material: "MI_Wing_309_HP02_LOD1;MI_Wing_309_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620475
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_262_LOD1"
    modelType: 1
  }
}
rows {
  id: 620476
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_262_LOD1"
    material: "MI_Wing_262_HP01_LOD1;MI_Wing_262_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620477
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_262_LOD1"
    material: "MI_Wing_262_HP02_LOD1;MI_Wing_262_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620478
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_278_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_278_Idle_001"
  }
}
rows {
  id: 620479
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_278_LOD1"
    material: "Wing_1_LOD1:MI_Wing_278_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_278_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_278_3_HP01_LOD1;Wing_4_LOD1:MI_Wing_278_4_HP01_LOD1;Wing_1_LOD2:MI_Wing_278_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_278_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_278_Idle_001_HP01"
  }
}
rows {
  id: 620480
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_278_LOD1"
    material: "Wing_1_LOD1:MI_Wing_278_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_278_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_278_3_HP02_LOD1;Wing_4_LOD1:MI_Wing_278_4_HP02_LOD1;Wing_1_LOD2:MI_Wing_278_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_278_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_278_Idle_001_HP02"
  }
}
rows {
  id: 620481
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_286_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_286_Idle_001"
  }
}
rows {
  id: 620482
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_286_LOD1"
    material: "Wing_1_LOD1:MI_Wing_286_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_286_2_HP01_LOD1;Wing_LOD2:MI_Wing_286_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_286_Idle_001_HP01"
  }
}
rows {
  id: 620483
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_286_LOD1"
    material: "Wing_1_LOD1:MI_Wing_286_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_286_2_HP02_LOD1;Wing_LOD2:MI_Wing_286_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_286_Idle_001_HP02"
  }
}
rows {
  id: 620484
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_319_LOD1"
    modelType: 1
  }
}
rows {
  id: 620485
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_298_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_298_Idle_001"
  }
}
rows {
  id: 620486
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_298_LOD1"
    material: "Wing_1_LOD1:MI_Wing_298_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_298_2_HP01_LOD1;Wing_LOD2:MI_Wing_298_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_298_Idle_001_HP01"
  }
}
rows {
  id: 620487
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_298_LOD1"
    material: "Wing_1_LOD1:MI_Wing_298_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_298_2_HP02_LOD1;Wing_LOD2:MI_Wing_298_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_298_Idle_001_HP02"
  }
}
rows {
  id: 620488
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_275_LOD1"
    modelType: 1
  }
}
rows {
  id: 620489
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_279_LOD1"
    modelType: 1
  }
}
rows {
  id: 620490
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_277_LOD1"
    modelType: 1
  }
}
rows {
  id: 620491
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_277_LOD1"
    material: "MI_Wing_277_HP01_LOD1;MI_Wing_277_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620492
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_277_LOD1"
    material: "MI_Wing_277_HP02_LOD1;MI_Wing_277_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620493
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_287_LOD1"
    modelType: 1
  }
}
rows {
  id: 620494
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_320_LOD1"
    modelType: 1
  }
}
rows {
  id: 620495
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_321_LOD1"
    modelType: 1
  }
}
rows {
  id: 620496
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_287_LOD1"
    material: "Wing_LOD1:MI_Wing_287_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_287_2_HP01_LOD1;Wing_LOD2:MI_Wing_287_1_HP01_LOD2;Wing_Translucent_LOD2:MI_Wing_287_2_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620497
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_287_LOD1"
    material: "Wing_LOD1:MI_Wing_287_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_287_2_HP02_LOD1;Wing_LOD2:MI_Wing_287_1_HP02_LOD2;Wing_Translucent_LOD2:MI_Wing_287_2_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620498
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_306_LOD1"
    modelType: 1
  }
}
rows {
  id: 620499
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_307_LOD1"
    modelType: 1
  }
}
rows {
  id: 620500
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_285_LOD1"
    modelType: 1
  }
}
rows {
  id: 620501
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_285_LOD1"
    material: "MI_Wing_285_HP01_LOD1;MI_Wing_285_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620502
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_285_LOD1"
    material: "MI_Wing_285_HP02_LOD1;MI_Wing_285_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620503
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_308_LOD1"
    modelType: 1
  }
}
rows {
  id: 620504
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_308_LOD1"
    material: "MI_Wing_308_HP01_LOD1;MI_Wing_308_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620505
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_308_LOD1"
    material: "MI_Wing_308_HP02_LOD1;MI_Wing_308_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620506
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_284_LOD1"
    modelType: 1
  }
}
rows {
  id: 620507
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_284_LOD1"
    material: "MI_Wing_284_HP01_LOD1;MI_Wing_284_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620508
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_284_LOD1"
    material: "MI_Wing_284_HP02_LOD1;MI_Wing_284_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620509
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_317_LOD1"
    modelType: 1
  }
}
rows {
  id: 620510
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_317_LOD1"
    material: "MI_Wing_317_HP01_LOD1;MI_Wing_317_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620511
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_317_LOD1"
    material: "MI_Wing_317_HP02_LOD1;MI_Wing_317_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620512
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_270_LOD1"
    modelType: 1
  }
}
rows {
  id: 620513
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_271_LOD1"
    modelType: 1
  }
}
rows {
  id: 620514
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_272_LOD1"
    modelType: 1
  }
}
rows {
  id: 620515
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_273_LOD1"
    modelType: 1
  }
}
rows {
  id: 620516
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_276_LOD1"
    modelType: 1
  }
}
rows {
  id: 620517
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_299_LOD1"
    modelType: 1
  }
}
rows {
  id: 620518
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_299_LOD1"
    material: "Wing_1_LOD1:MI_Wing_299_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_299_2_HP01_LOD1;Wing_LOD2:MI_Wing_299_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620519
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_299_LOD1"
    material: "Wing_1_LOD1:MI_Wing_299_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_299_2_HP02_LOD1;Wing_LOD2:MI_Wing_299_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620520
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_304_LOD1"
    modelType: 1
  }
}
rows {
  id: 620521
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_304_LOD1"
    material: "MI_Wing_304_HP01_LOD1;MI_Wing_304_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620522
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_304_LOD1"
    material: "MI_Wing_304_HP02_LOD1;MI_Wing_304_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620523
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_313_LOD1"
    modelType: 1
  }
}
rows {
  id: 620524
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_313_LOD1"
    material: "MI_Wing_313_HP01_LOD1;MI_Wing_313_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620525
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_313_LOD1"
    material: "MI_Wing_313_HP02_LOD1;MI_Wing_313_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620526
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_288_LOD1"
    modelType: 1
  }
}
rows {
  id: 620527
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_288_LOD1"
    material: "MI_Wing_288_HP01_LOD1;MI_Wing_288_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620528
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_288_LOD1"
    material: "MI_Wing_288_HP02_LOD1;MI_Wing_288_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620529
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_289_LOD1"
    modelType: 1
  }
}
rows {
  id: 620530
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_297_LOD1"
    modelType: 1
  }
}
rows {
  id: 620531
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_294_LOD1"
    modelType: 1
  }
}
rows {
  id: 620532
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_295_LOD1"
    modelType: 1
  }
}
rows {
  id: 620533
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_296_LOD1"
    modelType: 1
  }
}
rows {
  id: 620534
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_303_LOD1"
    modelType: 1
  }
}
rows {
  id: 620535
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_316_LOD1"
    modelType: 1
  }
}
rows {
  id: 620536
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_311_LOD1"
    modelType: 1
  }
}
rows {
  id: 620537
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_312_LOD1"
    modelType: 1
  }
}
rows {
  id: 620538
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_312_LOD1"
    material: "MI_Wing_312_HP01_LOD1;MI_Wing_312_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620539
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_312_LOD1"
    material: "MI_Wing_312_HP02_LOD1;MI_Wing_312_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620540
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_293_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_293_Idle_001"
  }
}
rows {
  id: 620541
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_293_LOD1"
    material: "Wing_1_LOD1:MI_Wing_293_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_293_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_293_3_HP01_LOD1;Wing_1_LOD2:MI_Wing_293_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_293_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_293_Idle_001_HP01"
  }
}
rows {
  id: 620542
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_293_LOD1"
    material: "Wing_1_LOD1:MI_Wing_293_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_293_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_293_3_HP02_LOD1;Wing_1_LOD2:MI_Wing_293_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_293_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_293_Idle_001_HP02"
  }
}
rows {
  id: 620543
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_310_LOD1"
    modelType: 1
  }
}
rows {
  id: 620544
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_310_LOD1"
    material: "Wing_LOD1:MI_Wing_310_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_310_2_HP01_LOD1;Wing_LOD2:MI_Wing_310_1_HP01_LOD2;Wing_Translucent_LOD2:MI_Wing_310_2_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620545
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_310_LOD1"
    material: "Wing_LOD1:MI_Wing_310_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_310_2_HP02_LOD1;Wing_LOD2:MI_Wing_310_1_HP02_LOD2;Wing_Translucent_LOD2:MI_Wing_310_2_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620546
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_333_LOD1"
    modelType: 1
  }
}
rows {
  id: 620547
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_333_LOD1"
    material: "MI_Wing_333_HP01_LOD1;MI_Wing_333_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620548
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_333_LOD1"
    material: "MI_Wing_333_HP02_LOD1;MI_Wing_333_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620549
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_358_LOD1"
    modelType: 1
  }
}
rows {
  id: 620550
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_315_LOD1"
    modelType: 1
  }
}
rows {
  id: 620551
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_315_LOD1"
    material: "MI_Wing_315_HP01_LOD1;MI_Wing_315_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620552
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_315_LOD1"
    material: "MI_Wing_315_HP02_LOD1;MI_Wing_315_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620553
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_314_LOD1"
    modelType: 1
  }
}
rows {
  id: 620554
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_314_LOD1"
    material: "MI_Wing_314_HP01_LOD1;MI_Wing_314_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620555
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_314_LOD1"
    material: "MI_Wing_314_HP02_LOD1;MI_Wing_314_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620556
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_347_LOD1"
    modelType: 1
  }
}
rows {
  id: 620557
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_347_LOD1"
    material: "MI_Wing_347_HP01_LOD1;MI_Wing_347_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620558
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_347_LOD1"
    material: "MI_Wing_347_HP02_LOD1;MI_Wing_347_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620559
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_330_LOD1"
    modelType: 1
  }
}
rows {
  id: 620560
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_322_LOD1"
    modelType: 1
  }
}
rows {
  id: 620561
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_323_LOD1"
    modelType: 1
  }
}
rows {
  id: 620562
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_369_LOD1"
    modelType: 1
  }
}
rows {
  id: 620563
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_373_LOD1"
    modelType: 1
  }
}
rows {
  id: 620564
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_363_LOD1"
    modelType: 1
  }
}
rows {
  id: 620565
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_363_LOD1"
    material: "MI_Wing_363_HP01_LOD1;MI_Wing_363_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620566
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_363_LOD1"
    material: "MI_Wing_363_HP02_LOD1;MI_Wing_363_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620567
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_372_LOD1"
    modelType: 1
  }
}
rows {
  id: 620568
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_372_LOD1"
    material: "MI_Wing_372_HP01_LOD1;MI_Wing_372_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620569
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_372_LOD1"
    material: "MI_Wing_372_HP02_LOD1;MI_Wing_372_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620570
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_329_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_329_Idle_001"
  }
}
rows {
  id: 620571
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_329_LOD1"
    material: "Wing_1_LOD1:MI_Wing_329_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_329_2_HP01_LOD1;Wing_1_LOD2:MI_Wing_329_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_329_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_329_Idle_001_HP01"
  }
}
rows {
  id: 620572
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_329_LOD1"
    material: "Wing_1_LOD1:MI_Wing_329_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_329_2_HP02_LOD1;Wing_1_LOD2:MI_Wing_329_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_329_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_329_Idle_001_HP02"
  }
}
rows {
  id: 620573
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_370_LOD1"
    modelType: 1
  }
}
rows {
  id: 620574
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_370_LOD1"
    material: "Wing_1_LOD1:MI_Wing_370_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_370_2_HP01_LOD1;Wing_1_LOD2:MI_Wing_370_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_370_2_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620575
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_370_LOD1"
    material: "Wing_1_LOD1:MI_Wing_370_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_370_2_HP02_LOD1;Wing_1_LOD2:MI_Wing_370_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_370_2_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620576
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_367_LOD1"
    modelType: 1
  }
}
rows {
  id: 620577
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_367_LOD1"
    material: "MI_Wing_367_HP01_LOD1;MI_Wing_367_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620578
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_367_LOD1"
    material: "MI_Wing_367_HP02_LOD1;MI_Wing_367_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620579
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_324_LOD1"
    modelType: 1
  }
}
rows {
  id: 620580
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_324_LOD1"
    material: "MI_Wing_324_HP01_LOD1;MI_Wing_324_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620581
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_324_LOD1"
    material: "MI_Wing_324_HP02_LOD1;MI_Wing_324_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620582
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_392_LOD1"
    modelType: 1
  }
}
rows {
  id: 620583
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_392_LOD1"
    material: "MI_Wing_392_HP01_LOD1;MI_Wing_392_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620584
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_392_LOD1"
    material: "MI_Wing_392_HP02_LOD1;MI_Wing_392_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620585
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_393_LOD1"
    modelType: 1
  }
}
rows {
  id: 620586
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_393_LOD1"
    material: "MI_Wing_393_HP01_LOD1;MI_Wing_393_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620587
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_393_LOD1"
    material: "MI_Wing_393_HP02_LOD1;MI_Wing_393_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620588
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_331_LOD1"
    physics: "SK_Wing_331_Physics"
    modelType: 2
    idleAnim: "AS_Wing_331_Idle_001"
  }
}
rows {
  id: 620589
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_331_LOD1"
    material: "Wing_1_LOD1:MI_Wing_331_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_331_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_331_3_HP01_LOD1;Wing_1_LOD2:MI_Wing_331_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_331_2_HP01_LOD2"
    physics: "SK_Wing_331_Physics"
    modelType: 2
    idleAnim: "AS_Wing_331_Idle_001_HP01"
  }
}
rows {
  id: 620590
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_331_LOD1"
    material: "Wing_1_LOD1:MI_Wing_331_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_331_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_331_3_HP02_LOD1;Wing_1_LOD2:MI_Wing_331_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_331_2_HP02_LOD2"
    physics: "SK_Wing_331_Physics"
    modelType: 2
    idleAnim: "AS_Wing_331_Idle_001_HP02"
  }
}
rows {
  id: 620591
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_351_LOD1"
    modelType: 1
  }
}
rows {
  id: 620592
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_351_LOD1"
    material: "MI_Wing_351_HP01_LOD1;MI_Wing_351_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620593
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_351_LOD1"
    material: "MI_Wing_351_HP02_LOD1;MI_Wing_351_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620594
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_352_LOD1"
    modelType: 1
  }
}
rows {
  id: 620595
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_352_LOD1"
    material: "MI_Wing_352_HP01_LOD1;MI_Wing_352_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620596
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_352_LOD1"
    material: "MI_Wing_352_HP02_LOD1;MI_Wing_352_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620597
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_353_LOD1"
    modelType: 1
  }
}
rows {
  id: 620598
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_353_LOD1"
    material: "MI_Wing_353_HP01_LOD1;MI_Wing_353_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620599
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_353_LOD1"
    material: "MI_Wing_353_HP02_LOD1;MI_Wing_353_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620600
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_354_LOD1"
    modelType: 1
  }
}
rows {
  id: 620601
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_354_LOD1"
    material: "MI_Wing_354_HP01_LOD1;MI_Wing_354_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620602
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_354_LOD1"
    material: "MI_Wing_354_HP02_LOD1;MI_Wing_354_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620603
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_344_LOD1"
    modelType: 1
  }
}
rows {
  id: 620604
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_326_LOD1"
    modelType: 1
  }
}
rows {
  id: 620605
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_332_LOD1"
    modelType: 1
  }
}
rows {
  id: 620606
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_332_LOD1"
    material: "MI_Wing_332_HP01_LOD1;MI_Wing_332_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620607
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_332_LOD1"
    material: "MI_Wing_332_HP02_LOD1;MI_Wing_332_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620608
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_334_LOD1"
    modelType: 1
  }
}
rows {
  id: 620609
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_334_LOD1"
    material: "MI_Wing_334_HP01_LOD1;MI_Wing_334_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620610
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_334_LOD1"
    material: "MI_Wing_334_HP02_LOD1;MI_Wing_334_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620611
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_371_LOD1"
    modelType: 1
  }
}
rows {
  id: 620612
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_371_LOD1"
    material: "MI_Wing_371_HP01_LOD1;MI_Wing_371_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620613
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_371_LOD1"
    material: "MI_Wing_371_HP02_LOD1;MI_Wing_371_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620614
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_325_LOD1"
    modelType: 1
  }
}
rows {
  id: 620615
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_348_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_348_Idle_001"
  }
}
rows {
  id: 620616
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_348_LOD1"
    material: "Wing_1_LOD1:MI_Wing_348_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_348_2_HP01_LOD1;Wing_LOD2:MI_Wing_348_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_348_Idle_001_HP01"
  }
}
rows {
  id: 620617
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_348_LOD1"
    material: "Wing_1_LOD1:MI_Wing_348_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_348_2_HP02_LOD1;Wing_LOD2:MI_Wing_348_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_348_Idle_001_HP02"
  }
}
rows {
  id: 620618
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_356_LOD1"
    modelType: 1
  }
}
rows {
  id: 620619
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_356_LOD1"
    material: "MI_Wing_356_HP01_LOD1;MI_Wing_356_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620620
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_356_LOD1"
    material: "MI_Wing_356_HP02_LOD1;MI_Wing_356_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620621
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_327_LOD1"
    modelType: 1
  }
}
rows {
  id: 620622
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_350_LOD1"
    modelType: 1
  }
}
rows {
  id: 620623
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_328_LOD1"
    modelType: 1
  }
}
rows {
  id: 620624
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_300_LOD1"
    modelType: 1
  }
}
rows {
  id: 620625
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_305_LOD1"
    modelType: 1
  }
}
rows {
  id: 620626
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_305_LOD1"
    material: "MI_Wing_305_HP01_LOD1;MI_Wing_305_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620627
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_305_LOD1"
    material: "MI_Wing_305_HP02_LOD1;MI_Wing_305_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620628
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_318_LOD1"
    modelType: 1
  }
}
rows {
  id: 620629
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_318_LOD1"
    material: "MI_Wing_318_HP01_LOD1;MI_Wing_318_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620630
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_318_LOD1"
    material: "MI_Wing_318_HP02_LOD1;MI_Wing_318_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620631
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_400_LOD1"
    modelType: 1
  }
}
rows {
  id: 620632
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_399_LOD1"
    modelType: 1
  }
}
rows {
  id: 620633
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_401_LOD1"
    modelType: 1
  }
}
rows {
  id: 620634
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_401_LOD1"
    material: "Wing_1_LOD1:MI_Wing_401_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_401_2_HP01_LOD1;Wing_1_LOD2:MI_Wing_401_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_401_2_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620635
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_401_LOD1"
    material: "Wing_1_LOD1:MI_Wing_401_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_401_2_HP02_LOD1;Wing_1_LOD2:MI_Wing_401_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_401_2_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620636
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_409_LOD1"
    modelType: 1
  }
}
rows {
  id: 620637
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_349_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_349_Idle_001"
  }
}
rows {
  id: 620638
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_349_LOD1"
    material: "Wing_1_LOD1:MI_Wing_349_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_349_2_HP01_LOD1;Wing_LOD2:MI_Wing_349_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_349_Idle_001_HP01"
  }
}
rows {
  id: 620639
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_349_LOD1"
    material: "Wing_1_LOD1:MI_Wing_349_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_349_2_HP02_LOD1;Wing_LOD2:MI_Wing_349_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_349_Idle_001_HP02"
  }
}
rows {
  id: 620640
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_374_LOD1"
    modelType: 1
  }
}
rows {
  id: 620641
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_374_LOD1"
    material: "MI_Wing_374_HP01_LOD1;MI_Wing_374_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620642
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_374_LOD1"
    material: "MI_Wing_374_HP02_LOD1;MI_Wing_374_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620643
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_380_LOD1"
    modelType: 1
  }
}
rows {
  id: 620644
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_380_LOD1"
    material: "MI_Wing_380_HP01_LOD1;MI_Wing_380_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620645
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_380_LOD1"
    material: "MI_Wing_380_HP02_LOD1;MI_Wing_380_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620646
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_335_LOD1"
    modelType: 1
  }
}
rows {
  id: 620647
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_365_LOD1"
    modelType: 1
  }
}
rows {
  id: 620648
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_336_LOD1"
    modelType: 1
  }
}
rows {
  id: 620649
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_337_LOD1"
    modelType: 1
  }
}
rows {
  id: 620650
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_394_LOD1"
    modelType: 1
  }
}
rows {
  id: 620651
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_411_LOD1"
    modelType: 1
  }
}
rows {
  id: 620652
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_411_LOD1"
    material: "MI_Wing_411_HP01_LOD1;MI_Wing_411_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620653
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_411_LOD1"
    material: "MI_Wing_411_HP02_LOD1;MI_Wing_411_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620654
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_412_LOD1"
    modelType: 1
  }
}
rows {
  id: 620655
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_412_LOD1"
    material: "MI_Wing_412_HP01_LOD1;MI_Wing_412_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620656
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_412_LOD1"
    material: "MI_Wing_412_HP02_LOD1;MI_Wing_412_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620657
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_368_LOD1"
    modelType: 1
  }
}
rows {
  id: 620658
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_368_LOD1"
    material: "MI_Wing_368_HP01_LOD1;MI_Wing_368_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620659
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_368_LOD1"
    material: "MI_Wing_368_HP02_LOD1;MI_Wing_368_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620660
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_385_LOD1"
    modelType: 1
  }
}
rows {
  id: 620661
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_385_LOD1"
    material: "MI_Wing_385_HP01_LOD1;MI_Wing_385_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620662
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_385_LOD1"
    material: "MI_Wing_385_HP02_LOD1;MI_Wing_385_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620663
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_364_LOD1"
    modelType: 1
  }
}
rows {
  id: 620664
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_364_LOD1"
    material: "MI_Wing_364_HP01_LOD1;MI_Wing_364_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620665
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_364_LOD1"
    material: "MI_Wing_364_HP02_LOD1;MI_Wing_364_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620666
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_386_LOD1"
    modelType: 1
  }
}
rows {
  id: 620667
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_386_LOD1"
    material: "MI_Wing_386_HP01_LOD1;MI_Wing_386_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620668
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_386_LOD1"
    material: "MI_Wing_386_HP02_LOD1;MI_Wing_386_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620669
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_388_LOD1"
    modelType: 1
  }
}
rows {
  id: 620670
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_341_LOD1"
    modelType: 1
  }
}
rows {
  id: 620671
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_340_LOD1"
    modelType: 1
  }
}
rows {
  id: 620672
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_410_LOD1"
    physics: "SK_Wing_410_Physics"
    modelType: 2
    idleAnim: "AS_Wing_410_Idle_001"
  }
}
rows {
  id: 620673
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_410_LOD1"
    material: "Wing_1_LOD1:MI_Wing_410_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_410_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_410_3_HP01_LOD1;Wing_LOD2:MI_Wing_410_HP01_LOD2"
    physics: "SK_Wing_410_Physics"
    modelType: 2
    idleAnim: "AS_Wing_410_Idle_001_HP01"
  }
}
rows {
  id: 620674
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_410_LOD1"
    material: "Wing_1_LOD1:MI_Wing_410_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_410_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_410_3_HP02_LOD1;Wing_LOD2:MI_Wing_410_HP02_LOD2"
    physics: "SK_Wing_410_Physics"
    modelType: 2
    idleAnim: "AS_Wing_410_Idle_001_HP02"
  }
}
rows {
  id: 620675
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_402_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_402_Idle_001"
  }
}
rows {
  id: 620676
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_402_LOD1"
    material: "MI_Wing_402_HP01_LOD1;MI_Wing_402_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_402_Idle_001_HP01"
  }
}
rows {
  id: 620677
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_402_LOD1"
    material: "MI_Wing_402_HP02_LOD1;MI_Wing_402_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_402_Idle_001_HP02"
  }
}
rows {
  id: 620678
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_346_LOD1"
    modelType: 1
  }
}
rows {
  id: 620679
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_362_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_362_Idle_001"
  }
}
rows {
  id: 620680
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_362_LOD1"
    material: "Wing_1_LOD1:MI_Wing_362_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_362_2_HP01_LOD1;Wing_1_LOD2:MI_Wing_362_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_362_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_362_Idle_001_HP01"
  }
}
rows {
  id: 620681
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_362_LOD1"
    material: "Wing_1_LOD1:MI_Wing_362_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_362_2_HP02_LOD1;Wing_1_LOD2:MI_Wing_362_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_362_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_362_Idle_001_HP02"
  }
}
rows {
  id: 620682
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_408_LOD1"
    modelType: 1
  }
}
rows {
  id: 620683
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_408_LOD1"
    material: "MI_Wing_408_HP01_LOD1;MI_Wing_408_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620684
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_408_LOD1"
    material: "MI_Wing_408_HP02_LOD1;MI_Wing_408_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620685
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_343_LOD1"
    modelType: 1
  }
}
rows {
  id: 620686
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_430_LOD1"
    modelType: 1
  }
}
rows {
  id: 620687
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_431_LOD1"
    modelType: 1
  }
}
rows {
  id: 620688
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_395_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_395_Idle_001"
  }
}
rows {
  id: 620689
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_395_LOD1"
    material: "Wing_1_LOD1:MI_Wing_395_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_395_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_395_3_HP01_LOD1;Wing_1_LOD2:MI_Wing_395_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_395_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_395_Idle_001_HP01"
  }
}
rows {
  id: 620690
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_395_LOD1"
    material: "Wing_1_LOD1:MI_Wing_395_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_395_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_395_3_HP02_LOD1;Wing_1_LOD2:MI_Wing_395_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_395_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_395_Idle_001_HP02"
  }
}
rows {
  id: 620691
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_292_LOD1"
    modelType: 1
  }
}
rows {
  id: 620692
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_292_LOD1"
    material: "MI_Wing_292_HP01_LOD1;MI_Wing_292_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620693
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_292_LOD1"
    material: "MI_Wing_292_HP02_LOD1;MI_Wing_292_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620694
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_376_LOD1"
    modelType: 1
  }
}
rows {
  id: 620695
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_376_LOD1"
    material: "Wing_LOD1:MI_Wing_376_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_376_2_HP01_LOD1;Wing_LOD2:MI_Wing_376_1_HP01_LOD2;Wing_Translucent_LOD2:MI_Wing_376_2_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620696
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_376_LOD1"
    material: "Wing_LOD1:MI_Wing_376_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_376_2_HP02_LOD1;Wing_LOD2:MI_Wing_376_1_HP02_LOD2;Wing_Translucent_LOD2:MI_Wing_376_2_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620697
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_366_LOD1"
    modelType: 1
  }
}
rows {
  id: 620698
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_342_LOD1"
    modelType: 1
  }
}
rows {
  id: 620699
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_416_LOD1"
    modelType: 1
  }
}
rows {
  id: 620700
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_416_LOD1"
    material: "MI_Wing_416_HP01_LOD1;MI_Wing_416_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620701
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_416_LOD1"
    material: "MI_Wing_416_HP02_LOD1;MI_Wing_416_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620702
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_415_LOD1"
    modelType: 1
  }
}
rows {
  id: 620703
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_415_LOD1"
    material: "MI_Wing_415_HP01_LOD1;MI_Wing_415_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620704
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_415_LOD1"
    material: "MI_Wing_415_HP02_LOD1;MI_Wing_415_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620705
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_424_LOD1"
    modelType: 1
  }
}
rows {
  id: 620706
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_424_LOD1"
    material: "MI_Wing_424_HP01_LOD1;MI_Wing_424_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620707
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_424_LOD1"
    material: "MI_Wing_424_HP02_LOD1;MI_Wing_424_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620708
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_432_LOD1"
    modelType: 1
  }
}
rows {
  id: 620709
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_355_LOD1"
    modelType: 1
  }
}
rows {
  id: 620710
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_355_LOD1"
    material: "MI_Wing_355_HP01_LOD1;MI_Wing_355_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620711
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_355_LOD1"
    material: "MI_Wing_355_HP02_LOD1;MI_Wing_355_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620712
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_397_LOD1"
    modelType: 1
  }
}
rows {
  id: 620713
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_397_LOD1"
    material: "MI_Wing_397_HP01_LOD1;MI_Wing_397_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620714
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_397_LOD1"
    material: "MI_Wing_397_HP02_LOD1;MI_Wing_397_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620715
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_403_LOD1"
    modelType: 1
  }
}
rows {
  id: 620716
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_403_LOD1"
    material: "MI_Wing_403_HP01_LOD1;MI_Wing_403_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620717
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_403_LOD1"
    material: "MI_Wing_403_HP02_LOD1;MI_Wing_403_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620718
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_381_LOD1"
    modelType: 1
  }
}
rows {
  id: 620719
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_381_LOD1"
    material: "Wing_LOD1:MI_Wing_381_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_381_2_HP01_LOD1;Wing_LOD2:MI_Wing_381_1_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620720
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_381_LOD1"
    material: "Wing_LOD1:MI_Wing_381_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_381_2_HP02_LOD1;Wing_LOD2:MI_Wing_381_1_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620721
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_339_LOD1"
    modelType: 1
  }
}
rows {
  id: 620722
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_338_LOD1"
    modelType: 1
  }
}
rows {
  id: 620723
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_407_LOD1"
    modelType: 1
  }
}
rows {
  id: 620724
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_345_LOD1"
    modelType: 1
  }
}
rows {
  id: 620725
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_345_LOD1"
    material: "MI_Wing_345_HP01_LOD1;MI_Wing_345_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620726
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_345_LOD1"
    material: "MI_Wing_345_HP02_LOD1;MI_Wing_345_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620727
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_357_LOD1"
    modelType: 1
  }
}
rows {
  id: 620728
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_357_LOD1"
    material: "MI_Wing_357_HP01_LOD1;MI_Wing_357_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620729
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_357_LOD1"
    material: "MI_Wing_357_HP02_LOD1;MI_Wing_357_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620730
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_442_LOD1"
    modelType: 1
  }
}
rows {
  id: 620731
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_443_LOD1"
    modelType: 1
  }
}
rows {
  id: 620732
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_414_LOD1"
    modelType: 1
  }
}
rows {
  id: 620733
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_404_LOD1"
    modelType: 1
  }
}
rows {
  id: 620734
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_425_LOD1"
    physics: "SK_Wing_425_Physics"
    modelType: 2
    idleAnim: "AS_Wing_425_Idle_001"
  }
}
rows {
  id: 620735
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_425_LOD1"
    material: "Wing_1_LOD1:MI_Wing_425_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_425_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_425_3_HP01_LOD1;Wing_LOD2:MI_Wing_425_HP01_LOD2"
    physics: "SK_Wing_425_Physics"
    modelType: 2
    idleAnim: "AS_Wing_425_Idle_001_HP01"
  }
}
rows {
  id: 620736
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_425_LOD1"
    material: "Wing_1_LOD1:MI_Wing_425_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_425_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_425_3_HP02_LOD1;Wing_LOD2:MI_Wing_425_HP02_LOD2"
    physics: "SK_Wing_425_Physics"
    modelType: 2
    idleAnim: "AS_Wing_425_Idle_001_HP02"
  }
}
rows {
  id: 620737
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_405_LOD1"
    modelType: 1
  }
}
rows {
  id: 620738
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_405_LOD1"
    material: "MI_Wing_405_HP01_LOD1;MI_Wing_405_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620739
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_405_LOD1"
    material: "MI_Wing_405_HP02_LOD1;MI_Wing_405_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620740
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_451_LOD1"
    modelType: 1
  }
}
rows {
  id: 620741
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_451_LOD1"
    material: "MI_Wing_451_HP01_LOD1;MI_Wing_451_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620742
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_451_LOD1"
    material: "MI_Wing_451_HP02_LOD1;MI_Wing_451_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620743
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_436_LOD1"
    modelType: 1
  }
}
rows {
  id: 620744
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_436_LOD1"
    material: "MI_Wing_436_HP01_LOD1;MI_Wing_436_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620745
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_436_LOD1"
    material: "MI_Wing_436_HP02_LOD1;MI_Wing_436_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620746
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_417_LOD1"
    modelType: 1
  }
}
rows {
  id: 620747
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_417_LOD1"
    material: "MI_Wing_417_HP01_LOD1;MI_Wing_417_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620748
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_417_LOD1"
    material: "MI_Wing_417_HP02_LOD1;MI_Wing_417_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620749
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_406_LOD1"
    modelType: 1
  }
}
rows {
  id: 620750
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_406_LOD1"
    material: "MI_Wing_406_HP01_LOD1;MI_Wing_406_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620751
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_406_LOD1"
    material: "MI_Wing_406_HP02_LOD1;MI_Wing_406_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620752
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_446_LOD1"
    modelType: 1
  }
}
rows {
  id: 620753
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_446_LOD1"
    material: "MI_Wing_446_HP01_LOD1;MI_Wing_446_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620754
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_446_LOD1"
    material: "MI_Wing_446_HP02_LOD1;MI_Wing_446_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620755
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_379_LOD1"
    modelType: 1
  }
}
rows {
  id: 620756
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_379_LOD1"
    material: "MI_Wing_379_HP01_LOD1;MI_Wing_379_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620757
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_379_LOD1"
    material: "MI_Wing_379_HP02_LOD1;MI_Wing_379_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620758
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_396_LOD1"
    modelType: 1
  }
}
rows {
  id: 620759
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_396_LOD1"
    material: "MI_Wing_396_HP01_LOD1;MI_Wing_396_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620760
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_396_LOD1"
    material: "MI_Wing_396_HP02_LOD1;MI_Wing_396_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620761
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_420_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_420_Idle_001"
  }
}
rows {
  id: 620762
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_420_LOD1"
    material: "MI_Wing_420_HP01_LOD1;MI_Wing_420_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_420_Idle_001_HP01"
  }
}
rows {
  id: 620763
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_420_LOD1"
    material: "MI_Wing_420_HP02_LOD1;MI_Wing_420_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_420_Idle_001_HP02"
  }
}
rows {
  id: 620764
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_452_LOD1"
    modelType: 1
  }
}
rows {
  id: 620765
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_453_LOD1"
    modelType: 1
  }
}
rows {
  id: 620766
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_389_LOD1"
    modelType: 1
  }
}
rows {
  id: 620767
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_390_LOD1"
    modelType: 1
  }
}
rows {
  id: 620768
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_391_LOD1"
    modelType: 1
  }
}
rows {
  id: 620769
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_387_LOD1"
    modelType: 1
  }
}
rows {
  id: 620770
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_433_LOD1"
    physics: "SK_Wing_433_Physics"
    modelType: 2
    idleAnim: "AS_Wing_433_Idle_001"
  }
}
rows {
  id: 620771
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_433_LOD1"
    material: "Wing_1_LOD1:MI_Wing_433_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_433_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_433_3_HP01_LOD1;Wing_1_LOD2:MI_Wing_433_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_433_2_HP01_LOD2"
    physics: "SK_Wing_433_Physics"
    modelType: 2
    idleAnim: "AS_Wing_433_Idle_001_HP01"
  }
}
rows {
  id: 620772
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_433_LOD1"
    material: "Wing_1_LOD1:MI_Wing_433_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_433_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_433_3_HP02_LOD1;Wing_1_LOD2:MI_Wing_433_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_433_2_HP02_LOD2"
    physics: "SK_Wing_433_Physics"
    modelType: 2
    idleAnim: "AS_Wing_433_Idle_001_HP02"
  }
}
rows {
  id: 620773
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_423_LOD1"
    modelType: 1
  }
}
rows {
  id: 620774
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_398_LOD1"
    modelType: 1
  }
}
rows {
  id: 620775
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_413_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_413_Idle_001"
  }
}
rows {
  id: 620776
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_413_LOD1"
    material: "Wing_1_LOD1:MI_Wing_413_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_413_2_HP01_LOD1;Wing_LOD2:MI_Wing_413_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_413_Idle_001_HP01"
  }
}
rows {
  id: 620777
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_413_LOD1"
    material: "Wing_1_LOD1:MI_Wing_413_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_413_2_HP02_LOD1;Wing_LOD2:MI_Wing_413_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_413_Idle_001_HP02"
  }
}
rows {
  id: 620778
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_440_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_440_Idle_001"
  }
}
rows {
  id: 620779
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_440_LOD1"
    material: "MI_Wing_440_HP01_LOD1;MI_Wing_440_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_440_Idle_001_HP01"
  }
}
rows {
  id: 620780
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_440_LOD1"
    material: "MI_Wing_440_HP02_LOD1;MI_Wing_440_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_440_Idle_001_HP02"
  }
}
rows {
  id: 620781
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_418_LOD1"
    modelType: 1
  }
}
rows {
  id: 620782
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_418_LOD1"
    material: "Wing_LOD1:MI_Wing_418_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_418_HP01_LOD1;Wing_LOD2:MI_Wing_418_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620783
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_418_LOD1"
    material: "Wing_LOD1:MI_Wing_418_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_418_HP02_LOD1;Wing_LOD2:MI_Wing_418_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620784
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_464_LOD1"
    modelType: 1
  }
}
rows {
  id: 620785
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_441_LOD1"
    modelType: 1
  }
}
rows {
  id: 620786
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_441_LOD1"
    material: "MI_Wing_441_HP01_LOD1;MI_Wing_441_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620787
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_441_LOD1"
    material: "MI_Wing_441_HP02_LOD1;MI_Wing_441_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620788
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_450_LOD1"
    modelType: 1
  }
}
rows {
  id: 620789
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_438_LOD1"
    modelType: 1
  }
}
rows {
  id: 620790
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_435_LOD1"
    modelType: 1
  }
}
rows {
  id: 620791
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_449_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_449_Idle_001"
  }
}
rows {
  id: 620792
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_449_LOD1"
    material: "MI_Wing_449_HP01_LOD1;MI_Wing_449_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_449_Idle_001_HP01"
  }
}
rows {
  id: 620793
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_449_LOD1"
    material: "MI_Wing_449_HP02_LOD1;MI_Wing_449_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_449_Idle_001_HP02"
  }
}
rows {
  id: 620794
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_457_LOD1"
    modelType: 1
  }
}
rows {
  id: 620795
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_457_LOD1"
    material: "MI_Wing_457_HP01_LOD1;MI_Wing_457_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620796
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_457_LOD1"
    material: "MI_Wing_457_HP02_LOD1;MI_Wing_457_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620797
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_456_LOD1"
    modelType: 1
  }
}
rows {
  id: 620798
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_456_LOD1"
    material: "MI_Wing_456_HP01_LOD1;MI_Wing_456_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620799
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_456_LOD1"
    material: "MI_Wing_456_HP02_LOD1;MI_Wing_456_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620800
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_459_LOD1"
    modelType: 1
  }
}
rows {
  id: 620801
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_459_LOD1"
    material: "MI_Wing_459_HP01_LOD1;MI_Wing_459_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620802
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_459_LOD1"
    material: "MI_Wing_459_HP02_LOD1;MI_Wing_459_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620803
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_461_LOD1"
    modelType: 1
  }
}
rows {
  id: 620804
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_461_LOD1"
    material: "MI_Wing_461_HP01_LOD1;MI_Wing_461_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620805
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_461_LOD1"
    material: "MI_Wing_461_HP02_LOD1;MI_Wing_461_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620806
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_462_LOD1"
    modelType: 1
  }
}
rows {
  id: 620807
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_462_LOD1"
    material: "MI_Wing_462_HP01_LOD1;MI_Wing_462_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620808
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_462_LOD1"
    material: "MI_Wing_462_HP02_LOD1;MI_Wing_462_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620809
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_384_LOD1"
    modelType: 1
  }
}
rows {
  id: 620810
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_384_LOD1"
    material: "MI_Wing_384_HP01_LOD1;MI_Wing_384_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620811
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_384_LOD1"
    material: "MI_Wing_384_HP02_LOD1;MI_Wing_384_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620812
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_458_LOD1"
    modelType: 1
  }
}
rows {
  id: 620813
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_458_LOD1"
    material: "MI_Wing_458_HP01_LOD1;MI_Wing_458_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620814
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_458_LOD1"
    material: "MI_Wing_458_HP02_LOD1;MI_Wing_458_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620815
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_465_LOD1"
    modelType: 1
  }
}
rows {
  id: 620816
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_465_LOD1"
    material: "MI_Wing_465_HP01_LOD1;MI_Wing_465_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620817
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_465_LOD1"
    material: "MI_Wing_465_HP02_LOD1;MI_Wing_465_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620818
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_460_LOD1"
    modelType: 1
  }
}
rows {
  id: 620819
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_460_LOD1"
    material: "MI_Wing_460_HP01_LOD1;MI_Wing_460_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620820
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_460_LOD1"
    material: "MI_Wing_460_HP02_LOD1;MI_Wing_460_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620821
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_429_LOD1"
    modelType: 1
  }
}
rows {
  id: 620822
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_429_LOD1"
    material: "MI_Wing_429_HP01_LOD1;MI_Wing_429_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620823
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_429_LOD1"
    material: "MI_Wing_429_HP02_LOD1;MI_Wing_429_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620824
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_428_LOD1"
    modelType: 1
  }
}
rows {
  id: 620825
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_428_LOD1"
    material: "MI_Wing_428_HP01_LOD1;MI_Wing_428_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620826
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_428_LOD1"
    material: "MI_Wing_428_HP02_LOD1;MI_Wing_428_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620827
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_421_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_421_Idle_001"
  }
}
rows {
  id: 620828
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_421_LOD1"
    material: "MI_Wing_421_HP01_LOD1;MI_Wing_421_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_421_Idle_001_HP01"
  }
}
rows {
  id: 620829
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_421_LOD1"
    material: "MI_Wing_421_HP02_LOD1;MI_Wing_421_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_421_Idle_001_HP02"
  }
}
rows {
  id: 620830
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_470_LOD1"
    modelType: 1
  }
}
rows {
  id: 620831
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_427_LOD1"
    modelType: 1
  }
}
rows {
  id: 620832
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_434_LOD1"
    modelType: 1
  }
}
rows {
  id: 620833
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_426_LOD1"
    modelType: 1
  }
}
rows {
  id: 620834
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_439_LOD1"
    modelType: 1
  }
}
rows {
  id: 620835
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_437_LOD1"
    modelType: 1
  }
}
rows {
  id: 620836
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_437_LOD1"
    material: "MI_Wing_437_HP01_LOD1;MI_Wing_437_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620837
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_437_LOD1"
    material: "MI_Wing_437_HP02_LOD1;MI_Wing_437_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620838
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_463_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_463_Idle_001"
  }
}
rows {
  id: 620839
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_463_LOD1"
    material: "Wing_1_LOD1:MI_Wing_463_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_463_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_463_3_HP01_LOD1;Wing_1_LOD2:MI_Wing_463_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_463_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_463_Idle_001_HP01"
  }
}
rows {
  id: 620840
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_463_LOD1"
    material: "Wing_1_LOD1:MI_Wing_463_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_463_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_463_3_HP02_LOD1;Wing_1_LOD2:MI_Wing_463_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_463_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_463_Idle_001_HP02"
  }
}
rows {
  id: 620841
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_466_LOD1"
    modelType: 1
  }
}
rows {
  id: 620842
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_444_LOD1"
    modelType: 1
  }
}
rows {
  id: 620843
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_444_LOD1"
    material: "MI_Wing_444_HP01_LOD1;MI_Wing_444_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620844
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_444_LOD1"
    material: "MI_Wing_444_HP02_LOD1;MI_Wing_444_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620845
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_445_LOD1"
    modelType: 1
  }
}
rows {
  id: 620846
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_445_LOD1"
    material: "MI_Wing_445_HP01_LOD1;MI_Wing_445_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620847
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_445_LOD1"
    material: "MI_Wing_445_HP02_LOD1;MI_Wing_445_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620848
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_485_LOD1"
    modelType: 1
  }
}
rows {
  id: 620849
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_375_LOD1"
    modelType: 1
  }
}
rows {
  id: 620852
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_359_LOD1"
    modelType: 1
  }
}
rows {
  id: 620853
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_359_LOD1"
    material: "MI_Wing_359_HP01_LOD1;MI_Wing_359_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620854
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_359_LOD1"
    material: "MI_Wing_359_HP02_LOD1;MI_Wing_359_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620855
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_361_LOD1"
    modelType: 1
  }
}
rows {
  id: 620856
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_361_LOD1"
    material: "Wing_LOD1:MI_Wing_361_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_361_2_HP01_LOD1;Wing_LOD2:MI_Wing_361_1_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620857
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_361_LOD1"
    material: "Wing_LOD1:MI_Wing_361_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_361_2_HP02_LOD1;Wing_LOD2:MI_Wing_361_1_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620858
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_377_LOD1"
    modelType: 1
  }
}
rows {
  id: 620859
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_377_LOD1"
    material: "Wing_LOD1:MI_Wing_377_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_377_2_HP01_LOD1;Wing_LOD2:MI_Wing_377_1_HP01_LOD2;Wing_Translucent_LOD2:MI_Wing_377_2_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620860
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_377_LOD1"
    material: "Wing_LOD1:MI_Wing_377_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_377_2_HP02_LOD1;Wing_LOD2:MI_Wing_377_1_HP02_LOD2;Wing_Translucent_LOD2:MI_Wing_377_2_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620861
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_422_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_422_Idle_001"
  }
}
rows {
  id: 620862
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_422_LOD1"
    material: "MI_Wing_422_HP01_LOD1;MI_Wing_422_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_422_Idle_001_HP01"
  }
}
rows {
  id: 620863
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_422_LOD1"
    material: "MI_Wing_422_HP02_LOD1;MI_Wing_422_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_422_Idle_001_HP02"
  }
}
rows {
  id: 620864
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_469_LOD1"
    modelType: 2
    idleAnim: "AS_Wing_469_Idle_001"
  }
}
rows {
  id: 620865
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_469_LOD1"
    material: "Wing_1_LOD1:MI_Wing_469_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_469_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_469_3_HP01_LOD1;Wing_1_LOD2:MI_Wing_469_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_469_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_469_Idle_001_HP01"
  }
}
rows {
  id: 620866
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_469_LOD1"
    material: "Wing_1_LOD1:MI_Wing_469_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_469_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_469_3_HP02_LOD1;Wing_1_LOD2:MI_Wing_469_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_469_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Wing_469_Idle_001_HP02"
  }
}
rows {
  id: 620867
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_471_LOD1"
    modelType: 1
  }
}
rows {
  id: 620868
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_471_LOD1"
    material: "MI_Wing_471_HP01_LOD1;MI_Wing_471_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620869
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_471_LOD1"
    material: "MI_Wing_471_HP02_LOD1;MI_Wing_471_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620870
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_486_LOD1"
    modelType: 1
  }
}
rows {
  id: 620871
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_486_LOD1"
    material: "MI_Wing_486_HP01_LOD1;MI_Wing_486_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620872
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_486_LOD1"
    material: "MI_Wing_486_HP02_LOD1;MI_Wing_486_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620873
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_490_LOD1"
    modelType: 1
  }
}
rows {
  id: 620874
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_490_LOD1"
    material: "MI_Wing_490_HP01_LOD1;MI_Wing_490_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620875
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_490_LOD1"
    material: "MI_Wing_490_HP02_LOD1;MI_Wing_490_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620876
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_481_LOD1"
    modelType: 1
  }
}
rows {
  id: 620877
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_481_LOD1"
    material: "MI_Wing_481_HP01_LOD1;MI_Wing_481_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620878
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_481_LOD1"
    material: "MI_Wing_481_HP02_LOD1;MI_Wing_481_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620879
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_472_LOD1"
    modelType: 1
  }
}
rows {
  id: 620880
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_472_LOD1"
    material: "MI_Wing_472_HP01_LOD1;MI_Wing_472_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620881
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_472_LOD1"
    material: "MI_Wing_472_HP02_LOD1;MI_Wing_472_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620882
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_482_LOD1"
    modelType: 1
  }
}
rows {
  id: 620883
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_482_LOD1"
    material: "MI_Wing_482_HP01_LOD1;MI_Wing_482_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620884
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_482_LOD1"
    material: "MI_Wing_482_HP02_LOD1;MI_Wing_482_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620885
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_478_LOD1"
    modelType: 1
  }
}
rows {
  id: 620886
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_488_LOD1"
    modelType: 1
  }
}
rows {
  id: 620887
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_473_LOD1"
    modelType: 1
  }
}
rows {
  id: 620888
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_479_LOD1"
    modelType: 1
  }
}
rows {
  id: 620889
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_474_LOD1"
    modelType: 1
  }
}
rows {
  id: 620890
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_475_LOD1"
    modelType: 1
  }
}
rows {
  id: 620891
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_483_LOD1"
    modelType: 1
  }
}
rows {
  id: 620892
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_360_LOD1"
    modelType: 1
  }
}
rows {
  id: 620893
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_360_LOD1"
    material: "MI_Wing_360_HP01_LOD1;MI_Wing_360_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620894
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_360_LOD1"
    material: "MI_Wing_360_HP02_LOD1;MI_Wing_360_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620895
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_455_LOD1"
    modelType: 1
  }
}
rows {
  id: 620896
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_476_LOD1"
    modelType: 1
  }
}
rows {
  id: 620897
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_477_LOD1"
    modelType: 1
  }
}
rows {
  id: 620898
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_498_LOD1"
    modelType: 1
  }
}
rows {
  id: 620899
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_497_LOD1"
    modelType: 1
  }
}
rows {
  id: 620900
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_499_LOD1"
    emitter: "FX_CH_Decorate_Wing_499_001"
    modelType: 1
  }
}
rows {
  id: 620901
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_499_LOD1"
    material: "Wing_1_LOD1:MI_Wing_499_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_499_2_HP01_LOD1;Wing_1_LOD2:MI_Wing_499_1_HP01_LOD2"
    emitter: "FX_CH_Decorate_Wing_499_001_HP01"
    modelType: 1
  }
}
rows {
  id: 620902
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_499_LOD1"
    material: "Wing_1_LOD1:MI_Wing_499_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_499_2_HP02_LOD1;Wing_1_LOD2:MI_Wing_499_1_HP02_LOD2"
    emitter: "FX_CH_Decorate_Wing_499_001_HP02"
    modelType: 1
  }
}
rows {
  id: 620903
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_496_LOD1"
    modelType: 1
  }
}
rows {
  id: 620904
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_496_LOD1"
    material: "Wing_LOD1:MI_Wing_496_1_HP01_LOD1;Wing_Translucent_LOD1:MI_Wing_496_2_HP01_LOD1;Wing_LOD2:MI_Wing_496_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620905
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_496_LOD1"
    material: "Wing_LOD1:MI_Wing_496_1_HP02_LOD1;Wing_Translucent_LOD1:MI_Wing_496_2_HP02_LOD1;Wing_LOD2:MI_Wing_496_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620906
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_493_LOD1"
    modelType: 1
  }
}
rows {
  id: 620907
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_493_LOD1"
    material: "MI_Wing_493_HP01_LOD1;MI_Wing_493_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620908
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_493_LOD1"
    material: "MI_Wing_493_HP02_LOD1;MI_Wing_493_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620909
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_501_LOD1"
    modelType: 1
  }
}
rows {
  id: 620910
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_502_LOD1"
    modelType: 1
  }
}
rows {
  id: 620911
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_518_LOD1"
    modelType: 1
  }
}
rows {
  id: 620912
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_467_LOD1"
    modelType: 1
  }
}
rows {
  id: 620913
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_468_LOD1"
    modelType: 1
  }
}
rows {
  id: 620914
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_487_LOD1"
    modelType: 1
  }
}
rows {
  id: 620915
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_487_LOD1"
    material: "MI_Wing_487_HP01_LOD1;MI_Wing_487_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620916
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_487_LOD1"
    material: "MI_Wing_487_HP02_LOD1;MI_Wing_487_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620917
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_491_LOD1"
    modelType: 1
  }
}
rows {
  id: 620918
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_491_LOD1"
    material: "MI_Wing_491_HP01_LOD1;MI_Wing_491_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620919
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_491_LOD1"
    material: "MI_Wing_491_HP02_LOD1;MI_Wing_491_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620920
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_492_LOD1"
    modelType: 1
  }
}
rows {
  id: 620921
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_492_LOD1"
    material: "MI_Wing_492_HP01_LOD1;MI_Wing_492_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620922
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_492_LOD1"
    material: "MI_Wing_492_HP02_LOD1;MI_Wing_492_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620923
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_503_LOD1"
    modelType: 1
  }
}
rows {
  id: 620924
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_521_LOD1"
    modelType: 1
  }
}
rows {
  id: 620925
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_514_LOD1"
    physics: "SK_Wing_514_Physics"
    modelType: 2
    idleAnim: "AS_Wing_514_Idle_001"
  }
}
rows {
  id: 620926
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_514_LOD1"
    material: "Wing_1_LOD1:MI_Wing_514_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_514_2_HP01_LOD1;Wing_3_LOD1:MI_Wing_514_3_HP01_LOD1;Wing_1_LOD2:MI_Wing_514_1_HP01_LOD2;Wing_2_LOD2:MI_Wing_514_2_HP01_LOD2"
    physics: "SK_Wing_514_Physics"
    modelType: 2
    idleAnim: "AS_Wing_514_Idle_001_HP01"
  }
}
rows {
  id: 620927
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SK_Wing_514_LOD1"
    material: "Wing_1_LOD1:MI_Wing_514_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_514_2_HP02_LOD1;Wing_3_LOD1:MI_Wing_514_3_HP02_LOD1;Wing_1_LOD2:MI_Wing_514_1_HP02_LOD2;Wing_2_LOD2:MI_Wing_514_2_HP02_LOD2"
    physics: "SK_Wing_514_Physics"
    modelType: 2
    idleAnim: "AS_Wing_514_Idle_001_HP02"
  }
}
rows {
  id: 620928
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_517_LOD1"
    emitter: "FX_CH_Decorate_Wing_517_001"
    modelType: 1
  }
}
rows {
  id: 620929
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_517_LOD1"
    material: "Wing_1_LOD1:MI_Wing_517_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_517_2_HP01_LOD1;Wing_1_LOD2:MI_Wing_517_1_HP01_LOD2"
    emitter: "FX_CH_Decorate_Wing_517_001_HP01"
    modelType: 1
  }
}
rows {
  id: 620930
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_517_LOD1"
    material: "Wing_1_LOD1:MI_Wing_517_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_517_2_HP02_LOD1;Wing_1_LOD2:MI_Wing_517_1_HP02_LOD2"
    emitter: "FX_CH_Decorate_Wing_517_001_HP02"
    modelType: 1
  }
}
rows {
  id: 620931
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_515_LOD1"
    modelType: 1
  }
}
rows {
  id: 620932
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_515_LOD1"
    material: "MI_Wing_515_HP01_LOD1;MI_Wing_515_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620933
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_515_LOD1"
    material: "MI_Wing_515_HP02_LOD1;MI_Wing_515_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620934
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_516_LOD1"
    modelType: 1
  }
}
rows {
  id: 620935
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_516_LOD1"
    material: "MI_Wing_516_HP01_LOD1;MI_Wing_516_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620936
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_516_LOD1"
    material: "MI_Wing_516_HP02_LOD1;MI_Wing_516_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620937
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_519_LOD1"
    modelType: 1
  }
}
rows {
  id: 620938
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_519_LOD1"
    material: "MI_Wing_519_HP01_LOD1;MI_Wing_519_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620939
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_519_LOD1"
    material: "MI_Wing_519_HP02_LOD1;MI_Wing_519_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620940
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_504_LOD1"
    modelType: 1
  }
}
rows {
  id: 620941
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_505_LOD1"
    modelType: 1
  }
}
rows {
  id: 620942
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_506_LOD1"
    modelType: 1
  }
}
rows {
  id: 620943
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_520_LOD1"
    modelType: 1
  }
}
rows {
  id: 620944
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_522_LOD1"
    modelType: 1
  }
}
rows {
  id: 620946
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_447_LOD1"
    modelType: 1
  }
}
rows {
  id: 620947
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_447_LOD1"
    material: "MI_Wing_447_HP01_LOD1;MI_Wing_447_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620948
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_447_LOD1"
    material: "MI_Wing_447_HP02_LOD1;MI_Wing_447_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620949
  type: ItemType_BackOrnament
  quality: 3
  resourceConf {
    model: "SM_Wing_495_LOD1"
    modelType: 1
  }
}
rows {
  id: 620950
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_513_LOD1"
    modelType: 1
  }
}
rows {
  id: 620951
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_513_LOD1"
    material: "MI_Wing_513_HP01_LOD1;MI_Wing_513_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620952
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_513_LOD1"
    material: "MI_Wing_513_HP02_LOD1;MI_Wing_513_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620953
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_509_LOD1"
    modelType: 1
  }
}
rows {
  id: 620954
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_509_LOD1"
    material: "MI_Wing_509_HP01_LOD1;MI_Wing_509_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 620955
  type: ItemType_BackOrnament
  quality: 2
  resourceConf {
    model: "SM_Wing_509_LOD1"
    material: "MI_Wing_509_HP02_LOD1;MI_Wing_509_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 620956
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_500_LOD1"
    emitter: "FX_CH_Decorate_Wing_500_001"
    modelType: 1
  }
}
rows {
  id: 620957
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_500_LOD1"
    material: "Wing_1_LOD1:MI_Wing_500_1_HP01_LOD1;Wing_2_LOD1:MI_Wing_500_2_HP01_LOD1;Wing_LOD2:MI_Wing_500_1_HP01_LOD2"
    emitter: "FX_CH_Decorate_Wing_500_001_HP01"
    modelType: 1
  }
}
rows {
  id: 620958
  type: ItemType_BackOrnament
  quality: 1
  resourceConf {
    model: "SM_Wing_500_LOD1"
    material: "Wing_1_LOD1:MI_Wing_500_1_HP02_LOD1;Wing_2_LOD1:MI_Wing_500_2_HP02_LOD1;Wing_LOD2:MI_Wing_500_1_HP02_LOD2"
    emitter: "FX_CH_Decorate_Wing_500_001_HP02"
    modelType: 1
  }
}
rows {
  id: 630001
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_001_LOD1"
    modelType: 1
  }
}
rows {
  id: 630002
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_002_LOD1"
    modelType: 1
  }
}
rows {
  id: 630003
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_003_LOD1"
    modelType: 1
  }
}
rows {
  id: 630004
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_004_LOD1"
    modelType: 1
  }
}
rows {
  id: 630005
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_005_LOD1"
    modelType: 1
  }
}
rows {
  id: 630006
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_006_LOD1"
    modelType: 1
  }
}
rows {
  id: 630007
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_006_LOD1"
    material: "MI_Halo_006_HP01_LOD1;MI_Halo_006_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630008
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_006_LOD1"
    material: "MI_Halo_006_HP02_LOD1;MI_Halo_006_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630009
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_007_LOD1"
    modelType: 1
  }
}
rows {
  id: 630010
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_007_LOD1"
    material: "MI_Halo_007_HP01_LOD1;MI_Halo_007_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630011
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_007_LOD1"
    material: "MI_Halo_007_HP02_LOD1;MI_Halo_007_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630012
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_008_LOD1"
    modelType: 1
  }
}
rows {
  id: 630013
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_008_LOD1"
    material: "MI_Halo_008_HP01_LOD1;MI_Halo_008_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630014
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_008_LOD1"
    material: "MI_Halo_008_HP02_LOD1;MI_Halo_008_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630015
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_014_LOD1"
    emitter: "FX_CH_Decorate_Halo_014_02"
    modelType: 2
    idleAnim: "AS_Halo_014_idle_001"
  }
}
rows {
  id: 630016
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_017_LOD1"
    modelType: 1
  }
}
rows {
  id: 630017
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_017_LOD1"
    material: "MI_Halo_017_HP01_LOD1;MI_Halo_017_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630018
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_017_LOD1"
    material: "MI_Halo_017_HP02_LOD1;MI_Halo_017_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630019
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_009_LOD1"
    modelType: 1
  }
}
rows {
  id: 630020
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_009_LOD1"
    material: "MI_Halo_009_HP01_LOD1;MI_Halo_009_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630021
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_009_LOD1"
    material: "MI_Halo_009_HP02_LOD1;MI_Halo_009_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630022
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_016_LOD1"
    modelType: 1
  }
}
rows {
  id: 630023
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_016_LOD1"
    material: "MI_Halo_016_HP01_LOD1;MI_Halo_016_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630024
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_016_LOD1"
    material: "MI_Halo_016_HP02_LOD1;MI_Halo_016_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630025
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_012_LOD1"
    modelType: 1
  }
}
rows {
  id: 630026
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_012_LOD1"
    material: "MI_Halo_012_HP01_LOD1;MI_Halo_012_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630027
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_012_LOD1"
    material: "MI_Halo_012_HP02_LOD1;MI_Halo_012_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630028
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_011_LOD1"
    modelType: 1
  }
}
rows {
  id: 630029
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_011_LOD1"
    material: "MI_Halo_011_HP01_LOD1;MI_Halo_011_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630030
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_011_LOD1"
    material: "MI_Halo_011_HP02_LOD1;MI_Halo_011_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630031
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_021"
    modelType: 1
  }
  socketName: "Head"
}
rows {
  id: 630032
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_022"
    modelType: 1
  }
  socketName: "Head"
}
rows {
  id: 630033
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_023"
    modelType: 1
  }
  socketName: "Head"
}
rows {
  id: 630034
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_024"
    modelType: 1
  }
  socketName: "Head"
}
rows {
  id: 630035
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_015_LOD1"
    modelType: 1
  }
}
rows {
  id: 630036
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_015_LOD1"
    material: "MI_Halo_015_HP01_LOD1;MI_Halo_015_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630037
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_015_LOD1"
    material: "MI_Halo_015_HP02_LOD1;MI_Halo_015_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630038
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_013_LOD1"
    modelType: 1
  }
}
rows {
  id: 630039
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_013_LOD1"
    material: "MI_Halo_013_HP01_LOD1;MI_Halo_013_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630040
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_013_LOD1"
    material: "MI_Halo_013_HP02_LOD1;MI_Halo_013_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630041
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_018_LOD1"
    modelType: 1
  }
}
rows {
  id: 630042
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_019_LOD1"
    modelType: 1
  }
}
rows {
  id: 630043
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_020_LOD1"
    modelType: 1
  }
}
rows {
  id: 630044
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_010_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_010_idle_001"
  }
}
rows {
  id: 630045
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_010_LOD1"
    material: "MI_Halo_010_HP01_LOD1;MI_Halo_010_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_010_idle_001_HP01"
  }
}
rows {
  id: 630046
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_010_LOD1"
    material: "MI_Halo_010_HP02_LOD1;MI_Halo_010_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_010_idle_001_HP02"
  }
}
rows {
  id: 630047
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_003_LOD1"
    modelType: 1
  }
}
rows {
  id: 630048
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_025_LOD1"
    modelType: 1
  }
}
rows {
  id: 630049
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_038_LOD1"
    modelType: 1
  }
}
rows {
  id: 630050
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_039_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_039_Idle_001"
  }
}
rows {
  id: 630051
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_040_LOD1"
    emitter: "FX_CH_Decorate_Halo_040_001"
    modelType: 1
  }
}
rows {
  id: 630052
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_041_LOD1"
    modelType: 1
  }
}
rows {
  id: 630053
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_031"
    modelType: 1
  }
}
rows {
  id: 630054
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_047"
    modelType: 1
  }
}
rows {
  id: 630055
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_036_LOD1"
    modelType: 1
  }
}
rows {
  id: 630056
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_037_LOD1"
    modelType: 1
  }
}
rows {
  id: 630057
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_035_LOD1"
    modelType: 1
  }
}
rows {
  id: 630058
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_042_LOD1"
    modelType: 1
  }
}
rows {
  id: 630059
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_027"
    modelType: 1
  }
}
rows {
  id: 630060
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_028"
    modelType: 1
  }
}
rows {
  id: 630061
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_029"
    modelType: 1
  }
}
rows {
  id: 630062
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_045"
    modelType: 1
  }
}
rows {
  id: 630063
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_032_LOD1"
    modelType: 1
  }
}
rows {
  id: 630064
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_032_LOD1"
    material: "MI_Halo_032_HP01_LOD1;MI_Halo_032_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630065
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_032_LOD1"
    material: "MI_Halo_032_HP02_LOD1;MI_Halo_032_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630066
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_033"
    modelType: 1
  }
}
rows {
  id: 630067
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_033_HP01"
    modelType: 1
  }
}
rows {
  id: 630068
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_033_HP02"
    modelType: 1
  }
}
rows {
  id: 630069
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_043_LOD1"
    modelType: 1
  }
}
rows {
  id: 630070
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_043_LOD1"
    material: "MI_Halo_043_HP01_LOD1;MI_Halo_043_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630071
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_043_LOD1"
    material: "MI_Halo_043_HP02_LOD1;MI_Halo_043_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630072
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_046_LOD1"
    modelType: 1
  }
}
rows {
  id: 630073
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_030_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_030_idle_001"
  }
}
rows {
  id: 630074
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_030_LOD1"
    material: "MI_Halo_030_HP01_LOD1;MI_Halo_030_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_030_idle_001_HP01"
  }
}
rows {
  id: 630075
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_030_LOD1"
    material: "MI_Halo_030_HP02_LOD1;MI_Halo_030_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_030_idle_001_HP02"
  }
}
rows {
  id: 630076
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_044_LOD1"
    modelType: 1
  }
}
rows {
  id: 630077
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_050_LOD1"
    modelType: 1
  }
}
rows {
  id: 630078
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_050_LOD1"
    material: "MI_Halo_050_HP01_LOD1;MI_Halo_050_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630079
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_050_LOD1"
    material: "MI_Halo_050_HP02_LOD1;MI_Halo_050_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630080
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_049_LOD1"
    modelType: 1
  }
}
rows {
  id: 630081
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_034_LOD1"
    modelType: 1
  }
}
rows {
  id: 630082
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_034_LOD1"
    material: "Halo_LOD1:MI_Halo_034_HP01_1_LOD1;Halo_Translucent_LOD1:MI_Halo_034_HP01_2_LOD1;Halo_LOD2:MI_Halo_034_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630083
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_034_LOD1"
    material: "Halo_LOD1:MI_Halo_034_HP02_1_LOD1;Halo_Translucent_LOD1:MI_Halo_034_HP02_2_LOD1;Halo_LOD2:MI_Halo_034_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630084
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_051"
    modelType: 1
  }
}
rows {
  id: 630085
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_056"
    modelType: 1
  }
}
rows {
  id: 630086
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_055_LOD1"
    modelType: 1
  }
}
rows {
  id: 630087
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_080_LOD1"
    modelType: 1
  }
}
rows {
  id: 630088
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_057_LOD1"
    modelType: 1
  }
}
rows {
  id: 630089
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_077"
    modelType: 1
  }
}
rows {
  id: 630090
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_078_LOD1"
    modelType: 1
  }
}
rows {
  id: 630091
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_079_LOD1"
    modelType: 1
  }
}
rows {
  id: 630092
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_054_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_054_idle_001"
  }
}
rows {
  id: 630093
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_054_LOD1"
    material: "Halo_LOD1:MI_Halo_054_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_054_2_HP01_LOD1;Halo_LOD2:MI_Halo_054_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_054_idle_HP01"
  }
}
rows {
  id: 630094
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_054_LOD1"
    material: "Halo_LOD1:MI_Halo_054_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_054_2_HP02_LOD1;Halo_LOD2:MI_Halo_054_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_054_idle_HP02"
  }
}
rows {
  id: 630095
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_053_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_053_idle_001"
  }
}
rows {
  id: 630096
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_053_LOD1"
    material: "Halo_LOD1:MI_Halo_053_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_053_2_HP01_LOD1;Halo_LOD2:MI_Halo_053_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_053_idle_HP01"
  }
}
rows {
  id: 630097
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_053_LOD1"
    material: "Halo_LOD1:MI_Halo_053_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_053_2_HP02_LOD1;Halo_LOD2:MI_Halo_053_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_053_idle_HP02"
  }
}
rows {
  id: 630098
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_058_LOD1"
    modelType: 1
  }
}
rows {
  id: 630099
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_052_LOD1"
    modelType: 1
  }
}
rows {
  id: 630100
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_065"
    modelType: 1
  }
}
rows {
  id: 630101
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_059_LOD1"
    modelType: 1
  }
}
rows {
  id: 630102
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_068_LOD1"
    modelType: 1
  }
}
rows {
  id: 630103
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_081_LOD1"
    modelType: 1
  }
}
rows {
  id: 630104
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_069_LOD1"
    modelType: 1
  }
}
rows {
  id: 630105
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_069_LOD1"
    material: "MI_Halo_069_HP01_LOD1;MI_Halo_069_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630106
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_069_LOD1"
    material: "MI_Halo_069_HP02_LOD1;MI_Halo_069_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630107
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_082_LOD1"
    modelType: 1
  }
}
rows {
  id: 630108
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_082_LOD1"
    material: "Halo_LOD1:MI_Halo_082_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_082_2_HP01_LOD1;Halo_LOD2:MI_Halo_082_1_HP01_LOD2;Halo_Translucent_LOD2:MI_Halo_082_2_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630109
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_082_LOD1"
    material: "Halo_LOD1:MI_Halo_082_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_082_2_HP02_LOD1;Halo_LOD2:MI_Halo_082_1_HP02_LOD2;Halo_Translucent_LOD2:MI_Halo_082_2_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630110
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_061_LOD1"
    modelType: 1
  }
}
rows {
  id: 630111
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_061_LOD1"
    material: "MI_Halo_061_HP01_LOD1;MI_Halo_061_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630112
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_061_LOD1"
    material: "MI_Halo_061_HP02_LOD1;MI_Halo_061_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630113
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_070_LOD1"
    modelType: 1
  }
}
rows {
  id: 630114
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_073"
    modelType: 1
  }
}
rows {
  id: 630115
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_074_LOD1"
    modelType: 1
  }
}
rows {
  id: 630116
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_084_LOD1"
    modelType: 1
  }
}
rows {
  id: 630117
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_084_LOD1"
    material: "MI_Halo_084_HP01_LOD1;MI_Halo_084_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630118
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_084_LOD1"
    material: "MI_Halo_084_HP02_LOD1;MI_Halo_084_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630119
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_072_LOD1"
    modelType: 1
  }
}
rows {
  id: 630120
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_089_LOD1"
    modelType: 1
  }
}
rows {
  id: 630121
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_076_LOD1"
    modelType: 1
  }
}
rows {
  id: 630122
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_067_LOD1"
    modelType: 1
  }
}
rows {
  id: 630123
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_067_LOD1"
    material: "MI_Halo_067_HP01_LOD1;MI_Halo_067_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630124
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_067_LOD1"
    material: "MI_Halo_067_HP02_LOD1;MI_Halo_067_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630125
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_083_LOD1"
    modelType: 1
  }
}
rows {
  id: 630126
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_085_LOD1"
    modelType: 1
  }
}
rows {
  id: 630127
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_062_LOD1"
    modelType: 1
  }
}
rows {
  id: 630128
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_062_LOD1"
    material: "MI_Halo_062_HP01_LOD1;MI_Halo_062_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630129
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_062_LOD1"
    material: "MI_Halo_062_HP02_LOD1;MI_Halo_062_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630130
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_048_LOD1"
    modelType: 1
  }
}
rows {
  id: 630131
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_100"
    modelType: 1
  }
}
rows {
  id: 630132
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_086_LOD1"
    modelType: 1
  }
}
rows {
  id: 630133
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_088_LOD1"
    modelType: 1
  }
}
rows {
  id: 630134
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_095_LOD1"
    modelType: 1
  }
}
rows {
  id: 630135
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_095_LOD1"
    material: "MI_Halo_095_HP01_LOD1;MI_Halo_095_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630136
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_095_LOD1"
    material: "MI_Halo_095_HP02_LOD1;MI_Halo_095_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630137
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_106_LOD1"
    modelType: 1
  }
}
rows {
  id: 630138
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_090_LOD1"
    modelType: 1
  }
}
rows {
  id: 630139
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_090_LOD1"
    material: "MI_Halo_090_HP01_LOD1;MI_Halo_090_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630140
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_090_LOD1"
    material: "MI_Halo_090_HP02_LOD1;MI_Halo_090_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630141
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_091_LOD1"
    modelType: 1
  }
}
rows {
  id: 630142
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_091_LOD1"
    material: "MI_Halo_091_HP01_LOD1;MI_Halo_091_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630143
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_091_LOD1"
    material: "MI_Halo_091_HP02_LOD1;MI_Halo_091_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630144
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_101_LOD1"
    modelType: 1
  }
}
rows {
  id: 630145
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_101_LOD1"
    material: "MI_Halo_101_HP01_LOD1;MI_Halo_101_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630146
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_101_LOD1"
    material: "MI_Halo_101_HP02_LOD1;MI_Halo_101_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630147
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_108_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_108_idle_001"
  }
}
rows {
  id: 630148
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_108_LOD1"
    material: "Halo_LOD1:MI_Halo_108_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_108_2_HP01_LOD1;Halo_LOD2:MI_Halo_108_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_108_idle_001_HP01"
  }
}
rows {
  id: 630149
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_108_LOD1"
    material: "Halo_LOD1:MI_Halo_108_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_108_2_HP02_LOD1;Halo_LOD2:MI_Halo_108_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_108_idle_001_HP02"
  }
}
rows {
  id: 630150
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_093"
    modelType: 1
  }
}
rows {
  id: 630151
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_093_HP01"
    modelType: 1
  }
}
rows {
  id: 630152
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_093_HP02"
    modelType: 1
  }
}
rows {
  id: 630153
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_102"
    modelType: 1
  }
}
rows {
  id: 630154
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_105"
    modelType: 1
  }
}
rows {
  id: 630155
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_103_LOD1"
    modelType: 1
  }
}
rows {
  id: 630156
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_104_LOD1"
    modelType: 1
  }
}
rows {
  id: 630157
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_092_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_092_idle_001"
  }
}
rows {
  id: 630158
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_092_LOD1"
    material: "Halo_LOD1:MI_Halo_092_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_092_2_HP01_LOD1;Halo_LOD2:MI_Halo_092_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_092_idle_001_HP01"
  }
}
rows {
  id: 630159
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_092_LOD1"
    material: "Halo_LOD1:MI_Halo_092_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_092_2_HP02_LOD1;Halo_LOD2:MI_Halo_092_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_092_idle_001_HP02"
  }
}
rows {
  id: 630160
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_098_LOD1"
    modelType: 1
  }
}
rows {
  id: 630161
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_099_LOD1"
    modelType: 1
  }
}
rows {
  id: 630162
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_099_LOD1"
    material: "MI_Halo_099_HP01_LOD1;MI_Halo_099_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630163
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_099_LOD1"
    material: "MI_Halo_099_HP02_LOD1;MI_Halo_099_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630164
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_107"
    modelType: 1
  }
}
rows {
  id: 630165
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_117_LOD1"
    modelType: 1
  }
}
rows {
  id: 630166
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_060_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_060_idle_001"
  }
}
rows {
  id: 630167
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_060_LOD1"
    material: "Halo_LOD1:MI_Halo_060_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_060_2_HP01_LOD1;Halo_LOD2:MI_Halo_060_1_HP01_LOD2;Halo_Translucent_LOD2:MI_Halo_060_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_060_idle_HP01"
  }
}
rows {
  id: 630168
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_060_LOD1"
    material: "Halo_LOD1:MI_Halo_060_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_060_2_HP02_LOD1;Halo_LOD2:MI_Halo_060_1_HP02_LOD2;Halo_Translucent_LOD2:MI_Halo_060_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_060_idle_HP02"
  }
}
rows {
  id: 630169
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_110_LOD1"
    physics: "SK_Halo_110_Physics"
    modelType: 2
    idleAnim: "AS_Halo_110_idle_001"
  }
}
rows {
  id: 630170
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_110_LOD1"
    material: "Halo_LOD1:MI_Halo_110_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_110_2_HP01_LOD1;Halo_LOD2:MI_Halo_110_1_HP01_LOD2;Halo_Translucent_LOD2:MI_Halo_110_2_HP01_LOD2"
    physics: "SK_Halo_110_Physics"
    modelType: 2
    idleAnim: "AS_Halo_110_idle_HP01"
  }
}
rows {
  id: 630171
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_110_LOD1"
    material: "Halo_LOD1:MI_Halo_110_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_110_2_HP02_LOD1;Halo_LOD2:MI_Halo_110_1_HP02_LOD2;Halo_Translucent_LOD2:MI_Halo_110_2_HP02_LOD2"
    physics: "SK_Halo_110_Physics"
    modelType: 2
    idleAnim: "AS_Halo_110_idle_HP02"
  }
}
rows {
  id: 630172
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_096_LOD1"
    modelType: 1
  }
}
rows {
  id: 630173
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_097_LOD1"
    modelType: 1
  }
}
rows {
  id: 630174
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_109_LOD1"
    modelType: 1
  }
}
rows {
  id: 630175
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_109_LOD1"
    material: "MI_Halo_109_HP01_LOD1;MI_Halo_109_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630176
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_109_LOD1"
    material: "MI_Halo_109_HP02_LOD1;MI_Halo_109_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630177
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_113_LOD1"
    modelType: 1
  }
}
rows {
  id: 630178
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_113_LOD1"
    material: "MI_Halo_113_HP01_LOD1;MI_Halo_113_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630179
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_113_LOD1"
    material: "MI_Halo_113_HP02_LOD1;MI_Halo_113_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630180
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_112_LOD1"
    modelType: 1
  }
}
rows {
  id: 630181
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_112_LOD1"
    material: "Halo_LOD1:MI_Halo_112_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_112_2_HP01_LOD1;Halo_LOD2:MI_Halo_112_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630182
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_112_LOD1"
    material: "Halo_LOD1:MI_Halo_112_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_112_2_HP02_LOD1;Halo_LOD2:MI_Halo_112_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630183
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_121_LOD1"
    modelType: 1
  }
}
rows {
  id: 630184
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_121_LOD1"
    material: "MI_Halo_121_HP01_LOD1;MI_Halo_121_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630185
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_121_LOD1"
    material: "MI_Halo_121_HP02_LOD1;MI_Halo_121_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630186
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_120_LOD1"
    modelType: 1
  }
}
rows {
  id: 630187
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_120_LOD1"
    material: "MI_Halo_120_HP01_LOD1;MI_Halo_120_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630188
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_120_LOD1"
    material: "MI_Halo_120_HP02_LOD1;MI_Halo_120_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630189
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_123_LOD1"
    modelType: 1
  }
}
rows {
  id: 630190
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_094_LOD1"
    modelType: 1
  }
}
rows {
  id: 630191
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_094_LOD1"
    material: "MI_Halo_094_HP01_LOD1;MI_Halo_094_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630192
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_094_LOD1"
    material: "MI_Halo_094_HP02_LOD1;MI_Halo_094_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630193
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_115_LOD1"
    modelType: 1
  }
}
rows {
  id: 630194
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_116_LOD1"
    modelType: 1
  }
}
rows {
  id: 630195
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_114_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_114_idle_001"
  }
}
rows {
  id: 630196
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_114_LOD1"
    material: "Halo_LOD1:MI_Halo_114_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_114_2_HP01_LOD1;Halo_LOD2:MI_Halo_114_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_114_idle_001_HP01"
  }
}
rows {
  id: 630197
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_114_LOD1"
    material: "Halo_LOD1:MI_Halo_114_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_114_2_HP02_LOD1;Halo_LOD2:MI_Halo_114_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_114_idle_001_HP02"
  }
}
rows {
  id: 630198
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_122_LOD1"
    modelType: 1
  }
}
rows {
  id: 630199
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_122_LOD1"
    material: "MI_Halo_122_HP01_LOD1;MI_Halo_122_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630200
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_122_LOD1"
    material: "MI_Halo_122_HP02_LOD1;MI_Halo_122_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630201
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_119_LOD1"
    modelType: 1
  }
}
rows {
  id: 630202
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_119_LOD1"
    material: "MI_Halo_119_HP01_LOD1;MI_Halo_119_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630203
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_119_LOD1"
    material: "MI_Halo_119_HP02_LOD1;MI_Halo_119_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630204
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_131_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_131_idle_001"
  }
}
rows {
  id: 630205
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_131_LOD1"
    material: "Halo_LOD1:MI_Halo_131_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_131_2_HP01_LOD1;Halo_LOD2:MI_Halo_131_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_131_idle_001_HP01"
  }
}
rows {
  id: 630206
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_131_LOD1"
    material: "Halo_LOD1:MI_Halo_131_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_131_2_HP02_LOD1;Halo_LOD2:MI_Halo_131_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_131_idle_001_HP02"
  }
}
rows {
  id: 630207
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_124_LOD1"
    modelType: 1
  }
}
rows {
  id: 630208
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_124_LOD1"
    material: "MI_Halo_124_HP01_LOD1;MI_Halo_124_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630209
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_124_LOD1"
    material: "MI_Halo_124_HP02_LOD1;MI_Halo_124_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630210
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_125_LOD1"
    modelType: 1
  }
}
rows {
  id: 630211
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_125_LOD1"
    material: "MI_Halo_125_HP01_LOD1;MI_Halo_125_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630212
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_125_LOD1"
    material: "MI_Halo_125_HP02_LOD1;MI_Halo_125_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630213
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_118_LOD1"
    modelType: 1
  }
}
rows {
  id: 630214
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_118_LOD1"
    material: "Halo_LOD1:MI_Halo_118_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_118_2_HP01_LOD1;Halo_LOD2:MI_Halo_118_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630215
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_118_LOD1"
    material: "Halo_LOD1:MI_Halo_118_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_118_2_HP02_LOD1;Halo_LOD2:MI_Halo_118_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630216
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_126_LOD1"
    modelType: 1
  }
}
rows {
  id: 630217
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_127_LOD1"
    modelType: 1
  }
}
rows {
  id: 630218
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_128_LOD1"
    modelType: 1
  }
}
rows {
  id: 630219
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_141"
    modelType: 1
  }
}
rows {
  id: 630220
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_141_HP01"
    modelType: 1
  }
}
rows {
  id: 630221
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_141_HP02"
    modelType: 1
  }
}
rows {
  id: 630222
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_111_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_111_idle_001"
  }
}
rows {
  id: 630223
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_111_LOD1"
    material: "Halo_LOD1:MI_Halo_111_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_111_2_HP01_LOD1;Halo_LOD2:MI_Halo_111_1_HP01_LOD2;Halo_Translucent_LOD2:MI_Halo_111_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_111_idle_001_HP01"
  }
}
rows {
  id: 630224
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_111_LOD1"
    material: "Halo_LOD1:MI_Halo_111_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_111_2_HP02_LOD1;Halo_LOD2:MI_Halo_111_1_HP02_LOD2;Halo_Translucent_LOD2:MI_Halo_111_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_111_idle_001_HP02"
  }
}
rows {
  id: 630225
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_132_LOD1"
    modelType: 1
  }
}
rows {
  id: 630226
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_132_LOD1"
    material: "MI_Halo_132_HP01_LOD1;MI_Halo_132_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630227
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_132_LOD1"
    material: "MI_Halo_132_HP02_LOD1;MI_Halo_132_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630228
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_129_LOD1"
    modelType: 1
  }
}
rows {
  id: 630229
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_148"
    modelType: 1
  }
}
rows {
  id: 630230
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_138_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_idle_001"
  }
}
rows {
  id: 630231
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_138_LOD1"
    material: "Halo_LOD1:MI_Halo_138_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_138_2_HP01_LOD1;Halo_LOD2:MI_Halo_138_1_HP01_LOD2;Halo_Translucent_LOD2:MI_Halo_138_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_idle_001_HP01"
  }
}
rows {
  id: 630232
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_138_LOD1"
    material: "Halo_LOD1:MI_Halo_138_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_138_2_HP02_LOD1;Halo_LOD2:MI_Halo_138_1_HP02_LOD2;Halo_Translucent_LOD2:MI_Halo_138_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_idle_001_HP02"
  }
}
rows {
  id: 630233
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_149_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_149_idle_001"
  }
}
rows {
  id: 630234
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_149_LOD1"
    material: "Halo_LOD1:MI_Halo_149_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_149_2_HP01_LOD1;Halo_LOD2:MI_Halo_149_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_149_idle_001_HP01"
  }
}
rows {
  id: 630235
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_149_LOD1"
    material: "Halo_LOD1:MI_Halo_149_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_149_2_HP02_LOD1;Halo_LOD2:MI_Halo_149_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_149_idle_001_HP02"
  }
}
rows {
  id: 630236
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_130_LOD1"
    modelType: 1
  }
}
rows {
  id: 630237
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_133_LOD1"
    modelType: 1
  }
}
rows {
  id: 630238
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_133_LOD1"
    material: "MI_Halo_133_HP01_LOD1;MI_Halo_133_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630239
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_133_LOD1"
    material: "MI_Halo_133_HP02_LOD1;MI_Halo_133_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630240
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_134_LOD1"
    modelType: 1
  }
}
rows {
  id: 630241
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_134_LOD1"
    material: "MI_Halo_134_HP01_LOD1;MI_Halo_134_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630242
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_134_LOD1"
    material: "MI_Halo_134_HP02_LOD1;MI_Halo_134_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630243
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_135_LOD1"
    modelType: 1
  }
}
rows {
  id: 630244
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_139_LOD1"
    modelType: 1
  }
}
rows {
  id: 630245
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_137_LOD1"
    modelType: 1
  }
}
rows {
  id: 630246
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_163"
    modelType: 1
  }
}
rows {
  id: 630247
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_164"
    modelType: 1
  }
}
rows {
  id: 630248
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_143_LOD1"
    modelType: 1
  }
}
rows {
  id: 630249
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_143_LOD1"
    material: "Halo_LOD1:MI_Halo_143_1_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_143_2_HP01_LOD1;Halo_LOD2:MI_Halo_143_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630250
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_143_LOD1"
    material: "Halo_LOD1:MI_Halo_143_1_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_143_2_HP02_LOD1;Halo_LOD2:MI_Halo_143_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630251
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_142"
    modelType: 1
  }
}
rows {
  id: 630252
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_136_LOD1"
    modelType: 1
  }
}
rows {
  id: 630253
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_140"
    modelType: 1
  }
}
rows {
  id: 630254
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_140_HP01"
    modelType: 1
  }
}
rows {
  id: 630255
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_140_HP02"
    modelType: 1
  }
}
rows {
  id: 630256
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_165_LOD1"
    modelType: 1
  }
}
rows {
  id: 630257
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_147_LOD1"
    modelType: 1
  }
}
rows {
  id: 630258
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_150"
    modelType: 1
  }
}
rows {
  id: 630259
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_151_LOD1"
    modelType: 1
  }
}
rows {
  id: 630260
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_152_LOD1"
    modelType: 1
  }
}
rows {
  id: 630261
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_153_LOD1"
    modelType: 1
  }
}
rows {
  id: 630262
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_155_LOD1"
    modelType: 1
  }
}
rows {
  id: 630263
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_160"
    modelType: 1
  }
}
rows {
  id: 630264
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_146_LOD1"
    modelType: 1
  }
}
rows {
  id: 630265
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_157_LOD1"
    modelType: 1
  }
}
rows {
  id: 630266
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_156_LOD1"
    modelType: 1
  }
}
rows {
  id: 630267
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_145_LOD1"
    modelType: 1
  }
}
rows {
  id: 630268
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_145_LOD1"
    material: "MI_Halo_145_HP01_LOD1;MI_Halo_145_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630269
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_145_LOD1"
    material: "MI_Halo_145_HP02_LOD1;MI_Halo_145_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630270
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_144_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_144_idle_001"
  }
}
rows {
  id: 630271
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_144_LOD1"
    material: "Halo_LOD1:MI_Halo_144_HP01_LOD1;Halo_LOD2:MI_Halo_144_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_144_idle_001_HP01"
  }
}
rows {
  id: 630272
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_144_LOD1"
    material: "Halo_LOD1:MI_Halo_144_HP02_LOD1;Halo_LOD2:MI_Halo_144_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_144_idle_001_HP02"
  }
}
rows {
  id: 630273
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_161_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_161_idle_001"
  }
}
rows {
  id: 630274
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_161_LOD1"
    material: "Halo_1_LOD1:MI_Halo_161_1_HP01_LOD1;Halo_2_LOD1:MI_Halo_161_2_HP01_LOD1;Halo_1_LOD2:MI_Halo_161_1_HP01_LOD2;Halo_2_LOD2:MI_Halo_161_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_161_idle_001_HP01"
  }
}
rows {
  id: 630275
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_161_LOD1"
    material: "Halo_1_LOD1:MI_Halo_161_1_HP02_LOD1;Halo_2_LOD1:MI_Halo_161_2_HP02_LOD1;Halo_1_LOD2:MI_Halo_161_1_HP02_LOD2;Halo_2_LOD2:MI_Halo_161_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_161_idle_001_HP02"
  }
}
rows {
  id: 630276
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_154_LOD1"
    modelType: 1
  }
}
rows {
  id: 630277
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_174"
    modelType: 1
  }
}
rows {
  id: 630278
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_173"
    modelType: 1
  }
}
rows {
  id: 630279
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_158_LOD1"
    modelType: 1
  }
}
rows {
  id: 630280
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_176_LOD1"
    modelType: 1
  }
}
rows {
  id: 630281
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_166_LOD1"
    emitter: "FX_CH_Decorate_Halo_166_001"
    modelType: 2
  }
}
rows {
  id: 630282
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_166_LOD1"
    material: "Halo_1_LOD1:MI_Halo_166_1_HP01_LOD1;Halo_2_LOD1:MI_Halo_166_2_HP01_LOD1;Halo_3_LOD1:MI_Halo_166_3_HP01_LOD1;Halo_LOD2:MI_Halo_166_HP01_LOD2"
    emitter: "FX_CH_Decorate_Halo_166_001_HP01"
    modelType: 2
  }
}
rows {
  id: 630283
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_166_LOD1"
    material: "Halo_1_LOD1:MI_Halo_166_1_HP02_LOD1;Halo_2_LOD1:MI_Halo_166_2_HP02_LOD1;Halo_3_LOD1:MI_Halo_166_3_HP02_LOD1;Halo_LOD2:MI_Halo_166_HP02_LOD2"
    emitter: "FX_CH_Decorate_Halo_166_001_HP02"
    modelType: 2
  }
}
rows {
  id: 630284
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_168_LOD1"
    modelType: 1
  }
}
rows {
  id: 630285
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_168_LOD1"
    material: "MI_Halo_168_HP01_LOD1;MI_Halo_168_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630286
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_168_LOD1"
    material: "MI_Halo_168_HP02_LOD1;MI_Halo_168_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630287
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_169_LOD1"
    modelType: 1
  }
}
rows {
  id: 630288
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_169_LOD1"
    material: "MI_Halo_169_HP01_LOD1;MI_Halo_169_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630289
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_169_LOD1"
    material: "MI_Halo_169_HP02_LOD1;MI_Halo_169_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630290
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_172"
    modelType: 1
  }
}
rows {
  id: 630291
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_162_LOD1"
    modelType: 1
  }
}
rows {
  id: 630292
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_159_LOD1"
    modelType: 1
  }
}
rows {
  id: 630293
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_196_LOD1"
    modelType: 1
  }
}
rows {
  id: 630294
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_188_LOD1"
    modelType: 1
  }
}
rows {
  id: 630295
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_189_LOD1"
    modelType: 1
  }
}
rows {
  id: 630296
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_178_LOD1"
    modelType: 1
  }
}
rows {
  id: 630297
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_177_LOD1"
    modelType: 1
  }
}
rows {
  id: 630298
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_194"
    modelType: 1
  }
}
rows {
  id: 630299
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_195"
    modelType: 1
  }
}
rows {
  id: 630300
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_167"
    modelType: 1
  }
}
rows {
  id: 630301
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_167_HP01"
    modelType: 1
  }
}
rows {
  id: 630302
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_167_HP02"
    modelType: 1
  }
}
rows {
  id: 630303
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_175"
    modelType: 1
  }
}
rows {
  id: 630304
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_175_HP01"
    modelType: 1
  }
}
rows {
  id: 630305
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_175_HP02"
    modelType: 1
  }
}
rows {
  id: 630306
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_170_LOD1"
    modelType: 1
  }
}
rows {
  id: 630307
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_186_LOD1"
    modelType: 1
  }
}
rows {
  id: 630308
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_186_LOD1"
    material: "MI_Halo_186_HP01_LOD1;MI_Halo_186_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630309
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_186_LOD1"
    material: "MI_Halo_186_HP02_LOD1;MI_Halo_186_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630310
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_192_LOD1"
    modelType: 1
  }
}
rows {
  id: 630311
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_193_LOD1"
    modelType: 1
  }
}
rows {
  id: 630312
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_182_LOD1"
    modelType: 1
  }
}
rows {
  id: 630313
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_183"
    modelType: 1
  }
}
rows {
  id: 630314
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_184"
    modelType: 1
  }
}
rows {
  id: 630315
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_181"
    modelType: 1
  }
}
rows {
  id: 630316
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_179"
    modelType: 1
  }
}
rows {
  id: 630317
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_180"
    modelType: 1
  }
}
rows {
  id: 630318
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_187_LOD1"
    modelType: 1
  }
}
rows {
  id: 630319
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_187_LOD1"
    material: "MI_Halo_187_HP01_LOD1;MI_Halo_187_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630320
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_187_LOD1"
    material: "MI_Halo_187_HP02_LOD1;MI_Halo_187_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630321
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_191"
    modelType: 1
  }
}
rows {
  id: 630322
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_191_HP01"
    modelType: 1
  }
}
rows {
  id: 630323
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_191_HP02"
    modelType: 1
  }
}
rows {
  id: 630324
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_171_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_171_idle_001"
  }
}
rows {
  id: 630325
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_171_LOD1"
    material: "Halo_1_LOD1:MI_Halo_171_1_HP01_LOD1;Halo_2_LOD1:MI_Halo_171_2_HP01_LOD1;Halo_3_LOD1:MI_Halo_171_3_HP01_LOD1;Halo_4_LOD1:MI_Halo_171_4_HP01_LOD1;Halo_1_LOD2:MI_Halo_171_1_HP01_LOD2;Halo_2_LOD2:MI_Halo_171_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_171_idle_001_HP01"
  }
}
rows {
  id: 630326
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_171_LOD1"
    material: "Halo_1_LOD1:MI_Halo_171_1_HP02_LOD1;Halo_2_LOD1:MI_Halo_171_2_HP02_LOD1;Halo_3_LOD1:MI_Halo_171_3_HP02_LOD1;Halo_4_LOD1:MI_Halo_171_4_HP02_LOD1;Halo_1_LOD2:MI_Halo_171_1_HP02_LOD2;Halo_2_LOD2:MI_Halo_171_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_171_idle_001_HP02"
  }
}
rows {
  id: 630327
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_200_LOD1"
    modelType: 1
  }
}
rows {
  id: 630328
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_198_LOD1"
    modelType: 1
  }
}
rows {
  id: 630329
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_185_LOD1"
    modelType: 1
  }
}
rows {
  id: 630330
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_185_LOD1"
    material: "MI_Halo_185_HP01_LOD1;MI_Halo_185_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630331
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_185_LOD1"
    material: "MI_Halo_185_HP02_LOD1;MI_Halo_185_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630332
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_199_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_199_idle_001"
  }
}
rows {
  id: 630333
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_199_LOD1"
    material: "Halo_LOD1:MI_Halo_199_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_199_1_HP01_LOD1;Halo_LOD2:MI_Halo_199_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_199_idle_001_HP01"
  }
}
rows {
  id: 630334
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_199_LOD1"
    material: "Halo_LOD1:MI_Halo_199_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_199_1_HP02_LOD1;Halo_LOD2:MI_Halo_199_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_199_idle_001_HP02"
  }
}
rows {
  id: 630335
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_197_LOD1"
    modelType: 1
  }
}
rows {
  id: 630336
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_190_LOD1"
    modelType: 1
  }
}
rows {
  id: 630337
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_190_LOD1"
    material: "MI_Halo_190_HP01_LOD1;MI_Halo_190_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630338
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_190_LOD1"
    material: "MI_Halo_190_HP02_LOD1;MI_Halo_190_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630339
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_227_LOD1"
    modelType: 1
  }
}
rows {
  id: 630340
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_228_LOD1"
    modelType: 1
  }
}
rows {
  id: 630341
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_229_LOD1"
    modelType: 1
  }
}
rows {
  id: 630342
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_230_LOD1"
    modelType: 1
  }
}
rows {
  id: 630343
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_231_LOD1"
    modelType: 1
  }
}
rows {
  id: 630344
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_232_LOD1"
    modelType: 1
  }
}
rows {
  id: 630345
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_227_LOD1"
    material: "MI_Halo_227_HP01_LOD1;MI_Halo_227_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630346
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_227_LOD1"
    material: "MI_Halo_227_HP02_LOD1;MI_Halo_227_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630347
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_215_LOD1"
    modelType: 1
  }
}
rows {
  id: 630348
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_215_LOD1"
    material: "MI_Halo_215_HP01_LOD1;MI_Halo_215_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630349
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_215_LOD1"
    material: "MI_Halo_215_HP02_LOD1;MI_Halo_215_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630350
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_214_LOD1"
    modelType: 1
  }
}
rows {
  id: 630351
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_213_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_213_idle_001"
  }
}
rows {
  id: 630352
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_213_LOD1"
    material: "Halo_LOD1:MI_Halo_213_HP01_LOD1;Halo_LOD2:MI_Halo_213_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_213_idle_001_HP01"
  }
}
rows {
  id: 630353
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_213_LOD1"
    material: "Halo_LOD1:MI_Halo_213_HP02_LOD1;Halo_LOD2:MI_Halo_213_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_213_idle_001_HP02"
  }
}
rows {
  id: 630354
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_201_LOD1"
    modelType: 1
  }
}
rows {
  id: 630355
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_201_LOD1"
    material: "MI_Halo_201_HP01_LOD1;MI_Halo_201_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630356
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_201_LOD1"
    material: "MI_Halo_201_HP02_LOD1;MI_Halo_201_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630357
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_206"
    modelType: 1
  }
}
rows {
  id: 630358
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_202_LOD1"
    modelType: 1
  }
}
rows {
  id: 630359
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_208_LOD1"
    modelType: 1
  }
}
rows {
  id: 630360
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_204_LOD1"
    modelType: 1
  }
}
rows {
  id: 630361
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_207_LOD1"
    modelType: 1
  }
}
rows {
  id: 630362
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_207_LOD1"
    material: "MI_Halo_207_HP01_LOD1;MI_Halo_207_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630363
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_207_LOD1"
    material: "MI_Halo_207_HP02_LOD1;MI_Halo_207_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630364
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_217_LOD1"
    modelType: 1
  }
}
rows {
  id: 630365
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_217_LOD1"
    material: "MI_Halo_217_HP01_LOD1;MI_Halo_217_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630366
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_217_LOD1"
    material: "MI_Halo_217_HP02_LOD1;MI_Halo_217_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630367
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_210_LOD1"
    modelType: 1
  }
}
rows {
  id: 630368
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_219"
    modelType: 1
  }
}
rows {
  id: 630369
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_205"
    modelType: 1
  }
}
rows {
  id: 630370
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_216"
    modelType: 1
  }
}
rows {
  id: 630371
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_216_HP01"
    modelType: 1
  }
}
rows {
  id: 630372
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_216_HP02"
    modelType: 1
  }
}
rows {
  id: 630373
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_209_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_209_idle_001"
  }
}
rows {
  id: 630374
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_209_LOD1"
    material: "Halo_LOD1:MI_Halo_209_HP01_LOD1;Halo_LOD2:MI_Halo_209_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_209_idle_001_HP01"
  }
}
rows {
  id: 630375
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_209_LOD1"
    material: "Halo_LOD1:MI_Halo_209_HP02_LOD1;Halo_LOD2:MI_Halo_209_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_209_idle_001_HP02"
  }
}
rows {
  id: 630376
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_212_LOD1"
    modelType: 1
  }
}
rows {
  id: 630377
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_212_LOD1"
    material: "MI_Halo_212_HP01_LOD1;MI_Halo_212_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630378
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_212_LOD1"
    material: "MI_Halo_212_HP02_LOD1;MI_Halo_212_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630379
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_211_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_211_idle_001"
  }
}
rows {
  id: 630380
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_211_LOD1"
    material: "MI_Halo_211_HP01_LOD1;MI_Halo_211_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_211_idle_001_HP01"
  }
}
rows {
  id: 630381
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_211_LOD1"
    material: "MI_Halo_211_HP02_LOD1;MI_Halo_211_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_211_idle_001_HP02"
  }
}
rows {
  id: 630382
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_225"
    modelType: 1
  }
}
rows {
  id: 630383
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_224_LOD1"
    modelType: 1
  }
}
rows {
  id: 630384
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_220_LOD1"
    modelType: 1
  }
}
rows {
  id: 630385
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_220_LOD1"
    material: "MI_Halo_220_HP01_LOD1;MI_Halo_220_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630386
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_220_LOD1"
    material: "MI_Halo_220_HP02_LOD1;MI_Halo_220_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630387
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_238_LOD1"
    modelType: 1
  }
}
rows {
  id: 630388
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_218_LOD1"
    modelType: 1
  }
}
rows {
  id: 630389
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_226_LOD1"
    modelType: 1
  }
}
rows {
  id: 630390
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_226_LOD1"
    material: "MI_Halo_226_HP01_LOD1;MI_Halo_226_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630391
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_226_LOD1"
    material: "MI_Halo_226_HP02_LOD1;MI_Halo_226_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630392
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_234_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_234_idle_001"
  }
}
rows {
  id: 630393
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_234_LOD1"
    material: "MI_Halo_234_HP01_LOD1;MI_Halo_234_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_234_idle_001_HP01"
  }
}
rows {
  id: 630394
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_234_LOD1"
    material: "MI_Halo_234_HP02_LOD1;MI_Halo_234_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_234_idle_001_HP02"
  }
}
rows {
  id: 630395
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_203_LOD1"
    modelType: 1
  }
}
rows {
  id: 630396
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_235_LOD1"
    modelType: 1
  }
}
rows {
  id: 630397
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_235_LOD1"
    material: "MI_Halo_235_HP01_LOD1;MI_Halo_235_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630398
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_235_LOD1"
    material: "MI_Halo_235_HP02_LOD1;MI_Halo_235_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630399
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_237_LOD1"
    modelType: 1
  }
}
rows {
  id: 630400
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_237_LOD1"
    material: "MI_Halo_237_HP01_LOD1;MI_Halo_237_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630401
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_237_LOD1"
    material: "MI_Halo_237_HP02_LOD1;MI_Halo_237_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630402
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_239_LOD1"
    modelType: 1
  }
}
rows {
  id: 630403
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_240_LOD1"
    modelType: 1
  }
}
rows {
  id: 630404
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_241_LOD1"
    modelType: 1
  }
}
rows {
  id: 630405
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_233_LOD1"
    modelType: 1
  }
}
rows {
  id: 630406
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_257"
    modelType: 1
  }
}
rows {
  id: 630407
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_243"
    modelType: 1
  }
}
rows {
  id: 630408
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_256_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_256_idle_001"
  }
}
rows {
  id: 630409
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_256_LOD1"
    material: "MI_Halo_256_HP01_LOD1;MI_Halo_256_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_256_idle_001_HP01"
  }
}
rows {
  id: 630410
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_256_LOD1"
    material: "MI_Halo_256_HP02_LOD1;MI_Halo_256_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_256_idle_001_HP02"
  }
}
rows {
  id: 630411
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_221_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_221_idle_001"
  }
}
rows {
  id: 630412
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_221_LOD1"
    material: "MI_Halo_221_HP01_LOD1;MI_Halo_221_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_221_idle_001_HP01"
  }
}
rows {
  id: 630413
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_221_LOD1"
    material: "MI_Halo_221_HP02_LOD1;MI_Halo_221_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_221_idle_001_HP02"
  }
}
rows {
  id: 630414
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_222_LOD1"
    modelType: 1
  }
}
rows {
  id: 630415
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_222_LOD1"
    material: "MI_Halo_222_HP01_LOD1;MI_Halo_222_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630416
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_222_LOD1"
    material: "MI_Halo_222_HP02_LOD1;MI_Halo_222_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630417
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_223_LOD1"
    modelType: 1
  }
}
rows {
  id: 630418
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_223_LOD1"
    material: "MI_Halo_223_HP01_LOD1;MI_Halo_223_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630419
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_223_LOD1"
    material: "MI_Halo_223_HP02_LOD1;MI_Halo_223_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630420
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_283_LOD1"
    modelType: 1
  }
}
rows {
  id: 630421
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_251"
    modelType: 1
  }
}
rows {
  id: 630422
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_294"
    modelType: 1
  }
}
rows {
  id: 630423
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_253_LOD1"
    modelType: 1
  }
}
rows {
  id: 630424
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_253_LOD1"
    material: "MI_Halo_253_HP01_LOD1;MI_Halo_253_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630425
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_253_LOD1"
    material: "MI_Halo_253_HP02_LOD1;MI_Halo_253_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630426
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_293_LOD1"
    modelType: 1
  }
}
rows {
  id: 630427
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_293_LOD1"
    material: "MI_Halo_293_HP01_LOD1;MI_Halo_293_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630428
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_293_LOD1"
    material: "MI_Halo_293_HP02_LOD1;MI_Halo_293_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630429
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_295_LOD1"
    modelType: 1
  }
}
rows {
  id: 630430
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_274"
    modelType: 1
  }
}
rows {
  id: 630431
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_274_HP01"
    modelType: 1
  }
}
rows {
  id: 630432
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_274_HP02"
    modelType: 1
  }
}
rows {
  id: 630433
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_258_LOD1"
    modelType: 1
  }
}
rows {
  id: 630434
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_248_LOD1"
    modelType: 1
  }
}
rows {
  id: 630435
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_296_LOD1"
    modelType: 1
  }
}
rows {
  id: 630436
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_266_LOD1"
    modelType: 1
  }
}
rows {
  id: 630437
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_266_LOD1"
    material: "MI_Halo_266_HP01_LOD1;MI_Halo_266_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630438
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_266_LOD1"
    material: "MI_Halo_266_HP02_LOD1;MI_Halo_266_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630439
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_265_LOD1"
    modelType: 1
  }
}
rows {
  id: 630440
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_265_LOD1"
    material: "MI_Halo_265_HP01_LOD1;MI_Halo_265_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630441
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_265_LOD1"
    material: "MI_Halo_265_HP02_LOD1;MI_Halo_265_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630442
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_260_LOD1"
    modelType: 1
  }
}
rows {
  id: 630443
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_261_LOD1"
    modelType: 1
  }
}
rows {
  id: 630444
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_305"
    modelType: 1
  }
}
rows {
  id: 630445
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_254_LOD1"
    modelType: 1
  }
}
rows {
  id: 630446
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_254_LOD1"
    material: "MI_Halo_254_HP01_LOD1;MI_Halo_254_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630447
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_254_LOD1"
    material: "MI_Halo_254_HP02_LOD1;MI_Halo_254_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630448
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_277_LOD1"
    modelType: 1
  }
}
rows {
  id: 630449
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_277_LOD1"
    material: "MI_Halo_277_HP01_LOD1;MI_Halo_277_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630450
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_277_LOD1"
    material: "MI_Halo_277_HP02_LOD1;MI_Halo_277_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630451
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_246"
    modelType: 1
  }
}
rows {
  id: 630452
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_272_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_272_idle_001"
  }
}
rows {
  id: 630453
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_272_LOD1"
    material: "Halo_1_LOD1:MI_Halo_272_1_HP01_LOD1;Halo_2_LOD1:MI_Halo_272_2_HP01_LOD1;Halo_3_LOD1:MI_Halo_272_3_HP01_LOD1;Halo_LOD2:MI_Halo_272_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_272_idle_001_HP01"
  }
}
rows {
  id: 630454
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_272_LOD1"
    material: "Halo_1_LOD1:MI_Halo_272_1_HP02_LOD1;Halo_2_LOD1:MI_Halo_272_2_HP02_LOD1;Halo_3_LOD1:MI_Halo_272_3_HP02_LOD1;Halo_LOD2:MI_Halo_272_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_272_idle_001_HP02"
  }
}
rows {
  id: 630455
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_291_LOD1"
    modelType: 1
  }
}
rows {
  id: 630456
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_291_LOD1"
    material: "MI_Halo_291_HP01_LOD1;MI_Halo_291_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630457
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_291_LOD1"
    material: "MI_Halo_291_HP02_LOD1;MI_Halo_291_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630458
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_259_LOD1"
    modelType: 1
  }
}
rows {
  id: 630459
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_255_LOD1"
    modelType: 1
  }
}
rows {
  id: 630460
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_255_LOD1"
    material: "MI_Halo_255_HP01_LOD1;MI_Halo_255_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630461
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_255_LOD1"
    material: "MI_Halo_255_HP02_LOD1;MI_Halo_255_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630462
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_249_LOD1"
    modelType: 1
  }
}
rows {
  id: 630463
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_250"
    modelType: 1
  }
}
rows {
  id: 630464
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_299_LOD1"
    modelType: 1
  }
}
rows {
  id: 630465
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_299_LOD1"
    material: "MI_Halo_299_HP01_LOD1;MI_Halo_299_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630466
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_299_LOD1"
    material: "MI_Halo_299_HP02_LOD1;MI_Halo_299_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630467
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_264"
    modelType: 1
  }
}
rows {
  id: 630468
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_264_HP01"
    modelType: 1
  }
}
rows {
  id: 630469
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_264_HP02"
    modelType: 1
  }
}
rows {
  id: 630470
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_262"
    modelType: 1
  }
}
rows {
  id: 630471
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_263"
    modelType: 1
  }
}
rows {
  id: 630472
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_302"
    modelType: 1
  }
}
rows {
  id: 630473
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_303"
    modelType: 1
  }
}
rows {
  id: 630474
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_304"
    modelType: 1
  }
}
rows {
  id: 630475
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_269"
    modelType: 1
  }
}
rows {
  id: 630476
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_310_LOD1"
    modelType: 1
  }
}
rows {
  id: 630477
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_298_LOD1"
    emitter: "FX_CH_Decorate_Halo_298_001"
    modelType: 1
  }
}
rows {
  id: 630478
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_298_LOD1"
    material: "Halo_LOD1:MI_Halo_298_HP01_LOD1;Halo_LOD2:MI_Halo_298_HP01_LOD2"
    emitter: "FX_CH_Decorate_Halo_298_001_HP01"
    modelType: 1
  }
}
rows {
  id: 630479
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_298_LOD1"
    material: "Halo_LOD1:MI_Halo_298_HP02_LOD1;Halo_LOD2:MI_Halo_298_HP02_LOD2"
    emitter: "FX_CH_Decorate_Halo_298_001_HP02"
    modelType: 1
  }
}
rows {
  id: 630480
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_247_LOD1"
    modelType: 1
  }
}
rows {
  id: 630481
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_245_LOD1"
    modelType: 1
  }
}
rows {
  id: 630482
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_244_LOD1"
    modelType: 1
  }
}
rows {
  id: 630483
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_242_LOD1"
    modelType: 1
  }
}
rows {
  id: 630484
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_307_LOD1"
    emitter: "FX_CH_Decorate_Halo_307_001"
    modelType: 1
  }
}
rows {
  id: 630485
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_307_LOD1"
    material: "Halo_LOD1:MI_Halo_307_HP01_LOD1;Halo_LOD2:MI_Halo_307_HP01_LOD2"
    emitter: "FX_CH_Decorate_Halo_307_001_HP01"
    modelType: 1
  }
}
rows {
  id: 630486
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_307_LOD1"
    material: "Halo_LOD1:MI_Halo_307_HP02_LOD1;Halo_LOD2:MI_Halo_307_HP02_LOD2"
    emitter: "FX_CH_Decorate_Halo_307_001_HP02"
    modelType: 1
  }
}
rows {
  id: 630487
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_285_LOD1"
    modelType: 1
  }
}
rows {
  id: 630488
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_300_LOD1"
    modelType: 1
  }
}
rows {
  id: 630489
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_306_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_306_idle_001"
  }
}
rows {
  id: 630490
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_306_LOD1"
    material: "MI_Halo_306_HP01_LOD1;MI_Halo_306_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_306_idle_001_HP01"
  }
}
rows {
  id: 630491
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_306_LOD1"
    material: "MI_Halo_306_HP02_LOD1;MI_Halo_306_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_306_idle_001_HP02"
  }
}
rows {
  id: 630492
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_279_LOD1"
    modelType: 1
  }
}
rows {
  id: 630493
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_279_LOD1"
    material: "MI_Halo_279_HP01_LOD1;MI_Halo_279_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630494
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_279_LOD1"
    material: "MI_Halo_279_HP02_LOD1;MI_Halo_279_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630495
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_284_LOD1"
    modelType: 1
  }
}
rows {
  id: 630496
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_284_LOD1"
    material: "MI_Halo_284_HP01_LOD1;MI_Halo_284_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630497
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_284_LOD1"
    material: "MI_Halo_284_HP02_LOD1;MI_Halo_284_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630498
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_280_LOD1"
    modelType: 1
  }
}
rows {
  id: 630499
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_280_LOD1"
    material: "MI_Halo_280_HP01_LOD1;MI_Halo_280_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630500
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_280_LOD1"
    material: "MI_Halo_280_HP02_LOD1;MI_Halo_280_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630501
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_267_LOD1"
    modelType: 1
  }
}
rows {
  id: 630502
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_267_LOD1"
    material: "MI_Halo_267_HP01_LOD1;MI_Halo_267_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630503
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_267_LOD1"
    material: "MI_Halo_267_HP02_LOD1;MI_Halo_267_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630504
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_278_LOD1"
    modelType: 1
  }
}
rows {
  id: 630505
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_278_LOD1"
    material: "MI_Halo_278_HP01_LOD1;MI_Halo_278_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630506
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_278_LOD1"
    material: "MI_Halo_278_HP02_LOD1;MI_Halo_278_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630507
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_290_LOD1"
    modelType: 1
  }
}
rows {
  id: 630508
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_290_LOD1"
    material: "MI_Halo_290_HP01_LOD1;MI_Halo_290_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630509
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_290_LOD1"
    material: "MI_Halo_290_HP02_LOD1;MI_Halo_290_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630510
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_281_LOD1"
    modelType: 1
  }
}
rows {
  id: 630511
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_282_LOD1"
    modelType: 1
  }
}
rows {
  id: 630512
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_275_LOD1"
    modelType: 1
  }
}
rows {
  id: 630513
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_271_LOD1"
    modelType: 1
  }
}
rows {
  id: 630514
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_317_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_317_idle_001"
  }
}
rows {
  id: 630515
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_317_LOD1"
    material: "MI_Halo_317_HP01_LOD1;MI_Halo_317_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_317_idle_001_HP01"
  }
}
rows {
  id: 630516
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_317_LOD1"
    material: "MI_Halo_317_HP02_LOD1;MI_Halo_317_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_317_idle_001_HP02"
  }
}
rows {
  id: 630517
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_273_LOD1"
    modelType: 1
  }
}
rows {
  id: 630518
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_270_LOD1"
    modelType: 1
  }
}
rows {
  id: 630519
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_297_LOD1"
    modelType: 1
  }
}
rows {
  id: 630520
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_297_LOD1"
    material: "MI_Halo_297_HP01_LOD1;MI_Halo_297_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630521
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_297_LOD1"
    material: "MI_Halo_297_HP02_LOD1;MI_Halo_297_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630522
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_335_LOD1"
    modelType: 1
  }
}
rows {
  id: 630523
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_326_LOD1"
    modelType: 1
  }
}
rows {
  id: 630524
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_326_LOD1"
    material: "MI_Halo_326_HP01_LOD1;MI_Halo_326_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630525
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_326_LOD1"
    material: "MI_Halo_326_HP02_LOD1;MI_Halo_326_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630526
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_327_LOD1"
    modelType: 1
  }
}
rows {
  id: 630527
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_327_LOD1"
    material: "MI_Halo_327_HP01_LOD1;MI_Halo_327_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630528
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_327_LOD1"
    material: "MI_Halo_327_HP02_LOD1;MI_Halo_327_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630529
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_308_LOD1"
    modelType: 1
  }
}
rows {
  id: 630530
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_318"
    modelType: 1
  }
}
rows {
  id: 630531
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_334_LOD1"
    modelType: 1
  }
}
rows {
  id: 630532
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_334_LOD1"
    material: "Halo_1_LOD1:MI_Halo_334_1_HP01_LOD1;Halo_2_LOD1:MI_Halo_334_2_HP01_LOD1;Halo_1_LOD2:MI_Halo_334_1_HP01_LOD2;Halo_2_LOD2:MI_Halo_334_2_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630533
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_334_LOD1"
    material: "Halo_1_LOD1:MI_Halo_334_1_HP02_LOD1;Halo_2_LOD1:MI_Halo_334_2_HP02_LOD1;Halo_1_LOD2:MI_Halo_334_1_HP02_LOD2;Halo_2_LOD2:MI_Halo_334_2_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630534
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_324_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_324_idle_001"
  }
}
rows {
  id: 630535
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_324_LOD1"
    material: "Halo_1_LOD1:MI_Halo_324_1_HP01_LOD1;Halo_2_LOD1:MI_Halo_324_2_HP01_LOD1;Halo_1_LOD2:MI_Halo_324_1_HP01_LOD2;Halo_2_LOD2:MI_Halo_324_2_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_324_idle_001_HP01"
  }
}
rows {
  id: 630536
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_324_LOD1"
    material: "Halo_1_LOD1:MI_Halo_324_1_HP02_LOD1;Halo_2_LOD1:MI_Halo_324_2_HP02_LOD1;Halo_1_LOD2:MI_Halo_324_1_HP02_LOD2;Halo_2_LOD2:MI_Halo_324_2_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_324_idle_001_HP02"
  }
}
rows {
  id: 630537
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_288_LOD1"
    modelType: 1
  }
}
rows {
  id: 630538
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_331_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_331_idle_001"
  }
}
rows {
  id: 630539
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_331_LOD1"
    material: "Halo_1_LOD1:MI_Halo_331_1_HP01_LOD1;Halo_2_LOD1:MI_Halo_331_2_HP01_LOD1;Halo_LOD2:MI_Halo_331_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_331_idle_001_HP01"
  }
}
rows {
  id: 630540
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_331_LOD1"
    material: "Halo_1_LOD1:MI_Halo_331_1_HP02_LOD1;Halo_2_LOD1:MI_Halo_331_2_HP02_LOD1;Halo_LOD2:MI_Halo_331_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_331_idle_001_HP02"
  }
}
rows {
  id: 630541
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_337_LOD1"
    modelType: 1
  }
}
rows {
  id: 630542
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_337_LOD1"
    material: "MI_Halo_337_HP01_LOD1;MI_Halo_337_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630543
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_337_LOD1"
    material: "MI_Halo_337_HP02_LOD1;MI_Halo_337_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630544
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_313_LOD1"
    modelType: 1
  }
}
rows {
  id: 630545
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_314_LOD1"
    modelType: 1
  }
}
rows {
  id: 630546
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_315_LOD1"
    modelType: 1
  }
}
rows {
  id: 630547
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_312_LOD1"
    modelType: 1
  }
}
rows {
  id: 630548
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_333"
    modelType: 1
  }
}
rows {
  id: 630549
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_316_LOD1"
    modelType: 1
  }
}
rows {
  id: 630550
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_309_LOD1"
    modelType: 1
  }
}
rows {
  id: 630551
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_309_LOD1"
    material: "MI_Halo_309_HP01_LOD1;MI_Halo_309_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630552
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_309_LOD1"
    material: "MI_Halo_309_HP02_LOD1;MI_Halo_309_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630553
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_286_LOD1"
    modelType: 1
  }
}
rows {
  id: 630554
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_286_LOD1"
    material: "MI_Halo_286_HP01_LOD1;MI_Halo_286_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630555
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_286_LOD1"
    material: "MI_Halo_286_HP02_LOD1;MI_Halo_286_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630556
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_325_LOD1"
    modelType: 1
  }
}
rows {
  id: 630557
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_325_LOD1"
    material: "MI_Halo_325_HP01_LOD1;MI_Halo_325_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630558
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_325_LOD1"
    material: "MI_Halo_325_HP02_LOD1;MI_Halo_325_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630559
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_332_LOD1"
    modelType: 1
  }
}
rows {
  id: 630560
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_332_LOD1"
    material: "MI_Halo_332_HP01_LOD1;MI_Halo_332_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630561
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_332_LOD1"
    material: "MI_Halo_332_HP02_LOD1;MI_Halo_332_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630562
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_287_LOD1"
    modelType: 1
  }
}
rows {
  id: 630563
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_287_LOD1"
    material: "MI_Halo_287_HP01_LOD1;MI_Halo_287_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630564
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_287_LOD1"
    material: "MI_Halo_287_HP02_LOD1;MI_Halo_287_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630565
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_336_LOD1"
    modelType: 1
  }
}
rows {
  id: 630566
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_330_LOD1"
    modelType: 1
  }
}
rows {
  id: 630567
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_330_LOD1"
    material: "MI_Halo_330_HP01_LOD1;MI_Halo_330_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630568
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_330_LOD1"
    material: "MI_Halo_330_HP02_LOD1;MI_Halo_330_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630569
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_353_LOD1"
    modelType: 1
  }
}
rows {
  id: 630570
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_301_LOD1"
    modelType: 1
  }
}
rows {
  id: 630571
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_301_LOD1"
    material: "MI_Halo_301_HP01_LOD1;MI_Halo_301_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630572
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_301_LOD1"
    material: "MI_Halo_301_HP02_LOD1;MI_Halo_301_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630573
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_292_LOD1"
    modelType: 1
  }
}
rows {
  id: 630574
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_292_LOD1"
    material: "MI_Halo_292_HP01_LOD1;MI_Halo_292_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630575
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_292_LOD1"
    material: "MI_Halo_292_HP02_LOD1;MI_Halo_292_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630576
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_347_LOD1"
    modelType: 1
  }
}
rows {
  id: 630577
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_347_LOD1"
    material: "MI_Halo_347_HP01_LOD1;MI_Halo_347_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630578
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_347_LOD1"
    material: "MI_Halo_347_HP02_LOD1;MI_Halo_347_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630579
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_350_LOD1"
    modelType: 1
  }
}
rows {
  id: 630580
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_342_LOD1"
    emitter: "FX_CH_Decorate_Halo_342_001"
    modelType: 1
  }
}
rows {
  id: 630581
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_342_LOD1"
    material: "Halo_1_LOD1:MI_Halo_342_1_HP01_LOD1;Halo_2_LOD1:MI_Halo_342_2_HP01_LOD1;Halo_LOD2:MI_Halo_342_HP01_LOD2"
    emitter: "FX_CH_Decorate_Halo_342_001_HP01"
    modelType: 1
  }
}
rows {
  id: 630582
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_342_LOD1"
    material: "Halo_1_LOD1:MI_Halo_342_1_HP02_LOD1;Halo_2_LOD1:MI_Halo_342_2_HP02_LOD1;Halo_LOD2:MI_Halo_342_HP02_LOD2"
    emitter: "FX_CH_Decorate_Halo_342_001_HP02"
    modelType: 1
  }
}
rows {
  id: 630583
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_343_LOD1"
    modelType: 1
  }
}
rows {
  id: 630584
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_343_LOD1"
    material: "MI_Halo_343_HP01_LOD1;MI_Halo_343_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630585
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_343_LOD1"
    material: "MI_Halo_343_HP02_LOD1;MI_Halo_343_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630586
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_357_LOD1"
    modelType: 1
  }
}
rows {
  id: 630587
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_357_LOD1"
    material: "MI_Halo_357_HP01_LOD1;MI_Halo_357_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630588
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_357_LOD1"
    material: "MI_Halo_357_HP02_LOD1;MI_Halo_357_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630589
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_345_LOD1"
    modelType: 1
  }
}
rows {
  id: 630590
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_356_LOD1"
    modelType: 1
  }
}
rows {
  id: 630591
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_354"
    modelType: 1
  }
}
rows {
  id: 630592
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_344"
    modelType: 1
  }
}
rows {
  id: 630593
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_341_LOD1"
    modelType: 1
  }
}
rows {
  id: 630594
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_321_LOD1"
    modelType: 1
  }
}
rows {
  id: 630595
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_329_LOD1"
    modelType: 1
  }
}
rows {
  id: 630596
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_365_LOD1"
    modelType: 1
  }
}
rows {
  id: 630597
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_365_LOD1"
    material: "MI_Halo_365_HP01_LOD1;MI_Halo_365_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630598
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_365_LOD1"
    material: "MI_Halo_365_HP02_LOD1;MI_Halo_365_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630599
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_362_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_362_idle_001"
  }
}
rows {
  id: 630600
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_363_LOD1"
    emitter: "FX_P_Halo_363_401;FX_P_Halo_363_402"
    modelType: 1
    idleAnim: "AS_Halo_363_idle_001"
  }
}
rows {
  id: 630601
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_364_LOD1"
    modelType: 1
  }
}
rows {
  id: 630602
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_367_LOD1"
    modelType: 1
  }
}
rows {
  id: 630603
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_368_LOD1"
    modelType: 1
  }
}
rows {
  id: 630604
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_369_LOD1"
    modelType: 1
  }
}
rows {
  id: 630605
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_372"
    modelType: 1
  }
}
rows {
  id: 630606
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_374_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_374_idle_001"
  }
}
rows {
  id: 630607
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_374_LOD1"
    material: "Halo_LOD1:MI_Halo_374_HP01_LOD1;Halo_Translucent_LOD1:MI_Halo_374_HP01_LOD1;Halo_LOD2:MI_Halo_374_HP01_LOD2;Halo_Translucent_LOD2:MI_Halo_374_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_374_idle_001_HP01"
  }
}
rows {
  id: 630608
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_374_LOD1"
    material: "Halo_LOD1:MI_Halo_374_HP02_LOD1;Halo_Translucent_LOD1:MI_Halo_374_HP02_LOD1;Halo_LOD2:MI_Halo_374_HP02_LOD2;Halo_Translucent_LOD2:MI_Halo_374_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_374_idle_001_HP02"
  }
}
rows {
  id: 630609
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_346_LOD1"
    modelType: 1
  }
}
rows {
  id: 630610
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_346_LOD1"
    material: "MI_Halo_346_HP01_LOD1;MI_Halo_346_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630611
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_346_LOD1"
    material: "MI_Halo_346_HP02_LOD1;MI_Halo_346_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630612
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_371_LOD1"
    modelType: 1
  }
}
rows {
  id: 630613
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_371_LOD1"
    material: "MI_Halo_371_HP01_LOD1;MI_Halo_371_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630614
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_371_LOD1"
    material: "MI_Halo_371_HP02_LOD1;MI_Halo_371_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630615
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_388_LOD1"
    modelType: 1
  }
}
rows {
  id: 630616
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_388_LOD1"
    material: "MI_Halo_388_HP01_LOD1;MI_Halo_388_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630617
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_388_LOD1"
    material: "MI_Halo_388_HP02_LOD1;MI_Halo_388_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630618
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_373_LOD1"
    modelType: 1
  }
}
rows {
  id: 630619
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_373_LOD1"
    material: "MI_Halo_373_HP01_LOD1;MI_Halo_373_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630620
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_373_LOD1"
    material: "MI_Halo_373_HP02_LOD1;MI_Halo_373_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630621
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_359_LOD1"
    modelType: 1
  }
}
rows {
  id: 630622
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_366_LOD1"
    modelType: 1
  }
}
rows {
  id: 630623
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_355"
    modelType: 1
  }
}
rows {
  id: 630624
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_268_LOD1"
    modelType: 1
  }
}
rows {
  id: 630625
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_360"
    modelType: 1
  }
}
rows {
  id: 630626
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_351_LOD1"
    modelType: 1
  }
}
rows {
  id: 630627
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_351_LOD1"
    material: "MI_Halo_351_HP01_LOD1;MI_Halo_351_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630628
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_351_LOD1"
    material: "MI_Halo_351_HP02_LOD1;MI_Halo_351_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630629
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_370_LOD1"
    modelType: 2
    idleAnim: "AS_Halo_370_idle_001"
  }
}
rows {
  id: 630630
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_370_LOD1"
    material: "MI_Halo_370_HP01_LOD1;MI_Halo_370_HP01_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_370_idle_001_HP01"
  }
}
rows {
  id: 630631
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SK_Halo_370_LOD1"
    material: "MI_Halo_370_HP02_LOD1;MI_Halo_370_HP02_LOD2"
    modelType: 2
    idleAnim: "AS_Halo_370_idle_001_HP02"
  }
}
rows {
  id: 630632
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_378_LOD1"
    emitter: "FX_CH_Decorate_Halo_378_001"
    modelType: 1
  }
}
rows {
  id: 630633
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_378_LOD1"
    material: "Halo_1_LOD1:MI_Halo_378_1_HP01_LOD1;Halo_2_LOD1:MI_Halo_378_2_HP01_LOD1;Halo_LOD2:MI_Halo_378_HP01_LOD2"
    emitter: "FX_CH_Decorate_Halo_378_001_HP01"
    modelType: 1
  }
}
rows {
  id: 630634
  type: ItemType_HeadWear
  quality: 1
  resourceConf {
    model: "SM_Halo_378_LOD1"
    material: "Halo_1_LOD1:MI_Halo_378_1_HP02_LOD1;Halo_2_LOD1:MI_Halo_378_2_HP02_LOD1;Halo_LOD2:MI_Halo_378_HP02_LOD2"
    emitter: "FX_CH_Decorate_Halo_378_001_HP02"
    modelType: 1
  }
}
rows {
  id: 630635
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_376_LOD1"
    modelType: 1
  }
}
rows {
  id: 630636
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_376_LOD1"
    material: "MI_Halo_376_HP01_LOD1;MI_Halo_376_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630637
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_376_LOD1"
    material: "MI_Halo_376_HP02_LOD1;MI_Halo_376_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630638
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_389_LOD1"
    modelType: 1
  }
}
rows {
  id: 630639
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_389_LOD1"
    material: "MI_Halo_389_HP01_LOD1;MI_Halo_389_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630640
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_389_LOD1"
    material: "MI_Halo_389_HP02_LOD1;MI_Halo_389_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630641
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_395_LOD1"
    modelType: 1
  }
}
rows {
  id: 630642
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_395_LOD1"
    material: "MI_Halo_395_HP01_LOD1;MI_Halo_395_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630643
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_395_LOD1"
    material: "MI_Halo_395_HP02_LOD1;MI_Halo_395_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630644
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_396_LOD1"
    modelType: 1
  }
}
rows {
  id: 630645
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_397_LOD1"
    modelType: 1
  }
}
rows {
  id: 630646
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_398_LOD1"
    modelType: 1
  }
}
rows {
  id: 630647
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_021_LOD1"
    emitter: "FX_CH_Decorate_Halo_400"
    modelType: 1
  }
}
rows {
  id: 630650
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_289_LOD1"
    modelType: 1
  }
}
rows {
  id: 630651
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_289_LOD1"
    material: "MI_Halo_289_HP01_LOD1;MI_Halo_289_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630652
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_289_LOD1"
    material: "MI_Halo_289_HP02_LOD1;MI_Halo_289_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630653
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_348_LOD1"
    modelType: 1
  }
}
rows {
  id: 630654
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_348_LOD1"
    material: "MI_Halo_348_HP01_LOD1;MI_Halo_348_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630655
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_348_LOD1"
    material: "MI_Halo_348_HP02_LOD1;MI_Halo_348_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630656
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_381_LOD1"
    modelType: 1
  }
}
rows {
  id: 630657
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_381_LOD1"
    material: "MI_Halo_381_HP01_LOD1;MI_Halo_381_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630658
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_381_LOD1"
    material: "MI_Halo_381_HP02_LOD1;MI_Halo_381_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630659
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_319_LOD1"
    modelType: 1
  }
}
rows {
  id: 630660
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_319_LOD1"
    material: "MI_Halo_319_HP01_LOD1;MI_Halo_319_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630661
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_319_LOD1"
    material: "MI_Halo_319_HP02_LOD1;MI_Halo_319_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 630662
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_380_LOD1"
    modelType: 1
  }
}
rows {
  id: 630663
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_380_LOD1"
    material: "MI_Halo_380_HP01_LOD1;MI_Halo_380_HP01_LOD2"
    modelType: 1
  }
}
rows {
  id: 630664
  type: ItemType_HeadWear
  quality: 2
  resourceConf {
    model: "SM_Halo_380_LOD1"
    material: "MI_Halo_380_HP02_LOD1;MI_Halo_380_HP02_LOD2"
    modelType: 1
  }
}
rows {
  id: 638001
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_391_LOD1"
    modelType: 1
  }
}
rows {
  id: 638002
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_392_LOD1"
    modelType: 1
  }
}
rows {
  id: 638003
  type: ItemType_HeadWear
  quality: 3
  resourceConf {
    model: "SM_Halo_393_LOD1"
    modelType: 1
  }
}

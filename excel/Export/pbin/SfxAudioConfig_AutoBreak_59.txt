com.tencent.wea.xlsRes.table_SfxAudioConfig
excel/xls/Y_音频之音效.xlsx sheet:音效
rows {
  id: 40828
  playEvent: "Play_LetsFarm_Obj_Harvester_Put"
  playEvent3p: "Play_LetsFarm_Obj_Harvester_Put"
  bank: "LetsFarm_Obj"
  stopEvent: "Stop_LetsFarm_Obj_Harvester_Put"
  stopEvent3p: "Stop_LetsFarm_Obj_Harvester_Put"
  is3d: 1
}
rows {
  id: 40829
  playEvent: "Play_LetsFarm_Obj_Harvester_Feeding"
  playEvent3p: "Play_LetsFarm_Obj_Harvester_Feeding"
  bank: "LetsFarm_Obj"
  stopEvent: "Stop_LetsFarm_Obj_Harvester_Feeding"
  stopEvent3p: "Stop_LetsFarm_Obj_Harvester_Feeding"
  is3d: 1
}
rows {
  id: 40830
  playEvent: "Play_LetsFarm_Obj_Harvester_Encourage"
  playEvent3p: "Play_LetsFarm_Obj_Harvester_Encourage"
  bank: "LetsFarm_Obj"
  stopEvent: "Stop_LetsFarm_Obj_Harvester_Encourage"
  stopEvent3p: "Stop_LetsFarm_Obj_Harvester_Encourage"
  is3d: 1
}
rows {
  id: 40831
  playEvent: "Play_LetsFarm_Obj_Harvester_Recycle"
  playEvent3p: "Play_LetsFarm_Obj_Harvester_Recycle"
  bank: "LetsFarm_Obj"
  stopEvent: "Stop_LetsFarm_Obj_Harvester_Recycle"
  stopEvent3p: "Stop_LetsFarm_Obj_Harvester_Recycle"
  is3d: 1
}
rows {
  id: 51086
  playEvent: "Play_sfx_watergun_basketball"
  playEvent3p: "Play_sfx_watergun_basketball_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 51087
  playEvent: "Play_sfx_watergun_hit_basketball"
  playEvent3p: "Play_sfx_watergun_hit_basketball_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 51088
  playEvent: "Play_sfx_watergun_bounce_basketball"
  playEvent3p: "Play_sfx_watergun_bounce_basketball_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 51089
  playEvent: "Play_sfx_watergun_bubblegun"
  playEvent3p: "Play_sfx_watergun_bubblegun_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 51090
  playEvent: "Play_sfx_watergun_hit_bubblegun"
  playEvent3p: "Play_sfx_watergun_hit_bubblegun_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 53242
  playEvent: "Play_amb_E3_treasurehunt_bird_footstep"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53243
  playEvent: "Play_amb_E3_treasurehunt_bird_jump"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53244
  playEvent: "Play_amb_E3_treasurehunt_bird_land"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53245
  playEvent: "Play_amb_E3_treasurehunt_bird_angry"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53246
  playEvent: "Play_amb_E3_treasurehunt_bird_attack"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53247
  playEvent: "Play_amb_E3_treasurehunt_vine_catch"
  bank: "E3"
  is3d: 1
  isLoop: 1
}
rows {
  id: 53248
  playEvent: "Play_amb_E3_treasurehunt_vine_putdown"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53249
  playEvent: "Play_amb_E3_treasurehunt_stonedoor_open"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53250
  playEvent: "Play_amb_E3_treasurehunt_woodendoor_open"
  bank: "E3"
  is3d: 1
}
rows {
  id: 96225
}
rows {
  id: 96226
}
rows {
  id: 96227
}
rows {
  id: 96228
}
rows {
  id: 96229
}
rows {
  id: 96230
}
rows {
  id: 96231
}
rows {
  id: 96232
}
rows {
  id: 96233
}
rows {
  id: 96234
}
rows {
  id: 96235
}
rows {
  id: 96236
}
rows {
  id: 96237
}
rows {
  id: 96238
}
rows {
  id: 96239
}

com.tencent.wea.xlsRes.table_WXGameHudMenuConfig
excel/xls/X_小游戏切片_主界面.xlsx sheet:新版hud
rows {
  id: 1001
  buttonName: "首充"
  buttonIcon: "T_Lobby_Img_RechargeNew"
  jumpId: 50001
  sortId: 9
  showArea: 6
  redDotType: 42
  showRuleFunc: "getFirstChargeShowAndDuration"
}
rows {
  id: 1002
  buttonName: "通行证"
  buttonIcon: "T_Lobby_Img_BattlePassNew"
  jumpId: 50004
  sortId: 1
  showArea: 6
  redDotType: 16
  isLockForMatch: 1
  showRuleFunc: "getBattlePassShowAndDuration"
}
rows {
  id: 1003
  buttonName: "幸运周末"
  buttonIcon: "T_Lobby_Img_LuckyStar"
  jumpId: 50011
  sortId: 7
  showArea: 6
  redDotType: 172
  isLockForMatch: 1
  showRuleFunc: "getLuckyStarShowAndDuration"
}
rows {
  id: 1004
  buttonName: "充值返利"
  buttonIcon: "T_Lobby_Img_Recharge2"
  jumpId: 50012
  sortId: 10
  showArea: 6
  redDotType: 128
  isLockForMatch: 1
  showRuleFunc: "getRechargeShowAndTime"
  cornerIconUI: "UI_WXGame_CornerRecharge"
}
rows {
  id: 1005
  buttonName: "摇人有礼"
  buttonIcon: "T_Lobby_Img_InviteFriends"
  jumpId: 50013
  sortId: 3
  showArea: 6
  redDotType: 28
  isLockForMatch: 1
  showRuleFunc: "getInvitePlayerEventShowAndTime"
}
rows {
  id: 1006
  buttonName: "限时礼包"
  buttonIcon: "T_Lobby_Img_GiftNew"
  jumpId: 50014
  sortId: 4
  showArea: 6
  redDotType: 128
  showRuleFunc: "getSceneGiftShowAndTime"
}
rows {
  id: 1007
  buttonName: "免费抽奖"
  buttonIcon: "T_Lobby_Icon_FreeDrawNew"
  jumpId: 334
  sortId: 8
  showArea: 6
  redDotType: 113
  showRuleFunc: "getFreeDrawShowAndTime"
  cornerIconUI: "UI_WXGame_CornerFreeDraw"
}
rows {
  id: 1008
  buttonName: "限时返利"
  buttonIcon: "T_Lobby_Img_Rebates"
  jumpId: 50015
  sortId: 5
  showArea: 6
  redDotType: 132
  showRuleFunc: "getMiniGameSceneGiftShowAndTime"
}
rows {
  id: 1009
  buttonName: "VA下载奖励"
  buttonIcon: "T_Lobby_Img_Excitation"
  jumpId: 50016
  sortId: 2
  showArea: 6
  redDotType: 171
  showRuleFunc: "getVADownloadShow"
}
rows {
  id: 2001
  buttonName: "多人广场"
  buttonIcon: "CDN:T_NewWXGame_Img_SquareBtn"
  jumpId: 253
  showArea: 7
}
rows {
  id: 2002
  buttonName: "星地图"
  buttonIcon: "CDN:T_NewWXGame_Img_UGCBtn"
  jumpId: 215
  showArea: 8
}
rows {
  id: 2003
  buttonName: "限时惊喜"
  buttonIcon: "T_Lobby_Img_GiftNew"
  jumpId: 50017
  sortId: 4
  showArea: 6
  redDotType: 400
  showRuleFunc: "getSurprisedShowAndTime"
}

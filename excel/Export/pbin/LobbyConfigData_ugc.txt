com.tencent.wea.xlsRes.table_LobbyConfig
excel/xls/G_广场配置.xlsx sheet:UGC广场
rows {
  mapId: 101
  name: "百日庆典"
  gameType: LMT_UGC
  ugcId: 1350844424930751986
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1710777600
    }
    endTime {
      seconds: 1712073600
    }
    chooseEnterConfig {
      tabId: 2
    }
  }
  extraConfig {
    bgmId: 1001
  }
  disable: false
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 103
  name: "巅峰盛典"
  gameType: LMT_UGC
  ugcId: 610844424934440075
  limitPlayerNum: 35
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1711776600
    }
    endTime {
      seconds: 1711900800
    }
  }
  extraConfig {
    bgmId: 1011
  }
  disable: false
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 102
  name: "熊猫乐园"
  gameType: LMT_UGC
  ugcId: 1350844424930762009
  limitPlayerNum: 20
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1711123200
    }
    endTime {
      seconds: 1712851200
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 3
    }
  }
  extraConfig {
    bgmId: 3036
    tags: "1"
  }
  disable: false
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 104
  name: "时光城堡"
  gameType: LMT_UGC
  ugcId: 1350844424930762217
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1712073600
    }
    endTime {
      seconds: 1714492800
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 4
    }
  }
  extraConfig {
    bgmId: 3009
    tags: "2"
  }
  disable: false
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 105
  name: "蔬菜精灵"
  gameType: LMT_UGC
  ugcId: 1350844424930762382
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1712851200
    }
    endTime {
      seconds: 1716480000
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 2
    }
  }
  extraConfig {
    bgmId: 3019
    tags: "3"
  }
  disable: false
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 106
  name: "小丸子"
  gameType: LMT_UGC
  ugcId: 1350844424930762542
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1716825600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5016
    tags: "4"
  }
  disable: false
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 107
  name: "腾讯游戏发布会"
  gameType: LMT_UGC
  ugcId: 620844424930336057
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 5
    }
  }
  extraConfig {
    bgmId: 1011
  }
  disable: true
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 108
  name: "Line Friends"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 6
    }
  }
  extraConfig {
    bgmId: 5008
  }
  disable: true
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 109
  name: "中轴线 - 太和殿"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 7
    }
  }
  extraConfig {
    bgmId: 5014
  }
  disable: true
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 110
  name: "阿童木"
  gameType: LMT_UGC
  ugcId: 620844424930336103
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 8
    }
  }
  extraConfig {
    bgmId: 5012
  }
  disable: true
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 111
  name: "良渚文化"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 9
    }
  }
  extraConfig {
    bgmId: 5013
  }
  disable: true
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 112
  name: "三丽鸥"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 10
    }
  }
  extraConfig {
    bgmId: 5011
  }
  disable: true
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 113
  name: "中轴线 - 钟鼓楼"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 7
    }
  }
  extraConfig {
    bgmId: 5017
  }
  disable: true
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 114
  name: "中轴线 - 社稷坛太庙"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 7
    }
  }
  extraConfig {
    bgmId: 5015
  }
  disable: true
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 115
  name: "星宝擂台争霸赛"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 12
    }
  }
  extraConfig {
    bgmId: 1002
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 116
  name: "chiikawa"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 11
    }
  }
  disable: true
  type: LT_UGC
  dsGameType: -2005
}
rows {
  mapId: 117
  name: "万圣节"
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 3046
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 118
  name: "冰雪"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 3046
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 119
  name: "小镇"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 1735574400
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 3046
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 120
  name: "国风"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5014
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 121
  name: "西湖小镇1"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5013
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 122
  name: "西湖小镇2"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5013
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 123
  name: "新春小镇"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 3036
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 124
  name: "情人节"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 3036
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 125
  name: "踏青"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 3036
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 126
  name: "樱花"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 127
  name: "愚人节"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 128
  name: "海岛跑酷"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 129
  name: "喵喵岛"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 130
  name: "造梦星学院"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 131
  name: "情侣岛"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 132
  name: "跑酷岛"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 133
  name: "儿童乐园"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 134
  name: "毕业季"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 135
  name: "倒反天罡"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 136
  name: "小红狐"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 137
  name: "过家家"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 138
  name: "观赏地图合集"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}
rows {
  mapId: 139
  name: "chiikawa2"
  gameType: LMT_UGC
  limitPlayerNum: 27
  limitExtraPlayerNum: 5
  allocConfig {
    maxRunMin: 120
  }
  enterConfig {
    beginTime {
      seconds: 1714492800
    }
    endTime {
      seconds: 4102329600
    }
    chooseEnterConfig {
      tabId: 2
      orderId: 1
    }
  }
  extraConfig {
    bgmId: 5008
    tags: "1"
  }
  disable: true
  type: LT_UGC
  dsGameType: -2500
}

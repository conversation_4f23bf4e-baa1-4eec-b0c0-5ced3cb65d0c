com.tencent.wea.xlsRes.table_PlaceableActorConfigData
excel/xls/LetsGoPlaceable/C_场景放置物_LevelPrivate.xlsx sheet:Sheet1
rows {
  typeId: 1100000000000000
  className: "BP_Wall_Cube_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/BoxBrush/BP/BP_Wall_Cube_001_A.BP_Wall_Cube_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000001
  className: "BP_Capture"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP/BP_Capture.BP_Capture_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000002
  className: "BP_MeshWater_Interact"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP/BP_MeshWater_Interact.BP_MeshWater_Interact_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000003
  className: "BP_SceneSpline"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP/BP_SceneSpline.BP_SceneSpline_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000004
  className: "BP_SpawnRef"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP/BP_SpawnRef.BP_SpawnRef_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000005
  className: "BP_BlockFactory"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000006
  className: "BP_BlockUnderWater"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000007
  className: "BP_fangkuai1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP_fangkuai1.BP_fangkuai1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000008
  className: "BP_fangkuai1_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP_fangkuai1_1.BP_fangkuai1_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000009
  className: "BP_fangkuai1_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP_fangkuai1_2.BP_fangkuai1_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000010
  className: "BP_fangkuai1_3"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000011
  className: "BP_fangkuai1_4"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000012
  className: "BP_fangkuai1_5"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000013
  className: "BP_fangkuai1_6"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP_fangkuai1_6.BP_fangkuai1_6_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000014
  className: "BP_fangkuai2"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000015
  className: "BP_fangkuai2_1"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000016
  className: "BP_fangkuai2_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP_fangkuai2_2.BP_fangkuai2_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000017
  className: "BP_fangkuai2_3"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000018
  className: "BP_fangkuai2_4"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000019
  className: "BP_fangkuai2_5"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000020
  className: "BP_fangkuai3"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000021
  className: "BP_FangKuaiDestroy"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP_FangKuaiDestroy.BP_FangKuaiDestroy_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000022
  className: "BP_FangKuaiWater"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000023
  className: "BP_kong"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000024
  className: "BP_shijianzhou"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP_shijianzhou.BP_shijianzhou_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000025
  className: "BP_SplineActor"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000026
  className: "BP_zhangai"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000027
  className: "BP_zhangai_2"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000028
  className: "BP_zhuzi"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP_zhuzi.BP_zhuzi_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000029
  className: "BP_IndicateArrowManager"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0057/BP_IndicateArrowManager.BP_IndicateArrowManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000030
  className: "BP_RandomBlock"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0057/BP_RandomBlock.BP_RandomBlock_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000031
  className: "BP_RandomBlockManager"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0057/BP_RandomBlockManager.BP_RandomBlockManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000032
  className: "BP_PA_GE_Robot_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_GE_Robot_001_A.BP_PA_GE_Robot_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3079
}
rows {
  typeId: 1100000000000033
  className: "BP_PA_GE_Safezone_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_GE_Safezone_001_A.BP_PA_GE_Safezone_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3077
}
rows {
  typeId: 1100000000000034
  className: "BP_PA_GE_BaolingTrigger_Test"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaolingTrigger_Test.BP_PA_GE_BaolingTrigger_Test_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000035
  className: "BP_PA_IT_Baoling_001_A_Test"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000036
  className: "SM_PA_GR_BaolingRingedge_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/SM_PA_GR_BaolingRingedge_001_A.SM_PA_GR_BaolingRingedge_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000037
  className: "SM_PA_GR_BaolingRingedge_002"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70002/SM_PA_GR_BaolingRingedge_002.SM_PA_GR_BaolingRingedge_002_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000038
  className: "BP_HexagonFloor_Roll"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70006/BP_HexagonFloor_Roll.BP_HexagonFloor_Roll_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000039
  className: "BP_HexagonRollFloorSpawn"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70006/BP_HexagonRollFloorSpawn.BP_HexagonRollFloorSpawn_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000040
  className: "BP_PA_DE_ArchedDoor_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_DE_ArchedDoor_001_A.BP_PA_DE_ArchedDoor_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000041
  className: "BP_PA_DE_CommenLamp_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_DE_CommenLamp_001_A.BP_PA_DE_CommenLamp_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000042
  className: "BP_PA_GE_FlipPlate_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_GE_FlipPlate_001_A.BP_PA_GE_FlipPlate_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3060
}
rows {
  typeId: 1100000000000043
  className: "BP_PA_GE_FlipPlate_002_A"
}
rows {
  typeId: 1100000000000044
  className: "BP_PA_GE_FlipPlate_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_GE_FlipPlate_003_A.BP_PA_GE_FlipPlate_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3060
}
rows {
  typeId: 1100000000000045
  className: "BP_PA_GE_FlipPlate_004_A"
}
rows {
  typeId: 1100000000000046
  className: "BP_PA_GE_ConveyorBelt_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70017/BP/BP_PA_GE_ConveyorBelt_001_A.BP_PA_GE_ConveyorBelt_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000047
  className: "BP_PA_GE_ConveyorBelt_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70017/BP/BP_PA_GE_ConveyorBelt_002_A.BP_PA_GE_ConveyorBelt_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000048
  className: "BP_PA_GE_Lilypad_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70018Lilypads/BP_PA_GE_Lilypad_002_A.BP_PA_GE_Lilypad_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3034
}
rows {
  typeId: 1100000000000049
  className: "BP_PA_DE_ButtonBorder_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70032/BP/BP_PA_DE_ButtonBorder_001_A.BP_PA_DE_ButtonBorder_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000050
  className: "BP_PA_GR_PixelgGround_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70032/BP/BP_PA_GR_PixelgGround_001_A.BP_PA_GR_PixelgGround_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000051
  className: "BP_PA_DE_85001_PlaneNumber_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_85001_PlaneNumber_001_A.BP_PA_DE_85001_PlaneNumber_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000052
  className: "BP_PA_DE_85001_PlaneNumber_001_A1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_85001_PlaneNumber_001_A1.BP_PA_DE_85001_PlaneNumber_001_A1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000053
  className: "BP_PA_DE_85001_PlaneNumber_001_A2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_85001_PlaneNumber_001_A2.BP_PA_DE_85001_PlaneNumber_001_A2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000054
  className: "BP_PA_DE_85001_PlaneNumber_001_A3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_85001_PlaneNumber_001_A3.BP_PA_DE_85001_PlaneNumber_001_A3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000055
  className: "BP_PA_DE_85001_PlaneNumber_001_A4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_85001_PlaneNumber_001_A4.BP_PA_DE_85001_PlaneNumber_001_A4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000056
  className: "BP_PA_DE_Balloon_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_Balloon_001_A.BP_PA_DE_Balloon_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000057
  className: "BP_PA_DE_Balloon_003_Color"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_Balloon_003_Color.BP_PA_DE_Balloon_003_Color_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000058
  className: "BP_PA_DE_Court_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_Court_001_A.BP_PA_DE_Court_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000059
  className: "BP_PA_DE_Flag_001_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000060
  className: "BP_PA_DE_Flag_001_B"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000061
  className: "BP_PA_DE_GoalFrame_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_GoalFrame_001_A.BP_PA_DE_GoalFrame_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000062
  className: "BP_PA_DE_GoalFrame_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_GoalFrame_001_B.BP_PA_DE_GoalFrame_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000063
  className: "BP_PA_DE_Robot_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_Robot_001_A.BP_PA_DE_Robot_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000064
  className: "BP_PA_DE_Robot_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_Robot_002_A.BP_PA_DE_Robot_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000065
  className: "BP_PA_DE_Screen_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_Screen_001_A.BP_PA_DE_Screen_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000066
  className: "BP_PA_DE_Screen_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_Screen_002_A.BP_PA_DE_Screen_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000067
  className: "BubbleCrisisMgr"
}
rows {
  typeId: 1100000000000068
  className: "BP_ColoringAddExtendComp"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/ColoringFloor/BP_ColoringAddExtendComp.BP_ColoringAddExtendComp_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000069
  className: "BP_CrazyFactoryBorn"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/CrazyFactory/BP_CrazyFactoryBorn.BP_CrazyFactoryBorn_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1100000000000070
  className: "BP_CrazyFactoryDestroyTriggerBox"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/CrazyFactory/BP_CrazyFactoryDestroyTriggerBox.BP_CrazyFactoryDestroyTriggerBox_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000071
  className: "BP_CrazyFactoryMgr"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/CrazyFactory/BP_CrazyFactoryMgr.BP_CrazyFactoryMgr_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000072
  className: "BP_CrazyFactoryProp"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/CrazyFactory/BP_CrazyFactoryProp.BP_CrazyFactoryProp_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3065
}
rows {
  typeId: 1100000000000073
  className: "BP_CanLimitBrokenGridManager"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/FakeCandy/BP_CanLimitBrokenGridManager.BP_CanLimitBrokenGridManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000074
  className: "BP_FakeDoorGridSet"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/FakeDoor/BP_FakeDoorGridSet.BP_FakeDoorGridSet_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000075
  className: "BP_BrickSpawn"
}
rows {
  typeId: 1100000000000076
  className: "BP_downcube"
}
rows {
  typeId: 1100000000000077
  className: "BP_PA_LP_Private_001_A"
}
rows {
  typeId: 1100000000000078
  className: "BP_PA_LP_Private_001_B"
}
rows {
  typeId: 1100000000000079
  className: "BP_RandomSwingCubeManager_Survive"
}
rows {
  typeId: 1100000000000080
  className: "BP_PA_M_Floor_008"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/floor/BP_PA_M_Floor_008.BP_PA_M_Floor_008_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000081
  className: "BP_PA_M_Floor_010"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/floor/BP_PA_M_Floor_010.BP_PA_M_Floor_010_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000082
  className: "BP_Football85001"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Football/BP/BP_Football85001.BP_Football85001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000083
  className: "BP_FootballAddExtendComp"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/Football/BP/BP_FootballAddExtendComp.BP_FootballAddExtendComp_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000084
  className: "BP_Footballassignment"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Football/BP/BP_Footballassignment.BP_Footballassignment_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000085
  className: "BP_FootballDoorTipos"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Football/BP/BP_FootballDoorTipos.BP_FootballDoorTipos_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000086
  className: "BP_FootballReSetPlan"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/Football/BP/BP_FootballReSetPlan.BP_FootballReSetPlan_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000087
  className: "BP_FootballReSetPlan_Plus"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Football/BP/BP_FootballReSetPlan_Plus.BP_FootballReSetPlan_Plus_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000088
  className: "BP_Goalblock"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/Football/BP/BP_Goalblock.BP_Goalblock_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000089
  className: "BP_PacmanMgr"
}
rows {
  typeId: 1100000000000090
  className: "BP_CubeBlock"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/qianglaieltaotai2/BP_CubeBlock.BP_CubeBlock_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000091
  className: "BP_qiang"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/qianglaieltaotai2/BP_qiang.BP_qiang_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000092
  className: "BP_zhuankuaiA"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/qianglaieltaotai2/BP_zhuankuaiA.BP_zhuankuaiA_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000093
  className: "BP_zhuankuaiB"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/qianglaieltaotai2/BP_zhuankuaiB.BP_zhuankuaiB_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000094
  className: "BP_zhuankuaiC"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/qianglaieltaotai2/BP_zhuankuaiC.BP_zhuankuaiC_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000095
  className: "BP_zhuankuaiD"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/qianglaieltaotai2/BP_zhuankuaiD.BP_zhuankuaiD_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000096
  className: "BP_Boxcreater"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000097
  className: "BP_RaftDestroyTrigger"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000098
  className: "RaftBoatSurvivalMgr"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000099
  className: "BP_PlaceHolder"
}
rows {
  typeId: 1100000000000100
  className: "BP_PA_GE_SPCollisionRotateBar_001_A"
}
rows {
  typeId: 1100000000000101
  className: "BP_PA_GE_BaoLingBall_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaoLingBall_001_A.BP_PA_GE_BaoLingBall_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4001
}
rows {
  typeId: 1100000000000102
  className: "BP_PA_GE_BaoLingBall_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaoLingBall_001_B.BP_PA_GE_BaoLingBall_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4001
}
rows {
  typeId: 1100000000000103
  className: "BP_PA_GE_BaoLingBall_001_C"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaoLingBall_001_C.BP_PA_GE_BaoLingBall_001_C_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4001
}
rows {
  typeId: 1100000000000104
  className: "BP_PA_GE_BaolingBrokenPlat_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaolingBrokenPlat_001_A.BP_PA_GE_BaolingBrokenPlat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000105
  className: "BP_PA_GE_BaolingPlat_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaolingPlat_001_A.BP_PA_GE_BaolingPlat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000106
  className: "BP_MoeBrokenPlatManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70006/BP_MoeBrokenPlatManager.BP_MoeBrokenPlatManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000107
  className: "BP_PA_DE_Grandstand_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_DE_Grandstand_001_A.BP_PA_DE_Grandstand_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000108
  className: "BP_PA_DE_Grandstand_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_DE_Grandstand_002_A.BP_PA_DE_Grandstand_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000109
  className: "BP_PA_DE_Grandstand_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_DE_Grandstand_003_A.BP_PA_DE_Grandstand_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000110
  className: "BP_PA_GE_FlipPlate_005_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_GE_FlipPlate_005_A.BP_PA_GE_FlipPlate_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3060
}
rows {
  typeId: 1100000000000111
  className: "BP_PA_GE_FlipPlate_006_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_GE_FlipPlate_006_A.BP_PA_GE_FlipPlate_006_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3060
}
rows {
  typeId: 1100000000000112
  className: "BP_PA_GE_FlipPlate_007_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_GE_FlipPlate_007_A.BP_PA_GE_FlipPlate_007_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3060
}
rows {
  typeId: 1100000000000113
  className: "BP_PA_GE_FlipPlate_008_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70016/BP/BP_PA_GE_FlipPlate_008_A.BP_PA_GE_FlipPlate_008_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3060
}
rows {
  typeId: 1100000000000114
  className: "BP_PA_DE_PoolAssets_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_001_A.BP_PA_DE_PoolAssets_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000115
  className: "BP_PA_DE_PoolAssets_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_002_A.BP_PA_DE_PoolAssets_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000116
  className: "BP_PA_DE_PoolAssets_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_003_A.BP_PA_DE_PoolAssets_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000117
  className: "BP_PA_DE_PoolAssets_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_004_A.BP_PA_DE_PoolAssets_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000118
  className: "BP_PA_DE_PoolAssets_005_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_005_A.BP_PA_DE_PoolAssets_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000119
  className: "BP_PA_DE_PoolAssets_006_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_006_A.BP_PA_DE_PoolAssets_006_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000120
  className: "BP_PA_DE_PoolAssets_007_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_007_A.BP_PA_DE_PoolAssets_007_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000121
  className: "BP_PA_DE_PoolAssets_008_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_008_A.BP_PA_DE_PoolAssets_008_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000122
  className: "BP_PA_DE_PoolAssets_009_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_009_A.BP_PA_DE_PoolAssets_009_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000123
  className: "BP_PA_DE_PoolAssets_010_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_010_A.BP_PA_DE_PoolAssets_010_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000124
  className: "BP_PA_DE_PoolAssets_011_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_PoolAssets_011_A.BP_PA_DE_PoolAssets_011_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000125
  className: "BP_PA_DE_Snookerball_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_Snookerball_001_A.BP_PA_DE_Snookerball_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000126
  className: "BP_PA_DE_Snookerball_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_Snookerball_002_A.BP_PA_DE_Snookerball_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000127
  className: "BP_PA_DE_Snookerball_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80007/BP/BP_PA_DE_Snookerball_003_A.BP_PA_DE_Snookerball_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000128
  className: "BP_BubbleCrisisMgr"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/BubbleCrisis/BP_BubbleCrisisMgr.BP_BubbleCrisisMgr_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000129
  className: "BP_PacmanBeanMgr"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/PacMan/BP_PacmanBeanMgr.BP_PacmanBeanMgr_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000130
  className: "BP_PA_GE_WebAutoDoor_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ColoringFloor/BP/BP_PA_GE_WebAutoDoor_001_A.BP_PA_GE_WebAutoDoor_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000131
  className: "BP_PA_GR_MemoryScreen_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70000/BP/BP_PA_GR_MemoryScreen_001_A.BP_PA_GR_MemoryScreen_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000132
  className: "BP_PA_DE_70000_Countdown_001_A1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70000/BP/BP_PA_DE_70000_Countdown_001_A1.BP_PA_DE_70000_Countdown_001_A1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000133
  className: "BP_PA_DE_70000_Countdown_001_A2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70000/BP/BP_PA_DE_70000_Countdown_001_A2.BP_PA_DE_70000_Countdown_001_A2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000134
  className: "BP_PA_GE_BulletLauncher_002_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000135
  className: "BP_PA_GE_BulletLauncher_001_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000136
  className: "BP_PA_GE_HexagonFloor_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70006/BP/BP_PA_GE_HexagonFloor_001_A.BP_PA_GE_HexagonFloor_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000137
  className: "BP_PA_GR_CircularGround_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70034/BP/BP_PA_GR_CircularGround_001_A.BP_PA_GR_CircularGround_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000138
  className: "BP_PA_DE_HexagonFloor_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70006/BP/BP_PA_DE_HexagonFloor_001_A.BP_PA_DE_HexagonFloor_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000139
  className: "BP_PA_DE_HexagonFloor_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70077/BP/BP_PA_DE_HexagonFloor_002_A.BP_PA_DE_HexagonFloor_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000140
  className: "BP_PA_DE_HexagonFloor_003_A"
}
rows {
  typeId: 1100000000000141
  className: "BP_PA_GE_HexagonFloor_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70006/BP/BP_PA_GE_HexagonFloor_001_B.BP_PA_GE_HexagonFloor_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000142
  className: "BP_PA_GE_HexagonFloor_001_C"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70006/BP/BP_PA_GE_HexagonFloor_001_C.BP_PA_GE_HexagonFloor_001_C_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000143
  className: "BP_PA_GE_ReboundRoller_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0048/BP_PA_GE_ReboundRoller_001_A.BP_PA_GE_ReboundRoller_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000144
  className: "BP_PA_DE_Cloud_007_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/85004/BP/BP_PA_DE_Cloud_007_A.BP_PA_DE_Cloud_007_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000145
  className: "BP_PA_DE_Cloud_008_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/85004/BP/BP_PA_DE_Cloud_008_A.BP_PA_DE_Cloud_008_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000146
  className: "BP_PA_GR_SlipBase_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GR_SlipBase_001_A.BP_PA_GR_SlipBase_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000147
  className: "BP_PA_GE_BengChuang_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/ColoringFloor/BP/BP_PA_GE_BengChuang_004_A.BP_PA_GE_BengChuang_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000148
  className: "BP_PA_GE_Awardring_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GE_Awardring_001_A.BP_PA_GE_Awardring_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000149
  className: "BP_PA_GE_Awardring_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GE_Awardring_002_A.BP_PA_GE_Awardring_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000150
  className: "BP_PA_GR_SlipWindmill_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GR_SlipWindmill_001_A.BP_PA_GR_SlipWindmill_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000151
  className: "BP_PA_GR_SlipWindmill_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GR_SlipWindmill_002_A.BP_PA_GR_SlipWindmill_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000152
  className: "BP_PA_SM_GR_CustomizedSlip_002_A"
}
rows {
  typeId: 1100000000000153
  className: "BP_PA_SM_GR_CustomizedSlip_003_A"
}
rows {
  typeId: 1100000000000154
  className: "BP_PA_SM_GR_CustomizedSlip_004_A"
}
rows {
  typeId: 1100000000000155
  className: "BP_PA_SM_GR_CustomizedSlip_005_A"
}
rows {
  typeId: 1100000000000156
  className: "BP_PA_SM_GR_CustomizedSlip_006_A"
}
rows {
  typeId: 1100000000000157
  className: "BP_PA_SM_GR_CustomizedSlip_007_A"
}
rows {
  typeId: 1100000000000158
  className: "BP_PA_GR_CustomizedSlip_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GR_CustomizedSlip_001_A.BP_PA_GR_CustomizedSlip_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000159
  className: "BP_PA_GR_CustomizedSlip_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GR_CustomizedSlip_002_A.BP_PA_GR_CustomizedSlip_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000160
  className: "BP_PA_GR_CustomizedSlip_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GR_CustomizedSlip_003_A.BP_PA_GR_CustomizedSlip_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000161
  className: "BP_PA_GR_CustomizedSlip_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GR_CustomizedSlip_004_A.BP_PA_GR_CustomizedSlip_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000162
  className: "BP_PA_GR_CustomizedSlip_005_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GR_CustomizedSlip_005_A.BP_PA_GR_CustomizedSlip_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000163
  className: "BP_PA_GR_CustomizedSlip_006_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80006/BP/BP_PA_GR_CustomizedSlip_006_A.BP_PA_GR_CustomizedSlip_006_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000164
  className: "BP_PA_GE_WallComing_Barrier_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80009/BP_PA_GE_WallComing_Barrier_001_A.BP_PA_GE_WallComing_Barrier_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3066
}
rows {
  typeId: 1100000000000165
  className: "BP_PA_GE_WallComing_Barrier_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0055/BP_PA_GE_WallComing_Barrier_002_A.BP_PA_GE_WallComing_Barrier_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000166
  className: "BP_PA_LG_BlockFactory_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80009/BP_PA_LG_BlockFactory_001_A.BP_PA_LG_BlockFactory_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000167
  className: "BP_PA_DE_CircularOrbit_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70018Lilypads/BP/BP_PA_DE_CircularOrbit_001_A.BP_PA_DE_CircularOrbit_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000168
  className: "BP_PA_DE_BulletLauncherFoundation_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70018Lilypads/BP/BP_PA_DE_BulletLauncherFoundation_001_A.BP_PA_DE_BulletLauncherFoundation_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000169
  className: "BP_PA_DE_BulletLauncherFoundation_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70018Lilypads/BP/BP_PA_DE_BulletLauncherFoundation_002_A.BP_PA_DE_BulletLauncherFoundation_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000170
  className: "BP_PA_DE_FootballFlag_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_FootballFlag_001_A.BP_PA_DE_FootballFlag_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000171
  className: "BP_PA_DE_FootballFlag_002_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000172
  className: "BP_PA_DE_HexAirWall_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0052/BP_PA_DE_HexAirWall_001_A.BP_PA_DE_HexAirWall_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000173
  className: "BP_PA_GR_Pneumatic_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70006/BP/BP_PA_GR_Pneumatic_001_A.BP_PA_GR_Pneumatic_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000174
  className: "BP_PA_GR_Pneumatic_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70006/BP/BP_PA_GR_Pneumatic_002_A.BP_PA_GR_Pneumatic_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000175
  className: "BP_PA_GR_Pneumatic_002_B"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70006/BP/BP_PA_GR_Pneumatic_002_B.BP_PA_GR_Pneumatic_002_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000176
  className: "BP_PA_DE_FootballFlag_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_FootballFlag_001_B.BP_PA_DE_FootballFlag_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000177
  className: "BP_PA_GE_RaftOnlySpringboard_001_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000178
  className: "BP_PA_IT_RaftOnlyBox_001_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000179
  className: "BP_PA_GE_Bumper_009_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000180
  className: "BP_PA_GE_Fans_002_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000181
  className: "BP_PA_GE_Plat_040_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000182
  className: "BP_PA_IT_BombPlus_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70022New/Bomb/BP_PA_IT_BombPlus_001_A.BP_PA_IT_BombPlus_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000183
  className: "BP_PA_IT_BombPlus_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70022New/Bomb/BP_PA_IT_BombPlus_002_A.BP_PA_IT_BombPlus_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000184
  className: "BP_PA_IT_BombPlus_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70022New/Bomb/BP_PA_IT_BombPlus_003_A.BP_PA_IT_BombPlus_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000185
  className: "BP_ClockPlat"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70038/BP/BP_ClockPlat.BP_ClockPlat_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000186
  className: "BP_PA_GE_Plat_041_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000187
  className: "BP_PA_GE_Ship_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70022New/BP_PA_GE_Ship_001_A.BP_PA_GE_Ship_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000188
  className: "BP_PA_GE_ExplodedPlat_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70022New/BP/BP_PA_GE_ExplodedPlat_001_B.BP_PA_GE_ExplodedPlat_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000189
  className: "BP_PA_GE_FloatAera_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70022New/BP/BP_PA_GE_FloatAera_001_A.BP_PA_GE_FloatAera_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3024
}
rows {
  typeId: 1100000000000190
  className: "BP_PA_GE_Plat_025_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70022New/BP/BP_PA_GE_Plat_025_B.BP_PA_GE_Plat_025_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000191
  className: "BP_PA_GE_Plat_0058"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0055_New/BP/BP_PA_GE_Plat_0058.BP_PA_GE_Plat_0058_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000192
  className: "BP_PA_GE_Bumper_0058"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0055_New/BP/BP_PA_GE_Bumper_0058.BP_PA_GE_Bumper_0058_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000193
  className: "BP_PA_GE_CircularScreen_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70038/BP/BP_PA_GE_CircularScreen_001_A.BP_PA_GE_CircularScreen_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3060
}
rows {
  typeId: 1100000000000194
  className: "BP_PA_GE_ClockPlat_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70038/BP/BP_PA_GE_ClockPlat_001_A.BP_PA_GE_ClockPlat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3060
}
rows {
  typeId: 1100000000000195
  className: "BP_PA_GE_BrokenCube_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70022New/BP/BP_PA_GE_BrokenCube_003_A.BP_PA_GE_BrokenCube_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000196
  className: "BP_PA_DE_Sailboat_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70022New/BP/BP_PA_DE_Sailboat_001_A.BP_PA_DE_Sailboat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000197
  className: "BP_PA_BioChaseAISubTrigger"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/BioChase/BP_PA_BioChaseAISubTrigger.BP_PA_BioChaseAISubTrigger_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000198
  className: "BP_PA_BioChaseRebirthArea"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/BioChase/BP_PA_BioChaseRebirthArea.BP_PA_BioChaseRebirthArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000199
  className: "BP_PA_DE_CircularOrbit_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70018Lilypads/BP/BP_PA_DE_CircularOrbit_001_B.BP_PA_DE_CircularOrbit_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000200
  className: "BP_PA_GE_CircularOrbit_001_C"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70018Lilypads/BP/BP_PA_GE_CircularOrbit_001_C.BP_PA_GE_CircularOrbit_001_C_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000201
  className: "BP_PA_GE_CircularOrbit_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70018Lilypads/BP/BP_PA_GE_CircularOrbit_002_A.BP_PA_GE_CircularOrbit_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000202
  className: "BP_ObjectGroupTriggerMove"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_ObjectGroupTriggerMove.BP_ObjectGroupTriggerMove_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000203
  className: "BP_PA_LP_ActionTriggerArea"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_LP_ActionTriggerArea.BP_PA_LP_ActionTriggerArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000204
  className: "BP_PA_GE_Robot_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_1.BP_PA_GE_Robot_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000205
  className: "BP_PA_GE_Robot_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_2.BP_PA_GE_Robot_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000206
  className: "BP_PA_GE_Robot_3"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_3.BP_PA_GE_Robot_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000207
  className: "BP_PA_GE_Robot_4"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_4.BP_PA_GE_Robot_4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000208
  className: "BP_PA_GE_Robot_5"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_5.BP_PA_GE_Robot_5_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000209
  className: "BP_PA_GE_Robot_6"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_6.BP_PA_GE_Robot_6_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000210
  className: "BP_PA_GE_Robot_7"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_7.BP_PA_GE_Robot_7_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000211
  className: "BP_PA_GE_Robot_8"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_8.BP_PA_GE_Robot_8_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000212
  className: "BP_PA_GE_Robot_built"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_built.BP_PA_GE_Robot_built_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000213
  className: "BP_PA_DE_Cloud_009_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/85004/BP/BP_PA_DE_Cloud_009_A.BP_PA_DE_Cloud_009_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000214
  className: "BP_PA_GunGameTDM_WeaponPickup"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/GunGameTDM/BP_PA_GunGameTDM_WeaponPickup.BP_PA_GunGameTDM_WeaponPickup_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000215
  className: "BP_GunGameTDMLogic"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/GunGameTDM/BP_GunGameTDMLogic.BP_GunGameTDMLogic_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000216
  className: "BP_PA_GunGameTDM_WeaponPickup_Spwaner"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/GunGameTDM/BP_PA_GunGameTDM_WeaponPickup_Spwaner.BP_PA_GunGameTDM_WeaponPickup_Spwaner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000217
  className: "BP_PA_DE_60123_IceCircular_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60123/BP/BP_PA_DE_60123_IceCircular_001_A.BP_PA_DE_60123_IceCircular_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000218
  className: "BP_PA_DE_60123_IceCircular_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60123/BP/BP_PA_DE_60123_IceCircular_002_A.BP_PA_DE_60123_IceCircular_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000219
  className: "BP_PA_DE_60123_IceCircular_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60123/BP/BP_PA_DE_60123_IceCircular_003_A.BP_PA_DE_60123_IceCircular_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000220
  className: "BP_PA_GE_BrokenCube_0058"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0055_New/BP/BP_PA_GE_BrokenCube_0058.BP_PA_GE_BrokenCube_0058_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000221
  className: "BP_PA_GE_ShengJiangTai_ToTarget"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70069/BP/BP_PA_GE_ShengJiangTai_ToTarget.BP_PA_GE_ShengJiangTai_ToTarget_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000222
  className: "BP_PA_GE_CircleHit_001_B"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_CircleHit_001_B.BP_PA_GE_CircleHit_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000223
  className: "BP_PA_GE_Robot_built2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_built2.BP_PA_GE_Robot_built2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000224
  className: "BP_PA_GE_Bumper_003_B_Child"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Bumper_003_B_Child.BP_PA_GE_Bumper_003_B_Child_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000225
  className: "BP_PA_GE_MeiHuaZhuang_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70069/BP/BP_PA_GE_MeiHuaZhuang_001_A.BP_PA_GE_MeiHuaZhuang_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4010
}
rows {
  typeId: 1100000000000226
  className: "BP_PA_GE_CrossGear_002_A_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_CrossGear_002_A_0057.BP_PA_GE_CrossGear_002_A_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3020
}
rows {
  typeId: 1100000000000227
  className: "BP_PA_GE_Plat_002_A_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_Plat_002_A_0057.BP_PA_GE_Plat_002_A_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000228
  className: "BP_PA_GE_Plat_005_A_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_Plat_005_A_0057.BP_PA_GE_Plat_005_A_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000229
  className: "BP_PA_GE_Plat_009_A_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_Plat_009_A_0057.BP_PA_GE_Plat_009_A_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000230
  className: "BP_PA_GE_Plat_010_A_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_Plat_010_A_0057.BP_PA_GE_Plat_010_A_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000231
  className: "BP_PA_GE_Plat_011_A_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_Plat_011_A_0057.BP_PA_GE_Plat_011_A_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000232
  className: "BP_PA_GE_Plat_012_A_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_Plat_012_A_0057.BP_PA_GE_Plat_012_A_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000233
  className: "BP_PA_GE_Launcher_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70066/BP_PA_GE_Launcher_001_A.BP_PA_GE_Launcher_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000234
  className: "BP_MahjongManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85013/BP/BP_MahjongManager.BP_MahjongManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000161
}
rows {
  typeId: 1100000000000235
  className: "BP_Gear_BnbFloorA_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70023/BP/BP_Gear_BnbFloorA_001_A.BP_Gear_BnbFloorA_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3082
}
rows {
  typeId: 1100000000000236
  className: "BP_Gear_BnbFloorA_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70023/BP/BP_Gear_BnbFloorA_001_B.BP_Gear_BnbFloorA_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000237
  className: "BP_Gear_BnbFloorA_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70023/BP/BP_Gear_BnbFloorA_002_A.BP_Gear_BnbFloorA_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3082
}
rows {
  typeId: 1100000000000238
  className: "BP_Gear_BnbFloorA_002_B"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70023/BP/BP_Gear_BnbFloorA_002_B.BP_Gear_BnbFloorA_002_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3082
}
rows {
  typeId: 1100000000000239
  className: "BP_PA_BnbBomb_Level_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70023/BP/BP_PA_BnbBomb_Level_1.BP_PA_BnbBomb_Level_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3081
}
rows {
  typeId: 1100000000000240
  className: "BP_InLevel_BallCar_Spawner"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_InLevel_BallCar_Spawner.BP_InLevel_BallCar_Spawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000241
  className: "BP_PA_BnbBomb_Level_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70023/BP/BP_PA_BnbBomb_Level_2.BP_PA_BnbBomb_Level_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000242
  className: "BP_BnbGameController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70023/BP/BP_BnbGameController.BP_BnbGameController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000243
  className: "BP_BnbPlayerComponent"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70023/BP/BP_BnbPlayerComponent.BP_BnbPlayerComponent_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000244
  className: "BP_BallCarIceBase"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70064/BP/BP_BallCarIceBase.BP_BallCarIceBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000245
  className: "BP_BulletLauncher_BallCarIceBase"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_BulletLauncher_BallCarIceBase.BP_BulletLauncher_BallCarIceBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3013
}
rows {
  typeId: 1100000000000246
  className: "BP_InLevel_BallCarBase"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_InLevel_BallCarBase.BP_InLevel_BallCarBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000143
}
rows {
  typeId: 1100000000000247
  className: "BP_InLevel_BallCarShellBase"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_InLevel_BallCarShellBase.BP_InLevel_BallCarShellBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000144
}
rows {
  typeId: 1100000000000248
  className: "BP_PA_GE_City_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/Legacy/BP_PA_GE_City_1.BP_PA_GE_City_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000249
  className: "BP_PA_GE_City_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/Legacy/BP_PA_GE_City_2.BP_PA_GE_City_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000250
  className: "BP_PA_GE_Robot_Arms_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Arms_1.BP_PA_GE_Robot_Arms_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000251
  className: "BP_PA_GE_Robot_Body_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Body_1.BP_PA_GE_Robot_Body_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000252
  className: "BP_PA_GE_Robot_Claw_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Claw_1.BP_PA_GE_Robot_Claw_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000253
  className: "BP_PA_GE_Robot_Foot_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Foot_1.BP_PA_GE_Robot_Foot_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000254
  className: "BP_PA_GE_Robot_Hand_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Hand_1.BP_PA_GE_Robot_Hand_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000255
  className: "BP_PA_GE_Robot_Leg_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Leg_1.BP_PA_GE_Robot_Leg_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000256
  className: "BP_PA_GE_Robot_Waist_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Waist_1.BP_PA_GE_Robot_Waist_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000257
  className: "BP_PA_GE_Mahjong_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85013/BP/BP_PA_GE_Mahjong_001_A.BP_PA_GE_Mahjong_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4003
}
rows {
  typeId: 1100000000000258
  className: "BP_PA_GE_MahjongFactory"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/85013/BP/BP_PA_GE_MahjongFactory.BP_PA_GE_MahjongFactory_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000259
  className: "BP_PA_GE_MahjongScoreArea_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85013/BP/BP_PA_GE_MahjongScoreArea_001_A.BP_PA_GE_MahjongScoreArea_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3079
}
rows {
  typeId: 1100000000000260
  className: "BP_PA_DE_FlyingSaucer_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70065/BP/BP_PA_DE_FlyingSaucer_001_A.BP_PA_DE_FlyingSaucer_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000261
  className: "BP_PA_DE_Satellite_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70065/BP/BP_PA_DE_Satellite_001_A.BP_PA_DE_Satellite_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000262
  className: "BP_PA_LP_GroupTriggerArea"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70069/BP/BP_PA_LP_GroupTriggerArea.BP_PA_LP_GroupTriggerArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000263
  className: "BP_PA_GE_Plat_009_B_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_Plat_009_B_0057.BP_PA_GE_Plat_009_B_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000264
  className: "BP_PA_GE_MemoryPlat_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70066/BP_PA_GE_MemoryPlat_002_A.BP_PA_GE_MemoryPlat_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000265
  className: "BP_PA_GE_MemoryScreen_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70066/BP_PA_GE_MemoryScreen_002_A.BP_PA_GE_MemoryScreen_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000266
  className: "BP_BallCarBoost_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_BallCarBoost_001_A.BP_BallCarBoost_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3009
}
rows {
  typeId: 1100000000000267
  className: "BP_BallCarBoostBase_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_BallCarBoostBase_002_A.BP_BallCarBoostBase_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3009
}
rows {
  typeId: 1100000000000268
  className: "BP_BallCarBrokenCube_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_BallCarBrokenCube_001_A.BP_BallCarBrokenCube_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000269
  className: "BP_BallCarIce_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_BallCarIce_001_A.BP_BallCarIce_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000270
  className: "BP_PA_GE_Robot_built1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_built1.BP_PA_GE_Robot_built1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000271
  className: "BP_PA_DE_DreamProps_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70067/BP/BP_PA_DE_DreamProps_001_A.BP_PA_DE_DreamProps_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000272
  className: "BP_PA_DE_DrawScreen_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/85003/BP/BP_PA_DE_DrawScreen_001_A.BP_PA_DE_DrawScreen_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000273
  className: "BP_PA_DE_Rocket_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70065/BP/BP_PA_DE_Rocket_001_A.BP_PA_DE_Rocket_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000274
  className: "BP_PA_GE_PenguinScoreArea_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85012/BP/BP_PA_GE_PenguinScoreArea_001_A.BP_PA_GE_PenguinScoreArea_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3089
}
rows {
  typeId: 1100000000000275
  className: "BP_PA_GE_Plat_009_D_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_Plat_009_D_0057.BP_PA_GE_Plat_009_D_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000276
  className: "BP_PA_GE_Plat_009_C_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_Plat_009_C_0057.BP_PA_GE_Plat_009_C_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000277
  className: "BP_PA_IT_CountObj_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70066/BP/BP_PA_IT_CountObj_001_A.BP_PA_IT_CountObj_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000278
  className: "BP_PA_DE_CircularPlat_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_DE_CircularPlat_003_A.BP_PA_DE_CircularPlat_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000279
  className: "BP_PA_GE_StonePlat_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70062/BP/BP_PA_GE_StonePlat_001_A.BP_PA_GE_StonePlat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000280
  className: "BP_PA_DE_MoveFence_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_DE_MoveFence_001_A.BP_PA_DE_MoveFence_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000281
  className: "BP_PA_DE_MoveFence_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_DE_MoveFence_002_A.BP_PA_DE_MoveFence_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000282
  className: "BP_PA_IT_CountObj_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70066/BP/BP_PA_IT_CountObj_002_A.BP_PA_IT_CountObj_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000283
  className: "BP_PA_IT_CountObj_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70066/BP/BP_PA_IT_CountObj_003_A.BP_PA_IT_CountObj_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000284
  className: "BP_PA_IT_CountObj_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70066/BP/BP_PA_IT_CountObj_004_A.BP_PA_IT_CountObj_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000285
  className: "BP_PA_IT_CountObj_005_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70066/BP/BP_PA_IT_CountObj_005_A.BP_PA_IT_CountObj_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000286
  className: "BP_PA_DE_Tumbler_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70060/BP/BP_PA_DE_Tumbler_001_A.BP_PA_DE_Tumbler_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000287
  className: "BP_PA_GE_MahjongPlat_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85013/BP/BP_PA_GE_MahjongPlat_001_A.BP_PA_GE_MahjongPlat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000162
}
rows {
  typeId: 1100000000000288
  className: "BP_PA_DE_ShadowDecal_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_DE_ShadowDecal_001_A.BP_PA_DE_ShadowDecal_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000289
  className: "BP_PA_IT_CountObj_Base"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70066/BP/BP_PA_IT_CountObj_Base.BP_PA_IT_CountObj_Base_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000290
  className: "BP_PA_DE_SquarePlat_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_DE_SquarePlat_001_A.BP_PA_DE_SquarePlat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000291
  className: "BP_PA_DE_CircularPlat_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_DE_CircularPlat_001_A.BP_PA_DE_CircularPlat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000146
}
rows {
  typeId: 1100000000000292
  className: "BP_PA_DE_WaterWaves_70069_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70069/BP/BP_PA_DE_WaterWaves_70069_001_A.BP_PA_DE_WaterWaves_70069_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000293
  className: "BP_PA_GE_BaoLingBall_001_A_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70075/Gear/BP_PA_GE_BaoLingBall_001_A_2.BP_PA_GE_BaoLingBall_001_A_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000294
  className: "BP_PA_GE_BaoLingBall_001_B_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70075/Gear/BP_PA_GE_BaoLingBall_001_B_2.BP_PA_GE_BaoLingBall_001_B_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000295
  className: "BP_PA_GE_BaoLingBall_001_C_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70075/Gear/BP_PA_GE_BaoLingBall_001_C_2.BP_PA_GE_BaoLingBall_001_C_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000296
  className: "BP_FootballMgr"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/Football/BP/BP_FootballMgr.BP_FootballMgr_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000297
  className: "BP_PA_DE_FloatPlat_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70067/BP/BP_PA_DE_FloatPlat_001_A.BP_PA_DE_FloatPlat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000298
  className: "BP_PA_DE_CubeCover_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70013/BP/BP_PA_DE_CubeCover_001_A.BP_PA_DE_CubeCover_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000299
  className: "BP_PA_DE_CubeCover_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70013/BP/BP_PA_DE_CubeCover_002_A.BP_PA_DE_CubeCover_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000300
  className: "BP_PA_DE_CubeCover_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70013/BP/BP_PA_DE_CubeCover_003_A.BP_PA_DE_CubeCover_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000301
  className: "BP_PA_GR_TE_Water_Warning"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70069/BP/BP_PA_GR_TE_Water_Warning.BP_PA_GR_TE_Water_Warning_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000302
  className: "BP_PA_DE_MeiHuaZhuangFake_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70069/BP/BP_PA_DE_MeiHuaZhuangFake_001_A.BP_PA_DE_MeiHuaZhuangFake_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000303
  className: "BP_PA_DE_MahjongDecal_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85013/BP/BP_PA_DE_MahjongDecal_001_A.BP_PA_DE_MahjongDecal_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3082
}
rows {
  typeId: 1100000000000304
  className: "BP_PA_GE_Plat_009_E_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PA_GE_Plat_009_E_0057.BP_PA_GE_Plat_009_E_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000305
  className: "BP_PA_DE_DecalLine_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60174/BP/BP_PA_DE_DecalLine_001_A.BP_PA_DE_DecalLine_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000306
  className: "BP_PA_DE_DecalLine_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60174/BP/BP_PA_DE_DecalLine_002_A.BP_PA_DE_DecalLine_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000307
  className: "BP_PA_DE_DecalLine_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60174/BP/BP_PA_DE_DecalLine_003_A.BP_PA_DE_DecalLine_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000308
  className: "BP_PA_DE_DecalLine_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60174/BP/BP_PA_DE_DecalLine_004_A.BP_PA_DE_DecalLine_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000309
  className: "BP_PA_DE_DreamPillar_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70067/BP/BP_PA_DE_DreamPillar_001_A.BP_PA_DE_DreamPillar_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000310
  className: "BP_PA_DE_Plants_Tree_013_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_DE_Plants_Tree_013_A.BP_PA_DE_Plants_Tree_013_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000311
  className: "BP_PA_DE_Plants_Tree_014_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_DE_Plants_Tree_014_A.BP_PA_DE_Plants_Tree_014_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000312
  className: "BP_PA_DE_Pool_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_DE_Pool_002_A.BP_PA_DE_Pool_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000313
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000314
  className: "BP_PA_DE_VendingMachine_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_DE_VendingMachine_002_A.BP_PA_DE_VendingMachine_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000315
  className: "BP_PA_DE_EyeGlasses_001"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_DE_EyeGlasses_001.BP_PA_DE_EyeGlasses_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000316
  className: "BP_PA_DE_HippoDoor_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_DE_HippoDoor_002_A.BP_PA_DE_HippoDoor_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000317
  className: "BP_PA_DE_SwimRingBig_001"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_DE_SwimRingBig_001.BP_PA_DE_SwimRingBig_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000318
  className: "BP_PA_DE_MahjongProps_001_A"
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000319
  className: "BP_PA_DE_DreamPlanet_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70067/BP/BP_PA_DE_DreamPlanet_001_A.BP_PA_DE_DreamPlanet_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000320
  className: "BP_PA_DE_DreamPlanet_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70067/BP/BP_PA_DE_DreamPlanet_002_A.BP_PA_DE_DreamPlanet_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000321
  className: "BP_PA_DE_DreamPlanet_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70067/BP/BP_PA_DE_DreamPlanet_003_A.BP_PA_DE_DreamPlanet_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000322
  className: "BP_PA_DE_MahjongProps_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85013/BP/BP_PA_DE_MahjongProps_002_A.BP_PA_DE_MahjongProps_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000323
  className: "BP_PA_DE_MahjongProps_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85013/BP/BP_PA_DE_MahjongProps_003_A.BP_PA_DE_MahjongProps_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000324
  className: "BP_DeathUFOActor"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60133/BP/BP_DeathUFOActor.BP_DeathUFOActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000325
  className: "BP_PA_GE_LightingLine_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_GE_LightingLine_001_A.BP_PA_GE_LightingLine_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000128
}
rows {
  typeId: 1100000000000326
  className: "BP_PA_GE_LightingLine_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_GE_LightingLine_002_A.BP_PA_GE_LightingLine_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000128
}
rows {
  typeId: 1100000000000327
  className: "BP_PA_GE_EdgeCloth_OutBend10M_Scale_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_GE_EdgeCloth_OutBend10M_Scale_002_A.BP_PA_GE_EdgeCloth_OutBend10M_Scale_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000328
  className: "BP_PA_GE_EdgeCloth_Bordure10M_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_GE_EdgeCloth_Bordure10M_001_A.BP_PA_GE_EdgeCloth_Bordure10M_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000329
  className: "BP_PA_GE_CubeLand_Cloth_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_GE_CubeLand_Cloth_001_B.BP_PA_GE_CubeLand_Cloth_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000330
  className: "BP_PA_GE_CubePatchLand_Cloth_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_GE_CubePatchLand_Cloth_001_B.BP_PA_GE_CubePatchLand_Cloth_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000331
  className: "BP_PA_GE_TriangleLand_Cloth_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_GE_TriangleLand_Cloth_001_B.BP_PA_GE_TriangleLand_Cloth_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000332
  className: "BP_PA_GE_AnnularLand_Cloth_004_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_GE_AnnularLand_Cloth_004_B.BP_PA_GE_AnnularLand_Cloth_004_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000333
  className: "BP_PA_GE_SquareFloor_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70006/BP/BP_PA_GE_SquareFloor_001_A.BP_PA_GE_SquareFloor_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000334
  className: "BP_PA_GE_SquareFloor_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70006/BP/BP_PA_GE_SquareFloor_001_B.BP_PA_GE_SquareFloor_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000335
  className: "BP_PA_GE_SquareFloor_001_C"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70006/BP/BP_PA_GE_SquareFloor_001_C.BP_PA_GE_SquareFloor_001_C_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000336
  className: "BP_PA_GE_BaolingAnnularLand_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/BP/BP_PA_GE_BaolingAnnularLand_001_A.BP_PA_GE_BaolingAnnularLand_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000337
  className: "BP_PA_GE_BaolingAnnularLand_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/BP/BP_PA_GE_BaolingAnnularLand_002_A.BP_PA_GE_BaolingAnnularLand_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000338
  className: "BP_PA_GE_BaolingAnnularLand_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/BP/BP_PA_GE_BaolingAnnularLand_003_A.BP_PA_GE_BaolingAnnularLand_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000339
  className: "BP_PA_GE_BaolingBottom_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/BP/BP_PA_GE_BaolingBottom_001_A.BP_PA_GE_BaolingBottom_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000340
  className: "BP_PA_GE_BaolingRingedge_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/BP/BP_PA_GE_BaolingRingedge_004_A.BP_PA_GE_BaolingRingedge_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000341
  className: "BP_PA_GE_Gravity_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70077/BP/BP_PA_GE_Gravity_004_A.BP_PA_GE_Gravity_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000342
  className: "BP_PA_GE_BaolingAnnularLand_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/BP/BP_PA_GE_BaolingAnnularLand_004_A.BP_PA_GE_BaolingAnnularLand_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000343
  className: "BP_PA_DE_StarLignting_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70067/BP/BP_PA_DE_StarLignting_001_A.BP_PA_DE_StarLignting_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000344
  className: "BP_PA_DE_DreamPlanet_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70067/BP/BP_PA_DE_DreamPlanet_004_A.BP_PA_DE_DreamPlanet_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000345
  className: "BP_GroundUVManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70037/BP/BP_GroundUVManager.BP_GroundUVManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000137
}
rows {
  typeId: 1100000000000346
  className: "BP_BumpersRobotBase"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_BumpersRobotBase.BP_BumpersRobotBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000347
  className: "BP_EmitterPlayerBase"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_EmitterPlayerBase.BP_EmitterPlayerBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000348
  className: "BP_PA_GE_Robot_Body_1_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Body_1_1.BP_PA_GE_Robot_Body_1_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000349
  className: "BP_PA_GE_Robot_Emitter_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Emitter_001_A.BP_PA_GE_Robot_Emitter_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000133
}
rows {
  typeId: 1100000000000350
  className: "BP_MaterialBase"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_MaterialBase.BP_MaterialBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000351
  className: "BP_PA_GE_Robot_Emitter_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Emitter_002_A.BP_PA_GE_Robot_Emitter_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000133
}
rows {
  typeId: 1100000000000352
  className: "BP_PA_GE_SafezoneBehindPlat_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_GE_SafezoneBehindPlat_001_A.BP_PA_GE_SafezoneBehindPlat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3059
}
rows {
  typeId: 1100000000000353
  className: "BP_PA_GE_Robot_Arms_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Arms_2.BP_PA_GE_Robot_Arms_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000354
  className: "BP_PA_IT_Ball_70068_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_IT_Ball_70068_B.BP_PA_IT_Ball_70068_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000355
  className: "BP_PA_GE_Robot_Emitter_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Emitter_003_A.BP_PA_GE_Robot_Emitter_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000133
}
rows {
  typeId: 1100000000000356
  className: "BP_PA_DE_DecorativeLamp_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_DE_DecorativeLamp_001_A.BP_PA_DE_DecorativeLamp_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000127
}
rows {
  typeId: 1100000000000357
  className: "BP_PA_GE_RobotTest_001_A_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_GE_RobotTest_001_A_1.BP_PA_GE_RobotTest_001_A_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3079
}
rows {
  typeId: 1100000000000358
  className: "BP_PA_GE_60133RobotEmitter_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_GE_60133RobotEmitter_001_A.BP_PA_GE_60133RobotEmitter_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000359
  className: "BP_PA_GE_Robot_Emitter_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Emitter_004_A.BP_PA_GE_Robot_Emitter_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000133
}
rows {
  typeId: 1100000000000360
  className: "BP_PacmanBeanMgr_Tag"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/PacMan/BP_PacmanBeanMgr_Tag.BP_PacmanBeanMgr_Tag_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000361
  className: "BP_PA_DE_Ice_mountain_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85012/BP/BP_PA_DE_Ice_mountain_001_A.BP_PA_DE_Ice_mountain_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000362
  className: "BP_PA_DE_Ice_mountain_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85012/BP/BP_PA_DE_Ice_mountain_002_A.BP_PA_DE_Ice_mountain_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000363
  className: "BP_PA_DE_Ice_mountain_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85012/BP/BP_PA_DE_Ice_mountain_003_A.BP_PA_DE_Ice_mountain_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000364
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000365
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000366
  protoName: "PlaceablesExportInfo"
}
rows {
  typeId: 1100000000000367
  className: "BP_PropSpawner_0057"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0057/BP_PropSpawner_0057.BP_PropSpawner_0057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000000368
  className: "BP_PA_DE_Emitter_70067_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70067/BP/BP_PA_DE_Emitter_70067_A.BP_PA_DE_Emitter_70067_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000133
}
rows {
  typeId: 1100000000000369
  className: "BP_PA_DE_DecorativeLamp_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_DE_DecorativeLamp_002_A.BP_PA_DE_DecorativeLamp_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000127
}
rows {
  typeId: 1100000000000370
  className: "BP_PA_GE_Robot_Emitter_002_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Emitter_002_B.BP_PA_GE_Robot_Emitter_002_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000133
}
rows {
  typeId: 1100000000000371
  className: "BP_PA_GE_Robot_Emitter_002_C"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Emitter_002_C.BP_PA_GE_Robot_Emitter_002_C_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000133
}
rows {
  typeId: 1100000000000372
  className: "BP_PA_GE_Robot_Emitter_005_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Emitter_005_A.BP_PA_GE_Robot_Emitter_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000133
}
rows {
  typeId: 1100000000000373
  className: "BP_PA_GE_Robot_Waist_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_GE_Robot_Waist_2.BP_PA_GE_Robot_Waist_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000374
  className: "BP_PA_DE_StarLignting_001_A_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70067/BP/BP_PA_DE_StarLignting_001_A_2.BP_PA_DE_StarLignting_001_A_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000375
  className: "BP_PA_DE_CircularPlat_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70068/BP/BP_PA_DE_CircularPlat_002_A.BP_PA_DE_CircularPlat_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000146
}
rows {
  typeId: 1100000000000376
  className: "BP_PA_GE_BaoLingBall_001_D"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/BP/BP_PA_GE_BaoLingBall_001_D.BP_PA_GE_BaoLingBall_001_D_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4001
}
rows {
  typeId: 1100000000000377
  className: "BP_PA_GE_BaoLingBall_001_E"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/BP/BP_PA_GE_BaoLingBall_001_E.BP_PA_GE_BaoLingBall_001_E_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4001
}
rows {
  typeId: 1100000000000378
  className: "BP_PA_GE_BaoLingBall_001_F"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70002/BP/BP_PA_GE_BaoLingBall_001_F.BP_PA_GE_BaoLingBall_001_F_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4001
}
rows {
  typeId: 1100000000000379
  className: "BP_snakeComponent"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP_snakeComponent.BP_SnakeComponent_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000380
  className: "BP_SnakeItem"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP_SnakeItem.BP_SnakeItem_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000381
  className: "BP_snakeManager"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP_snakeManager.BP_SnakeManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000382
  className: "BP_SnakeParts"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP_SnakeParts.BP_SnakeParts_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000383
  className: "BP_SpawnSnakeItem"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP_SpawnSnakeItem.BP_SpawnSnakeItem_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000384
  className: "BP_SnakeFixedCamera"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP_SnakeFixedCamera.BP_SnakeFixedCamera_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000385
  className: "BP_PA_GE_RobotTest_001_A_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60133/BP/BP_PA_GE_RobotTest_001_A_2.BP_PA_GE_RobotTest_001_A_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3079
}
rows {
  typeId: 1100000000000386
  className: "BP_SnakePartSplineMesh"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP_SnakePartSplineMesh.BP_SnakePartSplineMesh_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000387
  className: "BP_SnakeDeadZonetrack"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP_SnakeDeadZonetrack.BP_SnakeDeadZoneTrack_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000388
  className: "BP_jubaopeng"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80011/BP_jubaopeng.BP_jubaopeng_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000389
  className: "BP_Fortune"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80011/BP/BP_Fortune.BP_Fortune_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000156
}
rows {
  typeId: 1100000000000390
  className: "BP_FortuneHongBao"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_FortuneHongBao.BP_FortuneHongBao_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000157
}
rows {
  typeId: 1100000000000391
  className: "BP_FortuneYuanbao"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_FortuneYuanbao.BP_FortuneYuanbao_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000157
}
rows {
  typeId: 1100000000000392
  className: "BP_FortuneZhadan"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_FortuneZhadan.BP_FortuneZhadan_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000158
}
rows {
  typeId: 1100000000000393
  className: "BP_Pot_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80011/BP/BP_Pot_001_A.BP_Pot_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000159
}
rows {
  typeId: 1100000000000394
  className: "BP_PA_DE_HippoDoor_001_A"
  classPath: "/Game/Feature/JS/Assets/Placeables/LevelPrivate/70037/BP/BP_PA_DE_HippoDoor_001_A.BP_PA_DE_HippoDoor_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000395
  className: "BP_PA_DE_InflateWall_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70037/BP/BP_PA_DE_InflateWall_001_A.BP_PA_DE_InflateWall_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000396
  className: "BP_PA_DE_InflateWall_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70037/BP/BP_PA_DE_InflateWall_002_A.BP_PA_DE_InflateWall_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000397
  className: "BP_PA_GE_CircularPointer_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70038/BP/BP_PA_GE_CircularPointer_001_A.BP_PA_GE_CircularPointer_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000398
  className: "BP_PA_DE_StonePlatBase_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70062/BP/BP_PA_DE_StonePlatBase_001_A.BP_PA_DE_StonePlatBase_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000399
  className: "BP_PA_DE_Umbrella_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_DE_Umbrella_002_A.BP_PA_DE_Umbrella_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000400
  className: "BP_PA_DE_EjectionDrum_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70081/BP/Art/BP_PA_DE_EjectionDrum_001_A.BP_PA_DE_EjectionDrum_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000401
  className: "BP_PA_DE_EjectionDrum_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70081/BP/Art/BP_PA_DE_EjectionDrum_002_A.BP_PA_DE_EjectionDrum_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000402
  className: "BP_MoeSnakeGamePlayActor"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70081/BP/BP_MoeSnakeGamePlayActor.BP_MoeSnakeGamePlayActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000403
  className: "BP_SnakeBodyActor"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70081/BP/BP_SnakeBodyActor.BP_SnakeBodyActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000404
  className: "BP_FortuneYuanbao_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_FortuneYuanbao_2.BP_FortuneYuanbao_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000157
}
rows {
  typeId: 1100000000000405
  className: "BP_PA_DE_CoinGround_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_CoinGround_001_A.BP_PA_DE_CoinGround_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000406
  className: "BP_PA_DE_CoinGround_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_CoinGround_002_A.BP_PA_DE_CoinGround_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000407
  className: "BP_PA_DE_GoldCoin_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_GoldCoin_001_A.BP_PA_DE_GoldCoin_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000408
  className: "BP_PA_DE_GoldIngot_001_A"
  classPath: "/Game/Feature/CHASE/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_GoldIngot_001_A.BP_PA_DE_GoldIngot_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000002
}
rows {
  typeId: 1100000000000409
  className: "BP_PA_DE_GoldIngot_001_B"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_GoldIngot_001_B.BP_PA_DE_GoldIngot_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000410
  className: "BP_PA_DE_HuipaiWallBend_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_HuipaiWallBend_001_A.BP_PA_DE_HuipaiWallBend_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000411
  className: "BP_PA_DE_HuipaiWallBend_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_HuipaiWallBend_002_A.BP_PA_DE_HuipaiWallBend_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000412
  className: "BP_PA_DE_HuipaiWallBend_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_HuipaiWallBend_003_A.BP_PA_DE_HuipaiWallBend_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000413
  className: "BP_PA_DE_HuipaiWallBend_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_HuipaiWallBend_004_A.BP_PA_DE_HuipaiWallBend_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000414
  className: "BP_PA_DE_Court_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/85001/BP/BP_PA_DE_Court_002_A.BP_PA_DE_Court_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000415
  className: "BP_SnakeHeadActor"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70081/BP/BP_SnakeHeadActor.BP_SnakeHeadActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000416
  className: "BP_PA_GE_Bumper_60185_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60185/BP_PA_GE_Bumper_60185_A.BP_PA_GE_Bumper_60185_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000417
  className: "BP_PA_GE_Bumper_60185_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60185/BP_PA_GE_Bumper_60185_B.BP_PA_GE_Bumper_60185_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000418
  className: "BP_PA_GE_Bumper_60185_C"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60185/BP_PA_GE_Bumper_60185_C.BP_PA_GE_Bumper_60185_C_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000419
  className: "BP_PA_GE_Bumper_60185_D"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60185/BP_PA_GE_Bumper_60185_D.BP_PA_GE_Bumper_60185_D_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000420
  className: "BP_PA_GE_Bumper_60185_E"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60185/BP_PA_GE_Bumper_60185_E.BP_PA_GE_Bumper_60185_E_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000421
  className: "BP_PA_GE_zhufa_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70083/BP/BP_PA_GE_zhufa_001_A.BP_PA_GE_zhufa_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000422
  className: "BP_PA_GE_Warning_Decal_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70064/BP/BP_PA_GE_Warning_Decal_001_A.BP_PA_GE_Warning_Decal_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000423
  className: "BP_PA_GR_WarningEmitter_001A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70013/BP/BP_PA_GR_WarningEmitter_001A.BP_PA_GR_WarningEmitter_001A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000133
}
rows {
  typeId: 1100000000000424
  className: "BP_PA_GE_TopSideChinoiserieFloor_01_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70062/BP/BP_PA_GE_TopSideChinoiserieFloor_01_A.BP_PA_GE_TopSideChinoiserieFloor_01_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000425
  className: "BP_PA_GE_TopSideChinoiserieFloor_06_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70062/BP/BP_PA_GE_TopSideChinoiserieFloor_06_A.BP_PA_GE_TopSideChinoiserieFloor_06_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000426
  className: "BP_PA_GE_TopSideChinoiserieFloor_08_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70062/BP/BP_PA_GE_TopSideChinoiserieFloor_08_A.BP_PA_GE_TopSideChinoiserieFloor_08_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000427
  className: "BP_PA_GE_Launcher_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70066/BP/BP_PA_GE_Launcher_001_A.BP_PA_GE_Launcher_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4011
}
rows {
  typeId: 1100000000000428
  className: "BP_PA_GE_MemoryPlat_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70066/BP/BP_PA_GE_MemoryPlat_002_A.BP_PA_GE_MemoryPlat_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3035
}
rows {
  typeId: 1100000000000429
  className: "BP_PA_GE_MemoryScreen_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70066/BP/BP_PA_GE_MemoryScreen_002_A.BP_PA_GE_MemoryScreen_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3035
}
rows {
  typeId: 1100000000000430
  className: "BP_PA_GE_MeiHuaZhuangBase"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70069/BP/BP_PA_GE_MeiHuaZhuangBase.BP_PA_GE_MeiHuaZhuangBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4010
}
rows {
  typeId: 1100000000000431
  className: "BP_SnakeDeadZone"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70081/BP/BP_SnakeDeadZone.BP_SnakeDeadZone_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000153
}
rows {
  typeId: 1100000000000432
  className: "BP_SnakeDrum"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70081/BP/BP_SnakeDrum.BP_SnakeDrum_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000153
}
rows {
  typeId: 1100000000000433
  className: "BP_SnakeDrumLauncher"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70081/BP/BP_SnakeDrumLauncher.BP_SnakeDrumLauncher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000154
}
rows {
  typeId: 1100000000000434
  className: "BP_PA_DE_CoinGround_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_CoinGround_003_A.BP_PA_DE_CoinGround_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000435
  className: "BP_PA_GE_zhufa_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70083/BP/BP_PA_GE_zhufa_002_A.BP_PA_GE_zhufa_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000436
  className: "BP_PA_DE_zhufa_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70083/BP/BP_PA_DE_zhufa_001_A.BP_PA_DE_zhufa_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000437
  className: "BP_PropSpawner_BnbCount"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70023/BP/BP_PropSpawner_BnbCount.BP_PropSpawner_BnbCount_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000000438
  className: "BP_PropSpawner_BnbPower"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70023/BP/BP_PropSpawner_BnbPower.BP_PropSpawner_BnbPower_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000000439
  className: "BP_SnakeDeadZoneBase"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP/BP_SnakeDeadZoneBase.BP_SnakeDeadZoneBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000153
}
rows {
  typeId: 1100000000000440
  className: "BP_SnakeDrumBase"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP/BP_SnakeDrumBase.BP_SnakeDrumBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000153
}
rows {
  typeId: 1100000000000441
  className: "BP_SnakeDrumLauncherBase"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP/BP_SnakeDrumLauncherBase.BP_SnakeDrumLauncherBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000154
}
rows {
  typeId: 1100000000000442
  className: "BP_PA_GR_AirWall_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70013/BP/BP_PA_GR_AirWall_001_A.BP_PA_GR_AirWall_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000443
  className: "BP_PA_GR_AirWall_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70013/BP/BP_PA_GR_AirWall_002_A.BP_PA_GR_AirWall_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000444
  className: "BP_PA_GE_SPCollisionRotateBar_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0053/BP/BP_PA_GE_SPCollisionRotateBar_001_A.BP_PA_GE_SPCollisionRotateBar_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3049
}
rows {
  typeId: 1100000000000445
  className: "BP_Gear_BnbFloorA_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0053/BP/BP_Gear_BnbFloorA_003_A.BP_Gear_BnbFloorA_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 0
}
rows {
  typeId: 1100000000000446
  className: "BP_SnakeDeadZone_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70081/BP/BP_SnakeDeadZone_2.BP_SnakeDeadZone_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000153
}
rows {
  typeId: 1100000000000447
  className: "BP_PA_GE_HookGameManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_HookGameManager.BP_PA_GE_HookGameManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000448
  className: "BP_PA_DE_EjectionDrum_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP/BP_PA_DE_EjectionDrum_001_A.BP_PA_DE_EjectionDrum_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000449
  className: "BP_PA_DE_EjectionDrum_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70081/BP/BP_PA_DE_EjectionDrum_002_A.BP_PA_DE_EjectionDrum_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000450
  className: "BP_PA_DE_zhufa_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70083/BP/BP_PA_DE_zhufa_002_A.BP_PA_DE_zhufa_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000451
  className: "BP_PA_SM_DE_Youling_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_SM_DE_Youling_001_A.BP_PA_SM_DE_Youling_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000452
  className: "BP_PA_DE_GreedySnakeDecal_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80011/BP/BP_PA_DE_GreedySnakeDecal_001_A.BP_PA_DE_GreedySnakeDecal_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000453
  className: "BP_Gear_BnbFloorA_002_C"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70023/BP/BP_Gear_BnbFloorA_002_C.BP_Gear_BnbFloorA_002_C_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3082
}
rows {
  typeId: 1100000000000454
  className: "BP_PA_DE_MusicDragon_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicDragon_001_A.BP_PA_DE_MusicDragon_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000455
  className: "BP_PA_DE_MusicFrames_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFrames_001_A.BP_PA_DE_MusicFrames_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000456
  className: "BP_PA_DE_MusicFrames_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFrames_002_A.BP_PA_DE_MusicFrames_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000457
  className: "BP_PA_DE_MusicFrames_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFrames_003_A.BP_PA_DE_MusicFrames_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000458
  className: "BP_PA_DE_MusicFrames_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFrames_004_A.BP_PA_DE_MusicFrames_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000459
  className: "BP_PA_GE_HippoDoor_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70037/BP/BP_PA_GE_HippoDoor_001_A.BP_PA_GE_HippoDoor_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000460
  className: "BP_PA_DE_Youling_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_Youling_001_A.BP_PA_DE_Youling_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000461
  className: "BP_PA_DE_Claw_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70085/BP/BP_PA_DE_Claw_002_A.BP_PA_DE_Claw_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000462
  className: "BP_PA_DE_MusicScreen_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicScreen_001_A.BP_PA_DE_MusicScreen_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000463
  className: "BP_PA_DE_MusicScreen_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicScreen_002_A.BP_PA_DE_MusicScreen_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000464
  className: "BP_PA_DE_MusicScreen_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicScreen_003_A.BP_PA_DE_MusicScreen_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000465
  className: "BP_PA_DE_MusicSpeaker_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicSpeaker_001_A.BP_PA_DE_MusicSpeaker_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000466
  className: "BP_PA_DE_MusicStage_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicStage_001_A.BP_PA_DE_MusicStage_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000467
  className: "BP_PA_DE_MusicStage_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicStage_002_A.BP_PA_DE_MusicStage_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000468
  className: "BP_PA_DE_MusicStage_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicStage_003_A.BP_PA_DE_MusicStage_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000469
  className: "BP_PA_DE_MusicStage_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicStage_004_A.BP_PA_DE_MusicStage_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000470
  className: "BP_PA_DE_MusicStage_005_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicStage_005_A.BP_PA_DE_MusicStage_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000471
  className: "BP_PA_DE_MusicFloorInner_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorInner_001_A.BP_PA_DE_MusicFloorInner_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000472
  className: "BP_PA_DE_MusicFloorInner_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorInner_002_A.BP_PA_DE_MusicFloorInner_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000473
  className: "BP_PA_DE_MusicFloorInner_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorInner_003_A.BP_PA_DE_MusicFloorInner_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000474
  className: "BP_PA_DE_MusicFloorInner_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorInner_004_A.BP_PA_DE_MusicFloorInner_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000475
  className: "BP_PA_DE_MusicFloorInner_005_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorInner_005_A.BP_PA_DE_MusicFloorInner_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000476
  className: "BP_PA_DE_MusicFloorOut_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorOut_001_A.BP_PA_DE_MusicFloorOut_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000477
  className: "BP_PA_DE_MusicFloorOut_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorOut_002_A.BP_PA_DE_MusicFloorOut_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000478
  className: "BP_PA_DE_MusicFloorOut_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorOut_003_A.BP_PA_DE_MusicFloorOut_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000479
  className: "BP_PA_DE_MusicFloorOut_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorOut_004_A.BP_PA_DE_MusicFloorOut_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000480
  className: "BP_PA_DE_MusicFloorOut_005_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorOut_005_A.BP_PA_DE_MusicFloorOut_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000481
  className: "BP_PA_DE_MusicFrames_005_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFrames_005_A.BP_PA_DE_MusicFrames_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000482
  className: "BP_PA_DE_MusicFrames_006_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFrames_006_A.BP_PA_DE_MusicFrames_006_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000483
  className: "BP_PA_DE_MusicLight_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicLight_001_A.BP_PA_DE_MusicLight_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000484
  className: "BP_PA_DE_MusicLight_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicLight_002_A.BP_PA_DE_MusicLight_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000485
  className: "BP_PA_DE_MusicLight_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicLight_003_A.BP_PA_DE_MusicLight_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000486
  className: "BP_PA_DE_MusicLight_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicLight_004_A.BP_PA_DE_MusicLight_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000487
  className: "BP_PA_DE_MusicProps_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicProps_001_A.BP_PA_DE_MusicProps_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000488
  className: "BP_PA_DE_MusicProps_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicProps_002_A.BP_PA_DE_MusicProps_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000489
  className: "BP_PA_DE_MusicProps_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicProps_003_A.BP_PA_DE_MusicProps_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000490
  className: "BP_PA_DE_MusicShell_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicShell_002_A.BP_PA_DE_MusicShell_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000491
  className: "BP_PA_DE_MusicSpeaker_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicSpeaker_002_A.BP_PA_DE_MusicSpeaker_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000492
  className: "BP_PA_DE_MusicSpeaker_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicSpeaker_003_A.BP_PA_DE_MusicSpeaker_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000493
  className: "BP_PA_DE_MusicSpeaker_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicSpeaker_004_A.BP_PA_DE_MusicSpeaker_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000494
  className: "BP_PA_DE_MusicSpeaker_005_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicSpeaker_005_A.BP_PA_DE_MusicSpeaker_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000495
  className: "BP_PA_DE_MusicStage_006_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicStage_006_A.BP_PA_DE_MusicStage_006_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000496
  className: "BP_PA_DE_MusicStage_007_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicStage_007_A.BP_PA_DE_MusicStage_007_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000497
  className: "BP_RandomBlock"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0057/Legacy/BP_RandomBlock.BP_RandomBlock_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000498
  className: "BP_PA_GE_BaolingAnnularLand_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaolingAnnularLand_001_A.BP_PA_GE_BaolingAnnularLand_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000499
  className: "BP_PA_GE_BaolingAnnularLand_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaolingAnnularLand_002_A.BP_PA_GE_BaolingAnnularLand_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000500
  className: "BP_PA_GE_BaolingAnnularLand_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaolingAnnularLand_003_A.BP_PA_GE_BaolingAnnularLand_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000501
  className: "BP_PA_GE_BaolingAnnularLand_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaolingAnnularLand_004_A.BP_PA_GE_BaolingAnnularLand_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000502
  className: "BP_PA_GE_BaolingBottom_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaolingBottom_001_A.BP_PA_GE_BaolingBottom_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000503
  className: "BP_PA_GE_BaolingRingedge_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70002/Gear/BP_PA_GE_BaolingRingedge_004_A.BP_PA_GE_BaolingRingedge_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000504
  className: "BP_PA_GE_CircleLandLaser_Cloth_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70078/BP_PA_GE_CircleLandLaser_Cloth_001_B.BP_PA_GE_CircleLandLaser_Cloth_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000505
  className: "BP_PA_GE_CubeLandLaser_Cloth_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70078/BP_PA_GE_CubeLandLaser_Cloth_001_B.BP_PA_GE_CubeLandLaser_Cloth_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000506
  className: "BP_PA_GE_CubePatchLandLaser_Cloth_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70078/BP_PA_GE_CubePatchLandLaser_Cloth_001_B.BP_PA_GE_CubePatchLandLaser_Cloth_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000507
  className: "BP_BlockBase"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80009/BP_BlockBase.BP_BlockBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000508
  className: "BP_fangkuai1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80009/BP_fangkuai1.BP_fangkuai1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000472
}
rows {
  typeId: 1100000000000509
  className: "BP_PA_IT_RabbitDoll_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80012/BP_PA_IT_RabbitDoll_001_A.BP_PA_IT_RabbitDoll_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000510
  className: "BP_PA_DE_MusicFrames_007_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFrames_007_A.BP_PA_DE_MusicFrames_007_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000511
  className: "BP_PA_DE_MagicalFloatingCrystal_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalFloatingCrystal_001_A.BP_PA_DE_MagicalFloatingCrystal_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000512
  className: "BP_PA_DE_MagicalGhostBookcase_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalGhostBookcase_001_A.BP_PA_DE_MagicalGhostBookcase_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000513
  className: "BP_PA_DE_MagicalGhostBooks_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalGhostBooks_001_A.BP_PA_DE_MagicalGhostBooks_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000514
  className: "BP_PA_DE_MagicalGhostCarpet_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalGhostCarpet_001_A.BP_PA_DE_MagicalGhostCarpet_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000515
  className: "BP_PA_DE_MagicalGhostClock_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalGhostClock_001_A.BP_PA_DE_MagicalGhostClock_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000516
  className: "BP_PA_DE_MagicalGhostClock_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalGhostClock_002_A.BP_PA_DE_MagicalGhostClock_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000517
  className: "BP_PA_DE_MagicalGhostClock_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalGhostClock_003_A.BP_PA_DE_MagicalGhostClock_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000518
  className: "BP_PA_DE_MagicalGhostClock_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalGhostClock_004_A.BP_PA_DE_MagicalGhostClock_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000519
  className: "BP_PA_DE_MagicalGhostDoor_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalGhostDoor_001_A.BP_PA_DE_MagicalGhostDoor_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000520
  className: "BP_PA_DE_MagicalGhostDoor_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalGhostDoor_002_A.BP_PA_DE_MagicalGhostDoor_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000521
  className: "BP_PA_DE_MagicalGhostPillar_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalGhostPillar_001_A.BP_PA_DE_MagicalGhostPillar_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000522
  className: "BP_PA_DE_MagicalDome_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_MagicalDome_001_A.BP_PA_DE_MagicalDome_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000523
  className: "BP_PA_DE_MagicalDomeFrame_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_MagicalDomeFrame_001_A.BP_PA_DE_MagicalDomeFrame_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000524
  className: "BP_PA_DE_MagicalPlateFence_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_MagicalPlateFence_001_A.BP_PA_DE_MagicalPlateFence_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000525
  className: "BP_PA_DE_MagicalPlateFence_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_MagicalPlateFence_002_A.BP_PA_DE_MagicalPlateFence_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000526
  className: "BP_PA_DE_MagicalWheelPlateform_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_MagicalWheelPlateform_001_A.BP_PA_DE_MagicalWheelPlateform_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000527
  className: "BP_PA_DE_MusicCarpet_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicCarpet_001_A.BP_PA_DE_MusicCarpet_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000528
  className: "BP_PA_DE_MusicFloorOut_006_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicFloorOut_006_A.BP_PA_DE_MusicFloorOut_006_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000529
  className: "BP_PA_DE_MusicLogo_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicLogo_001_A.BP_PA_DE_MusicLogo_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000530
  className: "BP_PA_GE_MagicalTrunk_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70037/BP/BP_PA_GE_MagicalTrunk_001_B.BP_PA_GE_MagicalTrunk_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000531
  className: "BP_PA_GE_MagicalTrunk_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70037/BP/BP_PA_GE_MagicalTrunk_002_A.BP_PA_GE_MagicalTrunk_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000532
  className: "BP_PA_GE_MagicalWoodenCrate_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70037/BP/BP_PA_GE_MagicalWoodenCrate_001_A.BP_PA_GE_MagicalWoodenCrate_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000533
  className: "BP_PA_GE_MagicalWoodenCrate_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70037/BP/BP_PA_GE_MagicalWoodenCrate_002_A.BP_PA_GE_MagicalWoodenCrate_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000534
  className: "BP_PA_DE_MagicalDomeDial_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_MagicalDomeDial_001_A.BP_PA_DE_MagicalDomeDial_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000535
  className: "BP_PA_DE_Youling_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70084/BP/BP_PA_DE_Youling_001_B.BP_PA_DE_Youling_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000536
  className: "BP_LevelRule_Actor_80012"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80012/LevelRule/BP_LevelRule_Actor_80012.BP_LevelRule_Actor_80012_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000537
  className: "BP_LevelRule_Actor_70085"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70085/BP/BP_LevelRule_Actor_70085.BP_LevelRule_Actor_70085_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000538
  className: "BP_PA_DE_MusicScreen_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicScreen_004_A.BP_PA_DE_MusicScreen_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000539
  className: "BP_PA_DE_MusicStage_008_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicStage_008_A.BP_PA_DE_MusicStage_008_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000540
  className: "BP_PA_DE_MagicalMusicalNote_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MagicalMusicalNote_001_A.BP_PA_DE_MagicalMusicalNote_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000541
  className: "BP_PA_DE_MagicalMusicalNote_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MagicalMusicalNote_002_A.BP_PA_DE_MagicalMusicalNote_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000542
  className: "BP_PA_DE_MagicalMusicalNote_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MagicalMusicalNote_003_A.BP_PA_DE_MagicalMusicalNote_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000543
  className: "BP_PA_DE_MagicalMusicalNote_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MagicalMusicalNote_004_A.BP_PA_DE_MagicalMusicalNote_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000544
  className: "BP_PA_DE_MagicalMusicalNote_005_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MagicalMusicalNote_005_A.BP_PA_DE_MagicalMusicalNote_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000545
  className: "BP_PA_DE_MagicalMusicalNote_006_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MagicalMusicalNote_006_A.BP_PA_DE_MagicalMusicalNote_006_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000546
  className: "BP_PA_DE_MagicalMusicalNote_007_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MagicalMusicalNote_007_A.BP_PA_DE_MagicalMusicalNote_007_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000547
  className: "BP_PA_DE_MusicDoorDecoration_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicDoorDecoration_001_A.BP_PA_DE_MusicDoorDecoration_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000548
  className: "BP_PA_DE_MusicLight_005_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Music/BP/BP_PA_DE_MusicLight_005_A.BP_PA_DE_MusicLight_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000549
  className: "BP_PA_DE_MagicalDomeDial_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_MagicalDomeDial_001_A.BP_PA_DE_MagicalDomeDial_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000550
  className: "BP_LevelRule_Actor_70023"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70023/BP/BP_LevelRule_Actor_70023.BP_LevelRule_Actor_70023_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000551
  className: "BP_PA_GE_Audio_1x1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/Circle/BP_PA_GE_Audio_1x1.BP_PA_GE_Audio_1x1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000552
  className: "BP_PA_GE_Audio_2x2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/Circle/BP_PA_GE_Audio_2x2.BP_PA_GE_Audio_2x2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000553
  className: "BP_PA_GE_Audio_2x4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/Line/BP_PA_GE_Audio_2x4.BP_PA_GE_Audio_2x4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000554
  className: "BP_PA_GE_CircleHit_70096"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/BP_PA_GE_CircleHit_70096.BP_PA_GE_CircleHit_70096_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000555
  className: "BP_PA_GE_LineHit_Base"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70096/BP_PA_GE_LineHit_Base.BP_PA_GE_LineHit_Base_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000556
  className: "BP_PA_GE_MoveStage_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/BP_PA_GE_MoveStage_001_A.BP_PA_GE_MoveStage_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000557
  className: "BP_PA_GE_EqualizerBar_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70097/BP/BP_PA_GE_EqualizerBar_001_A.BP_PA_GE_EqualizerBar_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3039
}
rows {
  typeId: 1100000000000558
  className: "BP_BnbGameController_BNBChild"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70023/BP/BP_BnbGameController_BNBChild.BP_BnbGameController_BNBChild_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000559
  className: "BP_PA_GE_Audio_3x8"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/Line/BP_PA_GE_Audio_3x8.BP_PA_GE_Audio_3x8_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000560
  className: "BP_PA_GE_Audio_4x12"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/Line/BP_PA_GE_Audio_4x12.BP_PA_GE_Audio_4x12_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000561
  className: "BP_PA_GE_Audio_5x16"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/Line/BP_PA_GE_Audio_5x16.BP_PA_GE_Audio_5x16_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000562
  className: "BP_Gear_BnbFloorA_002_D"
  classPath: "/Game/Feature/CHASE/Assets/Placeables/LevelPrivate/70023/BP/BP_Gear_BnbFloorA_002_D.BP_Gear_BnbFloorA_002_D_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000006
}
rows {
  typeId: 1100000000000563
  className: "BP_Gear_BnbFloorA_002_E"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70023/BP/BP_Gear_BnbFloorA_002_E.BP_Gear_BnbFloorA_002_E_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3082
}
rows {
  typeId: 1100000000000564
  className: "BP_Gear_BnbFloorA_002_F"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70023/BP/BP_Gear_BnbFloorA_002_F.BP_Gear_BnbFloorA_002_F_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3082
}
rows {
  typeId: 1100000000000565
  className: "BP_PA_GE_Audio_1x1_Destory"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_1x1_Destory.BP_PA_GE_Audio_1x1_Destory_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000566
  className: "BP_PA_GE_Audio_2x4_OneSide"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70096/Audio/LineOneSide/BP_PA_GE_Audio_2x4_OneSide.BP_PA_GE_Audio_2x4_OneSide_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000567
  className: "BP_PA_GE_Audio_3x8_OneSide"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70096/Audio/LineOneSide/BP_PA_GE_Audio_3x8_OneSide.BP_PA_GE_Audio_3x8_OneSide_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000568
  className: "BP_PA_GE_Audio_4x12_OneSide"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70096/Audio/LineOneSide/BP_PA_GE_Audio_4x12_OneSide.BP_PA_GE_Audio_4x12_OneSide_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000569
  className: "BP_PA_GE_Audio_5x16_OneSide"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70096/Audio/LineOneSide/BP_PA_GE_Audio_5x16_OneSide.BP_PA_GE_Audio_5x16_OneSide_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000570
  className: "BP_PA_GE_Audio_5x10_OneSide_New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/NewLine/5/BP_PA_GE_Audio_5x10_OneSide_New.BP_PA_GE_Audio_5x10_OneSide_New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000571
  className: "BP_PA_GE_Audio_5x12_OneSide_New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/NewLine/5/BP_PA_GE_Audio_5x12_OneSide_New.BP_PA_GE_Audio_5x12_OneSide_New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000572
  className: "BP_PA_GE_Audio_5x14_OneSide_New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/NewLine/5/BP_PA_GE_Audio_5x14_OneSide_New.BP_PA_GE_Audio_5x14_OneSide_New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000573
  className: "BP_PA_GE_Audio_5x16_OneSide_New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/NewLine/5/BP_PA_GE_Audio_5x16_OneSide_New.BP_PA_GE_Audio_5x16_OneSide_New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000574
  className: "BP_PA_GE_Audio_6x10_OneSide_New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/NewLine/6/BP_PA_GE_Audio_6x10_OneSide_New.BP_PA_GE_Audio_6x10_OneSide_New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000575
  className: "BP_PA_GE_Audio_6x12_OneSide_New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/NewLine/6/BP_PA_GE_Audio_6x12_OneSide_New.BP_PA_GE_Audio_6x12_OneSide_New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000576
  className: "BP_PA_GE_Audio_6x14_OneSide_New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/NewLine/6/BP_PA_GE_Audio_6x14_OneSide_New.BP_PA_GE_Audio_6x14_OneSide_New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000577
  className: "BP_PA_GE_Audio_6x16_OneSide_New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/NewLine/6/BP_PA_GE_Audio_6x16_OneSide_New.BP_PA_GE_Audio_6x16_OneSide_New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000578
  className: "BP_PA_GE_LineHit_Base_ScaleY"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70096/BP_PA_GE_LineHit_Base_ScaleY.BP_PA_GE_LineHit_Base_ScaleY_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000579
  className: "BP_LevelRule_Actor_110002"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/110002/BP_LevelRule_Actor_110002.BP_LevelRule_Actor_110002_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000580
  className: "BP_PA_GE_GameHidden_60219_a"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60219/BP_PA_GE_GameHidden_60219_a.BP_PA_GE_GameHidden_60219_a_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5004
}
rows {
  typeId: 1100000000000581
  className: "BP_PA_GE_LineHit_60219_a"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60219/BP_PA_GE_LineHit_60219_a.BP_PA_GE_LineHit_60219_a_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000582
  className: "BP_PropSpawner_BnbSpeedup"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70023/BP/BP_PropSpawner_BnbSpeedup.BP_PropSpawner_BnbSpeedup_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000000583
  className: "BP_PA_GE_Audio_1x4_Destory"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_1x4_Destory.BP_PA_GE_Audio_1x4_Destory_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000584
  className: "BP_PA_GE_Audio_2x2_Destory"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_2x2_Destory.BP_PA_GE_Audio_2x2_Destory_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000585
  className: "BP_PA_GE_Audio_2x3_Destory_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_2x3_Destory_1.BP_PA_GE_Audio_2x3_Destory_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000586
  className: "BP_PA_GE_Audio_2x3_Destory_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_2x3_Destory_2.BP_PA_GE_Audio_2x3_Destory_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000587
  className: "BP_PA_GE_Audio_2x3_Destory_3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_2x3_Destory_3.BP_PA_GE_Audio_2x3_Destory_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000588
  className: "BP_PA_GE_Audio_5x8_OneSide_New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/NewLine/5/BP_PA_GE_Audio_5x8_OneSide_New.BP_PA_GE_Audio_5x8_OneSide_New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000589
  className: "BP_PA_GE_Audio_5x10_OneSide_New_Destory"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_5x10_OneSide_New_Destory.BP_PA_GE_Audio_5x10_OneSide_New_Destory_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000590
  className: "BP_PA_GE_Audio_5x12_OneSide_New_Destory"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_5x12_OneSide_New_Destory.BP_PA_GE_Audio_5x12_OneSide_New_Destory_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000591
  className: "BP_PA_GE_Audio_5x8_OneSide_New_Destory"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_5x8_OneSide_New_Destory.BP_PA_GE_Audio_5x8_OneSide_New_Destory_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000592
  className: "BP_PA_GE_Spawner_70096New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Component/BP_PA_GE_Spawner_70096New.BP_PA_GE_Spawner_70096New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000593
  className: "BP_PA_GE_SpawnPoint_70096New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Component/BP_PA_GE_SpawnPoint_70096New.BP_PA_GE_SpawnPoint_70096New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000594
  className: "BP_PA_GE_LineHit_Base_10"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/HitLine/Destory/BP_PA_GE_LineHit_Base_10.BP_PA_GE_LineHit_Base_10_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000468
}
rows {
  typeId: 1100000000000595
  className: "BP_PA_GE_LineHit_Base_12"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/HitLine/Destory/BP_PA_GE_LineHit_Base_12.BP_PA_GE_LineHit_Base_12_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000468
}
rows {
  typeId: 1100000000000596
  className: "BP_PA_GE_LineHit_Base_8"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/HitLine/Destory/BP_PA_GE_LineHit_Base_8.BP_PA_GE_LineHit_Base_8_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000468
}
rows {
  typeId: 1100000000000597
  className: "BP_PA_DE_MagicalMusicalNote_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80015/BP_PA_DE_MagicalMusicalNote_002_A.BP_PA_DE_MagicalMusicalNote_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000598
  className: "BP_PA_GE_80015_LightArea"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80015/BP_PA_GE_80015_LightArea.BP_PA_GE_80015_LightArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000599
  className: "BP_PA_GE_Audio_1x1_Destory_CircleHitBack"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_1x1_Destory_CircleHitBack.BP_PA_GE_Audio_1x1_Destory_CircleHitBack_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000600
  className: "BP_PA_GE_WavePlat_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/BP/BP_PA_GE_WavePlat_001_A.BP_PA_GE_WavePlat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000601
  className: "BP_80015_MusicBox"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80015/BP_80015_MusicBox.BP_80015_MusicBox_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000602
  className: "BP_80015_MusicBox_golden"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80015/BP_80015_MusicBox_golden.BP_80015_MusicBox_golden_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000603
  className: "BP_PA_DE_MagicalMusicalNote_golden"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80015/BP_PA_DE_MagicalMusicalNote_golden.BP_PA_DE_MagicalMusicalNote_golden_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000604
  className: "BP_PA_GE_GmaeManager_80017"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP_PA_GE_GmaeManager_80017.BP_PA_GE_GmaeManager_80017_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000605
  className: "BP_PA_IT_Ball_005_D_70094"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70094/BP_PA_IT_Ball_005_D_70094.BP_PA_IT_Ball_005_D_70094_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000606
  className: "BP_PA_GE_Audio_3x10_OneSide_New_Destory"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_3x10_OneSide_New_Destory.BP_PA_GE_Audio_3x10_OneSide_New_Destory_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000607
  className: "BP_PA_GE_Audio_3x12_OneSide_New_Destory"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_3x12_OneSide_New_Destory.BP_PA_GE_Audio_3x12_OneSide_New_Destory_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000608
  className: "BP_PA_GE_Audio_3x8_OneSide_New_Destory"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/Audio/DetoryGround/BP_PA_GE_Audio_3x8_OneSide_New_Destory.BP_PA_GE_Audio_3x8_OneSide_New_Destory_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000463
}
rows {
  typeId: 1100000000000609
  className: "BP_PA_GE_Audio_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/BP/BP_PA_GE_Audio_001_A.BP_PA_GE_Audio_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000610
  className: "BP_PA_GE_MoveStage_001_A_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/BP_PA_GE_MoveStage_001_A_1.BP_PA_GE_MoveStage_001_A_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000611
  className: "BP_PA_GE_MoveStage_001_A_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/BP_PA_GE_MoveStage_001_A_2.BP_PA_GE_MoveStage_001_A_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000612
  className: "BP_PA_DE_Musical80015RecordPlayer_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80015/BP/BP_PA_DE_Musical80015RecordPlayer_001_A.BP_PA_DE_Musical80015RecordPlayer_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000613
  className: "BP_PA_DE_Musical80015RecordPlayer_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80015/BP/BP_PA_DE_Musical80015RecordPlayer_002_A.BP_PA_DE_Musical80015RecordPlayer_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000614
  className: "BP_PA_DE_Musical80015Searchlight_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80015/BP/BP_PA_DE_Musical80015Searchlight_001_A.BP_PA_DE_Musical80015Searchlight_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000615
  className: "BP_PA_DE_Musical80015Searchlight_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80015/BP/BP_PA_DE_Musical80015Searchlight_002_A.BP_PA_DE_Musical80015Searchlight_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000616
  className: "BP_PA_DE_Musical80017Props_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017Props_001_A.BP_PA_DE_Musical80017Props_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000617
  className: "BP_PA_DE_Musical80017Props_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017Props_002_A.BP_PA_DE_Musical80017Props_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000618
  className: "BP_PA_DE_Musical80017Youling_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017Youling_001_A.BP_PA_DE_Musical80017Youling_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000619
  className: "BP_PA_GE_DestoryAera"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/BP_PA_GE_DestoryAera.BP_PA_GE_DestoryAera_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000620
  className: "BP_PA_DE_Musical80017CoverA_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017CoverA_001_A.BP_PA_DE_Musical80017CoverA_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000621
  className: "BP_PA_DE_Musical80017CoverA_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017CoverA_002_A.BP_PA_DE_Musical80017CoverA_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000622
  className: "BP_PA_DE_Musical80017CoverA_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017CoverA_003_A.BP_PA_DE_Musical80017CoverA_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000623
  className: "BP_PA_DE_Musical80017CoverA_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017CoverA_004_A.BP_PA_DE_Musical80017CoverA_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000624
  className: "BP_PA_DE_Musical80017CoverA_005_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017CoverA_005_A.BP_PA_DE_Musical80017CoverA_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000625
  className: "BP_PA_DE_Musical80017CoverB_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017CoverB_001_A.BP_PA_DE_Musical80017CoverB_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000626
  className: "BP_PA_DE_Musical80017CoverB_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017CoverB_002_A.BP_PA_DE_Musical80017CoverB_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000627
  className: "BP_PA_DE_Musical80017CoverB_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017CoverB_003_A.BP_PA_DE_Musical80017CoverB_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000628
  className: "BP_PA_DE_Musical80017CoverB_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP/BP_PA_DE_Musical80017CoverB_004_A.BP_PA_DE_Musical80017CoverB_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000629
  className: "BP_PA_DE_Musical70096Props_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/BP/BP_PA_DE_Musical70096Props_001_A.BP_PA_DE_Musical70096Props_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000630
  className: "BP_PA_DE_Musical80015Plat_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80015/BP/BP_PA_DE_Musical80015Plat_001_A.BP_PA_DE_Musical80015Plat_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000631
  className: "BP_PA_DE_Musical80015Plat_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80015/BP/BP_PA_DE_Musical80015Plat_002_A.BP_PA_DE_Musical80015Plat_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000632
  className: "BP_80015_MusicBox_golden_middle"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80015/BP_80015_MusicBox_golden_middle.BP_80015_MusicBox_golden_middle_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000633
  className: "BP_80015_MusicBox_middle"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80015/BP_80015_MusicBox_middle.BP_80015_MusicBox_middle_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000634
  className: "BP_SetMotionUseOldArithmetic"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70068/BP/BP_SetMotionUseOldArithmetic.BP_SetMotionUseOldArithmetic_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000635
  className: "BP_PA_GE_MusicalProps_005_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80017/BP_PA_GE_MusicalProps_005_A.BP_PA_GE_MusicalProps_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000636
  className: "BP_PA_GE_CircleHit_60219_a"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60219/BP_PA_GE_CircleHit_60219_a.BP_PA_GE_CircleHit_60219_a_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000031
}
rows {
  typeId: 1100000000000637
  className: "BP_PA_GE_CircleHit_60219_b"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60219/BP_PA_GE_CircleHit_60219_b.BP_PA_GE_CircleHit_60219_b_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000031
}
rows {
  typeId: 1100000000000638
  className: "BP_PA_GE_BulletLauncher_001_70094"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70094/BP_PA_GE_BulletLauncher_001_70094.BP_PA_GE_BulletLauncher_001_70094_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3013
}
rows {
  typeId: 1100000000000639
  className: "BP_PA_GE_CircleHit_60219_a2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/BP_PA_GE_CircleHit_60219_a2.BP_PA_GE_CircleHit_60219_a2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000031
}
rows {
  typeId: 1100000000000640
  className: "BP_PA_GE_CircleHit_60219_a3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/BP_PA_GE_CircleHit_60219_a3.BP_PA_GE_CircleHit_60219_a3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000031
}
rows {
  typeId: 1100000000000641
  className: "BP_PA_GE_CircleHit_60219_b2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/BP_PA_GE_CircleHit_60219_b2.BP_PA_GE_CircleHit_60219_b2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000031
}
rows {
  typeId: 1100000000000642
  className: "BP_PA_GE_CircleHit_60219_b3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/BP_PA_GE_CircleHit_60219_b3.BP_PA_GE_CircleHit_60219_b3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000031
}
rows {
  typeId: 1100000000000643
  className: "BP_PA_GE_CircleHit_60219_b4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/BP_PA_GE_CircleHit_60219_b4.BP_PA_GE_CircleHit_60219_b4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000031
}
rows {
  typeId: 1100000000000644
  className: "BP_PA_GE_MoveStage_001_A_3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70096/BP_PA_GE_MoveStage_001_A_3.BP_PA_GE_MoveStage_001_A_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000645
  className: "BP_PA_DE_Musical80015Plat_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80015/BP/BP_PA_DE_Musical80015Plat_003_A.BP_PA_DE_Musical80015Plat_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000646
  className: "BP_PA_DE_Musical80015RecordPlayer_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80015/BP/BP_PA_DE_Musical80015RecordPlayer_003_A.BP_PA_DE_Musical80015RecordPlayer_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000647
  className: "BP_PA_DE_ConferenceDragon_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceDragon_001_A.BP_PA_DE_ConferenceDragon_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000648
  className: "BP_PA_DE_ConferenceFighter_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceFighter_001_A.BP_PA_DE_ConferenceFighter_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000649
  className: "BP_PA_DE_ConferenceGingerBrave_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceGingerBrave_001_A.BP_PA_DE_ConferenceGingerBrave_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000650
  className: "BP_PA_DE_ConferenceGoose_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceGoose_001_A.BP_PA_DE_ConferenceGoose_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000651
  className: "BP_PA_DE_ConferenceMichele_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceMichele_001_A.BP_PA_DE_ConferenceMichele_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000652
  className: "BP_PA_DE_ConferenceMogu_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceMogu_001_A.BP_PA_DE_ConferenceMogu_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000653
  className: "BP_PA_DE_ConferenceYouyou_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceYouyou_001_A.BP_PA_DE_ConferenceYouyou_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000654
  className: "BP_Gear_PaopaoBnbFloorA_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70107/BP/BP_Gear_PaopaoBnbFloorA_001_A.BP_Gear_PaopaoBnbFloorA_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000309
}
rows {
  typeId: 1100000000000655
  className: "BP_Gear_PaopaoBnbFloorA_001_B"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70107/BP/BP_Gear_PaopaoBnbFloorA_001_B.BP_Gear_PaopaoBnbFloorA_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000309
}
rows {
  typeId: 1100000000000656
  className: "BP_PA_DE_ConferenceLogo_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceLogo_001_A.BP_PA_DE_ConferenceLogo_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000657
  className: "BP_PA_DE_ConferenceLogo_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceLogo_002_A.BP_PA_DE_ConferenceLogo_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000658
  className: "BP_PA_DE_ConferenceLogo_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceLogo_003_A.BP_PA_DE_ConferenceLogo_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000659
  className: "BP_PA_DE_ConferenceLogo_004_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceLogo_004_A.BP_PA_DE_ConferenceLogo_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000660
  className: "BP_PA_DE_ConferenceLogo_005_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceLogo_005_A.BP_PA_DE_ConferenceLogo_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000661
  className: "BP_PA_DE_ConferenceLogo_006_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceLogo_006_A.BP_PA_DE_ConferenceLogo_006_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000662
  className: "BP_PA_DE_ConferenceLogo_007_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceLogo_007_A.BP_PA_DE_ConferenceLogo_007_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000663
  className: "BP_PA_DE_ConferenceLogo_008_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceLogo_008_A.BP_PA_DE_ConferenceLogo_008_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000664
  className: "BP_PA_DE_ConferenceLogo_009_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceLogo_009_A.BP_PA_DE_ConferenceLogo_009_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000665
  className: "BP_PA_DE_ConferenceScreen_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceScreen_001_A.BP_PA_DE_ConferenceScreen_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000112
}
rows {
  typeId: 1100000000000666
  className: "BP_PA_DE_ConferenceScreen_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceScreen_002_A.BP_PA_DE_ConferenceScreen_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000667
  className: "BP_PA_DE_ConferenceScreen_003_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceScreen_003_A.BP_PA_DE_ConferenceScreen_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000668
  className: "BP_Gear_PaopaoBnbLandA_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70107/BP/BP_Gear_PaopaoBnbLandA_001_A.BP_Gear_PaopaoBnbLandA_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000309
}
rows {
  typeId: 1100000000000669
  className: "BP_PA_DE_ValentineDayGrass_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70107/BP/BP_PA_DE_ValentineDayGrass_001_A.BP_PA_DE_ValentineDayGrass_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000670
  className: "BP_PA_DE_ValentineDayHeart_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70107/BP/BP_PA_DE_ValentineDayHeart_001_A.BP_PA_DE_ValentineDayHeart_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000671
  className: "BP_PA_DE_ValentineDayHeart_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70107/BP/BP_PA_DE_ValentineDayHeart_002_A.BP_PA_DE_ValentineDayHeart_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000672
  className: "BP_PA_DE_ConferenceArch_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceArch_001_A.BP_PA_DE_ConferenceArch_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000673
  className: "BP_PA_DE_ConferenceProps_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceProps_001_A.BP_PA_DE_ConferenceProps_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000674
  className: "BP_PA_DE_ConferenceSofa_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceSofa_001_A.BP_PA_DE_ConferenceSofa_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000675
  className: "BP_PA_DE_ConferenceSofa_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceSofa_002_A.BP_PA_DE_ConferenceSofa_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000676
  className: "BP_PA_DE_ConferenceStage_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceStage_001_A.BP_PA_DE_ConferenceStage_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000677
  className: "BP_PA_DE_ConferenceStage_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceStage_002_A.BP_PA_DE_ConferenceStage_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000678
  className: "BP_PA_DE_ConferenceLamborgini_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceLamborgini_001_A.BP_PA_DE_ConferenceLamborgini_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000679
  className: "BP_TriggerAlwaysForceUpdate"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/0055_New/BP/BP_TriggerAlwaysForceUpdate.BP_TriggerAlwaysForceUpdate_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000680
  className: "BP_PA_DE_80048_Fish_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80018/BP/BP_PA_DE_80048_Fish_001_A.BP_PA_DE_80048_Fish_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000681
  className: "BP_PA_DE_80048_Fish_001_B"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80018/BP/BP_PA_DE_80048_Fish_001_B.BP_PA_DE_80048_Fish_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000682
  className: "BP_PA_DE_80018_Fish_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80018/BP/BP_PA_DE_80018_Fish_001_A.BP_PA_DE_80018_Fish_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000683
  className: "BP_PA_DE_80018_Fish_001_B"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80018/BP/BP_PA_DE_80018_Fish_001_B.BP_PA_DE_80018_Fish_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000684
  className: "BP_PA_DE_80018_Waste_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80018/BP/BP_PA_DE_80018_Waste_001_A.BP_PA_DE_80018_Waste_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000685
  className: "BP_PA_DE_80018_Waste_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80018/BP/BP_PA_DE_80018_Waste_002_A.BP_PA_DE_80018_Waste_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000686
  className: "BP_LevelRule_Actor_70022"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70022New/BP/BP_LevelRule_Actor_70022.BP_LevelRule_Actor_70022_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000687
  className: "BP_PA_DE_ConferenceFloor_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/Conference/BP/BP_PA_DE_ConferenceFloor_001_A.BP_PA_DE_ConferenceFloor_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000688
  className: "BP_PA_GE_Bumper_60228_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60228/BP_PA_GE_Bumper_60228_001_A.BP_PA_GE_Bumper_60228_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000689
  className: "BP_PA_DE_70086_ArchedDoor_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_70086_ArchedDoor_001_A.BP_PA_DE_70086_ArchedDoor_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000690
  className: "BP_PA_DE_70086_ArchedDoorB_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_70086_ArchedDoorB_001_A.BP_PA_DE_70086_ArchedDoorB_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000691
  className: "BP_PA_DE_70086_ArchedDoorB_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_70086_ArchedDoorB_002_A.BP_PA_DE_70086_ArchedDoorB_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000692
  className: "BP_PA_DE_70086_ArchedDoorB_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_70086_ArchedDoorB_003_A.BP_PA_DE_70086_ArchedDoorB_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000693
  className: "BP_PA_DE_70086_ArchedDoorB_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_70086_ArchedDoorB_004_A.BP_PA_DE_70086_ArchedDoorB_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000694
  className: "BP_PA_DE_70086_Seaweed_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_70086_Seaweed_001_A.BP_PA_DE_70086_Seaweed_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000695
  className: "BP_PA_DE_70086_Seaweed_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_70086_Seaweed_002_A.BP_PA_DE_70086_Seaweed_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000696
  className: "BP_PA_DE_70086_Seaweed_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_70086_Seaweed_003_A.BP_PA_DE_70086_Seaweed_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000697
  className: "BP_PA_DE_70086_WheelPlateform_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_DE_70086_WheelPlateform_001_A.BP_PA_DE_70086_WheelPlateform_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000698
  className: "BP_PA_GE_Spawner_70086New_Level1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_Spawner_70086New_Level1.BP_PA_GE_Spawner_70086New_Level1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000699
  className: "BP_PA_GE_Spawner_70086New_Level2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_Spawner_70086New_Level2.BP_PA_GE_Spawner_70086New_Level2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000700
  className: "BP_PA_GE_Spawner_70086New_Level3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_Spawner_70086New_Level3.BP_PA_GE_Spawner_70086New_Level3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000701
  className: "BP_PA_GE_EqualizerCube_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70097/BP/BP_PA_GE_EqualizerCube_001_A.BP_PA_GE_EqualizerCube_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000702
  className: "BP_PA_GE_EqualizerScreen_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70097/BP/BP_PA_GE_EqualizerScreen_001_A.BP_PA_GE_EqualizerScreen_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000703
  className: "BP_PA_DE_SeaMonsterOctopus_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70103/BP/BP_PA_DE_SeaMonsterOctopus_001_A.BP_PA_DE_SeaMonsterOctopus_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000704
  className: "BP_PA_DE_SeaMonsterOctopus_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70103/BP/BP_PA_DE_SeaMonsterOctopus_002_A.BP_PA_DE_SeaMonsterOctopus_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000705
  className: "BP_PA_DE_SeaMonsterShip_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70103/BP/BP_PA_DE_SeaMonsterShip_001_A.BP_PA_DE_SeaMonsterShip_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000706
  className: "BP_PA_DE_SeaMonsterShip_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70103/BP/BP_PA_DE_SeaMonsterShip_002_A.BP_PA_DE_SeaMonsterShip_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000707
  className: "BP_PA_DE_SeaMonsterShip_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70103/BP/BP_PA_DE_SeaMonsterShip_003_A.BP_PA_DE_SeaMonsterShip_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000708
  className: "BP_PA_GE_Plat_025_70103_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70103/BP_PA_GE_Plat_025_70103_B.BP_PA_GE_Plat_025_70103_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000709
  className: "BP_PA_DE_70104_SummerHouse_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_DE_70104_SummerHouse_001_A.BP_PA_DE_70104_SummerHouse_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000710
  className: "BP_PA_GE_70104_A_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_A_1.BP_PA_GE_70104_A_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000711
  className: "BP_PA_GE_70104_A_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_A_2.BP_PA_GE_70104_A_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000712
  className: "BP_PA_GE_70104_A_3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_A_3.BP_PA_GE_70104_A_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000713
  className: "BP_PA_GE_70104_A_4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_A_4.BP_PA_GE_70104_A_4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000714
  className: "BP_PA_GE_70104_A_5"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_A_5.BP_PA_GE_70104_A_5_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000715
  className: "BP_PA_GE_70104_A_6"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_A_6.BP_PA_GE_70104_A_6_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000716
  className: "BP_PA_GE_70104_B_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_B_1.BP_PA_GE_70104_B_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000717
  className: "BP_PA_GE_70104_B_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_B_2.BP_PA_GE_70104_B_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000718
  className: "BP_PA_GE_70104_B_3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_B_3.BP_PA_GE_70104_B_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000719
  className: "BP_PA_GE_70104_C_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_C_1.BP_PA_GE_70104_C_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000720
  className: "BP_PA_GE_70104_C_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_C_2.BP_PA_GE_70104_C_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000721
  className: "BP_PA_GE_70104_D_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_D_1.BP_PA_GE_70104_D_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3024
}
rows {
  typeId: 1100000000000722
  className: "BP_PA_GE_70104_E_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_E_1.BP_PA_GE_70104_E_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000723
  className: "BP_PA_GE_70104_Show_E"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104_Show_E.BP_PA_GE_70104_Show_E_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000724
  className: "BP_PA_GE_70104GameManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_70104GameManager.BP_PA_GE_70104GameManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000725
  className: "BP_PA_GE_Spawner_70104"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_Spawner_70104.BP_PA_GE_Spawner_70104_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000726
  className: "BP_PA_GE_SpawnPoint_70104"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PA_GE_SpawnPoint_70104.BP_PA_GE_SpawnPoint_70104_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000727
  className: "BP_PropSpawner_70104"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70104/BP/BP_PropSpawner_70104.BP_PropSpawner_70104_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000000728
  className: "BP_PA_GE_CircleHit_70105"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_CircleHit_70105.BP_PA_GE_CircleHit_70105_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000729
  className: "BP_PA_GE_GameManager_70105"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_GameManager_70105.BP_PA_GE_GameManager_70105_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000730
  className: "BP_PA_GE_Ink_70105_001"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Ink_70105_001.BP_PA_GE_Ink_70105_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000731
  className: "BP_PA_GE_InkBomb_70105"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_InkBomb_70105.BP_PA_GE_InkBomb_70105_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000732
  className: "BP_PA_GE_Lilypad_70105_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Lilypad_70105_001_A.BP_PA_GE_Lilypad_70105_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3034
}
rows {
  typeId: 1100000000000733
  className: "BP_PA_GE_Plat_025_70105_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Plat_025_70105_B.BP_PA_GE_Plat_025_70105_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000734
  className: "BP_PA_GE_Plat_025_70105_C"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Plat_025_70105_C.BP_PA_GE_Plat_025_70105_C_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000735
  className: "BP_PA_GE_Plat_025_70105_D"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Plat_025_70105_D.BP_PA_GE_Plat_025_70105_D_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000000736
  className: "BP_PA_GE_RocketBulletLauncher_70105_001"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_RocketBulletLauncher_70105_001.BP_PA_GE_RocketBulletLauncher_70105_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000737
  className: "BP_PA_GE_Tentacle_70105_001"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_001.BP_PA_GE_Tentacle_70105_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000738
  className: "BP_PA_GE_Tentacle_70105_002"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_002.BP_PA_GE_Tentacle_70105_002_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000739
  className: "BP_PA_GE_Tentacle_70105_003"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_003.BP_PA_GE_Tentacle_70105_003_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000740
  className: "BP_PA_GE_Tentacle_70105_004"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_004.BP_PA_GE_Tentacle_70105_004_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000741
  className: "BP_PA_GE_Tentacle_70105_005"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_005.BP_PA_GE_Tentacle_70105_005_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000742
  className: "BP_PA_GE_Tentacle_70105_006"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_006.BP_PA_GE_Tentacle_70105_006_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000743
  className: "BP_PA_GE_80018Manager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80018/BP_PA_GE_80018Manager.BP_PA_GE_80018Manager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000744
  className: "BP_PA_GE_Spawner_80018New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80018/BP_PA_GE_Spawner_80018New.BP_PA_GE_Spawner_80018New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000745
  className: "BP_PA_GE_SpawnPoint_80018New"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80018/BP_PA_GE_SpawnPoint_80018New.BP_PA_GE_SpawnPoint_80018New_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000746
  className: "BP_PA_GE_Crab_60229_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60229/BP/BP_PA_GE_Crab_60229_001_A.BP_PA_GE_Crab_60229_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000747
  className: "BP_PA_GE_Crab_60229_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60229/BP/BP_PA_GE_Crab_60229_002_A.BP_PA_GE_Crab_60229_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000748
  className: "BP_PA_DE_SeaMonsterOctopus_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70103/BP/BP_PA_DE_SeaMonsterOctopus_003_A.BP_PA_DE_SeaMonsterOctopus_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000749
  className: "BP_PA_GE_70086_WheelPlateform_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_70086_WheelPlateform_001_A.BP_PA_GE_70086_WheelPlateform_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000750
  className: "BP_PA_GE_70086_WheelPlateform_001_B"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_70086_WheelPlateform_001_B.BP_PA_GE_70086_WheelPlateform_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000751
  className: "BP_PA_GR_CircleLand_Summer_70086_C"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GR_CircleLand_Summer_70086_C.BP_PA_GR_CircleLand_Summer_70086_C_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000752
  className: "BP_PA_GE_Tentacle_70105_B_001"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_B_001.BP_PA_GE_Tentacle_70105_B_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000753
  className: "BP_LevelRule_Actor_60057"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/0057/BP_LevelRule_Actor_60057.BP_LevelRule_Actor_60057_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000754
  className: "BP_LevelRule_Actor_110004"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/110002/BP_LevelRule_Actor_110004.BP_LevelRule_Actor_110004_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000755
  className: "BP_PA_GE_KrakenHead_70105"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_KrakenHead_70105.BP_PA_GE_KrakenHead_70105_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000756
  className: "BP_PA_GE_Tentacle_70105_B_002"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_B_002.BP_PA_GE_Tentacle_70105_B_002_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000757
  className: "BP_PA_GE_Tentacle_70105_B_003"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_B_003.BP_PA_GE_Tentacle_70105_B_003_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000758
  className: "BP_PA_GE_Tentacle_70105_B_004"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_B_004.BP_PA_GE_Tentacle_70105_B_004_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000759
  className: "BP_PA_GE_Tentacle_70105_B_005"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_B_005.BP_PA_GE_Tentacle_70105_B_005_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000760
  className: "BP_PA_GE_Tentacle_70105_B_006"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70105/BP_PA_GE_Tentacle_70105_B_006.BP_PA_GE_Tentacle_70105_B_006_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000761
  className: "BP_LevelRule_Actor_70106"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70106/BP_LevelRule_Actor_70106.BP_LevelRule_Actor_70106_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000762
  className: "BP_PA_GE_HookPortal_70086"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_HookPortal_70086.BP_PA_GE_HookPortal_70086_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000763
  className: "BP_PA_GE_70086_AnnularLand_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_70086_AnnularLand_001_A.BP_PA_GE_70086_AnnularLand_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000764
  className: "BP_PA_GE_70086_AnnularLand_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_70086_AnnularLand_002_A.BP_PA_GE_70086_AnnularLand_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000765
  className: "BP_PA_GE_70086_AnnularLand_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_70086_AnnularLand_003_A.BP_PA_GE_70086_AnnularLand_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000766
  className: "BP_PA_GE_70086_AnnularLand_004_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_70086_AnnularLand_004_A.BP_PA_GE_70086_AnnularLand_004_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000767
  className: "BP_PA_GE_70086_AnnularLand_005_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70086/BP/BP_PA_GE_70086_AnnularLand_005_A.BP_PA_GE_70086_AnnularLand_005_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000768
  className: "BP_LevelRule_Actor_70087"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70087/BP_LevelRule_Actor_70087.BP_LevelRule_Actor_70087_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000769
  className: "BP_LevelRule_Actor_70114"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70114/BP_LevelRule_Actor_70114.BP_LevelRule_Actor_70114_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000770
  className: "BP_LevelRule_Actor_70115"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70115/BP_LevelRule_Actor_70115.BP_LevelRule_Actor_70115_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000771
  className: "BP_LevelRule_Actor_70116"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70116/BP_LevelRule_Actor_70116.BP_LevelRule_Actor_70116_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000772
  className: "BP_LevelRule_Actor_70117"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70117/BP_LevelRule_Actor_70117.BP_LevelRule_Actor_70117_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000773
  className: "BP_PA_GE_80021_yuanzhu"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80021/BP_PA_GE_80021_yuanzhu.BP_PA_GE_80021_yuanzhu_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000774
  className: "BP_PA_GE_80021GameManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80021/BP_PA_GE_80021GameManager.BP_PA_GE_80021GameManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000775
  className: "BP_PA_GE_1x1_70110"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70110/BP_PA_GE_1x1_70110.BP_PA_GE_1x1_70110_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000776
  className: "BP_PA_GE_MoveStage_70110"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70110/BP_PA_GE_MoveStage_70110.BP_PA_GE_MoveStage_70110_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000777
  className: "BP_PA_DE_Vehicle_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60230/BP/BP_PA_DE_Vehicle_001_A.BP_PA_DE_Vehicle_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3006
}
rows {
  typeId: 1100000000000778
  className: "BP_PA_DE_Vehicle_002_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60230/BP/BP_PA_DE_Vehicle_002_A.BP_PA_DE_Vehicle_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3006
}
rows {
  typeId: 1100000000000779
  className: "BP_PA_GE_FlyArea_60232"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_FlyArea_60232.BP_PA_GE_FlyArea_60232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000780
  className: "BP_PA_GE_70097_jumpmanage"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70097/BP/BP_PA_GE_70097_jumpmanage.BP_PA_GE_70097_jumpmanage_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000781
  className: "BP_PA_DE_Sanrio70109Rainbow_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70109/BP/BP_PA_DE_Sanrio70109Rainbow_001_A.BP_PA_DE_Sanrio70109Rainbow_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000782
  className: "BP_PA_GE_selector"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70110/BP/BP_PA_GE_selector.BP_PA_GE_selector_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000783
  className: "BP_PA_GE_70111_Tank"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70111/BP/BP_PA_GE_70111_Tank.BP_PA_GE_70111_Tank_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000784
  className: "BP_PA_GE_70112_Balloon"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70112/BP/BP_PA_GE_70112_Balloon.BP_PA_GE_70112_Balloon_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1100000000000785
  className: "BP_PA_GE_GroundSet_70113_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_1.BP_PA_GE_GroundSet_70113_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000786
  className: "BP_PA_GE_GroundSet_70113_10"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_10.BP_PA_GE_GroundSet_70113_10_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000787
  className: "BP_PA_GE_GroundSet_70113_11"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_11.BP_PA_GE_GroundSet_70113_11_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000788
  className: "BP_PA_GE_GroundSet_70113_12"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_12.BP_PA_GE_GroundSet_70113_12_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000789
  className: "BP_PA_GE_GroundSet_70113_13"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_13.BP_PA_GE_GroundSet_70113_13_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000790
  className: "BP_PA_GE_GroundSet_70113_14"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_14.BP_PA_GE_GroundSet_70113_14_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000791
  className: "BP_PA_GE_GroundSet_70113_15"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_15.BP_PA_GE_GroundSet_70113_15_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000792
  className: "BP_PA_GE_GroundSet_70113_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_2.BP_PA_GE_GroundSet_70113_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000793
  className: "BP_PA_GE_GroundSet_70113_3"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_3.BP_PA_GE_GroundSet_70113_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000794
  className: "BP_PA_GE_GroundSet_70113_4"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_4.BP_PA_GE_GroundSet_70113_4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000795
  className: "BP_PA_GE_GroundSet_70113_5"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_5.BP_PA_GE_GroundSet_70113_5_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000796
  className: "BP_PA_GE_GroundSet_70113_6"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_6.BP_PA_GE_GroundSet_70113_6_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000797
  className: "BP_PA_GE_GroundSet_70113_7"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_7.BP_PA_GE_GroundSet_70113_7_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000798
  className: "BP_PA_GE_GroundSet_70113_8"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_8.BP_PA_GE_GroundSet_70113_8_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000799
  className: "BP_PA_GE_GroundSet_70113_9"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_9.BP_PA_GE_GroundSet_70113_9_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000800
  className: "BP_PA_ColorMatch"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80021/BP_PA_ColorMatch.BP_PA_ColorMatch_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000318
}
rows {
  typeId: 1100000000000801
  className: "BP_PA_ColorMatchFake"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80021/BP_PA_ColorMatchFake.BP_PA_ColorMatchFake_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000318
}
rows {
  typeId: 1100000000000802
  className: "BP_PA_FakeColorMan"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80021/BP_PA_FakeColorMan.BP_PA_FakeColorMan_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1100000000000803
  className: "BP_PA_GE_SweetTrojanHouse_60232"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_SweetTrojanHouse_60232.BP_PA_GE_SweetTrojanHouse_60232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000804
  className: "BP_PA_DE_Vehicle_art"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60230/BP/BP_PA_DE_Vehicle_art.BP_PA_DE_Vehicle_art_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3006
}
rows {
  typeId: 1100000000000805
  className: "BP_PA_GE_Laser_60232_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_Laser_60232_A.BP_PA_GE_Laser_60232_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000806
  className: "BP_Pillar_Down_Test"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70097/BP/BP_Pillar_Down_Test.BP_Pillar_Down_Test_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000807
  className: "BP_Pillar_Up_Test"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70097/BP/BP_Pillar_Up_Test.BP_Pillar_Up_Test_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000808
  className: "BP_TetrisRandom_Test"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70097/BP/BP_TetrisRandom_Test.BP_TetrisRandom_Test_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1100000000000809
  className: "BP_PA_GE_BalloonGameManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70112/BP/BP_PA_GE_BalloonGameManager.BP_PA_GE_BalloonGameManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000810
  className: "BP_PA_GE_selector_70112"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70112/BP/BP_PA_GE_selector_70112.BP_PA_GE_selector_70112_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000811
  className: "BP_PropSpawner_70112"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70112/BP/BP_PropSpawner_70112.BP_PropSpawner_70112_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000000812
  className: "BP_PA_GE_FlappyBirdArea"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_FlappyBirdArea.BP_PA_GE_FlappyBirdArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000813
  className: "BP_PA_GE_Gravity_70113_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_Gravity_70113_A.BP_PA_GE_Gravity_70113_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3028
}
rows {
  typeId: 1100000000000814
  className: "BP_PA_GE_GroundSet_70113_16"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_16.BP_PA_GE_GroundSet_70113_16_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000815
  className: "BP_PA_GE_GroundSet_70113_17"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_17.BP_PA_GE_GroundSet_70113_17_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000816
  className: "BP_PA_GE_GroundSet_70113_18"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_18.BP_PA_GE_GroundSet_70113_18_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000817
  className: "BP_PA_GE_GroundSet_70113_19"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_19.BP_PA_GE_GroundSet_70113_19_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000818
  className: "BP_PA_GE_GroundSet_70113_20"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_20.BP_PA_GE_GroundSet_70113_20_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000819
  className: "BP_PA_GE_GroundSet_70113_21"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_21.BP_PA_GE_GroundSet_70113_21_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000820
  className: "BP_PA_GE_GroundSet_70113_22"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_22.BP_PA_GE_GroundSet_70113_22_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000821
  className: "BP_PA_GE_GroundSet_70113_23"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_23.BP_PA_GE_GroundSet_70113_23_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000822
  className: "BP_PA_GE_GroundSet_70113_24"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSet_70113_24.BP_PA_GE_GroundSet_70113_24_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000823
  className: "BP_PA_GE_HurtZone_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_HurtZone_70113.BP_PA_GE_HurtZone_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000824
  className: "BP_PA_GE_60233_Muma"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60233/BP/BP_PA_GE_60233_Muma.BP_PA_GE_60233_Muma_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000825
  className: "BP_PA_GE_MoveStage_70110"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70110/BP/BP_PA_GE_MoveStage_70110.BP_PA_GE_MoveStage_70110_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000826
  className: "BP_PA_GE_MoveStage_70110_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70110/BP/BP_PA_GE_MoveStage_70110_2.BP_PA_GE_MoveStage_70110_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000827
  className: "BP_PA_GE_70113_plat_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_70113_plat_01.BP_PA_GE_70113_plat_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000828
  className: "BP_PA_GE_70113_roll_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_70113_roll_01.BP_PA_GE_70113_roll_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000829
  className: "BP_PA_GE_FlappyBirdGameManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_FlappyBirdGameManager.BP_PA_GE_FlappyBirdGameManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000830
  className: "BP_PA_GE_GroundSpawner_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_GroundSpawner_70113.BP_PA_GE_GroundSpawner_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000831
  className: "BP_FlappyBirdVehicle"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_FlappyBirdVehicle.BP_FlappyBirdVehicle_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1100000000000832
  className: "BP_PA_DE_Vehicle_001_A_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60230/BP/BP_PA_DE_Vehicle_001_A_2.BP_PA_DE_Vehicle_001_A_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000833
  className: "BP_PA_DE_Vehicle_003_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60230/BP/BP_PA_DE_Vehicle_003_A.BP_PA_DE_Vehicle_003_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000834
  className: "BP_PA_PR_TankLauncher"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70111/BP/BP_PA_PR_TankLauncher.BP_PA_PR_TankLauncher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100033
}
rows {
  typeId: 1100000000000835
  className: "BP_PA_PR_TankTripleLauncher"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70111/BP/BP_PA_PR_TankTripleLauncher.BP_PA_PR_TankTripleLauncher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100033
}
rows {
  typeId: 1100000000000836
  className: "BP_PA_GE_MoveGround_01_70112"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70112/BP/BP_PA_GE_MoveGround_01_70112.BP_PA_GE_MoveGround_01_70112_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000837
  className: "BP_PA_GE_MoveGround_02_70112"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70112/BP/BP_PA_GE_MoveGround_02_70112.BP_PA_GE_MoveGround_02_70112_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000838
  className: "BP_Cloud"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_Cloud.BP_Cloud_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000000839
  className: "BP_LevelRule_Actor_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_LevelRule_Actor_70113.BP_LevelRule_Actor_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000840
  className: "BP_MoeObjectGroupMove_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_MoeObjectGroupMove_70113.BP_MoeObjectGroupMove_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000841
  className: "BP_PA_DE_Youling_001_B_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_DE_Youling_001_B_70113.BP_PA_DE_Youling_001_B_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000842
  className: "BP_PA_GE_70113_plat_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_70113_plat_1.BP_PA_GE_70113_plat_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000843
  className: "BP_PA_GE_70113_plat_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_70113_plat_2.BP_PA_GE_70113_plat_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000844
  className: "BP_PA_GE_70113_plat_3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_70113_plat_3.BP_PA_GE_70113_plat_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000845
  className: "BP_PA_GE_CloudHurtZone_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_CloudHurtZone_70113.BP_PA_GE_CloudHurtZone_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000846
  className: "BP_PA_GE_CloudHurtZone_70113_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_CloudHurtZone_70113_2.BP_PA_GE_CloudHurtZone_70113_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000847
  className: "BP_PA_GE_DestroyArea_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_DestroyArea_70113.BP_PA_GE_DestroyArea_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000848
  className: "BP_PA_GE_tips_70113_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_tips_70113_1.BP_PA_GE_tips_70113_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000849
  className: "BP_PA_GE_tips_70113_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_tips_70113_2.BP_PA_GE_tips_70113_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000850
  className: "BP_PA_GlassA_70113_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GlassA_70113_A.BP_PA_GlassA_70113_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000851
  className: "BP_PA_PR_Cloud_FlappyBird"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_PR_Cloud_FlappyBird.BP_PA_PR_Cloud_FlappyBird_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100007
}
rows {
  typeId: 1100000000000852
  className: "BP_PA_PR_PlaceCloud_FlappyBird"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_PR_PlaceCloud_FlappyBird.BP_PA_PR_PlaceCloud_FlappyBird_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3074
}
rows {
  typeId: 1100000000000853
  className: "BP_SpawnerCloud_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_SpawnerCloud_70113.BP_SpawnerCloud_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000854
  className: "BP_PA_GE_selector_70119"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70119/BP_PA_GE_selector_70119.BP_PA_GE_selector_70119_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000855
  className: "BP_PA_GE_MoveStage_70119"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70119/BP/BP_PA_GE_MoveStage_70119.BP_PA_GE_MoveStage_70119_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000856
  className: "BP_MoeTankGamePlayActor"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70111/BP/BP_MoeTankGamePlayActor.BP_MoeTankGamePlayActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000857
  className: "BP_PA_GE_MoveGround_03_70112"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70112/BP/BP_PA_GE_MoveGround_03_70112.BP_PA_GE_MoveGround_03_70112_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000858
  className: "BP_PA_GE_MoveGround_04_70112"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70112/BP/BP_PA_GE_MoveGround_04_70112.BP_PA_GE_MoveGround_04_70112_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000859
  className: "BP_PA_GE_CloudHurtZone_70113_3"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_CloudHurtZone_70113_3.BP_PA_GE_CloudHurtZone_70113_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000860
  className: "BP_PA_GE_CloudHurtZone_70113_4"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_CloudHurtZone_70113_4.BP_PA_GE_CloudHurtZone_70113_4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000861
  className: "BP_PA_GE_CloudHurtZone_70113_5"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_CloudHurtZone_70113_5.BP_PA_GE_CloudHurtZone_70113_5_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000862
  className: "BP_PA_GE_MoveGround_05_70112"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70112/BP/BP_PA_GE_MoveGround_05_70112.BP_PA_GE_MoveGround_05_70112_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000863
  className: "BP_PA_GE_BackHurtZone_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_BackHurtZone_70113.BP_PA_GE_BackHurtZone_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000864
  className: "BP_PA_DE_FantasyMushroom_60236_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60236/BP/BP_PA_DE_FantasyMushroom_60236_A.BP_PA_DE_FantasyMushroom_60236_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000865
  className: "BP_PA_FU_BfLiHOKIsStreetLight_011_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60236/BP/BP_PA_FU_BfLiHOKIsStreetLight_011_A.BP_PA_FU_BfLiHOKIsStreetLight_011_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000112
}
rows {
  typeId: 1100000000000866
  className: "BP_PA_GE_Boost_FootPrint"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60236/BP/BP_PA_GE_Boost_FootPrint.BP_PA_GE_Boost_FootPrint_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000867
  className: "BP_PA_GR_Leaf_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60236/BP/BP_PA_GR_Leaf_002_A.BP_PA_GR_Leaf_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000868
  className: "BP_SpawnerFootSprint"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60236/BP/BP_SpawnerFootSprint.BP_SpawnerFootSprint_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000869
  className: "BP_PA_PR_TripleTankLauncher"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70111/BP/BP_PA_PR_TripleTankLauncher.BP_PA_PR_TripleTankLauncher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100033
}
rows {
  typeId: 1100000000000870
  className: "BP_PA_GE_UpDownHurtZone_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_UpDownHurtZone_70113.BP_PA_GE_UpDownHurtZone_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000871
  className: "BP_PA_GE_UpHurtZone_70113"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70113/BP/BP_PA_GE_UpHurtZone_70113.BP_PA_GE_UpHurtZone_70113_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000872
  className: "BP_LevelRule_Actor_70223"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70223/BP_LevelRule_Actor_70223.BP_LevelRule_Actor_70223_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000873
  className: "BP_LevelRule_Actor_70224"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70224/BP_LevelRule_Actor_70224.BP_LevelRule_Actor_70224_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000874
  className: "BP_LevelRule_Actor_70225"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70225/BP_LevelRule_Actor_70225.BP_LevelRule_Actor_70225_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000875
  className: "BP_LevelRule_Actor_70226"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70226/BP_LevelRule_Actor_70226.BP_LevelRule_Actor_70226_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000876
  className: "BP_LevelRule_Actor_70227"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70227/BP_LevelRule_Actor_70227.BP_LevelRule_Actor_70227_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000877
  className: "BP_LevelRule_Actor_70228"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70228/BP_LevelRule_Actor_70228.BP_LevelRule_Actor_70228_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000878
  className: "BP_PA_GE_BlastBalloon_001"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_BlastBalloon_001.BP_PA_GE_BlastBalloon_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3007
}
rows {
  typeId: 1100000000000879
  className: "BP_PA_GE_GameManager_60232"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_GameManager_60232.BP_PA_GE_GameManager_60232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000880
  className: "BP_PA_GE_Pillar"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_Pillar.BP_PA_GE_Pillar_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000881
  className: "BP_PA_GE_Boost_001_D"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_Boost_001_D.BP_PA_GE_Boost_001_D_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3009
}
rows {
  typeId: 1100000000000882
  className: "BP_PA_GE_60235_Bee"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60235/BP/BP_PA_GE_60235_Bee.BP_PA_GE_60235_Bee_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000883
  className: "BP_PA_GE_60235_Bullet"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60235/BP/BP_PA_GE_60235_Bullet.BP_PA_GE_60235_Bullet_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000884
  className: "BP_PA_GE_60235_BulletLauncher"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60235/BP/BP_PA_GE_60235_BulletLauncher.BP_PA_GE_60235_BulletLauncher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000085
}
rows {
  typeId: 1100000000000885
  className: "BP_PA_GE_60235_GameManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60235/BP/BP_PA_GE_60235_GameManager.BP_PA_GE_60235_GameManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000886
  className: "BP_PA_GE_60235_SlipperyArea"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60235/BP/BP_PA_GE_60235_SlipperyArea.BP_PA_GE_60235_SlipperyArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000887
  className: "BP_PA_GE_StrangeFlower_Bullet_002"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60235/BP/BP_PA_GE_StrangeFlower_Bullet_002.BP_PA_GE_StrangeFlower_Bullet_002_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000000888
  className: "BP_Decal_M13"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60236/BP/BP_Decal_M13.BP_Decal_M13_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000889
  className: "BP_Decal_M13_2"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60236/BP/BP_Decal_M13_2.BP_Decal_M13_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000890
  className: "BP_Mist_60236"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60236/BP/BP_Mist_60236.BP_Mist_60236_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000891
  className: "BP_NR3E1_PA_GR_TE_Water_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60236/BP/BP_NR3E1_PA_GR_TE_Water_001_A.BP_NR3E1_PA_GR_TE_Water_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000892
  className: "BP_PA_DE_ChinoiserieTree_004_A60236"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60236/BP/BP_PA_DE_ChinoiserieTree_004_A60236.BP_PA_DE_ChinoiserieTree_004_A60236_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000893
  className: "BP_PA_GR_Leaf_002_A_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60236/BP/BP_PA_GR_Leaf_002_A_2.BP_PA_GR_Leaf_002_A_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000894
  className: "BP_test"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60236/BP/BP_test.BP_test_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000895
  className: "BP_PA_GE_Elf_60240"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60240/BP/BP_PA_GE_Elf_60240.BP_PA_GE_Elf_60240_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000000896
  className: "BP_PA_GE_70111_BrokenBox"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70111/BP/BP_PA_GE_70111_BrokenBox.BP_PA_GE_70111_BrokenBox_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000897
  className: "BP_PA_GE_70111_PlasmaBallLauncher"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70111/BP/BP_PA_GE_70111_PlasmaBallLauncher.BP_PA_GE_70111_PlasmaBallLauncher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000072
}
rows {
  typeId: 1100000000000898
  className: "BP_SpawnerTankBloodPocket"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70111/BP/BP_SpawnerTankBloodPocket.BP_SpawnerTankBloodPocket_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000899
  className: "BP_TankBloodPocket"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70111/BP/BP_TankBloodPocket.BP_TankBloodPocket_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000900
  className: "BP_PA_GE_70119GameManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70119/BP/BP_PA_GE_70119GameManager.BP_PA_GE_70119GameManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000901
  className: "BP_PA_GE_MemoryPlat_002_70119"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70119/BP/BP_PA_GE_MemoryPlat_002_70119.BP_PA_GE_MemoryPlat_002_70119_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3035
}
rows {
  typeId: 1100000000000902
  className: "BP_PA_GE_MemoryPlat_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70119/BP/BP_PA_GE_MemoryPlat_002_A.BP_PA_GE_MemoryPlat_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3035
}
rows {
  typeId: 1100000000000903
  className: "BP_PA_GE_CylinderFans_001_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70120/BP/BP_PA_GE_CylinderFans_001_B.BP_PA_GE_CylinderFans_001_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3024
}
rows {
  typeId: 1100000000000904
  className: "BP_LevelRule_Actor_70222"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70222/BP_LevelRule_Actor_70222.BP_LevelRule_Actor_70222_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000905
  className: "BP_DestroyArea_BasketBall"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_DestroyArea_BasketBall.BP_DestroyArea_BasketBall_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000906
  className: "BP_GA_GE_BallFrame"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_GA_GE_BallFrame.BP_GA_GE_BallFrame_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000907
  className: "BP_GA_GE_BallFrame_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_GA_GE_BallFrame_2.BP_GA_GE_BallFrame_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000473
}
rows {
  typeId: 1100000000000908
  className: "BP_GA_GE_BallFrame_3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_GA_GE_BallFrame_3.BP_GA_GE_BallFrame_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000473
}
rows {
  typeId: 1100000000000909
  className: "BP_GA_GE_Basketball"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_GA_GE_Basketball.BP_GA_GE_Basketball_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000910
  className: "BP_GA_GE_BasketballSpawner"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_GA_GE_BasketballSpawner.BP_GA_GE_BasketballSpawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000911
  className: "BP_MoeBasketBallGameActor"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_MoeBasketBallGameActor.BP_MoeBasketBallGameActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000912
  className: "BP_PA_GE_BasketballLauncher"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80020/BP/BP_PA_GE_BasketballLauncher.BP_PA_GE_BasketballLauncher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3013
}
rows {
  typeId: 1100000000000913
  className: "BP_PA_GE_Spawner_80020"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_PA_GE_Spawner_80020.BP_PA_GE_Spawner_80020_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000914
  className: "BP_PA_GE_SpawnPoint_80020"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_PA_GE_SpawnPoint_80020.BP_PA_GE_SpawnPoint_80020_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000915
  className: "BP_PA_PR_BasketBallBase"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_PA_PR_BasketBallBase.BP_PA_PR_BasketBallBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000431
}
rows {
  typeId: 1100000000000916
  className: "BP_PA_GE_60235_Leaf"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60235/BP/BP_PA_GE_60235_Leaf.BP_PA_GE_60235_Leaf_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000917
  className: "BP_PA_DE_Flower80020Disc_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_PA_DE_Flower80020Disc_001_A.BP_PA_DE_Flower80020Disc_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000918
  className: "BP_PA_GE_Leaf_60232"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_Leaf_60232.BP_PA_GE_Leaf_60232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000919
  className: "BP_PA_GE_FlowerRoller"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60237/BP/BP_PA_GE_FlowerRoller.BP_PA_GE_FlowerRoller_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000920
  className: "BP_PA_GE_MaterialChanger_Plat"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70119/BP/BP_PA_GE_MaterialChanger_Plat.BP_PA_GE_MaterialChanger_Plat_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000921
  className: "BP_PA_GE_70120"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70120/BP/BP_PA_GE_70120.BP_PA_GE_70120_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000922
  className: "BP_PA_GE_BrokenPlat_001_E"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70120/BP/BP_PA_GE_BrokenPlat_001_E.BP_PA_GE_BrokenPlat_001_E_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000000923
  className: "BP_PA_GE_CylinderFans_001_B_70120"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70120/BP/BP_PA_GE_CylinderFans_001_B_70120.BP_PA_GE_CylinderFans_001_B_70120_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3024
}
rows {
  typeId: 1100000000000924
  className: "BP_PA_DE_Flower80020Disc_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_PA_DE_Flower80020Disc_001_A.BP_PA_DE_Flower80020Disc_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000925
  className: "BP_GA_GE_BallFrameBase"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_GA_GE_BallFrameBase.BP_GA_GE_BallFrameBase_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000926
  className: "BP_GA_GE_BallFrame_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80020/BP/BP_GA_GE_BallFrame_1.BP_GA_GE_BallFrame_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000473
}
rows {
  typeId: 1100000000000927
  className: "BP_PA_GE_Boost_001_D"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_Boost_001_D.BP_PA_GE_Boost_001_D_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3009
}
rows {
  typeId: 1100000000000928
  className: "BP_TankClass"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70111/BP/BP_TankClass.BP_TankClass_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1100000000000929
  className: "BP_PA_GE_Boost_001_D"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_Boost_001_D.BP_PA_GE_Boost_001_D_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3009
}
rows {
  typeId: 1100000000000930
  className: "BP_PA_DE_Flower80020Disc_001_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/80020/BP/BP_PA_DE_Flower80020Disc_001_A.BP_PA_DE_Flower80020Disc_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000000931
  className: "BP_PA_GE_Boost_001_C_60237"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60237/BP/BP_PA_GE_Boost_001_C_60237.BP_PA_GE_Boost_001_C_60237_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3009
}
rows {
  typeId: 1100000000000932
  className: "BP_LevelRule_Actor_70120"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70120/BP_LevelRule_Actor_70120.BP_LevelRule_Actor_70120_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000933
  className: "BP_PA_GlassA_60232_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GlassA_60232_A.BP_PA_GlassA_60232_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000000934
  className: "BP_GE_PA_HideGrass_005_A_70222"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70222/BP_GE_PA_HideGrass_005_A_70222.BP_GE_PA_HideGrass_005_A_70222_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3030
}
rows {
  typeId: 1100000000000935
  className: "BP_70231_ClockShowTime"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70231/BP/BP_70231_ClockShowTime.BP_70231_ClockShowTime_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000936
  className: "BP_70231_GameController"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70231/BP/BP_70231_GameController.BP_70231_GameController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000937
  className: "BP_PA_GE_70231_ClockHands_A"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70231/BP/BP_PA_GE_70231_ClockHands_A.BP_PA_GE_70231_ClockHands_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000938
  className: "BP_PA_GE_70231_ClockHands_B"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70231/BP/BP_PA_GE_70231_ClockHands_B.BP_PA_GE_70231_ClockHands_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000939
  className: "BP_70231_WaveLogicController"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70231/BP/BP_70231_WaveLogicController.BP_70231_WaveLogicController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000151
}
rows {
  typeId: 1100000000000940
  className: "BP_GA_GE_70231_HexPlat"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70231/BP/BP_GA_GE_70231_HexPlat.BP_GA_GE_70231_HexPlat_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000941
  className: "BP_GA_GE_TimeDoor_70232"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_GA_GE_TimeDoor_70232.BP_GA_GE_TimeDoor_70232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000942
  className: "BP_PA_GE_GameManager_70232"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_PA_GE_GameManager_70232.BP_PA_GE_GameManager_70232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000943
  className: "BP_PA_GE_HookPortal_70232"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_PA_GE_HookPortal_70232.BP_PA_GE_HookPortal_70232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000944
  className: "BP_TimeCapsule_70232"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_TimeCapsule_70232.BP_TimeCapsule_70232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000227
}
rows {
  typeId: 1100000000000945
  className: "BP_PA_GE_70104_E_1"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70120/BP/BP_PA_GE_70104_E_1.BP_PA_GE_70104_E_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000946
  className: "BP_PA_GE_BrokenPlat_001_E"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/70120/BP/BP_PA_GE_BrokenPlat_001_E.BP_PA_GE_BrokenPlat_001_E_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000018
}
rows {
  typeId: 1100000000000947
  className: "BP_LevelRule_Actor_110011"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/110011/BP_LevelRule_Actor_110011.BP_LevelRule_Actor_110011_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000948
  className: "BP_LevelRule_Actor_70231"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70231/BP_LevelRule_Actor_70231.BP_LevelRule_Actor_70231_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000949
  className: "BP_70231_ClockShowTime"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70231/BP/BP_70231_ClockShowTime.BP_70231_ClockShowTime_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000950
  className: "BP_70231_GameController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70231/BP/BP_70231_GameController.BP_70231_GameController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000951
  className: "BP_70231_WaveLogicController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70231/BP/BP_70231_WaveLogicController.BP_70231_WaveLogicController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000952
  className: "BP_GA_GE_70231_HexPlat"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70231/BP/BP_GA_GE_70231_HexPlat.BP_GA_GE_70231_HexPlat_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000953
  className: "BP_PA_GE_70231_ClockHands_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70231/BP/BP_PA_GE_70231_ClockHands_A.BP_PA_GE_70231_ClockHands_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000954
  className: "BP_PA_GE_70231_ClockHands_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70231/BP/BP_PA_GE_70231_ClockHands_B.BP_PA_GE_70231_ClockHands_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000955
  className: "BP_TimeCapsuleBase_70232"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_TimeCapsuleBase_70232.BP_TimeCapsuleBase_70232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000227
}
rows {
  typeId: 1100000000000956
  className: "BP_TimeCapsule_70232_1s"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_TimeCapsule_70232_1s.BP_TimeCapsule_70232_1s_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000227
}
rows {
  typeId: 1100000000000957
  className: "BP_TimeCapsule_70232_2s"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_TimeCapsule_70232_2s.BP_TimeCapsule_70232_2s_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000227
}
rows {
  typeId: 1100000000000958
  className: "BP_TimeCapsule_70232_3s"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_TimeCapsule_70232_3s.BP_TimeCapsule_70232_3s_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000227
}
rows {
  typeId: 1100000000000959
  className: "BP_TimeCapsule_70232_4s"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_TimeCapsule_70232_4s.BP_TimeCapsule_70232_4s_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000227
}
rows {
  typeId: 1100000000000960
  className: "BP_LevelRule_Actor_70112"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70112/BP/BP_LevelRule_Actor_70112.BP_LevelRule_Actor_70112_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000961
  className: "BP_PA_GE_SpawnPoint_70232"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_PA_GE_SpawnPoint_70232.BP_PA_GE_SpawnPoint_70232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000962
  className: "BP_60241_BridgeTimeManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60241/BP/BP_60241_BridgeTimeManager.BP_60241_BridgeTimeManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000963
  className: "BP_60241_RandomSelector"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60241/BP/BP_60241_RandomSelector.BP_60241_RandomSelector_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000964
  className: "BP_60241_RandomSelector_TD"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60241/BP/BP_60241_RandomSelector_TD.BP_60241_RandomSelector_TD_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000000965
  className: "BP_PA_GE_SpawnPoint_60243"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60243/BP/BP_PA_GE_SpawnPoint_60243.BP_PA_GE_SpawnPoint_60243_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000966
  className: "BP_PA_GR_CubeLand_Magic_001_A_testleft"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60243/BP/BP_PA_GR_CubeLand_Magic_001_A_testleft.BP_PA_GR_CubeLand_Magic_001_A_testleft_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000967
  className: "BP_PA_GR_CubeLand_Magic_001_A_testright"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60243/BP/BP_PA_GR_CubeLand_Magic_001_A_testright.BP_PA_GR_CubeLand_Magic_001_A_testright_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000968
  className: "BP_PA_GR_CubeLand_Magic_60243_right"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60243/BP/BP_PA_GR_CubeLand_Magic_60243_right.BP_PA_GR_CubeLand_Magic_60243_right_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000969
  className: "BP_PA_GR_CubeLand_Magic_60243_testleft"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60243/BP/BP_PA_GR_CubeLand_Magic_60243_testleft.BP_PA_GR_CubeLand_Magic_60243_testleft_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000970
  className: "BP_PA_GR_CubeLand_Magic_60243_testright"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60243/BP/BP_PA_GR_CubeLand_Magic_60243_testright.BP_PA_GR_CubeLand_Magic_60243_testright_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000971
  className: "BP_PA_IT_Ball_008_A_60243"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60243/BP/BP_PA_IT_Ball_008_A_60243.BP_PA_IT_Ball_008_A_60243_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000972
  className: "BP_Spawner60243"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60243/BP/BP_Spawner60243.BP_Spawner60243_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000973
  className: "BP_LevelRule_Actor_70230"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70230/BP/BP_LevelRule_Actor_70230.BP_LevelRule_Actor_70230_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000974
  className: "BP_LogicActor_70230_GameController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70230/BP/BP_LogicActor_70230_GameController.BP_LogicActor_70230_GameController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000975
  className: "BP_PA_GE_70230_HexPlat"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70230/BP/BP_PA_GE_70230_HexPlat.BP_PA_GE_70230_HexPlat_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1100000000000976
  className: "BP_LevelRule_Actor_70231"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70231/BP/BP_LevelRule_Actor_70231.BP_LevelRule_Actor_70231_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000000977
  className: "BP_PA_GE_70231_Fish"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70231/BP/BP_PA_GE_70231_Fish.BP_PA_GE_70231_Fish_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000978
  className: "BP_GA_GE_TimeDoor_Base"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_GA_GE_TimeDoor_Base.BP_GA_GE_TimeDoor_Base_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000979
  className: "BP_Spawner70232"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70232/BP/BP_Spawner70232.BP_Spawner70232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000980
  className: "BP_PA_GE_70233_A_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_A_1.BP_PA_GE_70233_A_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000981
  className: "BP_PA_GE_70233_A_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_A_2.BP_PA_GE_70233_A_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000982
  className: "BP_PA_GE_70233_A_3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_A_3.BP_PA_GE_70233_A_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000983
  className: "BP_PA_GE_70233_A_4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_A_4.BP_PA_GE_70233_A_4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000984
  className: "BP_PA_GE_70233_A_5"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_A_5.BP_PA_GE_70233_A_5_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000985
  className: "BP_PA_GE_70233_A_6"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_A_6.BP_PA_GE_70233_A_6_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000986
  className: "BP_PA_GE_70233_B_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_B_1.BP_PA_GE_70233_B_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000987
  className: "BP_PA_GE_70233_B_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_B_2.BP_PA_GE_70233_B_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000988
  className: "BP_PA_GE_70233_B_3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_B_3.BP_PA_GE_70233_B_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000989
  className: "BP_PA_GE_70233_B_4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_B_4.BP_PA_GE_70233_B_4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000990
  className: "BP_PA_GE_70233_B_5"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_B_5.BP_PA_GE_70233_B_5_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000991
  className: "BP_PA_GE_70233_B_6"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_B_6.BP_PA_GE_70233_B_6_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000992
  className: "BP_PA_GE_70233_C_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_C_1.BP_PA_GE_70233_C_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000993
  className: "BP_PA_GE_70233_C_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_C_2.BP_PA_GE_70233_C_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000994
  className: "BP_PA_GE_70233_C_3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_C_3.BP_PA_GE_70233_C_3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000995
  className: "BP_PA_GE_70233_D_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_D_1.BP_PA_GE_70233_D_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000996
  className: "BP_PA_GE_70233_D_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_D_2.BP_PA_GE_70233_D_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000997
  className: "BP_PA_GE_70233_E_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70233/BP_PA_GE_70233_E_1.BP_PA_GE_70233_E_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3024
}
rows {
  typeId: 1100000000000998
  className: "BP_GameManager_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_GameManager_70234.BP_GameManager_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000000999
  className: "BP_MinusHPArea"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_MinusHPArea.BP_MinusHPArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001000
  className: "BP_RockPlat_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_RockPlat_70234.BP_RockPlat_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001001
  className: "BP_RockPlat_B_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_RockPlat_B_70234.BP_RockPlat_B_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001002
  className: "BP_BrokenCube_Base_70235_L_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_BrokenCube_Base_70235_L_mid.BP_BrokenCube_Base_70235_L_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000025
}
rows {
  typeId: 1100000000001003
  className: "BP_BrokenCube_Base_70235_XL_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_BrokenCube_Base_70235_XL_mid.BP_BrokenCube_Base_70235_XL_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000025
}
rows {
  typeId: 1100000000001004
  className: "BP_PA_GE_Plat_025_A_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_Plat_025_A_70235_mid.BP_PA_GE_Plat_025_A_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000001005
  className: "BP_PA_GE_Plat_025_A_70235_mid_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_Plat_025_A_70235_mid_2.BP_PA_GE_Plat_025_A_70235_mid_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000001006
  className: "BP_BrokenCube_Base_70235_L_SIDE"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_BrokenCube_Base_70235_L_SIDE.BP_BrokenCube_Base_70235_L_SIDE_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000025
}
rows {
  typeId: 1100000000001007
  className: "BP_BrokenCube_Base_70235_square_SIDE"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_BrokenCube_Base_70235_square_SIDE.BP_BrokenCube_Base_70235_square_SIDE_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000025
}
rows {
  typeId: 1100000000001008
  className: "BP_PA_GE_Bounce_002_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_Bounce_002_A.BP_PA_GE_Bounce_002_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3010
}
rows {
  typeId: 1100000000001009
  className: "BP_PA_GE_Plat_025_A_70235_side_XL"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_Plat_025_A_70235_side_XL.BP_PA_GE_Plat_025_A_70235_side_XL_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000001010
  className: "BP_GA_GE_70231_Plat"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70231/BP/BP_GA_GE_70231_Plat.BP_GA_GE_70231_Plat_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001011
  className: "BP_PA_GE_RockPlat_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_PA_GE_RockPlat_70234.BP_PA_GE_RockPlat_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001012
  className: "BP_PA_GE_RockPlat_B_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_PA_GE_RockPlat_B_70234.BP_PA_GE_RockPlat_B_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001013
  className: "BP_PA_GE_Bounce_70235"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_Bounce_70235.BP_PA_GE_Bounce_70235_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3010
}
rows {
  typeId: 1100000000001014
  className: "BP_PA_PR_Bomb_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_PA_PR_Bomb_70234.BP_PA_PR_Bomb_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100003
}
rows {
  typeId: 1100000000001015
  className: "BP_PA_PR_Boomerang_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_PA_PR_Boomerang_70234.BP_PA_PR_Boomerang_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100004
}
rows {
  typeId: 1100000000001016
  className: "BP_PA_PR_Ice_001_A_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_PA_PR_Ice_001_A_70234.BP_PA_PR_Ice_001_A_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100011
}
rows {
  typeId: 1100000000001017
  className: "BP_PA_PR_Landmine_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_PA_PR_Landmine_70234.BP_PA_PR_Landmine_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100013
}
rows {
  typeId: 1100000000001018
  className: "BP_PA_PR_RotateHammer_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_PA_PR_RotateHammer_70234.BP_PA_PR_RotateHammer_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000230
}
rows {
  typeId: 1100000000001019
  className: "BP_PA_PR_ScreamingChicken_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_PA_PR_ScreamingChicken_70234.BP_PA_PR_ScreamingChicken_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100017
}
rows {
  typeId: 1100000000001020
  className: "BP_PA_PR_VacuumCleaner_70234"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70234/BP/BP_PA_PR_VacuumCleaner_70234.BP_PA_PR_VacuumCleaner_70234_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000236
}
rows {
  typeId: 1100000000001021
  className: "BP_PA_GE_70238_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP_PA_GE_70238_A.BP_PA_GE_70238_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001022
  className: "BP_PA_GE_70238_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP_PA_GE_70238_B.BP_PA_GE_70238_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001023
  className: "BP_PA_DE_80022_PopcornMachine"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_DE_80022_PopcornMachine.BP_PA_DE_80022_PopcornMachine_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001024
  className: "BP_PA_DE_Shelves"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_DE_Shelves.BP_PA_DE_Shelves_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001025
  className: "BP_PA_GE_80022_BulletLauncher"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_BulletLauncher.BP_PA_GE_80022_BulletLauncher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000085
}
rows {
  typeId: 1100000000001026
  className: "BP_PA_GE_80022_PopCorn"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_PopCorn.BP_PA_GE_80022_PopCorn_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001027
  className: "BP_PA_GE_80022_PopcornMachine"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_PopcornMachine.BP_PA_GE_80022_PopcornMachine_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000085
}
rows {
  typeId: 1100000000001028
  className: "BP_LevelRule_Actor_76072"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76072.BP_LevelRule_Actor_76072_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001029
  className: "BP_LevelRule_Actor_76073"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76073.BP_LevelRule_Actor_76073_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001030
  className: "BP_LevelRule_Actor_76074"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76074.BP_LevelRule_Actor_76074_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001031
  className: "BP_LevelRule_Actor_76075"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76075.BP_LevelRule_Actor_76075_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001032
  className: "BP_LevelRule_Actor_76076"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76076.BP_LevelRule_Actor_76076_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001033
  className: "BP_LevelRule_Actor_76077"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76077.BP_LevelRule_Actor_76077_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001034
  className: "BP_LevelRule_Actor_76078"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76078.BP_LevelRule_Actor_76078_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001035
  className: "BP_LevelRule_Actor_76079"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76079.BP_LevelRule_Actor_76079_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001036
  className: "BP_LevelRule_Actor_76080"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76080.BP_LevelRule_Actor_76080_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001037
  className: "BP_LevelRule_Actor_76081"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76081.BP_LevelRule_Actor_76081_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001038
  className: "BP_LevelRule_Actor_76082"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76082.BP_LevelRule_Actor_76082_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001039
  className: "BP_LevelRule_Actor_76083"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_76083.BP_LevelRule_Actor_76083_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001040
  className: "BP_PA_GE_Frog_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_Frog_mid.BP_PA_GE_Frog_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3026
}
rows {
  typeId: 1100000000001041
  className: "BP_PA_GE_MoveBar_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_MoveBar_70235_mid.BP_PA_GE_MoveBar_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001042
  className: "BP_PA_GE_PushDoor_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_PushDoor_70235_mid.BP_PA_GE_PushDoor_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000237
}
rows {
  typeId: 1100000000001043
  className: "BP_PA_GE_RotateBar_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_RotateBar_70235_mid.BP_PA_GE_RotateBar_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3049
}
rows {
  typeId: 1100000000001044
  className: "BP_BrokenCube_Base_70235_ractan1_SIDE"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_BrokenCube_Base_70235_ractan1_SIDE.BP_BrokenCube_Base_70235_ractan1_SIDE_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000025
}
rows {
  typeId: 1100000000001045
  className: "BP_BrokenCube_Base_70235_rectan2_SIDE"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_BrokenCube_Base_70235_rectan2_SIDE.BP_BrokenCube_Base_70235_rectan2_SIDE_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000025
}
rows {
  typeId: 1100000000001046
  className: "BP_PA_GE_Frog_side"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_Frog_side.BP_PA_GE_Frog_side_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3026
}
rows {
  typeId: 1100000000001047
  className: "BP_PA_GE_Plat_025_A_70235_side1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_Plat_025_A_70235_side1.BP_PA_GE_Plat_025_A_70235_side1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001048
  className: "BP_PA_GlassA_empty_70235"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GlassA_empty_70235.BP_PA_GlassA_empty_70235_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000001049
  className: "BP_PA_GE_Cake_70237"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70237/BP/BP_PA_GE_Cake_70237.BP_PA_GE_Cake_70237_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001050
  className: "BP_PA_GE_Fork_70237"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70237/BP/BP_PA_GE_Fork_70237.BP_PA_GE_Fork_70237_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001051
  className: "BP_PA_GE_Knife_70237"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70237/BP/BP_PA_GE_Knife_70237.BP_PA_GE_Knife_70237_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001052
  className: "BP_PA_GE_LineWarning_70237"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70237/BP/BP_PA_GE_LineWarning_70237.BP_PA_GE_LineWarning_70237_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001053
  className: "BP_PA_GE_selector_70237"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70237/BP/BP_PA_GE_selector_70237.BP_PA_GE_selector_70237_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001054
  className: "BP_LevelRule_Actor_80022"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_LevelRule_Actor_80022.BP_LevelRule_Actor_80022_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001055
  className: "BP_PA_DE_80022_Shelves"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_DE_80022_Shelves.BP_PA_DE_80022_Shelves_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001056
  className: "BP_PA_PR_VacuumCleaner_80022"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_PR_VacuumCleaner_80022.BP_PA_PR_VacuumCleaner_80022_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000236
}
rows {
  typeId: 1100000000001057
  className: "BP_LevelRule_Actor_60001"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60001/BP_LevelRule_Actor_60001.BP_LevelRule_Actor_60001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001058
  className: "BP_VaccumCleanerTrigger_80022"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_VaccumCleanerTrigger_80022.BP_VaccumCleanerTrigger_80022_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001059
  className: "BP_PA_GE_Boost_80022"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_Boost_80022.BP_PA_GE_Boost_80022_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001060
  className: "BP_PA_GE_BengChuang_70235"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_BengChuang_70235.BP_PA_GE_BengChuang_70235_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3006
}
rows {
  typeId: 1100000000001061
  className: "BP_PA_GE_NoCollisionPushDoor_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_NoCollisionPushDoor_70235_mid.BP_PA_GE_NoCollisionPushDoor_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001062
  className: "BP_PA_GE_NoReboundRotateBar_70235"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_NoReboundRotateBar_70235.BP_PA_GE_NoReboundRotateBar_70235_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3049
}
rows {
  typeId: 1100000000001063
  className: "BP_PA_GE_Plat_EZ_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_Plat_EZ_70235_mid.BP_PA_GE_Plat_EZ_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001064
  className: "BP_PA_GE_Plat_EZ_withprop1_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_Plat_EZ_withprop1_70235_mid.BP_PA_GE_Plat_EZ_withprop1_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001065
  className: "BP_PA_GE_Plat_EZ_withprop2_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_Plat_EZ_withprop2_70235_mid.BP_PA_GE_Plat_EZ_withprop2_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001066
  className: "BP_PA_GE_Plat_EZ_withprop3_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_Plat_EZ_withprop3_70235_mid.BP_PA_GE_Plat_EZ_withprop3_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001067
  className: "BP_PA_GR_plat_hard_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GR_plat_hard_70235_mid.BP_PA_GR_plat_hard_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000001068
  className: "BP_PropSpawner_level1_70235"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PropSpawner_level1_70235.BP_PropSpawner_level1_70235_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000001069
  className: "BP_PropSpawner_level2_70236"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PropSpawner_level2_70236.BP_PropSpawner_level2_70236_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000001070
  className: "BP_PropSpawner_level3_70237"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PropSpawner_level3_70237.BP_PropSpawner_level3_70237_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000001071
  className: "BP_BrokenCube_Base_70235_ractan1_hard_SIDE"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_BrokenCube_Base_70235_ractan1_hard_SIDE.BP_BrokenCube_Base_70235_ractan1_hard_SIDE_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000001072
  className: "BP_BrokenCube_Base_70235_rectan2_EZ_SIDE"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_BrokenCube_Base_70235_rectan2_EZ_SIDE.BP_BrokenCube_Base_70235_rectan2_EZ_SIDE_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000001073
  className: "BP_BrokenCube_Base_70235_rectan2_hard_SIDE"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_BrokenCube_Base_70235_rectan2_hard_SIDE.BP_BrokenCube_Base_70235_rectan2_hard_SIDE_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000001074
  className: "BP_BrokenCube_Base_70235_square3_ez_SIDE"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_BrokenCube_Base_70235_square3_ez_SIDE.BP_BrokenCube_Base_70235_square3_ez_SIDE_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000001075
  className: "BP_BrokenCube_Base_70235_square3_hard_SIDE_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_BrokenCube_Base_70235_square3_hard_SIDE_2.BP_BrokenCube_Base_70235_square3_hard_SIDE_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000001076
  className: "BP_PA_GE_MoveBar_70235_side"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_MoveBar_70235_side.BP_PA_GE_MoveBar_70235_side_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001077
  className: "BP_PA_GE_RotateBar_70235_side"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_RotateBar_70235_side.BP_PA_GE_RotateBar_70235_side_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3049
}
rows {
  typeId: 1100000000001078
  className: "BP_LevelRule_Actor_70236"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70236/BP/BP_LevelRule_Actor_70236.BP_LevelRule_Actor_70236_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001079
  className: "BP_LogicActor_70236_PillarController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70236/BP/BP_LogicActor_70236_PillarController.BP_LogicActor_70236_PillarController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001080
  className: "BP_PA_GE_70236_Pillar"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70236/BP/BP_PA_GE_70236_Pillar.BP_PA_GE_70236_Pillar_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001081
  className: "BP_LevelRule_Actor_70237"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70237/BP/BP_LevelRule_Actor_70237.BP_LevelRule_Actor_70237_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001082
  className: "BP_PA_GE_CakeAnnularLand_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70237/BP/BP_PA_GE_CakeAnnularLand_001_A.BP_PA_GE_CakeAnnularLand_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001083
  className: "BP_PA_GE_CakeCircleLand_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70237/BP/BP_PA_GE_CakeCircleLand_001_A.BP_PA_GE_CakeCircleLand_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001084
  className: "BP_PA_GE_CircleHit_70237"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70237/BP/BP_PA_GE_CircleHit_70237.BP_PA_GE_CircleHit_70237_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001085
  className: "BP_PA_PR_Cake_70238"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP_PA_PR_Cake_70238.BP_PA_PR_Cake_70238_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100011
}
rows {
  typeId: 1100000000001086
  className: "BP_PA_SU_CakeBomb_70238"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP_PA_SU_CakeBomb_70238.BP_PA_SU_CakeBomb_70238_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000373
}
rows {
  typeId: 1100000000001087
  className: "BP_LevelRule_Actor_70238"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP/BP_LevelRule_Actor_70238.BP_LevelRule_Actor_70238_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001088
  className: "BP_PA_GE_CreamBusBase_S9_70238"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP/BP_PA_GE_CreamBusBase_S9_70238.BP_PA_GE_CreamBusBase_S9_70238_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001089
  className: "BP_PA_GE_Cream_70238"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP/BP_PA_GE_Cream_70238.BP_PA_GE_Cream_70238_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001090
  className: "BP_PA_GE_Cream_70238_NEW"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP/BP_PA_GE_Cream_70238_NEW.BP_PA_GE_Cream_70238_NEW_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001091
  className: "BP_PA_GE_ThrowCake_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP/BP_PA_GE_ThrowCake_001_A.BP_PA_GE_ThrowCake_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001092
  className: "BP_PA_PR_Ice_001_A_70238"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP/BP_PA_PR_Ice_001_A_70238.BP_PA_PR_Ice_001_A_70238_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100011
}
rows {
  typeId: 1100000000001093
  className: "BP_LogicActor_80022_BulletTimeRandom"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_LogicActor_80022_BulletTimeRandom.BP_LogicActor_80022_BulletTimeRandom_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001094
  className: "BP_PA_DE_ConveyEnd"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_DE_ConveyEnd.BP_PA_DE_ConveyEnd_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1100000000001095
  className: "BP_PA_GE_80022_CylinderFans"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_CylinderFans.BP_PA_GE_80022_CylinderFans_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3024
}
rows {
  typeId: 1100000000001096
  className: "BP_PA_GE_80022_Fist"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_Fist.BP_PA_GE_80022_Fist_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001097
  className: "BP_PA_GE_80022_Mixer"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_Mixer.BP_PA_GE_80022_Mixer_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001098
  className: "BP_PA_GE_80022_Pillar"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_Pillar.BP_PA_GE_80022_Pillar_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001099
  className: "BP_PA_GE_80022_RotateBar_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_RotateBar_A.BP_PA_GE_80022_RotateBar_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001100
  className: "BP_LogicActor_70235_GameController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/BP_LogicActor_70235_GameController.BP_LogicActor_70235_GameController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001101
  className: "BP_PA_GE_BengChuang_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_BengChuang_mid.BP_PA_GE_BengChuang_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3006
}
rows {
  typeId: 1100000000001102
  className: "BP_PA_GE_BengChuang_side"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_BengChuang_side.BP_PA_GE_BengChuang_side_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3006
}
rows {
  typeId: 1100000000001103
  className: "BP_PA_GE_70238_SlipperyArea"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP/BP_PA_GE_70238_SlipperyArea.BP_PA_GE_70238_SlipperyArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001104
  className: "BP_PA_GE_60235_Bee_02"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60235/BP/BP_PA_GE_60235_Bee_02.BP_PA_GE_60235_Bee_02_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001105
  className: "BP_PA_PR_Fireworks_UGCInfinite_60252"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60252/BP_PA_PR_Fireworks_UGCInfinite_60252.BP_PA_PR_Fireworks_UGCInfinite_60252_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100027
}
rows {
  typeId: 1100000000001106
  className: "BP_PA_GE_80022_PushDoor"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_PushDoor.BP_PA_GE_80022_PushDoor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000001107
  className: "BP_AddAbilityTestActor"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/110011/BP_AddAbilityTestActor.BP_AddAbilityTestActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1100000000001108
  className: "BP_MovePlatBase_70235"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/BP_MovePlatBase_70235.BP_MovePlatBase_70235_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001109
  className: "BP_PA_GE_CrossGear_rotating_anticlockwise_70235_static_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_CrossGear_rotating_anticlockwise_70235_static_mid.BP_PA_GE_CrossGear_rotating_anticlockwise_70235_static_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001110
  className: "BP_PA_GE_CrossGear_rotating_clockwise_70235_dynamic_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_CrossGear_rotating_clockwise_70235_dynamic_mid.BP_PA_GE_CrossGear_rotating_clockwise_70235_dynamic_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001111
  className: "BP_PA_GE_Turntable_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_Turntable_70235_mid.BP_PA_GE_Turntable_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3062
}
rows {
  typeId: 1100000000001112
  className: "BP_PA_GE_CrossGear_rotating_anticlockwise_70235_static_side"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_CrossGear_rotating_anticlockwise_70235_static_side.BP_PA_GE_CrossGear_rotating_anticlockwise_70235_static_side_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3020
}
rows {
  typeId: 1100000000001113
  className: "BP_PA_GE_CrossGear_rotating_clockwise_70235_dynamic_side"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_CrossGear_rotating_clockwise_70235_dynamic_side.BP_PA_GE_CrossGear_rotating_clockwise_70235_dynamic_side_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3020
}
rows {
  typeId: 1100000000001114
  className: "BP_BrokenCubeBase_70235"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/BP_BrokenCubeBase_70235.BP_BrokenCubeBase_70235_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3012
}
rows {
  typeId: 1100000000001115
  className: "BP_PA_GE_MoveBarTop_70235_mid"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/MID/BP_PA_GE_MoveBarTop_70235_mid.BP_PA_GE_MoveBarTop_70235_mid_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3037
}
rows {
  typeId: 1100000000001116
  className: "BP_PA_GE_MoveBarTop_70235_side"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70235/SIDE/BP_PA_GE_MoveBarTop_70235_side.BP_PA_GE_MoveBarTop_70235_side_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3037
}
rows {
  typeId: 1100000000001117
  className: "BP_LevelRule_Actor_60252"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60252/BP_LevelRule_Actor_60252.BP_LevelRule_Actor_60252_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001118
  className: "BP_PA_GE_80022_Conveyor"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_Conveyor.BP_PA_GE_80022_Conveyor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3019
}
rows {
  typeId: 1100000000001119
  className: "BP_PA_GE_80022_Conveyor_006"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_Conveyor_006.BP_PA_GE_80022_Conveyor_006_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3019
}
rows {
  typeId: 1100000000001120
  className: "BP_PA_GE_80022_Conveyor_008"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80022/BP/BP_PA_GE_80022_Conveyor_008.BP_PA_GE_80022_Conveyor_008_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3019
}
rows {
  typeId: 1100000000001121
  className: "BP_PA_GE_Lantern_RoofTop"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/RoofTop/BP_PA_GE_Lantern_RoofTop.BP_PA_GE_Lantern_RoofTop_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001122
  className: "BP_PA_GE_Leaf_60232"
  classPath: "/Game/LetsGo/Assets/Placeables/LevelPrivate/60232/BP/BP_PA_GE_Leaf_60232.BP_PA_GE_Leaf_60232_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001123
  className: "BP_PA_PR_Cake_CanBeReplaced"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70238/BP_PA_PR_Cake_CanBeReplaced.BP_PA_PR_Cake_CanBeReplaced_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100011
}
rows {
  typeId: 1100000000001124
  className: "BP_LevelRule_Actor_IcePrincess"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_IcePrincess.BP_LevelRule_Actor_IcePrincess_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001125
  className: "BP_LevelRule_Actor_Meteorite"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_Meteorite.BP_LevelRule_Actor_Meteorite_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001126
  className: "BP_LevelRule_Actor_RandomProp"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_RandomProp.BP_LevelRule_Actor_RandomProp_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001127
  className: "BP_LevelRule_Actor_Shield"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_Shield.BP_LevelRule_Actor_Shield_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001128
  className: "BP_LevelRule_Actor_SnowBall"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_SnowBall.BP_LevelRule_Actor_SnowBall_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001129
  className: "BP_LevelRule_Actor_Vine"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/ShanDianSai/BP_LevelRule_Actor_Vine.BP_LevelRule_Actor_Vine_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001130
  className: "BP_PA_GE_BrokenBox_Base"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70240/BP_PA_GE_BrokenBox_Base.BP_PA_GE_BrokenBox_Base_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001131
  className: "BP_PA_GE_70241_BrokenBox"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70241/BP/BP_PA_GE_70241_BrokenBox.BP_PA_GE_70241_BrokenBox_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4003
}
rows {
  typeId: 1100000000001132
  className: "BP_PA_GE_60257_RocketBullet"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60257/BP_PA_GE_60257_RocketBullet.BP_PA_GE_60257_RocketBullet_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000001133
  className: "BP_PA_GE_70239_Bullet"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70239/BP/BP_PA_GE_70239_Bullet.BP_PA_GE_70239_Bullet_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001134
  className: "BP_PA_GE_70239_BulletSpawner"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70239/BP/BP_PA_GE_70239_BulletSpawner.BP_PA_GE_70239_BulletSpawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001135
  className: "BP_PA_GE_70239_Debug"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70239/BP/BP_PA_GE_70239_Debug.BP_PA_GE_70239_Debug_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001136
  className: "BP_PA_GE_70239_SkillController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70239/BP/BP_PA_GE_70239_SkillController.BP_PA_GE_70239_SkillController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001137
  className: "BP_PA_GE_70239_ThunderStrike"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70239/BP/BP_PA_GE_70239_ThunderStrike.BP_PA_GE_70239_ThunderStrike_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001138
  className: "BP_PA_GE_70239_Tornado"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70239/BP/BP_PA_GE_70239_Tornado.BP_PA_GE_70239_Tornado_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000469
}
rows {
  typeId: 1100000000001139
  className: "BP_PA_GE_70239_TornadoSkill"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70239/BP/BP_PA_GE_70239_TornadoSkill.BP_PA_GE_70239_TornadoSkill_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001140
  className: "BP_ChangePorpSlot_70240"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70240/BP_ChangePorpSlot_70240.BP_ChangePorpSlot_70240_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001141
  className: "BP_MoeGamePlayActor_70240"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70240/BP_MoeGamePlayActor_70240.BP_MoeGamePlayActor_70240_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001142
  className: "BP_PA_GE_BrokenBox_70240"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70240/BP_PA_GE_BrokenBox_70240.BP_PA_GE_BrokenBox_70240_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000470
}
rows {
  typeId: 1100000000001143
  className: "BP_PA_PR_CurveWeapon_70240"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70240/BP_PA_PR_CurveWeapon_70240.BP_PA_PR_CurveWeapon_70240_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100032
}
rows {
  typeId: 1100000000001144
  className: "BP_PA_PR_ThreeCurveWeapon_70240"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70240/BP_PA_PR_ThreeCurveWeapon_70240.BP_PA_PR_ThreeCurveWeapon_70240_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100032
}
rows {
  typeId: 1100000000001145
  className: "BP_PA_SU_CurveBullet_70240"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70240/BP_PA_SU_CurveBullet_70240.BP_PA_SU_CurveBullet_70240_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000445
}
rows {
  typeId: 1100000000001146
  className: "BP_PA_SU_SecondBombBullet_70240"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70240/BP_PA_SU_SecondBombBullet_70240.BP_PA_SU_SecondBombBullet_70240_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000445
}
rows {
  typeId: 1100000000001147
  className: "BP_PA_SU_SpawnerCurveBullet_70240"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70240/BP_PA_SU_SpawnerCurveBullet_70240.BP_PA_SU_SpawnerCurveBullet_70240_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000445
}
rows {
  typeId: 1100000000001148
  className: "BP_LevelRule_Actor_70241"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70241/BP_LevelRule_Actor_70241.BP_LevelRule_Actor_70241_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001149
  className: "BP_PA_GE_70241_Raft"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70241/BP/BP_PA_GE_70241_Raft.BP_PA_GE_70241_Raft_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3046
}
rows {
  typeId: 1100000000001150
  className: "BP_PA_GE_70241_RaftController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70241/BP/BP_PA_GE_70241_RaftController.BP_PA_GE_70241_RaftController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001151
  className: "BP_PA_GE_70241_SwordMan"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70241/BP/BP_PA_GE_70241_SwordMan.BP_PA_GE_70241_SwordMan_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001152
  className: "BP_PA_GE_70241_SwordWave"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70241/BP/BP_PA_GE_70241_SwordWave.BP_PA_GE_70241_SwordWave_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001153
  className: "BP_PA_GE_BrokenBox_70243"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70243/BP/BP_PA_GE_BrokenBox_70243.BP_PA_GE_BrokenBox_70243_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000478
}
rows {
  typeId: 1100000000001154
  className: "BP_PA_GE_GyroSpawner"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70243/BP/BP_PA_GE_GyroSpawner.BP_PA_GE_GyroSpawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001155
  className: "BP_PA_GE_Gyro_S10"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70243/BP/BP_PA_GE_Gyro_S10.BP_PA_GE_Gyro_S10_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000471
}
rows {
  typeId: 1100000000001156
  className: "BP_LevelRule_Actor_70244"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70244/BP_LevelRule_Actor_70244.BP_LevelRule_Actor_70244_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001157
  className: "BP_PA_GE_80023_MusicCircleHit"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_MusicCircleHit.BP_PA_GE_80023_MusicCircleHit_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000031
}
rows {
  typeId: 1100000000001158
  className: "BP_PA_GE_80023_MusicCircleHit_Small"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_MusicCircleHit_Small.BP_PA_GE_80023_MusicCircleHit_Small_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000031
}
rows {
  typeId: 1100000000001159
  className: "BP_PA_GE_80023_ScoreActor"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_ScoreActor.BP_PA_GE_80023_ScoreActor_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001160
  className: "BP_PA_GE_80023_ScoreActor_Five"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_ScoreActor_Five.BP_PA_GE_80023_ScoreActor_Five_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001161
  className: "BP_PA_GE_80023_ScoreActor_Two"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_ScoreActor_Two.BP_PA_GE_80023_ScoreActor_Two_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001162
  className: "BP_PA_GE_80023_ScoreSpawner"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_ScoreSpawner.BP_PA_GE_80023_ScoreSpawner_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001163
  className: "BP_PA_GE_80023_SpawnerController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_SpawnerController.BP_PA_GE_80023_SpawnerController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001164
  className: "BP_PA_GE_80023_UIController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_UIController.BP_PA_GE_80023_UIController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001165
  className: "BP_PA_GE_80023_WaveSpawnController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_WaveSpawnController.BP_PA_GE_80023_WaveSpawnController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001166
  className: "BP_PA_GE_80023_Dancer"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_Dancer.BP_PA_GE_80023_Dancer_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001167
  className: "BP_PA_GE_BrokenBoxGroup_70243"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70243/BP/BP_PA_GE_BrokenBoxGroup_70243.BP_PA_GE_BrokenBoxGroup_70243_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001168
  className: "BP_PA_GE_Plat_70240"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70240/BP_PA_GE_Plat_70240.BP_PA_GE_Plat_70240_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001169
  className: "BP_PA_GE_WallTrigger"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70243/BP/BP_PA_GE_WallTrigger.BP_PA_GE_WallTrigger_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000001170
  className: "BP_PA_GE_80023_ScoreActor_Three"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80023/BP/BP_PA_GE_80023_ScoreActor_Three.BP_PA_GE_80023_ScoreActor_Three_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001171
  className: "BP_PA_PE_GroundDown"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70243/BP/BP_PA_PE_GroundDown.BP_PA_PE_GroundDown_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001172
  className: "BP_LevelRule_Actor_70246"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70246/BP_LevelRule_Actor_70246.BP_LevelRule_Actor_70246_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001173
  className: "BP_PA_GE_Lantern_RoofTop"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60257/BP_PA_GE_Lantern_RoofTop.BP_PA_GE_Lantern_RoofTop_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001174
  className: "BP_LevelRule_Actor_76108"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/76108/BP_LevelRule_Actor_76108.BP_LevelRule_Actor_76108_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001175
  className: "BP_PA_GE_BulletLauncher_GyroLauncher"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70243/BP/BP_PA_GE_BulletLauncher_GyroLauncher.BP_PA_GE_BulletLauncher_GyroLauncher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3013
}
rows {
  typeId: 1100000000001176
  className: "BP_PA_GE_BrokenBox_70243_ugc"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70243/BP/BP_PA_GE_BrokenBox_70243_ugc.BP_PA_GE_BrokenBox_70243_ugc_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000478
}
rows {
  typeId: 1100000000001177
  className: "BP_LevelRule_Actor_76109"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/76109/BP_LevelRule_Actor_76109.BP_LevelRule_Actor_76109_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001178
  className: "BP_PA_GE_Selector70245"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70245/BP/BP_PA_GE_Selector70245.BP_PA_GE_Selector70245_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001179
  className: "BP_PA_GE_TriggerAttach"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70245/BP/BP_PA_GE_TriggerAttach.BP_PA_GE_TriggerAttach_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001180
  className: "BP_PA_GE_AttachBumper"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70245/BP/BP_PA_GE_AttachBumper.BP_PA_GE_AttachBumper_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001181
  className: "BP_LogicActor_70247_PlatController"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70247/BP_LogicActor_70247_PlatController.BP_LogicActor_70247_PlatController_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001182
  className: "BP_PA_GE_Plat_70247"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70247/BP_PA_GE_Plat_70247.BP_PA_GE_Plat_70247_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001183
  className: "BP_LevelRule_Actor_WerewolfVillage"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/WerewolfVillage/BP_LevelRule_Actor_WerewolfVillage.BP_LevelRule_Actor_WerewolfVillage_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001184
  className: "BP_LevelRule_Actor_60262"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60262/BP_LevelRule_Actor_60262.BP_LevelRule_Actor_60262_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001185
  className: "BP_PA_GE_70248_Bengchuang_D1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Bengchuang_D1.BP_PA_GE_70248_Bengchuang_D1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3056
}
rows {
  typeId: 1100000000001186
  className: "BP_PA_GE_70248_Bengchuang_D2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Bengchuang_D2.BP_PA_GE_70248_Bengchuang_D2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3056
}
rows {
  typeId: 1100000000001187
  className: "BP_PA_GE_70248_Bengchuang_D3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Bengchuang_D3.BP_PA_GE_70248_Bengchuang_D3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3056
}
rows {
  typeId: 1100000000001188
  className: "BP_PA_GE_70248_Bengchuang_D4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Bengchuang_D4.BP_PA_GE_70248_Bengchuang_D4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3056
}
rows {
  typeId: 1100000000001189
  className: "BP_PA_GE_70248_Frog_C1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Frog_C1.BP_PA_GE_70248_Frog_C1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001190
  className: "BP_PA_GE_70248_Frog_C2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Frog_C2.BP_PA_GE_70248_Frog_C2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001191
  className: "BP_PA_GE_70248_Frog_C3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Frog_C3.BP_PA_GE_70248_Frog_C3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001192
  className: "BP_PA_GE_70248_Frog_C4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Frog_C4.BP_PA_GE_70248_Frog_C4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001193
  className: "BP_PA_GE_70248_Plat_A1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Plat_A1.BP_PA_GE_70248_Plat_A1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001194
  className: "BP_PA_GE_70248_Plat_A3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Plat_A3.BP_PA_GE_70248_Plat_A3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001195
  className: "BP_PA_GE_70248_Plat_A4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Plat_A4.BP_PA_GE_70248_Plat_A4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001196
  className: "BP_PA_GE_70248_Plat_B1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Plat_B1.BP_PA_GE_70248_Plat_B1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001197
  className: "BP_PA_GE_70248_Plat_B2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Plat_B2.BP_PA_GE_70248_Plat_B2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001198
  className: "BP_PA_GE_70248_Plat_B3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Plat_B3.BP_PA_GE_70248_Plat_B3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001199
  className: "BP_PA_GE_70248_Plat_B4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Plat_B4.BP_PA_GE_70248_Plat_B4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001200
  className: "BP_PropSpawner_70248"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PropSpawner_70248.BP_PropSpawner_70248_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3067
}
rows {
  typeId: 1100000000001201
  className: "BP_PA_GE_FlowerRoller_S11"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60237/BP/BP_PA_GE_FlowerRoller_S11.BP_PA_GE_FlowerRoller_S11_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001202
  className: "BP_PA_GE_70248_Plat_A2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Plat_A2.BP_PA_GE_70248_Plat_A2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001203
  className: "BP_PA_GE_YuYanJia_70247"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70247/BP_PA_GE_YuYanJia_70247.BP_PA_GE_YuYanJia_70247_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001204
  className: "BP_PA_PR_TimeTravel"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60265/BP/BP_PA_PR_TimeTravel.BP_PA_PR_TimeTravel_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000227
}
rows {
  typeId: 1100000000001205
  className: "BP_PA_GE_Fountain_70245"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70245/BP/BP_PA_GE_Fountain_70245.BP_PA_GE_Fountain_70245_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3076
}
rows {
  typeId: 1100000000001206
  className: "BP_PA_GE_FantasyAdorn_008_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyAdorn_008_A_70249.BP_PA_GE_FantasyAdorn_008_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001207
  className: "BP_PA_GE_FantasyBridge_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyBridge_001_A_70249.BP_PA_GE_FantasyBridge_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001208
  className: "BP_PA_GE_FantasyBuild_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyBuild_001_A_70249.BP_PA_GE_FantasyBuild_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001209
  className: "BP_PA_GE_FantasyBuild_002_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyBuild_002_A_70249.BP_PA_GE_FantasyBuild_002_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001210
  className: "BP_PA_GE_FantasyBuild_003_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyBuild_003_A_70249.BP_PA_GE_FantasyBuild_003_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001211
  className: "BP_PA_GE_FantasyBuild_005_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyBuild_005_A_70249.BP_PA_GE_FantasyBuild_005_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001212
  className: "BP_PA_GE_FantasyBuild_006_B_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyBuild_006_B_70249.BP_PA_GE_FantasyBuild_006_B_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001213
  className: "BP_PA_GE_FantasyBuild_009_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyBuild_009_A_70249.BP_PA_GE_FantasyBuild_009_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001214
  className: "BP_PA_GE_FantasyChair_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyChair_001_A_70249.BP_PA_GE_FantasyChair_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001215
  className: "BP_PA_GE_FantasyDoor_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyDoor_001_A_70249.BP_PA_GE_FantasyDoor_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001216
  className: "BP_PA_GE_FantasyFence_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyFence_001_A_70249.BP_PA_GE_FantasyFence_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001217
  className: "BP_PA_GE_FantasyFence_002_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyFence_002_A_70249.BP_PA_GE_FantasyFence_002_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001218
  className: "BP_PA_GE_FantasyLamp_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyLamp_001_A_70249.BP_PA_GE_FantasyLamp_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001219
  className: "BP_PA_GE_FantasyPillar_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyPillar_001_A_70249.BP_PA_GE_FantasyPillar_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001220
  className: "BP_PA_GE_FantasyPillar_002_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyPillar_002_A_70249.BP_PA_GE_FantasyPillar_002_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001221
  className: "BP_PA_GE_FantasyShrub_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyShrub_001_A_70249.BP_PA_GE_FantasyShrub_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001222
  className: "BP_PA_GE_FantasyTable_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyTable_001_A_70249.BP_PA_GE_FantasyTable_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001223
  className: "BP_PA_GE_Fantasytree_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_Fantasytree_001_A_70249.BP_PA_GE_Fantasytree_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001224
  className: "BP_PA_GE_HideGrass_005_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_HideGrass_005_A_70249.BP_PA_GE_HideGrass_005_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3030
}
rows {
  typeId: 1100000000001225
  className: "BP_PA_GE_MagicalGroundCorner_002_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_MagicalGroundCorner_002_A_70249.BP_PA_GE_MagicalGroundCorner_002_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001226
  className: "BP_PA_GE_Stone_Wall_001_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_Stone_Wall_001_70249.BP_PA_GE_Stone_Wall_001_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001227
  className: "BP_PA_GE_TreeHole_002_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_TreeHole_002_A_70249.BP_PA_GE_TreeHole_002_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001228
  className: "BP_PA_GE_WoodBoxA_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_WoodBoxA_001_A_70249.BP_PA_GE_WoodBoxA_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001229
  className: "BP_PA_GR_RotatingCylinder_Base_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GR_RotatingCylinder_Base_70249.BP_PA_GR_RotatingCylinder_Base_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001230
  className: "BP_PA_GE_SquarePlat_60258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60258/BP/BP_PA_GE_SquarePlat_60258.BP_PA_GE_SquarePlat_60258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3038
}
rows {
  typeId: 1100000000001231
  className: "BP_PA_GE_ToxicPot_60261"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60261/BP_PA_GE_ToxicPot_60261.BP_PA_GE_ToxicPot_60261_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001232
  className: "BP_PA_GR_TE_ToxicWater_60261"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60261/BP_PA_GR_TE_ToxicWater_60261.BP_PA_GR_TE_ToxicWater_60261_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000001233
  className: "BP_PA_GE_DreamGrassland_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_DreamGrassland_001_A_70249.BP_PA_GE_DreamGrassland_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001234
  className: "BP_PA_GE_TipController_60258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60258/BP/BP_PA_GE_TipController_60258.BP_PA_GE_TipController_60258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001235
  className: "BP_PA_GE_70248_Youling_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70248/BP/BP_PA_GE_70248_Youling_001_A.BP_PA_GE_70248_Youling_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001236
  className: "BP_PA_GE_BfArYYJNFenceBase_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_BfArYYJNFenceBase_001_A_70249.BP_PA_GE_BfArYYJNFenceBase_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001237
  className: "BP_PA_GE_Conveyor_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_Conveyor_001_A_70249.BP_PA_GE_Conveyor_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3019
}
rows {
  typeId: 1100000000001238
  className: "BP_PA_GE_Conveyor_002_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_Conveyor_002_A_70249.BP_PA_GE_Conveyor_002_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3019
}
rows {
  typeId: 1100000000001239
  className: "BP_PA_GE_FantasyAdorn_002_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyAdorn_002_A_70249.BP_PA_GE_FantasyAdorn_002_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001240
  className: "BP_PA_GE_FantasyAdorn_003_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyAdorn_003_A_70249.BP_PA_GE_FantasyAdorn_003_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001241
  className: "BP_PA_GE_FantasyAdorn_004_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyAdorn_004_A_70249.BP_PA_GE_FantasyAdorn_004_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001242
  className: "BP_PA_GE_FantasyAdorn_006_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyAdorn_006_A_70249.BP_PA_GE_FantasyAdorn_006_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001243
  className: "BP_PA_GE_FantasyAdorn_010_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyAdorn_010_70249.BP_PA_GE_FantasyAdorn_010_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001244
  className: "BP_PA_GE_FantasyEdge_Bend10M_Scale_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyEdge_Bend10M_Scale_001_A_70249.BP_PA_GE_FantasyEdge_Bend10M_Scale_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001245
  className: "BP_PA_GE_FantasyEdge_Straight10M_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyEdge_Straight10M_001_A_70249.BP_PA_GE_FantasyEdge_Straight10M_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001246
  className: "BP_PA_GE_FantasyPlatform_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyPlatform_001_A_70249.BP_PA_GE_FantasyPlatform_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001247
  className: "BP_PA_GE_FantasyPot_001_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_FantasyPot_001_A_70249.BP_PA_GE_FantasyPot_001_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001248
  className: "BP_PA_GE_MagicalTimeprops_005_A_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_MagicalTimeprops_005_A_70249.BP_PA_GE_MagicalTimeprops_005_A_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001249
  className: "BP_PA_GE_CylinderVenom_70249"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70249/BP/BP_PA_GE_CylinderVenom_70249.BP_PA_GE_CylinderVenom_70249_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001250
  className: "BP_PA_GE_BookShelf_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_BookShelf_80024.BP_PA_GE_BookShelf_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001251
  className: "BP_PA_GE_Books_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_Books_80024.BP_PA_GE_Books_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001252
  className: "BP_PA_GE_Cloud_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_Cloud_80024.BP_PA_GE_Cloud_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000467
}
rows {
  typeId: 1100000000001253
  className: "BP_PA_GE_LevelRuler_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_LevelRuler_80024.BP_PA_GE_LevelRuler_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001254
  className: "BP_PA_GE_Platform_80024_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_Platform_80024_01.BP_PA_GE_Platform_80024_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001255
  className: "BP_PA_GE_Platform_80024_02"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_Platform_80024_02.BP_PA_GE_Platform_80024_02_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001256
  className: "BP_PA_GE_PortaL80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_PortaL80024.BP_PA_GE_PortaL80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3042
}
rows {
  typeId: 1100000000001257
  className: "BP_PA_GE_PortalTargetPoint_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_PortalTargetPoint_80024.BP_PA_GE_PortalTargetPoint_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3087
}
rows {
  typeId: 1100000000001258
  className: "BP_PA_GE_TrophyShelf_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_TrophyShelf_80024.BP_PA_GE_TrophyShelf_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001259
  className: "BP_PA_GE_Window_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_Window_80024.BP_PA_GE_Window_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001260
  className: "BP_LevelRule_Actor_70250"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70250/BP/BP_LevelRule_Actor_70250.BP_LevelRule_Actor_70250_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001261
  className: "BP_AG_70250Skill3_DamageArea"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70250/Skill/Ability_Skill3/BP/BP_AG_70250Skill3_DamageArea.BP_AG_70250Skill3_DamageArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000486
}
rows {
  typeId: 1100000000001262
  className: "BP_PA_GE_70252_Forniture_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/Temp/BP_PA_GE_70252_Forniture_01.BP_PA_GE_70252_Forniture_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001263
  className: "BP_PA_GE_70252_Forniture_02"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/Temp/BP_PA_GE_70252_Forniture_02.BP_PA_GE_70252_Forniture_02_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001264
  className: "BP_PA_GE_70252_Forniture_03"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/Temp/BP_PA_GE_70252_Forniture_03.BP_PA_GE_70252_Forniture_03_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001265
  className: "BP_PA_GE_70252_Forniture_04"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/Temp/BP_PA_GE_70252_Forniture_04.BP_PA_GE_70252_Forniture_04_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001266
  className: "BP_PA_GE_70252_Forniture_05"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/Temp/BP_PA_GE_70252_Forniture_05.BP_PA_GE_70252_Forniture_05_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001267
  className: "BP_PA_GE_70252_Forniture_Base"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/Temp/BP_PA_GE_70252_Forniture_Base.BP_PA_GE_70252_Forniture_Base_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001268
  className: "BP_PA_PR_Forniture_70252"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/Temp/BP_PA_PR_Forniture_70252.BP_PA_PR_Forniture_70252_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000236
}
rows {
  typeId: 1100000000001269
  className: "BP_PA_GE_OuterWall_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_OuterWall_80024.BP_PA_GE_OuterWall_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001270
  className: "BP_PA_GE_Platform_80024_03"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_Platform_80024_03.BP_PA_GE_Platform_80024_03_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001271
  className: "BP_PA_GE_Stair_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_Stair_80024.BP_PA_GE_Stair_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001272
  className: "BP_PA_PR_70250Skill2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70250/BP/BP_PA_PR_70250Skill2.BP_PA_PR_70250Skill2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001273
  className: "BP_PA_PR_70250Skill3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70250/BP/BP_PA_PR_70250Skill3.BP_PA_PR_70250Skill3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001274
  className: "BP_PA_PR_70250Skill4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70250/BP/BP_PA_PR_70250Skill4.BP_PA_PR_70250Skill4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001275
  className: "BP_PA_GE_LevelRule_70252"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/BP/BP_PA_GE_LevelRule_70252.BP_PA_GE_LevelRule_70252_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001276
  className: "BP_LevelRule_Actor_70254"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70254/BP/BP_LevelRule_Actor_70254.BP_LevelRule_Actor_70254_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001277
  className: "BP_LevelRule_Actor_70254Sup"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70254/BP/BP_LevelRule_Actor_70254Sup.BP_LevelRule_Actor_70254Sup_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001278
  className: "BP_GA_GE_TimeDoor_70255"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70255/BP/BP_GA_GE_TimeDoor_70255.BP_GA_GE_TimeDoor_70255_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001279
  className: "BP_PA_GE_GameManager_70255"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70255/BP/BP_PA_GE_GameManager_70255.BP_PA_GE_GameManager_70255_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001280
  className: "BP_PA_GE_HookPortal_70255"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70255/BP/BP_PA_GE_HookPortal_70255.BP_PA_GE_HookPortal_70255_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001281
  className: "BP_PA_GE_SpawnPoint_70255"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70255/BP/BP_PA_GE_SpawnPoint_70255.BP_PA_GE_SpawnPoint_70255_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001282
  className: "BP_Spawner70255"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70255/BP/BP_Spawner70255.BP_Spawner70255_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001283
  className: "BP_TimeCapsule_70255_1s"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70255/BP/BP_TimeCapsule_70255_1s.BP_TimeCapsule_70255_1s_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000227
}
rows {
  typeId: 1100000000001284
  className: "BP_TimeCapsule_70255_2s"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70255/BP/BP_TimeCapsule_70255_2s.BP_TimeCapsule_70255_2s_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000227
}
rows {
  typeId: 1100000000001285
  className: "BP_PA_GE_ScoreA_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_ScoreA_80024.BP_PA_GE_ScoreA_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001286
  className: "BP_PA_GE_ScoreB_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_ScoreB_80024.BP_PA_GE_ScoreB_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001287
  className: "BP_PA_PR_70250Skill1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70250/BP/BP_PA_PR_70250Skill1.BP_PA_PR_70250Skill1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001288
  className: "BP_PA_PR_70254Skill1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70254/BP/BP_PA_PR_70254Skill1.BP_PA_PR_70254Skill1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001289
  className: "BP_PA_PR_70254Skill2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70254/BP/BP_PA_PR_70254Skill2.BP_PA_PR_70254Skill2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001290
  className: "BP_PA_GE_ScoreC_80024"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_ScoreC_80024.BP_PA_GE_ScoreC_80024_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001291
  className: "BP_PA_GE_Stair_70252"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/BP/BP_PA_GE_Stair_70252.BP_PA_GE_Stair_70252_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001292
  className: "BP_PA_GE_MagicalBooks_70254_A_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70254/BP/BP_PA_GE_MagicalBooks_70254_A_01.BP_PA_GE_MagicalBooks_70254_A_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001293
  className: "BP_PA_GE_MagicalBooks_70254_A_02"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70254/BP/BP_PA_GE_MagicalBooks_70254_A_02.BP_PA_GE_MagicalBooks_70254_A_02_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001294
  className: "BP_PA_GE_Shelf_80025"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_Shelf_80025.BP_PA_GE_Shelf_80025_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001295
  className: "BP_PA_DE_SchoolEdge_Straight10M_70252"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/BP/BP_PA_DE_SchoolEdge_Straight10M_70252.BP_PA_DE_SchoolEdge_Straight10M_70252_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001296
  className: "BP_PA_GR_Water_70252"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70252/BP/BP_PA_GR_Water_70252.BP_PA_GR_Water_70252_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000001297
  className: "BP_PA_GE_BookShelf_80024_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_BookShelf_80024_2.BP_PA_GE_BookShelf_80024_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001298
  className: "BP_PA_PR_70254Skill_Flash"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70254/BP/BP_PA_PR_70254Skill_Flash.BP_PA_PR_70254Skill_Flash_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001299
  className: "BP_PA_PR_70254Skill3"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70254/BP/BP_PA_PR_70254Skill3.BP_PA_PR_70254Skill3_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001300
  className: "BP_PA_GE_BookShelf_80025_2"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/80024/BP/BP_PA_GE_BookShelf_80025_2.BP_PA_GE_BookShelf_80025_2_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001301
  className: "BP_PA_PR_70254Skill4"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70254/BP/BP_PA_PR_70254Skill4.BP_PA_PR_70254Skill4_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001302
  className: "BP_PA_GE_60269GameManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_60269GameManager.BP_PA_GE_60269GameManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000247
}
rows {
  typeId: 1100000000001303
  className: "BP_PA_GE_ColorMatch_Base"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Base.BP_PA_GE_ColorMatch_Base_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001304
  className: "BP_PA_GE_ColorMatch_Boost"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Boost.BP_PA_GE_ColorMatch_Boost_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000497
}
rows {
  typeId: 1100000000001305
  className: "BP_PA_GE_ColorMatch_Boost_Blue1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Boost_Blue1.BP_PA_GE_ColorMatch_Boost_Blue1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000497
}
rows {
  typeId: 1100000000001306
  className: "BP_PA_GE_ColorMatch_Boost_red"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Boost_red.BP_PA_GE_ColorMatch_Boost_red_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000497
}
rows {
  typeId: 1100000000001307
  className: "BP_PA_GE_ColorMatch_Bounce"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Bounce.BP_PA_GE_ColorMatch_Bounce_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000497
}
rows {
  typeId: 1100000000001308
  className: "BP_PA_GE_ColorMatch_Bounce_Green"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Bounce_Green.BP_PA_GE_ColorMatch_Bounce_Green_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000497
}
rows {
  typeId: 1100000000001309
  className: "BP_PA_GE_ColorMatch_Bounce_Red"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Bounce_Red.BP_PA_GE_ColorMatch_Bounce_Red_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000497
}
rows {
  typeId: 1100000000001310
  className: "BP_PA_GE_60270_LauncherPillar_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60270/BP/BP_PA_GE_60270_LauncherPillar_01.BP_PA_GE_60270_LauncherPillar_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001311
  className: "BP_PA_GE_60270_LauncherPillar_02"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60270/BP/BP_PA_GE_60270_LauncherPillar_02.BP_PA_GE_60270_LauncherPillar_02_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001312
  className: "BP_PA_GE_60270_LauncherPillar_03"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60270/BP/BP_PA_GE_60270_LauncherPillar_03.BP_PA_GE_60270_LauncherPillar_03_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001313
  className: "BP_PA_GE_60270_LauncherPillar_04"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60270/BP/BP_PA_GE_60270_LauncherPillar_04.BP_PA_GE_60270_LauncherPillar_04_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001314
  className: "BP_PA_GE_60270_Stone_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60270/BP/BP_PA_GE_60270_Stone_01.BP_PA_GE_60270_Stone_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001315
  className: "BP_PA_GE_YuYanJia_70256"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70256/BP/BP_PA_GE_YuYanJia_70256.BP_PA_GE_YuYanJia_70256_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001316
  className: "BP_PA_IT_BombPlusBumper_70256"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70256/BP/BP_PA_IT_BombPlusBumper_70256.BP_PA_IT_BombPlusBumper_70256_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000001317
  className: "BP_PA_IT_BombPlusFire_70256"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70256/BP/BP_PA_IT_BombPlusFire_70256.BP_PA_IT_BombPlusFire_70256_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000001318
  className: "BP_PA_IT_BombPlusFrozen_70256"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70256/BP/BP_PA_IT_BombPlusFrozen_70256.BP_PA_IT_BombPlusFrozen_70256_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000001319
  className: "BP_LevelRule_Actor_70257"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70257/BP/BP_LevelRule_Actor_70257.BP_LevelRule_Actor_70257_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001320
  className: "BP_PA_GE_70257_MonsterHider"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70257/BP/BP_PA_GE_70257_MonsterHider.BP_PA_GE_70257_MonsterHider_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001321
  className: "BP_PA_GE_70257_Platform_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70257/BP/BP_PA_GE_70257_Platform_01.BP_PA_GE_70257_Platform_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001322
  className: "BP_PA_GE_70257_Platform_02"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70257/BP/BP_PA_GE_70257_Platform_02.BP_PA_GE_70257_Platform_02_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001323
  className: "BP_PA_GE_70257_Plot_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70257/BP/BP_PA_GE_70257_Plot_01.BP_PA_GE_70257_Plot_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001324
  className: "BP_PA_BulletLauncherManager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_BulletLauncherManager.BP_PA_BulletLauncherManager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000001325
  className: "BP_PA_GE_70258_Barbette_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_GE_70258_Barbette_01.BP_PA_GE_70258_Barbette_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001326
  className: "BP_PA_GE_BulletLauncherPickable_70258_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_GE_BulletLauncherPickable_70258_01.BP_PA_GE_BulletLauncherPickable_70258_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3013
}
rows {
  typeId: 1100000000001327
  className: "BP_PA_GE_BulletLauncherPickable_70258_02"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_GE_BulletLauncherPickable_70258_02.BP_PA_GE_BulletLauncherPickable_70258_02_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3013
}
rows {
  typeId: 1100000000001328
  className: "BP_PA_GE_BulletLauncherPickable_70258_03"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_GE_BulletLauncherPickable_70258_03.BP_PA_GE_BulletLauncherPickable_70258_03_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3013
}
rows {
  typeId: 1100000000001329
  className: "BP_PA_GE_FantasyFence_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_GE_FantasyFence_70258.BP_PA_GE_FantasyFence_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001330
  className: "BP_PA_GE_Pillar_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_GE_Pillar_70258.BP_PA_GE_Pillar_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001331
  className: "BP_PA_GR_Floor_70258_001_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_GR_Floor_70258_001_A.BP_PA_GR_Floor_70258_001_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000001332
  className: "BP_BulletLauncherBomb_test"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/Test/BP_BulletLauncherBomb_test.BP_BulletLauncherBomb_test_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000001333
  className: "BP_PA_Bumper_Test"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/Test/BP_PA_Bumper_Test.BP_PA_Bumper_Test_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3014
}
rows {
  typeId: 1100000000001334
  className: "BP_PA_Bumper_Test_1"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/Test/BP_PA_Bumper_Test_1.BP_PA_Bumper_Test_1_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001335
  className: "BP_PA_IT_BombPlusBumper_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/Test/BP_PA_IT_BombPlusBumper_70258.BP_PA_IT_BombPlusBumper_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000001336
  className: "BP_PA_IT_BombPlusFire_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/Test/BP_PA_IT_BombPlusFire_70258.BP_PA_IT_BombPlusFire_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000001337
  className: "BP_PA_IT_BombPlusFrozen_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/Test/BP_PA_IT_BombPlusFrozen_70258.BP_PA_IT_BombPlusFrozen_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1001
}
rows {
  typeId: 1100000000001338
  className: "BP_PA_GE_FireDamage_70259"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70259/BP/BP_PA_GE_FireDamage_70259.BP_PA_GE_FireDamage_70259_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001339
  className: "BP_PA_GE_GameManager_70259"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70259/BP/BP_PA_GE_GameManager_70259.BP_PA_GE_GameManager_70259_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001340
  className: "BP_PA_GE_GoldRing_70259"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70259/BP/BP_PA_GE_GoldRing_70259.BP_PA_GE_GoldRing_70259_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001341
  className: "BP_PA_GE_RingSpawnManager_70259"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70259/BP/BP_PA_GE_RingSpawnManager_70259.BP_PA_GE_RingSpawnManager_70259_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001342
  className: "BP_PA_GE_SpawnPoint_70259"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70259/BP/BP_PA_GE_SpawnPoint_70259.BP_PA_GE_SpawnPoint_70259_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001343
  className: "BP_PA_GE_ColorMatch_Boost_001"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Boost_001.BP_PA_GE_ColorMatch_Boost_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000497
}
rows {
  typeId: 1100000000001344
  className: "BP_PA_GE_ColorMatch_Boost_002"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Boost_002.BP_PA_GE_ColorMatch_Boost_002_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000497
}
rows {
  typeId: 1100000000001345
  className: "BP_PA_GE_ColorMatch_Bounce_001"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Bounce_001.BP_PA_GE_ColorMatch_Bounce_001_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000497
}
rows {
  typeId: 1100000000001346
  className: "BP_PA_GE_ColorMatch_Bounce_002"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_ColorMatch_Bounce_002.BP_PA_GE_ColorMatch_Bounce_002_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000497
}
rows {
  typeId: 1100000000001347
  className: "BP_PA_GE_ShockWave"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_GE_ShockWave.BP_PA_GE_ShockWave_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001348
  className: "BP_PA_GE_60269Manager"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA_GE_60269Manager.BP_PA_GE_60269Manager_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001349
  className: "BP_PA__GE_CleanColorMatch"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60269/BP_PA__GE_CleanColorMatch.BP_PA__GE_CleanColorMatch_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}
rows {
  typeId: 1100000000001350
  className: "BP_ChangePorpSlot_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_ChangePorpSlot_70258.BP_ChangePorpSlot_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 5002
}
rows {
  typeId: 1100000000001351
  className: "BP_PA_PR_CurveWeapon_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_PR_CurveWeapon_70258.BP_PA_PR_CurveWeapon_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100033
}
rows {
  typeId: 1100000000001352
  className: "BP_PA_PR_ThreeCurveWeapon_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_PR_ThreeCurveWeapon_70258.BP_PA_PR_ThreeCurveWeapon_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 100033
}
rows {
  typeId: 1100000000001353
  className: "BP_PA_GE_70261_Bullet_A"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70261/BP/BP_PA_GE_70261_Bullet_A.BP_PA_GE_70261_Bullet_A_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001354
  className: "BP_PA_GE_70261_Bullet_B"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70261/BP/BP_PA_GE_70261_Bullet_B.BP_PA_GE_70261_Bullet_B_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001355
  className: "BP_PA_GE_70261_DaPao"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70261/BP/BP_PA_GE_70261_DaPao.BP_PA_GE_70261_DaPao_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001356
  className: "BP_PA_GE_70261_GameRuler"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70261/BP/BP_PA_GE_70261_GameRuler.BP_PA_GE_70261_GameRuler_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001357
  className: "BP_PA_PR_70261_Launcher"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70261/BP/BP_PA_PR_70261_Launcher.BP_PA_PR_70261_Launcher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001358
  className: "BP_PA_PR_70261_SpecialLauncher"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70261/BP/BP_PA_PR_70261_SpecialLauncher.BP_PA_PR_70261_SpecialLauncher_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000460
}
rows {
  typeId: 1100000000001359
  className: "BP_PA_IT_Box_002_A_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_IT_Box_002_A_70258.BP_PA_IT_Box_002_A_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 4003
}
rows {
  typeId: 1100000000001360
  className: "BP_PA_SU_FireworkBullet_01B_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_SU_FireworkBullet_01B_70258.BP_PA_SU_FireworkBullet_01B_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000445
}
rows {
  typeId: 1100000000001361
  className: "BP_PA_SU_FireworkBullet_70258"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70258/BP/BP_PA_SU_FireworkBullet_70258.BP_PA_SU_FireworkBullet_70258_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000445
}
rows {
  typeId: 1100000000001362
  className: "BP_PA_GE_70261_Track"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70261/BP/BP_PA_GE_70261_Track.BP_PA_GE_70261_Track_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001363
  className: "BP_PA_GE_70263_GlassPlat_01"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70263/BP/BP_PA_GE_70263_GlassPlat_01.BP_PA_GE_70263_GlassPlat_01_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001364
  className: "BP_PA_GE_70263_GlassPlat_02"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70263/BP/BP_PA_GE_70263_GlassPlat_02.BP_PA_GE_70263_GlassPlat_02_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001365
  className: "BP_PA_GE_70263_PlatFrame"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70263/BP/BP_PA_GE_70263_PlatFrame.BP_PA_GE_70263_PlatFrame_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 3090
}
rows {
  typeId: 1100000000001366
  className: "BP_PA_GE_NoteArea"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60275/BP/BP_PA_GE_NoteArea.BP_PA_GE_NoteArea_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1004
}
rows {
  typeId: 1100000000001367
  className: "BP_PA_GE_NotePlot_60275"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/60275/BP/BP_PA_GE_NotePlot_60275.BP_PA_GE_NotePlot_60275_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000501
}
rows {
  typeId: 1100000000001368
  className: "BP_PA_DE_Glass_70256"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70256/BP/BP_PA_DE_Glass_70256.BP_PA_DE_Glass_70256_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1005
}
rows {
  typeId: 1100000000001369
  className: "BP_PA_GE_Ghoost_70263"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70263/BP/BP_PA_GE_Ghoost_70263.BP_PA_GE_Ghoost_70263_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000502
}
rows {
  typeId: 1100000000001370
  className: "BP_PA_GE_Ghost_Base"
  classPath: "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70263/BP/BP_PA_GE_Ghost_Base.BP_PA_GE_Ghost_Base_C"
  protoName: "PlaceablesExportInfo"
  subTypeID: 1000126
}

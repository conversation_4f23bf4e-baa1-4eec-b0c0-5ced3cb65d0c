com.tencent.wea.xlsRes.table_SfxAudioConfig
excel/xls/Y_音频之音效.xlsx sheet:音效
rows {
  id: 96134
  playEvent: "Play_LetsMOBA_Hero_LuBan_SFX_Attack_01_Hit_XKSCJ_1P"
  playEvent3p: "Play_LetsMOBA_Hero_LuBan_SFX_Attack_01_Hit_XKSCJ_3P"
  bank: "LetsMOBA_Hero_LuBan_SFX"
  is3d: 1
}
rows {
  id: 96135
  playEvent: "Play_LetsMOBA_Hero_LuBan_SFX_Attack_02_Hit_XKSCJ_1P"
  playEvent3p: "Play_LetsMOBA_Hero_LuBan_SFX_Attack_02_Hit_XKSCJ_3P"
  bank: "LetsMOBA_Hero_LuBan_SFX"
  is3d: 1
}
rows {
  id: 96136
  playEvent: "Play_LetsMOBA_Hero_LuBan_SFX_Attack_03_Hit_XKSCJ_1P"
  playEvent3p: "Play_LetsMOBA_Hero_LuBan_SFX_Attack_03_Hit_XKSCJ_3P"
  bank: "LetsMOBA_Hero_LuBan_SFX"
  is3d: 1
}
rows {
  id: 96137
  playEvent: "Play_LetsMOBA_Hero_LuBan_SFX_SkillA_Hit_XKSCJ_1P"
  playEvent3p: "Play_LetsMOBA_Hero_LuBan_SFX_SkillA_Hit_XKSCJ_3P"
  bank: "LetsMOBA_Hero_LuBan_SFX"
  is3d: 1
}
rows {
  id: 96138
  playEvent: "Play_LetsMOBA_Hero_LuBan_SFX_SkillB_Hit_XKSCJ_1P"
  playEvent3p: "Play_LetsMOBA_Hero_LuBan_SFX_SkillB_Hit_XKSCJ_3P"
  bank: "LetsMOBA_Hero_LuBan_SFX"
  is3d: 1
}
rows {
  id: 96139
  playEvent: "Play_LetsMOBA_Hero_LuBan_SFX_SkillC_Hit_XKSCJ_1P"
  playEvent3p: "Play_LetsMOBA_Hero_LuBan_SFX_SkillC_Hit_XKSCJ_3P"
  bank: "LetsMOBA_Hero_LuBan_SFX"
  is3d: 1
}
rows {
  id: 96140
  playEvent: "Play_LetsMOBA_SFX_RandomEvent_FastRun"
  bank: "LetsMOBA_Hero_Common_SFX"
}
rows {
  id: 96141
  playEvent: "Stop_LetsMOBA_SFX_RandomEvent_FastRun"
  bank: "LetsMOBA_Hero_Common_SFX"
}
rows {
  id: 96142
  playEvent: "Play_LetsMOBA_SFX_RandomEvent_YelloEffect_Start"
  bank: "LetsMOBA_Hero_Common_SFX"
}
rows {
  id: 96143
  playEvent: "Play_LetsMOBA_SFX_RandomEvent_YelloEffect_End"
  bank: "LetsMOBA_Hero_Common_SFX"
}
rows {
  id: 96144
  playEvent: "Play_LetsMOBA_SFX_RandomEvent_KestrelDead"
  bank: "LetsMOBA_Hero_Common_SFX"
  is3d: 1
}
rows {
  id: 96145
  playEvent: "Play_LetsMOBA_SFX_RandomEvent_Spirit_Dead"
  bank: "LetsMOBA_Hero_Common_SFX"
  is3d: 1
}
rows {
  id: 96146
  playEvent: "Play_LetsMOBA_SFX_RandomEvent_Spirit_Born"
  bank: "LetsMOBA_Hero_Common_SFX"
  is3d: 1
}
rows {
  id: 96147
  playEvent: "Play_LetsMOBA_Monster_RedBuff_Birth"
  playEvent3p: "Play_LetsMOBA_Monster_RedBuff_Birth_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96148
  playEvent: "Play_LetsMOBA_Monster_RedBuff_Death"
  playEvent3p: "Play_LetsMOBA_Monster_RedBuff_Death_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96149
  playEvent: "Play_LetsMOBA_Monster_BlueBuff_Birth"
  playEvent3p: "Play_LetsMOBA_Monster_BlueBuff_Birth_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96182
  playEvent: "Play_LetsMOBA_Monster_BlueBuff_Death"
  playEvent3p: "Play_LetsMOBA_Monster_BlueBuff_Death_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96183
  playEvent: "Play_LetsMOBA_Monster_Lizard_Birth"
  playEvent3p: "Play_LetsMOBA_Monster_Lizard_Birth_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96184
  playEvent: "Play_LetsMOBA_Monster_Lizard_Death"
  playEvent3p: "Play_LetsMOBA_Monster_Lizard_Death_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96185
  playEvent: "Play_LetsMOBA_Monster_ShanHao_Birth"
  playEvent3p: "Play_LetsMOBA_Monster_ShanHao_Birth_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96154
  playEvent: "Play_LetsMOBA_Monster_ShanHao_Death"
  playEvent3p: "Play_LetsMOBA_Monster_ShanHao_Death_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96155
  playEvent: "Play_LetsMOBA_Tower_Crystal_Loop"
  playEvent3p: "Play_LetsMOBA_Tower_Crystal_Loop_3P"
  bank: "LetsMOBA_Tower"
  stopEvent: "Stop_LetsMOBA_Tower_Crystal_Loop"
  stopEvent3p: "Stop_LetsMOBA_Tower_Crystal_Loop_3P"
  is3d: 1
  isLoop: 1
}
rows {
  id: 96156
  playEvent: "Play_LetsMOBA_Tower_Coins"
  playEvent3p: "Play_LetsMOBA_Tower_Coins_3P"
  bank: "LetsMOBA_Tower"
  is3d: 1
}
rows {
  id: 96157
  playEvent: "Play_LetsMOBA_Tower_Crystal_Attack"
  playEvent3p: "Play_LetsMOBA_Tower_Crystal_Attack_3P"
  bank: "LetsMOBA_Tower"
  is3d: 1
}
rows {
  id: 96158
  playEvent: "Play_LetsMOBA_Tower_Crystal_Attack_Hit"
  playEvent3p: "Play_LetsMOBA_Tower_Crystal_Attack_Hit_3P"
  bank: "LetsMOBA_Tower"
  is3d: 1
}
rows {
  id: 96159
  playEvent: "Play_LetsMOBA_Tower_Transform"
  playEvent3p: "Play_LetsMOBA_Tower_Transform_3P"
  bank: "LetsMOBA_Tower"
}
rows {
  id: 96160
  playEvent: "Play_LetsMOBA_Tower_Crystal_Transform"
  playEvent3p: "Play_LetsMOBA_Tower_Crystal_Transform_3P"
  bank: "LetsMOBA_Tower"
  is3d: 1
}
rows {
  id: 96161
  playEvent: "Play_LetsMOBA_Ambience_Windy_Loop"
  bank: "LetsMOBA_Ambience"
  stopEvent: "Stop_LetsMOBA_Ambience_Windy_Loop"
  is3d: 1
  isLoop: 1
}
rows {
  id: 96162
  playEvent: "Play_LetsMOBA_RandomEvent_TianJiangBaoPai"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96163
  playEvent: "Play_LetsMOBA_RandomEvent_BountyHunter_01"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96164
  playEvent: "Play_LetsMOBA_RandomEvent_BountyHunter_02"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96165
  playEvent: "Play_LetsMOBA_RandomEvent_YouPaoYouTiao_01"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96166
  playEvent: "Play_LetsMOBA_RandomEvent_YouPaoYouTiao_02"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96167
  playEvent: "Play_LetsMOBA_RandomEvent_BeiShuiYiZhan_01"
  bank: "LetsMOBA_UIHUD"
  is3d: 1
}
rows {
  id: 96168
  playEvent: "Play_LetsMOBA_RandomEvent_BeiShuiYiZhan_02"
  bank: "LetsMOBA_UIHUD"
  is3d: 1
}
rows {
  id: 96169
  playEvent: "Play_LetsMOBA_RandomEvent_YingXiongJueXing_01"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96170
  playEvent: "Play_LetsMOBA_RandomEvent_YingXiongJueXing_02"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96171
  playEvent: "Play_LetsMOBA_HUD_ChiJi_Victory"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96172
  playEvent: "Play_LetsMOBA_GPO_Destructible_Ice"
  playEvent3p: "Play_LetsMOBA_GPO_Destructible_Ice_3P"
  bank: "LetsMOBA_OBJ"
  is3d: 1
}
rows {
  id: 96173
  playEvent: "Play_LetsMOBA_GPO_Hit_Ice_OBJ"
  playEvent3p: "Play_LetsMOBA_GPO_Hit_Ice_OBJ_3P"
  bank: "LetsMOBA_OBJ"
  is3d: 1
}
rows {
  id: 96174
  playEvent: "Play_LetsMOBA_Blow_Attack"
  playEvent3p: "Play_LetsMOBA_Blow_Attack_3P"
  bank: "LetsMOBA_OBJ"
  is3d: 1
}
rows {
  id: 96175
  playEvent: "Play_LetsMOBA_Blow_Hit"
  playEvent3p: "Play_LetsMOBA_Blow_Hit_3P"
  bank: "LetsMOBA_OBJ"
  is3d: 1
}
rows {
  id: 96176
  playEvent: "Play_LetsMOBA_OBJ_GetCoins"
  playEvent3p: "Play_LetsMOBA_OBJ_GetCoins_3P"
  bank: "LetsMOBA_OBJ"
  is3d: 1
}
rows {
  id: 96177
  playEvent: "Play_LetsMOBA_HeroUpgrades"
  playEvent3p: "Play_LetsMOBA_HeroUpgrades_3P"
  bank: "LetsMOBA_OBJ"
  is3d: 1
}
rows {
  id: 96178
  playEvent: "Play_LetsMOBA_Ambience_Snow_Loop"
  bank: "LetsMOBA_Ambience"
  stopEvent: "Stop_LetsMOBA_Ambience_Snow_Loop"
  isLoop: 1
}
rows {
  id: 96179
  playEvent: "Play_LetsMOBA_HUD_Portal_Chanting_BackHome"
  bank: "LetsMOBA_UIHUD"
  stopEvent: "Stop_LetsMOBA_HUD_Portal_Chanting_BackHome"
  is3d: 1
}
rows {
  id: 96180
  playEvent: "Play_LetsMOBA_HUD_Portal_Landed_BackHome"
  bank: "LetsMOBA_UIHUD"
  is3d: 1
}
rows {
  id: 96181
  playEvent: "Play_LetsMOBA_HUD_HeroArrive"
  playEvent3p: "Play_LetsMOBA_HUD_HeroArrive_3P"
  bank: "LetsMOBA_UIHUD"
  is3d: 1
}
rows {
  id: 96186
  playEvent: "Play_LetsMOBA_HUD_PeaceEquipmentProgress"
  bank: "LetsMOBA_UIHUD"
  stopEvent: "Stop_LetsMOBA_HUD_PeaceEquipmentProgress"
}
rows {
  id: 96187
  playEvent: "Play_LetsMOBA_HUD_PeaceEquipmentGet"
  bank: "LetsMOBA_UIHUD"
  stopEvent: "Stop_LetsMOBA_HUD_PeaceEquipmentGet"
}
rows {
  id: 96188
  playEvent: "Play_LetsMOBA_HUD_PeaceEquipmentRemove"
  bank: "LetsMOBA_UIHUD"
  stopEvent: "Stop_LetsMOBA_HUD_PeaceEquipmentRemove"
}
rows {
  id: 96189
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_136"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_136"
}
rows {
  id: 96190
  playEvent: "Play_LetsMOBA_Monster_BaoJun_Foot"
  playEvent3p: "Play_LetsMOBA_Monster_BaoJun_Foot_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96191
  playEvent: "Play_LetsMOBA_Monster_RedBuff_Foot"
  playEvent3p: "Play_LetsMOBA_Monster_RedBuff_Foot_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96192
  playEvent: "Play_LetsMOBA_Monster_BlueBuff_Foot"
  playEvent3p: "Play_LetsMOBA_Monster_BlueBuff_Foot_3P"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96193
}
rows {
  id: 96194
}
rows {
  id: 96195
  playEvent: "Play_LetsMOBA_UI_KillImpact"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96196
  playEvent: "Play_LetsMOBA_HUD_DarkScreen_JingKe"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96197
  playEvent: "Play_LetsMOBA_Cannon_Shot_1P"
  playEvent3p: "Play_LetsMOBA_Cannon_Shot_3P"
  bank: "LetsMOBA_OBJ"
  is3d: 1
}
rows {
  id: 96198
  playEvent: "Play_LetsMOBA_Cannon_Explo_1P"
  playEvent3p: "Play_LetsMOBA_Cannon_Explo_3P"
  bank: "LetsMOBA_OBJ"
  is3d: 1
}
rows {
  id: 96199
  playEvent: "Play_LetsMOBA_HUD_Signal_State"
  playEvent3p: "Play_LetsMOBA_HUD_Signal_State_3P"
  bank: "LetsMOBA_UIHUD"
}
rows {
  id: 96200
  playEvent: "Play_LetsMOBA_Monster_Ride_20000_Foot_1P_Origin"
  playEvent3p: "Play_LetsMOBA_Monster_Ride_20000_Foot_3P_Origin"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96201
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_150"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_150"
}
rows {
  id: 96202
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_151"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_151"
}
rows {
  id: 96203
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_152"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_152"
}
rows {
  id: 100001
  playEvent: "Play_LetsCOC_Tutorial_SEQ_00"
  playEvent3p: "Play_LetsCOC_Tutorial_SEQ_00"
  bank: "LetsCOC_Tutorial"
  stopEvent: "Stop_LetsCOC_Tutorial_SEQ_00"
  stopEvent3p: "Stop_LetsCOC_Tutorial_SEQ_00"
}
rows {
  id: 100002
  playEvent: "Play_LetsCOC_Tutorial_SEQ_01"
  playEvent3p: "Play_LetsCOC_Tutorial_SEQ_01"
  bank: "LetsCOC_Tutorial"
  stopEvent: "Stop_LetsCOC_Tutorial_SEQ_01"
  stopEvent3p: "Stop_LetsCOC_Tutorial_SEQ_01"
}
rows {
  id: 100003
  playEvent: "Play_LetsCOC_Tutorial_SEQ_02"
  playEvent3p: "Play_LetsCOC_Tutorial_SEQ_02"
  bank: "LetsCOC_Tutorial"
  stopEvent: "Stop_LetsCOC_Tutorial_SEQ_02"
  stopEvent3p: "Stop_LetsCOC_Tutorial_SEQ_02"
}
rows {
  id: 100004
  playEvent: "Play_LetsCOC_Tutorial_SEQ_03"
  playEvent3p: "Play_LetsCOC_Tutorial_SEQ_03"
  bank: "LetsCOC_Tutorial"
  stopEvent: "Stop_LetsCOC_Tutorial_SEQ_03"
  stopEvent3p: "Stop_LetsCOC_Tutorial_SEQ_03"
}
rows {
  id: 100005
  playEvent: "Play_LetsCOC_Tutorial_SEQ_04"
  playEvent3p: "Play_LetsCOC_Tutorial_SEQ_04"
  bank: "LetsCOC_Tutorial"
  stopEvent: "Stop_LetsCOC_Tutorial_SEQ_04"
  stopEvent3p: "Stop_LetsCOC_Tutorial_SEQ_04"
}
rows {
  id: 100006
  playEvent: "Play_LetsCOC_Tutorial_SEQ_05"
  playEvent3p: "Play_LetsCOC_Tutorial_SEQ_05"
  bank: "LetsCOC_Tutorial"
  stopEvent: "Stop_LetsCOC_Tutorial_SEQ_05"
  stopEvent3p: "Stop_LetsCOC_Tutorial_SEQ_05"
}
rows {
  id: 100007
  playEvent: "Play_LetsCOC_Tutorial_SEQ_06"
  playEvent3p: "Play_LetsCOC_Tutorial_SEQ_06"
  bank: "LetsCOC_Tutorial"
  stopEvent: "Stop_LetsCOC_Tutorial_SEQ_06"
  stopEvent3p: "Stop_LetsCOC_Tutorial_SEQ_06"
}
rows {
  id: 100008
}
rows {
  id: 100009
}
rows {
  id: 100010
}
rows {
  id: 100011
}
rows {
  id: 100101
  playEvent: "Play_LetsCOC_NPC_VO_Cheif_Agreed"
  playEvent3p: "Play_LetsCOC_NPC_VO_Cheif_Agreed"
  bank: "LetsCOC_NPC"
  stopEvent: "Stop_LetsCOC_NPC_VO_Cheif_Agreed"
  stopEvent3p: "Stop_LetsCOC_NPC_VO_Cheif_Agreed"
}
rows {
  id: 100102
  playEvent: "Play_LetsCOC_NPC_VO_Cheif_Angry"
  playEvent3p: "Play_LetsCOC_NPC_VO_Cheif_Angry"
  bank: "LetsCOC_NPC"
  stopEvent: "Stop_LetsCOC_NPC_VO_Cheif_Angry"
  stopEvent3p: "Stop_LetsCOC_NPC_VO_Cheif_Angry"
}
rows {
  id: 100103
  playEvent: "Play_LetsCOC_NPC_VO_Cheif_Disagree"
  playEvent3p: "Play_LetsCOC_NPC_VO_Cheif_Disagree"
  bank: "LetsCOC_NPC"
  stopEvent: "Stop_LetsCOC_NPC_VO_Cheif_Disagree"
  stopEvent3p: "Stop_LetsCOC_NPC_VO_Cheif_Disagree"
}
rows {
  id: 100104
  playEvent: "Play_LetsCOC_NPC_VO_Cheif_Happy"
  playEvent3p: "Play_LetsCOC_NPC_VO_Cheif_Happy"
  bank: "LetsCOC_NPC"
  stopEvent: "Stop_LetsCOC_NPC_VO_Cheif_Happy"
  stopEvent3p: "Stop_LetsCOC_NPC_VO_Cheif_Suprised"
}
rows {
  id: 100105
  playEvent: "Play_LetsCOC_NPC_VO_Cheif_Suprised"
  playEvent3p: "Play_LetsCOC_NPC_VO_Cheif_Suprised"
  bank: "LetsCOC_NPC"
  stopEvent: "Stop_LetsCOC_NPC_VO_Cheif_Suprised"
  stopEvent3p: "Stop_LetsCOC_NPC_VO_Cheif_Suprised"
}
rows {
  id: 100106
  playEvent: "Play_LetsCOC_NPC_VO_Goblin_Agreed"
  playEvent3p: "Play_LetsCOC_NPC_VO_Goblin_Agreed"
  bank: "LetsCOC_NPC"
  stopEvent: "Stop_LetsCOC_NPC_VO_Goblin_Agreed"
  stopEvent3p: "Stop_LetsCOC_NPC_VO_Goblin_Agreed"
}
rows {
  id: 100107
  playEvent: "Play_LetsCOC_NPC_VO_Goblin_Angry"
  playEvent3p: "Play_LetsCOC_NPC_VO_Goblin_Angry"
  bank: "LetsCOC_NPC"
  stopEvent: "Stop_LetsCOC_NPC_VO_Goblin_Angry"
  stopEvent3p: "Stop_LetsCOC_NPC_VO_Goblin_Angry"
}
rows {
  id: 100108
  playEvent: "Play_LetsCOC_NPC_VO_Goblin_Happy"
  playEvent3p: "Play_LetsCOC_NPC_VO_Goblin_Happy"
  bank: "LetsCOC_NPC"
  stopEvent: "Stop_LetsCOC_NPC_VO_Goblin_Happy"
  stopEvent3p: "Stop_LetsCOC_NPC_VO_Goblin_Happy"
}
rows {
  id: 100109
  playEvent: "Play_LetsCOC_NPC_Cmn_AreaClear_Start"
  playEvent3p: "Play_LetsCOC_NPC_Cmn_AreaClear_Start"
  bank: "LetsCOC_NPC"
  stopEvent: "Stop_LetsCOC_NPC_Cmn_AreaClear_Start"
  stopEvent3p: "Stop_LetsCOC_NPC_Cmn_AreaClear_Start"
}
rows {
  id: 100110
  playEvent: "Play_LetsCOC_NPC_Cmn_AreaClear_Small"
  playEvent3p: "Play_LetsCOC_NPC_Cmn_AreaClear_Small"
  bank: "LetsCOC_NPC"
}
rows {
  id: 100111
  playEvent: "Play_LetsCOC_NPC_Cmn_AreaClear_Large"
  playEvent3p: "Play_LetsCOC_NPC_Cmn_AreaClear_Large"
  bank: "LetsCOC_NPC"
}
rows {
  id: 100501
  playEvent: "Play_LetsCOC_ENV_Bedding_Default"
  playEvent3p: "Play_LetsCOC_ENV_Bedding_Default"
  bank: "LetsCOC_ENV"
  stopEvent: "Stop_LetsCOC_ENV_Bedding_Default"
  stopEvent3p: "Stop_LetsCOC_ENV_Bedding_Default"
  is3d: 1
  isLoop: 1
}
rows {
  id: 100502
  playEvent: "Play_LetsCOC_ENV_Bedding_Forest"
  playEvent3p: "Play_LetsCOC_ENV_Bedding_Forest"
  bank: "LetsCOC_ENV"
  stopEvent: "Stop_LetsCOC_ENV_Bedding_Forest"
  stopEvent3p: "Stop_LetsCOC_ENV_Bedding_Forest"
  is3d: 1
  isLoop: 1
}
rows {
  id: 100503
  playEvent: "Play_LetsCOC_ENV_Bedding_Sea"
  playEvent3p: "Play_LetsCOC_ENV_Bedding_Sea"
  bank: "LetsCOC_ENV"
  stopEvent: "Stop_LetsCOC_ENV_Bedding_Sea"
  stopEvent3p: "Stop_LetsCOC_ENV_Bedding_Sea"
  is3d: 1
  isLoop: 1
}
rows {
  id: 100504
  playEvent: "Play_LetsCOC_ENV_Position_River"
  playEvent3p: "Play_LetsCOC_ENV_Position_River"
  bank: "LetsCOC_ENV"
  stopEvent: "Stop_LetsCOC_ENV_Position_River"
  stopEvent3p: "Stop_LetsCOC_ENV_Position_River"
  is3d: 1
  isLoop: 1
}
rows {
  id: 100505
  playEvent: "Play_LetsCOC_ENV_Bedding_Night"
  playEvent3p: "Play_LetsCOC_ENV_Bedding_Night"
  bank: "LetsCOC_ENV"
  stopEvent: "Stop_LetsCOC_ENV_Bedding_Night"
  stopEvent3p: "Stop_LetsCOC_ENV_Bedding_Night"
  is3d: 1
  isLoop: 1
}
rows {
  id: 100551
  playEvent: "Play_LetsCOC_Building_103_Cannon_Touch"
  playEvent3p: "Play_LetsCOC_Building_103_Cannon_Touch"
  bank: "LetsCOC_Building"
  is3d: 1
}
rows {
  id: 100552
  playEvent: "Play_LetsCOC_Building_103_Cannon_Build"
  playEvent3p: "Play_LetsCOC_Building_103_Cannon_Build"
  bank: "LetsCOC_Building"
  is3d: 1
}
rows {
  id: 100553
  playEvent: "Play_LetsCOC_Building_103_Cannon_Fire"
  playEvent3p: "Play_LetsCOC_Building_103_Cannon_Fire"
  bank: "LetsCOC_Building"
  is3d: 1
}
rows {
  id: 100554
  playEvent: "Play_LetsCOC_Building_103_Cannon_Hit"
  playEvent3p: "Play_LetsCOC_Building_103_Cannon_Hit"
  bank: "LetsCOC_Building"
  is3d: 1
}
rows {
  id: 100555
  playEvent: "Play_LetsCOC_Building_103_Cannon_Hurt"
  playEvent3p: "Play_LetsCOC_Building_103_Cannon_Hurt"
  bank: "LetsCOC_Building"
  is3d: 1
}
rows {
  id: 100556
  playEvent: "Play_LetsCOC_Building_103_Cannon_Die"
  playEvent3p: "Play_LetsCOC_Building_103_Cannon_Die"
  bank: "LetsCOC_Building"
  is3d: 1
}
rows {
  id: 100557
  playEvent: "Play_LetsCOC_Building_103_Cannon_Loop"
  playEvent3p: "Play_LetsCOC_Building_103_Cannon_Loop"
  bank: "LetsCOC_Building"
  stopEvent: "Stop_LetsCOC_Building_103_Cannon_Loop"
  stopEvent3p: "Stop_LetsCOC_Building_103_Cannon_Loop"
  is3d: 1
  isLoop: 1
}

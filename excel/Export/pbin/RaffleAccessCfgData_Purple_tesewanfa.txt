com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_特色玩法.xlsx sheet:活动-紫色卡池
rows {
  raffleId: 11000001
  name: "特色玩法测试21"
  startTime {
    seconds: 1723392000
  }
  endTime {
    seconds: 1726502399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000000
    subPoolIds: 13000001
    subPoolIds: 13000002
    subPoolIds: 13000003
    subPoolIds: 13000004
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 159
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.7.95"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "HelloKitty"
  raffleTagIcon: "T_Sanrio_Img_HeadKitty"
  jumpIds: 25
  commodityIds: 120060
  commodityIds: 120061
  commodityIds: 120062
  commodityIds: 120063
  commodityIds: 120064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1723392000
  }
  showEndTime {
    seconds: 1726502399
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 28
}
rows {
  raffleId: 11000002
  name: "特色玩法测试22"
  startTime {
    seconds: 1723392000
  }
  endTime {
    seconds: 1726502399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000005
    subPoolIds: 13000006
    subPoolIds: 13000007
    subPoolIds: 13000008
    subPoolIds: 13000009
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 160
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.7.95"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "Kuromi"
  raffleTagIcon: "T_Sanrio_Img_HeadSanrio"
  jumpIds: 25
  commodityIds: 120060
  commodityIds: 120061
  commodityIds: 120062
  commodityIds: 120063
  commodityIds: 120064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1723392000
  }
  showEndTime {
    seconds: 1726502399
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 28
}
rows {
  raffleId: 13000010
  name: "洋葱牛奶"
  startTime {
    seconds: 1723392000
  }
  endTime {
    seconds: 1726243199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000010
    subPoolIds: 13000011
    subPoolIds: 13000012
  }
  dailyLimit: 14
  maxLimit: 42
  textRuleId: 214
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮7折"
  lowestVersion: "1.3.18.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "洋葱头蔬果屋"
  raffleTagIcon: "T_Farm2_Icon_Tab2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1723392000
  }
  showEndTime {
    seconds: 1726243199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 14
}
rows {
  raffleId: 13000011
  name: "洋葱牛奶"
  startTime {
    seconds: 1723392000
  }
  endTime {
    seconds: 1726243199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000013
    subPoolIds: 13000014
    subPoolIds: 13000015
  }
  dailyLimit: 14
  maxLimit: 42
  textRuleId: 215
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮7折"
  lowestVersion: "1.3.18.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "牛牛牧场小店"
  raffleTagIcon: "T_Farm2_Icon_Tab1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1723392000
  }
  showEndTime {
    seconds: 1726243199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 14
}
rows {
  raffleId: 13000012
  name: "登月卡池"
  startTime {
    seconds: 1723392000
  }
  endTime {
    seconds: 1728489599
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000120
    subPoolIds: 13000121
    subPoolIds: 13000122
    subPoolIds: 13000123
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 220
  text: "抽到密钥会自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折"
  lowestVersion: "1.3.18.31"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "滑板涂鸦"
  raffleTagIcon: "T_WerewolfMoonshot_Icon_AwardPlateSelect "
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1723392000
  }
  showEndTime {
    seconds: 1728489599
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10052
  tagDailyLimit: 21
}
rows {
  raffleId: 13000013
  name: "登月卡池"
  startTime {
    seconds: 1723392000
  }
  endTime {
    seconds: 1728489599
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000130
    subPoolIds: 13000131
    subPoolIds: 13000132
    subPoolIds: 13000133
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 221
  text: "抽到密钥会自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折"
  lowestVersion: "1.3.18.31"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "闪烁突袭"
  raffleTagIcon: "T_WerewolfMoonshot_Icon_AwardPlateNormal"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1723392000
  }
  showEndTime {
    seconds: 1728489599
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10052
  tagDailyLimit: 21
}
rows {
  raffleId: 13000014
  name: "梦幻告白"
  startTime {
    seconds: 1738857600
  }
  endTime {
    seconds: 1739462399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000160
    subPoolIds: 13000161
    subPoolIds: 13000162
    subPoolIds: 13000163
    subPoolIds: 13000164
    subPoolIds: 13000165
    subPoolIds: 13000166
    subPoolIds: 13000167
    subPoolIds: 13000168
    subPoolIds: 13000169
    subPoolIds: 13000170
    subPoolIds: 13000171
    subPoolIds: 13000172
  }
  dailyLimit: 120
  maxLimit: 120
  textRuleId: 250
  text: "祈愿10次时提前获得大奖会返还幸运币，限时前20次祈愿4折，第21到40次祈愿5折，第41到80次祈愿75折！|星宝农场梦幻告白系列装饰上线"
  lowestVersion: "1.3.18.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1738857600
  }
  showEndTime {
    seconds: 1739462399
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  gotGrandRule: 1
  tagDailyLimit: 120
}
rows {
  raffleId: 13000015
  name: "云霄飞车奇遇"
  startTime {
    seconds: 1730304000
  }
  endTime {
    seconds: 1732204799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000173
    subPoolIds: 13000174
    subPoolIds: 13000175
    subPoolIds: 13000176
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 261
  text: "抽到密钥会自动抽取上方奖池，祈愿1轮时提前获得密钥会返还幸运币|限时折扣，1轮5折，2轮6折，3轮75折"
  lowestVersion: "1.3.26.45"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "魔法扫帚"
  raffleTagIcon: "T_WerewolfRollercoaster_Icon_AwardPlateSelect1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  viewIndex: 13
  viewIndex: 14
  viewIndex: 15
  showStartTime {
    seconds: 1730304000
  }
  showEndTime {
    seconds: 1732204799
  }
  isShow: true
  showRule: 4
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10060
  tagDailyLimit: 15
}
rows {
  raffleId: 13000016
  name: "云霄飞车奇遇"
  startTime {
    seconds: 1730563200
  }
  endTime {
    seconds: 1732204799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000177
    subPoolIds: 13000178
    subPoolIds: 13000179
    subPoolIds: 13000180
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 262
  text: "抽到密钥会自动抽取上方奖池，祈愿1轮时提前获得密钥会返还幸运币|限时折扣，1轮5折，2轮6折，3轮75折"
  lowestVersion: "1.3.26.45"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "爱心冲击"
  raffleTagIcon: "T_WerewolfRollercoaster_Icon_AwardPlateNormal1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  viewIndex: 13
  viewIndex: 14
  viewIndex: 15
  showStartTime {
    seconds: 1730563200
  }
  showEndTime {
    seconds: 1732204799
  }
  isShow: true
  showRule: 4
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10061
  tagDailyLimit: 15
}
rows {
  raffleId: 13000017
  name: "招财喵"
  startTime {
    seconds: 1729785600
  }
  endTime {
    seconds: 1735228799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000020
    subPoolIds: 13000021
    subPoolIds: 13000022
    subPoolIds: 13000023
  }
  dailyLimit: 30
  maxLimit: 30
  textRuleId: 263
  text: "抽到幸运钥匙会自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币，限时第1轮2折，第2轮6折|农场全新装饰<FortuneCat2>招财喵、梦幻熊礼盒</>登场"
  lowestVersion: "1.3.18.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  showStartTime {
    seconds: 1729785600
  }
  showEndTime {
    seconds: 1735228799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 30
}
rows {
  raffleId: 13000018
  name: "蔚海绮梦"
  startTime {
    seconds: 1730822400
  }
  endTime {
    seconds: 1734623999
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000190
    subPoolIds: 13000191
    subPoolIds: 13000192
    subPoolIds: 13000193
    subPoolIds: 13000194
    subPoolIds: 13000195
    subPoolIds: 13000196
    subPoolIds: 13000197
    subPoolIds: 13000198
    subPoolIds: 13000199
    subPoolIds: 13000200
    subPoolIds: 13000201
    subPoolIds: 13000202
  }
  dailyLimit: 120
  maxLimit: 120
  textRuleId: 268
  text: "祈愿1轮时提前获得大奖会返还幸运币，限时第1、2轮祈愿4折，第3、4轮祈愿5折，第5到8轮祈愿75折！|星宝农场蔚海绮梦系列装饰上线"
  lowestVersion: "1.3.18.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1730822400
  }
  showEndTime {
    seconds: 1734623999
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "3;UI_Lottery_GloryRoadPanel#5;UI_Lottery_Farm4_ChoiceView"
  gotGrandRule: 1
  tagDailyLimit: 120
}
rows {
  raffleId: 13000023
  name: "太空觅宝"
  startTime {
    seconds: 1731945600
  }
  endTime {
    seconds: 1733673599
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000230
    subPoolIds: 13000231
    subPoolIds: 13000232
    subPoolIds: 13000233
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 220
  text: "抽到密钥会自动抽上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折"
  lowestVersion: "1.3.26.93"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "滑板涂鸦"
  raffleTagIcon: "T_WerewolfMoonshot_Icon_AwardPlateSelect "
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1731945600
  }
  showEndTime {
    seconds: 1733673599
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10052
  tagDailyLimit: 21
}
rows {
  raffleId: 13000024
  name: "太空觅宝"
  startTime {
    seconds: 1731945600
  }
  endTime {
    seconds: 1733673599
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000240
    subPoolIds: 13000241
    subPoolIds: 13000242
    subPoolIds: 13000243
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 221
  text: "抽到密钥会自动抽上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折"
  lowestVersion: "1.3.26.93"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "闪烁突袭"
  raffleTagIcon: "T_WerewolfMoonshot_Icon_AwardPlateNormal"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1731945600
  }
  showEndTime {
    seconds: 1733673599
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10052
  tagDailyLimit: 21
}
rows {
  raffleId: 13000025
  name: "狼人柯南联动"
  startTime {
    seconds: 1734710400
  }
  endTime {
    seconds: 1737043199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000250
    subPoolIds: 13000251
    subPoolIds: 13000252
    subPoolIds: 13000253
  }
  dailyLimit: 21
  maxLimit: 28
  textRuleId: 294
  text: "抽到密钥会自动抽上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮6折，3轮8折"
  lowestVersion: "1.3.37.37"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "真相动画"
  raffleTagIcon: "T_WerewolfReasoning_Icon_Award01"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1734710400
  }
  showEndTime {
    seconds: 1737043199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10052
  tagDailyLimit: 21
}
rows {
  raffleId: 13000026
  name: "狼人柯南联动"
  startTime {
    seconds: 1734710400
  }
  endTime {
    seconds: 1737043199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000260
    subPoolIds: 13000261
    subPoolIds: 13000262
    subPoolIds: 13000263
  }
  dailyLimit: 21
  maxLimit: 28
  textRuleId: 295
  text: "抽到密钥会自动抽上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮6折，3轮8折"
  lowestVersion: "1.3.37.37"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "麻醉针动画"
  raffleTagIcon: "T_WerewolfReasoning_Icon_Award02"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1734710400
  }
  showEndTime {
    seconds: 1737043199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10052
  tagDailyLimit: 21
}
rows {
  raffleId: 13000027
  name: "雪境欢颂"
  startTime {
    seconds: 1735660800
  }
  endTime {
    seconds: 1737734399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000270
    subPoolIds: 13000271
    subPoolIds: 13000272
    subPoolIds: 13000273
    subPoolIds: 13000274
    subPoolIds: 13000275
    subPoolIds: 13000276
    subPoolIds: 13000277
    subPoolIds: 13000278
    subPoolIds: 13000279
    subPoolIds: 13000280
    subPoolIds: 13000281
    subPoolIds: 13000282
  }
  dailyLimit: 120
  maxLimit: 120
  textRuleId: 290
  text: "祈愿1轮时提前获得大奖会返还幸运币，限时第1、2轮祈愿4折，第3、4轮祈愿5折，第5到8轮祈愿75折！|星宝农场雪境欢颂系列装饰上线"
  lowestVersion: "1.3.37.68"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1735660800
  }
  showEndTime {
    seconds: 1737734399
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "3;UI_Lottery_GloryRoadPanel#5;UI_Lottery_Farm5_ChoiceView"
  gotGrandRule: 1
  tagDailyLimit: 120
}
rows {
  raffleId: 13000021
  name: "猫猫搜城记"
  startTime {
    seconds: 1732680000
  }
  endTime {
    seconds: 1746115199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000210
    subPoolIds: 13000211
    subPoolIds: 13000212
    subPoolIds: 13000213
    subPoolIds: 13000214
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 283
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折，4轮8折"
  lowestVersion: "1.3.26.115"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "雷霆之怒"
  raffleTagIcon: "T_HidingTreasure_Icon_Award01"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1732680000
  }
  showEndTime {
    seconds: 1746115199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 28
}
rows {
  raffleId: 13000022
  name: "猫猫搜城记"
  startTime {
    seconds: 1732680000
  }
  endTime {
    seconds: 1746115199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000220
    subPoolIds: 13000221
    subPoolIds: 13000222
    subPoolIds: 13000223
    subPoolIds: 13000224
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 284
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折，4轮8折"
  lowestVersion: "1.3.26.115"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "彩带飘飘"
  raffleTagIcon: "T_HidingTreasure_Icon_Award02"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1732680000
  }
  showEndTime {
    seconds: 1746115199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 28
}
rows {
  raffleId: 13000029
  name: "猫猫搜城记"
  startTime {
    seconds: 1736784000
  }
  endTime {
    seconds: 1746115199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000290
    subPoolIds: 13000291
    subPoolIds: 13000292
    subPoolIds: 13000293
    subPoolIds: 13000294
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 308
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折，4轮8折"
  lowestVersion: "1.3.37.99"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "璀璨焰火"
  raffleTagIcon: "T_HidingTreasure_Icon_Award04"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1736784000
  }
  showEndTime {
    seconds: 1746115199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 28
}
rows {
  raffleId: 13000030
  name: "猫猫搜城记"
  startTime {
    seconds: 1736784000
  }
  endTime {
    seconds: 1746115199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000300
    subPoolIds: 13000301
    subPoolIds: 13000302
    subPoolIds: 13000303
    subPoolIds: 13000304
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 309
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折，4轮8折"
  lowestVersion: "1.3.37.100"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "甜心雪灵"
  raffleTagIcon: "T_HidingTreasure_Icon_Award03"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1736784000
  }
  showEndTime {
    seconds: 1746115199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 28
}
rows {
  raffleId: 13000031
  name: "云霄飞车奇遇"
  startTime {
    seconds: 1736611200
  }
  endTime {
    seconds: 1739116799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000310
    subPoolIds: 13000311
    subPoolIds: 13000312
    subPoolIds: 13000313
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 261
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得密钥会返还幸运币|限时折扣，1轮5折，2轮6折，3轮75折"
  lowestVersion: "1.3.37.99"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "魔法扫帚"
  raffleTagIcon: "T_WerewolfRollercoaster_Icon_AwardPlateSelect1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  viewIndex: 13
  viewIndex: 14
  viewIndex: 15
  showStartTime {
    seconds: 1736611200
  }
  showEndTime {
    seconds: 1739116799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10060
  tagDailyLimit: 15
}
rows {
  raffleId: 13000032
  name: "云霄飞车奇遇"
  startTime {
    seconds: 1736611200
  }
  endTime {
    seconds: 1739116799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000320
    subPoolIds: 13000321
    subPoolIds: 13000322
    subPoolIds: 13000323
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 262
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得密钥会返还幸运币|限时折扣，1轮5折，2轮6折，3轮75折"
  lowestVersion: "1.3.37.99"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "爱心冲击"
  raffleTagIcon: "T_WerewolfRollercoaster_Icon_AwardPlateNormal1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  viewIndex: 13
  viewIndex: 14
  viewIndex: 15
  showStartTime {
    seconds: 1736611200
  }
  showEndTime {
    seconds: 1739116799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10061
  tagDailyLimit: 15
}
rows {
  raffleId: 13000033
  name: "福运琳琅"
  startTime {
    seconds: 1736092800
  }
  endTime {
    seconds: 1740671999
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000330
    subPoolIds: 13000331
    subPoolIds: 13000332
    subPoolIds: 13000333
    subPoolIds: 13000334
    subPoolIds: 13000335
    subPoolIds: 13000336
    subPoolIds: 13000337
    subPoolIds: 13000338
    subPoolIds: 13000339
    subPoolIds: 13000340
  }
  dailyLimit: 100
  maxLimit: 100
  textRuleId: 311
  text: "祈愿1轮时提前获得大奖会返还幸运币，限时第1、2轮祈愿4折，第3、4轮祈愿5折，第5、6轮祈愿75折！|星宝农场福运琳琅系列装饰上线"
  lowestVersion: "1.3.37.68"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1736092800
  }
  showEndTime {
    seconds: 1740671999
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "3;UI_Lottery_GloryRoadPanel#5;UI_Lottery_Farm6_ChoiceView"
  gotGrandRule: 1
  tagDailyLimit: 100
}
rows {
  raffleId: 13000035
  name: "幸福满屋"
  startTime {
    seconds: 1736611200
  }
  endTime {
    seconds: 1741276799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000350
    subPoolIds: 13000351
    subPoolIds: 13000352
    subPoolIds: 13000353
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 313
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得密钥会返还幸运币|限时折扣，1轮5折，2轮5折，3轮75折"
  lowestVersion: "1.3.37.111"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "年年有鱼"
  raffleTagIcon: "T_WerewolfSpringfestival_Icon_Award01"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  viewIndex: 13
  viewIndex: 14
  viewIndex: 15
  showStartTime {
    seconds: 1736611200
  }
  showEndTime {
    seconds: 1741276799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 21
}
rows {
  raffleId: 13000036
  name: "幸福满屋"
  startTime {
    seconds: 1736611200
  }
  endTime {
    seconds: 1741276799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000360
    subPoolIds: 13000361
    subPoolIds: 13000362
    subPoolIds: 13000363
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 314
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得密钥会返还幸运币|限时折扣，1轮5折，2轮5折，3轮75折"
  lowestVersion: "1.3.37.111"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "冬日暖阳"
  raffleTagIcon: "T_WerewolfSpringfestival_Icon_Award02"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  viewIndex: 13
  viewIndex: 14
  viewIndex: 15
  showStartTime {
    seconds: 1736611200
  }
  showEndTime {
    seconds: 1741276799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 21
}
rows {
  raffleId: 13000037
  name: "梦幻告白"
  startTime {
    seconds: 1739462400
  }
  endTime {
    seconds: 1741881599
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000370
    subPoolIds: 13000371
    subPoolIds: 13000372
    subPoolIds: 13000373
    subPoolIds: 13000374
    subPoolIds: 13000375
    subPoolIds: 13000376
    subPoolIds: 13000377
    subPoolIds: 13000378
    subPoolIds: 13000379
    subPoolIds: 13000380
    subPoolIds: 13000381
    subPoolIds: 13000382
  }
  dailyLimit: 120
  maxLimit: 120
  textRuleId: 250
  text: "祈愿1轮时提前获得大奖会返还幸运币，限时第1、2轮祈愿4折，第3、4轮祈愿5折，第5到8轮祈愿75折！|星宝农场梦幻告白系列装饰上线"
  lowestVersion: "1.3.26.23"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1739462400
  }
  showEndTime {
    seconds: 1741881599
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  gotGrandRule: 1
  tagDailyLimit: 120
}
rows {
  raffleId: 13000038
  name: "桃坞问春"
  startTime {
    seconds: 1740758400
  }
  endTime {
    seconds: 1742486399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000390
    subPoolIds: 13000391
    subPoolIds: 13000392
    subPoolIds: 13000393
    subPoolIds: 13000394
    subPoolIds: 13000395
    subPoolIds: 13000396
    subPoolIds: 13000397
    subPoolIds: 13000398
    subPoolIds: 13000399
    subPoolIds: 13000400
  }
  dailyLimit: 100
  maxLimit: 100
  textRuleId: 327
  text: "祈愿1轮时提前获得大奖会返还幸运币，限时第1到4轮祈愿5折，第5到9轮祈愿75折！|星宝农场桃坞问春系列装饰上线"
  lowestVersion: "1.3.68.116"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1740758400
  }
  showEndTime {
    seconds: 1742486399
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "3;UI_Lottery_NineTailedFox_GloryRoadPanel#5;UI_Lottery_Farm7_ChoiceView"
  gotGrandRule: 1
  tagDailyLimit: 100
}
rows {
  raffleId: 13000041
  name: "猫猫造物台"
  startTime {
    seconds: 1741838400
  }
  endTime {
    seconds: 1755187199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000410
    subPoolIds: 13000411
    subPoolIds: 13000412
    subPoolIds: 13000413
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 332
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮75折"
  lowestVersion: "1.3.78.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "萌动甜点"
  raffleTagIcon: "T_HidingTable_Img_SweetWeapon_01"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1741838400
  }
  showEndTime {
    seconds: 1755187199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 21
}
rows {
  raffleId: 13000042
  name: "猫猫造物台"
  startTime {
    seconds: 1741838400
  }
  endTime {
    seconds: 1755187199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000420
    subPoolIds: 13000421
    subPoolIds: 13000422
    subPoolIds: 13000423
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 333
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮75折"
  lowestVersion: "1.3.78.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "量子魔方"
  raffleTagIcon: "T_HidingTable_Img_SweetWeapon_02"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1741838400
  }
  showEndTime {
    seconds: 1755187199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 21
}
rows {
  raffleId: 13000039
  name: "珍馐百味"
  startTime {
    seconds: 1742486400
  }
  endTime {
    seconds: 1746028799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000430
    subPoolIds: 13000431
    subPoolIds: 13000432
    subPoolIds: 13000433
    subPoolIds: 13000434
    subPoolIds: 13000435
    subPoolIds: 13000436
    subPoolIds: 13000437
    subPoolIds: 13000438
    subPoolIds: 13000439
    subPoolIds: 13000440
  }
  dailyLimit: 30
  maxLimit: 100
  textRuleId: 336
  text: "祈愿1轮时提前获得大奖会返还幸运币，限时第1、2轮祈愿4折，第3、4轮祈愿5折，第5、6轮祈愿75折！|星宝农场珍馐百味系列装饰上线"
  lowestVersion: "1.3.78.33"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1742486400
  }
  showEndTime {
    seconds: 1746028799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "3;UI_Lottery_GloryRoadPanel#5;UI_Lottery_Farm8_ChoiceView"
  gotGrandRule: 1
  tagDailyLimit: 100
}
rows {
  raffleId: 13000045
  name: "精灵谷修行"
  startTime {
    seconds: 1743350400
  }
  endTime {
    seconds: 1746460799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000450
    subPoolIds: 13000451
    subPoolIds: 13000452
    subPoolIds: 13000453
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 342
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折"
  lowestVersion: "1.3.78.53"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "都市疾驰"
  raffleTagIcon: "T_WerewolfElf_Img_Animation1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1743350400
  }
  showEndTime {
    seconds: 1746460799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 21
}
rows {
  raffleId: 13000046
  name: "精灵谷修行"
  startTime {
    seconds: 1743350400
  }
  endTime {
    seconds: 1746460799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000460
    subPoolIds: 13000461
    subPoolIds: 13000462
    subPoolIds: 13000463
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 343
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折"
  lowestVersion: "1.3.78.53"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "胖哒出击"
  raffleTagIcon: "T_WerewolfElf_Img_Animation2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1743350400
  }
  showEndTime {
    seconds: 1746460799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 21
}
rows {
  raffleId: 13000040
  name: "奶油乐园奇遇"
  startTime {
    seconds: 1742140800
  }
  endTime {
    seconds: 1749139199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000470
    subPoolIds: 13000471
    subPoolIds: 13000472
    subPoolIds: 13000473
    subPoolIds: 13000474
    subPoolIds: 13000475
    subPoolIds: 13000476
  }
  dailyLimit: 30
  maxLimit: 92
  textRuleId: 345
  text: "祈愿多次时提前获得大奖会返还幸运币，限时第1到2轮祈愿2.5折，第3到6轮祈愿5折!"
  lowestVersion: "1.3.26.45"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagIcon: "T_WerewolfRollercoaster_Icon_AwardPlateSelect1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1742140800
  }
  showEndTime {
    seconds: 1749139199
  }
  isShow: true
  showRule: 4
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "10;UI_DrawReward_MallView#3;UI_Lottery_GloryRoadPanel"
  gotGrandRule: 1
  maxCountMultiDraw: 10
  giftGiveCommodityIds: 300010
  giftGiveCommodityIds: 300011
  giftGiveCommodityIds: 300012
  giftGiveCommodityIds: 300013
  giftGiveCommodityIds: 300014
  giftGiveCommodityIds: 300015
  giftGiveCommodityIds: 300016
  giftGiveCommodityIds: 300017
  tagDailyLimit: 92
}
rows {
  raffleId: 13000047
  name: "满杯蜜桃猫"
  startTime {
    seconds: 1743436800
  }
  endTime {
    seconds: 1749743999
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000480
    subPoolIds: 13000481
    subPoolIds: 13000482
    subPoolIds: 13000483
    subPoolIds: 13000484
  }
  dailyLimit: 30
  maxLimit: 32
  textRuleId: 349
  text: "抽到幸运钥匙会自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币，限时第1轮2.5折，第2、3轮5折|农场全新装饰<PeachCatLottery> 蜜桃猫星星杯、蜜桃猫星礼盒</>登场"
  lowestVersion: "1.3.18.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  showStartTime {
    seconds: 1743436800
  }
  showEndTime {
    seconds: 1749743999
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 32
}
rows {
  raffleId: 13000048
  name: "蔚海绮梦"
  startTime {
    seconds: 1744905600
  }
  endTime {
    seconds: 1747324799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000490
    subPoolIds: 13000491
    subPoolIds: 13000492
    subPoolIds: 13000493
    subPoolIds: 13000494
    subPoolIds: 13000495
    subPoolIds: 13000496
    subPoolIds: 13000497
    subPoolIds: 13000498
    subPoolIds: 13000499
    subPoolIds: 13000500
    subPoolIds: 13000501
    subPoolIds: 13000502
  }
  dailyLimit: 30
  maxLimit: 120
  textRuleId: 268
  text: "祈愿1轮时提前获得大奖会返还幸运币，限时第1、2轮祈愿4折，第3、4轮祈愿5折，第5到8轮祈愿75折！|星宝农场蔚海绮梦系列装饰上线"
  lowestVersion: "1.3.78.96"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1744905600
  }
  showEndTime {
    seconds: 1747324799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "3;UI_Lottery_GloryRoadPanel#5;UI_Lottery_Farm4_ChoiceView"
  gotGrandRule: 1
  tagDailyLimit: 120
}
rows {
  raffleId: 13000051
  name: "狼人柯南联动"
  startTime {
    seconds: 4080211200
  }
  endTime {
    seconds: 4082803199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000510
    subPoolIds: 13000511
    subPoolIds: 13000512
    subPoolIds: 13000513
  }
  dailyLimit: 21
  maxLimit: 28
  textRuleId: 294
  text: "抽到钥匙自动抽上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮6折，3轮8折"
  lowestVersion: "1.3.78.100"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "真相动画"
  raffleTagIcon: "T_WerewolfReasoning_Icon_Award01"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 4080211200
  }
  showEndTime {
    seconds: 4082803199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10052
  tagDailyLimit: 21
}
rows {
  raffleId: 13000052
  name: "狼人柯南联动"
  startTime {
    seconds: 4080211200
  }
  endTime {
    seconds: 4082803199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000520
    subPoolIds: 13000521
    subPoolIds: 13000522
    subPoolIds: 13000523
  }
  dailyLimit: 21
  maxLimit: 28
  textRuleId: 295
  text: "抽到钥匙自动抽上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮6折，3轮8折"
  lowestVersion: "1.3.78.100"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "麻醉枪动画"
  raffleTagIcon: "T_WerewolfReasoning_Icon_Award02"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 4080211200
  }
  showEndTime {
    seconds: 4082803199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  shareId: 10052
  tagDailyLimit: 21
}
rows {
  raffleId: 13000049
  name: "甜梦嘉年华"
  startTime {
    seconds: 1741449600
  }
  endTime {
    seconds: 1749139199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000530
    subPoolIds: 13000531
    subPoolIds: 13000532
    subPoolIds: 13000533
    subPoolIds: 13000534
    subPoolIds: 13000535
    subPoolIds: 13000536
    subPoolIds: 13000537
    subPoolIds: 13000538
    subPoolIds: 13000539
    subPoolIds: 13000540
  }
  dailyLimit: 30
  maxLimit: 100
  textRuleId: 351
  text: "祈愿1轮时提前获得大奖会返还幸运币，限时第1、2轮祈愿4折，第3、4轮祈愿5折，第5、6轮祈愿75折！|星宝农场甜梦嘉年华系列装饰上线"
  lowestVersion: "1.3.18.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1741449600
  }
  showEndTime {
    seconds: 1749139199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "3;UI_Lottery_GloryRoadPanel#5;UI_Lottery_FarmPark_ChoiceView"
  gotGrandRule: 1
  tagDailyLimit: 100
}
rows {
  raffleId: 13000055
  name: "猫猫补习班"
  startTime {
    seconds: 1745985600
  }
  endTime {
    seconds: 1755187199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000550
    subPoolIds: 13000551
    subPoolIds: 13000552
    subPoolIds: 13000553
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 352
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮75折"
  lowestVersion: "1.3.88.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "繁星法杖"
  raffleTagIcon: "T_HidingSchool_Img_SweetWeapon_01"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1745985600
  }
  showEndTime {
    seconds: 1755187199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 21
}
rows {
  raffleId: 13000056
  name: "猫猫补习班"
  startTime {
    seconds: 1745985600
  }
  endTime {
    seconds: 1755187199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000560
    subPoolIds: 13000561
    subPoolIds: 13000562
    subPoolIds: 13000563
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 353
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮75折"
  lowestVersion: "1.3.88.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "嘟嘟号"
  raffleTagIcon: "T_HidingSchool_Img_SweetWeapon_02"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1745985600
  }
  showEndTime {
    seconds: 1755187199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 21
}
rows {
  raffleId: 13000057
  name: "狼人太空战"
  startTime {
    seconds: 1747411200
  }
  endTime {
    seconds: 1750003199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000570
    subPoolIds: 13000571
    subPoolIds: 13000572
    subPoolIds: 13000573
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 361
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折"
  lowestVersion: "1.3.88.50"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "星球大战"
  raffleTagIcon: "T_WerewolfSpace_Img_Animation1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1747411200
  }
  showEndTime {
    seconds: 1750003199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 21
}
rows {
  raffleId: 13000058
  name: "雪境欢颂"
  startTime {
    seconds: 1747065600
  }
  endTime {
    seconds: 1751558399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000580
    subPoolIds: 13000581
    subPoolIds: 13000582
    subPoolIds: 13000583
    subPoolIds: 13000584
    subPoolIds: 13000585
    subPoolIds: 13000586
    subPoolIds: 13000587
    subPoolIds: 13000588
    subPoolIds: 13000589
    subPoolIds: 13000590
    subPoolIds: 13000591
    subPoolIds: 13000592
  }
  dailyLimit: 30
  maxLimit: 120
  textRuleId: 290
  text: "祈愿1轮时提前获得大奖会返还幸运币，限时第1、2轮祈愿4折，第3、4轮祈愿5折，第5到8轮祈愿75折！|星宝农场雪境欢颂系列装饰上线"
  lowestVersion: "1.3.37.68"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1747065600
  }
  showEndTime {
    seconds: 1751558399
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "3;UI_Lottery_GloryRoadPanel#5;UI_Lottery_Farm5_ChoiceView"
  gotGrandRule: 1
  tagDailyLimit: 120
}
rows {
  raffleId: 13000060
  name: "狼人世界杯"
  startTime {
    seconds: 1748534400
  }
  endTime {
    seconds: 1751212799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000600
    subPoolIds: 13000601
    subPoolIds: 13000602
    subPoolIds: 13000603
  }
  dailyLimit: 21
  maxLimit: 21
  textRuleId: 368
  text: "抽到钥匙自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1轮5折，2轮5折，3轮6折"
  lowestVersion: "1.3.88.50"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "热血足球"
  raffleTagIcon: "T_WerewolfFootball_Img_Animation"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1748534400
  }
  showEndTime {
    seconds: 1751212799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 21
}
rows {
  raffleId: 13000061
  name: "招财喵"
  startTime {
    seconds: 1747670400
  }
  endTime {
    seconds: 1750348799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000610
    subPoolIds: 13000611
    subPoolIds: 13000612
    subPoolIds: 13000613
  }
  dailyLimit: 30
  maxLimit: 30
  textRuleId: 263
  text: "抽到幸运钥匙会自动抽取上方奖池，祈愿1轮时提前获得钥匙会返还幸运币，限时第1轮2折，第2轮6折|农场全新装饰<FortuneCat2>招财喵、梦幻熊礼盒</>登场"
  lowestVersion: "1.3.88.92"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  showStartTime {
    seconds: 1747670400
  }
  showEndTime {
    seconds: 1750348799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
  tagDailyLimit: 30
}
rows {
  raffleId: 13000062
  name: "炽光海岸"
  startTime {
    seconds: 1748448000
  }
  endTime {
    seconds: 1753372799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000620
    subPoolIds: 13000621
    subPoolIds: 13000622
    subPoolIds: 13000623
    subPoolIds: 13000624
  }
  dailyLimit: 42
  maxLimit: 42
  textRuleId: 375
  text: "祈愿多次时提前获得大奖会返还幸运币，限时第1轮祈愿5折!"
  lowestVersion: "1.3.88.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagIcon: "T_WerewolfRollercoaster_Icon_AwardPlateSelect1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  showStartTime {
    seconds: 1748448000
  }
  showEndTime {
    seconds: 1753372799
  }
  isShow: true
  showRule: 4
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "10;UI_DrawReward_MallView#3;UI_Lottery_GloryRoadPanel"
  gotGrandRule: 1
  maxCountMultiDraw: 10
  tagDailyLimit: 42
}
rows {
  raffleId: 13000063
  name: "天穹圣域"
  startTime {
    seconds: 1748707200
  }
  endTime {
    seconds: 1753372799
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 13000630
    subPoolIds: 13000631
    subPoolIds: 13000632
    subPoolIds: 13000633
    subPoolIds: 13000634
    subPoolIds: 13000635
    subPoolIds: 13000636
    subPoolIds: 13000637
    subPoolIds: 13000638
    subPoolIds: 13000639
    subPoolIds: 13000640
  }
  dailyLimit: 100
  maxLimit: 100
  textRuleId: 376
  text: "祈愿1轮时提前获得大奖会返还幸运币，限时第1、2轮祈愿4折，第3、4轮祈愿5折，第5、6轮祈愿75折！|星宝农场甜梦嘉年华系列装饰上线"
  lowestVersion: "1.3.88.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  viewIndex: 12
  showStartTime {
    seconds: 1748707200
  }
  showEndTime {
    seconds: 1753372799
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  animType: 4
  relateUmgSetting: "3;UI_Lottery_GloryRoadPanel#5;UI_Lottery_FarmPark_ChoiceView"
  gotGrandRule: 1
  tagDailyLimit: 100
}

com.tencent.wea.xlsRes.table_MiscConf
excel/xls/Z_杂项配置.xlsx sheet:杂项配置-LetsGo
rows {
  id: 1
  playerReg {
    regDefaultItem {
      itemId: 501
      itemNum: 100
    }
    regDefaultItem {
      itemId: 502
      itemNum: 100
    }
    regDefaultItem {
      itemId: 222001
      itemNum: 1
    }
    regDefaultItem {
      itemId: 222002
      itemNum: 1
    }
    regDefaultItem {
      itemId: 222003
      itemNum: 1
    }
    regDefaultItem {
      itemId: 222004
      itemNum: 1
    }
    regDefaultItem {
      itemId: 222005
      itemNum: 1
    }
    regDefaultItem {
      itemId: 222006
      itemNum: 1
    }
    regDefaultItem {
      itemId: 222007
      itemNum: 1
    }
    regDefaultItem {
      itemId: 222008
      itemNum: 1
    }
    regDefaultItem {
      itemId: 740001
      itemNum: 1
    }
    regDefaultItem {
      itemId: 240001
      itemNum: 1
    }
    regDefaultItem {
      itemId: 240201
      itemNum: 1
    }
    regDefaultItem {
      itemId: 241001
      itemNum: 1
    }
    regDefaultItem {
      itemId: 241201
      itemNum: 1
    }
    regDefaultItem {
      itemId: 241401
      itemNum: 1
    }
    regDefaultItem {
      itemId: 890001
      itemNum: 1
    }
  }
  battleConf {
    battleDropDailyLimit {
      itemId: 4
      itemNum: 12000
    }
    battleDropDailyLimit {
      itemId: 1001
      itemNum: 2500
    }
    battleDropDailyLimit {
      itemId: 3541
      itemNum: 1000
    }
    battleDropDailyRefreshTime: "00:00:00"
    fpsDropLimit {
      gameTypeId: 38
      itemId: 4
      weeklyDropLimit: 6000
      seasonalCostLimit: 2000000
      weeklyRefreshTime: "00:00:00"
    }
    fpsDropLimit {
      gameTypeId: 38
      itemId: 6
      weeklyDropLimit: 5000
      seasonalCostLimit: 100000
      weeklyRefreshTime: "00:00:00"
    }
    fpsDropLimit {
      gameTypeId: 37
      itemId: 4
      weeklyDropLimit: 4000
      seasonalCostLimit: 0
      weeklyRefreshTime: "00:00:00"
    }
  }
  guideConf {
    dsGuide {
      modeId: 6001
      guideModeId: 201
      guideTaskId: 10000
    }
    dsGuide {
      modeId: 6001
      guideModeId: 202
      guideTaskId: 10001
    }
    dsGuide {
      modeId: 6001
      guideModeId: 203
      guideTaskId: 10002
    }
    dsGuide {
      modeId: 16001
      guideModeId: 1201
      guideTaskId: 10000
    }
    dsGuide {
      modeId: 16001
      guideModeId: 1202
      guideTaskId: 10001
    }
    dsGuide {
      modeId: 16001
      guideModeId: 1203
      guideTaskId: 10002
    }
    dsGuide {
      modeId: 6002
      guideModeId: 201
      guideTaskId: 10000
    }
    dsGuide {
      modeId: 6002
      guideModeId: 202
      guideTaskId: 10001
    }
    dsGuide {
      modeId: 6002
      guideModeId: 203
      guideTaskId: 10002
    }
    dsGuide {
      modeId: 16002
      guideModeId: 1201
      guideTaskId: 10000
    }
    dsGuide {
      modeId: 16002
      guideModeId: 1202
      guideTaskId: 10001
    }
    dsGuide {
      modeId: 16002
      guideModeId: 1203
      guideTaskId: 10002
    }
    dsGuide {
      modeId: 6101
      guideModeId: 204
      guideTaskId: 10000
    }
    dsGuide {
      modeId: 6101
      guideModeId: 205
      guideTaskId: 10001
    }
    dsGuide {
      modeId: 6101
      guideModeId: 206
      guideTaskId: 10002
    }
    dsGuide {
      modeId: 16101
      guideModeId: 1204
      guideTaskId: 10000
    }
    dsGuide {
      modeId: 16101
      guideModeId: 1205
      guideTaskId: 10001
    }
    dsGuide {
      modeId: 16101
      guideModeId: 1206
      guideTaskId: 10002
    }
    dsGuide {
      modeId: 6102
      guideModeId: 204
      guideTaskId: 10000
    }
    dsGuide {
      modeId: 6102
      guideModeId: 205
      guideTaskId: 10001
    }
    dsGuide {
      modeId: 6102
      guideModeId: 206
      guideTaskId: 10002
    }
    dsGuide {
      modeId: 16102
      guideModeId: 1204
      guideTaskId: 10000
    }
    dsGuide {
      modeId: 16102
      guideModeId: 1205
      guideTaskId: 10001
    }
    dsGuide {
      modeId: 16102
      guideModeId: 1206
      guideTaskId: 10002
    }
    limitDsCount: 3000
    guideTaskId: 10002
    defaultSwitch: 1
    lowVersion: "1.0.7.0"
  }
  rankConf {
    imageWeeklyRefreshTime: "Mon 00:00:00"
    seasonChangeShowIntervalMin: 5
    geoCodeSize {
      key: 1
      value: 4000
    }
    geoCodeSize {
      key: 2
      value: 1000
    }
    geoCodeSize {
      key: 3
      value: 100
    }
    geoCodeSize {
      key: 4
      value: 100
    }
    platDataSyncSize: 20000
    platDataSyncSize: 200
    platDataSyncSize: 200
    platDataSyncSize: 200
    dailySnapshotExpireDays: 3
    chaseConf {
      settleSize: 100
      expireDays: 10
      delayShowHours: 6
    }
  }
  luckyMoneyConf {
    shareLimitCount: 4
    receiveLimitCount: 2
    limitCount: 4
  }
  messageSlipConf {
    newestSlipLimitCount: 100
    hotSlipLimitCount: 100
    messageSlipLimitCount: 15
    messageSlipCdTimeSecond: 600
    messageCommentLimitCount: 99
    messageFavourLimitCount: 20
  }
  changeNameCostItem {
    itemId: 200010
    itemNum: 1
  }
  checkInPlanConf {
    makeUpTicketItemId: 4
    makeUpTicketItemNum: 2000
    stickerCDN: "www"
    checkCumDrawRewardNum: 4
    makeUpMaxTimes: 8
  }
  newStarLabelExpireTime: 259200000
  playerLabelSetMaxNum: 3
  playerPersonalityStateExpireTime: 259200000
  playerBackpackGridLimit: 10000
  chatConf {
    globalChatSendMsgLevelConstraint: 5
    globalChatServerNoticeMinIntervalMs: 2000
    globalChatServerNoticeMaxNumber: 10
    lobbyChatSendMsgLevelConstraint: 2
    lobbyChatSendMsgMinIntervalMs: 5000
    sayHiDailyCountLimit: 10
    sayHiMsgCountLimit: 1
    sayHiCoolDownSec: 259200
    sayHiAliveCountLimit: 30
    beGreetedDailyCountLimit: 10
    communityChannelSendMsgLevelConstraint: 5
    communityChannelMVPGameTypeID: 20
    communityChannelMVPGameTypeID: 702
    communityChannelMVPDurationSec: 86400
  }
  defaultPersonalityStateId: 1
  experienceDes: "参加排位赛获得历练值！参与排位赛<YellowCurrency>+15</>，进入第二轮<YellowCurrency>+30</>，进入第三轮<YellowCurrency>+80</>，进入最终关<YellowCurrency>+150</>"
  degreeConfig {
    degreeType: 4
    degreeID: 1
  }
  aiRandomFashionValues: 20
  aiRandomFashionValues: 45
  aiRandomFashionValues: 70
  aiRandomFashionValues: 95
  prayStaticConfig {
    DrawDailyLimit: 1
    LimitReachPrompt: "今日的祈福已达上限，留一些好运给明天吧！"
    DrawPrompt: "点击翻开您的专属幸运签哦"
    DrawShareResetDailyLimit: 1
    RandomPropertyNum: 1
  }
  setGenderColdDownTime: 172800000
  roomValidTime: 3600000
  IntegralCommonProtectItemId: 200018
  IntegralTeamProtectItemId: 200019
  IntegralCommonAdditionalItemId: 200020
  IntegralTeamAdditionalItemId: 200021
  BattleDropRewardActivityCoinAdditionalItemId: 200022
  BattleDropRewardExpAdditionalItemId: 200023
  overseamatchping: 200
  UGCTranslateWatingTime: 3000
  platPrivilegesQQ {
    key: 4
    value: 1
  }
  platPrivilegesWX {
    key: 4
    value: 1
  }
  fittingSlotIdWithNoOutlookGroup: 5
  newbieTaskExistDays: 30
  exchangeCD: 3
  preCreateRoleStartTime {
    seconds: 1701705600
  }
  preCreateRoleEndTime {
    seconds: 1702828799
  }
  platChannelQQ: 1001
  platChannelQQ: 10000144
  platChannelQQ: 10035116
  platChannelQQ: 10031280
  platChannelQQ: 10036618
  platChannelQQ: 10036619
  platChannelQQ: 10409435
  platChannelQQ: 10056349
  platChannelQQ: 10005462
  platChannelQQ: 2002
  platChannelQQ: 10440407
  platChannelQQ: 10033458
  platChannelQQ: 10018275
  platChannelQQ: 10034775
  platChannelQQ: 10028384
  platChannelQQ: 10053534
  platChannelQQ: 10012373
  platChannelQQ: 200000181
  platChannelQQ: 200000182
  platChannelQQ: 10029061
  platChannelQQ: 10053761
  platChannelQQ: 19000013
  platChannelQQ: 10297529
  platChannelQQ: 10035118
  platChannelQQ: 10227767
  platChannelQQ: 10439271
  platChannelQQ: 10001814
  platChannelQQ: 10014427
  platChannelQQ: 10029860
  platChannelQQ: 10464950
  platChannelQQ: 10406234
  platChannelQQ: 10029869
  platChannelQQ: 10445605
  platChannelQQ: 10401020
  platChannelQQ: 10380015
  platChannelQQ: 10183224
  platChannelQQ: 10358399
  platChannelQQ: 10358398
  platChannelQQ: 10280751
  platChannelQQ: 10227421
  platChannelQQ: 10175063
  platChannelQQ: 10173418
  platChannelQQ: 10136642
  platChannelQQ: 10136641
  platChannelQQ: 10136640
  platChannelQQ: 10136639
  platChannelQQ: 10057091
  platChannelQQ: 10053426
  platChannelQQ: 10045813
  platChannelQQ: 10045814
  platChannelQQ: 10045815
  platChannelQQ: 10045812
  platChannelQQ: 10041206
  platChannelQQ: 10034858
  platChannelWX: 10000145
  platChannelWX: 10012729
  platChannelWX: 10014428
  platChannelWX: 10026482
  platChannelWX: 10035673
  platChannelWX: 10045816
  platChannelWX: 10045817
  platChannelWX: 10045818
  platChannelWX: 10045819
  platChannelWX: 10045820
  platChannelWX: 10056360
  platChannelWX: 10056361
  platChannelWX: 10056362
  platChannelWX: 10056363
  platChannelWX: 10056364
  platChannelWX: 10160288
  platChannelWX: 10439218
  platChannelWX: 10439221
  platChannelWX: 10439222
  platChannelWX: 10439223
  platChannelWX: 10439224
  platChannelWX: 10439225
  platChannelWX: 10439226
  platChannelWX: 10439227
  platChannelWX: 10439228
  platChannelWX: 10439229
  platChannelWX: 1001
  platChannelWX: 10005462
  platChannelWX: 2002
  platChannelWX: 10440407
  platChannelWX: 10033458
  platChannelWX: 10018275
  platChannelWX: 10034775
  platChannelWX: 10028384
  platChannelWX: 10053534
  platChannelWX: 10012373
  platChannelWX: 200000181
  platChannelWX: 200000182
  platChannelWX: 10029061
  platChannelWX: 10053761
  platChannelWX: 19000013
  platChannelWX: 10297529
  platChannelWX: 10464950
  platChannelWX: 10406234
  platChannelWX: 10029869
  platChannelWX: 10445605
  platChannelWX: 10401020
  platChannelWX: 10380015
  platChannelWX: 10183224
  platChannelWX: 10358399
  platChannelWX: 10358398
  platChannelWX: 10280751
  platChannelWX: 10227421
  platChannelWX: 10175063
  platChannelWX: 10173418
  platChannelWX: 10136642
  platChannelWX: 10136641
  platChannelWX: 10136640
  platChannelWX: 10136639
  platChannelWX: 10057091
  platChannelWX: 10053426
  platChannelWX: 10045813
  platChannelWX: 10045814
  platChannelWX: 10045815
  platChannelWX: 10045812
  platChannelWX: 10041206
  platChannelWX: 10034858
  ChampionBag: 89900078
  seasonExpireCurrency: 7
  seasonExpireCurrency: 101
  seasonExpireCurrency: 102
  seasonExpireCurrency: 103
  seasonExpireCurrency: 104
  seasonExpireCurrency: 201
  seasonExpireCurrency: 202
  seasonExpireCurrency: 208
  seasonExpireCurrency: 209
  seasonExpireCurrency: 3201
  seasonExpireCurrency: 210
  seasonExpireCurrency: 2068
  seasonExpireCurrency: 212
  seasonExpireCurrency: 214
  seasonExpireCurrency: 3560
  qrCodeDailyAcquireLimit: 30
  joinMidwayConf {
    friendsTopN: 10
    battleSvrNum: 1
  }
  wolfKillDefaultScore: 100
  wolfKillMinScore: 70
  wolfKillMaxRecord: 100
  wolfKillLoginScore: 10
  hidePlayerStatusWeekCnt: 8
  chatTypeWithoutCreditScore: 11
  chatTypeWithoutCreditScore: 19
  lbsRankDefaultSubs {
    key: 1
    value: 321081
  }
  hidePlayerStatusOfflineLimit: 3600
  lbsConf {
    nearbyRadius: 1
    nearbyRadius: 10
    nearbyRadius: 100
  }
  battleResMgrAllowResNames: "AIInfoDifficultyData"
  battleResMgrAllowResNames: "JSReliableAIConfig"
  battleResMgrAllowResNames: "AIScriptConfig"
  GatherStarActivityId: 197
  seasonRegisterTaskGroupConf {
    seasonId: 2
    taskGroupId: 14002
    taskGroupId: 10003
    taskGroupId: 13135
  }
  seasonRegisterTaskGroupConf {
    seasonId: 3
    taskGroupId: 14003
    taskGroupId: 10004
    taskGroupId: 13235
  }
  seasonRegisterTaskGroupConf {
    seasonId: 4
    taskGroupId: 10005
    taskGroupId: 13335
    taskGroupId: 14004
  }
  seasonRegisterTaskGroupConf {
    seasonId: 5
    taskGroupId: 10006
    taskGroupId: 13435
    taskGroupId: 14005
    taskGroupId: 15103
  }
  seasonRegisterTaskGroupConf {
    seasonId: 6
    taskGroupId: 14006
    taskGroupId: 13535
    taskGroupId: 10007
    taskGroupId: 15104
  }
  seasonRegisterTaskGroupConf {
    seasonId: 7
    taskGroupId: 15105
    taskGroupId: 14007
    taskGroupId: 10008
    taskGroupId: 13635
  }
  seasonRegisterTaskGroupConf {
    seasonId: 8
    taskGroupId: 15106
    taskGroupId: 14008
    taskGroupId: 10009
    taskGroupId: 13735
  }
  seasonRegisterTaskGroupConf {
    seasonId: 9
    taskGroupId: 15107
    taskGroupId: 14009
    taskGroupId: 10010
    taskGroupId: 13835
  }
  seasonRegisterTaskGroupConf {
    seasonId: 10
    taskGroupId: 15108
    taskGroupId: 14010
    taskGroupId: 10011
    taskGroupId: 13935
  }
  seasonRegisterTaskGroupConf {
    seasonId: 11
    taskGroupId: 15109
    taskGroupId: 14011
    taskGroupId: 10012
    taskGroupId: 14035
  }
  seasonRegisterTaskGroupConf {
    seasonId: 12
    taskGroupId: 15110
    taskGroupId: 14012
    taskGroupId: 10013
    taskGroupId: 14135
  }
  seasonRegisterTaskGroupConf {
    seasonId: 13
    taskGroupId: 15111
    taskGroupId: 14013
    taskGroupId: 10014
    taskGroupId: 14235
  }
  protectedScoreOpenConfig {
    ProtectedScoreOpenTimestamp {
      seconds: 1706198400
    }
    protectedScoreOpenMinVersion: "1.2.67.1"
  }
  UGCCustomModelLimitCount: 80
  springPrayActivityResult: 100201
  springPrayActivityResult: 100202
  SupplyActivityNoonTaskID: 515254
  SpringPrayActivityID: 240
  UGCCustomModelingOpened: true
  levelChooserMaxHistory: 10
  stickFriendMax: 5
  monthcardDemandMaxNum: 0
  monthcardMaxDays: 360
  onemonthcardDays: 30
  wolfKillSingleReportBatch: 3
  wolfKillReportEndTime: 10
  wolfKillReportSubScore: 10
  wolfKillDirectReport: 2
  ugcInAppPurchaseProtoVersion: 1
  ugcInAppPurchaseProtoContent: "《元梦之星》开发者奖励协议\n甲方：深圳市腾讯天游科技有限公司\n乙方：创作者\n \n鉴于：\n1. 甲方是游戏《元梦之星》在中国大陆地区的运营方，且甲方向游戏《元梦之星》用户提供了可在游戏内用于制作游戏、玩法等应用的游戏编辑器相关服务。\n2. 乙方是游戏《元梦之星》的用户/开发者，有意或已在游戏《元梦之星》中的造梦空间模块进行游戏及玩法的编辑、开发、制作（下称“开发者作品”），并将上述游戏、玩法于元梦之星中的星世界模块（又称“造梦空间”）进行发布和运行，乙方已同意《元梦之星游戏开发者平台服务协议》（网址：【https://ymzx.qq.com/cp/web20240319/index.shtml】 ）的全部内容。\n3. 乙方有意将乙方创作的开发者作品（又称“建造/开发内容”）授权甲方可在游戏内让其他用户付费购买和使用，并希望通过在甲方运营的《元梦之星》平台注册对其建造/开发内容进行管理。\n甲乙双方经平等、友好协商，订立条款如下，以资共同信守。\n\n一、定义\n1.1 《元梦之星》开发者服务提供方：指向乙方提供腾讯游戏《元梦之星》相关服务的深圳市腾讯天游科技有限公司，在本协议中简称为“腾讯”或“甲方”。\n1.2 相关服务/平台：又称“元梦之星开发者服务平台”或“元梦之星平台”，指由腾讯为乙方提供的用于制作游戏、玩法等应用的游戏编辑器，乙方可利用该工具于《元梦之星》中的造梦空间模块进行游戏及玩法的编辑、开发、制作并进行发布和运行。\n1.3 开发者作品：又称“建造/开发内容”，包括乙方使用《元梦之星》造梦空间模块制作、产出的素材、组件、模板、玩法地图等相关内容（包括个别元素、阶段性半成品、成品等）等在使用本服务过程中产生、生成的全部成果或内容，（以下合称“开发者作品”或“作品”，下同）。\n1.4 其他名词定义与《元梦之星游戏开发者平台服务协议》一致。\n\n二、建造/开发内容的管理\n2.1 乙方可使用《元梦之星》造梦空间相关功能对建造/开发内容进行管理，包括但不限于可选择建造/开发内容的定价。甲方有权对乙方选择的定价进行审核，如审核不通过，甲方可拒绝乙方发布建造/开发内容的需求。\n\n三、甲方权利义务\n3.1 甲方负责游戏《元梦之星》和《元梦之星》造梦空间的运营，包括但不限于甲方有权对游戏和平台的功能进行增减或调整。\n3.2 甲方保证拥有运营《元梦之星》及《元梦之星》造梦空间所具备的所有资质。\n3.3 甲方有权基于市场及用户反馈等情况要求乙方对建造/开发内容进行改进，乙方应配合甲方的要求对建造/开发内容进行改进。\n\n四、乙方权利义务\n4.1 乙方应保证建造/开发内容合法合规，不违反《元梦之星游戏开发者平台服务协议》规定的内容。\n4.2 乙方不得干涉甲方对游戏《元梦之星》和《元梦之星》造梦空间的运营事宜。\n4.3 乙方不得对游戏《元梦之星》和《元梦之星》造梦空间进行破解、反编辑等行为。\n\n五、奖励规则与支付\n5.1 乙方在没有任何违反本协议、《元梦之星游戏开发者平台服务协议》 和《腾讯游戏许可及服务协议》 的前提下，甲方向乙方支付奖励。\n5.2 乙方可得奖励：\n乙方可得奖励=乙方建造/开发内容通过所有渠道获得的可结算净收益×【100】%\n其中：可结算净收益为《元梦之星》游戏用户为购买乙方建造/开发内容通过所有渠道所实际支付的费用在扣除渠道成本费用，运营费用，坏账，文化事业建设费（如适用）以及用户退款后的净收益。\n5.3 特别激励\n自本协议签署日起至特别激励期结束，特别激励期相关时间和奖励方式以甲方在《元梦之星》平台发布的活动规则/或通知为准。\n5.4 结算周期按月\n甲方在每个自然月周期结束后，在第“M+1”月的第十个工作日之前向乙方提供第M月的可结算净收益，即《星元宝收益账单》。甲方在提供《星元宝收益账单》，并收到乙方提供的合法有效且金额相符的发票（发票的开具要求具体请见第5.12条的约定）15个工作日内将游戏《元梦之星》商业化上线期间产生的乙方可得奖励支付至乙方账户，乙方可得奖励以人民币结算。\n由于来自APP Store的渠道收入首先应由苹果公司按照“M+2”月的周期向甲方进行结算，即甲方是在未确定APP Store渠道在第M月的实际渠道成本费用，运营费用，坏账，文化事业建设费（如适用）以及用户退款的数额情况下在第“M+2”月预先向乙方支付了第M月的乙方可得奖励。如在确定APP Store渠道在第M月的实际渠道成本费用，运营费用，坏账，文化事业建设费（如适用）以及用户退款的数额后发现甲方支付给乙方的M月的乙方可得奖励多付了，甲方可在下个月应支付给乙方的乙方可得奖励中扣除多付的部分，如不足扣除的，乙方应按照甲方要求的时间和方式支付给甲方。\n5.5 甲方可能根据实际需要对乙方可得奖励计算方式进行修改和变更，前述修改、变更前，甲方将在相应服务页面进行通知或公告。如果乙方不同意上述修改、变更，则应在甲方进行通知或者公告后7日内向甲方提出书面异议，否则即视为乙方同意上述修改、变更。\n5.6 甲方约定向乙方支付的结算金额，包含乙方应缴纳的所有税费。双方应按照适用法律负责支付其应承担的税费。特别地，\n如乙方为个人，甲方将基于合同约定的支付时点按现行有效的法规，履行个人所得税和增值税（如适用）的扣缴义务。个人实收金额为协议约定的结算金额扣减掉甲方代扣代缴个人所得税和增值税（如适用）后的剩余金额。若在履行协议义务过程中相关法规发生变动、更新或出台新法规（以下合称新法规），甲方仅就新法规生效时点于协议同下尚未支付的款项，负有按新法规履行相关税费的扣缴的义务（如有）。对于新法规定生效前甲方已完成支付的款项，若按新法规涉及任何税款调整事宜（包括不限于计税方法及税率变更、所得综合申报、汇算清缴等），双方确认由乙方自行履行相关税款更正申报义务，并负责处理相关税款的补缴或退还，概与甲方无关。为配合甲方完成个税扣缴事宜，乙方须及时提供个人姓名、身份证号等信息，并自行确保信息的准确性和完整性。\n如乙方为企业，若依照中国法律，甲方需要依法负责从支付给乙方的款项中代扣代缴任何税费（包括但不限于增值税和企业所得税），乙方授权甲方可根据适用的税法法规向相关税务机关支付代扣代缴的相关税费，并仅需将扣缴完税费后的余额支付给乙方。\n如双方签订本协议后，税务相关法律法规发生变更或税务机关对税务相关法律法规的解释和要求发生改变（以下合称“税法变动”），导致甲方在本协议项下的涉税条款的相关权利和义务发生改变（包括但不限于代扣代缴义务、税款计算方法、税款申报缴纳期限等税务合规事宜的改变），甲方有权在合法合规的范围内进行调整而无需获得乙方的同意。\n5.7 本协议已约定甲方向乙方支付的全部款项，除此之外，非经甲方书面同意，乙方不得要求甲方向其支付任何其他费用，包括但不限于版权使用费等费用。\n5.8 经甲方通知后，甲方有权从乙方的可得奖励中直接扣除乙方应向甲方支付的赔偿金、违约金、政府部门罚款以及应向甲方用户支付的补偿金、赔偿金等款项。\n5.9 乙方同意，在计算可纯净收益时，如游戏用户就已充值的道具或相关游戏申请退款，甲方有权根据实际情况进行相应的扣除。\n5.11 乙方同意，甲方基于本协议应支付给乙方的奖励结算，应以人民币结算。\n5.12 乙方知悉并确认，在甲方支付本协议项下的结算金额之前，乙方需根据第5.13条所约定的开票信息向甲方开具发票，并及时按照第5.14条的约定方式邮寄或上传相关资料给甲方。具体如下：\n如乙方为境内个人，乙方须自行委托税局代开增值税普通发票。\n如乙方为境内企业，乙方须向甲方提供税率为6%的增值税专用发票。若乙方不能提供税率为6%的增值税专用发票，双方实际结算金额将根据如下公式进行确定：实际结算金额=原含税金额/（1+6%）*（1+调整后的税率差额）。其中，如乙方开具增值税普通发票的，调整后的税率差额为0；如乙方开具税率为1%的增值税专用发票的，则调整后的税率差额为1%；如乙方开具税率为3%的增值税专用发票的，则调整后的税率差额为3%。\n如乙方为境外个人或企业，乙方须向甲方开具与结算金额等额的Invoice。Invoice须列示含税金额。\n5.13 开票信息：\n公司名称：深圳市腾讯天游科技有限公司\n纳税人识别号：91440300MA5GCKGN0H\n公司地址：深圳市宝安区新安街道海滨社区宝兴路6号海纳百川总部大厦A座14层1405\n电话： 0755-86013388\n开户银行：招商银行深圳汉京中心支行\n银行账号：755951759810809  \n开票内容：授权费\n5.14 发票邮寄/上传信息：\n乙方为境（内）外个人或企业，进行结算操作时，甲方将通过《元梦之星》平台的邮箱/站内信等形式向乙方游戏账号发送结算相关资料。\n5.15 乙方应按照甲方结算系统中的结算流程提交资料。甲方收到资料并确认无误后，将按照本附件中的结算方式向乙方进行支付。\n5.16 关于收益结算的任何疑问，乙方可通过联系客服提交问题。\n\n六、知识产权及数据\n6.1 甲乙双方确认，开发者作品相关知识产权的约定遵从《元梦之星游戏开发者平台服务协议》 的约定。\n\n七、合作时间\n7.1 本协议从签署日即【2024】年【4】月【26】日起生效，有效期一年。如到期前30日内，甲方未提发出终止合作的通知，则本协议有效期顺延一年。\n\n八、协议终止及违约责任\n8.1 无论因何种原因导致本协议终止，乙方应配合甲方要求做好建造/开发内容的后续处理工作。\n8.2 如乙方违反本协议约定，甲方有权视违约情节的严重程度采取下列任一种或几种措施追究乙方的违约责任：\n（1）暂停向乙方结算乙方应得奖励；和/或\n（2）将建造/开发内容下架；和/或\n（3）将应当支付而尚未支付乙方应得奖励作为乙方支付甲方的违约金，直接予以扣减而不再支付乙方； 和/或\n（4）要求乙方赔偿甲方因乙方的违约行为而遭受的一切损失；和/或\n（5）单方面解除本协议。\n\n九、保密\n9.1 保密信息：是指任一方（\"接收方\"）可能会接触到的，另一方（\"披露方\"）尚未向公众公开的信息，包括但不限于任一方之营业秘密，商务机密，与技术有关之知识及信息、创意、设想、方案，提供的物品或厂商资料，客户资料，人事资料，商业计划，促销及行销活动，财务状况及其它商务活动等。保密信息呈现之形式及载体包括但不限于书面或口头、文件、磁盘、磁盘片、光盘片、电子邮件、电磁纪录、报告、文字往来、录音带、录像带、笔记、图纸、模型、规格说明、汇编文件、计算机程序及其它媒体。\n9.2 未经对方书面许可，任何一方不得向第三方披露有关本协议项下内容的任何信息及双方因履行本协议而获知的对方的保密信息，否则，接收方须承担因单方公开相关内容而对披露方造成的一切损失。为本协议合作约定或经披露方书面同意的使用除外。\n9.3 接收方根据有关法律规定必须向相关权力机构（如政府部门、证券交易所或其它监管机构）披露的，应提前书面通知披露方，并与披露方真诚合作将披露范围及影响程度减到最小。\n9.4 本协议期满后，本保密条款仍具有法律效力；保密义务直到另一方同意其解除此项义务，或相关信息已合法进入公共领域为止。\n9.5 特别地，双方为履行本协议而进行的沟通内容属于保密信息，未经甲方同意，乙方不得披露。\n\n十、通知\n10.1除非各方另有约定，甲方向乙方发出的通知可以通过快递服务、电子邮箱或挂号邮件、《元梦之星》平台站内信方式、《元梦之星》官网、乙方在《元梦之星》平台登记的地址/信息。乙方向甲方发出的通知可以通过甲方在《元梦之星》平台公布的电子邮箱方式传送至甲方 。\n\n十一、其他\n11.1 本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，则该条款被视为删除，但本协议的其余条款仍应有效并且有约束力。\n11.2 本协议的签订地为深圳市南山区，因执行本协议所发生的争议或与本协议有关的一切争议，双方应通过友好协商解决。如协商不成，则双方应将争议提交至本协议签订地深圳市南山区有管辖权的人民法院诉讼解决。\n11.3 本协议适用中华人民共和国大陆地区法律、法规（冲突法除外）。\n11.4 本协议未尽事宜由甲乙双方友好协商后，以书面形式加以补充，该补充协议与本协议具有同等法律效力。\n11.5 未经甲方事先书面同意，乙方方不得转让其在本协议项下的权利或义务（无论是全部或部分）给第三方。"
  wolfKillReportSub: 2
  ugcInAppPurchaseGoodsLimit: 100
  teamBattleBroadcastConf {
    conBattleCount: 3
    conWinBattleCount: 1
    goodRank: 3
    svpRankPercentValue: 25
    specialPromotedRound: 3
    specialPromotedRankPercentValue: 25
  }
  albumConf {
    picCountLimit: 100
    newPicCountLimit: 100
    dataMigrateLowestVersion: "1.3.26.1"
    tempPicCountLimit: 50
    atFriendLowestVersion: "1.3.68.1"
    atFriendShowLimit: "10"
    atFriendPicListLimit: 10
    friendAtPicListLimit: 10
    showAlbumLimitTimes: 3
    editTextLength: 10
  }
  seasonFashionMisConf {
    seasonFashionDefaultStatus: FSS_Show
  }
  supercoreActivityIconResourceName: "T_Common_Icon_Coin_02.astc;T_Common_Icon_Coin_32.astc;T_Common_Icon_Coin_53.astc;T_CutGift_Icon_Pack_05.astc"
  teamInfoShowMisConf {
    teamMemberCountDefaultStatus: FSS_Show
  }
  bagCombinationItemsNumLimit: 40
  friendRecommendConf {
    userSetCDDays: 1
    autoCDHours: 1
  }
  ActivityHYNResendMailId: 45
  DressDefaultItems: 740001
  DressDefaultItems: 240001
  DressDefaultItems: 240201
  DressDefaultItems: 241001
  DressDefaultItems: 241401
  DressDefaultItems: 241201
  pwd2 {
    forceCloseWaitTimeSec: 259200
    passwordLessDurationTimeSec: 3600
  }
  chatNotifyIntimacyLevel: 100
  roomPinCodeAliveLength: 300000
  needRecordUsedCountItemIds: 200101
  needRecordUsedCountItemIds: 200102
  wolfKillMaxFeedbackCount: 5
  SPInfoDisplayInPlayerInfoUIStartTime {
    seconds: 4088332800
  }
  BIScenePackageParam {
    moneyType: 1
    moneyType: 2
    moneyType: 3
  }
  stickRelationshipMax: 6
  wolfKillMaxSeasonRewardCount: 10
  activityWXInviteOffDay: 3
  raffleConf {
    testWaterExpireMailId: 229
    amsWaitSec: 3
    tagDailyLimitCondition {
      historyRechargeCount: 0
      dailyRechargeCount: 0
    }
  }
  LowestFarmLevelForVADownloadPushFace: 4
  wolfKillPassiveTime: 120
  wolfKillPassiveScore: 10
  wolfKillTextScore: 5
  wolfKillVoiceScore: 5
  wolfKillActionScoreDefault: 1000
  wolfKillActionScoreMin: 0
  wolfKillActionScoreMax: 2000
  seasonChangeAutoUseItems: 310261
  seasonChangeAutoUseItems: 310262
  seasonChangeAutoUseItems: 310274
  seasonChangeAutoUseItems: 310275
  seasonChangeAutoUseItems: 310295
  seasonChangeAutoUseItems: 310296
  seasonChangeAutoUseItems: 310315
  seasonChangeAutoUseItems: 310316
  seasonChangeAutoUseItems: 310325
  seasonChangeAutoUseItems: 310326
  seasonChangeAutoUseItems: 310343
  seasonChangeAutoUseItems: 310344
  wolfKillTextActionScore: 50
  wolfKillVoiceActionScore: 50
  wolfKillCommunicateActionScore: 10
  wolfKillCommunicateActionScore: 20
  wolfKillCommunicateActionScore: 30
  wolfKillPassiveActionScore: 10
  wolfKillPassiveActionScore: 20
  wolfKillPassiveActionScore: 30
  wolfKillReLoginActionScore: 20
  levelSelfEvaluationLengthLimits: 30
  DressItemInfoType: 104
  DressItemInfoType: 105
  DressItemInfoType: 107
  DressItemInfoType: 108
  DressItemInfoType: 109
  DressItemInfoType: 110
  DressItemInfoType: 113
  DressItemInfoType: 115
  DressItemInfoType: 116
  DressItemInfoType: 3000
  DressItemInfoType: 306
  DressItemInfoType: 4002
  DressItemInfoType: 4100
  DressItemInfoType: 4101
  DressItemInfoType: 4102
  DressItemInfoType: 4103
  DressItemInfoType: 4104
  DressItemInfoType: 4105
  DressItemInfoType: 5000
  DressItemInfoType: 5001
  DressItemInfoType: 5002
  DressItemInfoType: 5003
  DressItemInfoType: 5004
  DressItemInfoType: 5005
  DressItemInfoType: 5006
  DressItemInfoType: 6001
  DressItemInfoType: 6002
  PlaySwitchDaysLimit: 10
  UpdateRewards: 2007
  wolfKillComeBackDays: 14
  wolfKillComeBackTrigerDays: 30
  wolfKillTreasureAddNum: 580
  wolfKillTreasureItemId: 240999
  lobbyListPageCount: 10
  seasonRaffle {
    key: 30013
    value: 70
  }
  SmallCapacity: 999
  messageTipSpringFestivalShowTime: 5
  ugcRankScoreKeyName: "MRD_UGC_RANK_SCORE"
  SeasonReviewMinimumRange: 0.05
  SeasonReviewCondition: 1
  SeasonReviewCondition: 3
  useMultiScriptIDMatchIDs: 5
  useMultiScriptIDMatchIDs: 6
  showMoveActionBeginTime {
    seconds: 1743782400
  }
  showMoveActionEndTime {
    seconds: 4080374400
  }
  isMewMewBombOpen: true
  isMewMewDanceCopyPoseOpen: true
  wolfKillMonthCardDay: 30
  wolfKillMonthCardCost: 168
  wolfKillMonthCardMaxDay: 360
  wolfKillMonthCardExperienceGift: 330801
  wolfKillMonthCardExclusiveGift: 310801
  MCGMeowMeowBeginTime {
    seconds: 1746115200
  }
  MCGMeowMeowEndTime {
    seconds: 1750003199
  }
  mewMewSpinelconStarTime: 0
  mewMewSpinelconStarTime: 60
  mewMewSpinelconSecondTime: 200
  mewMewSpinelconSecondTime: 240
  mewMewSpinelocnMinTime: 1800
  mewMewOpencondition: 321
  mewMewOpencondition: 5080
  limitItemExperienceFreshTime: 5
  ugcStarWorldPlatformBpExpItemConf {
    key: 11
    value: 269011
  }
  ugcStarWorldPlatformBpExpItemConf {
    key: 12
    value: 269012
  }
  playerFarmDailyBigAwardLevelLimit: 4
  playerUpgradeCheckLastDayBeginTime {
    seconds: 1748448000
  }
  playerUpgradeCheckLastDayEndTime {
    seconds: 1748534399
  }
  suitIdSkillData: "410660,技能,/Game/LetsGo/Assets/Characters/OG/OG_043/Anim/Out/AS_CH_IdleShow_OG_043_Preview.AS_CH_IdleShow_OG_043_Preview,1,2,T_Tab_img_SkillBg01,1,41066001;410661,技能,/Game/LetsGo/Assets/Characters/OG/OG_043/Anim/Out/AS_CH_IdleShow_OG_043_Preview_HP01.AS_CH_IdleShow_OG_043_Preview_HP01,1,2,T_Tab_img_SkillBg03,1,41066101;410662,技能,/Game/LetsGo/Assets/Characters/OG/OG_043/Anim/Out/AS_CH_IdleShow_OG_043_Preview_HP02.AS_CH_IdleShow_OG_043_Preview_HP02,1,2,T_Tab_img_SkillBg02,1,41066201;411660,技能,/Game/LetsGo/Assets/Characters/OG/OG_051/Anim/Out/AS_CH_Transform_OG_051.AS_CH_Transform_OG_051,1,1,,1,41166001;411661,技能,/Game/LetsGo/Assets/Characters/OG/OG_051/Anim/Out/AS_CH_Transform_OG_051.AS_CH_Transform_OG_051,1,1,,1,41166101;411662,技能,/Game/LetsGo/Assets/Characters/OG/OG_051/Anim/Out/AS_CH_Transform_OG_051.AS_CH_Transform_OG_051,1,1,,1,41166201;411670,技能,/Game/LetsGo/Assets/Characters/OG/OG_051/Anim/Out/AS_CH_Transform_OG_051.AS_CH_Transform_OG_051,1,1,,1,41167001;411671,技能,/Game/LetsGo/Assets/Characters/OG/OG_051/Anim/Out/AS_CH_Transform_OG_051.AS_CH_Transform_OG_051,1,1,,1,41167101;411672,技能,/Game/LetsGo/Assets/Characters/OG/OG_051/Anim/Out/AS_CH_Transform_OG_051.AS_CH_Transform_OG_051,1,1,,1,41167201"
  teamRecruitModeTypeVersion: "1.6.5924.1"
  UgcDressItemInfoType: 104
  UgcDressItemInfoType: 107
  UgcDressItemInfoType: 109
  UgcDressItemInfoType: 4002
}

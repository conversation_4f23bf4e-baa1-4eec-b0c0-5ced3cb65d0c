com.tencent.wea.xlsRes.table_RankingDisplayConfData
excel/xls/P_排行榜.xlsx sheet:展示配置
rows {
  labelId: 1
  name: "段位排行"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 0
  delayDays: 0
  tipsSwitch: 1
  tipsId: "UI_RankTag_QualityRule"
  tipsId: "UI_RankTag_QualityDesc"
  firstTabShowIndex: 1
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Arena
  rankingGroupList: RG_Chase
}
rows {
  labelId: 2
  name: "时尚排行"
  isActive: true
  firstTabIndex: 2
  secondTabIndex: 0
  delayDays: 0
  tipsSwitch: 1
  tipsId: "UI_RankTag_FashionRule"
  tipsId: "UI_RankTag_FashionDesc"
  firstTabShowIndex: 2
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
}
rows {
  labelId: 3
  name: "地图排行"
  isActive: true
  firstTabIndex: 3
  secondTabIndex: 0
  tipsSwitch: 1
  tipsId: "UI_RankTag_LevelMpaRule"
  tipsId: "UI_RankTag_LevelMpaDesc"
  firstTabShowIndex: 5
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
}
rows {
  labelId: 4
  name: "竞速"
  isActive: true
  firstTabIndex: 3
  secondTabIndex: 1
  delayDays: 0
  rankIds: 1002
  rankIds: 1001
  rankIds: 1005
  rankIds: 1007
  rankIds: 1008
  rankIds: 1009
  rankIds: 1010
  rankIds: 1011
  rankIds: 1012
  rankIds: 1013
  rankIds: 1014
  rankIds: 1016
  rankIds: 1018
  rankIds: 1019
  rankIds: 1020
  rankIds: 1021
  rankIds: 1023
  rankIds: 1024
  rankIds: 1025
  rankIds: 1026
  rankIds: 1028
  rankIds: 1029
  rankIds: 1030
  rankIds: 1031
  rankIds: 1032
  rankIds: 1033
  rankIds: 1034
  rankIds: 1035
  rankIds: 1036
  rankIds: 1037
  rankIds: 1038
  rankIds: 1039
  rankIds: 1040
  rankIds: 1041
  rankIds: 1043
  rankIds: 1044
  rankIds: 1045
  rankIds: 1046
  rankIds: 1047
  rankIds: 1063
  rankIds: 1064
  rankIds: 1065
  rankIds: 1066
  rankIds: 1067
  rankIds: 1068
  rankIds: 1076
  rankIds: 1077
  rankIds: 1078
  rankIds: 1079
  rankIds: 1080
  rankIds: 1081
  rankIds: 1082
  rankIds: 1083
  rankIds: 1084
  rankIds: 1085
  rankIds: 1086
  rankIds: 1087
  rankIds: 1088
  rankIds: 1089
  rankIds: 1090
  rankIds: 1091
  rankIds: 1092
  rankIds: 1093
  rankIds: 1094
  rankIds: 1095
  rankIds: 1096
  rankIds: 1097
  rankIds: 1098
  rankIds: 1099
  rankIds: 1100
  rankIds: 1104
  rankIds: 1105
  rankIds: 1106
  rankIds: 1107
  rankIds: 1108
  rankIds: 1109
  rankIds: 1121
  rankIds: 1122
  rankIds: 1123
  rankIds: 1124
  rankIds: 1125
  rankIds: 1126
  rankIds: 1127
  rankIds: 1128
  rankIds: 1129
  rankIds: 1130
  rankIds: 1141
  rankIds: 1142
  rankIds: 1143
  rankIds: 1144
  rankIds: 1145
  rankIds: 1146
  rankIds: 1147
  rankIds: 1148
  rankIds: 1149
  rankIds: 1150
  rankIds: 1154
  rankIds: 1160
  rankIds: 1168
  rankIds: 1169
  rankIds: 1170
  rankIds: 1171
  rankIds: 1172
  rankIds: 1175
  rankIds: 1176
  rankIds: 1177
  rankIds: 1178
  rankIds: 1179
  rankIds: 1180
  rankIds: 1181
  rankIds: 1182
  rankIds: 1183
  rankIds: 1185
  rankIds: 1186
  rankIds: 1187
  rankIds: 1188
  rankIds: 1189
  rankIds: 1190
  rankIds: 1191
  rankIds: 1192
  rankIds: 1193
  rankIds: 1194
  rankIds: 1196
  rankIds: 1197
  rankIds: 1198
  rankIds: 1199
  rankIds: 1200
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  CustomUI: "UI_Rank_CustomUI_Map"
  DataUI: "UI_Rank_DataUI_Map"
}
rows {
  labelId: 5
  name: "生存"
  isActive: false
  firstTabIndex: 3
  secondTabIndex: 2
  delayDays: 0
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
}
rows {
  labelId: 6
  name: "星世界创作"
  isActive: true
  firstTabIndex: 4
  secondTabIndex: 0
  delayDays: 0
  tipsSwitch: 1
  tipsId: "Leaderboard_Tips_Title_StarCreate"
  tipsId: "Leaderboard_Tips_Desc_StarCreate"
  higherVersion: "*******"
  firstTabShowIndex: 6
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
}
rows {
  labelId: 7
  name: "积分"
  isActive: true
  firstTabIndex: 3
  secondTabIndex: 3
  delayDays: 0
  rankIds: 1069
  rankIds: 1070
  rankIds: 1071
  rankIds: 1073
  rankIds: 1074
  rankIds: 1075
  rankIds: 1101
  rankIds: 1102
  rankIds: 1103
  rankIds: 1110
  rankIds: 1142
  rankIds: 1151
  rankIds: 1153
  rankIds: 1173
  rankIds: 1174
  rankIds: 1184
  rankIds: 1195
  rankIds: 1201
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  CustomUI: "UI_Rank_CustomUI_Map"
  DataUI: "UI_Rank_DataUI_Map"
}
rows {
  labelId: 8
  name: "赛季\n创作榜"
  isActive: true
  firstTabIndex: 14
  secondTabIndex: 3
  delayDays: 0
  rankIds: 101
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: false
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_XSJCreate"
}
rows {
  labelId: 9
  name: "历史"
  isActive: false
  firstTabIndex: 14
  secondTabIndex: 7
  delayDays: 0
  rankIds: 102
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: false
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_XSJCreate"
}
rows {
  labelId: 10
  name: "每周\n创作榜"
  isActive: true
  firstTabIndex: 14
  secondTabIndex: 4
  delayDays: 0
  rankIds: 103
  tipsSwitch: 0
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: false
  CanShowGeo: false
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_XSJCreate"
}
rows {
  labelId: 11
  name: "每日\n创作榜"
  isActive: true
  firstTabIndex: 14
  secondTabIndex: 5
  delayDays: 0
  rankIds: 104
  tipsSwitch: 0
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: false
  CanShowGeo: false
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_XSJCreate"
}
rows {
  labelId: 12
  name: "资源\n创作榜"
  isActive: true
  firstTabIndex: 14
  secondTabIndex: 6
  delayDays: 0
  rankIds: 105
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: false
  CanShowGeo: false
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_XSJCreate"
}
rows {
  labelId: 13
  name: "星世界探索"
  isActive: true
  firstTabIndex: 5
  secondTabIndex: 0
  delayDays: 0
  tipsSwitch: 1
  tipsId: "Leaderboard_Tips_Title_StarWorld"
  tipsId: "Leaderboard_Tips_Desc_StarWorld"
  lowerVersion: "********"
  higherVersion: "*******"
  firstTabShowIndex: 7
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
}
rows {
  labelId: 14
  name: "通关"
  isActive: true
  firstTabIndex: 14
  secondTabIndex: 8
  delayDays: 0
  rankIds: 106
  tipsSwitch: 0
  higherVersion: "********"
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: false
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_XSJExplore"
}
rows {
  labelId: 15
  name: "精选\n通关榜"
  isActive: true
  firstTabIndex: 14
  secondTabIndex: 1
  delayDays: 0
  rankIds: 107
  tipsSwitch: 0
  lowerVersion: "********"
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: false
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_XSJExplore"
}
rows {
  labelId: 16
  name: "闯关\n挑战榜"
  isActive: true
  firstTabIndex: 14
  secondTabIndex: 2
  delayDays: 0
  rankIds: 108
  tipsSwitch: 0
  lowerVersion: "********"
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: false
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_XSJExplore"
}
rows {
  labelId: 18
  name: "天天\n晋级赛"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 1
  delayDays: 0
  rankIds: 1
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Level"
}
rows {
  labelId: 19
  name: "谁是\n狼人"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 3
  delayDays: 0
  rankIds: 202
  tipsSwitch: 0
  lowerVersion: "*******"
  timeLimitSwitch: false
  beginTime {
    seconds: 1724256000
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Level"
}
rows {
  labelId: 20
  name: "躲猫猫"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 5
  delayDays: 0
  rankIds: 203
  tipsSwitch: 0
  lowerVersion: "*******"
  timeLimitSwitch: false
  beginTime {
    seconds: 1721059200
  }
  endTime {
    seconds: 4102415999
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 104
  MatchIDList: 141
}
rows {
  labelId: 21
  name: "大乱斗"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 13
  delayDays: 0
  rankIds: 204
  tipsSwitch: 0
  lowerVersion: "*********"
  timeLimitSwitch: true
  beginTime {
    seconds: 1716480000
  }
  endTime {
    seconds: 1717689599
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Level"
}
rows {
  labelId: 22
  name: "极速\n飞车"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 10
  delayDays: 0
  rankIds: 205
  tipsSwitch: 0
  lowerVersion: "*******"
  timeLimitSwitch: false
  beginTime {
    seconds: 1721059200
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 604
  MatchIDList: 605
  MatchIDList: 606
  MatchIDList: 607
  MatchIDList: 608
  MatchIDList: 609
}
rows {
  labelId: 23
  name: "武器\n大师"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 6
  delayDays: 0
  rankIds: 206
  tipsSwitch: 0
  lowerVersion: "*******"
  timeLimitSwitch: false
  beginTime {
    seconds: 1721059200
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 501
  MatchIDList: 503
  MatchIDList: 701
  MatchIDList: 703
}
rows {
  labelId: 24
  name: "大王\n别抓我\n星宝"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 7
  delayDays: 0
  rankIds: 207
  tipsSwitch: 0
  lowerVersion: "*******"
  higherVersion: "********"
  timeLimitSwitch: false
  beginTime {
    seconds: 1721059200
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Chase
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 350
  MatchIDList: 351
  MatchIDList: 352
  MatchIDList: 353
}
rows {
  labelId: 25
  name: "大王\n别抓我\n暗星"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 8
  delayDays: 0
  rankIds: 208
  tipsSwitch: 0
  lowerVersion: "*******"
  higherVersion: "********"
  timeLimitSwitch: false
  beginTime {
    seconds: 1721059200
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Chase
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 350
  MatchIDList: 351
  MatchIDList: 352
  MatchIDList: 353
}
rows {
  labelId: 26
  name: "突围\n梦幻岛"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 11
  delayDays: 0
  rankIds: 209
  tipsSwitch: 0
  lowerVersion: "*******"
  timeLimitSwitch: false
  beginTime {
    seconds: 1721059200
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 508
  MatchIDList: 509
  MatchIDList: 510
  MatchIDList: 708
  MatchIDList: 709
  MatchIDList: 710
}
rows {
  labelId: 27
  name: "卧底\n行动"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 12
  delayDays: 0
  rankIds: 211
  tipsSwitch: 0
  lowerVersion: "*******"
  timeLimitSwitch: false
  beginTime {
    seconds: 1721059200
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 103
  MatchIDList: 131
}
rows {
  labelId: 28
  name: "峡谷\n3v3"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 9
  delayDays: 0
  rankIds: 212
  tipsSwitch: 0
  timeLimitSwitch: false
  beginTime {
    seconds: 1724256000
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Arena
  DataUI: "UI_Rank_DataUI_Level"
}
rows {
  labelId: 29
  name: "社团排行榜"
  isActive: true
  firstTabIndex: 7
  secondTabIndex: 0
  delayDays: 0
  rankIds: 7
  tipsSwitch: 1
  tipsId: "ClubRankRule"
  tipsId: "ClubRankRuleDesc"
  childWndName: "UI_ClubRank_RankListMain"
  childwidgetName: "Feature.System.Script.System.Club.ClubRank.UI_ClubRank_RankChild"
  firstTabShowIndex: 9
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
}
rows {
  labelId: 30
  name: "奖杯排行"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 0
  delayDays: 0
  rankIds: 8
  tipsSwitch: 1
  tipsId: "CupRankRule"
  tipsId: "CupRankRuleDesc"
  lowerVersion: "********"
  beginTime {
    seconds: 1719244800
  }
  endTime {
    seconds: 4084444799
  }
  firstTabShowIndex: 3
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Arena
  rankingGroupList: RG_Chase
}
rows {
  labelId: 31
  name: "谁是\n狼人\n专精"
  isActive: true
  firstTabIndex: 13
  secondTabIndex: 1
  delayDays: 0
  rankIds: 3400
  tipsSwitch: 1
  tipsId: "UI_RankTag_NR3E3_FinalPoints_Rule"
  tipsId: "UI_RankTag_NR3E3_FinalPoints_Desc"
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  CustomUI: "UI_Rank_CustomUI_NR3E"
  DataUI: "UI_Rank_DataUI_NR3E"
}
rows {
  labelId: 32
  name: "泡泡\n大战"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 14
  delayDays: 0
  rankIds: 213
  tipsSwitch: 0
  lowerVersion: "*******"
  timeLimitSwitch: false
  beginTime {
    seconds: 1722528000
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 12
  MatchIDList: 13
  MatchIDList: 14
  MatchIDList: 18
  MatchIDList: 19
  MatchIDList: 20
}
rows {
  labelId: 33
  name: "奖杯\n征程\n进度"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 1
  delayDays: 0
  rankIds: 501
  tipsSwitch: 0
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_CupHistory"
  BottomUI: "UI_Rank_BottomUI_StarCup"
}
rows {
  labelId: 34
  name: "赛季\n奖杯\n获取"
  isActive: false
  firstTabIndex: 8
  secondTabIndex: 99
  delayDays: 0
  rankIds: 502
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Cup"
}
rows {
  labelId: 35
  name: "天天\n晋级赛"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 3
  delayDays: 0
  rankIds: 401
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 36
  name: "谁是\n狼人"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 5
  delayDays: 0
  rankIds: 402
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 37
  name: "极速\n飞车"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 10
  delayDays: 0
  rankIds: 403
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 38
  name: "躲猫猫"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 7
  delayDays: 0
  rankIds: 404
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 39
  name: "武器\n大师"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 8
  delayDays: 0
  rankIds: 405
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 40
  name: "大王\n别抓我"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 6
  delayDays: 0
  rankIds: 406
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Chase
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 41
  name: "峡谷\n3v3"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 9
  delayDays: 0
  rankIds: 407
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Arena
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 42
  name: "赛季\n奖杯\n获取"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 2
  delayDays: 0
  rankIds: 503
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 43
  name: "峡谷\n吃鸡"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 12
  delayDays: 0
  rankIds: 408
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Arena
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 44
  name: "峡谷\n5v5"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 4
  delayDays: 0
  rankIds: 409
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Arena
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 45
  name: "峡谷英雄榜"
  isActive: true
  firstTabIndex: 9
  secondTabIndex: 0
  delayDays: 0
  tipsSwitch: 1
  tipsId: "UI_RankTag_ArenaHeroRule"
  tipsId: "UI_RankTag_ArenaHeroDesc"
  higherVersion: "*******"
  firstTabShowIndex: 9
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  pakCheck: 50021
  rankingGroupList: RG_Arena
}
rows {
  labelId: 46
  name: "峡谷\n3v3\n英雄榜"
  isActive: true
  firstTabIndex: 13
  secondTabIndex: 4
  delayDays: 0
  rankIds: 3601
  tipsSwitch: 1
  tipsId: "UI_RankTag_ArenaHeroRule"
  tipsId: "UI_RankTag_ArenaHeroDesc"
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: false
  CanShowGeo: true
  GeoLevel: 2
  pakCheck: 50021
  rankingGroupList: RG_Arena
  CustomUI: "UI_Rank_CustomUI_Arena"
  DataUI: "UI_Rank_DataUI_XGHero"
}
rows {
  labelId: 47
  name: "时尚"
  isActive: true
  firstTabIndex: 2
  secondTabIndex: 1
  delayDays: 0
  rankIds: 2
  tipsSwitch: 0
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Fashion"
  BottomUI: "UI_Rank_BottomUI_SuitInfo"
}
rows {
  labelId: 48
  name: "装扮"
  isActive: true
  firstTabIndex: 2
  secondTabIndex: 2
  delayDays: 0
  rankIds: 11
  tipsSwitch: 1
  tipsId: "Tips_DecorationRank_1"
  tipsId: "Tips_DecorationRank_2"
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Decoration"
  BottomUI: "UI_Rank_BottomUI_Attired"
}
rows {
  labelId: 49
  name: "成就排行榜"
  isActive: true
  firstTabIndex: 10
  secondTabIndex: 0
  delayDays: 0
  rankIds: 10
  tipsSwitch: 1
  tipsId: "UI_RankTag_AchieveRule"
  tipsId: "UI_RankTag_AchieveDesc"
  timeLimitSwitch: false
  firstTabShowIndex: 8
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Achievement"
  BottomUI: "UI_Rank_BottomUI_Achievement"
}
rows {
  labelId: 50
  name: "卡牌排行"
  isActive: true
  firstTabIndex: 11
  secondTabIndex: 0
  delayDays: 0
  tipsSwitch: 1
  tipsId: "Tips_CardRank_1"
  tipsId: "Tips_CardRank_2"
  lowerVersion: "********"
  timeLimitSwitch: false
  firstTabShowIndex: 4
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
}
rows {
  labelId: 51
  name: "历史\n卡牌\n星数"
  isActive: true
  firstTabIndex: 11
  secondTabIndex: 1
  delayDays: 0
  rankIds: 600
  tipsSwitch: 0
  lowerVersion: "********"
  timeLimitSwitch: false
  beginTime {
    seconds: 1736784000
  }
  endTime {
    seconds: 4102415999
  }
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Card"
  BottomUI: "UI_Rank_BottomUI_Card"
}
rows {
  labelId: 52
  name: "田园牧歌卡册星数"
  isActive: true
  firstTabIndex: 11
  secondTabIndex: 99
  delayDays: 0
  rankIds: 611
  tipsSwitch: 0
  lowerVersion: "********"
  timeLimitSwitch: false
  beginTime {
    seconds: 1736784000
  }
  endTime {
    seconds: 1746115199
  }
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Card"
  BottomUI: "UI_Rank_BottomUI_Card"
}
rows {
  labelId: 53
  name: "峡谷\n5v5"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 2
  delayDays: 0
  rankIds: 214
  tipsSwitch: 0
  timeLimitSwitch: false
  beginTime {
    seconds: 1724256000
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Arena
  DataUI: "UI_Rank_DataUI_Level"
}
rows {
  labelId: 54
  name: "峡谷\n5v5\n英雄榜"
  isActive: true
  firstTabIndex: 13
  secondTabIndex: 2
  delayDays: 0
  rankIds: 3801
  tipsSwitch: 1
  tipsId: "UI_RankTag_ArenaHeroRule"
  tipsId: "UI_RankTag_ArenaHeroDesc"
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: false
  CanShowGeo: true
  GeoLevel: 2
  pakCheck: 50021
  rankingGroupList: RG_Arena
  CustomUI: "UI_Rank_CustomUI_Arena"
  DataUI: "UI_Rank_DataUI_XGHero"
}
rows {
  labelId: 55
  name: "峡谷\n占地盘"
  isActive: true
  firstTabIndex: 8
  secondTabIndex: 11
  delayDays: 0
  rankIds: 410
  tipsSwitch: 0
  beginTime {
    seconds: 1737648000
  }
  endTime {
    seconds: 4102415999
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Arena
  DataUI: "UI_Rank_DataUI_Cup"
  BottomUI: "UI_Rank_BottomUI_Cup"
}
rows {
  labelId: 56
  name: "大王别抓我专精"
  isActive: true
  firstTabIndex: 12
  secondTabIndex: 0
  delayDays: 0
  tipsSwitch: 1
  higherVersion: "*******"
  firstTabShowIndex: 11
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  pakCheck: 50001
  rankingGroupList: RG_Chase
}
rows {
  labelId: 57
  name: "大王\n别抓我\n专精"
  isActive: true
  firstTabIndex: 13
  secondTabIndex: 3
  delayDays: 0
  rankIds: 4401
  tipsSwitch: 1
  tipsId: "UI_RankTag_ChaseFinalPointsRule"
  tipsId: "UI_RankTag_ChaseFinalPointsDesc"
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: false
  CanShowGeo: true
  GeoLevel: 2
  pakCheck: 50001
  rankingGroupList: RG_Chase
  CustomUI: "UI_Chase_Rank_BtnChange"
  DataUI: "UI_Rank_ChaseUI_IdentitySpecialization"
}
rows {
  labelId: 58
  name: "玩法排行"
  isActive: true
  firstTabIndex: 13
  secondTabIndex: 0
  delayDays: 0
  tipsSwitch: 1
  tipsId: "UI_RankTag_ArenaHeroRule"
  tipsId: "UI_RankTag_ArenaHeroDesc"
  timeLimitSwitch: false
  firstTabShowIndex: 6
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  rankingGroupList: RG_Arena
  rankingGroupList: RG_Chase
}
rows {
  labelId: 59
  name: "星世界排行"
  isActive: true
  firstTabIndex: 14
  secondTabIndex: 0
  delayDays: 0
  tipsSwitch: 1
  tipsId: "Leaderboard_Tips_Title_StarWorld_Mix"
  tipsId: "Leaderboard_Tips_Desc_StarWorld_Mix"
  timeLimitSwitch: false
  firstTabShowIndex: 7
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
}
rows {
  labelId: 60
  name: "大王别抓我"
  isActive: true
  firstTabIndex: 1
  secondTabIndex: 4
  delayDays: 0
  rankIds: 207
  tipsSwitch: 0
  lowerVersion: "********"
  timeLimitSwitch: false
  beginTime {
    seconds: 1721059200
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  pakCheck: 50001
  rankingGroupList: RG_Chase
  CustomUI: "UI_Rank_CustomUI_ChaseLevel"
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 350
  MatchIDList: 351
  MatchIDList: 352
  MatchIDList: 353
}
rows {
  labelId: 61
  name: "缤纷市集卡册收集"
  isActive: true
  firstTabIndex: 11
  secondTabIndex: 97
  delayDays: 0
  rankIds: 612
  tipsSwitch: 0
  lowerVersion: "********"
  timeLimitSwitch: false
  beginTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1755791999
  }
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Card"
  BottomUI: "UI_Rank_BottomUI_Card"
}
rows {
  labelId: 62
  name: "缤纷市集获取总星数"
  isActive: true
  firstTabIndex: 11
  secondTabIndex: 98
  delayDays: 0
  rankIds: 800
  tipsSwitch: 0
  lowerVersion: "********"
  timeLimitSwitch: false
  beginTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1755791999
  }
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Card"
  BottomUI: "UI_Rank_BottomUI_Card"
}
rows {
  labelId: 63
  name: "卡牌\n赠送"
  isActive: true
  firstTabIndex: 11
  secondTabIndex: 2
  delayDays: 0
  rankIds: 601
  tipsSwitch: 0
  lowerVersion: "********"
  timeLimitSwitch: false
  beginTime {
    seconds: 1746115200
  }
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Card"
  BottomUI: "UI_Rank_BottomUI_Card"
}
rows {
  labelId: 64
  name: "大王-三王"
  isActive: false
  firstTabIndex: 1
  secondTabIndex: 4
  delayDays: 0
  tipsSwitch: 0
  timeLimitSwitch: false
  beginTime {
    seconds: 1758988800
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  pakCheck: 50001
  rankingGroupList: RG_Chase
  CustomUI: "UI_Rank_CustomUI_ChaseLevel"
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 350
  MatchIDList: 351
  MatchIDList: 352
  MatchIDList: 353
}
rows {
  labelId: 65
  name: "大王-单王"
  isActive: false
  firstTabIndex: 1
  secondTabIndex: 4
  delayDays: 0
  tipsSwitch: 0
  timeLimitSwitch: false
  beginTime {
    seconds: 1758988800
  }
  endTime {
    seconds: 4084444799
  }
  RankSeasonTips: true
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  pakCheck: 50001
  rankingGroupList: RG_Chase
  CustomUI: "UI_Rank_CustomUI_ChaseLevel"
  DataUI: "UI_Rank_DataUI_Level"
  MatchIDList: 350
  MatchIDList: 351
  MatchIDList: 352
  MatchIDList: 353
}
rows {
  labelId: 66
  name: "夏日联动卡册收集"
  isActive: true
  firstTabIndex: 11
  secondTabIndex: 95
  delayDays: 0
  rankIds: 613
  tipsSwitch: 0
  lowerVersion: "********"
  timeLimitSwitch: false
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1755791999
  }
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Card"
  BottomUI: "UI_Rank_BottomUI_Card"
}
rows {
  labelId: 67
  name: "夏日联动获取总星数"
  isActive: true
  firstTabIndex: 11
  secondTabIndex: 96
  delayDays: 0
  rankIds: 801
  tipsSwitch: 0
  lowerVersion: "********"
  timeLimitSwitch: false
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1755791999
  }
  RankSeasonTips: false
  CanShowGlobal: true
  CanShowFriend: true
  CanShowGeo: true
  GeoLevel: 1
  DataUI: "UI_Rank_DataUI_Card"
  BottomUI: "UI_Rank_BottomUI_Card"
}

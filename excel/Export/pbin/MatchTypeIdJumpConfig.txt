com.tencent.wea.xlsRes.table_MatchTypeIdJumpConfig
excel/xls/P_排位玩法分组.xlsx sheet:排位玩法跳转
rows {
  MatchTypeId: 352
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 151
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 607
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 608
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 609
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 701
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 703
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 708
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 709
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 710
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 4013
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 4084444800
    }
    QualifyMatchJumpCloseTime {
      seconds: 4088073599
    }
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
  }
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1724947200
    }
    QualifyMatchJumpCloseTime {
      seconds: 1729180799
    }
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
  }
}
rows {
  MatchTypeId: 15
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1746115200
    }
    QualifyMatchJumpCloseTime {
      seconds: 1750953599
    }
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>周五、六、日限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202007
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>周五、六、日限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202008
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 16
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1746115200
    }
    QualifyMatchJumpCloseTime {
      seconds: 1750953599
    }
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>周五、六、日限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202007
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>周五、六、日限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202008
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 17
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1746115200
    }
    QualifyMatchJumpCloseTime {
      seconds: 1750953599
    }
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>周五、六、日限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202007
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>周五、六、日限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202008
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 21
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1746115200
    }
    QualifyMatchJumpCloseTime {
      seconds: 1746374399
    }
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>赛季前3天限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202007
  }
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1750953600
    }
    QualifyMatchJumpCloseTime {
      seconds: 1751212799
    }
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>赛季前3天限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202008
  }
}
rows {
  MatchTypeId: 22
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1746115200
    }
    QualifyMatchJumpCloseTime {
      seconds: 1746374399
    }
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>赛季前3天限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202007
  }
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1750953600
    }
    QualifyMatchJumpCloseTime {
      seconds: 1751212799
    }
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>赛季前3天限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202008
  }
}
rows {
  MatchTypeId: 23
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1746115200
    }
    QualifyMatchJumpCloseTime {
      seconds: 1746374399
    }
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>赛季前3天限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202007
  }
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1750953600
    }
    QualifyMatchJumpCloseTime {
      seconds: 1751212799
    }
    QualifyMatchJumpId: 20003
    QualifyMatchJumpDesc: "<GoldYellow>赛季前3天限时开放！</>点击<GoldYellow>前往</>领取参与奖励！"
    RedDotType: 147
    RewardItemId: 202008
  }
}
rows {
  MatchTypeId: 131
  JumpInfoConfig {
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1005
  }
  JumpInfoConfig {
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
    PlayModeSeasonId: 1006
  }
}
rows {
  MatchTypeId: 18
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 4084444800
    }
    QualifyMatchJumpCloseTime {
      seconds: 4088073599
    }
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
  }
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 4084444800
    }
    QualifyMatchJumpCloseTime {
      seconds: 4088073599
    }
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
  }
}
rows {
  MatchTypeId: 19
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 4084444800
    }
    QualifyMatchJumpCloseTime {
      seconds: 4088073599
    }
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
  }
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 4084444800
    }
    QualifyMatchJumpCloseTime {
      seconds: 4088073599
    }
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
  }
}
rows {
  MatchTypeId: 20
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 4084444800
    }
    QualifyMatchJumpCloseTime {
      seconds: 4088073599
    }
    QualifyMatchJumpId: 20001
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
  }
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 4084444800
    }
    QualifyMatchJumpCloseTime {
      seconds: 4088073599
    }
    QualifyMatchJumpId: 20004
    QualifyMatchJumpDesc: "<GoldYellow>娱乐排位赛</>开启中！可点击前往参与活动"
  }
}
rows {
  MatchTypeId: 141
  JumpInfoConfig {
    QualifyMatchJumpOpenTime {
      seconds: 1729180800
    }
    QualifyMatchJumpCloseTime {
      seconds: 4070966399
    }
    QualifyMatchJumpId: 670
    QualifyMatchJumpDesc: "<GoldYellow>猫猫补习班</>开启中！ 获取躲猫猫乐趣道具吧"
    RedDotType: 368
    RedDotParam: 151
  }
}

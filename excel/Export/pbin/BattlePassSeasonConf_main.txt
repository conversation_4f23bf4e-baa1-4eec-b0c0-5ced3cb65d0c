com.tencent.wea.xlsRes.table_BattlePassSeasonConf
excel/xls/B_BP通行证_主表.xlsx sheet:赛季配置
rows {
  id: 13
  type: BT_Main
  expItemId: 1005
  title: "昆仑秘境通行证"
  time {
    beginTime {
      seconds: 1750953600
    }
    endTime {
      seconds: 1755791999
    }
  }
  lowestVersion: "1.3.99.1"
  task {
    seasonGroupId: 14235
    weekGroupIds: 14227
    weekGroupIds: 14228
    weekGroupIds: 14229
    weekGroupIds: 14230
    weekGroupIds: 14231
    weekGroupIds: 14232
    weekGroupIds: 14233
    weekGroupIds: 14234
  }
  free {
    enable: true
    totalRewards {
      itemId: 6
      itemNum: 1200
    }
    totalRewards {
      itemId: 4
      itemNum: 19500
    }
    totalRewards {
      itemId: 630462
      itemNum: 1
    }
    totalRewards {
      itemId: 510209
      itemNum: 1
    }
    totalRewards {
      itemId: 520148
      itemNum: 1
    }
    totalRewards {
      itemId: 530125
      itemNum: 1
    }
    totalRewards {
      itemId: 722072
      itemNum: 1
    }
    totalRewards {
      itemId: 711412
      itemNum: 1
    }
    totalRewards {
      itemId: 840313
      itemNum: 1
    }
    totalRewards {
      itemId: 820197
      itemNum: 1
    }
    totalRewards {
      itemId: 200006
      itemNum: 10
    }
    totalRewards {
      itemId: 200008
      itemNum: 10
    }
  }
  payed {
    enable: true
    totalRewards {
      itemId: 1
      itemNum: 300
    }
    totalRewards {
      itemId: 2
      itemNum: 30
    }
    totalRewards {
      itemId: 411470
      itemNum: 1
    }
    totalRewards {
      itemId: 411510
      itemNum: 1
    }
    totalRewards {
      itemId: 411520
      itemNum: 1
    }
    totalRewards {
      itemId: 850645
      itemNum: 1
    }
    totalRewards {
      itemId: 630638
      itemNum: 1
    }
    totalRewards {
      itemId: 620934
      itemNum: 1
    }
    totalRewards {
      itemId: 610416
      itemNum: 1
    }
    totalRewards {
      itemId: 722062
      itemNum: 1
    }
    totalRewards {
      itemId: 711407
      itemNum: 1
    }
    totalRewards {
      itemId: 6
      itemNum: 200
    }
    totalRewards {
      itemId: 840314
      itemNum: 1
    }
    totalRewards {
      itemId: 820198
      itemNum: 1
    }
    totalRewards {
      itemId: 200006
      itemNum: 10
    }
    totalRewards {
      itemId: 200008
      itemNum: 10
    }
    totalRewards {
      itemId: 725003
      itemNum: 4
    }
    totalRewards {
      itemId: 200014
      itemNum: 8
    }
    totalRewards {
      itemId: 320011
      itemNum: 37
    }
    highlightRewards {
      itemId: 1
      itemNum: 300
    }
    highlightRewards {
      itemId: 2
      itemNum: 30
    }
    highlightRewards {
      itemId: 411470
      itemNum: 1
    }
    highlightRewards {
      itemId: 411510
      itemNum: 1
    }
    highlightRewards {
      itemId: 411520
      itemNum: 1
    }
    highlightRewards {
      itemId: 630638
      itemNum: 1
    }
    highlightRewards {
      itemId: 620934
      itemNum: 1
    }
    highlightRewards {
      itemId: 610416
      itemNum: 1
    }
    price {
      coinType: 1
      price: 300
      origin: 300
      discount: "0"
      midas: "18_1_1"
    }
    replacement {
      itemId: 6
      itemNum: 300
    }
  }
  payed {
    enable: true
    highlightRewards {
      itemId: 1
      itemNum: 300
    }
    highlightRewards {
      itemId: 2
      itemNum: 30
    }
    highlightRewards {
      itemId: 411470
      itemNum: 1
    }
    highlightRewards {
      itemId: 411510
      itemNum: 1
    }
    highlightRewards {
      itemId: 411520
      itemNum: 1
    }
    highlightRewards {
      itemId: 630638
      itemNum: 1
    }
    highlightRewards {
      itemId: 620934
      itemNum: 1
    }
    highlightRewards {
      itemId: 610416
      itemNum: 1
    }
    extraRewards {
      itemId: 860218
      itemNum: 1
    }
    extraRewards {
      itemId: 860219
      itemNum: 1
    }
    extraRewards {
      itemId: 2
      itemNum: 15
    }
    extraRewards {
      itemId: 1005
      itemNum: 1000
    }
    price {
      coinType: 1
      price: 600
      origin: 800
      discount: "7.5"
      midas: "18_1_2"
    }
    replacement {
      itemId: 6
      itemNum: 600
    }
  }
  upgrade {
    range {
      key: 1
      value: 2
    }
    price {
      coinType: 1
      price: 400
      origin: 500
      discount: "8"
      midas: "18_1_3"
    }
    replacement {
      itemId: 6
      itemNum: 300
    }
  }
  level {
    coinType: 1
    price: 50
    origin: 50
    discount: "0"
    midas: "18_1_4"
  }
  grandRewardId: 411470
  expiredMailId: 14
  unlockViewPath: "UI_NewBattlePass_Wolf_S1_Unlock"
  unlockBtnShow: "1,300"
  mainViewPath: "UI_NewBattlePass_Main_S9_Tabs"
  discountBottomDes: "45,300,18"
  showViewPaths: "UI_NewBattlePass_Main_S9_Show"
  totalShowViewPath: "UI_NewBattlePass_Main_S9_TotalShow"
  mainViewCameraTag: "Camera_BattlePass_S3"
  totalShowViewCameraTag: "Camera_BattlePassTotal_S3"
  rewardViewPath: "UI_NewBattlePass_Main_S9_Reward"
  taskViewPath: "UI_NewBattlePass_Main_S9_TaskPage"
  helpId: 162
  midTitle1: "昆仑秘境"
  midTitle2: "通行证"
  unlockNormalTitle: "高级通行证"
  unlockNormalDes: "达到指定等级可领取"
  unlockSuperTitle: "豪华通行证"
  unlockSuperDes: "包含所有高级奖励+立升10级+2套搭配方案栏位"
  normalBattlePassId: 102004
  gloryBattlePassId: 102005
  isShow: true
  normalToGloryBattlePassId: 102033
}
rows {
  id: 12
  type: BT_Main
  expItemId: 1005
  title: "超能学园通行证"
  time {
    beginTime {
      seconds: 1746115200
    }
    endTime {
      seconds: 1750953599
    }
  }
  lowestVersion: "1.3.80.1"
  task {
    seasonGroupId: 14135
    weekGroupIds: 14127
    weekGroupIds: 14128
    weekGroupIds: 14129
    weekGroupIds: 14130
    weekGroupIds: 14131
    weekGroupIds: 14132
    weekGroupIds: 14133
    weekGroupIds: 14134
  }
  free {
    enable: true
    totalRewards {
      itemId: 6
      itemNum: 1200
    }
    totalRewards {
      itemId: 4
      itemNum: 19500
    }
    totalRewards {
      itemId: 630314
      itemNum: 1
    }
    totalRewards {
      itemId: 510366
      itemNum: 1
    }
    totalRewards {
      itemId: 520244
      itemNum: 1
    }
    totalRewards {
      itemId: 530220
      itemNum: 1
    }
    totalRewards {
      itemId: 720880
      itemNum: 1
    }
    totalRewards {
      itemId: 711403
      itemNum: 1
    }
    totalRewards {
      itemId: 840285
      itemNum: 1
    }
    totalRewards {
      itemId: 820181
      itemNum: 1
    }
    totalRewards {
      itemId: 200006
      itemNum: 10
    }
    totalRewards {
      itemId: 200008
      itemNum: 10
    }
  }
  payed {
    enable: true
    totalRewards {
      itemId: 1
      itemNum: 300
    }
    totalRewards {
      itemId: 2
      itemNum: 30
    }
    totalRewards {
      itemId: 410800
      itemNum: 1
    }
    totalRewards {
      itemId: 411010
      itemNum: 1
    }
    totalRewards {
      itemId: 411040
      itemNum: 1
    }
    totalRewards {
      itemId: 850645
      itemNum: 1
    }
    totalRewards {
      itemId: 630586
      itemNum: 1
    }
    totalRewards {
      itemId: 620179
      itemNum: 1
    }
    totalRewards {
      itemId: 610382
      itemNum: 1
    }
    totalRewards {
      itemId: 722003
      itemNum: 1
    }
    totalRewards {
      itemId: 711402
      itemNum: 1
    }
    totalRewards {
      itemId: 6
      itemNum: 200
    }
    totalRewards {
      itemId: 840286
      itemNum: 1
    }
    totalRewards {
      itemId: 820182
      itemNum: 1
    }
    totalRewards {
      itemId: 200006
      itemNum: 10
    }
    totalRewards {
      itemId: 200008
      itemNum: 10
    }
    totalRewards {
      itemId: 725003
      itemNum: 4
    }
    totalRewards {
      itemId: 200014
      itemNum: 8
    }
    totalRewards {
      itemId: 320011
      itemNum: 37
    }
    highlightRewards {
      itemId: 1
      itemNum: 300
    }
    highlightRewards {
      itemId: 2
      itemNum: 30
    }
    highlightRewards {
      itemId: 410800
      itemNum: 1
    }
    highlightRewards {
      itemId: 411010
      itemNum: 1
    }
    highlightRewards {
      itemId: 411040
      itemNum: 1
    }
    highlightRewards {
      itemId: 630586
      itemNum: 1
    }
    highlightRewards {
      itemId: 620179
      itemNum: 1
    }
    highlightRewards {
      itemId: 610382
      itemNum: 1
    }
    price {
      coinType: 1
      price: 300
      origin: 300
      discount: "0"
      midas: "18_1_1"
    }
    replacement {
      itemId: 6
      itemNum: 300
    }
  }
  payed {
    enable: true
    highlightRewards {
      itemId: 1
      itemNum: 300
    }
    highlightRewards {
      itemId: 2
      itemNum: 30
    }
    highlightRewards {
      itemId: 410800
      itemNum: 1
    }
    highlightRewards {
      itemId: 411010
      itemNum: 1
    }
    highlightRewards {
      itemId: 411040
      itemNum: 1
    }
    highlightRewards {
      itemId: 630586
      itemNum: 1
    }
    highlightRewards {
      itemId: 620179
      itemNum: 1
    }
    highlightRewards {
      itemId: 610382
      itemNum: 1
    }
    extraRewards {
      itemId: 860203
      itemNum: 1
    }
    extraRewards {
      itemId: 860204
      itemNum: 1
    }
    extraRewards {
      itemId: 2
      itemNum: 15
    }
    extraRewards {
      itemId: 1005
      itemNum: 1000
    }
    price {
      coinType: 1
      price: 600
      origin: 800
      discount: "7.5"
      midas: "18_1_2"
    }
    replacement {
      itemId: 6
      itemNum: 600
    }
  }
  upgrade {
    range {
      key: 1
      value: 2
    }
    price {
      coinType: 1
      price: 400
      origin: 500
      discount: "8"
      midas: "18_1_3"
    }
    replacement {
      itemId: 6
      itemNum: 300
    }
  }
  level {
    coinType: 1
    price: 50
    origin: 50
    discount: "0"
    midas: "18_1_4"
  }
  grandRewardId: 410800
  expiredMailId: 14
  unlockViewPath: "UI_NewBattlePass_Wolf_S1_Unlock"
  unlockBtnShow: "1,300"
  mainViewPath: "UI_NewBattlePass_Main_S8_Tabs"
  discountBottomDes: "45,300,18"
  showViewPaths: "UI_NewBattlePass_Main_S8_Show"
  totalShowViewPath: "UI_NewBattlePass_Main_S8_TotalShow"
  mainViewCameraTag: "Camera_BattlePass_S4"
  totalShowViewCameraTag: "Camera_BattlePassTotal_S4"
  rewardViewPath: "UI_NewBattlePass_Main_S8_Reward"
  taskViewPath: "UI_NewBattlePass_Main_S8_TaskPage"
  helpId: 162
  midTitle1: "超能学园"
  midTitle2: "通行证"
  unlockNormalTitle: "高级通行证"
  unlockNormalDes: "达到指定等级可领取"
  unlockSuperTitle: "豪华通行证"
  unlockSuperDes: "包含所有高级奖励+立升10级+2套搭配方案栏位"
  normalBattlePassId: 102007
  gloryBattlePassId: 102008
  isShow: true
  normalToGloryBattlePassId: 102032
}

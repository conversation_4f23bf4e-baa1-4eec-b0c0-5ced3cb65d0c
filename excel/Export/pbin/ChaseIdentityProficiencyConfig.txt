com.tencent.wea.xlsRes.table_ChaseIdentityProficiencyConfig
excel/xls/Chase/D_大王身份熟练度.xlsx sheet:角色熟练度
rows {
  ActorId: 21001
  ProficiencyId: 1
  needProficiency: 100
  needUnlockBiography: 1
}
rows {
  ActorId: 21001
  ProficiencyId: 2
  needProficiency: 200
  needUnlockBiography: 2
}
rows {
  ActorId: 21001
  ProficiencyId: 3
  needProficiency: 300
  needUnlockBiography: 3
}
rows {
  ActorId: 21001
  ProficiencyId: 4
  needProficiency: 400
}
rows {
  ActorId: 21001
  ProficiencyId: 5
  needProficiency: 500
}
rows {
  ActorId: 21001
  ProficiencyId: 6
  unLockFightPower: 1
  bigReward: 1
  needProficiency: 600
}

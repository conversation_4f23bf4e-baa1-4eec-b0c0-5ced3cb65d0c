com.tencent.wea.xlsRes.table_SfxAudioConfig
excel/xls/Y_音频之音效.xlsx sheet:音效
rows {
  id: 4098
  playEvent: "Play_SFX_Baize_Menu_ShowEnter"
  bank: "SFX_OG_048_Baize"
  stopEvent: "Stop_SFX_Baize_Menu_ShowEnter"
}
rows {
  id: 4099
  playEvent: "Play_VO_Baize_Menu_ShowEnter"
  bank: "VO_OG_048_Baize"
  stopEvent: "Stop_VO_Baize"
}
rows {
  id: 4100
  playEvent: "Play_SFX_BaizeSP_Menu_ShowEnter"
  bank: "SFX_OG_049_BaizeSP"
  stopEvent: "Stop_SFX_BaizeSP_Menu_ShowEnter"
}
rows {
  id: 4101
  playEvent: "Play_VO_BaizeSP_Menu_ShowEnter"
  bank: "VO_OG_049_BaizeSP"
  stopEvent: "Stop_VO_BaizeSP"
}
rows {
  id: 10327
  playEvent: "Play_SFX_Vehicle_Butterfly_Change"
  playEvent3p: "Play_SFX_Vehicle_Butterfly_Change"
  bank: "SFX_Vanessa"
  stopEvent: "Stop_SFX_Vehicle_Butterfly_Change"
  stopEvent3p: "Stop_SFX_Vehicle_Butterfly_Change"
  is3d: 1
}
rows {
  id: 10328
  playEvent: "Play_SFX_Vehicle_Vanessa_IdleShow_WingChime"
  playEvent3p: "Play_SFX_Vehicle_Vanessa_IdleShow_WingChime"
  bank: "SFX_Vanessa"
  stopEvent: "Stop_SFX_Vehicle_Vanessa_IdleShow_WingChime"
  stopEvent3p: "Stop_SFX_Vehicle_Vanessa_IdleShow_WingChime"
  is3d: 1
  isLoop: 1
}
rows {
  id: 40561
  playEvent: "Play_LetsFarm_ENV_Beding_Volcano"
  playEvent3p: "Play_LetsFarm_ENV_Beding_Volcano"
  bank: "LetsFarm_ENV_Volcano"
  stopEvent: "Stop_LetsFarm_ENV_Beding_Volcano"
  stopEvent3p: "Stop_LetsFarm_ENV_Beding_Volcano"
  isLoop: 1
}
rows {
  id: 40627
  playEvent: "Play_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L08_All"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L08_All"
  bank: "LetsFarm_NPC"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L08_All"
  stopEvent3p: "Stop_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L08_All"
}
rows {
  id: 40628
  playEvent: "Play_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L09_All"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L09_All"
  bank: "LetsFarm_NPC"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L09_All"
  stopEvent3p: "Stop_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L09_All"
}
rows {
  id: 40629
  playEvent: "Play_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L10_All"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L10_All"
  bank: "LetsFarm_NPC"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L10_All"
  stopEvent3p: "Stop_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L10_All"
}
rows {
  id: 40630
  playEvent: "Play_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L11_All"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L11_All"
  bank: "LetsFarm_NPC"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L11_All"
  stopEvent3p: "Stop_LetsFarm_NPC_Kylin_SEQ_ShenShou_SE_L11_All"
}
rows {
  id: 40666
  playEvent: "Play_LetsFarm_Shop_TPMessageBoard_OG_016"
  playEvent3p: "Play_LetsFarm_Shop_TPMessageBoard_OG_016"
  bank: "LetsFarm_Shop_TPMessageBoard_OG_016"
  stopEvent: "Stop_LetsFarm_Shop_TPMessageBoard_OG_016"
  stopEvent3p: "Stop_LetsFarm_Shop_TPMessageBoard_OG_016"
}
rows {
  id: 40667
  playEvent: "Play_LetsFarm_Shop_SharkFishStall_OG_015"
  playEvent3p: "Play_LetsFarm_Shop_SharkFishStall_OG_015"
  bank: "LetsFarm_Shop_SharkFishStall_OG_015"
  stopEvent: "Stop_LetsFarm_Shop_SharkFishStall_OG_015"
  stopEvent3p: "Stop_LetsFarm_Shop_SharkFishStall_OG_015"
}
rows {
  id: 46003
  playEvent: "Play_Obj_GamePlay_FireCircle_Wave"
  playEvent3p: "Play_Obj_GamePlay_FireCircle_Wave"
  bank: "Obj_GamePlay_FireCircle"
  is3d: 1
}
rows {
  id: 46004
  playEvent: "Play_Obj_GamePlay_FireCircle_GoldCircle"
  playEvent3p: "Play_Obj_GamePlay_FireCircle_GoldCircle"
  bank: "Obj_GamePlay_FireCircle"
  is3d: 1
}
rows {
  id: 46005
  playEvent: "Play_Obj_GamePlay_FireCircle_Impact"
  playEvent3p: "Play_Obj_GamePlay_FireCircle_Impact"
  bank: "Obj_GamePlay_FireCircle"
  is3d: 1
}
rows {
  id: 46006
}
rows {
  id: 46051
  playEvent: "Play_Obj_GamePlay_Elf_Ice"
  playEvent3p: "Play_Obj_GamePlay_Elf_Ice"
  bank: "Obj_GamePlay_Elf"
  is3d: 1
}
rows {
  id: 46052
  playEvent: "Play_Obj_GamePlay_Elf_Fire"
  playEvent3p: "Play_Obj_GamePlay_Elf_Fire"
  bank: "Obj_GamePlay_Elf"
  is3d: 1
}
rows {
  id: 46053
  playEvent: "Play_Obj_GamePlay_Elf_Electric"
  playEvent3p: "Play_Obj_GamePlay_Elf_Electric"
  bank: "Obj_GamePlay_Elf"
  is3d: 1
}
rows {
  id: 46054
  playEvent: "Play_Obj_GamePlay_Elf_Bilibili"
  playEvent3p: "Play_Obj_GamePlay_Elf_Bilibili"
  bank: "Obj_GamePlay_Elf"
  is3d: 1
}
rows {
  id: 46055
  playEvent: "Play_Obj_GamePlay_Elf_Bron"
  playEvent3p: "Play_Obj_GamePlay_Elf_Bron"
  bank: "Obj_GamePlay_Elf"
  is3d: 1
}
rows {
  id: 46056
  playEvent: "Play_Obj_GamePlay_Elf_Follow"
  playEvent3p: "Play_Obj_GamePlay_Elf_Follow"
  bank: "Obj_GamePlay_Elf"
  is3d: 1
}
rows {
  id: 46057
}
rows {
  id: 46058
}
rows {
  id: 46101
  playEvent: "Play_Obj_GamePlay_StarBall_Enter"
  bank: "Obj_GamePlay_StarBall"
  is3d: 1
}
rows {
  id: 46102
  playEvent: "Play_Obj_GamePlay_StarBall_Exit"
  bank: "Obj_GamePlay_StarBall"
  is3d: 1
}
rows {
  id: 51080
  playEvent: "Play_sfx_pretend_light"
  playEvent3p: "Play_sfx_pretend_light_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 51081
  playEvent: "Play_sfx_hide_light"
  playEvent3p: "Play_sfx_hide_light_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 51082
  playEvent: "Play_sfx_hide_over_light"
  playEvent3p: "Play_sfx_hide_over_light_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 51083
  playEvent: "Play_sfx_pretend_starsky"
  playEvent3p: "Play_sfx_pretend_starsky_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 51084
  playEvent: "Play_sfx_hide_starsky"
  playEvent3p: "Play_sfx_hide_starsky_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 51085
  playEvent: "Play_sfx_hide_over_starsky"
  playEvent3p: "Play_sfx_hide_over_starsky_3P"
  bank: "HideAndSeek"
  is3d: 1
}
rows {
  id: 52207
  playEvent: "Play_sfx_E3_skill_battlewolf"
  bank: "E3"
}
rows {
  id: 52208
  playEvent: "Play_ui_E3_meetingtool_superheart_start"
  bank: "E3"
  stopEvent: "Play_ui_E3_meetingtool_superheart_end"
}
rows {
  id: 52209
  playEvent: "Play_ui_E3_meetingtool_gold_start"
  bank: "E3"
  stopEvent: "Play_ui_E3_meetingtool_gold_end"
}
rows {
  id: 52210
  playEvent: "Play_ui_E3_meetingtool_merit_start"
  bank: "E3"
  stopEvent: "Play_ui_E3_meetingtool_merit_end"
}
rows {
  id: 52211
  playEvent: "Play_sfx_E3_skill_teacher_start"
  bank: "E3"
  stopEvent: "Play_sfx_E3_skill_teacher_end"
  is3d: 1
  isLoop: 1
}
rows {
  id: 52212
  playEvent: "Play_sfx_E3_skill_curse_mark"
  bank: "E3"
}
rows {
  id: 52213
  playEvent: "Play_sfx_E3_skill_curse_start"
  playEvent3p: "Play_sfx_E3_skill_curse_start_3P"
  bank: "E3"
  stopEvent: "Play_sfx_E3_skill_curse_end"
  stopEvent3p: "Play_sfx_E3_skill_curse_end_3P"
  is3d: 1
  isLoop: 1
}
rows {
  id: 53225
  playEvent: "Play_sfx_E3_treasurehunt_explore"
  bank: "E3"
}
rows {
  id: 53226
  playEvent: "Play_sfx_E3_treasurehunt_explore_over"
  bank: "E3"
}
rows {
  id: 53227
  playEvent: "Play_ui_E3_treasurehunt_bell"
  bank: "E3"
}
rows {
  id: 53228
  playEvent: "Play_sfx_E3_treasurehunt_dig"
  playEvent3p: "Play_sfx_E3_treasurehunt_dig_3P"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53229
  playEvent: "Play_sfx_E3_treasurehunt_treasure_appear"
  bank: "E3"
}
rows {
  id: 53230
  playEvent: "Play_sfx_E3_treasurehunt_treasure_acquire"
  bank: "E3"
}
rows {
  id: 53231
  playEvent: "Play_sfx_E3_treasurehunt_treasure_putdown"
  bank: "E3"
}
rows {
  id: 53232
  playEvent: "Play_sfx_E3_treasurehunt_treasure_submit"
  bank: "E3"
}
rows {
  id: 53233
  playEvent: "Play_ui_E3_treasurehunt_shop_open"
  bank: "E3"
}
rows {
  id: 53234
  playEvent: "Play_ui_E3_treasurehunt_buy"
  bank: "E3"
}
rows {
  id: 53235
  playEvent: "Play_sfx_E3_treasurehunt_boostrun"
  bank: "E3"
}
rows {
  id: 53236
  playEvent: "Play_sfx_E3_treasurehunt_chest_open"
  bank: "E3"
}
rows {
  id: 53237
  playEvent: "Play_sfx_E3_treasurehunt_chest_open_big"
  bank: "E3"
}
rows {
  id: 53238
  playEvent: "Play_sfx_E3_treasurehunt_task_fire"
  playEvent3p: "Play_sfx_E3_treasurehunt_task_fire_3P"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53239
  playEvent: "Play_sfx_E3_treasurehunt_task_mist"
  playEvent3p: "Play_sfx_E3_treasurehunt_task_mist_3P"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53240
  playEvent: "Play_sfx_E3_treasurehunt_task_medicine"
  playEvent3p: "Play_sfx_E3_treasurehunt_task_medicine_3P"
  bank: "E3"
  is3d: 1
}
rows {
  id: 53241
  playEvent: "Play_sfx_E3_treasurehunt_task_eat"
  playEvent3p: "Play_sfx_E3_treasurehunt_task_eat_3P"
  bank: "E3"
  is3d: 1
}
rows {
  id: 55143
  playEvent: "Play_seq_E3_attack_prisoner"
  bank: "E3"
  stopEvent: "Stop_seq_E3"
}
rows {
  id: 55144
  playEvent: "Play_seq_E3_attack_accident"
  bank: "E3"
  stopEvent: "Stop_seq_E3"
}
rows {
  id: 55145
  playEvent: "Play_seq_E3_report_airballon"
  bank: "E3"
  stopEvent: "Stop_seq_E3"
}
rows {
  id: 55146
  playEvent: "Play_seq_E3_report_vacation"
  bank: "E3"
  stopEvent: "Stop_seq_E3"
}
rows {
  id: 55147
  playEvent: "Play_seq_E3_report_kid"
  bank: "E3"
  stopEvent: "Stop_seq_E3"
}
rows {
  id: 55148
  playEvent: "Play_seq_E3_mvp_graduation"
  bank: "E3"
  stopEvent: "Stop_seq_E3"
}
rows {
  id: 55149
  playEvent: "Play_seq_E3_treasurehunt_opening"
  bank: "E3"
  stopEvent: "Stop_seq_E3"
}
rows {
  id: 55901
  playEvent: "Play_LetsChase_SEQ_System_GameStart"
  playEvent3p: "Play_LetsChase_SEQ_System_GameStart"
  bank: "LetsChase_SEQ"
  stopEvent: "Stop_LetsChase_SEQ_System_GameStart"
  is3d: 1
}
rows {
  id: 55902
  playEvent: "Play_LetsChase_SEQ_System_Victory_StarBaby"
  playEvent3p: "Play_LetsChase_SEQ_System_Victory_StarBaby"
  bank: "LetsChase_SEQ"
  stopEvent: "Stop_LetsChase_SEQ_System_Victory_StarBaby"
  is3d: 1
}
rows {
  id: 55903
  playEvent: "Play_LetsChase_SEQ_System_Victory_DarkStar"
  playEvent3p: "Play_LetsChase_SEQ_System_Victory_DarkStar"
  bank: "LetsChase_SEQ"
  stopEvent: "Stop_LetsChase_SEQ_System_Victory_DarkStar"
  is3d: 1
}
rows {
  id: 55951
  playEvent: "Set_GP_MUS_InGame_LetsChase_Encountered"
  playEvent3p: "Set_GP_MUS_InGame_LetsChase_Encountered"
  bank: "MUS_InGame_LetsChase_NightCity"
  is3d: 1
}
rows {
  id: 55952
  playEvent: "Set_GP_MUS_InGame_LetsChase_Escaped"
  playEvent3p: "Set_GP_MUS_InGame_LetsChase_Escaped"
  bank: "MUS_InGame_LetsChase_NightCity"
  is3d: 1
}
rows {
  id: 55953
  playEvent: "Play_LetsChase_UIHUD_InGame_Perspective"
  playEvent3p: "Play_LetsChase_UIHUD_InGame_Perspective"
  bank: "LetsChase_UIHUD"
}
rows {
  id: 55954
  playEvent: "Play_LetsChase_UIHUD_InGame_ImHere"
  playEvent3p: "Play_LetsChase_UIHUD_InGame_ImHere"
  bank: "LetsChase_UIHUD"
}
rows {
  id: 55955
}
rows {
  id: 96204
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_153"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_153"
}
rows {
  id: 96205
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_154"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_154"
}
rows {
  id: 96206
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_155"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_155"
}
rows {
  id: 96207
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_156"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_156"
}
rows {
  id: 96208
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_157"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_157"
}
rows {
  id: 96209
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_158"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_158"
}
rows {
  id: 96210
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_159"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_159"
}
rows {
  id: 96211
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_160"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_160"
}
rows {
  id: 96212
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_161"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_161"
}
rows {
  id: 96213
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_162"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_162"
}
rows {
  id: 96214
  playEvent: "Play_VO_LetsMOBA_Sys_SGame_In_Node_Voice_163"
  bank: "LetsMOBA_VO_System"
  stopEvent: "Stop_VO_LetsMOBA_Sys_SGame_In_Node_Voice_163"
}
rows {
  id: 96215
  playEvent: "Play_LetsMOBA_Monster_HongSun01_SFX_Death_Origin"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96216
  playEvent: "Play_LetsMOBA_Monster_HongSun02_SFX_Death_Origin"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96217
  playEvent: "Play_LetsMOBA_Monster_XianFeng01_SFX_Death_Origin"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96218
  playEvent: "Play_LetsMOBA_Monster_XianFeng03_SFX_Birth_Origin"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 96219
  playEvent: "Play_LetsMOBA_Monster_XianFeng04_Foot_Origin"
  bank: "LetsMOBA_Monster"
  is3d: 1
}
rows {
  id: 98004
  playEvent: "Play_LetsMOBA_Soccer_HUD_Goal_Own"
  bank: "LetsMOBA_Soccer"
}
rows {
  id: 98005
  playEvent: "Play_LetsMOBA_Soccer_HUD_Scoreboard"
  bank: "LetsMOBA_Soccer"
}
rows {
  id: 98006
  playEvent: "Play_LetsMOBA_Soccer_HUD_Win"
  bank: "LetsMOBA_Soccer"
}
rows {
  id: 98007
  playEvent: "Play_LetsMOBA_Soccer_HUD_Draw"
  bank: "LetsMOBA_Soccer"
}
rows {
  id: 98008
  playEvent: "Play_LetsMOBA_Soccer_HUD_Lose"
  bank: "LetsMOBA_Soccer"
}
rows {
  id: 98009
  playEvent: "Play_MUS_InGame_LetsMOBA_Football_Goal_Stinger"
  bank: "MUS_InGame_LetsMOBA_Football"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_Football_Goal_Stinger"
}
rows {
  id: 98010
  playEvent: "Play_MUS_InGame_LetsMOBA_Football_OwnGoal_Stinger"
  bank: "MUS_InGame_LetsMOBA_Football"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_Football_OwnGoal_Stinger"
}
rows {
  id: 98011
}
rows {
  id: 98012
}
rows {
  id: 98013
}
rows {
  id: 98014
}
rows {
  id: 98015
}

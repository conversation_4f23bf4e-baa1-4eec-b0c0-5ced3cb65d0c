com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置_社团.xlsx sheet:文本配置
rows {
  content: "{0}和你相互拜了个年，继续分享给其他星宝吧"
  switch: 1
  stringId: "Text_SFPlaze_Bainian_Passive_Tips"
}
rows {
  content: "{0}和你共同点亮了花灯，继续分享给其他星宝吧"
  switch: 1
  stringId: "Text_SFPlaze_Huadeng_Passive_Tips"
}
rows {
  content: "请靠近其他星宝后再试试哦~"
  switch: 1
  stringId: "Text_SFPlaze_Need2beClose_Tips"
}
rows {
  content: "升级版本后即可体验"
  switch: 1
  stringId: "Text_SFPlaze_Need2Update_Tips"
}
rows {
  content: "你刚刚和TA贴过福字啦，找其他星宝试试吧"
  switch: 1
  stringId: "Text_SFPlaze_InitiativeCD_Fuqi_Tips"
}
rows {
  content: "你刚刚和TA拜过年啦，找其他星宝试试吧"
  switch: 1
  stringId: "Text_SFPlaze_InitiativeCD_Bainian_Tips"
}
rows {
  content: "你刚刚和TA点过花灯啦，找其他星宝试试吧"
  switch: 1
  stringId: "Text_SFPlaze_InitiativeCD_Huadeng_Tips"
}
rows {
  content: "TA的福气要爆炸啦~准备升空！"
  switch: 1
  stringId: "Text_SFPlaze_LevelMax_Fuqi_Tips"
}
rows {
  content: "TA的福气要爆炸啦~准备升空！"
  switch: 1
  stringId: "Text_SFPlaze_LevelMax_Bainian_Tips"
}
rows {
  content: "TA的花灯已经很亮啦~准备升空！"
  switch: 1
  stringId: "Text_SFPlaze_LevelMax_Huadeng_Tips"
}
rows {
  content: "升级最新版本后，即可互动"
  switch: 1
  stringId: "WallaceVersionUpdateTips"
}
rows {
  content: "前往广场上的福字摊位即可继续获得福字道具"
  switch: 1
  stringId: "Text_Fuzi_NotEnough_Tips"
}
rows {
  content: "这位星宝状态有些特殊，换一位试试吧"
  switch: 1
  stringId: "Text_General_ActionNegetive_Tips"
}
rows {
  content: "贴福字"
  switch: 1
  stringId: "Text_SFPlaze_Name_Tiefuzi"
}
rows {
  content: "拜年"
  switch: 1
  stringId: "Text_SFPlaze_Name_Bainian"
}
rows {
  content: "点花灯"
  switch: 1
  stringId: "Text_SFPlaze_Name_Dianhuadeng"
}
rows {
  content: "已经很高啦，无法再抱起这么多星宝了~"
  switch: 1
  stringId: "Text_SFPlaze_HeightLimiet_Tips"
}
rows {
  content: "您已成为叠高高小队长，继续抱起其他星宝吧~"
  switch: 1
  stringId: "Text_DoubleHigher_Intro_Tips"
}
rows {
  content: "吃桶变桶，你已经拥有了神奇的能力，快去抱起其他星宝试试吧~"
  switch: 1
  stringId: "Text_Wallace_Change_Tips_1"
}
rows {
  content: "财神赐福，你已经拥有了神奇的能力，快去抱起其他星宝试试吧~"
  switch: 1
  stringId: "Text_Wallace_Change_Tips_2"
}
rows {
  content: "福气已满，快点【升空】按钮，立即出发~"
  switch: 1
  stringId: "Text_SFPlaze_LevelMax_Tiefuzi_Tips"
}
rows {
  content: "福气已满，快点【升空】按钮，立即出发~"
  switch: 1
  stringId: "Text_SFPlaze_LevelMax_Bainian_Tips_1"
}
rows {
  content: "花灯耀眼，快点【升空】按钮，立即出发~"
  switch: 1
  stringId: "Text_SFPlaze_LevelMax_Dianhuadeng_Tips"
}
rows {
  content: "升空"
  switch: 1
  stringId: "Text_SFPlaze_LevelMax_Button_Tips"
}
rows {
  content: "靠近其他星宝后，点此即可送上祝福"
  switch: 1
  stringId: "Text_SFPlaze_Name_Tiefuzi_Tips"
}
rows {
  content: "靠近其他星宝后，点此即可送上祝福"
  switch: 1
  stringId: "Text_SFPlaze_Name_Tiefuzi_MoreTips"
}
rows {
  content: "拿到福字啦，快去给其他星宝贴上吧~"
  switch: 1
  stringId: "Text_SFPlaze_Name_Tiefuzi_Get_Tips"
}
rows {
  content: "您的火车太长了，舞龙暂时最多支持{0}位"
  switch: 1
  stringId: "Text_SFPlaze_Wulong_OverLimit_Tips"
}
rows {
  content: "这条龙已经有太多星宝啦~"
  switch: 1
  stringId: "Text_SFPlaze_Wulong_NotAccess_Tips"
}
rows {
  content: "{0}内的星宝"
  switch: 1
  stringId: "UI_NewChat_PlayerNumTitle"
}
rows {
  content: "展示{0}中的其他星宝"
  switch: 1
  stringId: "UI_NewChat_PlayerNumSwitch"
}
rows {
  content: "{0}主人关闭了该功能"
  switch: 1
  stringId: "UI_NewChat_PlayerNumEmpty"
}
rows {
  content: "家园"
  switch: 1
  stringId: "UI_NewChat_PlayerNumHome"
}
rows {
  content: "农场"
  switch: 1
  stringId: "UI_NewChat_PlayerNumFarm"
}
rows {
  content: "等<PlayerNum>{0}人</>{1}"
  switch: 1
  stringId: "UI_NewChatLobbyBottom_PlayerNum_Title"
}
rows {
  content: "正在{0}中"
  switch: 1
  stringId: "UI_NewChatLobbyBottom_PlayerNum_Content"
}
rows {
  content: "你的状态有些特殊，暂时无法抱起对方哦~"
  switch: 1
  stringId: "Text_SFPlaze_HugNegative_Tips"
}
rows {
  content: "分享了我的位置"
  switch: 1
  stringId: "NewChat_SelfSharePosition"
}
rows {
  content: "您当前所使用的游戏版本不支持微信群聊功能，请前往app使用该功能"
  switch: 1
  stringId: "Text_Club_WeixinGroup_CloudGame_Tips"
}
rows {
  content: "重复发言过快，请勿刷屏~"
  switch: 1
  stringId: "NewChat_TooManyDuplicateText"
}
rows {
  content: "喵喵炸弹"
  switch: 1
  stringId: "Prop_MewMewBomb_Name"
}
rows {
  content: "丢出喵喵炸弹，一起畅享喵喵舞姿~"
  switch: 1
  stringId: "Prop_MewMewBomb_Tips"
}
rows {
  content: "丢出喵喵炸弹，一起畅享喵喵舞姿~"
  switch: 1
  stringId: "Prop_MewMewBomb_MoreTips"
}
rows {
  content: "暂无位置信息"
  switch: 1
  stringId: "UI_Club_NoneLocation"
}
rows {
  content: "确定是否擦除所有红点提示，您可能会错过某些<Red28>未读消息</>"
  switch: 1
  stringId: "NewChat_ClearAllNotRedDotMsg_Content"
}
rows {
  content: "怪盗基德的预告函…"
  switch: 1
  stringId: "Community_Conan_ChatBubble_CatchKid_1"
}
rows {
  content: "捉迷藏开始，请和柯南找到基德的所在处！"
  switch: 1
  stringId: "Community_Kid_Enter_Pretender"
}
rows {
  content: "原来是这么一回事…"
  switch: 1
  stringId: "Community_Conan_ChatBubble_CatchKid_2"
}
rows {
  content: "怪盗基德再次现身空中，等待星宝前往！"
  switch: 1
  stringId: "Community_Kid_Enter_Air"
}
rows {
  content: "下一幕的演出…"
  switch: 1
  stringId: "Community_Conan_ChatBubble_CatchKid_3"
}

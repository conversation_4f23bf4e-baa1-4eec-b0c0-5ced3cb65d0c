com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_特色玩法.xlsx sheet:活动-配饰卡池
rows {
  raffleId: 6001
  name: "梦幻狼人"
  startTime {
    seconds: 1722528000
  }
  endTime {
    seconds: 1724947199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 9001
    subPoolIds: 9001
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 57
  text: "奖励不会重复获得，9次必得舞台报告动画"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "初始奖池"
  raffleTagIcon: "T_Toby_Icon_Tab1"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1722528000
  }
  showEndTime {
    seconds: 1724947199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 6002
  name: "梦幻狼人"
  startTime {
    seconds: 1722528000
  }
  endTime {
    seconds: 1724947199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 9002
    subPoolIds: 9002
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 57
  text: "奖励不会重复获得，9次必得青蛙魔法攻击动画"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "六折奖池"
  raffleTagIcon: "T_Toby_Icon_Tab2"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1722528000
  }
  showEndTime {
    seconds: 1724947199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000001
  name: "特色玩法测试1"
  startTime {
    seconds: 1723392000
  }
  endTime {
    seconds: 1726502399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000001
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 205
  text: "奖励不会重复获得，9次内必得甜兔屋"
  lowestVersion: "1.3.12.52"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1723392000
  }
  showEndTime {
    seconds: 1726502399
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 9003
  name: "狼人登月卡池测试-弃"
  startTime {
    seconds: 1723824000
  }
  endTime {
    seconds: 1724947199
  }
  poolSelection {
    policy: RPP_OnGrandFreeRest
    poolId: 9003
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 218
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>9</>次必得舞台报告动画"
  lowestVersion: "1.3.12.116"
  raffleTagName: "时光穿梭"
  raffleTagIcon: "T_WerewolfTanabata_Icon_AwardPlateNormal"
  viewIndex: 7
  viewIndex: 8
  viewIndex: 5
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  viewIndex: 9
  showStartTime {
    seconds: 1723824000
  }
  showEndTime {
    seconds: 1724947199
  }
  isShow: true
  showRule: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 9004
  name: "狼人登月卡池测试-弃"
  startTime {
    seconds: 1723824000
  }
  endTime {
    seconds: 1724947199
  }
  poolSelection {
    policy: RPP_OnGrandFreeRest
    poolId: 9003
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 218
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>9</>次必得变蛙魔法攻击动画"
  lowestVersion: "1.3.12.116"
  raffleTagName: "月影之刃"
  raffleTagIcon: "T_WerewolfTanabata_Icon_AwardPlateSelect"
  viewIndex: 9
  viewIndex: 7
  viewIndex: 5
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  viewIndex: 8
  showStartTime {
    seconds: 1723824000
  }
  showEndTime {
    seconds: 1724947199
  }
  isShow: true
  showRule: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000002
  name: "丰收兔"
  startTime {
    seconds: 1727107200
  }
  endTime {
    seconds: 1729785599
  }
  poolSelection {
    policy: RPP_MultiLayer
    poolId: 10001001
    subPoolIds: 10001001
    subPoolIds: 10001002
    subPoolIds: 10001003
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 230
  text: "奖励不会重复获得，前两轮祈愿抽中大奖会进入下一轮"
  lowestVersion: "1.3.18.51"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 3
  viewIndex: 1
  viewIndex: 2
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 8
  viewIndex: 4
  viewIndex: 15
  viewIndex: 12
  viewIndex: 10
  viewIndex: 11
  viewIndex: 13
  viewIndex: 14
  viewIndex: 9
  showStartTime {
    seconds: 1727107200
  }
  showEndTime {
    seconds: 1729785599
  }
  isShow: true
  drawOverState: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000005
  name: "甜兔仙踪"
  startTime {
    seconds: 1726156800
  }
  endTime {
    seconds: 1729785599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000005
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 205
  text: "奖励不会重复获得，9次内必得甜兔屋"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1726156800
  }
  showEndTime {
    seconds: 1729785599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000006
  name: "绿洲奇遇"
  startTime {
    seconds: 1727884800
  }
  endTime {
    seconds: 1729785599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000006
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 235
  text: "奖励不会重复获得，9次内必得农场小屋装饰-仙人掌花屋！"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1727884800
  }
  showEndTime {
    seconds: 1729785599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000007
  name: "狼人魔法屋"
  startTime {
    seconds: 1727798400
  }
  endTime {
    seconds: 1728403199
  }
  poolSelection {
    policy: RPP_OnGrandFreeRest
    poolId: 10001007
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 210
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>9</>次必得变蛙魔法攻击动画"
  lowestVersion: "1.3.12.116"
  raffleTagName: "变蛙魔法"
  raffleTagIcon: "T_WerewolfTanabata_Icon_AwardPlateSelect"
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  viewIndex: 8
  showStartTime {
    seconds: 1727798400
  }
  showEndTime {
    seconds: 1728403199
  }
  isShow: true
  showRule: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000008
  name: "狼人魔法屋"
  startTime {
    seconds: 1727798400
  }
  endTime {
    seconds: 1728403199
  }
  poolSelection {
    policy: RPP_OnGrandFreeRest
    poolId: 10001008
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 210
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>9</>次必得舞台报告动画"
  lowestVersion: "1.3.12.116"
  raffleTagName: "舞台剧"
  raffleTagIcon: "T_WerewolfTanabata_Icon_AwardPlateNormal"
  viewIndex: 7
  viewIndex: 8
  viewIndex: 5
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  viewIndex: 9
  showStartTime {
    seconds: 1727798400
  }
  showEndTime {
    seconds: 1728403199
  }
  isShow: true
  showRule: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 60001031
  name: "扬帆起航"
  startTime {
    seconds: 1732550400
  }
  endTime {
    seconds: 1746201599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 60001031
    subPoolIds: 60001031
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 282
  text: "奖励不会重复获得，9次必得乘风破浪 夏侯惇"
  lowestVersion: "1.3.26.111"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "乘风破浪"
  raffleTagIcon: "T_HonorofKings6_Img_Head2"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1732550400
  }
  showEndTime {
    seconds: 1746201599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 60001032
  name: "扬帆起航"
  startTime {
    seconds: 1732550400
  }
  endTime {
    seconds: 1746201599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 60001032
    subPoolIds: 60001032
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 282
  text: "奖励不会重复获得，9次必得缤纷绘卷 张良"
  lowestVersion: "1.3.26.111"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "缤纷绘卷"
  raffleTagIcon: "T_HonorofKings6_Img_Head1"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1732550400
  }
  showEndTime {
    seconds: 1746201599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000009
  name: "仙狐花隐"
  startTime {
    seconds: 1727884800
  }
  endTime {
    seconds: 1732204799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000009
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 255
  text: "奖励不会重复获得，9次内必得农场稻草人装饰-狐仙！"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1727884800
  }
  showEndTime {
    seconds: 1732204799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000010
  name: "丰收兔"
  startTime {
    seconds: 1729785600
  }
  endTime {
    seconds: 1735228799
  }
  poolSelection {
    policy: RPP_MultiLayer
    poolId: 10000012
    subPoolIds: 10000010
    subPoolIds: 10000011
    subPoolIds: 10000012
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 230
  text: "奖励不会重复获得，前两轮祈愿抽中大奖会进入下一轮"
  lowestVersion: "1.3.18.51"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 3
  viewIndex: 1
  viewIndex: 2
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 8
  viewIndex: 4
  viewIndex: 15
  viewIndex: 12
  viewIndex: 10
  viewIndex: 11
  viewIndex: 13
  viewIndex: 14
  viewIndex: 9
  showStartTime {
    seconds: 1729785600
  }
  showEndTime {
    seconds: 1735228799
  }
  isShow: true
  drawOverState: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000011
  name: "海狮公主"
  startTime {
    seconds: 1740153600
  }
  endTime {
    seconds: 1743091199
  }
  poolSelection {
    policy: RPP_MultiLayer
    poolId: 10000015
    subPoolIds: 10000013
    subPoolIds: 10000014
    subPoolIds: 10000015
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 291
  text: "奖励不会重复获得，前两层获得配饰或农场装饰会直接进入下一层"
  lowestVersion: "1.3.18.51"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 3
  viewIndex: 1
  viewIndex: 2
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 8
  viewIndex: 4
  viewIndex: 15
  viewIndex: 12
  viewIndex: 10
  viewIndex: 11
  viewIndex: 13
  viewIndex: 14
  viewIndex: 9
  showStartTime {
    seconds: 1740153600
  }
  showEndTime {
    seconds: 1743091199
  }
  isShow: true
  drawOverState: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000012
  name: "幸运大翻牌"
  startTime {
    seconds: 1735747200
  }
  endTime {
    seconds: 1738252799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000012
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 297
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得大锤击飞动画"
  lowestVersion: "1.3.37.68"
  raffleTagName: "大锤击飞"
  raffleTagIcon: "T_WerewolfLuckyflop_Img_Animation1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  drawConditions {
    condition {
      conditionType: 827
      value: 2
      subConditionList {
        type: 242
        value: 10000013
        value: 10000014
        value: 10000015
      }
    }
  }
  showStartTime {
    seconds: 1735747200
  }
  showEndTime {
    seconds: 1738252799
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard_DrawView"
}
rows {
  raffleId: 10000013
  name: "幸运大翻牌"
  startTime {
    seconds: 1735747200
  }
  endTime {
    seconds: 1738252799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000013
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 297
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得敲锣打鼓动画"
  lowestVersion: "1.3.37.68"
  raffleTagName: "敲锣打鼓"
  raffleTagIcon: "T_WerewolfLuckyflop_Img_Animation2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  drawConditions {
    condition {
      conditionType: 827
      value: 2
      subConditionList {
        type: 242
        value: 10000012
        value: 10000014
        value: 10000015
      }
    }
  }
  showStartTime {
    seconds: 1735747200
  }
  showEndTime {
    seconds: 1738252799
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard_DrawView"
}
rows {
  raffleId: 10000014
  name: "幸运大翻牌"
  startTime {
    seconds: 1735747200
  }
  endTime {
    seconds: 1738252799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000014
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 297
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得小猪冲锋动画"
  lowestVersion: "1.3.37.68"
  raffleTagName: "小猪冲锋"
  raffleTagIcon: "T_WerewolfLuckyflop_Img_Animation3"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  drawConditions {
    condition {
      conditionType: 827
      value: 2
      subConditionList {
        type: 242
        value: 10000012
        value: 10000013
        value: 10000015
      }
    }
  }
  showStartTime {
    seconds: 1735747200
  }
  showEndTime {
    seconds: 1738252799
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard_DrawView"
}
rows {
  raffleId: 10000015
  name: "幸运大翻牌"
  startTime {
    seconds: 1735747200
  }
  endTime {
    seconds: 1738252799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000015
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 297
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得时空特警动画"
  lowestVersion: "1.3.37.68"
  raffleTagName: "时空特警"
  raffleTagIcon: "T_WerewolfLuckyflop_Img_Animation4"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  drawConditions {
    condition {
      conditionType: 827
      value: 2
      subConditionList {
        type: 242
        value: 10000012
        value: 10000013
        value: 10000014
      }
    }
  }
  showStartTime {
    seconds: 1735747200
  }
  showEndTime {
    seconds: 1738252799
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard_DrawView"
}
rows {
  raffleId: 10000016
  name: "雪球精灵"
  startTime {
    seconds: 1736438400
  }
  endTime {
    seconds: 1738252799
  }
  poolSelection {
    policy: RPP_MultiLayer
    poolId: 10000018
    subPoolIds: 10000016
    subPoolIds: 10000017
    subPoolIds: 10000018
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 293
  text: "奖励不会重复获得，前两轮祈愿抽中大奖会进入下一轮"
  lowestVersion: "1.3.18.51"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 3
  viewIndex: 1
  viewIndex: 2
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 8
  viewIndex: 4
  viewIndex: 15
  viewIndex: 12
  viewIndex: 10
  viewIndex: 11
  viewIndex: 13
  viewIndex: 14
  viewIndex: 9
  showStartTime {
    seconds: 1736438400
  }
  showEndTime {
    seconds: 1738252799
  }
  isShow: true
  drawOverState: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 60001061
  name: "峡谷战神"
  startTime {
    seconds: 1735920000
  }
  endTime {
    seconds: 1739462399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 60001061
    subPoolIds: 60001061
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 282
  text: "奖励不会重复获得，9次必得无双之魔 吕布"
  lowestVersion: "1.3.26.111"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "无双之魔"
  raffleTagIcon: "T_HonorofKings8_Img_Head2"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1735920000
  }
  showEndTime {
    seconds: 1739462399
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 60001062
  name: "峡谷战神"
  startTime {
    seconds: 1735920000
  }
  endTime {
    seconds: 1739462399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 60001062
    subPoolIds: 60001062
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 282
  text: "奖励不会重复获得，9次必得水晶猎龙者 花木兰"
  lowestVersion: "1.3.26.111"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "水晶猎龙者"
  raffleTagIcon: "T_HonorofKings8_Img_Head1"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1735920000
  }
  showEndTime {
    seconds: 1739462399
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000017
  name: "仙福盈门"
  startTime {
    seconds: 1735833600
  }
  endTime {
    seconds: 1740671999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000021
    subPoolIds: 10000021
  }
  dailyLimit: 10
  maxLimit: 10
  textRuleId: 307
  text: "奖励不会重复获得，勾选额外奖池可享受 <LoversYellow1>9</> 折总价优惠！|未勾选额外奖池抽中宠物装饰、配饰和农场装饰时，也可花费额外奖池相同抽数所需的幸运币直接购买对应的大奖哦！"
  lowestVersion: "1.3.18.51"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  viewIndex: 10
  showStartTime {
    seconds: 1735833600
  }
  showEndTime {
    seconds: 1740671999
  }
  isShow: true
  drawOverState: 1
  juniorItemAnimType: 1
  rewardStructureType: 8
}
rows {
  raffleId: 70001011
  name: "coc建筑皮肤测试"
  startTime {
    seconds: 1733241600
  }
  endTime {
    seconds: 1739462399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 70001011
    subPoolIds: 70001011
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 282
  text: "奖励不会重复获得，9次必得蓄露池皮肤"
  lowestVersion: "1.3.26.111"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "蓄露池皮肤"
  raffleTagIcon: "T_HonorofKings8_Img_Head2"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1733241600
  }
  showEndTime {
    seconds: 1739462399
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 70001012
  name: "coc建筑皮肤测试"
  startTime {
    seconds: 1733241600
  }
  endTime {
    seconds: 1739462399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 70001012
    subPoolIds: 70001012
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 282
  text: "奖励不会重复获得，9次必得储金罐皮肤"
  lowestVersion: "1.3.26.111"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "储金罐皮肤"
  raffleTagIcon: "T_HonorofKings8_Img_Head1"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1733241600
  }
  showEndTime {
    seconds: 1739462399
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000018
  name: "嘶嘶灵宝"
  startTime {
    seconds: 1736092800
  }
  endTime {
    seconds: 1740067199
  }
  poolSelection {
    policy: RPP_MultiLayer
    poolId: 10000022
    subPoolIds: 10000019
    subPoolIds: 10000020
    subPoolIds: 10000022
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 312
  text: "奖励不会重复获得，前两轮祈愿抽中大奖会进入下一轮"
  lowestVersion: "1.3.18.51"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 3
  viewIndex: 1
  viewIndex: 2
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 8
  viewIndex: 4
  viewIndex: 15
  viewIndex: 12
  viewIndex: 10
  viewIndex: 11
  viewIndex: 13
  viewIndex: 14
  viewIndex: 9
  showStartTime {
    seconds: 1736092800
  }
  showEndTime {
    seconds: 1740067199
  }
  isShow: true
  drawOverState: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000019
  name: "甜蜜魔法"
  startTime {
    seconds: 1744646400
  }
  endTime {
    seconds: 1747151999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000023
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 316
  text: "奖励不会重复获得，9次内必得梦幻萌宠屋！"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1744646400
  }
  showEndTime {
    seconds: 1747151999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000020
  name: "莱恩特咖啡屋"
  startTime {
    seconds: 1740153600
  }
  endTime {
    seconds: 1743091199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000020
  }
  dailyLimit: 7
  maxLimit: 7
  textRuleId: 322
  text: "奖励不会重复获得，<ShibaInuYellow>7</>次祈愿内必得<ShibaInuYellow>莱恩特</>"
  lowestVersion: "1.3.68.69"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1740153600
  }
  showEndTime {
    seconds: 1743091199
  }
  isShow: true
}
rows {
  raffleId: 60001111
  name: "峡谷女明星"
  startTime {
    seconds: 1750694400
  }
  endTime {
    seconds: 1752595199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 60001111
    subPoolIds: 60001111
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 282
  text: "奖励不会重复获得，9次必得绝世舞姬 貂蝉"
  lowestVersion: "1.3.88.116"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "绝世舞姬"
  raffleTagIcon: "T_HonorofKings10_Img_Head1"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1750694400
  }
  showEndTime {
    seconds: 1752595199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 60001112
  name: "峡谷女明星"
  startTime {
    seconds: 1750694400
  }
  endTime {
    seconds: 1752595199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 60001112
    subPoolIds: 60001112
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 282
  text: "奖励不会重复获得，9次必得冰雪之华 王昭君"
  lowestVersion: "1.3.88.116"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "冰雪之华"
  raffleTagIcon: "T_HonorofKings10_Img_Head2"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1750694400
  }
  showEndTime {
    seconds: 1752595199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000021
  name: "幸运大翻牌"
  startTime {
    seconds: 1739635200
  }
  endTime {
    seconds: 1742745599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000021
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 323
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得大锤击飞动画"
  lowestVersion: "1.3.68.69"
  raffleTagName: "大锤击飞"
  raffleTagIcon: "T_WerewolfLuckyflop_Img_Animation1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1739635200
  }
  showEndTime {
    seconds: 1742745599
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard_DrawView"
}
rows {
  raffleId: 10000022
  name: "幸运大翻牌"
  startTime {
    seconds: 1739635200
  }
  endTime {
    seconds: 1742745599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000022
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 323
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得敲锣打鼓动画"
  lowestVersion: "1.3.68.69"
  raffleTagName: "敲锣打鼓"
  raffleTagIcon: "T_WerewolfLuckyflop_Img_Animation2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1739635200
  }
  showEndTime {
    seconds: 1742745599
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard_DrawView"
}
rows {
  raffleId: 10000023
  name: "幸运大翻牌"
  startTime {
    seconds: 1739635200
  }
  endTime {
    seconds: 1742745599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000023
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 323
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得小猪冲锋动画"
  lowestVersion: "1.3.68.69"
  raffleTagName: "小猪冲锋"
  raffleTagIcon: "T_WerewolfLuckyflop_Img_Animation3"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1739635200
  }
  showEndTime {
    seconds: 1742745599
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard_DrawView"
}
rows {
  raffleId: 10000024
  name: "幸运大翻牌"
  startTime {
    seconds: 1739635200
  }
  endTime {
    seconds: 1742745599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000024
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 323
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得时空特警动画"
  lowestVersion: "1.3.68.69"
  raffleTagName: "时空特警"
  raffleTagIcon: "T_WerewolfLuckyflop_Img_Animation4"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1739635200
  }
  showEndTime {
    seconds: 1742745599
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard_DrawView"
}
rows {
  raffleId: 10000025
  name: "丰收派对"
  startTime {
    seconds: 1739462400
  }
  endTime {
    seconds: 1741017599
  }
  poolSelection {
    policy: RPP_OnDesire
    poolId: 10000025
    subPoolIds: 10000025
    subPoolIds: 10000026
  }
  dailyLimit: 18
  maxLimit: 18
  textRuleId: 331
  text: "两个奖池 <LoversYellow1>独立抽取</>，每个奖池内奖励不会重复获得||{0}/{1}次内必得"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  showStartTime {
    seconds: 1739462400
  }
  showEndTime {
    seconds: 1741017599
  }
  isShow: true
  animType: 4
  relateUmgSetting: "10;UI_Lottery_FarmPuppy_Popup_GiveGift"
  juniorItemAnimType: 1
  rewardStructureType: 7
  giftGiveCommodityIds: 300008
  giftGiveCommodityIds: 300009
  giftGiveCommodityIds: 300007
}
rows {
  raffleId: 10000026
  name: "幸运翻翻乐"
  startTime {
    seconds: 1742572800
  }
  endTime {
    seconds: 1744559999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000026
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 337
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得我胆子小动画"
  lowestVersion: "1.3.78.34"
  raffleTagName: "我胆子小"
  raffleTagIcon: "T_WerewolfLuckyflop02_Img_Animation2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1742572800
  }
  showEndTime {
    seconds: 1744559999
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard2_DrawView"
}
rows {
  raffleId: 10000027
  name: "幸运翻翻乐"
  startTime {
    seconds: 1742572800
  }
  endTime {
    seconds: 1744559999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000027
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 337
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得遥控坦克动画"
  lowestVersion: "1.3.78.34"
  raffleTagName: "遥控坦克"
  raffleTagIcon: "T_WerewolfLuckyflop02_Img_Animation1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1742572800
  }
  showEndTime {
    seconds: 1744559999
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard2_DrawView"
}
rows {
  raffleId: 60001131
  name: "峡谷英豪"
  startTime {
    seconds: 1743436800
  }
  endTime {
    seconds: 1746806399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 60001131
    subPoolIds: 60001131
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 340
  text: "奖励不会重复获得，6次必得绝代智谋 诸葛亮"
  lowestVersion: "1.3.78.29"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "绝代智谋"
  raffleTagIcon: "T_HonorofKings12_Img_Head1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1743436800
  }
  showEndTime {
    seconds: 1746806399
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 60001132
  name: "峡谷英豪"
  startTime {
    seconds: 1743436800
  }
  endTime {
    seconds: 1746806399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 60001132
    subPoolIds: 60001132
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 340
  text: "奖励不会重复获得，6次必得仁德义枪 刘备"
  lowestVersion: "1.3.78.29"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "仁德义枪"
  raffleTagIcon: "T_HonorofKings12_Img_Head2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1743436800
  }
  showEndTime {
    seconds: 1746806399
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000028
  name: "狐爷爷"
  startTime {
    seconds: 1743177600
  }
  endTime {
    seconds: 1745510399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000028
    poolDrawEnterPeriod: 9
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 339
  text: "奖励不会重复获得，9次内必得农场稻草人装饰-狐爷爷！"
  lowestVersion: "1.3.78.58"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1743177600
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  animType: 4
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000029
  name: "蜜糖彩虹之梦"
  startTime {
    seconds: 1745510400
  }
  endTime {
    seconds: 1748534399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000029
    subPoolIds: 10000029
  }
  dailyLimit: 10
  maxLimit: 10
  textRuleId: 341
  text: "10次内必得内圈所有奖励|祈愿获得内圈大奖时，可根据祈愿次数消耗不同数量幸运币带走对应外圈<FarmAmusementParkYellow>额外大奖</>！"
  lowestVersion: "1.3.78.96"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  viewIndex: 10
  showStartTime {
    seconds: 1745510400
  }
  showEndTime {
    seconds: 1748534399
  }
  isShow: true
  drawOverState: 1
  juniorItemAnimType: 1
  rewardStructureType: 8
}
rows {
  raffleId: 10000030
  name: "翡光仙灵"
  startTime {
    seconds: 1743782400
  }
  endTime {
    seconds: 1746460799
  }
  poolSelection {
    policy: RPP_MultiLayer
    poolId: 10000032
    subPoolIds: 10000030
    subPoolIds: 10000031
    subPoolIds: 10000032
  }
  dailyLimit: 18
  maxLimit: 18
  textRuleId: 344
  text: "再祈愿<VegetableHouse2>{0}次</>必得本层所有大奖|奖励不会重复获得，前两层获得配饰或农场装饰会进入下一层"
  lowestVersion: "*********"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 3
  viewIndex: 1
  viewIndex: 2
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 9
  viewIndex: 4
  viewIndex: 18
  viewIndex: 15
  viewIndex: 13
  viewIndex: 12
  viewIndex: 11
  viewIndex: 14
  viewIndex: 16
  viewIndex: 17
  viewIndex: 10
  showStartTime {
    seconds: 1743782400
  }
  showEndTime {
    seconds: 1746460799
  }
  isShow: true
  drawOverState: 1
  animType: 4
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000031
  name: "幸运MVP"
  startTime {
    seconds: 1743868800
  }
  endTime {
    seconds: 1746460799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000031
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 346
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得巨星登场动画"
  lowestVersion: "1.3.78.50"
  raffleTagName: "巨星登场"
  raffleTagIcon: "T_WerewolfMvp_Img_Animation1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1743868800
  }
  showEndTime {
    seconds: 1746460799
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfMVP_DrawView"
}
rows {
  raffleId: 10000032
  name: "煎饼超人"
  startTime {
    seconds: 1744387200
  }
  endTime {
    seconds: 1746719999
  }
  poolSelection {
    policy: RPP_MultiLayer
    poolId: 10000035
    subPoolIds: 10000033
    subPoolIds: 10000034
    subPoolIds: 10000035
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 347
  text: "再祈愿<FarmFoodScaYellow2>{0}次</>必定获得本层所有大奖"
  lowestVersion: "1.3.78.82"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 3
  viewIndex: 1
  viewIndex: 2
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 8
  viewIndex: 4
  viewIndex: 15
  viewIndex: 12
  viewIndex: 10
  viewIndex: 11
  viewIndex: 13
  viewIndex: 14
  viewIndex: 9
  showStartTime {
    seconds: 1744387200
  }
  showEndTime {
    seconds: 1746719999
  }
  isShow: true
  drawOverState: 1
  animType: 4
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000033
  name: "绿洲奇遇"
  startTime {
    seconds: 1727884800
  }
  endTime {
    seconds: 1732204799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000036
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 235
  text: "奖励不会重复获得，9次内必得农场小屋装饰-仙人掌花屋！"
  lowestVersion: "1.3.78.68"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1727884800
  }
  showEndTime {
    seconds: 1732204799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000034
  name: "幸运大翻牌"
  startTime {
    seconds: 1746720000
  }
  endTime {
    seconds: 1748793599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000034
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 354
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得紧急播报动画"
  lowestVersion: "1.3.88.1"
  raffleTagName: "紧急播报"
  raffleTagIcon: "T_WerewolfLuckyflop02_Img_Animation1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1746720000
  }
  showEndTime {
    seconds: 1748793599
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard_DrawView"
}
rows {
  raffleId: 10000035
  name: "幸运大翻牌"
  startTime {
    seconds: 1746720000
  }
  endTime {
    seconds: 1748793599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000035
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 354
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得宝葫芦动画"
  lowestVersion: "1.3.88.1"
  raffleTagName: "宝葫芦"
  raffleTagIcon: "T_WerewolfLuckyflop02_Img_Animation2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1746720000
  }
  showEndTime {
    seconds: 1748793599
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard_DrawView"
}
rows {
  raffleId: 10000036
  name: "幸运MVP"
  startTime {
    seconds: 1747929600
  }
  endTime {
    seconds: 1750607999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000036
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 360
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得彩虹天桥动画"
  lowestVersion: "1.3.88.50"
  raffleTagName: "彩虹天桥"
  raffleTagIcon: "T_WerewolfMvp_Img_Animation2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1747929600
  }
  showEndTime {
    seconds: 1750607999
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfMVP_DrawView"
}
rows {
  raffleId: 10000037
  name: "旅者驿站"
  startTime {
    seconds: 1748534400
  }
  endTime {
    seconds: 1750953599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000037
    poolDrawEnterPeriod: 9
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 365
  text: "奖励不会重复获得，9次内必得农场小屋装饰-沙洲旅人石屋！"
  lowestVersion: "1.3.88.112"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1748534400
  }
  showEndTime {
    seconds: 1750953599
  }
  isShow: true
  animType: 4
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000038
  name: "怒海狂鲨"
  startTime {
    seconds: 1747065600
  }
  endTime {
    seconds: 1752767999
  }
  poolSelection {
    policy: RPP_MultiLayer
    poolId: 10000040
    subPoolIds: 10000038
    subPoolIds: 10000039
    subPoolIds: 10000040
  }
  dailyLimit: 18
  maxLimit: 18
  textRuleId: 367
  text: "再祈愿<Shark2>{0}次</>必得本层所有大奖|奖励不会重复获得，前两层获得配饰或农场装饰会进入下一层"
  lowestVersion: "*********"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 3
  viewIndex: 1
  viewIndex: 2
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 9
  viewIndex: 4
  viewIndex: 18
  viewIndex: 15
  viewIndex: 13
  viewIndex: 12
  viewIndex: 11
  viewIndex: 14
  viewIndex: 16
  viewIndex: 17
  viewIndex: 10
  showStartTime {
    seconds: 1747065600
  }
  showEndTime {
    seconds: 1752767999
  }
  isShow: true
  drawOverState: 1
  animType: 4
  juniorItemAnimType: 1
}
rows {
  raffleId: 6000115
  name: "奇遇舞章"
  startTime {
    seconds: 1749830400
  }
  endTime {
    seconds: 1752508799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 6000115
    subPoolIds: 6000115
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 282
  text: "奖励不会重复获得，9次必得奇遇舞章 艾琳"
  lowestVersion: "1.3.88.105"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1749830400
  }
  showEndTime {
    seconds: 1752508799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000039
  name: "甜心琪琪"
  startTime {
    seconds: 1747584000
  }
  endTime {
    seconds: 1753372799
  }
  poolSelection {
    policy: RPP_MultiLayer
    poolId: 10000043
    subPoolIds: 10000041
    subPoolIds: 10000042
    subPoolIds: 10000043
  }
  dailyLimit: 15
  maxLimit: 15
  textRuleId: 369
  text: "再祈愿<FarmAmusementPark3Tips2>{0}次</>必得本层所有大奖|奖励不会重复获得，前两层获得配饰或农场装饰会进入下一层"
  lowestVersion: "*********"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 3
  viewIndex: 1
  viewIndex: 2
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 8
  viewIndex: 4
  viewIndex: 15
  viewIndex: 12
  viewIndex: 10
  viewIndex: 11
  viewIndex: 13
  viewIndex: 14
  viewIndex: 9
  showStartTime {
    seconds: 1747584000
  }
  showEndTime {
    seconds: 1753372799
  }
  isShow: true
  drawOverState: 1
  animType: 4
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000040
  name: "拾味桃源"
  startTime {
    seconds: 1750089600
  }
  endTime {
    seconds: 1753372799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10000044
    poolDrawEnterPeriod: 9
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 377
  text: "奖励不会重复获得，9次内必得农场餐厅装饰-杏花酒家！"
  lowestVersion: "**********"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1750089600
  }
  showEndTime {
    seconds: 1753372799
  }
  isShow: true
  animType: 4
  juniorItemAnimType: 1
}
rows {
  raffleId: 10000041
  name: "幸运翻翻乐"
  startTime {
    seconds: 1749744000
  }
  endTime {
    seconds: 1752163199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000041
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 337
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得花样滑冰动画"
  lowestVersion: "**********"
  raffleTagName: "花样滑冰"
  raffleTagIcon: "T_WerewolfLuckyflop02_Img_Animation2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1749744000
  }
  showEndTime {
    seconds: 1752163199
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard2_DrawView"
}
rows {
  raffleId: 10000042
  name: "幸运翻翻乐"
  startTime {
    seconds: 1749744000
  }
  endTime {
    seconds: 1752163199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000042
  }
  dailyLimit: 6
  maxLimit: 6
  textRuleId: 337
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>6</>次内必得钓了个鱼动画"
  lowestVersion: "**********"
  raffleTagName: "钓了个鱼"
  raffleTagIcon: "T_WerewolfLuckyflop02_Img_Animation1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  showStartTime {
    seconds: 1749744000
  }
  showEndTime {
    seconds: 1752163199
  }
  isShow: true
  showRule: 1
  relateUmgSetting: "11;UI_Lottery_WerewolfLuckCard2_DrawView"
}

com.tencent.wea.xlsRes.table_ChaseIdentityMvpAddProficiencyConfig
excel/xls/Chase/D_大王身份熟练度.xlsx sheet:熟练度MVP加分
rows {
  id: 1
  mvpScore: 0
  addCoefficient: 0.0
}
rows {
  id: 2
  mvpScore: 1
  addCoefficient: 0.01
}
rows {
  id: 3
  mvpScore: 49
  addCoefficient: 0.02
}
rows {
  id: 4
  mvpScore: 99
  addCoefficient: 0.03
}
rows {
  id: 5
  mvpScore: 149
  addCoefficient: 0.04
}
rows {
  id: 6
  mvpScore: 199
  addCoefficient: 0.05
}
rows {
  id: 7
  mvpScore: 249
  addCoefficient: 0.06
}
rows {
  id: 8
  mvpScore: 299
  addCoefficient: 0.07
}
rows {
  id: 9
  mvpScore: 349
  addCoefficient: 0.08
}
rows {
  id: 10
  mvpScore: 399
  addCoefficient: 0.09
}
rows {
  id: 11
  mvpScore: 449
  addCoefficient: 0.1
}
rows {
  id: 12
  mvpScore: 499
  addCoefficient: 0.11
}
rows {
  id: 13
  mvpScore: 549
  addCoefficient: 0.12
}
rows {
  id: 14
  mvpScore: 599
  addCoefficient: 0.13
}
rows {
  id: 15
  mvpScore: 649
  addCoefficient: 0.14
}
rows {
  id: 16
  mvpScore: 699
  addCoefficient: 0.15
}
rows {
  id: 17
  mvpScore: 749
  addCoefficient: 0.16
}
rows {
  id: 18
  mvpScore: 799
  addCoefficient: 0.17
}
rows {
  id: 19
  mvpScore: 849
  addCoefficient: 0.18
}
rows {
  id: 20
  mvpScore: 899
  addCoefficient: 0.19
}
rows {
  id: 21
  mvpScore: 949
  addCoefficient: 0.2
}

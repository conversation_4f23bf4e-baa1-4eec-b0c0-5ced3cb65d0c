com.tencent.wea.xlsRes.table_FarmCookLevelConf
excel/xls/Farm/N_农场餐厅等级表.xlsx sheet:餐厅等级表
rows {
  level: 1
  customerFlow: 10
  menuSize: 1
  workingCount: 2
  employeeLevel: 1
  hireRefreshCost: 120000
  basicCoin: 5
  coinGainParam {
    coin1h: 45000
    coin6h: 270000
    coin8h: 360000
    coin12h: 405000
    coin16h: 540000
    coin32h: 720000
  }
  customerFlowDebuff: -50
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 80000
  }
  employeeQualityRandInfo {
    randWeight: 20000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "10"
  effectDesc2: "菜单容量："
  effectDesc2: "1"
  effectDesc3: "可工作人数："
  effectDesc3: "2"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "0"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "1"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "1"
}
rows {
  level: 2
  needLike: 15
  needMainLevel: 80
  costCoin: 1001
  costNum: 42000000
  obtainFarmExp: 91000
  customerFlow: 20
  menuSize: 1
  workingCount: 2
  employeeLevel: 2
  hireRefreshCost: 140000
  basicCoin: 20
  coinGainParam {
    coin1h: 45000
    coin6h: 270000
    coin8h: 360000
    coin12h: 405000
    coin16h: 540000
    coin32h: 720000
  }
  customerFlowDebuff: -100
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 80000
  }
  employeeQualityRandInfo {
    randWeight: 20000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "20"
  effectDesc2: "菜单容量："
  effectDesc2: "1"
  effectDesc3: "可工作人数："
  effectDesc3: "2"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "0"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "1"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "2"
}
rows {
  level: 3
  needLike: 50
  needMainLevel: 80
  costCoin: 1001
  costNum: 42000000
  obtainFarmExp: 92000
  customerFlow: 30
  menuSize: 1
  workingCount: 2
  employeeLevel: 3
  hireRefreshCost: 160000
  basicCoin: 60
  coinGainParam {
    coin1h: 45000
    coin6h: 270000
    coin8h: 360000
    coin12h: 405000
    coin16h: 540000
    coin32h: 720000
  }
  customerFlowDebuff: -150
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 80000
  }
  employeeQualityRandInfo {
    randWeight: 20000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "30"
  effectDesc2: "菜单容量："
  effectDesc2: "1"
  effectDesc3: "可工作人数："
  effectDesc3: "2"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "0"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "1"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "3"
}
rows {
  level: 4
  needLike: 80
  needMainLevel: 80
  costCoin: 1001
  costNum: 43000000
  obtainFarmExp: 93000
  customerFlow: 40
  menuSize: 1
  workingCount: 2
  employeeLevel: 4
  hireRefreshCost: 180000
  basicCoin: 120
  coinGainParam {
    coin1h: 45000
    coin6h: 270000
    coin8h: 360000
    coin12h: 405000
    coin16h: 540000
    coin32h: 720000
  }
  customerFlowDebuff: -200
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 80000
  }
  employeeQualityRandInfo {
    randWeight: 20000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "40"
  effectDesc2: "菜单容量："
  effectDesc2: "1"
  effectDesc3: "可工作人数："
  effectDesc3: "2"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "0"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "1"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "4"
}
rows {
  level: 5
  needLike: 120
  needMainLevel: 80
  costCoin: 1001
  costNum: 44000000
  obtainFarmExp: 94000
  customerFlow: 50
  menuSize: 2
  workingCount: 3
  employeeLevel: 5
  hireRefreshCost: 200000
  basicCoin: 300
  coinGainParam {
    coin1h: 45000
    coin6h: 270000
    coin8h: 360000
    coin12h: 405000
    coin16h: 540000
    coin32h: 720000
  }
  customerFlowDebuff: -250
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 80000
  }
  employeeQualityRandInfo {
    randWeight: 20000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "50"
  effectDesc2: "菜单容量："
  effectDesc2: "2"
  effectDesc3: "可工作人数："
  effectDesc3: "3"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "0"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "1"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "4"
  effectDesc11: "解锁雇佣员工"
}
rows {
  level: 6
  needLike: 200
  needMainLevel: 80
  costCoin: 1001
  costNum: 45000000
  obtainFarmExp: 95000
  customerFlow: 60
  menuSize: 2
  workingCount: 3
  employeeLevel: 6
  hireRefreshCost: 220000
  basicCoin: 700
  coinGainParam {
    coin1h: 45000
    coin6h: 270000
    coin8h: 360000
    coin12h: 405000
    coin16h: 540000
    coin32h: 720000
  }
  customerFlowDebuff: -300
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 80000
  }
  employeeQualityRandInfo {
    randWeight: 20000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "60"
  effectDesc2: "菜单容量："
  effectDesc2: "2"
  effectDesc3: "可工作人数："
  effectDesc3: "3"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "0"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "1"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "5"
}
rows {
  level: 7
  needLike: 300
  needMainLevel: 80
  costCoin: 1001
  costNum: 46000000
  obtainFarmExp: 96000
  customerFlow: 70
  menuSize: 2
  workingCount: 3
  employeeLevel: 7
  hireRefreshCost: 240000
  basicCoin: 1800
  coinGainParam {
    coin1h: 45000
    coin6h: 270000
    coin8h: 360000
    coin12h: 405000
    coin16h: 540000
    coin32h: 720000
  }
  customerFlowDebuff: -320
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 80000
  }
  employeeQualityRandInfo {
    randWeight: 20000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "70"
  effectDesc2: "菜单容量："
  effectDesc2: "2"
  effectDesc3: "可工作人数："
  effectDesc3: "3"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "1"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "1"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "5"
  effectDesc11: "解锁餐厅点评"
}
rows {
  level: 8
  needLike: 1000
  needMainLevel: 80
  costCoin: 1001
  costNum: 47000000
  obtainFarmExp: 97000
  customerFlow: 80
  menuSize: 3
  workingCount: 4
  employeeLevel: 8
  hireRefreshCost: 260000
  basicCoin: 4400
  coinGainParam {
    coin1h: 45000
    coin6h: 270000
    coin8h: 360000
    coin12h: 405000
    coin16h: 540000
    coin32h: 720000
  }
  customerFlowDebuff: -340
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 80000
  }
  employeeQualityRandInfo {
    randWeight: 20000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "80"
  effectDesc2: "菜单容量："
  effectDesc2: "3"
  effectDesc3: "可工作人数："
  effectDesc3: "4"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "1"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "1"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "6"
}
rows {
  level: 9
  needLike: 3000
  needMainLevel: 80
  costCoin: 1001
  costNum: 48000000
  obtainFarmExp: 98000
  customerFlow: 90
  menuSize: 3
  workingCount: 4
  employeeLevel: 9
  hireRefreshCost: 280000
  basicCoin: 13000
  coinGainParam {
    coin1h: 45000
    coin6h: 270000
    coin8h: 360000
    coin12h: 405000
    coin16h: 540000
    coin32h: 720000
  }
  customerFlowDebuff: -360
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 80000
  }
  employeeQualityRandInfo {
    randWeight: 20000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "90"
  effectDesc2: "菜单容量："
  effectDesc2: "3"
  effectDesc3: "可工作人数："
  effectDesc3: "4"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "1"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "1"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "7"
}
rows {
  level: 10
  needLike: 10000
  needMainLevel: 80
  costCoin: 1001
  costNum: 49000000
  obtainFarmExp: 99000
  customerFlow: 100
  menuSize: 3
  workingCount: 4
  employeeLevel: 10
  hireRefreshCost: 300000
  basicCoin: 30000
  coinGainParam {
    coin1h: 45000
    coin6h: 270000
    coin8h: 360000
    coin12h: 405000
    coin16h: 540000
    coin32h: 720000
  }
  customerFlowDebuff: -380
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 80000
  }
  employeeQualityRandInfo {
    randWeight: 20000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "100"
  effectDesc2: "菜单容量："
  effectDesc2: "3"
  effectDesc3: "可工作人数："
  effectDesc3: "4"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "2"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "1"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "7"
  effectDesc10: "解锁室外流动屏"
  effectDesc11: "解锁预约玩法"
}
rows {
  level: 11
  needLike: 20000
  needMainLevel: 80
  costCoin: 1001
  costNum: 50000000
  obtainFarmExp: 100000
  customerFlow: 110
  menuSize: 3
  workingCount: 4
  employeeLevel: 11
  hireRefreshCost: 350000
  basicCoin: 48000
  coinGainParam {
    coin1h: 49500
    coin6h: 297000
    coin8h: 396000
    coin12h: 445500
    coin16h: 594000
    coin32h: 792000
  }
  customerFlowDebuff: -400
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 70000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "110"
  effectDesc2: "菜单容量："
  effectDesc2: "3"
  effectDesc3: "可工作人数："
  effectDesc3: "4"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "2"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "7"
  effectDesc7: "普通员工招募概率提升"
}
rows {
  level: 12
  needLike: 40000
  needMainLevel: 80
  costCoin: 1001
  costNum: 51000000
  obtainFarmExp: 100000
  customerFlow: 120
  menuSize: 3
  workingCount: 4
  employeeLevel: 12
  hireRefreshCost: 400000
  basicCoin: 68000
  coinGainParam {
    coin1h: 54000
    coin6h: 324000
    coin8h: 432000
    coin12h: 486000
    coin16h: 648000
    coin32h: 864000
  }
  customerFlowDebuff: -420
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 70000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "120"
  effectDesc2: "菜单容量："
  effectDesc2: "3"
  effectDesc3: "可工作人数："
  effectDesc3: "4"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "2"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "8"
}
rows {
  level: 13
  needLike: 50000
  needMainLevel: 80
  costCoin: 1001
  costNum: 52000000
  obtainFarmExp: 100000
  customerFlow: 130
  menuSize: 4
  workingCount: 5
  employeeLevel: 13
  hireRefreshCost: 450000
  basicCoin: 90000
  coinGainParam {
    coin1h: 58500
    coin6h: 351000
    coin8h: 468000
    coin12h: 526500
    coin16h: 702000
    coin32h: 936000
  }
  customerFlowDebuff: -440
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 70000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "130"
  effectDesc2: "菜单容量："
  effectDesc2: "4"
  effectDesc3: "可工作人数："
  effectDesc3: "5"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "3"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "8"
}
rows {
  level: 14
  needLike: 60000
  needMainLevel: 80
  costCoin: 1001
  costNum: 53000000
  obtainFarmExp: 105000
  customerFlow: 140
  menuSize: 4
  workingCount: 5
  employeeLevel: 14
  hireRefreshCost: 500000
  basicCoin: 110000
  coinGainParam {
    coin1h: 63000
    coin6h: 378000
    coin8h: 504000
    coin12h: 567000
    coin16h: 756000
    coin32h: 1008000
  }
  customerFlowDebuff: -460
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 70000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "140"
  effectDesc2: "菜单容量："
  effectDesc2: "4"
  effectDesc3: "可工作人数："
  effectDesc3: "5"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "3"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "9"
}
rows {
  level: 15
  needLike: 70000
  needMainLevel: 80
  costCoin: 1001
  costNum: 54000000
  obtainFarmExp: 105000
  customerFlow: 150
  menuSize: 4
  workingCount: 5
  employeeLevel: 15
  hireRefreshCost: 550000
  basicCoin: 140000
  coinGainParam {
    coin1h: 67500
    coin6h: 405000
    coin8h: 540000
    coin12h: 607500
    coin16h: 810000
    coin32h: 1080000
  }
  customerFlowDebuff: -480
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 70000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "150"
  effectDesc2: "菜单容量："
  effectDesc2: "4"
  effectDesc3: "可工作人数："
  effectDesc3: "5"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "3"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "10"
}
rows {
  level: 16
  needLike: 80000
  needMainLevel: 80
  costCoin: 1001
  costNum: 60000000
  obtainFarmExp: 105000
  customerFlow: 160
  menuSize: 4
  workingCount: 5
  employeeLevel: 16
  hireRefreshCost: 600000
  basicCoin: 170000
  coinGainParam {
    coin1h: 72000
    coin6h: 432000
    coin8h: 576000
    coin12h: 648000
    coin16h: 864000
    coin32h: 1152000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 63000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 7000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "160"
  effectDesc2: "菜单容量："
  effectDesc2: "4"
  effectDesc3: "可工作人数："
  effectDesc3: "5"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "4"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "10"
  effectDesc9: "可招募资深员工"
}
rows {
  level: 17
  needLike: 100000
  needMainLevel: 80
  costCoin: 1001
  costNum: 60000000
  obtainFarmExp: 105000
  customerFlow: 160
  menuSize: 4
  workingCount: 5
  employeeLevel: 17
  hireRefreshCost: 650000
  basicCoin: 200000
  coinGainParam {
    coin1h: 76500
    coin6h: 459000
    coin8h: 612000
    coin12h: 688500
    coin16h: 918000
    coin32h: 1224000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 63000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 7000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "160"
  effectDesc2: "菜单容量："
  effectDesc2: "4"
  effectDesc3: "可工作人数："
  effectDesc3: "5"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "4"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "10"
}
rows {
  level: 18
  needLike: 120000
  needMainLevel: 80
  costCoin: 1001
  costNum: 60000000
  obtainFarmExp: 105000
  customerFlow: 160
  menuSize: 5
  workingCount: 5
  employeeLevel: 18
  hireRefreshCost: 700000
  basicCoin: 230000
  coinGainParam {
    coin1h: 81000
    coin6h: 486000
    coin8h: 648000
    coin12h: 729000
    coin16h: 972000
    coin32h: 1296000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 63000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 7000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "160"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "5"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "4"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "11"
}
rows {
  level: 19
  needLike: 140000
  needMainLevel: 80
  costCoin: 1001
  costNum: 60000000
  obtainFarmExp: 110000
  customerFlow: 160
  menuSize: 5
  workingCount: 5
  employeeLevel: 19
  hireRefreshCost: 750000
  basicCoin: 260000
  coinGainParam {
    coin1h: 85500
    coin6h: 513000
    coin8h: 684000
    coin12h: 769500
    coin16h: 1026000
    coin32h: 1368000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 63000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 7000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "160"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "5"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "5"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "11"
}
rows {
  level: 20
  needLike: 160000
  needMainLevel: 80
  costCoin: 1001
  costNum: 60000000
  obtainFarmExp: 110000
  customerFlow: 200
  menuSize: 5
  workingCount: 6
  employeeLevel: 20
  hireRefreshCost: 800000
  basicCoin: 300000
  coinGainParam {
    coin1h: 90000
    coin6h: 540000
    coin8h: 720000
    coin12h: 810000
    coin16h: 1080000
    coin32h: 1440000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 63000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 7000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "200"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "5"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "11"
}
rows {
  level: 21
  needLike: 180000
  needMainLevel: 80
  costCoin: 1001
  costNum: 66000000
  obtainFarmExp: 110000
  customerFlow: 200
  menuSize: 5
  workingCount: 6
  employeeLevel: 21
  hireRefreshCost: 900000
  basicCoin: 340000
  coinGainParam {
    coin1h: 99000
    coin6h: 594000
    coin8h: 792000
    coin12h: 891000
    coin16h: 1188000
    coin32h: 1584000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "200"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "5"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "12"
  effectDesc7: "资深员工招募概率提升"
}
rows {
  level: 22
  needLike: 210000
  needMainLevel: 80
  costCoin: 1001
  costNum: 66000000
  obtainFarmExp: 110000
  customerFlow: 200
  menuSize: 5
  workingCount: 6
  employeeLevel: 22
  hireRefreshCost: 1000000
  basicCoin: 380000
  coinGainParam {
    coin1h: 108000
    coin6h: 648000
    coin8h: 864000
    coin12h: 972000
    coin16h: 1296000
    coin32h: 1728000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "200"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "6"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "12"
}
rows {
  level: 23
  needLike: 240000
  needMainLevel: 80
  costCoin: 1001
  costNum: 66000000
  obtainFarmExp: 110000
  customerFlow: 250
  menuSize: 5
  workingCount: 6
  employeeLevel: 23
  hireRefreshCost: 1100000
  basicCoin: 450000
  coinGainParam {
    coin1h: 117000
    coin6h: 702000
    coin8h: 936000
    coin12h: 1053000
    coin16h: 1404000
    coin32h: 1872000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "250"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "6"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "12"
}
rows {
  level: 24
  needLike: 270000
  needMainLevel: 80
  costCoin: 1001
  costNum: 66000000
  obtainFarmExp: 110000
  customerFlow: 250
  menuSize: 5
  workingCount: 6
  employeeLevel: 24
  hireRefreshCost: 1200000
  basicCoin: 520000
  coinGainParam {
    coin1h: 126000
    coin6h: 756000
    coin8h: 1008000
    coin12h: 1134000
    coin16h: 1512000
    coin32h: 2016000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "250"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "6"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "13"
}
rows {
  level: 25
  needLike: 300000
  needMainLevel: 80
  costCoin: 1001
  costNum: 66000000
  obtainFarmExp: 110000
  customerFlow: 250
  menuSize: 5
  workingCount: 6
  employeeLevel: 25
  hireRefreshCost: 1300000
  basicCoin: 600000
  coinGainParam {
    coin1h: 135000
    coin6h: 810000
    coin8h: 1080000
    coin12h: 1215000
    coin16h: 1620000
    coin32h: 2160000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "250"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "7"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "13"
}
rows {
  level: 26
  needLike: 330000
  needMainLevel: 85
  costCoin: 1001
  costNum: 72000000
  obtainFarmExp: 110000
  customerFlow: 250
  menuSize: 5
  workingCount: 6
  employeeLevel: 26
  hireRefreshCost: 1400000
  basicCoin: 680000
  coinGainParam {
    coin1h: 144000
    coin6h: 864000
    coin8h: 1152000
    coin12h: 1296000
    coin16h: 1728000
    coin32h: 2304000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 400
    protectValue: 12
    protectValue: 18
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "250"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "7"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "13"
  effectDesc9: "可招募知名员工"
  effectDesc10: "解锁人才推荐信"
}
rows {
  level: 27
  needLike: 380000
  needMainLevel: 85
  costCoin: 1001
  costNum: 72000000
  obtainFarmExp: 110000
  customerFlow: 250
  menuSize: 5
  workingCount: 6
  employeeLevel: 27
  hireRefreshCost: 1500000
  basicCoin: 770000
  coinGainParam {
    coin1h: 153000
    coin6h: 918000
    coin8h: 1224000
    coin12h: 1377000
    coin16h: 1836000
    coin32h: 2448000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 400
    protectValue: 12
    protectValue: 18
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "250"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "7"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "14"
}
rows {
  level: 28
  needLike: 430000
  needMainLevel: 85
  costCoin: 1001
  costNum: 78000000
  obtainFarmExp: 110000
  customerFlow: 250
  menuSize: 5
  workingCount: 6
  employeeLevel: 28
  hireRefreshCost: 1600000
  basicCoin: 860000
  coinGainParam {
    coin1h: 162000
    coin6h: 972000
    coin8h: 1296000
    coin12h: 1458000
    coin16h: 1944000
    coin32h: 2592000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 400
    protectValue: 12
    protectValue: 18
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "250"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "8"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "14"
}
rows {
  level: 29
  needLike: 480000
  needMainLevel: 85
  costCoin: 1001
  costNum: 78000000
  obtainFarmExp: 110000
  customerFlow: 300
  menuSize: 5
  workingCount: 6
  employeeLevel: 29
  hireRefreshCost: 1700000
  basicCoin: 950000
  coinGainParam {
    coin1h: 171000
    coin6h: 1026000
    coin8h: 1368000
    coin12h: 1539000
    coin16h: 2052000
    coin32h: 2736000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 400
    protectValue: 12
    protectValue: 18
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "300"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "8"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "14"
}
rows {
  level: 30
  needLike: 530000
  needMainLevel: 85
  costCoin: 1001
  costNum: 84000000
  obtainFarmExp: 110000
  customerFlow: 300
  menuSize: 5
  workingCount: 6
  employeeLevel: 30
  hireRefreshCost: 1800000
  basicCoin: 1200000
  coinGainParam {
    coin1h: 180000
    coin6h: 1080000
    coin8h: 1440000
    coin12h: 1620000
    coin16h: 2160000
    coin32h: 2880000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 400
    protectValue: 12
    protectValue: 18
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "300"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "6"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "8"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "2"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "15"
  effectDesc9: "可招募指定类型员工"
}
rows {
  level: 31
  needLike: 600000
  needMainLevel: 85
  costCoin: 1001
  costNum: 84000000
  obtainFarmExp: 110000
  customerFlow: 300
  menuSize: 5
  workingCount: 7
  employeeLevel: 31
  hireRefreshCost: 2000000
  basicCoin: 1300000
  coinGainParam {
    coin1h: 193500
    coin6h: 1161000
    coin8h: 1548000
    coin12h: 1741500
    coin16h: 2322000
    coin32h: 3096000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 500
    protectValue: 10
    protectValue: 15
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "300"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "7"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "9"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "15"
  effectDesc7: "知名员工招募概率提升"
}
rows {
  level: 32
  needLike: 700000
  needMainLevel: 85
  costCoin: 1001
  costNum: 91500000
  obtainFarmExp: 110000
  customerFlow: 350
  menuSize: 5
  workingCount: 7
  employeeLevel: 32
  hireRefreshCost: 2200000
  basicCoin: 1500000
  coinGainParam {
    coin1h: 207000
    coin6h: 1242000
    coin8h: 1656000
    coin12h: 1863000
    coin16h: 2484000
    coin32h: 3312000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 500
    protectValue: 10
    protectValue: 15
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "350"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "7"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "9"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "15"
}
rows {
  level: 33
  needLike: 800000
  needMainLevel: 85
  costCoin: 1001
  costNum: 93000000
  obtainFarmExp: 110000
  customerFlow: 350
  menuSize: 5
  workingCount: 7
  employeeLevel: 33
  hireRefreshCost: 2400000
  basicCoin: 1700000
  coinGainParam {
    coin1h: 220500
    coin6h: 1323000
    coin8h: 1764000
    coin12h: 1984500
    coin16h: 2646000
    coin32h: 3528000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 500
    protectValue: 10
    protectValue: 15
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "350"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "7"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "9"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "16"
}
rows {
  level: 34
  needLike: 900000
  needMainLevel: 85
  costCoin: 1001
  costNum: 110000000
  obtainFarmExp: 125000
  customerFlow: 350
  menuSize: 5
  workingCount: 7
  employeeLevel: 34
  hireRefreshCost: 2600000
  basicCoin: 2000000
  coinGainParam {
    coin1h: 234000
    coin6h: 1404000
    coin8h: 1872000
    coin12h: 2106000
    coin16h: 2808000
    coin32h: 3744000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 500
    protectValue: 10
    protectValue: 15
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "350"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "7"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "9"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "16"
}
rows {
  level: 35
  needLike: 1000000
  needMainLevel: 85
  costCoin: 1001
  costNum: 110000000
  obtainFarmExp: 125000
  customerFlow: 350
  menuSize: 5
  workingCount: 7
  employeeLevel: 35
  hireRefreshCost: 2800000
  basicCoin: 2200000
  coinGainParam {
    coin1h: 247500
    coin6h: 1485000
    coin8h: 1980000
    coin12h: 2227500
    coin16h: 2970000
    coin32h: 3960000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 60000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 500
    protectValue: 10
    protectValue: 15
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "350"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "7"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "9"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "16"
}
rows {
  level: 36
  needLike: 1100000
  needMainLevel: 90
  costCoin: 1001
  costNum: 135000000
  obtainFarmExp: 135000
  customerFlow: 350
  menuSize: 5
  workingCount: 7
  employeeLevel: 36
  hireRefreshCost: 3000000
  basicCoin: 2500000
  coinGainParam {
    coin1h: 261000
    coin6h: 1566000
    coin8h: 2088000
    coin12h: 2349000
    coin16h: 3132000
    coin32h: 4176000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 600
    protectValue: 8
    protectValue: 12
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "350"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "7"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "10"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "17"
  effectDesc7: "知名员工招募概率提升"
}
rows {
  level: 37
  needLike: 1200000
  needMainLevel: 90
  costCoin: 1001
  costNum: 140000000
  obtainFarmExp: 140000
  customerFlow: 350
  menuSize: 5
  workingCount: 7
  employeeLevel: 37
  hireRefreshCost: 3200000
  basicCoin: 2800000
  coinGainParam {
    coin1h: 274500
    coin6h: 1647000
    coin8h: 2196000
    coin12h: 2470500
    coin16h: 3294000
    coin32h: 4392000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 600
    protectValue: 8
    protectValue: 12
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "350"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "7"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "10"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "17"
}
rows {
  level: 38
  needLike: 1300000
  needMainLevel: 90
  costCoin: 1001
  costNum: 170000000
  obtainFarmExp: 145000
  customerFlow: 350
  menuSize: 5
  workingCount: 7
  employeeLevel: 38
  hireRefreshCost: 3400000
  basicCoin: 3200000
  coinGainParam {
    coin1h: 288000
    coin6h: 1728000
    coin8h: 2304000
    coin12h: 2592000
    coin16h: 3456000
    coin32h: 4608000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 600
    protectValue: 8
    protectValue: 12
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "350"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "7"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "10"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "17"
}
rows {
  level: 39
  needLike: 1400000
  needMainLevel: 90
  costCoin: 1001
  costNum: 170000000
  obtainFarmExp: 145000
  customerFlow: 400
  menuSize: 5
  workingCount: 7
  employeeLevel: 39
  hireRefreshCost: 3600000
  basicCoin: 3500000
  coinGainParam {
    coin1h: 301500
    coin6h: 1809000
    coin8h: 2412000
    coin12h: 2713500
    coin16h: 3618000
    coin32h: 4824000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 600
    protectValue: 8
    protectValue: 12
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "7"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "10"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "18"
}
rows {
  level: 40
  needLike: 1500000
  needMainLevel: 90
  costCoin: 1001
  costNum: 200000000
  obtainFarmExp: 150000
  customerFlow: 400
  menuSize: 5
  workingCount: 7
  employeeLevel: 40
  hireRefreshCost: 3800000
  basicCoin: 4000000
  coinGainParam {
    coin1h: 315000
    coin6h: 1890000
    coin8h: 2520000
    coin12h: 2835000
    coin16h: 3780000
    coin32h: 5040000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 600
    protectValue: 8
    protectValue: 12
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "7"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "10"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "18"
}
rows {
  level: 41
  needLike: 1600000
  needMainLevel: 90
  costCoin: 1001
  costNum: 205000000
  obtainFarmExp: 155000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 41
  hireRefreshCost: 4100000
  basicCoin: 4400000
  coinGainParam {
    coin1h: 333000
    coin6h: 1998000
    coin8h: 2664000
    coin12h: 2997000
    coin16h: 3996000
    coin32h: 5328000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 750
    protectValue: 7
    protectValue: 10
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "11"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "18"
  effectDesc7: "知名员工招募概率提升"
}
rows {
  level: 42
  needLike: 1800000
  needMainLevel: 90
  costCoin: 1001
  costNum: 235000000
  obtainFarmExp: 160000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 42
  hireRefreshCost: 4400000
  basicCoin: 4800000
  coinGainParam {
    coin1h: 351000
    coin6h: 2106000
    coin8h: 2808000
    coin12h: 3159000
    coin16h: 4212000
    coin32h: 5616000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 750
    protectValue: 7
    protectValue: 10
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "11"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "19"
}
rows {
  level: 43
  needLike: 2000000
  needMainLevel: 90
  costCoin: 1001
  costNum: 240000000
  obtainFarmExp: 160000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 43
  hireRefreshCost: 4700000
  basicCoin: 5200000
  coinGainParam {
    coin1h: 369000
    coin6h: 2214000
    coin8h: 2952000
    coin12h: 3321000
    coin16h: 4428000
    coin32h: 5904000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 750
    protectValue: 7
    protectValue: 10
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "11"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "19"
}
rows {
  level: 44
  needLike: 2200000
  needMainLevel: 90
  costCoin: 1001
  costNum: 270000000
  obtainFarmExp: 170000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 44
  hireRefreshCost: 5000000
  basicCoin: 5600000
  coinGainParam {
    coin1h: 387000
    coin6h: 2322000
    coin8h: 3096000
    coin12h: 3483000
    coin16h: 4644000
    coin32h: 6192000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 750
    protectValue: 7
    protectValue: 10
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "11"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "19"
}
rows {
  level: 45
  needLike: 2400000
  needMainLevel: 90
  costCoin: 1001
  costNum: 275000000
  obtainFarmExp: 170000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 45
  hireRefreshCost: 5300000
  basicCoin: 6200000
  coinGainParam {
    coin1h: 405000
    coin6h: 2430000
    coin8h: 3240000
    coin12h: 3645000
    coin16h: 4860000
    coin32h: 6480000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 750
    protectValue: 7
    protectValue: 10
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "11"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 46
  needLike: 2600000
  needMainLevel: 95
  costCoin: 1001
  costNum: 310000000
  obtainFarmExp: 175000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 46
  hireRefreshCost: 5600000
  basicCoin: 6900000
  coinGainParam {
    coin1h: 423000
    coin6h: 2538000
    coin8h: 3384000
    coin12h: 3807000
    coin16h: 5076000
    coin32h: 6768000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
  effectDesc7: "知名员工招募概率提升"
}
rows {
  level: 47
  needLike: 2800000
  needMainLevel: 95
  costCoin: 1001
  costNum: 340000000
  obtainFarmExp: 180000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 47
  hireRefreshCost: 5900000
  basicCoin: 7600000
  coinGainParam {
    coin1h: 441000
    coin6h: 2646000
    coin8h: 3528000
    coin12h: 3969000
    coin16h: 5292000
    coin32h: 7056000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 48
  needLike: 3000000
  needMainLevel: 95
  costCoin: 1001
  costNum: 385000000
  obtainFarmExp: 195000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 48
  hireRefreshCost: 6200000
  basicCoin: 8400000
  coinGainParam {
    coin1h: 459000
    coin6h: 2754000
    coin8h: 3672000
    coin12h: 4131000
    coin16h: 5508000
    coin32h: 7344000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 49
  needLike: 3200000
  needMainLevel: 95
  costCoin: 1001
  costNum: 470000000
  obtainFarmExp: 215000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 49
  hireRefreshCost: 6500000
  basicCoin: 9200000
  coinGainParam {
    coin1h: 477000
    coin6h: 2862000
    coin8h: 3816000
    coin12h: 4293000
    coin16h: 5724000
    coin32h: 7632000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 50
  needLike: 3400000
  needMainLevel: 95
  costCoin: 1001
  costNum: 630000000
  obtainFarmExp: 255000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 50
  hireRefreshCost: 6800000
  basicCoin: 10000000
  coinGainParam {
    coin1h: 495000
    coin6h: 2970000
    coin8h: 3960000
    coin12h: 4455000
    coin16h: 5940000
    coin32h: 7920000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 51
  needLike: 3600000
  needMainLevel: 101
  costCoin: 1001
  costNum: 760000000
  obtainFarmExp: 255000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 51
  hireRefreshCost: 7100000
  basicCoin: 11000000
  coinGainParam {
    coin1h: 522000
    coin6h: 3132000
    coin8h: 4176000
    coin12h: 4698000
    coin16h: 6264000
    coin32h: 8352000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
  effectDesc9: "可招募传奇员工"
}
rows {
  level: 52
  needLike: 3900000
  needMainLevel: 101
  costCoin: 1001
  costNum: 810000000
  obtainFarmExp: 265000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 52
  hireRefreshCost: 7400000
  basicCoin: 13000000
  coinGainParam {
    coin1h: 549000
    coin6h: 3294000
    coin8h: 4392000
    coin12h: 4941000
    coin16h: 6588000
    coin32h: 8784000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 53
  needLike: 4200000
  needMainLevel: 101
  costCoin: 1001
  costNum: 900000000
  obtainFarmExp: 285000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 53
  hireRefreshCost: 7700000
  basicCoin: 14000000
  coinGainParam {
    coin1h: 576000
    coin6h: 3456000
    coin8h: 4608000
    coin12h: 5184000
    coin16h: 6912000
    coin32h: 9216000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 54
  needLike: 4500000
  needMainLevel: 101
  costCoin: 1001
  costNum: 995000000
  obtainFarmExp: 310000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 54
  hireRefreshCost: 8000000
  basicCoin: 15000000
  coinGainParam {
    coin1h: 603000
    coin6h: 3618000
    coin8h: 4824000
    coin12h: 5427000
    coin16h: 7236000
    coin32h: 9648000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 55
  needLike: 4800000
  needMainLevel: 101
  costCoin: 1001
  costNum: 1100000000
  obtainFarmExp: 335000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 55
  hireRefreshCost: 8300000
  basicCoin: 17000000
  coinGainParam {
    coin1h: 630000
    coin6h: 3780000
    coin8h: 5040000
    coin12h: 5670000
    coin16h: 7560000
    coin32h: 10080000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 56
  needLike: 5100000
  needMainLevel: 106
  costCoin: 1001
  costNum: 1200000000
  obtainFarmExp: 355000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 56
  hireRefreshCost: 8600000
  basicCoin: 18000000
  coinGainParam {
    coin1h: 657000
    coin6h: 3942000
    coin8h: 5256000
    coin12h: 5913000
    coin16h: 7884000
    coin32h: 10512000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 57
  needLike: 5400000
  needMainLevel: 106
  costCoin: 1001
  costNum: 1300000000
  obtainFarmExp: 375000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 57
  hireRefreshCost: 8900000
  basicCoin: 20000000
  coinGainParam {
    coin1h: 684000
    coin6h: 4104000
    coin8h: 5472000
    coin12h: 6156000
    coin16h: 8208000
    coin32h: 10944000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 58
  needLike: 5700000
  needMainLevel: 106
  costCoin: 1001
  costNum: 1400000000
  obtainFarmExp: 400000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 58
  hireRefreshCost: 9200000
  basicCoin: 21000000
  coinGainParam {
    coin1h: 711000
    coin6h: 4266000
    coin8h: 5688000
    coin12h: 6399000
    coin16h: 8532000
    coin32h: 11376000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 59
  needLike: 6000000
  needMainLevel: 106
  costCoin: 1001
  costNum: 1500000000
  obtainFarmExp: 420000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 59
  hireRefreshCost: 9500000
  basicCoin: 23000000
  coinGainParam {
    coin1h: 738000
    coin6h: 4428000
    coin8h: 5904000
    coin12h: 6642000
    coin16h: 8856000
    coin32h: 11808000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 60
  needLike: 6300000
  needMainLevel: 106
  costCoin: 1001
  costNum: 1600000000
  obtainFarmExp: 440000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 60
  hireRefreshCost: 9800000
  basicCoin: 24000000
  coinGainParam {
    coin1h: 765000
    coin6h: 4590000
    coin8h: 6120000
    coin12h: 6885000
    coin16h: 9180000
    coin32h: 12240000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 61
  needLike: 6600000
  needMainLevel: 111
  costCoin: 1001
  costNum: 1700000000
  obtainFarmExp: 460000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 61
  hireRefreshCost: 10100000
  basicCoin: 26000000
  coinGainParam {
    coin1h: 805500
    coin6h: 4833000
    coin8h: 6444000
    coin12h: 7249500
    coin16h: 9666000
    coin32h: 12888000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 62
  needLike: 7000000
  needMainLevel: 111
  costCoin: 1001
  costNum: 1800000000
  obtainFarmExp: 485000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 62
  hireRefreshCost: 10400000
  basicCoin: 27000000
  coinGainParam {
    coin1h: 846000
    coin6h: 5076000
    coin8h: 6768000
    coin12h: 7614000
    coin16h: 10152000
    coin32h: 13536000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 63
  needLike: 7400000
  needMainLevel: 111
  costCoin: 1001
  costNum: 1950000000
  obtainFarmExp: 505000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 63
  hireRefreshCost: 10700000
  basicCoin: 29000000
  coinGainParam {
    coin1h: 886500
    coin6h: 5319000
    coin8h: 7092000
    coin12h: 7978500
    coin16h: 10638000
    coin32h: 14184000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 64
  needLike: 7800000
  needMainLevel: 111
  costCoin: 1001
  costNum: 2050000000
  obtainFarmExp: 525000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 64
  hireRefreshCost: 11000000
  basicCoin: 30000000
  coinGainParam {
    coin1h: 927000
    coin6h: 5562000
    coin8h: 7416000
    coin12h: 8343000
    coin16h: 11124000
    coin32h: 14832000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 65
  needLike: 8200000
  needMainLevel: 111
  costCoin: 1001
  costNum: 2150000000
  obtainFarmExp: 545000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 65
  hireRefreshCost: 11300000
  basicCoin: 32000000
  coinGainParam {
    coin1h: 967500
    coin6h: 5805000
    coin8h: 7740000
    coin12h: 8707500
    coin16h: 11610000
    coin32h: 15480000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 66
  needLike: 8600000
  needMainLevel: 116
  costCoin: 1001
  costNum: 2300000000
  obtainFarmExp: 565000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 66
  hireRefreshCost: 11600000
  basicCoin: 33000000
  coinGainParam {
    coin1h: 1008000
    coin6h: 6048000
    coin8h: 8064000
    coin12h: 9072000
    coin16h: 12096000
    coin32h: 16128000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 67
  needLike: 9000000
  needMainLevel: 116
  costCoin: 1001
  costNum: 2400000000
  obtainFarmExp: 585000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 67
  hireRefreshCost: 11900000
  basicCoin: 34000000
  coinGainParam {
    coin1h: 1048500
    coin6h: 6291000
    coin8h: 8388000
    coin12h: 9436500
    coin16h: 12582000
    coin32h: 16776000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 68
  needLike: 9400000
  needMainLevel: 116
  costCoin: 1001
  costNum: 2500000000
  obtainFarmExp: 600000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 68
  hireRefreshCost: 12200000
  basicCoin: 36000000
  coinGainParam {
    coin1h: 1089000
    coin6h: 6534000
    coin8h: 8712000
    coin12h: 9801000
    coin16h: 13068000
    coin32h: 17424000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 69
  needLike: 9800000
  needMainLevel: 116
  costCoin: 1001
  costNum: 2650000000
  obtainFarmExp: 620000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 69
  hireRefreshCost: 12500000
  basicCoin: 37000000
  coinGainParam {
    coin1h: 1129500
    coin6h: 6777000
    coin8h: 9036000
    coin12h: 10165500
    coin16h: 13554000
    coin32h: 18072000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 70
  needLike: 10200000
  needMainLevel: 116
  costCoin: 1001
  costNum: 2850000000
  obtainFarmExp: 665000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 70
  hireRefreshCost: 12800000
  basicCoin: 40000000
  coinGainParam {
    coin1h: 1170000
    coin6h: 7020000
    coin8h: 9360000
    coin12h: 10530000
    coin16h: 14040000
    coin32h: 18720000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 71
  needLike: 10600000
  needMainLevel: 121
  costCoin: 1001
  costNum: 3100000000
  obtainFarmExp: 715000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 71
  hireRefreshCost: 13200000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1215000
    coin6h: 7290000
    coin8h: 9720000
    coin12h: 10935000
    coin16h: 14580000
    coin32h: 19440000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 72
  needLike: 11100000
  needMainLevel: 121
  costCoin: 1001
  costNum: 3400000000
  obtainFarmExp: 780000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 72
  hireRefreshCost: 13600000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1260000
    coin6h: 7560000
    coin8h: 10080000
    coin12h: 11340000
    coin16h: 15120000
    coin32h: 20160000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 73
  needLike: 11600000
  needMainLevel: 121
  costCoin: 1001
  costNum: 3700000000
  obtainFarmExp: 850000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 73
  hireRefreshCost: 14000000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1305000
    coin6h: 7830000
    coin8h: 10440000
    coin12h: 11745000
    coin16h: 15660000
    coin32h: 20880000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 74
  needLike: 12100000
  needMainLevel: 121
  costCoin: 1001
  costNum: 4100000000
  obtainFarmExp: 940000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 74
  hireRefreshCost: 14400000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1350000
    coin6h: 8100000
    coin8h: 10800000
    coin12h: 12150000
    coin16h: 16200000
    coin32h: 21600000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 75
  needLike: 12600000
  needMainLevel: 121
  costCoin: 1001
  costNum: 4500000000
  obtainFarmExp: 1050000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 75
  hireRefreshCost: 14800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1395000
    coin6h: 8370000
    coin8h: 11160000
    coin12h: 12555000
    coin16h: 16740000
    coin32h: 22320000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 76
  needLike: 13100000
  needMainLevel: 126
  costCoin: 1001
  costNum: 4900000000
  obtainFarmExp: 1100000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 76
  hireRefreshCost: 15200000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1440000
    coin6h: 8640000
    coin8h: 11520000
    coin12h: 12960000
    coin16h: 17280000
    coin32h: 23040000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 77
  needLike: 13600000
  needMainLevel: 126
  costCoin: 1001
  costNum: 5300000000
  obtainFarmExp: 1200000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 77
  hireRefreshCost: 15600000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1485000
    coin6h: 8910000
    coin8h: 11880000
    coin12h: 13365000
    coin16h: 17820000
    coin32h: 23760000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 78
  needLike: 14100000
  needMainLevel: 126
  costCoin: 1001
  costNum: 5700000000
  obtainFarmExp: 1300000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 78
  hireRefreshCost: 16000000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1530000
    coin6h: 9180000
    coin8h: 12240000
    coin12h: 13770000
    coin16h: 18360000
    coin32h: 24480000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 79
  needLike: 14600000
  needMainLevel: 126
  costCoin: 1001
  costNum: 6100000000
  obtainFarmExp: 1400000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 79
  hireRefreshCost: 16400000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1575000
    coin6h: 9450000
    coin8h: 12600000
    coin12h: 14175000
    coin16h: 18900000
    coin32h: 25200000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 80
  needLike: 15100000
  needMainLevel: 126
  costCoin: 1001
  costNum: 6500000000
  obtainFarmExp: 1450000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 80
  hireRefreshCost: 16800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1620000
    coin6h: 9720000
    coin8h: 12960000
    coin12h: 14580000
    coin16h: 19440000
    coin32h: 25920000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 81
  needLike: 15600000
  needMainLevel: 131
  costCoin: 1001
  costNum: 6750000000
  obtainFarmExp: 1500000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 81
  hireRefreshCost: 17300000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1674000
    coin6h: 10044000
    coin8h: 13392000
    coin12h: 15066000
    coin16h: 20088000
    coin32h: 26784000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 82
  needLike: 16300000
  needMainLevel: 131
  costCoin: 1001
  costNum: 7000000000
  obtainFarmExp: 1550000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 82
  hireRefreshCost: 17800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1728000
    coin6h: 10368000
    coin8h: 13824000
    coin12h: 15552000
    coin16h: 20736000
    coin32h: 27648000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 83
  needLike: 17000000
  needMainLevel: 131
  costCoin: 1001
  costNum: 7250000000
  obtainFarmExp: 1650000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 83
  hireRefreshCost: 18300000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1782000
    coin6h: 10692000
    coin8h: 14256000
    coin12h: 16038000
    coin16h: 21384000
    coin32h: 28512000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 84
  needLike: 17700000
  needMainLevel: 131
  costCoin: 1001
  costNum: 7500000000
  obtainFarmExp: 1700000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 84
  hireRefreshCost: 18800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1836000
    coin6h: 11016000
    coin8h: 14688000
    coin12h: 16524000
    coin16h: 22032000
    coin32h: 29376000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 85
  needLike: 18400000
  needMainLevel: 131
  costCoin: 1001
  costNum: 7750000000
  obtainFarmExp: 1750000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 85
  hireRefreshCost: 19300000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1890000
    coin6h: 11340000
    coin8h: 15120000
    coin12h: 17010000
    coin16h: 22680000
    coin32h: 30240000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 86
  needLike: 19100000
  needMainLevel: 136
  costCoin: 1001
  costNum: 8000000000
  obtainFarmExp: 1800000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 86
  hireRefreshCost: 19800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1944000
    coin6h: 11664000
    coin8h: 15552000
    coin12h: 17496000
    coin16h: 23328000
    coin32h: 31104000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 87
  needLike: 19800000
  needMainLevel: 136
  costCoin: 1001
  costNum: 8250000000
  obtainFarmExp: 1850000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 87
  hireRefreshCost: 20300000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 1998000
    coin6h: 11988000
    coin8h: 15984000
    coin12h: 17982000
    coin16h: 23976000
    coin32h: 31968000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 88
  needLike: 20500000
  needMainLevel: 136
  costCoin: 1001
  costNum: 8500000000
  obtainFarmExp: 1900000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 88
  hireRefreshCost: 20800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2052000
    coin6h: 12312000
    coin8h: 16416000
    coin12h: 18468000
    coin16h: 24624000
    coin32h: 32832000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 89
  needLike: 21200000
  needMainLevel: 136
  costCoin: 1001
  costNum: 8750000000
  obtainFarmExp: 1950000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 89
  hireRefreshCost: 21300000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2106000
    coin6h: 12636000
    coin8h: 16848000
    coin12h: 18954000
    coin16h: 25272000
    coin32h: 33696000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 90
  needLike: 21900000
  needMainLevel: 136
  costCoin: 1001
  costNum: 9000000000
  obtainFarmExp: 2000000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 90
  hireRefreshCost: 21800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2160000
    coin6h: 12960000
    coin8h: 17280000
    coin12h: 19440000
    coin16h: 25920000
    coin32h: 34560000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 91
  needLike: 22600000
  needMainLevel: 141
  costCoin: 1001
  costNum: 9400000000
  obtainFarmExp: 2050000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 91
  hireRefreshCost: 22500000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2232000
    coin6h: 13392000
    coin8h: 17856000
    coin12h: 20088000
    coin16h: 26784000
    coin32h: 35712000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 92
  needLike: 23600000
  needMainLevel: 141
  costCoin: 1001
  costNum: 9800000000
  obtainFarmExp: 2150000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 92
  hireRefreshCost: 23200000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2304000
    coin6h: 13824000
    coin8h: 18432000
    coin12h: 20736000
    coin16h: 27648000
    coin32h: 36864000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 93
  needLike: 24600000
  needMainLevel: 141
  costCoin: 1001
  costNum: 10000000000
  obtainFarmExp: 2250000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 93
  hireRefreshCost: 23900000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2376000
    coin6h: 14256000
    coin8h: 19008000
    coin12h: 21384000
    coin16h: 28512000
    coin32h: 38016000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 94
  needLike: 25600000
  needMainLevel: 141
  costCoin: 1001
  costNum: 10500000000
  obtainFarmExp: 2300000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 94
  hireRefreshCost: 24600000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2448000
    coin6h: 14688000
    coin8h: 19584000
    coin12h: 22032000
    coin16h: 29376000
    coin32h: 39168000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 95
  needLike: 26600000
  needMainLevel: 141
  costCoin: 1001
  costNum: 11000000000
  obtainFarmExp: 2400000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 95
  hireRefreshCost: 25300000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2520000
    coin6h: 15120000
    coin8h: 20160000
    coin12h: 22680000
    coin16h: 30240000
    coin32h: 40320000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 96
  needLike: 27600000
  needMainLevel: 146
  costCoin: 1001
  costNum: 11500000000
  obtainFarmExp: 2500000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 96
  hireRefreshCost: 26000000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2592000
    coin6h: 15552000
    coin8h: 20736000
    coin12h: 23328000
    coin16h: 31104000
    coin32h: 41472000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 97
  needLike: 28600000
  needMainLevel: 146
  costCoin: 1001
  costNum: 12000000000
  obtainFarmExp: 2550000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 97
  hireRefreshCost: 26700000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2664000
    coin6h: 15984000
    coin8h: 21312000
    coin12h: 23976000
    coin16h: 31968000
    coin32h: 42624000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 98
  needLike: 29600000
  needMainLevel: 146
  costCoin: 1001
  costNum: 12000000000
  obtainFarmExp: 2650000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 98
  hireRefreshCost: 27400000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2736000
    coin6h: 16416000
    coin8h: 21888000
    coin12h: 24624000
    coin16h: 32832000
    coin32h: 43776000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 99
  needLike: 30600000
  needMainLevel: 146
  costCoin: 1001
  costNum: 12500000000
  obtainFarmExp: 2750000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 99
  hireRefreshCost: 28100000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2808000
    coin6h: 16848000
    coin8h: 22464000
    coin12h: 25272000
    coin16h: 33696000
    coin32h: 44928000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 100
  needLike: 31600000
  needMainLevel: 146
  costCoin: 1001
  costNum: 13000000000
  obtainFarmExp: 2800000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 100
  hireRefreshCost: 28800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2880000
    coin6h: 17280000
    coin8h: 23040000
    coin12h: 25920000
    coin16h: 34560000
    coin32h: 46080000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 101
  needLike: 32600000
  needMainLevel: 151
  costCoin: 1001
  costNum: 13500000000
  obtainFarmExp: 2900000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 101
  hireRefreshCost: 29700000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 2970000
    coin6h: 17820000
    coin8h: 23760000
    coin12h: 26730000
    coin16h: 35640000
    coin32h: 47520000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 102
  needLike: 33900000
  needMainLevel: 151
  costCoin: 1001
  costNum: 14000000000
  obtainFarmExp: 3000000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 102
  hireRefreshCost: 30600000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 3060000
    coin6h: 18360000
    coin8h: 24480000
    coin12h: 27540000
    coin16h: 36720000
    coin32h: 48960000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 103
  needLike: 35200000
  needMainLevel: 151
  costCoin: 1001
  costNum: 14500000000
  obtainFarmExp: 3100000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 103
  hireRefreshCost: 31500000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 3150000
    coin6h: 18900000
    coin8h: 25200000
    coin12h: 28350000
    coin16h: 37800000
    coin32h: 50400000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 104
  needLike: 36500000
  needMainLevel: 151
  costCoin: 1001
  costNum: 15000000000
  obtainFarmExp: 3200000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 104
  hireRefreshCost: 32400000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 3240000
    coin6h: 19440000
    coin8h: 25920000
    coin12h: 29160000
    coin16h: 38880000
    coin32h: 51840000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 105
  needLike: 37800000
  needMainLevel: 151
  costCoin: 1001
  costNum: 15500000000
  obtainFarmExp: 3300000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 105
  hireRefreshCost: 33300000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 3330000
    coin6h: 19980000
    coin8h: 26640000
    coin12h: 29970000
    coin16h: 39960000
    coin32h: 53280000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 106
  needLike: 39100000
  needMainLevel: 156
  costCoin: 1001
  costNum: 16000000000
  obtainFarmExp: 3400000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 106
  hireRefreshCost: 34200000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 3420000
    coin6h: 20520000
    coin8h: 27360000
    coin12h: 30780000
    coin16h: 41040000
    coin32h: 54720000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 107
  needLike: 40400000
  needMainLevel: 156
  costCoin: 1001
  costNum: 16500000000
  obtainFarmExp: 3500000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 107
  hireRefreshCost: 35100000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 3510000
    coin6h: 21060000
    coin8h: 28080000
    coin12h: 31590000
    coin16h: 42120000
    coin32h: 56160000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 108
  needLike: 41700000
  needMainLevel: 156
  costCoin: 1001
  costNum: 17000000000
  obtainFarmExp: 3600000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 108
  hireRefreshCost: 36000000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 3600000
    coin6h: 21600000
    coin8h: 28800000
    coin12h: 32400000
    coin16h: 43200000
    coin32h: 57600000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 109
  needLike: 43000000
  needMainLevel: 156
  costCoin: 1001
  costNum: 17500000000
  obtainFarmExp: 3700000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 109
  hireRefreshCost: 36900000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 3690000
    coin6h: 22140000
    coin8h: 29520000
    coin12h: 33210000
    coin16h: 44280000
    coin32h: 59040000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 110
  needLike: 44300000
  needMainLevel: 156
  costCoin: 1001
  costNum: 18000000000
  obtainFarmExp: 3800000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 110
  hireRefreshCost: 37800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 3780000
    coin6h: 22680000
    coin8h: 30240000
    coin12h: 34020000
    coin16h: 45360000
    coin32h: 60480000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 111
  needLike: 45600000
  needMainLevel: 161
  costCoin: 1001
  costNum: 19000000000
  obtainFarmExp: 3950000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 111
  hireRefreshCost: 39000000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 3892500
    coin6h: 23355000
    coin8h: 31140000
    coin12h: 35032500
    coin16h: 46710000
    coin32h: 62280000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 112
  needLike: 47200000
  needMainLevel: 161
  costCoin: 1001
  costNum: 19500000000
  obtainFarmExp: 4050000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 112
  hireRefreshCost: 40200000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 4005000
    coin6h: 24030000
    coin8h: 32040000
    coin12h: 36045000
    coin16h: 48060000
    coin32h: 64080000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 113
  needLike: 48800000
  needMainLevel: 161
  costCoin: 1001
  costNum: 20500000000
  obtainFarmExp: 4200000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 113
  hireRefreshCost: 41400000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 4117500
    coin6h: 24705000
    coin8h: 32940000
    coin12h: 37057500
    coin16h: 49410000
    coin32h: 65880000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 114
  needLike: 50400000
  needMainLevel: 161
  costCoin: 1001
  costNum: 21000000000
  obtainFarmExp: 4300000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 114
  hireRefreshCost: 42600000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 4230000
    coin6h: 25380000
    coin8h: 33840000
    coin12h: 38070000
    coin16h: 50760000
    coin32h: 67680000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 115
  needLike: 52000000
  needMainLevel: 161
  costCoin: 1001
  costNum: 22000000000
  obtainFarmExp: 4400000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 115
  hireRefreshCost: 43800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 4342500
    coin6h: 26055000
    coin8h: 34740000
    coin12h: 39082500
    coin16h: 52110000
    coin32h: 69480000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 116
  needLike: 53600000
  needMainLevel: 166
  costCoin: 1001
  costNum: 22500000000
  obtainFarmExp: 4500000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 116
  hireRefreshCost: 45000000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 4455000
    coin6h: 26730000
    coin8h: 35640000
    coin12h: 40095000
    coin16h: 53460000
    coin32h: 71280000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 117
  needLike: 55200000
  needMainLevel: 166
  costCoin: 1001
  costNum: 23500000000
  obtainFarmExp: 4600000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 117
  hireRefreshCost: 46200000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 4567500
    coin6h: 27405000
    coin8h: 36540000
    coin12h: 41107500
    coin16h: 54810000
    coin32h: 73080000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 118
  needLike: 56800000
  needMainLevel: 166
  costCoin: 1001
  costNum: 24000000000
  obtainFarmExp: 4700000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 118
  hireRefreshCost: 47400000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 4680000
    coin6h: 28080000
    coin8h: 37440000
    coin12h: 42120000
    coin16h: 56160000
    coin32h: 74880000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 119
  needLike: 58400000
  needMainLevel: 166
  costCoin: 1001
  costNum: 25000000000
  obtainFarmExp: 4800000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 119
  hireRefreshCost: 48600000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 4792500
    coin6h: 28755000
    coin8h: 38340000
    coin12h: 43132500
    coin16h: 57510000
    coin32h: 76680000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}
rows {
  level: 120
  needLike: 60000000
  needMainLevel: 166
  costCoin: 1001
  costNum: 25500000000
  obtainFarmExp: 4900000
  customerFlow: 400
  menuSize: 5
  workingCount: 8
  employeeLevel: 120
  hireRefreshCost: 49800000
  basicCoin: 42000000
  coinGainParam {
    coin1h: 4905000
    coin6h: 29430000
    coin8h: 39240000
    coin12h: 44145000
    coin16h: 58860000
    coin32h: 78480000
  }
  customerFlowDebuff: -500
  charmDebuff: 0
  employeeQualityRandInfo {
    randWeight: 59000
  }
  employeeQualityRandInfo {
    randWeight: 30000
  }
  employeeQualityRandInfo {
    randWeight: 10000
  }
  employeeQualityRandInfo {
    randWeight: 1000
    protectValue: 5
    protectValue: 8
  }
  employeeQualityRandInfo {
    randWeight: 125
    protectValue: 40
    protectValue: 60
  }
  incomeAdd: 0.0
  effectDesc1: "人气值："
  effectDesc1: "400"
  effectDesc2: "菜单容量："
  effectDesc2: "5"
  effectDesc3: "可工作人数："
  effectDesc3: "8"
  effectDesc4: "餐厅可扩建次数："
  effectDesc4: "12"
  effectDesc5: "可摆放灶台数量："
  effectDesc5: "3"
  effectDesc6: "可摆放餐位数量："
  effectDesc6: "20"
}

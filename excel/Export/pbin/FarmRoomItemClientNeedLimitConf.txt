com.tencent.wea.xlsRes.table_FarmRoomItemClientNeedLimitConf
excel/xls/Farm/N_农场家具表.xlsx sheet:客户端功能家具等级限制表
rows {
  itemId: 10614
  buildingTypeId: 9
  buildingLevel: 53
}
rows {
  itemId: 10615
  buildingTypeId: 9
  buildingLevel: 56
}
rows {
  itemId: 10616
  buildingTypeId: 9
  buildingLevel: 51
}
rows {
  itemId: 10618
  buildingTypeId: 9
  buildingLevel: 7
}
rows {
  itemId: 10619
  buildingTypeId: 9
  buildingLevel: 17
}
rows {
  itemId: 10620
  buildingTypeId: 9
  buildingLevel: 1
}
rows {
  itemId: 10621
  buildingTypeId: 9
  buildingLevel: 1
}
rows {
  itemId: 10622
  buildingTypeId: 9
  buildingLevel: 1
}
rows {
  itemId: 10628
  buildingTypeId: 9
  buildingLevel: 11
}
rows {
  itemId: 10629
  buildingTypeId: 9
  buildingLevel: 13
}
rows {
  itemId: 10630
  buildingTypeId: 9
  buildingLevel: 16
}
rows {
  itemId: 10631
  buildingTypeId: 9
  buildingLevel: 21
}
rows {
  itemId: 10632
  buildingTypeId: 9
  buildingLevel: 23
}
rows {
  itemId: 10633
  buildingTypeId: 9
  buildingLevel: 26
}
rows {
  itemId: 10634
  buildingTypeId: 9
  buildingLevel: 28
}
rows {
  itemId: 10635
  buildingTypeId: 9
  buildingLevel: 31
}
rows {
  itemId: 10636
  buildingTypeId: 9
  buildingLevel: 33
}
rows {
  itemId: 10637
  buildingTypeId: 9
  buildingLevel: 36
}
rows {
  itemId: 10638
  buildingTypeId: 9
  buildingLevel: 38
}
rows {
  itemId: 10639
  buildingTypeId: 9
  buildingLevel: 41
}
rows {
  itemId: 10640
  buildingTypeId: 9
  buildingLevel: 43
}
rows {
  itemId: 10641
  buildingTypeId: 9
  buildingLevel: 46
}
rows {
  itemId: 10642
  buildingTypeId: 9
  buildingLevel: 48
}
rows {
  itemId: 10643
  buildingTypeId: 9
  buildingLevel: 58
}
rows {
  itemId: 10644
  buildingTypeId: 9
  buildingLevel: 61
}
rows {
  itemId: 10645
  buildingTypeId: 9
  buildingLevel: 63
}
rows {
  itemId: 10646
  buildingTypeId: 9
  buildingLevel: 66
}
rows {
  itemId: 10647
  buildingTypeId: 9
  buildingLevel: 68
}
rows {
  itemId: 10648
  buildingTypeId: 9
  buildingLevel: 73
}
rows {
  itemId: 10649
  buildingTypeId: 9
  buildingLevel: 76
}
rows {
  itemId: 10650
  buildingTypeId: 9
  buildingLevel: 71
}
rows {
  itemId: 10651
  buildingTypeId: 9
  buildingLevel: 78
}
rows {
  itemId: 10652
  buildingTypeId: 9
  buildingLevel: 83
}
rows {
  itemId: 10653
  buildingTypeId: 9
  buildingLevel: 86
}
rows {
  itemId: 10654
  buildingTypeId: 9
  buildingLevel: 81
}
rows {
  itemId: 10655
  buildingTypeId: 9
  buildingLevel: 88
}
rows {
  itemId: 10744
  buildingTypeId: 9
  buildingLevel: 80
}
rows {
  itemId: 10745
  buildingTypeId: 9
  buildingLevel: 80
}
rows {
  itemId: 10746
  buildingTypeId: 9
  buildingLevel: 80
}
rows {
  itemId: 10747
  buildingTypeId: 9
  buildingLevel: 85
}
rows {
  itemId: 10748
  buildingTypeId: 9
  buildingLevel: 90
}
rows {
  itemId: 10749
  buildingTypeId: 9
  buildingLevel: 101
}
rows {
  itemId: 10750
  buildingTypeId: 9
  buildingLevel: 111
}
rows {
  itemId: 10751
  buildingTypeId: 9
  buildingLevel: 121
}
rows {
  itemId: 10752
  buildingTypeId: 9
  buildingLevel: 131
}
rows {
  itemId: 10753
  buildingTypeId: 9
  buildingLevel: 141
}
rows {
  itemId: 10754
  buildingTypeId: 9
  buildingLevel: 146
}
rows {
  itemId: 10755
  buildingTypeId: 9
  buildingLevel: 146
}
rows {
  itemId: 10756
  buildingTypeId: 9
  buildingLevel: 141
}
rows {
  itemId: 10757
  buildingTypeId: 9
  buildingLevel: 141
}
rows {
  itemId: 10821
  buildingTypeId: 9
  buildingLevel: 156
}
rows {
  itemId: 10822
  buildingTypeId: 9
  buildingLevel: 151
}
rows {
  itemId: 10823
  buildingTypeId: 9
  buildingLevel: 151
}
rows {
  itemId: 10824
  buildingTypeId: 9
  buildingLevel: 156
}
rows {
  itemId: 10825
  buildingTypeId: 9
  buildingLevel: 151
}

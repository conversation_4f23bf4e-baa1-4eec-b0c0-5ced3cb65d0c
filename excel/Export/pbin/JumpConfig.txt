com.tencent.wea.xlsRes.table_JumpConfig
excel/xls/T_跳转功能表.xlsx sheet:Sheet1
rows {
  id: 1
  needCloseOthers: false
  jumpType: 1
  jumpText: "跳转模式选择"
}
rows {
  id: 2
  needCloseOthers: false
  jumpType: 2
  jumpText: "赛季主界面"
}
rows {
  id: 3
  needCloseOthers: false
  jumpType: 3
  jumpText: "赛季任务界面"
}
rows {
  id: 4
  needCloseOthers: true
  jumpType: 4
  jumpText: "跳转主界面并打开模式选择"
}
rows {
  id: 5
  needCloseOthers: false
  jumpType: 5
  jumpText: "赠送好友道具"
}
rows {
  id: 6
  needCloseOthers: false
  jumpType: 6
  jumpText: "源货币购买目标代币礼包弹窗"
}
rows {
  id: 7
  needCloseOthers: false
  jumpType: 7
  jumpText: "源货币兑换目标代币弹窗"
}
rows {
  id: 8
  needCloseOthers: false
  jumpType: 8
  jumpText: "充值界面"
}
rows {
  id: 9
  needCloseOthers: false
  jumpType: 9
  jumpText: "BP主界面"
}
rows {
  id: 10
  needCloseOthers: false
  jumpParam: "1"
  jumpType: 10
  jumpText: "任务主界面"
}
rows {
  id: 11
  needCloseOthers: false
  jumpParam: "30005"
  jumpType: 11
  jumpText: "赛季抽奖界面"
}
rows {
  id: 12
  needCloseOthers: false
  jumpParam: "1000"
  jumpType: 11
  jumpText: "活跃币抽奖界面"
}
rows {
  id: 13
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 11
  jumpText: "金币抽奖界面"
}
rows {
  id: 14
  needCloseOthers: true
  jumpType: 14
  jumpText: "大厅"
}
rows {
  id: 15
  needCloseOthers: false
  jumpType: 15
  jumpText: "商城首页"
}
rows {
  id: 16
  needCloseOthers: false
  jumpType: 16
  jumpText: "赛季兑换"
}
rows {
  id: 17
  needCloseOthers: false
  jumpParam: "4"
  jumpType: 10
  jumpText: "等级任务页签"
}
rows {
  id: 18
  needCloseOthers: false
  jumpType: 18
  jumpText: "个人信息主页"
}
rows {
  id: 19
  needCloseOthers: false
  jumpType: 19
  jumpText: "时装图鉴页签"
}
rows {
  id: 20
  needCloseOthers: false
  jumpType: 20
  jumpText: "活动中心首页"
}
rows {
  id: 21
  needCloseOthers: false
  jumpType: 0
  jumpText: "测试跳转"
}
rows {
  id: 22
  needCloseOthers: false
  jumpParam: "5106"
  jumpType: 11
  jumpText: "紫色奖池-星梦者"
}
rows {
  id: 23
  needCloseOthers: false
  jumpParam: "5201"
  jumpType: 11
  jumpText: "蘑咕咕抽奖"
}
rows {
  id: 24
  needCloseOthers: false
  jumpParam: "5000"
  jumpType: 11
  jumpText: "配饰奖池抽奖界面"
}
rows {
  id: 25
  needCloseOthers: false
  jumpType: 25
  jumpText: "特惠礼包界面"
}
rows {
  id: 26
  needCloseOthers: false
  jumpType: 26
  jumpText: "赛季储蓄界面"
}
rows {
  id: 27
  needCloseOthers: false
  jumpType: 27
  jumpText: "首充活动界面"
}
rows {
  id: 28
  needCloseOthers: false
  jumpType: 28
  jumpText: "背包界面"
}
rows {
  id: 29
  needCloseOthers: false
  jumpParam: "请在指定时间登录"
  jumpType: 29
  jumpText: "任务跳转错误提示"
}
rows {
  id: 30
  needCloseOthers: false
  jumpParam: "该界面暂未开放"
  jumpType: 29
  jumpText: "道具获取跳转错误提示"
}
rows {
  id: 31
  needCloseOthers: false
  jumpParam: "10"
  jumpType: 31
  jumpText: "跳转星世界(第一个页签造梦空间)"
}
rows {
  id: 32
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 31
  jumpText: "跳转星世界(第二个页签乐园星图)"
}
rows {
  id: 33
  needCloseOthers: false
  jumpParam: "3"
  jumpType: 31
  jumpText: "跳转星世界(第三个页签星海巡游)"
}
rows {
  id: 34
  needCloseOthers: false
  jumpType: 34
  jumpText: "月卡界面"
}
rows {
  id: 35
  needCloseOthers: false
  jumpParam: "1"
  jumpType: 10
  jumpText: "日常任务"
}
rows {
  id: 36
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 10
  jumpText: "周常任务"
}
rows {
  id: 37
  needCloseOthers: false
  jumpParam: "4"
  jumpType: 10
  jumpText: "等级任务"
}
rows {
  id: 38
  needCloseOthers: false
  jumpParam: "3"
  jumpType: 10
  jumpText: "新手任务"
}
rows {
  id: 39
  needCloseOthers: false
  jumpParam: "5"
  jumpType: 10
  jumpText: "段位任务"
}
rows {
  id: 40
  needCloseOthers: false
  jumpParam: "114"
  jumpType: 20
  jumpText: "活动中心（庆典签到）"
}
rows {
  id: 41
  needCloseOthers: false
  jumpParam: "115"
  jumpType: 20
  jumpText: "活动中心（开服开局礼）"
}
rows {
  id: 42
  needCloseOthers: false
  jumpParam: "116"
  jumpType: 20
  jumpText: "活动中心（宝宝来啦）"
}
rows {
  id: 43
  needCloseOthers: false
  jumpParam: "3"
  jumpType: 20
  jumpText: "活动中心（超级红包）"
}
rows {
  id: 44
  needCloseOthers: false
  jumpParam: "123"
  jumpType: 20
  jumpText: "活动中心（小乔送星运）"
}
rows {
  id: 45
  needCloseOthers: false
  jumpParam: "103"
  jumpType: 20
  jumpText: "活动中心（娱乐大师礼）"
}
rows {
  id: 46
  needCloseOthers: false
  jumpParam: "118"
  jumpType: 20
  jumpText: "活动中心（潮玩星方向）"
}
rows {
  id: 47
  needCloseOthers: false
  jumpParam: "120"
  jumpType: 20
  jumpText: "活动中心（初星代言人）"
}
rows {
  id: 48
  needCloseOthers: false
  jumpParam: "167"
  jumpType: 20
  jumpText: "活动中心（Toby贴纸簿）"
}
rows {
  id: 49
  needCloseOthers: false
  jumpParam: "58"
  jumpType: 20
  jumpText: "活动中心（赶海小队）"
}
rows {
  id: 50
  needCloseOthers: false
  jumpParam: "134"
  jumpType: 20
  jumpText: "活动中心（和梦奇玩耍）"
}
rows {
  id: 51
  needCloseOthers: false
  jumpParam: "135"
  jumpType: 20
  jumpText: "活动中心（舞动青春秀）"
}
rows {
  id: 52
  needCloseOthers: false
  jumpParam: "8"
  jumpType: 52
  jumpText: "跳转到聊天主界面"
}
rows {
  id: 53
  needCloseOthers: false
  jumpParam: "8;5.11赛车小哈前来助阵"
  jumpType: 52
  jumpText: "跳转到世界频道并自动输入一句话"
}
rows {
  id: 54
  needCloseOthers: false
  jumpParam: "19"
  jumpType: 15
  jumpText: "商城avatar礼包1"
}
rows {
  id: 55
  needCloseOthers: false
  jumpParam: "20"
  jumpType: 15
  jumpText: "商城avatar礼包2"
}
rows {
  id: 56
  needCloseOthers: false
  jumpParam: "20"
  jumpType: 15
  jumpText: "商城avatar礼包3"
}
rows {
  id: 57
  needCloseOthers: false
  jumpParam: "20"
  jumpType: 15
  jumpText: "商城avatar礼包4"
}
rows {
  id: 58
  needCloseOthers: false
  jumpParam: "20"
  jumpType: 15
  jumpText: "商城avatar礼包5"
}
rows {
  id: 59
  needCloseOthers: false
  jumpParam: "8;元梦之星，我来啦！"
  jumpType: 52
  jumpText: "跳转到世界频道并自动输入一句话"
}
rows {
  id: 60
  needCloseOthers: false
  jumpParam: "12;8.20谜底是磷虾"
  jumpType: 52
  jumpText: "跳转到世界频道并自动输入一句话"
}
rows {
  id: 61
  needCloseOthers: false
  jumpParam: "12;3月7日免费领桃源联动时装！"
  jumpType: 52
  jumpText: "跳转到广场频道并自动输入一句话"
}
rows {
  id: 62
  needCloseOthers: false
  jumpParam: "12;8.22谜底是磷虾"
  jumpType: 52
  jumpText: "跳转到广场频道并自动输入一句话"
}
rows {
  id: 63
  needCloseOthers: false
  jumpParam: "12;8.23谜底是磷虾"
  jumpType: 52
  jumpText: "跳转到广场频道并自动输入一句话"
}
rows {
  id: 64
  needCloseOthers: false
  jumpType: 64
  jumpText: "跳转到创作者排行榜"
}
rows {
  id: 65
  needCloseOthers: false
  jumpType: 65
  jumpText: "跳转到亲密关系-亲密关系奖励页签"
}
rows {
  id: 66
  needCloseOthers: false
  jumpType: 66
  jumpText: "会员中心页签"
}
rows {
  id: 67
  needCloseOthers: false
  jumpParam: "4"
  jumpType: 15
  jumpText: "商城道具页签"
}
rows {
  id: 68
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 68
  jumpText: "打开地图详情房间列表界面"
}
rows {
  id: 69
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 69
  jumpText: "打开星图&地图详情房间列表界面"
}
rows {
  id: 70
  needCloseOthers: false
  jumpParam: "1000;true"
  jumpType: 11
  jumpText: "打开印章卡池的兑换商城界面"
}
rows {
  id: 71
  needCloseOthers: false
  jumpType: 71
  jumpText: "商品购买弹窗页面"
}
rows {
  id: 72
  needCloseOthers: false
  jumpType: 72
  jumpText: "钻石不足直接购买对应的钻石界面"
}
rows {
  id: 73
  needCloseOthers: false
  jumpParam: "58"
  jumpType: 73
  jumpText: "活动中心（多人小队）"
}
rows {
  id: 74
  needCloseOthers: false
  jumpType: 74
  jumpText: "UGC造梦之旅"
}
rows {
  id: 75
  needCloseOthers: false
  jumpParam: "5407;false;true"
  jumpType: 11
  jumpText: "社交裂变卡池"
}
rows {
  id: 76
  needCloseOthers: false
  jumpParam: "1"
  jumpType: 31
  jumpText: "跳转星世界(星图推荐)"
}
rows {
  id: 77
  needCloseOthers: false
  jumpParam: "60"
  jumpType: 77
  jumpText: "活动中心（棉棉外卖）"
}
rows {
  id: 78
  needCloseOthers: false
  jumpParam: "5105"
  jumpType: 11
  jumpText: "紫色卡池-动感超人"
}
rows {
  id: 79
  needCloseOthers: false
  jumpParam: "5301"
  jumpType: 11
  jumpText: "月光女神卡池"
}
rows {
  id: 80
  needCloseOthers: false
  jumpParam: "5104"
  jumpType: 11
  jumpText: "紫色卡池-龙虾小新"
}
rows {
  id: 81
  needCloseOthers: false
  jumpType: 81
  JumpPlatfromLimit: 7
  JumpPlatfromLimitToastID: 203740
  jumpText: "qq微信特权界面"
}
rows {
  id: 82
  needCloseOthers: false
  jumpType: 82
  jumpText: "一元幸启"
}
rows {
  id: 83
  jumpParam: "2"
  jumpType: 83
  jumpText: "游玩指定id的地图"
}
rows {
  id: 84
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 84
  jumpText: "活动中心一级页签"
}
rows {
  id: 85
  needCloseOthers: false
  jumpParam: "3"
  jumpType: 84
  jumpText: "活动中心一级页签"
}
rows {
  id: 86
  needCloseOthers: false
  jumpParam: "6"
  jumpType: 84
  jumpText: "活动中心一级页签"
}
rows {
  id: 87
  needCloseOthers: false
  jumpType: 20
  jumpText: "活动中心（备选8）"
}
rows {
  id: 88
  needCloseOthers: false
  jumpType: 20
  jumpText: "活动中心（备选9）"
}
rows {
  id: 89
  needCloseOthers: false
  jumpType: 20
  jumpText: "活动中心（备选10）"
}
rows {
  id: 90
  needCloseOthers: false
  jumpParam: "https://game.weixin.qq.com/cgi-bin/comm/openlink?auth_appid=wx62d9035fd4fd2059&url=https%3A%2F%2Fgame.weixin.qq.com%2Fcgi-bin%2Fh5%2Flite%2Fcirclecenter%2Findex.html%3Fwechat_pkgid%3Dlite_circlecenter%26liteapp%3Dliteapp%253A%252F%252Fwxalited17d79803d8c228a7eac78129f40484c%253Fpath%253Dpages%25252Findex%25252Findex%26appid%3Dwx692970c60bffaaa1%26tabid%3D7%26ssid%3D46%23wechat_redirect#wechat_redirect"
  jumpType: 90
  JumpPlatfromLimit: 7
  JumpPlatfromLimitToastID: 203740
  jumpText: "微信特权任务h5链接跳转"
}
rows {
  id: 91
  needCloseOthers: false
  jumpParam: "https://speed.gamecenter.qq.com/pushgame/v1/inner-game/privilege?launchqq=1"
  jumpType: 90
  JumpPlatfromLimit: 7
  JumpPlatfromLimitToastID: 203740
  jumpText: "h5链接跳转"
}
rows {
  id: 92
  needCloseOthers: false
  jumpParam: "https://youxi.gamecenter.qq.com/m/act/5d6f52242db01bb5_10186944.html?_wv=1&_wwv=4"
  jumpType: 90
  jumpText: "h5链接跳转"
}
rows {
  id: 93
  needCloseOthers: false
  jumpParam: "https://g0wx.cn/s/KhSv0P1JG"
  jumpType: 90
  jumpText: "h5链接跳转"
}
rows {
  id: 94
  needCloseOthers: false
  jumpParam: "https://wsurl.cc/2exgzu"
  jumpType: 90
  jumpText: "h5链接跳转"
}
rows {
  id: 95
  needCloseOthers: false
  jumpParam: "https://game.weixin.qq.com/cgi-bin/comm/openlink?noticeid=90302332&auth_appid=wx692970c60bffaaa1&url=https%3A%2F%2Fgame.weixin.qq.com%2Fcgi-bin%2Fh5%2Fstatic%2Fcirclecenter%2Fmixed_circle.html%3Ftabid%3D7%26appid%3Dwx692970c60bffaaa1%26ssid%3D22%23wechat_redirect"
  jumpType: 90
  jumpText: "h5链接跳转"
}
rows {
  id: 96
  needCloseOthers: false
  jumpParam: "https://youxi.gamecenter.qq.com/ogame/sgame-join-qgroup/index.html?appid=1112198072&open_mode=view_external"
  jumpType: 90
  jumpText: "h5链接跳转"
}
rows {
  id: 97
  needCloseOthers: false
  jumpParam: "https://qun.qq.com/qqweb/qunpro/share?_wv=3&_wwv=128&appChannel=share&inviteCode=21TcjNP5upQ&businessType=9&from=181074&biz=ka"
  jumpType: 90
  jumpText: "h5链接跳转"
}
rows {
  id: 98
  needCloseOthers: false
  jumpParam: "https://ymzx.qq.com/cp/a20241121ymzxlllk/index.html"
  jumpType: 90
  jumpText: "h5链接跳转"
}
rows {
  id: 99
  needCloseOthers: false
  jumpParam: "https://m.v.qq.com/x/m/play?lid=&cid=mzc00200vkqr54u&vid=g4100uhjiby&ptag=share_11_11&url_from=share&second_share=0&share_from=copy"
  jumpType: 90
  jumpText: "h5链接跳转"
}
rows {
  id: 100
  needCloseOthers: false
  jumpType: 100
  jumpText: "跳转PixUI界面"
}
rows {
  id: 102
  needCloseOthers: false
  jumpType: 102
  jumpText: "通过param里的id跳转活动pixUI子界面(目前主要用于活动PIXUI界面打开分享消息回传)"
}
rows {
  id: 103
  needCloseOthers: false
  jumpParam: "6"
  jumpType: 103
  jumpText: "赛事系统跳转-赛事说明-奖励"
}
rows {
  id: 104
  needCloseOthers: false
  jumpType: 104
  jumpText: "赛事系统跳转-赛事奖杯"
}
rows {
  id: 105
  needCloseOthers: false
  jumpType: 105
  jumpText: "赛事系统跳转-赛事主界面"
}
rows {
  id: 106
  needCloseOthers: false
  jumpType: 106
  jumpText: "跳转到jumpParams配置的h5界面"
}
rows {
  id: 107
  needCloseOthers: false
  jumpType: 107
  jumpText: "跳转PixUI界面(Param是lua table string)"
}
rows {
  id: 108
  needCloseOthers: false
  jumpType: 108
  jumpText: "跳转到红包雨界面"
}
rows {
  id: 109
  needCloseOthers: false
  jumpType: 109
  jumpText: "跳转到好友界面游戏好友页签"
}
rows {
  id: 111
  needCloseOthers: false
  jumpType: 111
  jumpText: "跳转到微信开播面版"
}
rows {
  id: 112
  needCloseOthers: false
  jumpType: 112
  jumpText: "通过param(id)跳转活动子界面(目前主要用通用跳转逻辑)"
}
rows {
  id: 113
  jumpParam: "5306"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(赚零花钱拉新)——已改成社群侧跳转"
}
rows {
  id: 114
  jumpParam: "4988"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(赚零花钱留存)"
}
rows {
  id: 115
  jumpParam: "5795"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(投票)"
}
rows {
  id: 116
  jumpParam: "6230"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(绑定手机号)"
}
rows {
  id: 117
  jumpParam: "5532"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(鹅厂福利团)"
}
rows {
  id: 118
  jumpParam: "4869"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(庆典集字)"
}
rows {
  id: 119
  jumpParam: "5657"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(集字四期)"
}
rows {
  id: 120
  needCloseOthers: true
  jumpParam: "5852"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(超星运动赛)"
}
rows {
  id: 121
  jumpParam: "5303"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(7月裂变)"
}
rows {
  id: 122
  jumpParam: "5973"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(组队消费)"
}
rows {
  id: 12201
  jumpParam: "5472"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(社区种草小应用)"
}
rows {
  id: 12202
  jumpParam: "5935"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(旅行日记)"
}
rows {
  id: 12203
  jumpParam: "6064"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(欢乐减压站)"
}
rows {
  id: 12204
  jumpParam: "6064"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(秘境探索礼)"
}
rows {
  id: 12205
  jumpParam: "5988"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(全民0元购)"
}
rows {
  id: 12206
  jumpParam: "6201"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(星宝趣玩铺)"
}
rows {
  id: 12207
  jumpParam: "6140"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(豫见非遗)"
}
rows {
  id: 12208
  jumpParam: "5176"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(双人成团)"
}
rows {
  id: 12209
  jumpParam: "5916"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(集字)"
}
rows {
  id: 12210
  jumpParam: "6253"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(0元购二期)"
}
rows {
  id: 12211
  jumpParam: "6313"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(爱满衣橱)"
}
rows {
  id: 12212
  jumpParam: "6358"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(鹅厂福利团5期)"
}
rows {
  id: 12213
  jumpParam: "6505"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(周年数据)"
}
rows {
  id: 12214
  needCloseOthers: false
  jumpParam: "96597"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "通过param(id)跳转活动子界面(华莱士财神)"
}
rows {
  id: 12215
  needCloseOthers: false
  jumpParam: "96624"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "通过param(id)跳转活动子界面(大耳狗集字)"
}
rows {
  id: 12216
  jumpParam: "96652"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(鹅厂福利团5期)"
}
rows {
  id: 10201
  needCloseOthers: false
  jumpParam: "6597"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(跳转参数那里填PixUI的AppId即可)-华莱士"
}
rows {
  id: 10202
  needCloseOthers: false
  jumpParam: "6624"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界(大耳狗)"
}
rows {
  id: 10203
  needCloseOthers: false
  jumpParam: "6652"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(鹅厂福利团)"
}
rows {
  id: 10204
  needCloseOthers: false
  jumpParam: "6415"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(春天星来到)"
}
rows {
  id: 10205
  needCloseOthers: false
  jumpParam: "6848"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(投票)"
}
rows {
  id: 10206
  needCloseOthers: false
  jumpParam: "6937"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(砍价)"
}
rows {
  id: 10207
  needCloseOthers: false
  jumpParam: "6961"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(鹅毛)"
}
rows {
  id: 10208
  needCloseOthers: false
  jumpParam: "7007"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(特色玩法)"
}
rows {
  id: 10209
  needCloseOthers: false
  jumpParam: "7109;isUgc=false"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(铁星三项)"
}
rows {
  id: 10210
  needCloseOthers: false
  jumpParam: "7148"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(鹅毛七期)"
}
rows {
  id: 10211
  needCloseOthers: false
  jumpParam: "7124"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(拯救喵星人)"
}
rows {
  id: 10212
  needCloseOthers: false
  jumpParam: "7247;isUgc=false"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(六一)"
}
rows {
  id: 10213
  needCloseOthers: false
  jumpParam: "6230"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(绑定手机号)"
}
rows {
  id: 10214
  needCloseOthers: false
  jumpParam: "7336"
  jumpType: 102
  jumpText: "通过param里的id跳转发现活动页里的PixUI活动子界面(投票)"
}
rows {
  id: 123
  needCloseOthers: false
  jumpType: 123
  jumpText: "跳转到合家欢任务"
}
rows {
  id: 12300
  jumpParam: "800"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【25年春节专用跳转】新春霸福节"
}
rows {
  id: 12301
  jumpParam: "688"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【25年春节专用跳转】三丽鸥家族"
}
rows {
  id: 12302
  jumpParam: "689"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【25年春节专用跳转】过个元梦年"
}
rows {
  id: 12303
  jumpParam: "691"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【25年春节专用跳转】喜迎开门红"
}
rows {
  id: 12304
  jumpParam: "692"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【25年春节专用跳转】牧场物语"
}
rows {
  id: 12305
  jumpParam: "920"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【25年春节专用跳转】时装基金"
}
rows {
  id: 12306
  jumpParam: "693"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【25年春节专用跳转】星宝送福签"
}
rows {
  id: 12307
  jumpParam: "852"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【25年春节专用跳转】新春天天领"
}
rows {
  id: 12308
  jumpParam: "693"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【25年春节专用跳转】星宝送福签"
}
rows {
  id: 12309
  jumpParam: "852"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【25年春节专用跳转】新春天天领"
}
rows {
  id: 124
  needCloseOthers: false
  jumpParam: "5033"
  jumpType: 20
  jumpText: "【长期】冲段挑战"
}
rows {
  id: 125
  needCloseOthers: false
  jumpParam: "765"
  jumpType: 20
  jumpText: "【4月25日-5月1日】星宝守护者"
}
rows {
  id: 126
  needCloseOthers: false
  jumpParam: "30351"
  jumpType: 20
  jumpText: "【4月30日-6月1日】元梦喵喵节导航"
}
rows {
  id: 127
  needCloseOthers: false
  jumpParam: "770"
  jumpType: 20
  jumpText: "【5月1日-5月18日】携友同行"
}
rows {
  id: 128
  needCloseOthers: false
  jumpParam: "771"
  jumpType: 20
  jumpText: "【5月2日-5月11日】喵力觉醒中"
}
rows {
  id: 129
  needCloseOthers: false
  jumpParam: "772"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【5月2日-5月11日】新赛季来袭"
}
rows {
  id: 130
  needCloseOthers: true
  jumpParam: "766"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【5月1日-5月11日】喵喵相册礼"
}
rows {
  id: 131
  needCloseOthers: true
  jumpParam: "780"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【5月9日-5月22日】蜜桃猫"
}
rows {
  id: 132
  needCloseOthers: false
  jumpParam: "781"
  jumpType: 20
  jumpText: "【5月16日-5月22日】情定之时"
}
rows {
  id: 133
  needCloseOthers: false
  jumpParam: "782"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【5月23日-5月29日】汪汪队长到"
}
rows {
  id: 134
  needCloseOthers: false
  jumpParam: "30372"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【5月23日-5月29日】狼人狂欢节"
}
rows {
  id: 135
  needCloseOthers: false
  jumpParam: "790"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【5.30-6.12】阿卓来漫游"
}
rows {
  id: 136
  needCloseOthers: false
  jumpParam: "791"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【5.30-6.12】就是粽意你WX"
}
rows {
  id: 137
  needCloseOthers: false
  jumpParam: "792"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【5.30-6.12】就是粽意你QQ"
}
rows {
  id: 138
  needCloseOthers: false
  jumpParam: "793"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【6.6-6.12】清凉夏日礼"
}
rows {
  id: 139
  needCloseOthers: true
  jumpParam: "30378"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【6月2日-6月15日】观赛有好礼"
}
rows {
  id: 140
  needCloseOthers: true
  jumpParam: "30407"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【6.13-6.19】赛季末冲刺"
}
rows {
  id: 141
  needCloseOthers: false
  jumpParam: "30406"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【6.20-6.26】赛季末冲刺"
}
rows {
  id: 142
  needCloseOthers: false
  jumpParam: "425"
  jumpType: 20
  jumpText: "排位双重礼"
}
rows {
  id: 143
  needCloseOthers: false
  jumpParam: "205"
  jumpType: 20
  jumpText: "初星代言人（新手专属，不要删）"
}
rows {
  id: 144
  needCloseOthers: false
  jumpParam: "206"
  jumpType: 20
  jumpText: "庆典签到（新手专属，不要删）"
}
rows {
  id: 145
  needCloseOthers: false
  jumpParam: "207"
  jumpType: 20
  jumpText: "星宝来啦（新手专属，不要删）"
}
rows {
  id: 146
  needCloseOthers: false
  jumpParam: "208"
  jumpType: 20
  jumpText: "星宝来啦（新手专属，不要删）"
}
rows {
  id: 147
  needCloseOthers: false
  jumpParam: "612"
  jumpType: 20
  jumpText: "【10月19日-10月25日】许愿树"
}
rows {
  id: 148
  needCloseOthers: false
  jumpParam: "613"
  jumpType: 20
  jumpText: "【10月18日-10月27日】大漏勺来也"
}
rows {
  id: 149
  needCloseOthers: false
  jumpParam: "794"
  jumpType: 20
  jumpText: "【6.13-6.19】青春不散场"
}
rows {
  id: 150
  needCloseOthers: false
  jumpParam: "795"
  jumpType: 20
  jumpText: "【6.20-6.26】订阅新赛季"
}
rows {
  id: 151
  needCloseOthers: false
  jumpParam: "796"
  jumpType: 20
  jumpText: "【6.20-6.29】快闪季来袭"
}
rows {
  id: 152
  needCloseOthers: false
  jumpParam: "797"
  jumpType: 20
  jumpText: "【6.20-7.11】宝宝游园记"
}
rows {
  id: 153
  needCloseOthers: false
  jumpParam: "5001"
  jumpType: 20
  jumpText: "跳转限时排位活动"
}
rows {
  id: 154
  needCloseOthers: false
  jumpType: 154
  jumpText: "通过param(str)弹出提示"
}
rows {
  id: 155
  needCloseOthers: false
  jumpParam: "5001"
  jumpType: 11
  jumpText: "奶龙新春卡池"
}
rows {
  id: 156
  needCloseOthers: false
  jumpType: 156
  jumpText: "二维码加好友"
}
rows {
  id: 157
  needCloseOthers: false
  jumpType: 157
  jumpText: "二维码组队"
}
rows {
  id: 158
  needCloseOthers: false
  jumpParam: "UI_Recharge_ShuttleView"
  jumpType: 8
  jumpText: "微甜盛夏"
}
rows {
  id: 159
  needCloseOthers: false
  jumpType: 159
  jumpText: "拉起QQ\\微信邀请组队"
}
rows {
  id: 160
  needCloseOthers: false
  jumpType: 160
  jumpText: "匹配大厅"
}
rows {
  id: 161
  needCloseOthers: false
  jumpType: 161
  jumpText: "房间大厅"
}
rows {
  id: 162
  needCloseOthers: false
  jumpType: 162
  jumpText: "春节攒福气活动，邀请好友拉起助力"
}
rows {
  id: 163
  needCloseOthers: false
  jumpType: 163
  jumpText: "福星手账分享"
}
rows {
  id: 164
  needCloseOthers: false
  jumpType: 164
  jumpText: "福星手账索要"
}
rows {
  id: 165
  needCloseOthers: false
  jumpType: 165
  jumpText: "福星获得任务跳转界面"
}
rows {
  id: 166
  needCloseOthers: false
  jumpType: 166
  jumpText: "奥特曼"
}
rows {
  id: 167
  needCloseOthers: false
  jumpType: 167
  jumpText: "个人信息主页(个人标签界面)"
}
rows {
  id: 168
  needCloseOthers: false
  jumpType: 168
  jumpText: "跳转到绵绵外卖活动"
}
rows {
  id: 169
  needCloseOthers: false
  jumpType: 169
  jumpText: "通过pandoraManager跳转url"
}
rows {
  id: 10000
  needCloseOthers: false
  jumpType: 10000
  jumpText: "设置-语言"
}
rows {
  id: 10001
  needCloseOthers: false
  jumpParam: "https://discord.com/invite/ww32sTZWaV"
  jumpType: 10001
  jumpText: "discord关注"
}
rows {
  id: 10002
  needCloseOthers: false
  jumpParam: "https://www.facebook.com/gaming/ProjectFunParty"
  jumpType: 10002
  jumpText: "Facebook关注"
}
rows {
  id: 5001
  needCloseOthers: false
  jumpType: 5001
  jumpText: "跳转家园"
}
rows {
  id: 5002
  needCloseOthers: true
  jumpType: 5002
  jumpText: "跳转星家园派对"
}
rows {
  id: 5003
  needCloseOthers: false
  jumpType: 5003
  jumpText: "家园分享跳转"
}
rows {
  id: 5100
  needCloseOthers: false
  jumpType: 5100
  jumpText: "跳转农场"
}
rows {
  id: 5200
  needCloseOthers: false
  jumpType: 5200
  jumpText: "跳转COC"
}
rows {
  id: 1001
  needCloseOthers: false
  jumpParam: "401"
  jumpType: 1
  jumpText: "跳转至大乱斗玩法界面"
}
rows {
  id: 1002
  needCloseOthers: false
  jumpParam: "501"
  jumpType: 1
  jumpText: "跳转至武器大师玩法界面"
}
rows {
  id: 1003
  needCloseOthers: false
  jumpParam: "502"
  jumpType: 1
  jumpText: "跳转至生化追击玩法界面"
}
rows {
  id: 1004
  needCloseOthers: false
  jumpParam: "504"
  jumpType: 1
  jumpText: "跳转至冠军蟹仔玩法界面"
}
rows {
  id: 1005
  needCloseOthers: false
  jumpParam: "505"
  jumpType: 1
  jumpText: "跳转至生化对决玩法界面"
}
rows {
  id: 1006
  needCloseOthers: false
  jumpParam: "506"
  jumpType: 1
  jumpText: "跳转至冲锋竞技玩法界面"
}
rows {
  id: 1007
  needCloseOthers: false
  jumpParam: "103"
  jumpType: 1
  jumpText: "跳转至卧底行动玩法界面"
}
rows {
  id: 1008
  needCloseOthers: false
  jumpParam: "104"
  jumpType: 1
  jumpText: "跳转至躲猫猫玩法界面"
}
rows {
  id: 1009
  needCloseOthers: false
  jumpParam: "105"
  jumpType: 1
  jumpText: "跳转至谁是狼人玩法界面"
}
rows {
  id: 1010
  needCloseOthers: false
  jumpParam: "601"
  jumpType: 1
  jumpText: "跳转至极限竞速玩法界面"
}
rows {
  id: 1011
  needCloseOthers: true
  jumpParam: "10"
  jumpType: 1
  jumpText: "跳转至桃源竞技场玩法界面"
}
rows {
  id: 1012
  needCloseOthers: false
  jumpParam: "11"
  jumpType: 1
  jumpText: "跳转至激流勇进玩法界面"
}
rows {
  id: 1013
  needCloseOthers: false
  jumpParam: "508"
  jumpType: 1
  jumpText: "跳转至星宝大作战玩法界面"
}
rows {
  id: 1014
  needCloseOthers: false
  jumpParam: "801"
  jumpType: 1
  jumpText: "跳转至塔防大亨玩法界面"
}
rows {
  id: 1015
  needCloseOthers: false
  jumpParam: "5600"
  jumpType: 1
  jumpText: "跳转至峡谷相逢玩法界面"
}
rows {
  id: 1016
  needCloseOthers: false
  jumpParam: "5601"
  jumpType: 1
  jumpText: "峡谷排位"
}
rows {
  id: 1017
  needCloseOthers: false
  jumpType: 1
  jumpText: "BP任务跳转专用"
}
rows {
  id: 1018
  needCloseOthers: false
  jumpParam: "350"
  jumpType: 1
  jumpText: "跳转至大王别抓我玩法单王界面"
}
rows {
  id: 1019
  needCloseOthers: false
  jumpParam: "351"
  jumpType: 1
  jumpText: "跳转至大王别抓我玩法大小王界面"
}
rows {
  id: 1020
  needCloseOthers: false
  jumpParam: "5300"
  jumpType: 1
  jumpText: "跳转至拍击球玩法界面"
}
rows {
  id: 1021
  needCloseOthers: false
  jumpParam: "353"
  jumpType: 1
  jumpText: "跳转至大王别抓我玩法三王界面"
}
rows {
  id: 1022
  needCloseOthers: false
  jumpParam: "352"
  jumpType: 1
  jumpText: "跳转至【大王别抓我】玩法排位界面"
}
rows {
  id: 1023
  needCloseOthers: false
  jumpParam: "141"
  jumpType: 1
  jumpText: "跳转至【躲猫猫】玩法排位界面"
}
rows {
  id: 1024
  needCloseOthers: false
  jumpParam: "151"
  jumpType: 1
  jumpText: "跳转至【谁是狼人】玩法排位界面"
}
rows {
  id: 1025
  needCloseOthers: false
  jumpParam: "607"
  jumpType: 1
  jumpText: "跳转至【极速飞车】玩法排位界面"
}
rows {
  id: 1026
  needCloseOthers: false
  jumpParam: "701"
  jumpType: 1
  jumpText: "跳转至【武器大师】玩法排位界面"
}
rows {
  id: 1027
  needCloseOthers: false
  jumpParam: "708"
  jumpType: 1
  jumpText: "跳转至【突围梦幻岛】玩法排位界面"
}
rows {
  id: 1028
  needCloseOthers: false
  jumpParam: "4013"
  jumpType: 1
  jumpText: "跳转至【大乱斗】玩法排位界面"
}
rows {
  id: 1029
  needCloseOthers: false
  jumpParam: "353"
  jumpType: 1
  jumpText: "跳转至大王别抓我玩法三王界面"
}
rows {
  id: 1030
  needCloseOthers: false
  jumpParam: "12"
  jumpType: 1
  jumpText: "跳转至泡泡嘭嘭嘭单人界面"
}
rows {
  id: 1031
  needCloseOthers: false
  jumpParam: "13"
  jumpType: 1
  jumpText: "跳转至泡泡嘭嘭嘭双人界面"
}
rows {
  id: 1032
  needCloseOthers: false
  jumpParam: "17"
  jumpType: 1
  jumpText: "玩法选择备用"
}
rows {
  id: 1033
  needCloseOthers: false
  jumpParam: "5203"
  jumpType: 11
  jumpText: "跳转到古风少女"
}
rows {
  id: 1034
  needCloseOthers: false
  jumpParam: "604"
  jumpType: 1
  jumpText: "跳转至极速飞车玩法界面"
}
rows {
  id: 1035
  needCloseOthers: false
  jumpParam: "513"
  jumpType: 1
  jumpText: "跳转至宝藏猎人玩法界面"
}
rows {
  id: 1036
  needCloseOthers: false
  jumpParam: "808"
  jumpType: 1
  jumpText: "跳转至兽人必须死玩法界面"
}
rows {
  id: 1037
  needCloseOthers: false
  jumpParam: "520"
  jumpType: 1
  jumpText: "跳转至夺宝奇星玩法界面"
}
rows {
  id: 1038
  needCloseOthers: true
  jumpParam: "501,503:10,0,0;508,509,510:10,0,1;513,514,515:10,1,0; 520,521,522:10,1,1"
  jumpType: 1038
  jumpText: "随机玩法跳转"
}
rows {
  id: 1039
  needCloseOthers: false
  jumpParam: "131"
  jumpType: 1
  jumpText: "跳转至【卧底行动】玩法排位界面"
}
rows {
  id: 1040
  needCloseOthers: true
  jumpParam: "5600,5601,6006,15,16,17,103,501,503,508,509,510,502,805,803,801,109,106,112,108,107,105,12,13,14,601,602,603,604,605,606,11,531,104,520,521,522,350,353,351,401,4012,505,513,514,515:10,0,1"
  jumpType: 1038
  jumpText: "随机玩法跳转【娱乐玩法】"
}
rows {
  id: 1041
  needCloseOthers: false
  jumpParam: "508"
  jumpType: 1
  jumpText: "跳转至突围梦幻岛玩法界面"
}
rows {
  id: 1042
  needCloseOthers: false
  jumpParam: "18"
  jumpType: 1
  jumpText: "跳转至泡泡嘭嘭嘭玩法排位界面"
}
rows {
  id: 1043
  needCloseOthers: false
  jumpParam: "15"
  jumpType: 1
  jumpText: "跳转至【闪电赛】玩法界面"
}
rows {
  id: 1044
  needCloseOthers: false
  jumpParam: "16"
  jumpType: 1
  jumpText: "跳转至【闪电赛】玩法界面"
}
rows {
  id: 1045
  needCloseOthers: false
  jumpParam: "17"
  jumpType: 1
  jumpText: "跳转至【闪电赛】玩法界面"
}
rows {
  id: 1046
  needCloseOthers: false
  jumpParam: "5034"
  jumpType: 11
  jumpText: "星梭祈愿"
}
rows {
  id: 1047
  needCloseOthers: false
  jumpParam: "5036"
  jumpType: 11
  jumpText: "奶龙返场"
}
rows {
  id: 1048
  needCloseOthers: false
  jumpParam: "5116"
  jumpType: 11
  jumpText: "功夫熊猫返场"
}
rows {
  id: 1049
  needCloseOthers: false
  jumpParam: "5406"
  jumpType: 11
  jumpText: "暴暴龙返场"
}
rows {
  id: 1050
  needCloseOthers: false
  jumpParam: "5114"
  jumpType: 11
  jumpText: "三丽鸥"
}
rows {
  id: 1051
  needCloseOthers: false
  jumpParam: "5115"
  jumpType: 11
  jumpText: "三丽鸥"
}
rows {
  id: 1052
  needCloseOthers: false
  jumpParam: "4"
  jumpType: 244
  jumpText: "跳转至【天天晋级赛单人】玩法界面"
}
rows {
  id: 1053
  needCloseOthers: false
  jumpParam: "7"
  jumpType: 244
  jumpText: "跳转至【休闲赛】玩法界面"
}
rows {
  id: 1152
  needCloseOthers: false
  jumpParam: "5"
  jumpType: 244
  jumpText: "跳转至【天天晋级赛双人】玩法界面"
}
rows {
  id: 1153
  needCloseOthers: false
  jumpParam: "6"
  jumpType: 244
  jumpText: "跳转至【天天晋级赛四人】玩法界面"
}
rows {
  id: 1054
  needCloseOthers: false
  jumpParam: "5305"
  jumpType: 11
  jumpText: "长相思"
}
rows {
  id: 1055
  needCloseOthers: false
  jumpParam: "5043"
  jumpType: 11
  jumpText: "夏日派对"
}
rows {
  id: 1056
  needCloseOthers: false
  jumpParam: "5044"
  jumpType: 11
  jumpText: "夏日派对"
}
rows {
  id: 1057
  needCloseOthers: false
  jumpParam: "8005"
  jumpType: 11
  jumpText: "许愿星"
}
rows {
  id: 1058
  needCloseOthers: false
  jumpParam: "5051"
  jumpType: 11
  jumpText: "摩天乐园"
}
rows {
  id: 1059
  needCloseOthers: false
  jumpParam: "30000002"
  jumpType: 11
  jumpText: "吾皇猫"
}
rows {
  id: 1060
  needCloseOthers: false
  jumpParam: "30000003"
  jumpType: 11
  jumpText: "巴扎黑"
}
rows {
  id: 20001
  needCloseOthers: false
  jumpParam: "5078;1.3.88.1"
  jumpType: 520
  jumpText: "跳转至【娱乐排位赛】活动界面"
}
rows {
  id: 20002
  needCloseOthers: false
  jumpType: 20002
  jumpText: "跳转至【赛季商店-王冠商铺子商店】界面"
}
rows {
  id: 20003
  needCloseOthers: false
  jumpType: 20003
  jumpText: "跳转至【闪电赛】活动界面"
}
rows {
  id: 20004
  needCloseOthers: false
  jumpParam: "5082;1.3.99.1"
  jumpType: 520
  jumpText: "跳转至【娱乐排位赛】活动界面"
}
rows {
  id: 20005
  needCloseOthers: false
  jumpType: 20005
  jumpText: "主玩法备战界面跳转至活动界面"
}
rows {
  id: 1201
  needCloseOthers: false
  jumpParam: "1"
  jumpType: 1201
  jumpText: "跳转玩法选择页签——经典模式"
}
rows {
  id: 1202
  needCloseOthers: false
  jumpParam: "3"
  jumpType: 1201
  jumpText: "跳转玩法选择页签——娱乐模式"
}
rows {
  id: 1203
  needCloseOthers: false
  jumpParam: "7"
  jumpType: 1201
  jumpText: "跳转玩法选择页签——星世界推荐"
}
rows {
  id: 1204
  needCloseOthers: false
  jumpParam: "14"
  jumpType: 1201
  jumpText: "跳转玩法选择页签——小游戏专区"
}
rows {
  id: 12031
  needCloseOthers: false
  jumpParam: "22001"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22001"
}
rows {
  id: 12032
  needCloseOthers: false
  jumpParam: "22002"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22002"
}
rows {
  id: 12033
  needCloseOthers: false
  jumpParam: "22003"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22003"
}
rows {
  id: 12034
  needCloseOthers: false
  jumpParam: "22004"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22004"
}
rows {
  id: 12035
  needCloseOthers: false
  jumpParam: "22005"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22005"
}
rows {
  id: 12036
  needCloseOthers: false
  jumpParam: "22006"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22006"
}
rows {
  id: 12037
  needCloseOthers: false
  jumpParam: "22007"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22007"
}
rows {
  id: 12038
  needCloseOthers: false
  jumpParam: "22008"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22008"
}
rows {
  id: 12039
  needCloseOthers: false
  jumpParam: "22009"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22009"
}
rows {
  id: 12040
  needCloseOthers: false
  jumpParam: "22010"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22010"
}
rows {
  id: 12041
  needCloseOthers: false
  jumpParam: "22011"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22011"
}
rows {
  id: 12042
  needCloseOthers: false
  jumpParam: "22012"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22012"
}
rows {
  id: 12043
  needCloseOthers: false
  jumpParam: "22013"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22013"
}
rows {
  id: 12044
  needCloseOthers: false
  jumpParam: "22014"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22014"
}
rows {
  id: 12045
  needCloseOthers: false
  jumpParam: "22015"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22015"
}
rows {
  id: 12046
  needCloseOthers: false
  jumpParam: "22016"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22016"
}
rows {
  id: 12047
  needCloseOthers: false
  jumpParam: "22017"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22017"
}
rows {
  id: 12048
  needCloseOthers: false
  jumpParam: "22018"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22018"
}
rows {
  id: 12049
  needCloseOthers: false
  jumpParam: "22019"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22019"
}
rows {
  id: 12050
  needCloseOthers: false
  jumpParam: "22020"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22020"
}
rows {
  id: 12051
  needCloseOthers: false
  jumpParam: "22021"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22021"
}
rows {
  id: 12052
  needCloseOthers: false
  jumpParam: "22022"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22022"
}
rows {
  id: 12053
  needCloseOthers: false
  jumpParam: "22023"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22023"
}
rows {
  id: 12054
  needCloseOthers: false
  jumpParam: "22024"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22024"
}
rows {
  id: 12055
  needCloseOthers: false
  jumpParam: "22025"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22025"
}
rows {
  id: 12056
  needCloseOthers: false
  jumpParam: "22026"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22026"
}
rows {
  id: 12057
  needCloseOthers: false
  jumpParam: "22027"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22027"
}
rows {
  id: 12058
  needCloseOthers: false
  jumpParam: "22028"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22028"
}
rows {
  id: 12059
  needCloseOthers: false
  jumpParam: "22029"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22029"
}
rows {
  id: 12060
  needCloseOthers: false
  jumpParam: "22030"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22030"
}
rows {
  id: 12061
  needCloseOthers: false
  jumpParam: "22031"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22031"
}
rows {
  id: 12062
  needCloseOthers: false
  jumpParam: "22032"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22032"
}
rows {
  id: 12063
  needCloseOthers: false
  jumpParam: "22033"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22033"
}
rows {
  id: 12064
  needCloseOthers: false
  jumpParam: "22034"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22034"
}
rows {
  id: 12065
  needCloseOthers: false
  jumpParam: "22035"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22035"
}
rows {
  id: 12066
  needCloseOthers: false
  jumpParam: "22036"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22036"
}
rows {
  id: 12067
  needCloseOthers: false
  jumpParam: "22037"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22037"
}
rows {
  id: 12068
  needCloseOthers: false
  jumpParam: "22038"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22038"
}
rows {
  id: 12069
  needCloseOthers: false
  jumpParam: "22039"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22039"
}
rows {
  id: 12070
  needCloseOthers: false
  jumpParam: "22040"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22040"
}
rows {
  id: 12071
  needCloseOthers: false
  jumpParam: "22041"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22041"
}
rows {
  id: 12072
  needCloseOthers: false
  jumpParam: "22042"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22042"
}
rows {
  id: 12073
  needCloseOthers: false
  jumpParam: "22043"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22043"
}
rows {
  id: 12074
  needCloseOthers: false
  jumpParam: "22044"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22044"
}
rows {
  id: 12075
  needCloseOthers: false
  jumpParam: "22045"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22045"
}
rows {
  id: 12076
  needCloseOthers: false
  jumpParam: "22046"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22046"
}
rows {
  id: 12077
  needCloseOthers: false
  jumpParam: "22047"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22047"
}
rows {
  id: 12078
  needCloseOthers: false
  jumpParam: "22048"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22048"
}
rows {
  id: 12079
  needCloseOthers: false
  jumpParam: "22049"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22049"
}
rows {
  id: 12080
  needCloseOthers: false
  jumpParam: "22050"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22050"
}
rows {
  id: 12081
  needCloseOthers: false
  jumpParam: "22051"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22051"
}
rows {
  id: 12082
  needCloseOthers: false
  jumpParam: "22052"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22052"
}
rows {
  id: 12083
  needCloseOthers: false
  jumpParam: "22053"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22053"
}
rows {
  id: 12084
  needCloseOthers: false
  jumpParam: "22054"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22054"
}
rows {
  id: 12085
  needCloseOthers: false
  jumpParam: "22055"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22055"
}
rows {
  id: 12086
  needCloseOthers: false
  jumpParam: "22056"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22056"
}
rows {
  id: 12087
  needCloseOthers: false
  jumpParam: "22057"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22057"
}
rows {
  id: 12088
  needCloseOthers: false
  jumpParam: "22058"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22058"
}
rows {
  id: 12089
  needCloseOthers: false
  jumpParam: "22059"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22059"
}
rows {
  id: 12090
  needCloseOthers: false
  jumpParam: "22060"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22060"
}
rows {
  id: 12091
  needCloseOthers: false
  jumpParam: "22061"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22061"
}
rows {
  id: 12092
  needCloseOthers: false
  jumpParam: "22062"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22062"
}
rows {
  id: 12093
  needCloseOthers: false
  jumpParam: "22063"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22063"
}
rows {
  id: 12094
  needCloseOthers: false
  jumpParam: "22064"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22064"
}
rows {
  id: 12095
  needCloseOthers: false
  jumpParam: "22065"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22065"
}
rows {
  id: 12096
  needCloseOthers: false
  jumpParam: "22066"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22066"
}
rows {
  id: 12097
  needCloseOthers: false
  jumpParam: "22067"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22067"
}
rows {
  id: 12098
  needCloseOthers: false
  jumpParam: "22068"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22068"
}
rows {
  id: 12099
  needCloseOthers: false
  jumpParam: "22069"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22069"
}
rows {
  id: 12100
  needCloseOthers: false
  jumpParam: "22070"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22070"
}
rows {
  id: 12101
  needCloseOthers: false
  jumpParam: "22071"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22071"
}
rows {
  id: 12102
  needCloseOthers: false
  jumpParam: "22072"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22072"
}
rows {
  id: 12103
  needCloseOthers: false
  jumpParam: "22073"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22073"
}
rows {
  id: 12104
  needCloseOthers: false
  jumpParam: "22074"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22074"
}
rows {
  id: 12105
  needCloseOthers: false
  jumpParam: "22075"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22075"
}
rows {
  id: 12106
  needCloseOthers: false
  jumpParam: "22076"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22076"
}
rows {
  id: 12107
  needCloseOthers: false
  jumpParam: "22077"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22077"
}
rows {
  id: 12108
  needCloseOthers: false
  jumpParam: "22078"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22078"
}
rows {
  id: 12109
  needCloseOthers: false
  jumpParam: "22079"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22079"
}
rows {
  id: 12110
  needCloseOthers: false
  jumpParam: "22080"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22080"
}
rows {
  id: 12111
  needCloseOthers: false
  jumpParam: "22081"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22081"
}
rows {
  id: 12112
  needCloseOthers: false
  jumpParam: "22082"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22082"
}
rows {
  id: 12113
  needCloseOthers: false
  jumpParam: "22083"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22083"
}
rows {
  id: 12114
  needCloseOthers: false
  jumpParam: "22084"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22084"
}
rows {
  id: 12115
  needCloseOthers: false
  jumpParam: "22085"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22085"
}
rows {
  id: 12116
  needCloseOthers: false
  jumpParam: "22086"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22086"
}
rows {
  id: 12117
  needCloseOthers: false
  jumpParam: "22087"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22087"
}
rows {
  id: 12118
  needCloseOthers: false
  jumpParam: "22088"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22088"
}
rows {
  id: 12119
  needCloseOthers: false
  jumpParam: "22089"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22089"
}
rows {
  id: 12120
  needCloseOthers: false
  jumpParam: "22090"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22090"
}
rows {
  id: 12121
  needCloseOthers: false
  jumpParam: "22091"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22091"
}
rows {
  id: 12122
  needCloseOthers: false
  jumpParam: "22092"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22092"
}
rows {
  id: 12123
  needCloseOthers: false
  jumpParam: "22093"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22093"
}
rows {
  id: 12124
  needCloseOthers: false
  jumpParam: "22094"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22094"
}
rows {
  id: 12125
  needCloseOthers: false
  jumpParam: "22095"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22095"
}
rows {
  id: 12126
  needCloseOthers: false
  jumpParam: "22096"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22096"
}
rows {
  id: 12127
  needCloseOthers: false
  jumpParam: "22097"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22097"
}
rows {
  id: 12128
  needCloseOthers: false
  jumpParam: "22098"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22098"
}
rows {
  id: 12129
  needCloseOthers: false
  jumpParam: "22099"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22099"
}
rows {
  id: 12130
  needCloseOthers: false
  jumpParam: "22100"
  jumpType: 1
  jumpText: "跳转玩法选择——UGC槽位ID22100"
}
rows {
  id: 1101
  needCloseOthers: false
  jumpType: 1101
  jumpText: "跳转至微信朋友圈分享任务"
}
rows {
  id: 10005
  needCloseOthers: false
  jumpType: 10005
  jumpText: "跳转到红包雨界面并请求分享奖励"
}
rows {
  id: 170
  needCloseOthers: false
  jumpType: 170
  jumpText: "跳转到创建自定义房间的页面"
}
rows {
  id: 172
  needCloseOthers: false
  jumpType: 172
  jumpText: "跳转到跑图集星界面"
}
rows {
  id: 173
  needCloseOthers: false
  jumpType: 173
  jumpText: "跳转星世界轨迹"
}
rows {
  id: 174
  needCloseOthers: false
  jumpType: 174
  jumpText: "跳转到年兽活动界面"
}
rows {
  id: 175
  needCloseOthers: false
  jumpParam: "53000004"
  jumpType: 11
  jumpText: "永恒之誓祈愿"
}
rows {
  id: 176
  needCloseOthers: false
  jumpParam: "5023"
  jumpType: 11
  jumpText: "星梦使者祈愿"
}
rows {
  id: 177
  needCloseOthers: false
  jumpParam: "5014"
  jumpType: 11
  jumpText: "暗黑冰羽"
}
rows {
  id: 178
  needCloseOthers: false
  jumpType: 11
  jumpText: "跳转到新春合集界面"
}
rows {
  id: 179
  needCloseOthers: false
  jumpParam: "5026"
  jumpType: 11
  jumpText: "前线装备库"
}
rows {
  id: 180
  needCloseOthers: false
  jumpParam: "30004"
  jumpType: 11
  jumpText: "S4赛季祈愿"
}
rows {
  id: 181
  needCloseOthers: false
  jumpParam: "5030"
  jumpType: 11
  jumpText: "白鹤少年"
}
rows {
  id: 182
  needCloseOthers: false
  jumpParam: "5027"
  jumpType: 11
  jumpText: "阿童木卡池"
}
rows {
  id: 183
  needCloseOthers: false
  jumpParam: "5028"
  jumpType: 11
  jumpText: "小兰卡池"
}
rows {
  id: 184
  needCloseOthers: false
  jumpParam: "8003"
  jumpType: 11
  jumpText: "星光剧场新"
}
rows {
  id: 185
  needCloseOthers: false
  jumpParam: "5302"
  jumpType: 11
  jumpText: "霜天冰雨"
}
rows {
  id: 186
  needCloseOthers: false
  jumpParam: "5501"
  jumpType: 11
  jumpText: "春水溯游"
}
rows {
  id: 187
  needCloseOthers: false
  jumpParam: "5404"
  jumpType: 11
  jumpText: "小丸子便当屋"
}
rows {
  id: 188
  needCloseOthers: false
  jumpParam: "5110"
  jumpType: 11
  jumpText: "盛装派对"
}
rows {
  id: 189
  needCloseOthers: false
  jumpParam: "10012"
  jumpType: 11
  jumpText: "S4印章祈愿"
}
rows {
  id: 190
  needCloseOthers: false
  jumpParam: "5107"
  jumpType: 11
  jumpText: "功夫熊猫"
}
rows {
  id: 191
  needCloseOthers: false
  jumpParam: "10002"
  jumpType: 11
  jumpText: "S3印章祈愿"
}
rows {
  id: 192
  needCloseOthers: false
  jumpParam: "5019"
  jumpType: 11
  jumpText: "小新二期"
}
rows {
  id: 193
  needCloseOthers: false
  jumpParam: "20"
  jumpType: 31
  jumpText: "跳转到星世界新春活动Tab"
}
rows {
  id: 194
  needCloseOthers: true
  jumpType: 194
  jumpText: "跳转到主广场并切换到弹幕状态"
}
rows {
  id: 195
  needCloseOthers: false
  jumpType: 195
  jumpText: "跳转到红包所在场景"
}
rows {
  id: 196
  jumpType: 196
  jumpText: "跳转到UGC合集地图"
}
rows {
  id: 197
  jumpType: 197
  jumpText: "跳转回流中心"
}
rows {
  id: 198
  jumpType: 198
  jumpText: "领取回流每日奖励"
}
rows {
  id: 199
  needCloseOthers: false
  jumpType: 199
  jumpText: "跳转到星运红包活动"
}
rows {
  id: 200
  needCloseOthers: false
  jumpType: 200
  jumpText: "跳转到脑力达人活动"
}
rows {
  id: 201
  needCloseOthers: false
  jumpType: 201
  jumpText: "跳转到奥特曼排行榜界面"
}
rows {
  id: 202
  needCloseOthers: false
  jumpType: 202
  jumpText: "跳转到奥特曼小队加入界面"
}
rows {
  id: 203
  jumpType: 203
  jumpText: "从平台回到游戏后自动开始匹配"
}
rows {
  id: 204
  needCloseOthers: true
  jumpParam: "2101.5,3789.9,63.0,58"
  jumpType: 204
  jumpText: "跳转到草莓音乐节面前"
}
rows {
  id: 207
  needCloseOthers: false
  jumpType: 207
  jumpText: "跳转到春季大回馈活动"
}
rows {
  id: 208
  jumpType: 208
  jumpText: "同行证解锁"
}
rows {
  id: 209
  jumpType: 209
  jumpText: "开学返利"
}
rows {
  id: 210
  needCloseOthers: false
  jumpParam: "101"
  jumpType: 210
  jumpText: "跳转到UGC广场1 (G 广场配置表中mapid: 101)"
}
rows {
  id: 211
  needCloseOthers: true
  jumpType: 211
  jumpText: "跳转到功夫熊猫功夫面馆"
}
rows {
  id: 212
  needCloseOthers: false
  jumpType: 212
  jumpText: "跳转到Ugc地图合集详情页"
}
rows {
  id: 213
  needCloseOthers: false
  jumpParam: "5"
  jumpType: 31
  jumpText: "跳转到星世界-地图合集"
}
rows {
  id: 214
  needCloseOthers: false
  jumpParam: "6"
  jumpType: 31
  jumpText: "跳转到星世界-我的星图"
}
rows {
  id: 215
  needCloseOthers: false
  jumpType: 215
  jumpText: "跳转到星世界-指定TabId(通用接口)"
}
rows {
  id: 216
  needCloseOthers: false
  jumpParam: "https://y.qq.com/forest/GEAY9WvZjM-46Wb1/index.html"
  jumpType: 90
  jumpText: "h5链接跳转-绿钻购买页"
}
rows {
  id: 217
  needCloseOthers: false
  jumpType: 217
  jumpText: "跳转地图合集分享"
}
rows {
  id: 218
  needCloseOthers: false
  jumpType: 218
  jumpText: "跳转到充值返利2"
}
rows {
  id: 219
  needCloseOthers: true
  jumpParam: "105"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为105蔬菜精灵乐园）"
}
rows {
  id: 21901
  needCloseOthers: true
  jumpParam: "115"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为115星宝擂台争霸赛）"
}
rows {
  id: 21902
  needCloseOthers: true
  jumpParam: "114"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为114数字中轴左祖右社）"
}
rows {
  id: 21903
  needCloseOthers: true
  jumpParam: "113"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为113数字中轴钟鼓楼）"
}
rows {
  id: 21904
  needCloseOthers: true
  jumpParam: "117"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为117星宝奇妙夜）"
}
rows {
  id: 21905
  needCloseOthers: true
  jumpParam: "102"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为102熊猫乐园）"
}
rows {
  id: 21906
  needCloseOthers: true
  jumpParam: "104"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为104时光城堡乐园）"
}
rows {
  id: 21907
  needCloseOthers: true
  jumpParam: "118"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为118周年庆2024）"
}
rows {
  id: 21908
  needCloseOthers: true
  jumpParam: "106"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为106丸子的舞会）"
}
rows {
  id: 21909
  needCloseOthers: true
  jumpParam: "108"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为108LINE FRIENDS 泳池派对）"
}
rows {
  id: 21910
  needCloseOthers: true
  jumpParam: "109"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为109数字中轴故宫）"
}
rows {
  id: 21911
  needCloseOthers: true
  jumpParam: "110"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为110铁腕飞船乐园）"
}
rows {
  id: 21912
  needCloseOthers: true
  jumpParam: "111"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为111良渚文化乐园）"
}
rows {
  id: 21913
  needCloseOthers: true
  jumpParam: "112"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为112三丽鸥家族梦幻乐园）"
}
rows {
  id: 21914
  needCloseOthers: true
  jumpParam: "119"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为119星宝小镇冬日）"
}
rows {
  id: 21915
  needCloseOthers: true
  jumpParam: "120"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为120国风）"
}
rows {
  id: 21916
  needCloseOthers: true
  jumpParam: "121"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为121西湖小镇1）"
}
rows {
  id: 21917
  needCloseOthers: true
  jumpParam: "122"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为122西湖小镇2）"
}
rows {
  id: 21918
  needCloseOthers: true
  jumpParam: "123"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为123新春小镇）"
}
rows {
  id: 21919
  needCloseOthers: true
  jumpParam: "124"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为124星宝小镇心动）"
}
rows {
  id: 21920
  needCloseOthers: true
  jumpParam: "125"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为125春日踏青）"
}
rows {
  id: 21921
  needCloseOthers: true
  jumpParam: "126"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为126樱花）"
}
rows {
  id: 21922
  needCloseOthers: true
  jumpParam: "127"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为127愚人节）"
}
rows {
  id: 21923
  needCloseOthers: true
  jumpParam: "128"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为128海岛）"
}
rows {
  id: 21924
  needCloseOthers: true
  jumpParam: "129"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为129喵喵岛）"
}
rows {
  id: 21925
  needCloseOthers: true
  jumpParam: "130"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为130星学院）"
}
rows {
  id: 21926
  needCloseOthers: true
  jumpParam: "131"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为131情侣派对）"
}
rows {
  id: 21927
  needCloseOthers: true
  jumpParam: "132"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为132森谷跑酷）"
}
rows {
  id: 21928
  needCloseOthers: true
  jumpParam: "133"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为133儿童乐园）"
}
rows {
  id: 21929
  needCloseOthers: true
  jumpParam: "134"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为134毕业季）"
}
rows {
  id: 21930
  needCloseOthers: true
  jumpParam: "135"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为135倒反天罡）"
}
rows {
  id: 21931
  needCloseOthers: true
  jumpParam: "136"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为136小红狐）"
}
rows {
  id: 21932
  needCloseOthers: true
  jumpParam: "137"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为137过家家）"
}
rows {
  id: 21933
  needCloseOthers: true
  jumpParam: "138"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为138观赏地图合集）"
}
rows {
  id: 21934
  needCloseOthers: true
  jumpParam: "139"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为139chiikawa）"
}
rows {
  id: 21935
  needCloseOthers: true
  jumpParam: "140"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为140预留）"
}
rows {
  id: 21936
  needCloseOthers: true
  jumpParam: "141"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为141预留）"
}
rows {
  id: 21937
  needCloseOthers: true
  jumpParam: "142"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为142预留）"
}
rows {
  id: 21938
  needCloseOthers: true
  jumpParam: "143"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为143预留）"
}
rows {
  id: 21939
  needCloseOthers: true
  jumpParam: "144"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为144预留）"
}
rows {
  id: 21940
  needCloseOthers: true
  jumpParam: "145"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为145预留）"
}
rows {
  id: 21941
  needCloseOthers: true
  jumpParam: "146"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为146预留）"
}
rows {
  id: 21942
  needCloseOthers: true
  jumpParam: "147"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为147预留）"
}
rows {
  id: 21943
  needCloseOthers: true
  jumpParam: "148"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为148预留）"
}
rows {
  id: 21944
  needCloseOthers: true
  jumpParam: "149"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为149预留）"
}
rows {
  id: 21945
  needCloseOthers: true
  jumpParam: "150"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为150预留）"
}
rows {
  id: 21946
  needCloseOthers: true
  jumpParam: "151"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为151预留）"
}
rows {
  id: 21947
  needCloseOthers: true
  jumpParam: "152"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为152预留）"
}
rows {
  id: 21948
  needCloseOthers: true
  jumpParam: "153"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为153预留）"
}
rows {
  id: 21949
  needCloseOthers: true
  jumpParam: "154"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为154预留）"
}
rows {
  id: 21950
  needCloseOthers: true
  jumpParam: "155"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为155预留）"
}
rows {
  id: 21951
  needCloseOthers: true
  jumpParam: "156"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为156预留）"
}
rows {
  id: 21952
  needCloseOthers: true
  jumpParam: "157"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为157预留）"
}
rows {
  id: 21953
  needCloseOthers: true
  jumpParam: "158"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为158预留）"
}
rows {
  id: 21954
  needCloseOthers: true
  jumpParam: "159"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为159预留）"
}
rows {
  id: 21955
  needCloseOthers: true
  jumpParam: "160"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为160预留）"
}
rows {
  id: 21956
  needCloseOthers: true
  jumpParam: "161"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为161预留）"
}
rows {
  id: 21957
  needCloseOthers: true
  jumpParam: "162"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为162预留）"
}
rows {
  id: 21958
  needCloseOthers: true
  jumpParam: "163"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为163预留）"
}
rows {
  id: 21959
  needCloseOthers: true
  jumpParam: "164"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为164预留）"
}
rows {
  id: 21960
  needCloseOthers: true
  jumpParam: "165"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为165预留）"
}
rows {
  id: 21961
  needCloseOthers: true
  jumpParam: "166"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为166预留）"
}
rows {
  id: 21962
  needCloseOthers: true
  jumpParam: "167"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为167预留）"
}
rows {
  id: 21963
  needCloseOthers: true
  jumpParam: "168"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为168预留）"
}
rows {
  id: 21964
  needCloseOthers: true
  jumpParam: "169"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为169预留）"
}
rows {
  id: 21965
  needCloseOthers: true
  jumpParam: "170"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为170预留）"
}
rows {
  id: 21966
  needCloseOthers: true
  jumpParam: "301"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为301预留）"
}
rows {
  id: 21967
  needCloseOthers: true
  jumpParam: "302"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为302预留）"
}
rows {
  id: 21968
  needCloseOthers: true
  jumpParam: "303"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为303预留）"
}
rows {
  id: 21969
  needCloseOthers: true
  jumpParam: "304"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为304预留）"
}
rows {
  id: 21970
  needCloseOthers: true
  jumpParam: "305"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为305预留）"
}
rows {
  id: 21971
  needCloseOthers: true
  jumpParam: "306"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为306预留）"
}
rows {
  id: 21972
  needCloseOthers: true
  jumpParam: "307"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为307预留）"
}
rows {
  id: 21973
  needCloseOthers: true
  jumpParam: "308"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为308预留）"
}
rows {
  id: 21974
  needCloseOthers: true
  jumpParam: "309"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为309预留）"
}
rows {
  id: 21975
  needCloseOthers: true
  jumpParam: "310"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为310预留）"
}
rows {
  id: 21976
  needCloseOthers: true
  jumpParam: "311"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为311预留）"
}
rows {
  id: 21977
  needCloseOthers: true
  jumpParam: "312"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为312预留）"
}
rows {
  id: 21978
  needCloseOthers: true
  jumpParam: "313"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为313预留）"
}
rows {
  id: 21979
  needCloseOthers: true
  jumpParam: "314"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为314预留）"
}
rows {
  id: 21980
  needCloseOthers: true
  jumpParam: "315"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为315预留）"
}
rows {
  id: 21981
  needCloseOthers: true
  jumpParam: "316"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为316预留）"
}
rows {
  id: 21982
  needCloseOthers: true
  jumpParam: "317"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为317预留）"
}
rows {
  id: 21983
  needCloseOthers: true
  jumpParam: "318"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为318预留）"
}
rows {
  id: 21984
  needCloseOthers: true
  jumpParam: "319"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为319预留）"
}
rows {
  id: 21985
  needCloseOthers: true
  jumpParam: "320"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为320预留）"
}
rows {
  id: 21986
  needCloseOthers: true
  jumpParam: "321"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为321预留）"
}
rows {
  id: 21987
  needCloseOthers: true
  jumpParam: "322"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为322预留）"
}
rows {
  id: 21988
  needCloseOthers: true
  jumpParam: "323"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为323预留）"
}
rows {
  id: 21989
  needCloseOthers: true
  jumpParam: "324"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为324预留）"
}
rows {
  id: 21990
  needCloseOthers: true
  jumpParam: "325"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为325预留）"
}
rows {
  id: 21991
  needCloseOthers: true
  jumpParam: "326"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为326预留）"
}
rows {
  id: 21992
  needCloseOthers: true
  jumpParam: "327"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为327预留）"
}
rows {
  id: 21993
  needCloseOthers: true
  jumpParam: "328"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为328预留）"
}
rows {
  id: 21994
  needCloseOthers: true
  jumpParam: "329"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为329预留）"
}
rows {
  id: 21995
  needCloseOthers: true
  jumpParam: "330"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为330预留）"
}
rows {
  id: 21996
  needCloseOthers: true
  jumpParam: "331"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为331预留）"
}
rows {
  id: 21997
  needCloseOthers: true
  jumpParam: "332"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为332预留）"
}
rows {
  id: 21998
  needCloseOthers: true
  jumpParam: "333"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为333预留）"
}
rows {
  id: 21999
  needCloseOthers: true
  jumpParam: "334"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为334预留）"
}
rows {
  id: 22000
  needCloseOthers: true
  jumpParam: "335"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为335预留）"
}
rows {
  id: 22001
  needCloseOthers: true
  jumpParam: "336"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为336预留）"
}
rows {
  id: 22002
  needCloseOthers: true
  jumpParam: "337"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为337预留）"
}
rows {
  id: 22003
  needCloseOthers: true
  jumpParam: "338"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为338预留）"
}
rows {
  id: 22004
  needCloseOthers: true
  jumpParam: "339"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为339预留）"
}
rows {
  id: 22005
  needCloseOthers: true
  jumpParam: "340"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为340预留）"
}
rows {
  id: 22006
  needCloseOthers: true
  jumpParam: "341"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为341预留）"
}
rows {
  id: 22007
  needCloseOthers: true
  jumpParam: "342"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为342预留）"
}
rows {
  id: 22008
  needCloseOthers: true
  jumpParam: "343"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为343预留）"
}
rows {
  id: 22009
  needCloseOthers: true
  jumpParam: "344"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为344预留）"
}
rows {
  id: 22010
  needCloseOthers: true
  jumpParam: "345"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为345预留）"
}
rows {
  id: 22011
  needCloseOthers: true
  jumpParam: "346"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为346预留）"
}
rows {
  id: 22012
  needCloseOthers: true
  jumpParam: "347"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为347预留）"
}
rows {
  id: 22013
  needCloseOthers: true
  jumpParam: "348"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为348预留）"
}
rows {
  id: 22014
  needCloseOthers: true
  jumpParam: "349"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为349预留）"
}
rows {
  id: 22015
  needCloseOthers: true
  jumpParam: "350"
  jumpType: 219
  jumpText: "跳转到指定观赏地图分广场，读取右侧主参数列中填写的mapId（目前为350预留）"
}
rows {
  id: 220
  jumpType: 220
  jumpText: "购买高级通行证"
}
rows {
  id: 221
  needCloseOthers: false
  jumpParam: "5017"
  jumpType: 221
  jumpText: "跳转四人小队留影"
}
rows {
  id: 222
  jumpType: 222
  jumpText: "跳转到竞速模式备战界面"
}
rows {
  id: 223
  jumpType: 223
  jumpText: "跳转到玩法模式界面"
}
rows {
  id: 224
  needCloseOthers: false
  jumpType: 224
  jumpText: "截屏二维码扫描"
}
rows {
  id: 225
  needCloseOthers: true
  jumpParam: "106"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为106小丸子广场）"
}
rows {
  id: 226
  needCloseOthers: false
  jumpType: 226
  jumpText: "跳转赛事列表界面"
}
rows {
  id: 227
  needCloseOthers: true
  jumpType: 227
  jumpText: "跳转定制赛事界面"
}
rows {
  id: 300
  needCloseOthers: false
  jumpType: 300
  jumpText: "跳转星友滴滴"
}
rows {
  id: 301
  needCloseOthers: false
  jumpType: 301
  jumpText: "跳转到种草社区"
}
rows {
  id: 302
  needCloseOthers: false
  jumpParam: "5016"
  jumpType: 302
  jumpText: "跳转四人小队"
}
rows {
  id: 303
  needCloseOthers: false
  jumpParam: "10003"
  jumpType: 303
  jumpText: "跳转兽人必须死BP"
}
rows {
  id: 304
  needCloseOthers: false
  jumpType: 304
  jumpText: "活动导航跳转"
}
rows {
  id: 305
  needCloseOthers: false
  jumpType: 305
  jumpText: "赛季商店导航页跳转-经典模式"
}
rows {
  id: 306
  needCloseOthers: false
  jumpParam: "103"
  jumpType: 305
  jumpText: "赛季商店跳转-娱乐模式"
}
rows {
  id: 307
  needCloseOthers: false
  jumpParam: "151"
  jumpType: 307
  jumpText: "默契商店跳转-默契之心说明"
}
rows {
  id: 401
  needCloseOthers: false
  jumpType: 401
  jumpText: "话题详情跳转"
}
rows {
  id: 402
  needCloseOthers: false
  jumpType: 402
  jumpText: "跳转组队邀请"
}
rows {
  id: 500
  needCloseOthers: false
  jumpType: 500
  JumpPlatfromLimit: 7
  JumpPlatfromLimitToastID: 203740
  jumpText: "订阅qq机器人"
}
rows {
  id: 511
  needCloseOthers: false
  jumpParam: "61016;61017"
  jumpType: 511
  jumpText: "跳转到大富翁骰子获得"
}
rows {
  id: 555
  needCloseOthers: false
  jumpType: 555
  jumpText: "人拉人活动"
}
rows {
  id: 50001
  needCloseOthers: false
  jumpType: 50001
  jumpText: "跳转到小游戏首充"
}
rows {
  id: 50002
  needCloseOthers: false
  jumpType: 50002
  jumpText: "跳转到小游戏热购"
}
rows {
  id: 50003
  needCloseOthers: false
  jumpType: 50003
  jumpText: "跳转到小游戏祈愿"
}
rows {
  id: 50004
  needCloseOthers: false
  jumpType: 50004
  jumpText: "跳转到小游戏通行证"
}
rows {
  id: 50005
  needCloseOthers: false
  jumpType: 50005
  jumpText: "跳转到小游戏背包"
}
rows {
  id: 50006
  needCloseOthers: false
  jumpType: 50006
  jumpText: "跳转到小游戏个人信息"
}
rows {
  id: 50007
  needCloseOthers: false
  jumpType: 50007
  jumpText: "跳转到小游戏好友"
}
rows {
  id: 50008
  needCloseOthers: false
  jumpType: 50008
  jumpText: "跳转到小游戏邮箱"
}
rows {
  id: 308
  needCloseOthers: true
  jumpParam: "107"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为105菜狗广场）"
}
rows {
  id: 309
  needCloseOthers: true
  jumpParam: "110"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为107游戏发布会）"
}
rows {
  id: 310
  needCloseOthers: false
  jumpParam: "105"
  jumpType: 310
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedWerewolf"
  }
  jumpText: "跳转到备战界面"
}
rows {
  id: 320
  needCloseOthers: false
  jumpParam: "5021"
  jumpType: 21
  jumpText: "跳转进酷洛米手帐铺"
}
rows {
  id: 321
  needCloseOthers: false
  jumpParam: "3"
  jumpType: 304
  jumpText: "跳转进寻梦嘉年华"
}
rows {
  id: 600
  needCloseOthers: false
  jumpParam: "5029"
  jumpType: 11
  jumpText: "小肥柴卡池"
}
rows {
  id: 601
  needCloseOthers: false
  jumpParam: "5031"
  jumpType: 11
  jumpText: "布朗熊卡池"
}
rows {
  id: 602
  needCloseOthers: false
  jumpParam: "5032"
  jumpType: 11
  jumpText: "可妮兔卡池"
}
rows {
  id: 603
  needCloseOthers: false
  jumpParam: "5025"
  jumpType: 11
  jumpText: "黑白天鹅"
}
rows {
  id: 604
  needCloseOthers: false
  jumpParam: "5033"
  jumpType: 11
  jumpText: "天鹅之心"
}
rows {
  id: 605
  needCloseOthers: false
  jumpParam: "5030"
  jumpType: 11
  jumpText: "白鹤少年"
}
rows {
  id: 606
  needCloseOthers: false
  jumpParam: "5502"
  jumpType: 11
  jumpText: "浪漫旅程"
}
rows {
  id: 607
  needCloseOthers: false
  jumpParam: "5702"
  jumpType: 11
  jumpText: "IAA长期使用"
}
rows {
  id: 610
  needCloseOthers: false
  jumpParam: "5304"
  jumpType: 11
  jumpText: "秋千卡池"
}
rows {
  id: 611
  needCloseOthers: false
  jumpParam: "6000"
  jumpType: 11
  jumpText: "王者二期卡池"
}
rows {
  id: 612
  needCloseOthers: false
  jumpParam: "5205"
  jumpType: 11
  jumpText: "阿努比斯卡池"
}
rows {
  id: 613
  needCloseOthers: false
  jumpParam: "5124"
  jumpType: 11
  jumpText: "百变小新-睡衣"
}
rows {
  id: 614
  needCloseOthers: false
  jumpParam: "5045"
  jumpType: 11
  jumpText: "向日葵小班-正男"
}
rows {
  id: 615
  needCloseOthers: false
  jumpParam: "5046"
  jumpType: 11
  jumpText: "向日葵小班-风间"
}
rows {
  id: 616
  needCloseOthers: false
  jumpParam: "5047"
  jumpType: 11
  jumpText: "向日葵小班-妮妮"
}
rows {
  id: 617
  needCloseOthers: false
  jumpParam: "5048"
  jumpType: 11
  jumpText: "向日葵小班-阿呆"
}
rows {
  id: 618
  needCloseOthers: false
  jumpParam: "5125"
  jumpType: 11
  jumpText: "百变小新-左卫门"
}
rows {
  id: 619
  needCloseOthers: false
  jumpParam: "30008"
  jumpType: 11
  jumpText: "精灵之森"
}
rows {
  id: 620
  needCloseOthers: false
  jumpParam: "70000001"
  jumpType: 11
  jumpText: "泡泡玛特2"
}
rows {
  id: 621
  needCloseOthers: false
  jumpParam: "8007"
  jumpType: 11
  jumpText: "星光剧场"
}
rows {
  id: 622
  needCloseOthers: false
  jumpParam: "70005201"
  jumpType: 11
  jumpText: "蔷薇少女"
}
rows {
  id: 623
  needCloseOthers: false
  jumpParam: "30009"
  jumpType: 11
  jumpText: "S8赛季祈愿"
}
rows {
  id: 624
  needCloseOthers: false
  jumpParam: "30009"
  jumpType: 11
  jumpText: "小甜豆返场"
}
rows {
  id: 625
  needCloseOthers: false
  jumpParam: "UI_ValuePreorder_MainView"
  jumpType: 8
  jumpText: "S8赛季预购"
}
rows {
  id: 626
  needCloseOthers: false
  jumpParam: "UI_SecondChargeRebate_Main"
  jumpType: 8
  jumpText: "S8赛季累充"
}
rows {
  id: 627
  needCloseOthers: false
  jumpParam: "https://act.supercore.qq.com/commercial/act/a95fdec8755f040c8bc738078cbde9062prerelease/index.html"
  jumpType: 90
  jumpText: "超核排行榜"
}
rows {
  id: 628
  needCloseOthers: false
  jumpParam: "5360"
  jumpType: 11
  jumpText: "摩天轮卡池"
}
rows {
  id: 629
  jumpParam: "UI_WeekendLuckyStar_MainView"
  jumpType: 8
  jumpText: "周末幸运星"
}
rows {
  id: 630
  needCloseOthers: false
  jumpParam: "30010"
  jumpType: 11
  jumpText: "S9赛季祈愿"
}
rows {
  id: 631
  needCloseOthers: false
  jumpParam: "120200"
  jumpType: 15
  jumpText: "S9赛季累充"
}
rows {
  id: 632
  needCloseOthers: false
  jumpParam: "121100"
  jumpType: 15
  jumpText: "S9赛季预购"
}
rows {
  id: 634
  needCloseOthers: false
  jumpParam: "80001"
  jumpType: 11
  jumpText: "马车三阶载具卡池"
}
rows {
  id: 635
  needCloseOthers: false
  jumpParam: "121100"
  jumpType: 15
  jumpText: "S10赛季预购"
}
rows {
  id: 636
  needCloseOthers: false
  jumpParam: "30011"
  jumpType: 11
  jumpText: "S10赛季祈愿"
}
rows {
  id: 637
  needCloseOthers: false
  jumpParam: "120200"
  jumpType: 15
  jumpText: "S10赛季累充"
}
rows {
  id: 638
  needCloseOthers: false
  jumpParam: "5361"
  jumpType: 11
  jumpText: "拱门卡池"
}
rows {
  id: 639
  needCloseOthers: false
  jumpParam: "121100"
  jumpType: 15
  jumpText: "S11赛季预购"
}
rows {
  id: 640
  needCloseOthers: false
  jumpParam: "30012"
  jumpType: 11
  jumpText: "S11赛季祈愿"
}
rows {
  id: 641
  needCloseOthers: false
  jumpParam: "40000015"
  jumpType: 11
  jumpText: "玩偶之家祈愿"
}
rows {
  id: 660
  needCloseOthers: false
  jumpParam: "121100"
  jumpType: 15
  jumpText: "S12赛季预购"
}
rows {
  id: 661
  needCloseOthers: false
  jumpParam: "30013"
  jumpType: 11
  jumpText: "赛季祈愿"
}
rows {
  id: 800
  needCloseOthers: false
  jumpParam: "30006"
  jumpType: 11
  jumpText: "骑士颂歌卡池"
}
rows {
  id: 330
  needCloseOthers: false
  jumpType: 330
  jumpText: "狼人杀备战跳转至大师之路"
}
rows {
  id: 331
  needCloseOthers: false
  jumpType: 331
  JumpConditionGroup {
    condition {
      subConditionList {
        type: 1001
        value: 0
        value: 1801
        value: 70
        value: 2
        value: 1501
        value: 0
        listTipsKey: "WerewolfNotOpenJumpFailed"
        listTipsKey: "ActivityNeedWerewolf"
      }
    }
  }
  jumpText: "跳转至狼人宝典"
}
rows {
  id: 228
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 15
  jumpText: "商城道具页签"
}
rows {
  id: 229
  needCloseOthers: true
  jumpParam: "112"
  jumpType: 219
  jumpText: "跳转到指定分广场，读取右侧主参数列中填写的mapId（目前为105菜狗广场）"
}
rows {
  id: 230
  needCloseOthers: false
  jumpType: 230
  jumpText: "跳转至装饰系统互动道具"
}
rows {
  id: 700
  needCloseOthers: false
  jumpType: 700
  jumpText: "跳转至半周年庆预热活动"
}
rows {
  id: 701
  needCloseOthers: false
  jumpType: 701
  jumpText: "新版玩法模式分享跳转"
}
rows {
  id: 231
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 15
  jumpText: "商城（装扮）页签"
}
rows {
  id: 311
  needCloseOthers: false
  jumpParam: "5035"
  jumpType: 11
  jumpText: "守护圣翼祈愿"
}
rows {
  id: 702
  needCloseOthers: false
  jumpType: 702
  jumpText: "半周年庆走格子活动跳转"
}
rows {
  id: 232
  needCloseOthers: false
  jumpType: 232
  jumpText: "打开MOBA英雄预览界面"
}
rows {
  id: 233
  needCloseOthers: false
  jumpType: 233
  jumpText: "打开通行证界面"
}
rows {
  id: 234
  needCloseOthers: false
  jumpParam: "1"
  jumpType: 234
  jumpText: "奖杯征程-主页签"
}
rows {
  id: 235
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 234
  jumpText: "奖杯征程-任务页签"
}
rows {
  id: 236
  needCloseOthers: false
  jumpType: 236
  jumpText: "打开下载管理"
}
rows {
  id: 312
  needCloseOthers: false
  jumpParam: "105"
  jumpType: 312
  jumpText: "跳转到备战成就界面"
}
rows {
  id: 313
  needCloseOthers: false
  jumpType: 313
  jumpText: "跳转狼人备战兑换商店"
}
rows {
  id: 314
  needCloseOthers: false
  jumpType: 314
  jumpText: "跳转狼人备战装饰报告动画"
}
rows {
  id: 315
  needCloseOthers: false
  jumpType: 315
  jumpText: "跳转狼人备战装饰攻击动画"
}
rows {
  id: 316
  needCloseOthers: false
  jumpType: 316
  jumpText: "跳转狼人备战装饰会议表情"
}
rows {
  id: 317
  needCloseOthers: false
  jumpType: 317
  jumpText: "跳转狼人备战装饰道具"
}
rows {
  id: 318
  needCloseOthers: false
  jumpType: 318
  jumpText: "跳转狼人备战身份选择"
}
rows {
  id: 319
  needCloseOthers: false
  jumpType: 319
  jumpText: "跳转狼人备战房间界面15人"
}
rows {
  id: 324
  needCloseOthers: false
  jumpType: 320
  jumpText: "跳转狼人备战装饰MVP动画"
}
rows {
  id: 323
  needCloseOthers: false
  jumpType: 323
  jumpText: "跳转狼人备战房间界面10人"
}
rows {
  id: 10500
  needCloseOthers: false
  jumpType: 10500
  jumpText: "跳转狼人阵营卡购买"
}
rows {
  id: 10501
  needCloseOthers: false
  jumpType: 10501
  jumpText: "跳转狼人身份卡购买"
}
rows {
  id: 704
  jumpParam: "1;805"
  jumpType: 704
  jumpText: "运动特训营跳转"
}
rows {
  id: 705
  jumpParam: "2"
  jumpType: 78
  jumpText: "印章祈愿跳转"
}
rows {
  id: 706
  jumpParam: "1"
  jumpType: 78
  jumpText: "赛季祈愿跳转"
}
rows {
  id: 707
  jumpParam: "3"
  jumpType: 78
  jumpText: "星光剧场跳转"
}
rows {
  id: 332
  needCloseOthers: false
  jumpParam: "444"
  jumpType: 20
  jumpText: "活动中心（峡谷竞技）"
}
rows {
  id: 333
  needCloseOthers: false
  jumpType: 333
  jumpText: "跳转至峡谷3V3宝典"
}
rows {
  id: 322
  needCloseOthers: false
  jumpType: 322
  jumpText: "跳转狼人通行证界面"
}
rows {
  id: 334
  needCloseOthers: false
  jumpType: 334
  jumpText: "跳转到微信小游戏广告抽奖界面"
}
rows {
  id: 340
  needCloseOthers: false
  jumpParam: "444"
  jumpType: 20
  jumpText: "活动中心（峡谷竞技）"
}
rows {
  id: 341
  needCloseOthers: false
  jumpParam: "3"
  jumpType: 341
  jumpText: "跳转至峡谷3V3宝典"
}
rows {
  id: 342
  needCloseOthers: false
  jumpParam: "5600"
  jumpType: 1
  jumpText: "跳转至峡谷3v3玩法界面"
}
rows {
  id: 801
  needCloseOthers: false
  jumpParam: "5119"
  jumpType: 11
  jumpText: "甄嬛传"
}
rows {
  id: 802
  needCloseOthers: false
  jumpParam: "5122"
  jumpType: 11
  jumpText: "峡谷幻梦"
}
rows {
  id: 803
  needCloseOthers: false
  jumpParam: "10000005"
  jumpType: 11
  jumpText: "跳转至甜兔仙踪"
}
rows {
  id: 343
  needCloseOthers: false
  jumpParam: "50000001"
  jumpType: 11
  jumpText: "LuLu猪的夏天祈愿"
}
rows {
  id: 237
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 237
  jumpText: "跳转星世界单图匹配"
}
rows {
  id: 238
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 238
  jumpText: "跳转星世界图集匹配"
}
rows {
  id: 239
  needCloseOthers: false
  jumpParam: "507"
  jumpType: 20
  jumpText: "跳转至活动新界奇遇"
}
rows {
  id: 240
  needCloseOthers: false
  jumpParam: "507;2"
  jumpType: 20
  jumpText: "发现活动-星界奇遇-第二个页签"
}
rows {
  id: 344
  jumpParam: "813"
  jumpType: 20
  jumpText: "【8月9日周】狼人七夕礼"
}
rows {
  id: 901
  needCloseOthers: false
  jumpParam: "29"
  jumpType: 15
  jumpText: "商城-宝库-狼人"
}
rows {
  id: 809
  needCloseOthers: false
  jumpParam: "5306"
  jumpType: 11
  jumpText: "凤求凰"
}
rows {
  id: 335
  needCloseOthers: false
  jumpType: 335
  jumpText: "跳转到 moba备战 高光播报"
}
rows {
  id: 336
  needCloseOthers: false
  jumpType: 336
  jumpText: "跳转到 moba备战 头像框"
}
rows {
  id: 337
  needCloseOthers: false
  jumpType: 337
  jumpText: "跳转到 moba备战 卡牌图鉴"
}
rows {
  id: 338
  needCloseOthers: false
  jumpType: 338
  jumpText: "跳转到 moba备战 卡牌宝箱"
}
rows {
  id: 345
  needCloseOthers: false
  jumpParam: "8000"
  jumpType: 20
  jumpText: "活动中心（线性测试）"
}
rows {
  id: 346
  needCloseOthers: false
  jumpParam: "8001"
  jumpType: 20
  jumpText: "活动中心（兽人补给站）"
}
rows {
  id: 347
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 233
  jumpText: "打开通行证-任务"
}
rows {
  id: 348
  needCloseOthers: false
  jumpParam: "3"
  jumpType: 234
  jumpText: "奖杯征程-任务页签"
}
rows {
  id: 812
  needCloseOthers: false
  jumpParam: "5053"
  jumpType: 11
  jumpText: "【0815周】狼人卡池"
}
rows {
  id: 815
  needCloseOthers: false
  jumpType: 239
  jumpText: "组队减负"
}
rows {
  id: 349
  jumpType: 349
  jumpText: "跳转幸运好友界面"
}
rows {
  id: 888
  needCloseOthers: false
  jumpType: 888
  jumpText: "跳转到E8Rich玩法"
}
rows {
  id: 889
  needCloseOthers: false
  jumpParam: "188"
  jumpType: 1
  jumpText: "跳转到E8Rich玩法界面"
}
rows {
  id: 410
  needCloseOthers: false
  jumpParam: "10016"
  jumpType: 20
  jumpText: "活动中心（拼团返利）"
}
rows {
  id: 411
  needCloseOthers: false
  jumpType: 376
  jumpText: "跳转至云转端窗口"
}
rows {
  id: 412
  needCloseOthers: false
  jumpParam: "13000010"
  jumpType: 11
  jumpText: "洋葱头蔬果屋"
}
rows {
  id: 413
  needCloseOthers: false
  jumpParam: "13000011"
  jumpType: 11
  jumpText: "牛牛牧场小店"
}
rows {
  id: 420
  needCloseOthers: false
  jumpParam: "30045"
  jumpType: 491
  jumpText: "活动中心（狼人默契队）"
}
rows {
  id: 10502
  needCloseOthers: false
  jumpType: 10502
  jumpText: "狼人备战身份一览"
}
rows {
  id: 10645
  needCloseOthers: false
  jumpParam: "45"
  jumpType: 10502
  jumpText: "狼人备战身份一览-密道狼身份信息（10600~10700占用）"
}
rows {
  id: 10646
  needCloseOthers: false
  jumpParam: "46"
  jumpType: 10502
  jumpText: "狼人备战身份一览-管道工身份信息（10600~10700占用）"
}
rows {
  id: 10647
  needCloseOthers: false
  jumpParam: "47"
  jumpType: 10502
  jumpText: "狼人备战身份一览-捕梦者身份信息（10600~10700占用）"
}
rows {
  id: 10666
  needCloseOthers: false
  jumpParam: "30012"
  jumpType: 20
  jumpText: "【8月29日周】峡谷排位赛"
}
rows {
  id: 10667
  needCloseOthers: false
  jumpParam: "30211"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "【1月10日周】典韦试炼"
}
rows {
  id: 10668
  needCloseOthers: false
  jumpParam: "30014"
  jumpType: 20
  jumpText: "【8月29日周】钟馗预告"
}
rows {
  id: 10503
  needCloseOthers: true
  jumpType: 10503
  jumpText: "狼人杀-珍宝系统"
}
rows {
  id: 10504
  needCloseOthers: false
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面（10800~10900珍宝道具占用）"
}
rows {
  id: 10801
  needCloseOthers: false
  jumpParam: "240901"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-金钱罐"
}
rows {
  id: 10802
  needCloseOthers: false
  jumpParam: "240902"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-炫彩号牌"
}
rows {
  id: 10803
  needCloseOthers: false
  jumpParam: "240903"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-雷霆之锤"
}
rows {
  id: 10804
  needCloseOthers: false
  jumpParam: "240904"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-水晶之星"
}
rows {
  id: 10805
  needCloseOthers: false
  jumpParam: "240905"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-幸运骰子"
}
rows {
  id: 10806
  needCloseOthers: false
  jumpParam: "240906"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-灵笛"
}
rows {
  id: 10807
  needCloseOthers: false
  jumpParam: "240907"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-全息装置"
}
rows {
  id: 10808
  needCloseOthers: false
  jumpParam: "240908"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-天马缰绳"
}
rows {
  id: 10809
  needCloseOthers: false
  jumpParam: "240909"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-神奇画布"
}
rows {
  id: 10810
  needCloseOthers: false
  jumpParam: "240910"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-百宝箱"
}
rows {
  id: 10811
  needCloseOthers: false
  jumpParam: "240911"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-百变徽章"
}
rows {
  id: 10812
  needCloseOthers: false
  jumpParam: "240912"
  jumpType: 10504
  jumpText: "狼人杀-珍宝信息界面-共享权杖"
}
rows {
  id: 60001
  needCloseOthers: true
  jumpParam: "-830.0,-2038.0,15.0"
  jumpType: 204
  jumpText: "跳转到好好鸭面前"
}
rows {
  id: 816
  needCloseOthers: false
  jumpParam: "5054"
  jumpType: 11
  jumpText: "狼人卡池第二页签"
}
rows {
  id: 355
  jumpParam: "30008"
  jumpType: 20
  jumpText: "【8月23日周】狼人大师修炼"
}
rows {
  id: 356
  needCloseOthers: false
  jumpParam: "30015"
  jumpType: 20
  jumpText: "830狼人游学记"
}
rows {
  id: 5033
  needCloseOthers: false
  jumpParam: "5033;1.3.78.1;1"
  jumpType: 520
  jumpText: "跳转到发现-冲段挑战"
}
rows {
  id: 5034
  needCloseOthers: false
  jumpParam: "5034"
  jumpType: 20
  jumpText: "跳转到发现-造梦之旅"
}
rows {
  id: 5035
  needCloseOthers: false
  jumpParam: "5035"
  jumpType: 20
  jumpText: "跳转到发现-追梦星途"
}
rows {
  id: 5036
  needCloseOthers: false
  jumpParam: "5036"
  jumpType: 20
  jumpText: "跳转到发现-扫码一起玩"
}
rows {
  id: 339
  needCloseOthers: false
  jumpParam: "4444"
  jumpType: 20
  jumpText: "跳转到 幸运丰收"
}
rows {
  id: 358
  needCloseOthers: false
  jumpParam: "27"
  jumpType: 15
  jumpText: "跳转到 商城 宝库 农场"
}
rows {
  id: 10669
  needCloseOthers: false
  jumpParam: "30025"
  jumpType: 20
  jumpText: "【9月6日周】英雄周试练"
}
rows {
  id: 10670
  needCloseOthers: false
  jumpParam: "30181"
  jumpType: 20
  jumpText: "【9月6日周】新英雄预告"
}
rows {
  id: 50101
  needCloseOthers: false
  jumpType: 50101
  jumpText: "跳转到Arena商城热销页面(默认页面)"
}
rows {
  id: 50102
  needCloseOthers: false
  jumpType: 50102
  jumpText: "跳转到Arena商城英雄页面"
}
rows {
  id: 50103
  needCloseOthers: false
  jumpType: 50103
  jumpText: "跳转到arena商城 皮肤页签 英雄皮肤"
}
rows {
  id: 50104
  needCloseOthers: false
  jumpType: 50104
  jumpText: "跳转到arena商城 定制页签 播报"
}
rows {
  id: 50105
  needCloseOthers: false
  jumpType: 50105
  jumpText: "跳转到Arena商城道具页面"
}
rows {
  id: 50106
  needCloseOthers: false
  jumpType: 50106
  jumpText: "跳转到arena商城 定制页签 头像框"
}
rows {
  id: 50107
  needCloseOthers: false
  jumpType: 50107
  jumpText: "跳转到arena商城 皮肤页签 武器皮肤"
}
rows {
  id: 50108
  needCloseOthers: false
  jumpParam: "5600"
  jumpType: 1
  jumpText: "跳转到arena3V3休闲"
}
rows {
  id: 50109
  needCloseOthers: false
  jumpParam: "5601"
  jumpType: 1
  jumpText: "跳转到arena3V3排位"
}
rows {
  id: 50110
  needCloseOthers: false
  jumpParam: "6006"
  jumpType: 1
  jumpText: "跳转到arena3V3吃鸡"
}
rows {
  id: 50111
  needCloseOthers: false
  jumpParam: "6101"
  jumpType: 1
  jumpText: "跳转到arena5V5休闲"
}
rows {
  id: 50112
  needCloseOthers: false
  jumpParam: "6102"
  jumpType: 1
  jumpText: "跳转到arena5V5排位"
}
rows {
  id: 50113
  needCloseOthers: false
  jumpParam: "5700"
  jumpType: 1
  jumpText: "跳转到arena占地盘休闲"
}
rows {
  id: 8001
  needCloseOthers: false
  jumpParam: "50000002"
  jumpType: 11
  jumpText: "西行之路 齐天大圣"
}
rows {
  id: 8002
  needCloseOthers: false
  jumpParam: "50000003"
  jumpType: 11
  jumpText: "西行之路 金蝉子"
}
rows {
  id: 360
  needCloseOthers: false
  jumpParam: "30006"
  jumpType: 20
  jumpText: "【9月6日周】寻秘冒险礼"
}
rows {
  id: 361
  needCloseOthers: false
  jumpParam: "13000012"
  jumpType: 11
  jumpText: "登月卡池-滑板涂鸦"
}
rows {
  id: 362
  needCloseOthers: false
  jumpParam: "13000013"
  jumpType: 11
  jumpText: "登月卡池-闪烁刺客"
}
rows {
  id: 363
  needCloseOthers: false
  jumpParam: "40000001"
  jumpType: 11
  jumpText: "嫦娥卡池"
}
rows {
  id: 364
  needCloseOthers: false
  jumpParam: "40000003"
  jumpType: 11
  jumpText: "鎏金扇卡池"
}
rows {
  id: 365
  needCloseOthers: false
  jumpParam: "30009"
  jumpType: 20
  jumpText: "跳转到 鱼塘幸运星"
}
rows {
  id: 10671
  needCloseOthers: false
  jumpParam: "30224"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "0117峡谷排位赛"
}
rows {
  id: 10672
  needCloseOthers: false
  jumpParam: "30225"
  jumpType: 20
  jumpText: "0117卡牌羁绊试炼"
}
rows {
  id: 10673
  needCloseOthers: false
  jumpParam: "30226"
  jumpType: 20
  jumpText: "0117随机事件"
}
rows {
  id: 250
  needCloseOthers: false
  jumpParam: "16"
  jumpType: 250
  jumpText: "跳转到星世界-星图热榜-名图堂"
}
rows {
  id: 366
  needCloseOthers: false
  jumpParam: "10000005"
  jumpType: 11
  jumpText: "跳转到甜兔仙踪返场"
}
rows {
  id: 8003
  needCloseOthers: false
  jumpParam: "UI_Recharge_LuckyTurntable"
  jumpType: 8
  jumpText: "幸运转盘"
}
rows {
  id: 367
  jumpType: 367
  jumpText: "IOS小游戏_索要礼物跳邮箱"
}
rows {
  id: 10674
  needCloseOthers: false
  jumpParam: "6000101"
  jumpType: 11
  jumpText: "王者二期卡池"
}
rows {
  id: 368
  needCloseOthers: false
  jumpParam: "10000010"
  jumpType: 11
  jumpText: "丰收兔"
}
rows {
  id: 10675
  needCloseOthers: false
  jumpParam: "30246"
  jumpType: 20
  jumpText: "新年送刘备"
}
rows {
  id: 10676
  needCloseOthers: false
  jumpParam: "30044"
  jumpType: 20
  jumpText: "【9月19日周】新英雄预告"
}
rows {
  id: 251
  needCloseOthers: false
  jumpParam: "10000"
  jumpType: 251
  jumpText: "跳转到星世界-星图广场-广场首页-精品合集"
}
rows {
  id: 252
  needCloseOthers: false
  jumpParam: "10001"
  jumpType: 251
  jumpText: "跳转到星世界-星图广场-广场首页-猜你喜欢"
}
rows {
  id: 369
  needCloseOthers: false
  jumpParam: "28"
  jumpType: 15
  jumpText: "跳转到 商城 宝库 飞车"
}
rows {
  id: 370
  needCloseOthers: false
  jumpParam: "30020"
  jumpType: 20
  jumpText: "狼人捕梦旅"
}
rows {
  id: 371
  needCloseOthers: false
  jumpParam: "40000004"
  jumpType: 11
  jumpText: "星之子·龙霄卡池"
}
rows {
  id: 10677
  needCloseOthers: false
  jumpParam: "6000102"
  jumpType: 11
  jumpText: "王者二期卡池"
}
rows {
  id: 10678
  needCloseOthers: false
  jumpParam: "30050"
  jumpType: 20
  jumpText: "国庆moba连登活动"
}
rows {
  id: 10679
  needCloseOthers: false
  jumpParam: "30051"
  jumpType: 20
  jumpText: "国庆moba上分预热"
}
rows {
  id: 10680
  needCloseOthers: true
  jumpParam: "30361"
  jumpType: 20
  jumpText: "峡谷排位赛"
}
rows {
  id: 421
  needCloseOthers: false
  jumpParam: "5037"
  jumpType: 20
  jumpText: "国庆活动-收集"
}
rows {
  id: 422
  needCloseOthers: true
  jumpType: 410
  jumpText: "国庆活动-换装镜"
}
rows {
  id: 423
  needCloseOthers: true
  jumpParam: "5038"
  jumpType: 20
  jumpText: "国庆活动-换装镜"
}
rows {
  id: 424
  needCloseOthers: false
  jumpParam: "50844425085184712"
  jumpType: 68
  jumpText: "国庆活动-换装镜"
}
rows {
  id: 1061
  needCloseOthers: false
  jumpParam: "18"
  jumpType: 1
  jumpText: "跳转至【泡泡大战】玩法排位界面"
}
rows {
  id: 372
  needCloseOthers: false
  jumpParam: "10000036"
  jumpType: 11
  jumpText: "跳转到绿洲奇遇"
}
rows {
  id: 375
  needCloseOthers: false
  jumpParam: "10000007"
  jumpType: 11
  jumpText: "狼人魔法屋返场-变蛙魔法"
}
rows {
  id: 376
  needCloseOthers: false
  jumpParam: "10000008"
  jumpType: 11
  jumpText: "狼人魔法屋返场-舞台剧"
}
rows {
  id: 373
  needCloseOthers: false
  jumpType: 490
  jumpText: "狼人组队-查看默契指数h5"
}
rows {
  id: 8006
  needCloseOthers: false
  jumpParam: "50001001"
  jumpType: 11
  jumpText: "千都三彩"
}
rows {
  id: 1808
  needCloseOthers: false
  jumpType: 22
  jumpText: "跳转到个人成就【成长】页签"
}
rows {
  id: 374
  jumpParam: "6191"
  jumpType: 20
  jumpText: "通过param(id)跳转活动子界面(组队消费)"
}
rows {
  id: 10685
  needCloseOthers: false
  jumpParam: "UI_Arena_Mall_HotAvatarBuy"
  jumpType: 8
  jumpText: "云缨直售"
}
rows {
  id: 10686
  jumpParam: "6101,30224"
  jumpType: 23
  jumpText: "峡谷排位赛moba大厅跳转"
}
rows {
  id: 10695
  needCloseOthers: false
  jumpParam: "30066"
  jumpType: 20
  jumpText: "预告"
}
rows {
  id: 377
  needCloseOthers: true
  jumpType: 377
  jumpText: "跳转到个人信息页面下的心情设置界面"
}
rows {
  id: 10696
  needCloseOthers: false
  jumpParam: "30067"
  jumpType: 20
  jumpText: "冲分"
}
rows {
  id: 378
  needCloseOthers: false
  jumpParam: "13000014"
  jumpType: 11
  jumpText: "梦幻告白"
}
rows {
  id: 430
  needCloseOthers: false
  jumpParam: "30057"
  jumpType: 20
  jumpText: "派对前夜"
}
rows {
  id: 433
  jumpParam: "https://pd.qq.com/post/B_10e609674ee809001441152186900335840X60?from=ingame&guildId=650244193992991868&channelId=638318894&posterTinyId=144115218185223320&createTime=1715607848&traceId=8e0b94d16093487d5a946b6834d9f01d&joinSig=Z0FVby9JMmgyMXJDTHFtMnNUcTlRQUFhRzFPelZzbHlQTnBRUVNvOUVISGRFdkw1WTBWZFVoZFJvK1l2&utm_source=ingame&mode=1"
  jumpType: 90
  jumpText: "萌宠分享礼"
}
rows {
  id: 434
  jumpParam: "https://game.weixin.qq.com/cgi-bin/comm/openlink?auth_appid=wx62d9035fd4fd2059&url=https%3A%2F%2Fgame.weixin.qq.com%2Fcgi-bin%2Fh5%2Flite%2Fclustertopic%2Findex.html%3Fclusterid%3D31254%26cluster_content_id%3DJdzUYV2WbGfRH7rbUjGt5g%26wechat_pkgid%3Dlite_clustertopic%23wechat_redirect"
  jumpType: 90
  jumpText: "萌宠分享礼"
}
rows {
  id: 10697
  needCloseOthers: false
  jumpParam: "UI_Arena_Mall_HotAvatarBuy"
  jumpType: 8
  jumpText: "墨子直售"
}
rows {
  id: 10698
  needCloseOthers: false
  jumpParam: "6000104"
  jumpType: 11
  jumpText: "王者5期卡池"
}
rows {
  id: 241
  jumpType: 241
  jumpText: "跳转到躲猫猫模式备战界面"
}
rows {
  id: 435
  needCloseOthers: false
  jumpParam: "5056"
  jumpType: 11
  jumpText: "糖果女巫卡池"
}
rows {
  id: 379
  needCloseOthers: false
  jumpParam: "10000009"
  jumpType: 11
  jumpText: "跳转到仙狐花隐"
}
rows {
  id: 380
  needCloseOthers: false
  jumpParam: "30000004"
  jumpType: 11
  jumpText: "跳转到仙狐花隐"
}
rows {
  id: 431
  needCloseOthers: false
  jumpParam: "30378"
  jumpType: 20
  jumpText: "跳转狼人活跃活动-观赛有好礼"
}
rows {
  id: 432
  needCloseOthers: false
  jumpParam: "30366"
  jumpType: 20
  jumpText: "跳转狼人活跃活动-组队开宝箱"
}
rows {
  id: 436
  needCloseOthers: false
  jumpParam: "30073"
  jumpType: 20
  jumpText: "跳转狼人活跃活动-畅玩新身份2"
}
rows {
  id: 437
  needCloseOthers: true
  jumpParam: "105;30268"
  jumpType: 23
  jumpText: "跳转狼人活跃活动-精灵谷集结"
}
rows {
  id: 444
  needCloseOthers: false
  jumpParam: "30406"
  jumpType: 20
  jumpText: "跳转狼人活跃活动-赛季末冲刺"
}
rows {
  id: 497
  needCloseOthers: false
  jumpParam: "30403"
  jumpType: 20
  jumpText: "跳转狼人活跃活动-农场美食节"
}
rows {
  id: 516
  needCloseOthers: false
  jumpParam: "30310"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "跳转狼人活跃活动-峡谷挑战"
}
rows {
  id: 390
  needCloseOthers: false
  jumpType: 390
  jumpText: "跳转到微信游戏圈社区"
}
rows {
  id: 391
  needCloseOthers: false
  jumpType: 391
  jumpText: "跳转星世界-造梦空间-创建地图-默认"
}
rows {
  id: 392
  needCloseOthers: false
  jumpParam: "4101,410101"
  jumpType: 391
  JumpPlatfromLimit: 7
  JumpPlatfromLimitToastID: 203740
  jumpText: "跳转星世界-造梦空间-创建地图-竞速类-官方模板"
}
rows {
  id: 393
  needCloseOthers: false
  jumpParam: "4101,410102"
  jumpType: 391
  JumpPlatfromLimit: 7
  JumpPlatfromLimitToastID: 203740
  jumpText: "跳转星世界-造梦空间-创建地图-竞速类-社区模板"
}
rows {
  id: 394
  needCloseOthers: false
  jumpParam: "4101,410103"
  jumpType: 391
  jumpText: "跳转星世界-造梦空间-创建地图-竞速类-功能演示"
}
rows {
  id: 395
  needCloseOthers: false
  jumpParam: "4103,410301"
  jumpType: 391
  jumpText: "跳转星世界-造梦空间-创建地图-射击类-官方模板"
}
rows {
  id: 396
  needCloseOthers: false
  jumpParam: "4103,410302"
  jumpType: 391
  jumpText: "跳转星世界-造梦空间-创建地图-射击类-社区模板"
}
rows {
  id: 397
  needCloseOthers: false
  jumpParam: "4103,410303"
  jumpType: 391
  jumpText: "跳转星世界-造梦空间-创建地图-射击类-功能演示"
}
rows {
  id: 398
  needCloseOthers: false
  jumpParam: "4102,410201"
  jumpType: 391
  jumpText: "跳转星世界-造梦空间-创建地图-生存类-官方模板"
}
rows {
  id: 399
  needCloseOthers: false
  jumpParam: "4102,410202"
  jumpType: 391
  jumpText: "跳转星世界-造梦空间-创建地图-生存类-社区模板"
}
rows {
  id: 438
  needCloseOthers: false
  jumpParam: "30048"
  jumpType: 20
  jumpText: "跳转到农场礼物盒活动"
}
rows {
  id: 439
  needCloseOthers: false
  jumpParam: "13000015"
  jumpType: 11
  jumpText: "狼人S8卡池-魔法扫帚"
}
rows {
  id: 440
  needCloseOthers: false
  jumpParam: "13000016"
  jumpType: 11
  jumpText: "狼人S8卡池-爱心冲击"
}
rows {
  id: 381
  needCloseOthers: false
  jumpParam: "40000005"
  jumpType: 11
  jumpText: "开心超人卡池"
}
rows {
  id: 382
  needCloseOthers: false
  jumpParam: "40000006"
  jumpType: 11
  jumpText: "甜心超人卡池"
}
rows {
  id: 383
  needCloseOthers: false
  jumpParam: "40000007"
  jumpType: 11
  jumpText: "小新超人卡池"
}
rows {
  id: 441
  needCloseOthers: false
  jumpParam: "1"
  jumpType: 601
  jumpText: "跳转微信订阅 - 农场"
}
rows {
  id: 442
  needCloseOthers: false
  jumpParam: "2"
  jumpType: 601
  jumpText: "跳转微信订阅 - 活动"
}
rows {
  id: 400
  needCloseOthers: false
  jumpParam: "13000061"
  jumpType: 11
  jumpText: "跳转到招财喵"
}
rows {
  id: 1062
  needCloseOthers: false
  jumpParam: "5307"
  jumpType: 11
  jumpText: "月光女神卡池"
}
rows {
  id: 1063
  needCloseOthers: false
  jumpParam: "5055"
  jumpType: 11
  jumpText: "守护之翼卡池"
}
rows {
  id: 443
  needCloseOthers: true
  jumpParam: "-1763,4238,37,46.5"
  jumpType: 204
  jumpText: "跳转到周年庆送蛋糕关卡"
}
rows {
  id: 1064
  needCloseOthers: false
  jumpParam: "130200"
  jumpType: 15
  jumpText: "限时礼包"
}
rows {
  id: 1065
  needCloseOthers: false
  jumpParam: "160404"
  jumpType: 15
  jumpText: "超值礼包"
}
rows {
  id: 8010
  needCloseOthers: false
  jumpParam: "53000001"
  jumpType: 11
  jumpText: "永恒之舞"
}
rows {
  id: 445
  needCloseOthers: false
  jumpParam: "UI_Recharge_Werewolf_Promotion"
  jumpType: 8
  jumpText: "狼人满减"
}
rows {
  id: 446
  needCloseOthers: false
  jumpParam: "0"
  jumpType: 228
  jumpText: "柯南集卡-跳转任务"
}
rows {
  id: 447
  needCloseOthers: false
  jumpParam: "5019"
  jumpType: 447
  jumpText: "跳转侦探小队留影"
}
rows {
  id: 448
  needCloseOthers: false
  jumpParam: "5018"
  jumpType: 448
  jumpText: "跳转到侦探小队"
}
rows {
  id: 1066
  needCloseOthers: false
  jumpParam: "130300"
  jumpType: 15
  jumpText: "特色礼包"
}
rows {
  id: 449
  needCloseOthers: false
  jumpParam: "30085"
  jumpType: 20
  jumpText: "跳转到农场养绿植活动"
}
rows {
  id: 5101
  needCloseOthers: false
  jumpParam: "1112403222"
  jumpType: 5101
  jumpText: "跳隐形守护者qq小游戏"
}
rows {
  id: 8009
  needCloseOthers: false
  jumpParam: "UI_LuckyFree_MainView"
  jumpType: 8
  jumpText: "幸运免单"
}
rows {
  id: 450
  needCloseOthers: false
  jumpParam: "13000048"
  jumpType: 11
  jumpText: "蔚海绮梦"
}
rows {
  id: 451
  needCloseOthers: false
  jumpParam: "13000024"
  jumpType: 11
  jumpText: "闪烁突袭-登月返场"
}
rows {
  id: 452
  needCloseOthers: false
  jumpParam: "13000023"
  jumpType: 11
  jumpText: "滑板-登月返场"
}
rows {
  id: 650
  needCloseOthers: false
  jumpType: 650
  jumpText: "跳转到创作者中心-造梦学院-造梦系列课"
}
rows {
  id: 6501
  needCloseOthers: false
  jumpParam: "{\"tabIndex\":1,\"tagId\":\"1\"}"
  jumpType: 650
  jumpText: "跳转到创作者中心-造梦学院-造梦工具箱"
}
rows {
  id: 6502
  needCloseOthers: false
  jumpParam: "{\"tabIndex\":1,\"tagId\":\"2\"}"
  jumpType: 650
  jumpText: "跳转到创作者中心-造梦学院-造梦课代表"
}
rows {
  id: 6503
  needCloseOthers: false
  jumpParam: "{\"tabIndex\":1,\"tagId\":\"1\",\"typeId\":\"19\"}"
  jumpType: 650
  jumpText: "跳转到创作者中心-造梦学院-造梦工具箱-界面编辑"
}
rows {
  id: 455
  needCloseOthers: false
  jumpParam: "30120"
  jumpType: 20
  jumpText: "跳转至鱼塘幸运星二期活动"
}
rows {
  id: 10699
  needCloseOthers: false
  jumpParam: "60001031"
  jumpType: 11
  jumpText: "夏侯惇"
}
rows {
  id: 11000
  needCloseOthers: false
  jumpParam: "630"
  jumpType: 20
  jumpText: "隐形守护者上线活动"
}
rows {
  id: 11001
  needCloseOthers: false
  jumpParam: "104"
  jumpType: 16
  jumpText: "发现系统福利商店跳转"
}
rows {
  id: 60000
  jumpType: 60000
  jumpText: "跳转到 Arena 主界面或 Arena 主界面及英雄皮肤列表界面"
}
rows {
  id: 8011
  needCloseOthers: false
  jumpParam: "50001002"
  jumpType: 11
  jumpText: "巴啦啦小蓝"
}
rows {
  id: 8012
  needCloseOthers: false
  jumpParam: "50001003"
  jumpType: 11
  jumpText: "巴啦啦游乐"
}
rows {
  id: 456
  needCloseOthers: false
  jumpParam: "13000021"
  jumpType: 11
  jumpText: "躲猫猫卡池1"
}
rows {
  id: 457
  needCloseOthers: false
  jumpParam: "13000022"
  jumpType: 11
  jumpText: "躲猫猫卡池2"
}
rows {
  id: 458
  needCloseOthers: false
  jumpParam: "5206"
  jumpType: 11
  jumpText: "星光乐师"
}
rows {
  id: 10700
  needCloseOthers: false
  jumpParam: "60001032"
  jumpType: 11
  jumpText: "张良"
}
rows {
  id: 459
  jumpParam: "https://pd.qq.com/s/1mi8ws0cm?shareSource=5"
  jumpType: 90
  jumpText: "跳转至收藏品大赏社区活动"
}
rows {
  id: 460
  jumpParam: "https://game.weixin.qq.com/cgi-bin/comm/openlink?auth_appid=wx62d9035fd4fd2059&url=https%3A%2F%2Fgame.weixin.qq.com%2Fcgi-bin%2Fh5%2Flite%2Fclustertopic%2Findex.html%3Fclusterid%3D31863%26cluster_content_id%3DxbToKFOxnLiv6UPBVZod0g%26wechat_pkgid%3Dlite_clustertopic%23wechat_redirect"
  jumpType: 90
  jumpText: "跳转至收藏品大赏社区活动"
}
rows {
  id: 461
  jumpParam: "30110"
  jumpType: 20
  jumpText: "跳转至农场好友礼活动"
}
rows {
  id: 242
  jumpType: 242
  jumpText: "跳转到印章祈愿"
}
rows {
  id: 18911
  needCloseOthers: false
  jumpParam: "18911,https://fenxiang.qq.com/home/<USER>//fenxiang.qq.com/act/xfmix0Fxf4/20250520/ymzx/gameuvip"
  jumpType: 18911
  jumpText: "跳转客服"
}
rows {
  id: 492
  jumpType: 492
  jumpText: "跳转到【发现-特色玩法-组队开黑】"
}
rows {
  id: 493
  needCloseOthers: false
  jumpParam: "0"
  jumpType: 493
  jumpText: "跳转到柯南导航页"
}
rows {
  id: 495
  needCloseOthers: false
  jumpParam: "13000025"
  jumpType: 11
  jumpText: "狼人柯南卡池1"
}
rows {
  id: 496
  needCloseOthers: false
  jumpParam: "13000026"
  jumpType: 11
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "狼人柯南卡池2"
}
rows {
  id: 498
  needCloseOthers: false
  jumpParam: "0"
  jumpType: 498
  jumpText: "友谊之火页签跳转"
}
rows {
  id: 499
  jumpParam: " https://spk.qq.com/uAWs8UD1"
  jumpType: 90
  jumpText: "网赚小游戏icon跳转H5"
}
rows {
  id: 501
  jumpParam: " https://spk.qq.com/uAWs8UD1"
  jumpType: 106
  jumpText: "网赚小游戏icon跳转H5"
}
rows {
  id: 502
  needCloseOthers: true
  jumpParam: "UI_SnowBless_MainView"
  jumpType: 8
  jumpText: "冰雪赐福"
}
rows {
  id: 1067
  needCloseOthers: false
  jumpParam: "30000010"
  jumpType: 11
  jumpText: "天线宝宝"
}
rows {
  id: 1068
  needCloseOthers: false
  jumpParam: "30000007"
  jumpType: 11
  jumpText: "名侦探柯南"
}
rows {
  id: 503
  needCloseOthers: false
  jumpParam: "40000008"
  jumpType: 11
  jumpText: "冰雪圆舞曲祈愿"
}
rows {
  id: 504
  needCloseOthers: false
  jumpParam: "30094"
  jumpType: 20
  jumpText: "跳转到农场赠礼季"
}
rows {
  id: 505
  needCloseOthers: false
  jumpParam: "380"
  jumpType: 1
  jumpText: "跳转到玩法-mayday测试 合作"
}
rows {
  id: 506
  needCloseOthers: false
  jumpParam: "381"
  jumpType: 1
  jumpText: "跳转到玩法-mayday测试 内鬼"
}
rows {
  id: 507
  needCloseOthers: false
  jumpParam: "UI_Recharge_Werewolf_Promotion"
  jumpType: 8
  jumpText: "狼人满减二期"
}
rows {
  id: 10701
  needCloseOthers: false
  jumpParam: "6000105"
  jumpType: 11
  jumpText: "峡谷幻梦"
}
rows {
  id: 508
  needCloseOthers: false
  jumpType: 499
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "跳转至奖励找回"
}
rows {
  id: 509
  needCloseOthers: false
  jumpParam: "13000058"
  jumpType: 11
  jumpText: "雪境欢颂"
}
rows {
  id: 514
  jumpParam: "32"
  jumpType: 16
  jumpText: "跳转至旅行小狗兑换商城"
}
rows {
  id: 515
  jumpParam: "30154"
  jumpType: 20
  jumpText: "跳转至旅行小狗活动"
}
rows {
  id: 510
  needCloseOthers: false
  jumpParam: "10000011"
  jumpType: 11
  jumpText: "海狮公主"
}
rows {
  id: 517
  jumpParam: "200"
  jumpType: 10003
  jumpText: "跳转到电视台"
}
rows {
  id: 518
  jumpType: 243
  jumpText: "退出当前登录"
}
rows {
  id: 10702
  needCloseOthers: true
  jumpParam: "145"
  jumpType: 16
  jumpText: "峡谷幻梦兑换商店"
}
rows {
  id: 10703
  needCloseOthers: true
  jumpParam: "152"
  jumpType: 16
  jumpText: "峡谷幻梦兑换商店"
}
rows {
  id: 10704
  needCloseOthers: false
  jumpParam: "145"
  jumpType: 16
  jumpText: "峡谷幻梦兑换商店"
}
rows {
  id: 10705
  needCloseOthers: false
  jumpParam: "152"
  jumpType: 16
  jumpText: "峡谷幻梦兑换商店"
}
rows {
  id: 520
  jumpType: 501
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "moba英雄分享活动"
}
rows {
  id: 521
  needCloseOthers: false
  jumpParam: "10000016"
  jumpType: 11
  jumpText: "跳转至雪球精灵"
}
rows {
  id: 709
  jumpType: 709
  jumpText: "柯南答题活动"
}
rows {
  id: 522
  needCloseOthers: false
  jumpParam: "30173"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "跳转至周年返场礼"
}
rows {
  id: 523
  needCloseOthers: false
  jumpParam: "30177"
  jumpType: 20
  jumpText: "跳转至一起来推理"
}
rows {
  id: 524
  needCloseOthers: false
  jumpParam: "30186"
  jumpType: 20
  jumpText: "跳转至狼人全新彩蛋局"
}
rows {
  id: 902
  needCloseOthers: false
  jumpType: 902
  jumpText: "跳转到卡牌系统主界面"
}
rows {
  id: 525
  needCloseOthers: false
  jumpParam: "10000012"
  jumpType: 11
  jumpText: "狼人翻牌卡池1"
}
rows {
  id: 526
  needCloseOthers: false
  jumpParam: "10000013"
  jumpType: 11
  jumpText: "狼人翻牌卡池2"
}
rows {
  id: 527
  needCloseOthers: false
  jumpParam: "10000014"
  jumpType: 11
  jumpText: "狼人翻牌卡池3"
}
rows {
  id: 528
  needCloseOthers: false
  jumpParam: "10000015"
  jumpType: 11
  jumpText: "狼人翻牌卡池4"
}
rows {
  id: 10706
  needCloseOthers: false
  jumpParam: "60001061"
  jumpType: 11
  jumpText: "峡谷战神"
}
rows {
  id: 1069
  needCloseOthers: false
  jumpParam: "5056"
  jumpType: 11
  jumpText: "电竞少女"
}
rows {
  id: 8500
  needCloseOthers: false
  jumpParam: "109596"
  jumpType: 85
  jumpText: "飞鹰跳转测试"
}
rows {
  id: 529
  needCloseOthers: false
  jumpParam: "30160"
  jumpType: 20
  jumpText: "狼人福利局-鸵鸟新身份"
}
rows {
  id: 1072
  needCloseOthers: false
  jumpParam: "30167"
  jumpType: 20
  jumpText: "农场天天领"
}
rows {
  id: 10707
  needCloseOthers: false
  jumpParam: "60001062"
  jumpType: 11
  jumpText: "花木兰"
}
rows {
  id: 530
  needCloseOthers: false
  jumpParam: "30187"
  jumpType: 20
  jumpText: "狼人阵营对决活动"
}
rows {
  id: 531
  needCloseOthers: false
  jumpParam: "https://game.weixin.qq.com/cgi-bin/comm/openlink?auth_appid=wx62d9035fd4fd2059&url=https%3A%2F%2Fgame.weixin.qq.com%2Fcgi-bin%2Fh5%2Flite%2Fclustertopic%2Findex.html%3Fclusterid%3D32594%26cluster_content_id%3D2QH_Q78kTuw9MYpB6-lo8g%26wechat_pkgid%3Dlite_clustertopic%23wechat_redirect"
  jumpType: 90
  jumpText: "h5链接跳转农场迎初雪vx"
}
rows {
  id: 532
  needCloseOthers: false
  jumpParam: "https://pd.qq.com/s/d9gl8wn9a?shareSource=5"
  jumpType: 90
  jumpText: "h5链接跳转农场迎初雪qq"
}
rows {
  id: 533
  needCloseOthers: false
  jumpParam: "https://wxaurl.cn/fTZqXQDaZ1k"
  jumpType: 90
  jumpText: "h5链接跳转农场助手"
}
rows {
  id: 534
  needCloseOthers: false
  jumpParam: "https://static.gamecenter.qq.com/social-web/ok?open_kuikly_info=%7B%7D&page_name=amfoe&bundle_name=amfoe&i="
  jumpType: 90
  jumpText: "h5链接跳转农场送福利"
}
rows {
  id: 903
  needCloseOthers: false
  jumpType: 903
  jumpText: "跳转到Mayday无尽模式"
}
rows {
  id: 8501
  needCloseOthers: false
  jumpParam: "110100"
  jumpType: 85
  jumpText: "飞鹰跳转1"
}
rows {
  id: 8502
  needCloseOthers: false
  jumpParam: "110056"
  jumpType: 85
  jumpText: "飞鹰跳转2"
}
rows {
  id: 8503
  needCloseOthers: false
  jumpParam: "110064"
  jumpType: 85
  jumpText: "飞鹰跳转3"
}
rows {
  id: 8504
  needCloseOthers: false
  jumpParam: "109032"
  jumpType: 85
  jumpText: "飞鹰跳转4"
}
rows {
  id: 2001
  jumpParam: "18911"
  jumpType: 20
  jumpText: "先锋商城（长期）"
}
rows {
  id: 1070
  needCloseOthers: false
  jumpParam: "UI_ChargeRebate_Main"
  jumpType: 8
  jumpText: "新年充值送（载具累充）"
}
rows {
  id: 565
  needCloseOthers: false
  jumpParam: "30220"
  jumpType: 20
  jumpText: "祈福搭子活动跳转"
}
rows {
  id: 10710
  needCloseOthers: false
  jumpParam: "6000107"
  jumpType: 11
  jumpText: "峡谷幻梦"
}
rows {
  id: 543
  needCloseOthers: false
  jumpParam: "13000029"
  jumpType: 11
  jumpText: "躲猫猫二期1"
}
rows {
  id: 544
  needCloseOthers: false
  jumpParam: "13000030"
  jumpType: 11
  jumpText: "多猫猫二期2"
}
rows {
  id: 538
  needCloseOthers: false
  jumpParam: "10000017"
  jumpType: 11
  jumpText: "跳转至仙福盈门"
}
rows {
  id: 1073
  needCloseOthers: false
  jumpParam: "30000011"
  jumpType: 11
  jumpText: "三丽鸥二期1"
}
rows {
  id: 539
  needCloseOthers: false
  jumpParam: "13000031"
  jumpType: 11
  jumpText: "云霄飞车返场1"
}
rows {
  id: 540
  needCloseOthers: false
  jumpParam: "13000032"
  jumpType: 11
  jumpText: "云霄飞车返场2"
}
rows {
  id: 536
  needCloseOthers: false
  jumpParam: "5207"
  jumpType: 11
  jumpText: "春日精灵"
}
rows {
  id: 542
  needCloseOthers: false
  jumpParam: "30206"
  jumpType: 20
  jumpText: "农场预告季活动"
}
rows {
  id: 354
  needCloseOthers: false
  jumpParam: "4"
  jumpType: 354
  jumpText: "跳转到天天竞技赛bp"
}
rows {
  id: 60002
  needCloseOthers: true
  jumpParam: "-969.0,-1007.0,15.0"
  jumpType: 204
  jumpText: "跳转到专属伙伴面前"
}
rows {
  id: 10711
  needCloseOthers: false
  jumpParam: "6000110"
  jumpType: 11
  jumpText: "峡谷祈愿"
}
rows {
  id: 547
  needCloseOthers: false
  jumpParam: "30202"
  jumpType: 491
  jumpText: "活动中心（狼人默契队）"
}
rows {
  id: 545
  needCloseOthers: false
  jumpParam: "30207"
  jumpType: 20
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "狼人冲刺周活动"
}
rows {
  id: 560
  needCloseOthers: true
  jumpParam: "5072"
  jumpType: 20
  jumpText: "生日活动跳转"
}
rows {
  id: 546
  needCloseOthers: false
  jumpParam: "13000033"
  jumpType: 11
  jumpText: "福运琳琅"
}
rows {
  id: 535
  needCloseOthers: false
  jumpParam: "5207"
  jumpType: 11
  jumpText: "春日精灵"
}
rows {
  id: 60003
  needCloseOthers: true
  jumpParam: "458,3380,15"
  jumpType: 204
  jumpText: "万松书院活动跳转"
}
rows {
  id: 60004
  needCloseOthers: true
  jumpParam: "1762,-757,15"
  jumpType: 204
  jumpText: "元宵猜灯谜活动跳转"
}
rows {
  id: 550
  needCloseOthers: true
  jumpParam: "-2029,-206,15,200"
  jumpType: 204
  jumpText: "华莱士跳转"
}
rows {
  id: 551
  needCloseOthers: false
  jumpParam: "10000018"
  jumpType: 11
  jumpText: "跳转至嘶嘶灵宝"
}
rows {
  id: 8888
  needCloseOthers: false
  jumpParam: "5800001"
  jumpType: 11
  jumpText: "天启圣谕祈愿"
}
rows {
  id: 552
  needCloseOthers: false
  jumpParam: "30222"
  jumpType: 20
  jumpText: "跳转到农场温泉季活动"
}
rows {
  id: 425
  needCloseOthers: false
  jumpType: 245
  jumpText: "跳转至春节活动玩法选择界面"
}
rows {
  id: 553
  needCloseOthers: false
  jumpParam: "13000035"
  jumpType: 11
  jumpText: "狼人春节卡池1钓了个鱼"
}
rows {
  id: 554
  needCloseOthers: false
  jumpParam: "13000036"
  jumpType: 11
  jumpText: "狼人春节卡池2花样滑冰"
}
rows {
  id: 556
  needCloseOthers: false
  jumpParam: "40000012"
  jumpType: 11
  jumpText: "青白蛇卡池"
}
rows {
  id: 557
  needCloseOthers: false
  jumpParam: "40000009"
  jumpType: 11
  jumpText: "遗落的珍宝（返场卡池）"
}
rows {
  id: 1074
  needCloseOthers: false
  jumpParam: "30000012"
  jumpType: 11
  jumpText: "三丽鸥二期2"
}
rows {
  id: 1075
  needCloseOthers: false
  jumpParam: "30000013"
  jumpType: 11
  jumpText: "三丽鸥二期3"
}
rows {
  id: 1076
  needCloseOthers: false
  jumpParam: "30000014"
  jumpType: 11
  jumpText: "三丽鸥二期4"
}
rows {
  id: 558
  needCloseOthers: false
  jumpParam: "10000019"
  jumpType: 11
  jumpText: "甜蜜魔法"
}
rows {
  id: 559
  needCloseOthers: false
  jumpParam: "30229"
  jumpType: 20
  jumpText: "跳转到农场送装饰活动"
}
rows {
  id: 561
  needCloseOthers: false
  jumpParam: "40000014"
  jumpType: 11
  jumpText: "鲲载具卡池"
}
rows {
  id: 10712
  needCloseOthers: false
  jumpParam: "60001111"
  jumpType: 11
  jumpText: "貂蝉"
}
rows {
  id: 10713
  needCloseOthers: false
  jumpParam: "60001112"
  jumpType: 11
  jumpText: "王昭君"
}
rows {
  id: 562
  needCloseOthers: false
  jumpParam: "8009"
  jumpType: 11
  jumpText: "星光剧场荆棘鸟"
}
rows {
  id: 563
  needCloseOthers: false
  jumpParam: "30217"
  jumpType: 20
  jumpText: "跳转到新春鱼塘幸运星"
}
rows {
  id: 564
  needCloseOthers: false
  jumpParam: "https://act.supercore.qq.com/commercial/act/a44d288e837094854bffc007d34d12d28prerelease/index.html?gid=1465&via=upother_61&singleGame=1"
  jumpType: 90
  jumpText: "跳转到超核新春利是节"
}
rows {
  id: 566
  needCloseOthers: false
  jumpParam: "https://pd.qq.com/g/2l15flt5z9/post/B_f13a8a67b08200001441152186900335840X60?layout=1"
  jumpType: 90
  jumpText: "h5链接跳转农场社区活动-qq"
}
rows {
  id: 567
  needCloseOthers: false
  jumpParam: "https://game.weixin.qq.com/cgi-bin/comm/openlink?auth_appid=wx62d9035fd4fd2059&url=https%3A%2F%2Fgame.weixin.qq.com%2Fcgi-bin%2Fh5%2Flite%2Fclustertopic%2Findex.html%3Fclusterid%3D33162%26cluster_content_id%3DhBKpolQ0sd7USnpta6NF7Q%26wechat_pkgid%3Dlite_clustertopic%23wechat_redirect"
  jumpType: 90
  jumpText: "h5链接跳转农场社区活动-vx"
}
rows {
  id: 60101
  jumpType: 60101
  jumpText: "大王别抓我身份专精界面"
}
rows {
  id: 568
  needCloseOthers: false
  jumpParam: "10000020"
  jumpType: 11
  jumpText: "莱恩特咖啡屋"
}
rows {
  id: 569
  needCloseOthers: false
  jumpParam: "10000021"
  jumpType: 11
  jumpText: "狼人翻牌卡池二期1"
}
rows {
  id: 570
  needCloseOthers: false
  jumpParam: "10000022"
  jumpType: 11
  jumpText: "狼人翻牌卡池二期2"
}
rows {
  id: 571
  needCloseOthers: false
  jumpParam: "10000023"
  jumpType: 11
  jumpText: "狼人翻牌卡池二期3"
}
rows {
  id: 572
  needCloseOthers: false
  jumpParam: "10000024"
  jumpType: 11
  jumpText: "狼人翻牌卡池二期4"
}
rows {
  id: 573
  needCloseOthers: false
  jumpParam: "13000037"
  jumpType: 11
  jumpText: "跳转到梦幻告白复刻卡池"
}
rows {
  id: 575
  needCloseOthers: false
  jumpParam: "13000038"
  jumpType: 11
  jumpText: "桃坞问春"
}
rows {
  id: 10714
  needCloseOthers: false
  jumpParam: "6000112"
  jumpType: 11
  jumpText: "峡谷幻梦"
}
rows {
  id: 8018
  needCloseOthers: false
  jumpParam: "53000002"
  jumpType: 11
  jumpText: "桃源万千"
}
rows {
  id: 576
  needCloseOthers: false
  jumpParam: "30234"
  jumpType: 20
  jumpText: "跳转到农场美食节"
}
rows {
  id: 60102
  needCloseOthers: true
  jumpType: 60102
  jumpText: "跳转到大王皮肤选择界面"
}
rows {
  id: 5102
  needCloseOthers: true
  jumpParam: "UI_Farmyard_FriendList"
  jumpType: 5102
  JumpConditionGroup {
    condition {
      conditionType: 164
      value: 3
    }
    tipsKey: "FarmLevelNeed3"
  }
  jumpText: "跳转进入自己农场并打开界面(社交面板)"
}
rows {
  id: 5103
  needCloseOthers: false
  jumpType: 5103
  jumpText: "跳转进入自己农场或小屋(为了能快速操作加工物)"
}
rows {
  id: 5104
  needCloseOthers: true
  jumpType: 5104
  jumpText: "跳转进入自己农场或餐厅"
}
rows {
  id: 577
  needCloseOthers: false
  jumpParam: "30237"
  jumpType: 20
  jumpText: "跳转到田园恋语"
}
rows {
  id: 578
  needCloseOthers: false
  jumpParam: "30253"
  jumpType: 20
  jumpText: "跳转到田园春日季"
}
rows {
  id: 579
  needCloseOthers: false
  jumpParam: "105;30228"
  jumpType: 23
  jumpText: "狼人跳转测试"
}
rows {
  id: 580
  needCloseOthers: false
  jumpParam: "UI_Recharge_Werewolf_Promotion"
  jumpType: 8
  jumpText: "狼人满减3期"
}
rows {
  id: 1077
  needCloseOthers: false
  jumpParam: "https://act.supercore.qq.com/commercial/act/a44d288e837094854bffc007d34d12d28prerelease/index.html?gid=1465&via=upother_61&singleGame=1"
  jumpType: 90
  jumpText: "跳转到超核新春利是节测试"
}
rows {
  id: 1078
  needCloseOthers: false
  jumpParam: "5208"
  jumpType: 11
  jumpText: "百合信使"
}
rows {
  id: 1154
  needCloseOthers: false
  jumpParam: "354"
  jumpType: 244
  jumpText: "跳转至【大王别抓我】备战界面"
}
rows {
  id: 581
  needCloseOthers: false
  jumpParam: "30261"
  jumpType: 20
  jumpText: "大王皮肤活动"
}
rows {
  id: 60103
  needCloseOthers: true
  jumpParam: "1;1012;1012002"
  jumpType: 60102
  jumpText: "大王别抓我皮肤-拉弥娅鸢尾"
}
rows {
  id: 60104
  needCloseOthers: true
  jumpParam: "2;1026;1026002"
  jumpType: 60102
  jumpText: "大王别抓我皮肤-守护灵彩"
}
rows {
  id: 582
  needCloseOthers: false
  jumpParam: "10000025"
  jumpType: 11
  jumpText: "丰收派对"
}
rows {
  id: 583
  needCloseOthers: false
  jumpParam: "13000041"
  jumpType: 11
  jumpText: "躲猫猫卡池三期1"
}
rows {
  id: 584
  needCloseOthers: false
  jumpParam: "13000042"
  jumpType: 11
  jumpText: "躲猫猫卡池三期2"
}
rows {
  id: 585
  needCloseOthers: false
  jumpParam: "30257"
  jumpType: 20
  jumpText: "跳转到花朝节"
}
rows {
  id: 1079
  needCloseOthers: false
  jumpParam: "5209"
  jumpType: 11
  jumpText: "阿努比斯卡池--返场"
}
rows {
  id: 1080
  needCloseOthers: false
  jumpParam: "4;5;6"
  jumpType: 244
  jumpText: "跳转至【天天晋级赛】玩法界面"
}
rows {
  id: 586
  needCloseOthers: false
  jumpParam: "13000039"
  jumpType: 11
  jumpText: "珍馐百味"
}
rows {
  id: 589
  needCloseOthers: false
  jumpParam: "30264"
  jumpType: 20
  jumpText: "跳转到再遇小红狐"
}
rows {
  id: 590
  needCloseOthers: false
  jumpParam: "10000026"
  jumpType: 11
  jumpText: "狼人翻牌卡池三期1"
}
rows {
  id: 591
  needCloseOthers: false
  jumpParam: "10000027"
  jumpType: 11
  jumpText: "狼人翻牌卡池三期2"
}
rows {
  id: 651
  needCloseOthers: false
  jumpType: 651
  jumpText: "跳转到创作者主页(默认是自己的主页)"
}
rows {
  id: 65101
  jumpParam: "{\"uid\":\"7128788788\",\"creatorId\":\"98328723\"}"
  jumpType: 651
  jumpText: "跳转到指定人的创作者主页(参数填目标用户的uid和creatorId)"
}
rows {
  id: 10715
  needCloseOthers: false
  jumpParam: "60001131"
  jumpType: 11
  jumpText: "诸葛亮"
}
rows {
  id: 10716
  needCloseOthers: false
  jumpParam: "60001132"
  jumpType: 11
  jumpText: "刘备"
}
rows {
  id: 10717
  needCloseOthers: false
  jumpParam: "860"
  jumpType: 10717
  jumpText: "跳转双人成团活动页面"
}
rows {
  id: 592
  needCloseOthers: false
  jumpParam: "10000028"
  jumpType: 11
  jumpText: "狐爷爷"
}
rows {
  id: 593
  needCloseOthers: false
  jumpParam: "30254"
  jumpType: 20
  jumpText: "跳转到农场撕贴纸"
}
rows {
  id: 594
  needCloseOthers: false
  jumpParam: "10000029"
  jumpType: 11
  jumpText: "蜜糖彩虹之梦"
}
rows {
  id: 595
  needCloseOthers: false
  jumpParam: "30280"
  jumpType: 20
  jumpText: "大王身份专精活动"
}
rows {
  id: 60105
  needCloseOthers: true
  jumpParam: "1;1004;1004002"
  jumpType: 60102
  jumpText: "大王别抓我皮肤-压龙大仙桃花"
}
rows {
  id: 60106
  needCloseOthers: true
  jumpParam: "2;1018;1018002"
  jumpType: 60102
  jumpText: "大王别抓我皮肤-白马摩托独角兽"
}
rows {
  id: 596
  needCloseOthers: false
  jumpParam: "13000045"
  jumpType: 11
  jumpText: "S11狼人卡池1"
}
rows {
  id: 597
  needCloseOthers: false
  jumpParam: "13000046"
  jumpType: 11
  jumpText: "S11狼人卡池2"
}
rows {
  id: 10598
  jumpParam: "30000015;;;;;true"
  jumpType: 11
  jumpText: "1号小甜豆"
}
rows {
  id: 10599
  jumpParam: "30000016;;;;;true"
  jumpType: 11
  jumpText: "2号小甜豆"
}
rows {
  id: 10600
  jumpParam: "30000017;;;;;true"
  jumpType: 11
  jumpText: "3号小甜豆"
}
rows {
  id: 10601
  jumpParam: "30000018;;;;;true"
  jumpType: 11
  jumpText: "4号小甜豆"
}
rows {
  id: 10602
  jumpParam: "30000019;;;;;true"
  jumpType: 11
  jumpText: "5号小甜豆"
}
rows {
  id: 10603
  jumpParam: "30000020;;;;;true"
  jumpType: 11
  jumpText: "6号小甜豆"
}
rows {
  id: 598
  needCloseOthers: false
  jumpParam: "10000030"
  jumpType: 11
  jumpText: "翡光仙灵"
}
rows {
  id: 599
  needCloseOthers: false
  jumpParam: "13000040"
  jumpType: 11
  jumpText: "奶油乐园奇遇"
}
rows {
  id: 10604
  needCloseOthers: false
  jumpParam: "30314"
  jumpType: 20
  jumpText: "跳转到鱼塘幸运星"
}
rows {
  id: 106041
  jumpParam: "30000021;;;;;true"
  jumpType: 11
  jumpText: "7号小甜豆（端盒）"
}
rows {
  id: 1082
  needCloseOthers: false
  jumpParam: "40000016"
  jumpType: 11
  jumpText: "遗落的珍宝（返场卡池）"
}
rows {
  id: 652
  needCloseOthers: false
  jumpType: 652
  jumpText: "跳转到UGC星世界新活动页"
}
rows {
  id: 65201
  needCloseOthers: false
  jumpParam: "{\"id\":\"6572\",\"isUgc\":true,\"isPixUI\":true}"
  jumpType: 652
  jumpText: "跳转到UGC星世界新活动页(参数填目标活动id等信息)"
}
rows {
  id: 642
  needCloseOthers: false
  jumpParam: "10000031"
  jumpType: 11
  jumpText: "S11狼人MVP卡池"
}
rows {
  id: 643
  needCloseOthers: false
  jumpParam: "10000032"
  jumpType: 11
  jumpText: "煎饼超人"
}
rows {
  id: 653
  needCloseOthers: false
  jumpType: 653
  jumpText: "打开UGC星世界BP弹窗"
}
rows {
  id: 654
  needCloseOthers: false
  jumpType: 654
  jumpText: "打开玩法回流页面"
}
rows {
  id: 655
  needCloseOthers: false
  jumpParam: "30326"
  jumpType: 20
  jumpText: "跳转到春日绘彩行"
}
rows {
  id: 656
  needCloseOthers: false
  jumpParam: "30270"
  jumpType: 20
  jumpText: "田园绽芳华"
}
rows {
  id: 657
  jumpParam: "UI_Recharge_BlueOutfit"
  jumpType: 8
  jumpText: "跳转到充值送蓝装"
}
rows {
  id: 50009
  jumpType: 50009
  jumpText: "跳转社团"
}
rows {
  id: 50010
  jumpType: 50010
  jumpText: "跳转收藏"
}
rows {
  id: 50011
  jumpType: 50011
  jumpText: "跳转幸运周末"
}
rows {
  id: 50012
  jumpType: 50012
  jumpText: "跳转充值返利"
}
rows {
  id: 50013
  jumpType: 50013
  jumpText: "跳转摇人有礼"
}
rows {
  id: 50014
  jumpType: 50014
  jumpText: "跳转限时特惠"
}
rows {
  id: 50015
  jumpType: 50015
  jumpText: "限时返利"
}
rows {
  id: 50016
  jumpType: 50016
  jumpText: "VA下载福利"
}
rows {
  id: 50017
  jumpType: 50017
  jumpText: "限时惊喜"
}
rows {
  id: 658
  needCloseOthers: false
  jumpParam: "30328"
  jumpType: 20
  jumpText: "大王三王排位"
}
rows {
  id: 10718
  needCloseOthers: false
  jumpParam: "6000114"
  jumpType: 11
  jumpText: "峡谷幻梦"
}
rows {
  id: 1083
  needCloseOthers: false
  jumpParam: "5210"
  jumpType: 11
  jumpText: "星愿币卡池--黑金鱼"
}
rows {
  id: 659
  needCloseOthers: false
  jumpParam: "13000047"
  jumpType: 11
  jumpText: "满杯蜜桃猫"
}
rows {
  id: 663
  needCloseOthers: false
  jumpType: 663
  jumpText: "打开UGC星世界同时自动打开BP弹窗"
}
rows {
  id: 664
  needCloseOthers: false
  jumpType: 664
  jumpText: "打开UGC星世界，选中推荐页签，同时随机挑一张地图打开详情页"
}
rows {
  id: 50114
  needCloseOthers: false
  jumpType: 50114
  jumpText: "MOBA-峡谷勋章系统主页"
}
rows {
  id: 665
  needCloseOthers: false
  jumpParam: "13000051"
  jumpType: 11
  jumpText: "狼人柯南卡池返场1"
}
rows {
  id: 666
  needCloseOthers: false
  jumpParam: "13000052"
  jumpType: 11
  JumpConditionGroup {
    conditionRelation: ConditionRelation_Not
    condition {
      conditionType: 1501
    }
    tipsKey: "ActivityNeedCommunityScene"
  }
  jumpText: "狼人柯南卡池返场2"
}
rows {
  id: 1084
  needCloseOthers: false
  jumpParam: "5211"
  jumpType: 11
  jumpText: "雨荷仙子"
}
rows {
  id: 1085
  needCloseOthers: false
  jumpParam: "5503"
  jumpType: 11
  jumpText: "浪漫旅程"
}
rows {
  id: 1086
  needCloseOthers: false
  jumpParam: "40000015"
  jumpType: 11
  jumpText: "青霄龙吟"
}
rows {
  id: 662
  needCloseOthers: false
  jumpParam: "30267"
  jumpType: 20
  jumpText: "跳转农场知识星活动"
}
rows {
  id: 667
  needCloseOthers: false
  jumpParam: "UI_Recharge_Werewolf_Promotion"
  jumpType: 8
  jumpText: "狼人满减4期"
}
rows {
  id: 668
  needCloseOthers: false
  jumpParam: "30345"
  jumpType: 20
  jumpText: "农场增益狂欢"
}
rows {
  id: 669
  needCloseOthers: false
  jumpParam: "13000049"
  jumpType: 11
  jumpText: "甜梦嘉年华"
}
rows {
  id: 670
  needCloseOthers: false
  jumpParam: "13000055"
  jumpType: 11
  jumpText: "躲猫猫卡池三期1"
}
rows {
  id: 671
  needCloseOthers: false
  jumpParam: "13000056"
  jumpType: 11
  jumpText: "躲猫猫卡池三期2"
}
rows {
  id: 672
  needCloseOthers: false
  jumpParam: "10000034"
  jumpType: 11
  jumpText: "狼人翻牌卡池四期1"
}
rows {
  id: 673
  needCloseOthers: false
  jumpParam: "10000035"
  jumpType: 11
  jumpText: "狼人翻牌卡池四期2"
}
rows {
  id: 674
  needCloseOthers: false
  jumpParam: "UI_InflateHongBao_MainView"
  jumpType: 8
  jumpText: "膨胀爆红包"
}
rows {
  id: 1087
  needCloseOthers: false
  jumpParam: "UI_SecondChargeRebate_Main"
  jumpType: 8
  jumpText: "S12赛季累充"
}
rows {
  id: 1088
  needCloseOthers: false
  jumpParam: "30000022"
  jumpType: 11
  jumpText: "蜜桃猫"
}
rows {
  id: 675
  needCloseOthers: false
  jumpParam: "30348"
  jumpType: 20
  jumpText: "旅行小狗二期"
}
rows {
  id: 10690
  jumpType: 340
  jumpText: "打开MOBA段位冲刺界面"
}
rows {
  id: 678
  needCloseOthers: false
  jumpParam: "30358"
  jumpType: 20
  jumpText: "跳转到田园好食光"
}
rows {
  id: 253
  needCloseOthers: true
  jumpType: 253
  jumpText: "跳转星梦广场"
}
rows {
  id: 680
  needCloseOthers: false
  jumpParam: "5076"
  jumpType: 302
  jumpText: "跳转四人小队"
}
rows {
  id: 679
  jumpParam: "4;0;10000"
  jumpType: 378
  jumpText: "开始匹配UGC玩法"
}
rows {
  id: 681
  jumpParam: "1;4"
  jumpType: 378
  jumpText: "开始匹配普通玩法"
}
rows {
  id: 676
  needCloseOthers: false
  jumpParam: "30359"
  jumpType: 20
  jumpText: "大王三王限时排位"
}
rows {
  id: 677
  needCloseOthers: false
  jumpParam: "30354"
  jumpType: 20
  jumpText: "大王S12赛季排位奖励"
}
rows {
  id: 682
  needCloseOthers: false
  jumpParam: "10000036"
  jumpType: 11
  jumpText: "S12狼人MVP卡池"
}
rows {
  id: 1089
  needCloseOthers: false
  jumpParam: "5059"
  jumpType: 11
  jumpText: "夏日派对"
}
rows {
  id: 1090
  needCloseOthers: false
  jumpParam: "5060"
  jumpType: 11
  jumpText: "夏日派对"
}
rows {
  id: 1091
  needCloseOthers: false
  jumpParam: "5061"
  jumpType: 11
  jumpText: "摩天乐园"
}
rows {
  id: 1092
  needCloseOthers: false
  jumpParam: "30000025"
  jumpType: 11
  jumpText: "吾皇猫"
}
rows {
  id: 1093
  needCloseOthers: false
  jumpParam: "30000026"
  jumpType: 11
  jumpText: "巴扎黑"
}
rows {
  id: 1094
  needCloseOthers: false
  jumpParam: "50000004"
  jumpType: 11
  jumpText: "LuLu猪"
}
rows {
  id: 683
  needCloseOthers: false
  jumpParam: "13000057"
  jumpType: 11
  jumpText: "S12狼人报告动画卡池"
}
rows {
  id: 684
  needCloseOthers: false
  jumpParam: "30355"
  jumpType: 20
  jumpText: "餐厅星开业"
}
rows {
  id: 685
  needCloseOthers: false
  jumpParam: "30374"
  jumpType: 20
  jumpText: "快乐撕贴纸"
}
rows {
  id: 88888
  needCloseOthers: false
  jumpParam: "53000005"
  jumpType: 11
  jumpText: "幻彩调律祈愿"
}
rows {
  id: 5110
  needCloseOthers: false
  jumpParam: "UI_Recharge_Farm_MainView;UI_MallFarmLogin_MainView"
  jumpType: 5102
  JumpConditionGroup {
    condition {
      conditionType: 164
      value: 3
    }
    tipsKey: "FarmLevelNeed3"
  }
  jumpText: "跳转至天天领二期"
}
rows {
  id: 686
  needCloseOthers: false
  jumpParam: "30377"
  jumpType: 20
  jumpText: "大王飓风来袭"
}
rows {
  id: 687
  jumpParam: "30366"
  jumpType: 20
  jumpText: "组队开红包"
}
rows {
  id: 1096
  needCloseOthers: true
  jumpParam: "30000023"
  jumpType: 11
  jumpText: "卓大王咖啡师"
}
rows {
  id: 1097
  needCloseOthers: false
  jumpParam: "30000024"
  jumpType: 11
  jumpText: "卓大王兔兔"
}
rows {
  id: 689
  needCloseOthers: false
  jumpType: 10505
  jumpText: "狼人热购"
}
rows {
  id: 690
  needCloseOthers: false
  jumpParam: "10000037"
  jumpType: 11
  jumpText: "旅者驿站"
}
rows {
  id: 350
  jumpType: 350
  jumpText: "打开moba定制界面"
}
rows {
  id: 691
  needCloseOthers: false
  jumpParam: "10000038"
  jumpType: 11
  jumpText: "怒海狂鲨"
}
rows {
  id: 692
  needCloseOthers: false
  jumpParam: "13000060"
  jumpType: 11
  jumpText: "S12狼人攻击动画卡池"
}
rows {
  id: 693
  needCloseOthers: false
  jumpParam: "UI_Recharge_Werewolf_Promotion"
  jumpType: 8
  jumpText: "狼人满减4期"
}
rows {
  id: 694
  needCloseOthers: false
  jumpParam: "5308"
  jumpType: 11
  jumpText: "幻猫音境卡池"
}
rows {
  id: 696
  needCloseOthers: false
  jumpParam: "30369"
  jumpType: 20
  jumpText: "农场小队活动"
}
rows {
  id: 697
  needCloseOthers: false
  jumpParam: "30381"
  jumpType: 20
  jumpText: "鱼塘幸运星活动"
}
rows {
  id: 900
  needCloseOthers: false
  jumpParam: "10000039"
  jumpType: 11
  jumpText: "甜心琪琪"
}
rows {
  id: 699
  needCloseOthers: false
  jumpParam: "30393"
  jumpType: 20
  jumpText: "永夜城竞答"
}
rows {
  id: 711
  needCloseOthers: false
  jumpParam: "30394"
  jumpType: 20
  jumpText: "三王排位限时开启"
}
rows {
  id: 8899
  needCloseOthers: false
  jumpParam: "53000006"
  jumpType: 11
  jumpText: "蝶舞花间祈愿"
}
rows {
  id: 713
  jumpParam: "1;5081"
  jumpType: 713
  jumpText: "助威闪电赛跳转"
}
rows {
  id: 688
  needCloseOthers: false
  jumpType: 688
  jumpText: "跳转UGC星世界新匹配Tab同时自动打开某玩法详情页(可选)"
}
rows {
  id: 68801
  needCloseOthers: false
  jumpParam: "{\"autoOpenParam\":{\"MatchId\":22012}}"
  jumpType: 688
  jumpText: "跳转UGC星世界新匹配Tab同时自动打开某玩法详情页(带matchId)"
}
rows {
  id: 710
  needCloseOthers: false
  jumpParam: "10000040"
  jumpType: 11
  jumpText: "拾味桃源"
}
rows {
  id: 7001
  needCloseOthers: false
  jumpParam: "10000041"
  jumpType: 11
  jumpText: "幸运翻翻乐（花样滑冰）"
}
rows {
  id: 7002
  needCloseOthers: false
  jumpParam: "10000042"
  jumpType: 11
  jumpText: "幸运翻翻乐（钓了个鱼）"
}
rows {
  id: 10719
  needCloseOthers: false
  jumpParam: "6000115"
  jumpType: 11
  jumpText: "奇遇舞章"
}
rows {
  id: 70002
  jumpParam: "160810"
  jumpType: 60
  jumpText: "跳转到StarP玩法的月卡"
}
rows {
  id: 1098
  needCloseOthers: false
  jumpParam: "30000027"
  jumpType: 11
  jumpText: "天线宝宝"
}
rows {
  id: 1099
  needCloseOthers: false
  jumpParam: "5213"
  jumpType: 11
  jumpText: "跳转到古风少女"
}
rows {
  id: 718
  needCloseOthers: false
  jumpParam: "30399"
  jumpType: 20
  jumpText: "大王福利周"
}
rows {
  id: 719
  needCloseOthers: true
  jumpParam: "-2029,-206,15,200"
  jumpType: 204
  jumpText: "柯南二期跳转"
}
rows {
  id: 1100
  needCloseOthers: false
  jumpParam: "5212"
  jumpType: 11
  jumpText: "月华鹤影"
}
rows {
  id: 7003
  needCloseOthers: false
  jumpParam: "0"
  jumpType: 234
  jumpText: "跳转到奖杯导航"
}
rows {
  id: 720
  needCloseOthers: false
  jumpParam: "13000062"
  jumpType: 11
  jumpText: "炽光海岸"
}
rows {
  id: 721
  jumpType: 721
  jumpText: "打开亲密好友全部页签"
}
rows {
  id: 722
  needCloseOthers: false
  jumpParam: "13000063"
  jumpType: 11
  jumpText: "天穹圣域"
}
rows {
  id: 723
  jumpParam: "50844425189480783"
  jumpType: 689
  jumpText: "跳转到暑期快闪季的脑王地图"
}
rows {
  id: 724
  jumpParam: "30402"
  jumpType: 20
  jumpText: "跳转到农场暑期BP"
}
rows {
  id: 726
  needCloseOthers: true
  jumpParam: "2101.5,3789.9,63.0,58"
  jumpType: 204
  jumpText: "跳转到草莓音乐节面前"
}
rows {
  id: 727
  jumpType: 727
  jumpText: "跳转痛包编辑页"
}
rows {
  id: 728
  needCloseOthers: false
  jumpParam: "30420"
  jumpType: 20
  jumpText: "大王S13排位赛"
}

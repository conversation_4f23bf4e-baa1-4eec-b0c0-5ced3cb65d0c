com.tencent.wea.xlsRes.table_RaffleRewardCfgData
excel/xls/C_抽奖奖池_特色玩法.xlsx sheet:奖励
rows {
  rewardId: 9001001
  poolId: 9001
  name: "舞台剧动画*1"
  itemId: 240002
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 9001002
  poolId: 9001
  name: "鹊桥*5"
  itemId: 240407
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 9001003
  poolId: 9001
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 9001004
  poolId: 9001
  name: "送花*10"
  itemId: 240404
  itemNum: 10
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 9001005
  poolId: 9001
  name: "鸡蛋*10"
  itemId: 240401
  itemNum: 10
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9001006
  poolId: 9001
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9001007
  poolId: 9001
  name: "天使头像框*1"
  itemId: 840506
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9001008
  poolId: 9001
  name: "会议表情-无奈"
  itemId: 240607
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 9001009
  poolId: 9001
  name: "阵营卡*5"
  itemId: 200101
  itemNum: 5
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 9002001
  poolId: 9002
  name: "变青蛙动画*1"
  itemId: 240205
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 9002002
  poolId: 9002
  name: "戒指*5"
  itemId: 240408
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 9002003
  poolId: 9002
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 9002004
  poolId: 9002
  name: "拳头*10"
  itemId: 240402
  itemNum: 10
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 9002005
  poolId: 9002
  name: "爱心*10"
  itemId: 240403
  itemNum: 10
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9002006
  poolId: 9002
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9002007
  poolId: 9002
  name: "天使昵称框*1"
  itemId: 820506
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9002008
  poolId: 9002
  name: "阵营卡*5"
  itemId: 200101
  itemNum: 5
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 9002009
  poolId: 9002
  name: "天使头像*1"
  itemId: 860506
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000001
  poolId: 10000001
  name: "配饰-小黄鸭背包"
  itemId: 620408
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000002
  poolId: 10000001
  name: "星宝印章"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000003
  poolId: 10000001
  name: "服装染色剂"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 10000004
  poolId: 10000001
  name: "甜兔屋"
  itemId: 218103
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000005
  poolId: 10000001
  name: "配饰-农场币"
  itemId: 630280
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000006
  poolId: 10000001
  name: "动作-爱意传递"
  itemId: 720719
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000007
  poolId: 10000001
  name: "稻草人汉克"
  itemId: 218102
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000008
  poolId: 10000001
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 10000009
  poolId: 10000001
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000010
  poolId: 13000000
  name: "HelloKitty"
  itemId: 402420
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000011
  poolId: 13000000
  name: "红蝶结包包"
  itemId: 620376
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 10000012
  poolId: 13000000
  name: "萌萌护眼"
  itemId: 610180
  itemNum: 1
  groupId: 2
  weight: 50
  limit: 1
}
rows {
  rewardId: 10000013
  poolId: 13000000
  name: "粉粉仙女棒"
  itemId: 640022
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000014
  poolId: 13000001
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000015
  poolId: 13000001
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000016
  poolId: 13000001
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000017
  poolId: 13000001
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000018
  poolId: 13000001
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000019
  poolId: 13000001
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000020
  poolId: 13000001
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000021
  poolId: 13000002
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000022
  poolId: 13000002
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000023
  poolId: 13000002
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000024
  poolId: 13000002
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000025
  poolId: 13000002
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000026
  poolId: 13000002
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000027
  poolId: 13000002
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000028
  poolId: 13000003
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000029
  poolId: 13000003
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000030
  poolId: 13000003
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000031
  poolId: 13000003
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000032
  poolId: 13000003
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000033
  poolId: 13000003
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000034
  poolId: 13000003
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000035
  poolId: 13000004
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000036
  poolId: 13000004
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000037
  poolId: 13000004
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000038
  poolId: 13000004
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000039
  poolId: 13000004
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000040
  poolId: 13000004
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000041
  poolId: 13000004
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000042
  poolId: 13000005
  name: "酷洛米"
  itemId: 402430
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000043
  poolId: 13000005
  name: "甜酷包包"
  itemId: 620372
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 10000044
  poolId: 13000005
  name: "幻彩魅梦"
  itemId: 630244
  itemNum: 1
  groupId: 2
  weight: 50
  limit: 1
}
rows {
  rewardId: 10000045
  poolId: 13000005
  name: "幻想星愿"
  itemId: 640017
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000046
  poolId: 13000006
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000047
  poolId: 13000006
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000048
  poolId: 13000006
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000049
  poolId: 13000006
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000050
  poolId: 13000006
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000051
  poolId: 13000006
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000052
  poolId: 13000006
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000053
  poolId: 13000007
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000054
  poolId: 13000007
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000055
  poolId: 13000007
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000056
  poolId: 13000007
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000057
  poolId: 13000007
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000058
  poolId: 13000007
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000059
  poolId: 13000007
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000060
  poolId: 13000008
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000061
  poolId: 13000008
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000062
  poolId: 13000008
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000063
  poolId: 13000008
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000064
  poolId: 13000008
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000065
  poolId: 13000008
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000066
  poolId: 13000008
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000067
  poolId: 13000009
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000068
  poolId: 13000009
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000069
  poolId: 13000009
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000070
  poolId: 13000009
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000071
  poolId: 13000009
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000072
  poolId: 13000009
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000073
  poolId: 13000009
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000074
  poolId: 13000010
  name: "洋葱头蔬果屋"
  itemId: 218171
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000075
  poolId: 13000010
  name: "永不空军"
  itemId: 630298
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 10000076
  poolId: 13000011
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000077
  poolId: 13000011
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000078
  poolId: 13000011
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000079
  poolId: 13000011
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000080
  poolId: 13000011
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000081
  poolId: 13000011
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000082
  poolId: 13000011
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000083
  poolId: 13000012
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000084
  poolId: 13000012
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000085
  poolId: 13000012
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000086
  poolId: 13000012
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000087
  poolId: 13000012
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000088
  poolId: 13000012
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000089
  poolId: 13000012
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000090
  poolId: 13000013
  name: "牛牛牧场小店"
  itemId: 218124
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000091
  poolId: 13000013
  name: "种菜专家"
  itemId: 630299
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 10000092
  poolId: 13000014
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000093
  poolId: 13000014
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000094
  poolId: 13000014
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000095
  poolId: 13000014
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000096
  poolId: 13000014
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000097
  poolId: 13000014
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000098
  poolId: 13000014
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000099
  poolId: 13000015
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000100
  poolId: 13000015
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000101
  poolId: 13000015
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000102
  poolId: 13000015
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000103
  poolId: 13000015
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000104
  poolId: 13000015
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 10000105
  poolId: 13000015
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 9003001
  poolId: 9003
  name: "舞台剧动画*1"
  itemId: 240002
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 9003002
  poolId: 9003
  name: "鹊桥*5"
  itemId: 240407
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 9003003
  poolId: 9003
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 9003004
  poolId: 9003
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9003005
  poolId: 9003
  name: "阵营卡*5"
  itemId: 200101
  itemNum: 5
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 9003006
  poolId: 9003
  name: "天使昵称框*1"
  itemId: 820506
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 9003007
  poolId: 9003
  name: "送花*10"
  itemId: 240404
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 9003008
  poolId: 9003
  name: "天使头像*1"
  itemId: 860506
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9003009
  poolId: 9003
  name: "鸡蛋*10"
  itemId: 240401
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 9004001
  poolId: 9004
  name: "变青蛙动画*1"
  itemId: 240205
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 9004002
  poolId: 9004
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 9004003
  poolId: 9004
  name: "阵营卡*5"
  itemId: 200101
  itemNum: 5
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 9004004
  poolId: 9004
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9004005
  poolId: 9004
  name: "天使头像框*1"
  itemId: 840506
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 9004006
  poolId: 9004
  name: "会议表情-无奈"
  itemId: 240607
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 9004007
  poolId: 9004
  name: "戒指*5"
  itemId: 240408
  itemNum: 5
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9004008
  poolId: 9004
  name: "拳头*10"
  itemId: 240402
  itemNum: 10
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 9004009
  poolId: 9004
  name: "爱心*10"
  itemId: 240403
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 130010001
  poolId: 10001001
  name: "冲锋鲨鲨"
  itemId: 620493
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130010002
  poolId: 10001001
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130010003
  poolId: 10001001
  name: "星宝印章"
  itemId: 4
  itemNum: 1000
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130010004
  poolId: 10001002
  name: "沙沙渔获"
  itemId: 218110
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130010005
  poolId: 10001002
  name: "配饰染色剂"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130010006
  poolId: 10001002
  name: "服装染色剂"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130010007
  poolId: 10001002
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130010008
  poolId: 10001002
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130010009
  poolId: 10001003
  name: "丰收兔"
  itemId: 218107
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130010010
  poolId: 10001003
  name: "菜园猎手"
  itemId: 630347
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 130010011
  poolId: 10001003
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130010012
  poolId: 10001003
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 15
  limit: 1
}
rows {
  rewardId: 130010013
  poolId: 10001003
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 30
  limit: 1
}
rows {
  rewardId: 130010014
  poolId: 10001003
  name: "云朵币"
  itemId: 6
  itemNum: 30
  groupId: 3
  weight: 20
  limit: 1
}
rows {
  rewardId: 130010015
  poolId: 10001003
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 50
  limit: 1
}
rows {
  rewardId: 130001200
  poolId: 13000120
  name: "刺客狼头像框一套"
  itemId: 310255
  itemNum: 1
  groupId: 2
  weight: 19
  limit: 1
}
rows {
  rewardId: 130001201
  poolId: 13000120
  name: "滑板涂鸦-大奖"
  itemId: 240005
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001202
  poolId: 13000120
  name: "别说了一套表情"
  itemId: 310257
  itemNum: 1
  groupId: 1
  weight: 80
  limit: 1
}
rows {
  rewardId: 130001203
  poolId: 13000121
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001204
  poolId: 13000121
  name: "幸运钥匙"
  groupId: 3
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001205
  poolId: 13000121
  name: "小猪撞击*3"
  itemId: 240410
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001206
  poolId: 13000121
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001207
  poolId: 13000121
  name: "戒指*5"
  itemId: 240408
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001208
  poolId: 13000121
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001209
  poolId: 13000121
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001210
  poolId: 13000122
  name: "狼人币*150"
  itemId: 13
  itemNum: 150
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001211
  poolId: 13000122
  name: "幸运钥匙"
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001212
  poolId: 13000122
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001213
  poolId: 13000122
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001214
  poolId: 13000122
  name: "打call*5"
  itemId: 240413
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001215
  poolId: 13000122
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001216
  poolId: 13000122
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001217
  poolId: 13000123
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001218
  poolId: 13000123
  name: "幸运钥匙"
  groupId: 3
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001219
  poolId: 13000123
  name: "永恒之恋*2"
  itemId: 240409
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001220
  poolId: 13000123
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001221
  poolId: 13000123
  name: "拳头*5"
  itemId: 240402
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001222
  poolId: 13000123
  name: "炸弹*10"
  itemId: 240405
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001223
  poolId: 13000123
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001224
  poolId: 13000130
  name: "魔爆狼头像框一套"
  itemId: 310256
  itemNum: 1
  groupId: 2
  weight: 19
  limit: 1
}
rows {
  rewardId: 130001225
  poolId: 13000130
  name: "闪烁突袭"
  itemId: 240206
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001226
  poolId: 13000130
  name: "极力否认 一套表情"
  itemId: 310258
  itemNum: 1
  groupId: 1
  weight: 80
  limit: 1
}
rows {
  rewardId: 130001227
  poolId: 13000131
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001228
  poolId: 13000131
  name: "幸运钥匙"
  groupId: 3
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001229
  poolId: 13000131
  name: "鹊桥*3"
  itemId: 240407
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001230
  poolId: 13000131
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001231
  poolId: 13000131
  name: "打call*5"
  itemId: 240413
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001232
  poolId: 13000131
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001233
  poolId: 13000131
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001234
  poolId: 13000132
  name: "狼人币*150"
  itemId: 13
  itemNum: 150
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001235
  poolId: 13000132
  name: "幸运钥匙"
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001236
  poolId: 13000132
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001237
  poolId: 13000132
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001238
  poolId: 13000132
  name: "戒指*5"
  itemId: 240408
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001239
  poolId: 13000132
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001240
  poolId: 13000132
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001241
  poolId: 13000133
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001242
  poolId: 13000133
  name: "幸运钥匙"
  groupId: 3
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001243
  poolId: 13000133
  name: "小猪撞击*3"
  itemId: 240410
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001244
  poolId: 13000133
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001245
  poolId: 13000133
  name: "干杯*10"
  itemId: 240412
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001246
  poolId: 13000133
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001247
  poolId: 13000133
  name: "送花"
  itemId: 240404
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 600010101
  poolId: 6000101
  name: "偶像歌手 王昭君"
  itemId: 403640
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010102
  poolId: 6000101
  name: "记忆之芯 公孙离"
  itemId: 403650
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010103
  poolId: 6000101
  name: "心动热麦"
  itemId: 620495
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010104
  poolId: 6000101
  name: "琳琅千机"
  itemId: 620494
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010105
  poolId: 6000101
  name: "初次公演"
  itemId: 720777
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010106
  poolId: 6000101
  name: "灵动之舞"
  itemId: 720776
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010107
  poolId: 6000101
  name: "魅力偶像"
  itemId: 711137
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010108
  poolId: 6000101
  name: "程序启动"
  itemId: 711132
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010109
  poolId: 6000101
  name: "峡谷券*5"
  itemId: 218
  itemNum: 5
  groupId: 5
  weight: 4
  comboItemIds: 320063
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010110
  poolId: 6000101
  name: "峡谷券*3"
  itemId: 218
  itemNum: 3
  groupId: 5
  weight: 11
  comboItemIds: 320062
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010111
  poolId: 6000101
  name: "峡谷券*2"
  itemId: 218
  itemNum: 2
  groupId: 5
  weight: 85
  comboItemIds: 320061
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010112
  poolId: 6000101
  name: "峡谷币*10"
  itemId: 3541
  itemNum: 10
  groupId: 6
  weight: 1
  limit: 20
}
rows {
  rewardId: 600010113
  poolId: 6000101
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010114
  poolId: 6000101
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010115
  poolId: 6000101
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010116
  poolId: 6000101
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010117
  poolId: 6000101
  name: "龙胆现世"
  itemId: 720733
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 600010118
  poolId: 6000101
  name: "追逃游戏"
  itemId: 720735
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 600010119
  poolId: 6000101
  name: "龙魂再起"
  itemId: 711062
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 600010120
  poolId: 6000101
  name: "古灵精怪"
  itemId: 711061
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 600010121
  poolId: 6000101
  name: "心动节拍"
  itemId: 720680
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 600010122
  poolId: 6000101
  name: "天翔影龙"
  itemId: 720681
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 600010123
  poolId: 6000101
  name: "小乔头像"
  itemId: 860079
  itemNum: 1
  groupId: 7
  weight: 2
  limit: 1
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 600010124
  poolId: 6000101
  name: "兰陵王头像"
  itemId: 860080
  itemNum: 1
  groupId: 7
  weight: 2
  limit: 1
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 10000106
  poolId: 10000005
  name: "配饰-小黄鸭背包"
  itemId: 620408
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000107
  poolId: 10000005
  name: "星宝印章"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000108
  poolId: 10000005
  name: "服装染色剂"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 10000109
  poolId: 10000005
  name: "甜兔屋"
  itemId: 218103
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000110
  poolId: 10000005
  name: "配饰-农场币"
  itemId: 630280
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000111
  poolId: 10000005
  name: "动作-爱意传递"
  itemId: 720719
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000112
  poolId: 10000005
  name: "稻草人汉克"
  itemId: 218102
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000113
  poolId: 10000005
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 10000114
  poolId: 10000005
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 600010201
  poolId: 6000102
  name: "异界灵契 孙尚香"
  itemId: 403000
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010202
  poolId: 6000102
  name: "挚爱之约 孙策"
  itemId: 402120
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010203
  poolId: 6000102
  name: "异界旅伴"
  itemId: 620454
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010204
  poolId: 6000102
  name: "挚爱之锚"
  itemId: 620279
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010205
  poolId: 6000102
  name: "异界灵契"
  itemId: 720819
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010206
  poolId: 6000102
  name: "挚爱之约"
  itemId: 720758
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010207
  poolId: 6000102
  name: "轻松解决"
  itemId: 711074
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010208
  poolId: 6000102
  name: "为爱出发"
  itemId: 711138
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010209
  poolId: 6000102
  name: "峡谷券*5"
  itemId: 218
  itemNum: 5
  groupId: 5
  weight: 4
  comboItemIds: 320063
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010210
  poolId: 6000102
  name: "峡谷券*3"
  itemId: 218
  itemNum: 3
  groupId: 5
  weight: 11
  comboItemIds: 320062
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010211
  poolId: 6000102
  name: "峡谷券*2"
  itemId: 218
  itemNum: 2
  groupId: 5
  weight: 85
  comboItemIds: 320061
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010212
  poolId: 6000102
  name: "峡谷币*10"
  itemId: 3541
  itemNum: 10
  groupId: 6
  weight: 1
  limit: 20
}
rows {
  rewardId: 600010213
  poolId: 6000102
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010214
  poolId: 6000102
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010215
  poolId: 6000102
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010216
  poolId: 6000102
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010217
  poolId: 6000102
  name: "龙胆现世"
  itemId: 720733
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010218
  poolId: 6000102
  name: "追逃游戏"
  itemId: 720735
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010219
  poolId: 6000102
  name: "龙魂再起"
  itemId: 711062
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010220
  poolId: 6000102
  name: "古灵精怪"
  itemId: 711061
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010221
  poolId: 6000102
  name: "心动节拍"
  itemId: 720680
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010222
  poolId: 6000102
  name: "天翔影龙"
  itemId: 720681
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010223
  poolId: 6000102
  name: "小乔头像"
  itemId: 860079
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010224
  poolId: 6000102
  name: "兰陵王头像"
  itemId: 860080
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010225
  poolId: 6000102
  name: "初次公演"
  itemId: 720777
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010226
  poolId: 6000102
  name: "灵动之舞"
  itemId: 720776
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010227
  poolId: 6000102
  name: "魅力偶像"
  itemId: 711137
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 600010228
  poolId: 6000102
  name: "程序启动"
  itemId: 711132
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 12
    limit: 1
  }
}
rows {
  rewardId: 10000115
  poolId: 10000006
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000116
  poolId: 10000006
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000117
  poolId: 10000006
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 10000118
  poolId: 10000006
  name: "丰收浇灌者"
  itemId: 630343
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000119
  poolId: 10000006
  name: "仙人掌花屋"
  itemId: 218116
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 10000120
  poolId: 10000006
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000121
  poolId: 10000006
  name: "精品磷虾"
  itemId: 219000
  itemNum: 4
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 10000122
  poolId: 10000006
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 10000123
  poolId: 10000006
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100701
  poolId: 10001007
  name: "变青蛙动画*1"
  itemId: 240205
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1000100702
  poolId: 10001007
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100703
  poolId: 10001007
  name: "阵营卡*5"
  itemId: 200101
  itemNum: 5
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100704
  poolId: 10001007
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100705
  poolId: 10001007
  name: "天使头像框*1"
  itemId: 840506
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100706
  poolId: 10001007
  name: "会议表情-无奈"
  itemId: 240607
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100707
  poolId: 10001007
  name: "拳头*10"
  itemId: 240402
  itemNum: 10
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100708
  poolId: 10001007
  name: "干杯*10"
  itemId: 240412
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100709
  poolId: 10001007
  name: "爱心*10"
  itemId: 240403
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100710
  poolId: 10001008
  name: "舞台剧动画*1"
  itemId: 240002
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1000100711
  poolId: 10001008
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100712
  poolId: 10001008
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100713
  poolId: 10001008
  name: "阵营卡*5"
  itemId: 200101
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100714
  poolId: 10001008
  name: "天使头像*1"
  itemId: 860506
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100715
  poolId: 10001008
  name: "天使昵称框*1"
  itemId: 820506
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100716
  poolId: 10001008
  name: "送花*10"
  itemId: 240404
  itemNum: 10
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100717
  poolId: 10001008
  name: "干杯*10"
  itemId: 240412
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000100718
  poolId: 10001008
  name: "鸡蛋*10"
  itemId: 240401
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001031
  poolId: 60001031
  name: "乘风破浪 夏侯惇"
  itemId: 404450
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001032
  poolId: 60001031
  name: "大白鲨"
  itemId: 620632
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001033
  poolId: 60001031
  name: "钓鲨鱼"
  itemId: 720876
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001034
  poolId: 60001031
  name: "浪尖冲刺"
  itemId: 711245
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001035
  poolId: 60001031
  name: "乘风破浪"
  itemId: 861009
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001036
  poolId: 60001031
  name: "峡谷币*80"
  itemId: 3541
  itemNum: 80
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001037
  poolId: 60001031
  name: "夏侯惇头像"
  itemId: 860141
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001038
  poolId: 60001031
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001039
  poolId: 60001031
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001040
  poolId: 60001032
  name: "缤纷绘卷 张良"
  itemId: 404460
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001041
  poolId: 60001032
  name: "学霸挎包"
  itemId: 620631
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001042
  poolId: 60001032
  name: "参透天书"
  itemId: 720875
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001043
  poolId: 60001032
  name: "沉浸书香"
  itemId: 711244
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001044
  poolId: 60001032
  name: "缤纷绘卷"
  itemId: 861010
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001045
  poolId: 60001032
  name: "峡谷币*80"
  itemId: 3541
  itemNum: 80
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001046
  poolId: 60001032
  name: "张良头像"
  itemId: 860142
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001047
  poolId: 60001032
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001048
  poolId: 60001032
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 130001730
  poolId: 13000173
  name: "狼人尊贵大卡包*1"
  itemId: 310265
  itemNum: 1
  groupId: 2
  weight: 24
  limit: 1
}
rows {
  rewardId: 130001731
  poolId: 13000173
  name: "魔法扫帚-大奖"
  itemId: 240009
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001732
  poolId: 13000173
  name: "伪装狼三件套"
  itemId: 310266
  itemNum: 1
  groupId: 3
  weight: 75
  limit: 1
}
rows {
  rewardId: 130001733
  poolId: 13000174
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001734
  poolId: 13000174
  name: "乐园门票"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001735
  poolId: 13000174
  name: "丢鸡蛋*10"
  itemId: 240401
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001736
  poolId: 13000175
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001737
  poolId: 13000175
  name: "三选2选旧表情*1"
  itemId: 330079
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001738
  poolId: 13000175
  name: "乐园门票"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001739
  poolId: 13000175
  name: "阵营卡*5"
  itemId: 200101
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001740
  poolId: 13000175
  name: "拳击*10"
  itemId: 240402
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001741
  poolId: 13000176
  name: "头饰2选1宝箱"
  itemId: 330080
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001742
  poolId: 13000176
  name: "花好月圆*4"
  itemId: 240415
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001743
  poolId: 13000176
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001744
  poolId: 13000176
  name: "乐园门票"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001745
  poolId: 13000176
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001746
  poolId: 13000176
  name: "贴符*10"
  itemId: 240417
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001747
  poolId: 13000176
  name: "身份卡*3"
  itemId: 200102
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001770
  poolId: 13000177
  name: "庆祝"
  itemId: 240627
  itemNum: 1
  groupId: 3
  weight: 24
  limit: 1
}
rows {
  rewardId: 130001771
  poolId: 13000177
  name: "爱心冲击"
  itemId: 240207
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001772
  poolId: 13000177
  name: "狼人尊贵大卡包*1"
  itemId: 310265
  itemNum: 1
  groupId: 2
  weight: 75
  limit: 1
}
rows {
  rewardId: 130001773
  poolId: 13000178
  name: "狼人币*200"
  itemId: 13
  itemNum: 100
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001774
  poolId: 13000178
  name: "乐园门票"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001775
  poolId: 13000178
  name: "炸弹*10"
  itemId: 240405
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001776
  poolId: 13000179
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001777
  poolId: 13000179
  name: "三选2选旧表情*1"
  itemId: 330081
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001778
  poolId: 13000179
  name: "乐园门票"
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001779
  poolId: 13000179
  name: "暗影凝视饰品*1"
  itemId: 610253
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001780
  poolId: 13000179
  name: "鹊桥*8"
  itemId: 240407
  itemNum: 8
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001781
  poolId: 13000180
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001782
  poolId: 13000180
  name: "送花*10"
  itemId: 240404
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001783
  poolId: 13000180
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001784
  poolId: 13000180
  name: "乐园门票"
  groupId: 1
  weight: 6
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001785
  poolId: 13000180
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001786
  poolId: 13000180
  name: "祝福*10"
  itemId: 240419
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001787
  poolId: 13000180
  name: "身份卡*3"
  itemId: 200102
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 600010401
  poolId: 6000104
  name: "全息碎影 孙悟空"
  itemId: 403020
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010402
  poolId: 6000104
  name: "超时空战士 狄仁杰"
  itemId: 404140
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010403
  poolId: 6000104
  name: "全息灵庙"
  itemId: 620513
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010404
  poolId: 6000104
  name: "脉冲饰带"
  itemId: 620563
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010405
  poolId: 6000104
  name: "赛博行者"
  itemId: 720834
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010406
  poolId: 6000104
  name: "超时空追击"
  itemId: 720825
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010407
  poolId: 6000104
  name: "不耐烦"
  itemId: 711140
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010408
  poolId: 6000104
  name: "正义制裁"
  itemId: 711139
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010409
  poolId: 6000104
  name: "峡谷券*5"
  itemId: 218
  itemNum: 5
  groupId: 5
  weight: 4
  comboItemIds: 320063
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010410
  poolId: 6000104
  name: "峡谷券*3"
  itemId: 218
  itemNum: 3
  groupId: 5
  weight: 11
  comboItemIds: 320062
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010411
  poolId: 6000104
  name: "峡谷券*2"
  itemId: 218
  itemNum: 2
  groupId: 5
  weight: 85
  comboItemIds: 320061
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010412
  poolId: 6000104
  name: "峡谷币*10"
  itemId: 3541
  itemNum: 10
  groupId: 6
  weight: 1
  limit: 20
}
rows {
  rewardId: 600010413
  poolId: 6000104
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010414
  poolId: 6000104
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010415
  poolId: 6000104
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010416
  poolId: 6000104
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010417
  poolId: 6000104
  name: "异界灵契"
  itemId: 720819
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010418
  poolId: 6000104
  name: "挚爱之约"
  itemId: 720758
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010419
  poolId: 6000104
  name: "初次公演"
  itemId: 720777
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010420
  poolId: 6000104
  name: "灵动之舞"
  itemId: 720776
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010421
  poolId: 6000104
  name: "龙胆现世"
  itemId: 720733
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010422
  poolId: 6000104
  name: "追逃游戏"
  itemId: 720735
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010423
  poolId: 6000104
  name: "心动节拍"
  itemId: 720680
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010424
  poolId: 6000104
  name: "天翔影龙"
  itemId: 720681
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010425
  poolId: 6000104
  name: "轻松解决"
  itemId: 711074
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010426
  poolId: 6000104
  name: "为爱出发"
  itemId: 711138
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010427
  poolId: 6000104
  name: "魅力偶像"
  itemId: 711137
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010428
  poolId: 6000104
  name: "程序启动"
  itemId: 711132
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010429
  poolId: 6000104
  name: "龙魂再起"
  itemId: 711062
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010430
  poolId: 6000104
  name: "古灵精怪"
  itemId: 711061
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010431
  poolId: 6000104
  name: "小乔头像"
  itemId: 860079
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 600010432
  poolId: 6000104
  name: "兰陵王头像"
  itemId: 860080
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 16
    limit: 1
  }
}
rows {
  rewardId: 100000091
  poolId: 10000009
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000092
  poolId: 10000009
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000093
  poolId: 10000009
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000094
  poolId: 10000009
  name: "嘟嘟河马"
  itemId: 620556
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000095
  poolId: 10000009
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 35
  limit: 1
}
rows {
  rewardId: 100000096
  poolId: 10000009
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 50
  limit: 1
}
rows {
  rewardId: 100000097
  poolId: 10000009
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000098
  poolId: 10000009
  name: "花蔓时钟"
  itemId: 218122
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000099
  poolId: 10000009
  name: "狐仙"
  itemId: 218112
  itemNum: 1
  groupId: 4
  weight: 15
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001601
  poolId: 13000160
  name: "告白熊梦幻屋"
  itemId: 218115
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001602
  poolId: 13000160
  name: "蔷薇花车"
  itemId: 218119
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001603
  poolId: 13000160
  name: "彩虹牧场"
  itemId: 218120
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001604
  poolId: 13000160
  name: "梦幻海洋屋"
  itemId: 218121
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001605
  poolId: 13000160
  name: "云朵奶油时钟"
  itemId: 218118
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001606
  poolId: 13000160
  name: "农田小飞侠"
  itemId: 630339
  itemNum: 1
  groupId: 6
  weight: 30
  limit: 1
}
rows {
  rewardId: 130001607
  poolId: 13000160
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001608
  poolId: 13000160
  name: "幸运币*15"
  itemId: 3
  itemNum: 15
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001609
  poolId: 13000160
  name: "小熊趴趴背包"
  itemId: 620461
  itemNum: 1
  groupId: 6
  weight: 70
  limit: 1
}
rows {
  rewardId: 130001610
  poolId: 13000160
  name: "幸运币*5"
  itemId: 3
  itemNum: 5
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001611
  poolId: 13000160
  name: "精品磷虾*8"
  itemId: 219000
  itemNum: 8
  groupId: 5
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001612
  poolId: 13000160
  name: "精品磷虾*16"
  itemId: 219000
  itemNum: 16
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001613
  poolId: 13000161
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001614
  poolId: 13000161
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001615
  poolId: 13000161
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001616
  poolId: 13000161
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001617
  poolId: 13000161
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001618
  poolId: 13000162
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001619
  poolId: 13000162
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001620
  poolId: 13000162
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001621
  poolId: 13000162
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001622
  poolId: 13000162
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001623
  poolId: 13000163
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001624
  poolId: 13000163
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001625
  poolId: 13000163
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001626
  poolId: 13000163
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001627
  poolId: 13000163
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001628
  poolId: 13000164
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001629
  poolId: 13000164
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001630
  poolId: 13000164
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001631
  poolId: 13000164
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001632
  poolId: 13000164
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001633
  poolId: 13000165
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001634
  poolId: 13000165
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001635
  poolId: 13000165
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001636
  poolId: 13000165
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001637
  poolId: 13000165
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001638
  poolId: 13000166
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001639
  poolId: 13000166
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001640
  poolId: 13000166
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001641
  poolId: 13000166
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001642
  poolId: 13000166
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001643
  poolId: 13000167
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001644
  poolId: 13000167
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001645
  poolId: 13000167
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001646
  poolId: 13000167
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001647
  poolId: 13000167
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001648
  poolId: 13000168
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001649
  poolId: 13000168
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001650
  poolId: 13000168
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001651
  poolId: 13000168
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001652
  poolId: 13000168
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001653
  poolId: 13000169
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001654
  poolId: 13000169
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001655
  poolId: 13000169
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001656
  poolId: 13000169
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001657
  poolId: 13000169
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001658
  poolId: 13000170
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001659
  poolId: 13000170
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001660
  poolId: 13000170
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001661
  poolId: 13000170
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001662
  poolId: 13000170
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001663
  poolId: 13000171
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001664
  poolId: 13000171
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001665
  poolId: 13000171
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001666
  poolId: 13000171
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001667
  poolId: 13000171
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001668
  poolId: 13000172
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001669
  poolId: 13000172
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001670
  poolId: 13000172
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001671
  poolId: 13000172
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001672
  poolId: 13000172
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130001673
  poolId: 13000020
  name: "梦幻熊礼盒"
  itemId: 218130
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
}
rows {
  rewardId: 130001674
  poolId: 13000020
  name: "招财喵"
  itemId: 218117
  itemNum: 1
  groupId: 1
  weight: 20
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001675
  poolId: 13000020
  name: "海底奇遇包"
  itemId: 620543
  itemNum: 1
  groupId: 2
  weight: 35
  limit: 1
}
rows {
  rewardId: 130001676
  poolId: 13000021
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001677
  poolId: 13000021
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001678
  poolId: 13000021
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001679
  poolId: 13000021
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001680
  poolId: 13000021
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001681
  poolId: 13000021
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001682
  poolId: 13000021
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001683
  poolId: 13000021
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001684
  poolId: 13000022
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001685
  poolId: 13000022
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001686
  poolId: 13000022
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001687
  poolId: 13000022
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001688
  poolId: 13000022
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001689
  poolId: 13000022
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001690
  poolId: 13000022
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001691
  poolId: 13000022
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001692
  poolId: 13000023
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001693
  poolId: 13000023
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001694
  poolId: 13000023
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001695
  poolId: 13000023
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001696
  poolId: 13000023
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001697
  poolId: 13000023
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001698
  poolId: 13000023
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001699
  poolId: 13000023
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002101
  poolId: 13000210
  name: "永恒誓言"
  itemId: 241204
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 130002102
  poolId: 13000210
  name: "雷霆之怒"
  itemId: 241402
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002103
  poolId: 13000210
  name: "麻萤之眼"
  itemId: 610289
  itemNum: 1
  groupId: 2
  weight: 30
  limit: 1
}
rows {
  rewardId: 130002104
  poolId: 13000210
  name: "搜捕者装饰礼盒"
  itemId: 310278
  itemNum: 1
  groupId: 2
  weight: 150
  limit: 1
}
rows {
  rewardId: 130002105
  poolId: 13000211
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002106
  poolId: 13000211
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002107
  poolId: 13000211
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002108
  poolId: 13000211
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002109
  poolId: 13000211
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002110
  poolId: 13000211
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002111
  poolId: 13000211
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002112
  poolId: 13000212
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002113
  poolId: 13000212
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002114
  poolId: 13000212
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002115
  poolId: 13000212
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002116
  poolId: 13000212
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002117
  poolId: 13000212
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002118
  poolId: 13000212
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002119
  poolId: 13000213
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002120
  poolId: 13000213
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002121
  poolId: 13000213
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002122
  poolId: 13000213
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002123
  poolId: 13000213
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002124
  poolId: 13000213
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002125
  poolId: 13000213
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002126
  poolId: 13000214
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002127
  poolId: 13000214
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002128
  poolId: 13000214
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002129
  poolId: 13000214
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002130
  poolId: 13000214
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002131
  poolId: 13000214
  name: "西瓜头饰"
  itemId: 630297
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002132
  poolId: 13000214
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002201
  poolId: 13000220
  name: "繁花"
  itemId: 241202
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 130002202
  poolId: 13000220
  name: "彩带飘飘"
  itemId: 241403
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002203
  poolId: 13000220
  name: "精萤之翼"
  itemId: 620636
  itemNum: 1
  groupId: 2
  weight: 30
  limit: 1
}
rows {
  rewardId: 130002204
  poolId: 13000220
  name: "伪装者装饰礼盒"
  itemId: 310279
  itemNum: 1
  groupId: 2
  weight: 150
  limit: 1
}
rows {
  rewardId: 130002205
  poolId: 13000221
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002206
  poolId: 13000221
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002207
  poolId: 13000221
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002208
  poolId: 13000221
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002209
  poolId: 13000221
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002210
  poolId: 13000221
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002211
  poolId: 13000221
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002212
  poolId: 13000222
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002213
  poolId: 13000222
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002214
  poolId: 13000222
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002215
  poolId: 13000222
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002216
  poolId: 13000222
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002217
  poolId: 13000222
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002218
  poolId: 13000222
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002219
  poolId: 13000223
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002220
  poolId: 13000223
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002221
  poolId: 13000223
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002222
  poolId: 13000223
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002223
  poolId: 13000223
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002224
  poolId: 13000223
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002225
  poolId: 13000223
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002226
  poolId: 13000224
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002227
  poolId: 13000224
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002228
  poolId: 13000224
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002229
  poolId: 13000224
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002230
  poolId: 13000224
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002231
  poolId: 13000224
  name: "指南针头饰"
  itemId: 630257
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002232
  poolId: 13000224
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002901
  poolId: 13000290
  name: "伪装者特效：恭喜发财"
  itemId: 241205
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 130002902
  poolId: 13000290
  name: "璀璨焰火"
  itemId: 241405
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002903
  poolId: 13000290
  name: "飞人篮筐"
  itemId: 620708
  itemNum: 1
  groupId: 2
  weight: 30
  limit: 1
}
rows {
  rewardId: 130002904
  poolId: 13000290
  name: "最强搜捕者"
  itemId: 850577
  itemNum: 1
  groupId: 2
  weight: 150
  limit: 1
}
rows {
  rewardId: 130002905
  poolId: 13000291
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002906
  poolId: 13000291
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002907
  poolId: 13000291
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002908
  poolId: 13000291
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002909
  poolId: 13000291
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002910
  poolId: 13000291
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002911
  poolId: 13000291
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002912
  poolId: 13000292
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002913
  poolId: 13000292
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002914
  poolId: 13000292
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002915
  poolId: 13000292
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002916
  poolId: 13000292
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002917
  poolId: 13000292
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002918
  poolId: 13000292
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002919
  poolId: 13000293
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002920
  poolId: 13000293
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002921
  poolId: 13000293
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002922
  poolId: 13000293
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002923
  poolId: 13000293
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002924
  poolId: 13000293
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002925
  poolId: 13000293
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002926
  poolId: 13000294
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002927
  poolId: 13000294
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002928
  poolId: 13000294
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002929
  poolId: 13000294
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002930
  poolId: 13000294
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002931
  poolId: 13000294
  name: "棉朵朵"
  itemId: 630193
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002932
  poolId: 13000294
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003001
  poolId: 13000300
  name: "伪装者特效：暴风雪"
  itemId: 241206
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 130003002
  poolId: 13000300
  name: "甜心雪灵"
  itemId: 241404
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003003
  poolId: 13000300
  name: "热烈街篮"
  itemId: 630476
  itemNum: 1
  groupId: 2
  weight: 30
  limit: 1
}
rows {
  rewardId: 130003004
  poolId: 13000300
  name: "精英伪装者"
  itemId: 850575
  itemNum: 1
  groupId: 2
  weight: 150
  limit: 1
}
rows {
  rewardId: 130003005
  poolId: 13000301
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003006
  poolId: 13000301
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003007
  poolId: 13000301
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003008
  poolId: 13000301
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003009
  poolId: 13000301
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003010
  poolId: 13000301
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003011
  poolId: 13000301
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003012
  poolId: 13000302
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003013
  poolId: 13000302
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003014
  poolId: 13000302
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003015
  poolId: 13000302
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003016
  poolId: 13000302
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003017
  poolId: 13000302
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003018
  poolId: 13000302
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003019
  poolId: 13000303
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003020
  poolId: 13000303
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003021
  poolId: 13000303
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003022
  poolId: 13000303
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003023
  poolId: 13000303
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003024
  poolId: 13000303
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003025
  poolId: 13000303
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003026
  poolId: 13000304
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003027
  poolId: 13000304
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003028
  poolId: 13000304
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003029
  poolId: 13000304
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003030
  poolId: 13000304
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003031
  poolId: 13000304
  name: "星梦一号"
  itemId: 630236
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003032
  poolId: 13000304
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002301
  poolId: 13000230
  name: "刺客狼头像框一套"
  itemId: 310255
  itemNum: 1
  groupId: 2
  weight: 19
  limit: 1
}
rows {
  rewardId: 130002302
  poolId: 13000230
  name: "滑板涂鸦-大奖"
  itemId: 240005
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002303
  poolId: 13000230
  name: "别说了一套表情"
  itemId: 310257
  itemNum: 1
  groupId: 1
  weight: 80
  limit: 1
}
rows {
  rewardId: 130002304
  poolId: 13000231
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002305
  poolId: 13000231
  name: "幸运钥匙"
  groupId: 3
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002306
  poolId: 13000231
  name: "小猪撞击*3"
  itemId: 240410
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002307
  poolId: 13000231
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002308
  poolId: 13000231
  name: "戒指*5"
  itemId: 240408
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002309
  poolId: 13000231
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002310
  poolId: 13000231
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002311
  poolId: 13000232
  name: "狼人币*150"
  itemId: 13
  itemNum: 150
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002312
  poolId: 13000232
  name: "幸运钥匙"
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002313
  poolId: 13000232
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002314
  poolId: 13000232
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002315
  poolId: 13000232
  name: "打call*5"
  itemId: 240413
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002316
  poolId: 13000232
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002317
  poolId: 13000232
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002318
  poolId: 13000233
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002319
  poolId: 13000233
  name: "幸运钥匙"
  groupId: 3
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002320
  poolId: 13000233
  name: "永恒之恋*2"
  itemId: 240409
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002321
  poolId: 13000233
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002322
  poolId: 13000233
  name: "拳头*5"
  itemId: 240402
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002323
  poolId: 13000233
  name: "炸弹*10"
  itemId: 240405
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002324
  poolId: 13000233
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002401
  poolId: 13000240
  name: "魔爆狼头像框一套"
  itemId: 310256
  itemNum: 1
  groupId: 2
  weight: 19
  limit: 1
}
rows {
  rewardId: 130002402
  poolId: 13000240
  name: "闪烁突袭"
  itemId: 240206
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002403
  poolId: 13000240
  name: "极力否认 一套表情"
  itemId: 310258
  itemNum: 1
  groupId: 1
  weight: 80
  limit: 1
}
rows {
  rewardId: 130002404
  poolId: 13000241
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002405
  poolId: 13000241
  name: "幸运钥匙"
  groupId: 3
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002406
  poolId: 13000241
  name: "鹊桥*3"
  itemId: 240407
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002407
  poolId: 13000241
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002408
  poolId: 13000241
  name: "打call*5"
  itemId: 240413
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002409
  poolId: 13000241
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002410
  poolId: 13000241
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002411
  poolId: 13000242
  name: "狼人币*150"
  itemId: 13
  itemNum: 150
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002412
  poolId: 13000242
  name: "幸运钥匙"
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002413
  poolId: 13000242
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002414
  poolId: 13000242
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002415
  poolId: 13000242
  name: "戒指*5"
  itemId: 240408
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002416
  poolId: 13000242
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002417
  poolId: 13000242
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002418
  poolId: 13000243
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002419
  poolId: 13000243
  name: "幸运钥匙"
  groupId: 3
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002420
  poolId: 13000243
  name: "小猪撞击*3"
  itemId: 240410
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002421
  poolId: 13000243
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002422
  poolId: 13000243
  name: "干杯*10"
  itemId: 240412
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002423
  poolId: 13000243
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002424
  poolId: 13000243
  name: "送花"
  itemId: 240404
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001894
  poolId: 10000010
  name: "冲锋鲨鲨"
  itemId: 620493
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001895
  poolId: 10000010
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001896
  poolId: 10000010
  name: "星宝印章"
  itemId: 4
  itemNum: 1000
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001897
  poolId: 10000011
  name: "沙沙渔获"
  itemId: 218110
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001898
  poolId: 10000011
  name: "配饰染色剂"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001899
  poolId: 10000011
  name: "服装染色剂"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001900
  poolId: 10000011
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001901
  poolId: 10000011
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130001902
  poolId: 10000012
  name: "丰收兔"
  itemId: 218107
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001903
  poolId: 10000012
  name: "菜园猎手"
  itemId: 630347
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 130001904
  poolId: 10000012
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001905
  poolId: 10000012
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 15
  limit: 1
}
rows {
  rewardId: 130001906
  poolId: 10000012
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 30
  limit: 1
}
rows {
  rewardId: 130001907
  poolId: 10000012
  name: "云朵币"
  itemId: 6
  itemNum: 30
  groupId: 3
  weight: 20
  limit: 1
}
rows {
  rewardId: 130001908
  poolId: 10000012
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 50
  limit: 1
}
rows {
  rewardId: 130001910
  poolId: 13000190
  name: "绮丽海螺城堡"
  itemId: 218123
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001911
  poolId: 13000190
  name: "蔚海绮梦小摊自选礼盒"
  itemId: 331005
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001912
  poolId: 13000190
  name: "蔚海绮梦小摊自选礼盒"
  itemId: 331005
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001913
  poolId: 13000190
  name: "蔚海绮梦小摊自选礼盒"
  itemId: 331005
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001914
  poolId: 13000190
  name: "深海时钟"
  itemId: 218128
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 130001915
  poolId: 13000190
  name: "奇珍"
  itemId: 630376
  itemNum: 1
  groupId: 6
  weight: 30
  limit: 1
}
rows {
  rewardId: 130001916
  poolId: 13000190
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001917
  poolId: 13000190
  name: "幸运币*15"
  itemId: 3
  itemNum: 15
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001918
  poolId: 13000190
  name: "钓鱼全能王"
  itemId: 630344
  itemNum: 1
  groupId: 6
  weight: 70
  limit: 1
}
rows {
  rewardId: 130001919
  poolId: 13000190
  name: "幸运币*5"
  itemId: 3
  itemNum: 5
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001920
  poolId: 13000190
  name: "精品磷虾*8"
  itemId: 219000
  itemNum: 8
  groupId: 5
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001921
  poolId: 13000190
  name: "精品磷虾*16"
  itemId: 219000
  itemNum: 16
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130001922
  poolId: 13000191
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001923
  poolId: 13000191
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001924
  poolId: 13000191
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001925
  poolId: 13000191
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001926
  poolId: 13000191
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001927
  poolId: 13000191
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001928
  poolId: 13000191
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001929
  poolId: 13000192
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001930
  poolId: 13000192
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001931
  poolId: 13000192
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001932
  poolId: 13000192
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001933
  poolId: 13000192
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001934
  poolId: 13000192
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001935
  poolId: 13000192
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001936
  poolId: 13000193
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001937
  poolId: 13000193
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001938
  poolId: 13000193
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001939
  poolId: 13000193
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001940
  poolId: 13000193
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001941
  poolId: 13000193
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001942
  poolId: 13000193
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001943
  poolId: 13000194
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001944
  poolId: 13000194
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001945
  poolId: 13000194
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001946
  poolId: 13000194
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001947
  poolId: 13000194
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001948
  poolId: 13000194
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001949
  poolId: 13000194
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001950
  poolId: 13000195
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001951
  poolId: 13000195
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001952
  poolId: 13000195
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001953
  poolId: 13000195
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001954
  poolId: 13000195
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001955
  poolId: 13000195
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001956
  poolId: 13000195
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001957
  poolId: 13000196
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001958
  poolId: 13000196
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001959
  poolId: 13000196
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001960
  poolId: 13000196
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001961
  poolId: 13000196
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001962
  poolId: 13000196
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001963
  poolId: 13000196
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001964
  poolId: 13000197
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001965
  poolId: 13000197
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001966
  poolId: 13000197
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001967
  poolId: 13000197
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001968
  poolId: 13000197
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001969
  poolId: 13000197
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001970
  poolId: 13000197
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001971
  poolId: 13000198
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001972
  poolId: 13000198
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001973
  poolId: 13000198
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001974
  poolId: 13000198
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001975
  poolId: 13000198
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001976
  poolId: 13000198
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001977
  poolId: 13000198
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001978
  poolId: 13000199
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001979
  poolId: 13000199
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001980
  poolId: 13000199
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001981
  poolId: 13000199
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001982
  poolId: 13000199
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001983
  poolId: 13000199
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001984
  poolId: 13000199
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001985
  poolId: 13000200
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001986
  poolId: 13000200
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001987
  poolId: 13000200
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001988
  poolId: 13000200
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001989
  poolId: 13000200
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001990
  poolId: 13000200
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001991
  poolId: 13000200
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001992
  poolId: 13000201
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130001993
  poolId: 13000201
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001994
  poolId: 13000201
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001995
  poolId: 13000201
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001996
  poolId: 13000201
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001997
  poolId: 13000201
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130001998
  poolId: 13000201
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130001999
  poolId: 13000202
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002000
  poolId: 13000202
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002001
  poolId: 13000202
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002002
  poolId: 13000202
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002003
  poolId: 13000202
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002004
  poolId: 13000202
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002005
  poolId: 13000202
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002501
  poolId: 13000250
  name: "真相永远只有一个"
  itemId: 240011
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002502
  poolId: 13000250
  name: "二选一礼包"
  itemId: 331013
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 130002503
  poolId: 13000250
  name: "丢雷动作表情"
  itemId: 711313
  itemNum: 1
  groupId: 3
  weight: 79
  limit: 1
}
rows {
  rewardId: 130002504
  poolId: 13000251
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002505
  poolId: 13000251
  name: "拳击*5"
  itemId: 240402
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002506
  poolId: 13000251
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002507
  poolId: 13000251
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002508
  poolId: 13000251
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002509
  poolId: 13000251
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002510
  poolId: 13000251
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002511
  poolId: 13000252
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002512
  poolId: 13000252
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002513
  poolId: 13000252
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002514
  poolId: 13000252
  name: "新年快乐*5"
  itemId: 240422
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002515
  poolId: 13000252
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002516
  poolId: 13000252
  name: "身份卡*3"
  itemId: 200102
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002517
  poolId: 13000252
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002518
  poolId: 13000253
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002519
  poolId: 13000253
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002520
  poolId: 13000253
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002521
  poolId: 13000253
  name: "炸弹狼头像框"
  itemId: 840517
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002522
  poolId: 13000253
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002523
  poolId: 13000253
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002524
  poolId: 13000253
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002601
  poolId: 13000260
  name: "手表麻醉针攻击"
  itemId: 240212
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002602
  poolId: 13000260
  name: "饰品二选一"
  itemId: 331012
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 130002603
  poolId: 13000260
  name: "nice动作表情"
  itemId: 711314
  itemNum: 1
  groupId: 3
  weight: 79
  limit: 1
}
rows {
  rewardId: 130002604
  poolId: 13000261
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002605
  poolId: 13000261
  name: "送花*5"
  itemId: 240404
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002606
  poolId: 13000261
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002607
  poolId: 13000261
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002608
  poolId: 13000261
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002609
  poolId: 13000261
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002610
  poolId: 13000261
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002611
  poolId: 13000262
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002612
  poolId: 13000262
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002613
  poolId: 13000262
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002614
  poolId: 13000262
  name: "新年快乐*5"
  itemId: 240422
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002615
  poolId: 13000262
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002616
  poolId: 13000262
  name: "身份卡*3"
  itemId: 200102
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002617
  poolId: 13000262
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002618
  poolId: 13000263
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002619
  poolId: 13000263
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002620
  poolId: 13000263
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002621
  poolId: 13000263
  name: "炸弹狼头像+昵称框"
  itemId: 310283
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002622
  poolId: 13000263
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002623
  poolId: 13000263
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130002624
  poolId: 13000263
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 600010501
  poolId: 6000105
  name: "星辰之子 曜"
  itemId: 404210
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010502
  poolId: 6000105
  name: "剑圣 宫本武藏"
  itemId: 404620
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010503
  poolId: 6000105
  name: "绛天战刃"
  itemId: 620278
  itemNum: 1
  groupId: 2
  weight: 2
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010504
  poolId: 6000105
  name: "鸣雷"
  itemId: 640096
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010505
  poolId: 6000105
  name: "天才亮相"
  itemId: 720891
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010506
  poolId: 6000105
  name: "剑圣奥义"
  itemId: 720897
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010507
  poolId: 6000105
  name: "终极表演"
  itemId: 711311
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010508
  poolId: 6000105
  name: "无敌寂寞"
  itemId: 711236
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010509
  poolId: 6000105
  name: "峡谷券*5"
  itemId: 218
  itemNum: 5
  groupId: 5
  weight: 4
  comboItemIds: 320063
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010510
  poolId: 6000105
  name: "峡谷券*3"
  itemId: 218
  itemNum: 3
  groupId: 5
  weight: 11
  comboItemIds: 320062
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010511
  poolId: 6000105
  name: "峡谷券*2"
  itemId: 218
  itemNum: 2
  groupId: 5
  weight: 85
  comboItemIds: 320061
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010512
  poolId: 6000105
  name: "峡谷币*10"
  itemId: 3541
  itemNum: 10
  groupId: 6
  weight: 1
  limit: 20
}
rows {
  rewardId: 600010513
  poolId: 6000105
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010514
  poolId: 6000105
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010515
  poolId: 6000105
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010516
  poolId: 6000105
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010517
  poolId: 6000105
  name: "赛博行者"
  itemId: 720834
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010518
  poolId: 6000105
  name: "超时空追击"
  itemId: 720825
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010519
  poolId: 6000105
  name: "不耐烦"
  itemId: 711140
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010520
  poolId: 6000105
  name: "正义制裁"
  itemId: 711139
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010521
  poolId: 6000105
  name: "异界灵契"
  itemId: 720819
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010522
  poolId: 6000105
  name: "挚爱之约"
  itemId: 720758
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010523
  poolId: 6000105
  name: "初次公演"
  itemId: 720777
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010524
  poolId: 6000105
  name: "灵动之舞"
  itemId: 720776
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010525
  poolId: 6000105
  name: "龙胆现世"
  itemId: 720733
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010526
  poolId: 6000105
  name: "追逃游戏"
  itemId: 720735
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010527
  poolId: 6000105
  name: "心动节拍"
  itemId: 720680
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010528
  poolId: 6000105
  name: "天翔影龙"
  itemId: 720681
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010529
  poolId: 6000105
  name: "轻松解决"
  itemId: 711074
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010530
  poolId: 6000105
  name: "为爱出发"
  itemId: 711138
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010531
  poolId: 6000105
  name: "魅力偶像"
  itemId: 711137
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010532
  poolId: 6000105
  name: "程序启动"
  itemId: 711132
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010533
  poolId: 6000105
  name: "龙魂再起"
  itemId: 711062
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010534
  poolId: 6000105
  name: "古灵精怪"
  itemId: 711061
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010535
  poolId: 6000105
  name: "小乔头像"
  itemId: 860079
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 600010536
  poolId: 6000105
  name: "兰陵王头像"
  itemId: 860080
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 20
    limit: 1
  }
}
rows {
  rewardId: 130002701
  poolId: 13000270
  name: "冰晶星梦城堡"
  itemId: 218142
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002702
  poolId: 13000270
  name: "雪夜小摊自选礼盒"
  itemId: 331008
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130002703
  poolId: 13000270
  name: "雪夜小摊自选礼盒"
  itemId: 331008
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130002704
  poolId: 13000270
  name: "雪夜小摊自选礼盒"
  itemId: 331008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130002705
  poolId: 13000270
  name: "糖果松树时钟"
  itemId: 218138
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 130002706
  poolId: 13000270
  name: "喵来啦"
  itemId: 620682
  itemNum: 1
  groupId: 6
  weight: 30
  limit: 1
}
rows {
  rewardId: 130002707
  poolId: 13000270
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130002708
  poolId: 13000270
  name: "幸运币*15"
  itemId: 3
  itemNum: 15
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130002709
  poolId: 13000270
  name: "小暖狐"
  itemId: 620535
  itemNum: 1
  groupId: 6
  weight: 70
  limit: 1
}
rows {
  rewardId: 130002710
  poolId: 13000270
  name: "幸运币*5"
  itemId: 3
  itemNum: 5
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130002711
  poolId: 13000270
  name: "冰梦城堡头像"
  itemId: 860153
  itemNum: 1
  groupId: 5
  weight: 95
  limit: 1
}
rows {
  rewardId: 130002712
  poolId: 13000270
  name: "钓鱼大师"
  itemId: 720779
  itemNum: 1
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130002713
  poolId: 13000271
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002714
  poolId: 13000271
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002715
  poolId: 13000271
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002716
  poolId: 13000271
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002717
  poolId: 13000271
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002718
  poolId: 13000271
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002719
  poolId: 13000271
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002720
  poolId: 13000272
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002721
  poolId: 13000272
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002722
  poolId: 13000272
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002723
  poolId: 13000272
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002724
  poolId: 13000272
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002725
  poolId: 13000272
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002726
  poolId: 13000272
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002727
  poolId: 13000273
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002728
  poolId: 13000273
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002729
  poolId: 13000273
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002730
  poolId: 13000273
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002731
  poolId: 13000273
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002732
  poolId: 13000273
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002733
  poolId: 13000273
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002734
  poolId: 13000274
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002735
  poolId: 13000274
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002736
  poolId: 13000274
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002737
  poolId: 13000274
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002738
  poolId: 13000274
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002739
  poolId: 13000274
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002740
  poolId: 13000274
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002741
  poolId: 13000275
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002742
  poolId: 13000275
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002743
  poolId: 13000275
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002744
  poolId: 13000275
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002745
  poolId: 13000275
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002746
  poolId: 13000275
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002747
  poolId: 13000275
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002748
  poolId: 13000276
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002749
  poolId: 13000276
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002750
  poolId: 13000276
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002751
  poolId: 13000276
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002752
  poolId: 13000276
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002753
  poolId: 13000276
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002754
  poolId: 13000276
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002755
  poolId: 13000277
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002756
  poolId: 13000277
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002757
  poolId: 13000277
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002758
  poolId: 13000277
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002759
  poolId: 13000277
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002760
  poolId: 13000277
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002761
  poolId: 13000277
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002762
  poolId: 13000278
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002763
  poolId: 13000278
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002764
  poolId: 13000278
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002765
  poolId: 13000278
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002766
  poolId: 13000278
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002767
  poolId: 13000278
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002768
  poolId: 13000278
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002769
  poolId: 13000279
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002770
  poolId: 13000279
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002771
  poolId: 13000279
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002772
  poolId: 13000279
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002773
  poolId: 13000279
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002774
  poolId: 13000279
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002775
  poolId: 13000279
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002776
  poolId: 13000280
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002777
  poolId: 13000280
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002778
  poolId: 13000280
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002779
  poolId: 13000280
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002780
  poolId: 13000280
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002781
  poolId: 13000280
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002782
  poolId: 13000280
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002783
  poolId: 13000281
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002784
  poolId: 13000281
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002785
  poolId: 13000281
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002786
  poolId: 13000281
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002787
  poolId: 13000281
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002788
  poolId: 13000281
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002789
  poolId: 13000281
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130002790
  poolId: 13000282
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130002791
  poolId: 13000282
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002792
  poolId: 13000282
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002793
  poolId: 13000282
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002794
  poolId: 13000282
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002795
  poolId: 13000282
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130002796
  poolId: 13000282
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 100000131
  poolId: 10000013
  name: "泡泡海豚"
  itemId: 620643
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000132
  poolId: 10000013
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000133
  poolId: 10000013
  name: "星宝印章"
  itemId: 4
  itemNum: 1000
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 100000134
  poolId: 10000014
  name: "泡泡鱼礼盒"
  itemId: 218133
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000135
  poolId: 10000014
  name: "配饰染色剂"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000136
  poolId: 10000014
  name: "服装染色剂"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000137
  poolId: 10000014
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000138
  poolId: 10000014
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000139
  poolId: 10000015
  name: "海狮公主"
  itemId: 218124
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000140
  poolId: 10000015
  name: "回旋飞盘"
  itemId: 620591
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000141
  poolId: 10000015
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000142
  poolId: 10000015
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 15
  limit: 1
}
rows {
  rewardId: 100000143
  poolId: 10000015
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 30
  limit: 1
}
rows {
  rewardId: 100000144
  poolId: 10000015
  name: "云朵币"
  itemId: 6
  itemNum: 30
  groupId: 3
  weight: 20
  limit: 1
}
rows {
  rewardId: 100000145
  poolId: 10000015
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 50
  limit: 1
}
rows {
  rewardId: 100001202
  poolId: 1000012
  name: "倒咖啡*20"
  itemId: 240406
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001203
  poolId: 1000012
  name: "狼人币*400"
  itemId: 13
  itemNum: 400
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001201
  poolId: 1000012
  name: "大锤击飞"
  itemId: 240203
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100001206
  poolId: 1000012
  name: "保镖眼镜：暗影凝视"
  itemId: 610253
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001205
  poolId: 1000012
  name: "新年快乐*10"
  itemId: 240422
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001204
  poolId: 1000012
  name: "贴符*10"
  itemId: 240417
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001303
  poolId: 1000013
  name: "丢盲盒*10"
  itemId: 240418
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001304
  poolId: 1000013
  name: "手表麻醉针*10"
  itemId: 240421
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001301
  poolId: 1000013
  name: "敲锣打鼓"
  itemId: 240004
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100001306
  poolId: 1000013
  name: "馋哭了表情"
  itemId: 711032
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001305
  poolId: 1000013
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001302
  poolId: 1000013
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001403
  poolId: 1000014
  name: "四选1表情"
  itemId: 331014
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001404
  poolId: 1000014
  name: "手表麻醉针*10"
  itemId: 240421
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001401
  poolId: 1000014
  name: "小猪冲锋"
  itemId: 240208
  itemNum: 1
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100001405
  poolId: 1000014
  name: "新年快乐*10"
  itemId: 240422
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001406
  poolId: 1000014
  name: "身份卡/阵营卡包*1"
  itemId: 310287
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001402
  poolId: 1000014
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001502
  poolId: 1000015
  name: "疯狂点赞*20"
  itemId: 240416
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001505
  poolId: 1000015
  name: "新年快乐*10"
  itemId: 240422
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001501
  poolId: 1000015
  name: "时空特警"
  itemId: 240007
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100001504
  poolId: 1000015
  name: "庆祝 表情"
  itemId: 240627
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001506
  poolId: 1000015
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100001503
  poolId: 1000015
  name: "打call*10"
  itemId: 240413
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000161
  poolId: 10000016
  name: "烟囱小熊"
  itemId: 630455
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000162
  poolId: 10000016
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000163
  poolId: 10000016
  name: "星宝印章"
  itemId: 4
  itemNum: 1000
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 100000164
  poolId: 10000017
  name: "冬日萌宠屋"
  itemId: 218139
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000165
  poolId: 10000017
  name: "配饰染色剂"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000166
  poolId: 10000017
  name: "服装染色剂"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000167
  poolId: 10000017
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000168
  poolId: 10000017
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000169
  poolId: 10000018
  name: "雪球小精灵"
  itemId: 218137
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000170
  poolId: 10000018
  name: "小鹿星"
  itemId: 630448
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000171
  poolId: 10000018
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000172
  poolId: 10000018
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 15
  limit: 1
}
rows {
  rewardId: 100000173
  poolId: 10000018
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 30
  limit: 1
}
rows {
  rewardId: 100000174
  poolId: 10000018
  name: "云朵币"
  itemId: 6
  itemNum: 30
  groupId: 3
  weight: 20
  limit: 1
}
rows {
  rewardId: 100000175
  poolId: 10000018
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 50
  limit: 1
}
rows {
  rewardId: 60001061
  poolId: 60001061
  name: "无双之魔 吕布"
  itemId: 404760
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001062
  poolId: 60001061
  name: "方天画戟"
  itemId: 620686
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001063
  poolId: 60001061
  name: "阵前挑衅"
  itemId: 720907
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001064
  poolId: 60001061
  name: "放马过来"
  itemId: 711315
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001065
  poolId: 60001061
  name: "吕布昵称框"
  itemId: 820150
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001066
  poolId: 60001061
  name: "峡谷币*150"
  itemId: 3541
  itemNum: 150
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001067
  poolId: 60001061
  name: "吕布头像"
  itemId: 860160
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001068
  poolId: 60001061
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001069
  poolId: 60001061
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001070
  poolId: 60001062
  name: "水晶猎龙者 花木兰"
  itemId: 404770
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001071
  poolId: 60001062
  name: "猎龙双刃"
  itemId: 620687
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001072
  poolId: 60001062
  name: "火热剑舞"
  itemId: 720898
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001073
  poolId: 60001062
  name: "猎龙大师"
  itemId: 711190
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001074
  poolId: 60001062
  name: "花木兰昵称框"
  itemId: 820151
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001075
  poolId: 60001062
  name: "峡谷币*150"
  itemId: 3541
  itemNum: 150
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001076
  poolId: 60001062
  name: "花木兰头像"
  itemId: 860161
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001077
  poolId: 60001062
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001078
  poolId: 60001062
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000191
  poolId: 10000021
  name: "星宝印章*1000;星宝印章*2000"
  groupId: 5
  weight: 1
  limit: 1
  subRewards {
    itemId: 4
    itemNum: 1000
  }
  subRewards {
    itemId: 4
    itemNum: 2000
  }
}
rows {
  rewardId: 100000192
  poolId: 10000021
  name: "云朵币*20;云朵币*30"
  groupId: 4
  weight: 1
  limit: 1
  subRewards {
    itemId: 6
    itemNum: 20
  }
  subRewards {
    itemId: 6
    itemNum: 30
  }
}
rows {
  rewardId: 100000193
  poolId: 10000021
  name: "心心宝瓶*2;心心宝瓶*3"
  groupId: 5
  weight: 1
  limit: 1
  subRewards {
    itemId: 200016
    itemNum: 2
  }
  subRewards {
    itemId: 200016
    itemNum: 3
  }
}
rows {
  rewardId: 100000194
  poolId: 10000021
  name: "仙福满满时钟;庆丰年宅院"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  subRewards {
    itemId: 218156
    itemNum: 1
  }
  subRewards {
    itemId: 218159
    itemNum: 1
  }
}
rows {
  rewardId: 100000195
  poolId: 10000021
  name: "遇上彩虹;浣熊奇遇记"
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
  subRewards {
    itemId: 219801
    itemNum: 1
  }
  subRewards {
    itemId: 620678
    itemNum: 1
  }
}
rows {
  rewardId: 100000196
  poolId: 10000021
  name: "火焰精灵;锦绣狮宝"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
  subRewards {
    itemId: 620718
    itemNum: 1
  }
  subRewards {
    itemId: 219303
    itemNum: 1
  }
}
rows {
  rewardId: 100000197
  poolId: 10000021
  name: "星愿币*2;星愿币*3"
  groupId: 4
  weight: 1
  limit: 1
  subRewards {
    itemId: 2
    itemNum: 2
  }
  subRewards {
    itemId: 2
    itemNum: 3
  }
}
rows {
  rewardId: 100000198
  poolId: 10000021
  name: "配饰染色剂*2;配饰染色剂*3"
  groupId: 4
  weight: 1
  limit: 1
  subRewards {
    itemId: 200008
    itemNum: 2
  }
  subRewards {
    itemId: 200008
    itemNum: 3
  }
}
rows {
  rewardId: 100000199
  poolId: 10000021
  name: "时装染色剂*2;时装染色剂*3"
  groupId: 5
  weight: 1
  limit: 1
  subRewards {
    itemId: 200006
    itemNum: 2
  }
  subRewards {
    itemId: 200006
    itemNum: 3
  }
}
rows {
  rewardId: 100000200
  poolId: 10000021
  name: "烟花礼盒*10;烟花礼盒*20"
  groupId: 5
  weight: 1
  limit: 1
  subRewards {
    itemId: 725201
    itemNum: 10
  }
  subRewards {
    itemId: 725201
    itemNum: 20
  }
}
rows {
  rewardId: 600011001
  poolId: 6000110
  name: "峡谷之心"
  itemId: 3608
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 600011002
  poolId: 6000110
  name: "山河点破"
  itemId: 620764
  itemNum: 1
  groupId: 2
  weight: 4
  inGroupLimit {
    period: 3
    limit: 1
  }
}
rows {
  rewardId: 600011003
  poolId: 6000110
  name: "苍龙行云"
  itemId: 640125
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
}
rows {
  rewardId: 600011004
  poolId: 6000110
  name: "乾坤轮"
  itemId: 620765
  itemNum: 1
  groupId: 2
  weight: 2
  inGroupLimit {
    period: 3
    limit: 1
  }
}
rows {
  rewardId: 600011005
  poolId: 6000110
  name: "山河不灭"
  itemId: 720965
  itemNum: 1
  groupId: 3
  weight: 1
  inGroupLimit {
    period: 5
    limit: 1
  }
}
rows {
  rewardId: 600011006
  poolId: 6000110
  name: "意念瞄准"
  itemId: 711319
  itemNum: 1
  groupId: 3
  weight: 1
  inGroupLimit {
    period: 5
    limit: 1
  }
}
rows {
  rewardId: 600011007
  poolId: 6000110
  name: "云海立誓"
  itemId: 840245
  itemNum: 1
  groupId: 3
  weight: 1
  inGroupLimit {
    period: 5
    limit: 1
  }
}
rows {
  rewardId: 600011008
  poolId: 6000110
  name: "云海之上"
  itemId: 870038
  itemNum: 1
  groupId: 3
  weight: 1
  inGroupLimit {
    period: 5
    limit: 1
  }
}
rows {
  rewardId: 600011009
  poolId: 6000110
  name: "绝顶之巅"
  itemId: 820160
  itemNum: 1
  groupId: 3
  weight: 1
  inGroupLimit {
    period: 5
    limit: 1
  }
}
rows {
  rewardId: 600011010
  poolId: 6000110
  name: "伽罗头像"
  itemId: 860176
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 600011011
  poolId: 6000110
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 5
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
}
rows {
  rewardId: 600011012
  poolId: 6000110
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 5
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
}
rows {
  rewardId: 600011013
  poolId: 6000110
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
}
rows {
  rewardId: 600011014
  poolId: 6000110
  name: "貂蝉体验卡（7天）"
  itemId: 301601
  itemNum: 1
  groupId: 6
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
}
rows {
  rewardId: 600011015
  poolId: 6000110
  name: "曜体验卡（7天）"
  itemId: 301602
  itemNum: 1
  groupId: 6
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
}
rows {
  rewardId: 600011016
  poolId: 6000110
  name: "公孙离体验卡（7天）"
  itemId: 301603
  itemNum: 1
  groupId: 6
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
}
rows {
  rewardId: 600011017
  poolId: 6000110
  name: "典韦体验卡（7天）"
  itemId: 301604
  itemNum: 1
  groupId: 7
  weight: 1
  inGroupLimit {
    period: 7
    limit: 1
  }
}
rows {
  rewardId: 600011018
  poolId: 6000110
  name: "阿轲体验卡（7天）"
  itemId: 301605
  itemNum: 1
  groupId: 7
  weight: 1
  inGroupLimit {
    period: 7
    limit: 1
  }
}
rows {
  rewardId: 600011019
  poolId: 6000110
  name: "吕布体验卡（7天）"
  itemId: 301606
  itemNum: 1
  groupId: 7
  weight: 1
  inGroupLimit {
    period: 7
    limit: 1
  }
}
rows {
  rewardId: 600011020
  poolId: 6000110
  name: "虞姬体验卡（7天）"
  itemId: 301607
  itemNum: 1
  groupId: 7
  weight: 1
  inGroupLimit {
    period: 7
    limit: 1
  }
}
rows {
  rewardId: 600011021
  poolId: 6000110
  name: "马可波罗体验卡（7天）"
  itemId: 301608
  itemNum: 1
  groupId: 7
  weight: 1
  inGroupLimit {
    period: 7
    limit: 1
  }
}
rows {
  rewardId: 600011022
  poolId: 6000110
  name: "花木兰体验卡（7天）"
  itemId: 301609
  itemNum: 1
  groupId: 7
  weight: 1
  inGroupLimit {
    period: 7
    limit: 1
  }
}
rows {
  rewardId: 600011023
  poolId: 6000110
  name: "峡谷币*35"
  itemId: 3541
  itemNum: 35
  groupId: 7
  weight: 10
  limit: 15
  inGroupLimit {
    period: 7
    limit: 1
  }
}
rows {
  rewardId: 70001011
  poolId: 70001011
  name: "乘风破浪 夏侯惇"
  itemId: 404450
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 70001012
  poolId: 70001011
  name: "大白鲨"
  itemId: 620632
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 70001013
  poolId: 70001011
  name: "钓鲨鱼"
  itemId: 720876
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 70001014
  poolId: 70001011
  name: "浪尖冲刺"
  itemId: 711245
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 70001015
  poolId: 70001011
  name: "乘风破浪"
  itemId: 861009
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 70001016
  poolId: 70001011
  name: "峡谷币*80"
  itemId: 3541
  itemNum: 80
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 70001017
  poolId: 70001011
  name: "夏侯惇头像"
  itemId: 860141
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 70001018
  poolId: 70001011
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 70001019
  poolId: 70001011
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 70001020
  poolId: 70001012
  name: "缤纷绘卷 张良"
  itemId: 404460
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 70001021
  poolId: 70001012
  name: "学霸挎包"
  itemId: 620631
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 70001022
  poolId: 70001012
  name: "参透天书"
  itemId: 720875
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 70001023
  poolId: 70001012
  name: "沉浸书香"
  itemId: 711244
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 70001024
  poolId: 70001012
  name: "缤纷绘卷"
  itemId: 861010
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 70001025
  poolId: 70001012
  name: "峡谷币*80"
  itemId: 3541
  itemNum: 80
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 70001026
  poolId: 70001012
  name: "张良头像"
  itemId: 860142
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 70001027
  poolId: 70001012
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 70001028
  poolId: 70001012
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 600010701
  poolId: 6000107
  name: "节奏热浪 阿轲"
  itemId: 410060
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010702
  poolId: 6000107
  name: "蓝屏警告 典韦"
  itemId: 410080
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010703
  poolId: 6000107
  name: "闪耀应援"
  itemId: 640124
  itemNum: 1
  groupId: 2
  weight: 2
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010704
  poolId: 6000107
  name: "电竞一号"
  itemId: 620731
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600010705
  poolId: 6000107
  name: "音浪鼓舞"
  itemId: 720946
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010706
  poolId: 6000107
  name: "键不离手"
  itemId: 720947
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010707
  poolId: 6000107
  name: "看好你哟"
  itemId: 711316
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010708
  poolId: 6000107
  name: "大脑宕机"
  itemId: 711312
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600010709
  poolId: 6000107
  name: "峡谷券*5"
  itemId: 218
  itemNum: 5
  groupId: 5
  weight: 4
  comboItemIds: 320063
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010710
  poolId: 6000107
  name: "峡谷券*3"
  itemId: 218
  itemNum: 3
  groupId: 5
  weight: 11
  comboItemIds: 320062
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010711
  poolId: 6000107
  name: "峡谷券*2"
  itemId: 218
  itemNum: 2
  groupId: 5
  weight: 85
  comboItemIds: 320061
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600010712
  poolId: 6000107
  name: "峡谷币*10"
  itemId: 3541
  itemNum: 10
  groupId: 6
  weight: 1
  limit: 20
}
rows {
  rewardId: 600010713
  poolId: 6000107
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010714
  poolId: 6000107
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010715
  poolId: 6000107
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010716
  poolId: 6000107
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600010717
  poolId: 6000107
  name: "天才亮相"
  itemId: 720891
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010718
  poolId: 6000107
  name: "剑圣奥义"
  itemId: 720897
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010719
  poolId: 6000107
  name: "终极表演"
  itemId: 711311
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010720
  poolId: 6000107
  name: "无敌寂寞"
  itemId: 711236
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010721
  poolId: 6000107
  name: "赛博行者"
  itemId: 720834
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010722
  poolId: 6000107
  name: "超时空追击"
  itemId: 720825
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010723
  poolId: 6000107
  name: "不耐烦"
  itemId: 711140
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010724
  poolId: 6000107
  name: "正义制裁"
  itemId: 711139
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010725
  poolId: 6000107
  name: "异界灵契"
  itemId: 720819
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010726
  poolId: 6000107
  name: "挚爱之约"
  itemId: 720758
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010727
  poolId: 6000107
  name: "初次公演"
  itemId: 720777
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010728
  poolId: 6000107
  name: "灵动之舞"
  itemId: 720776
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010729
  poolId: 6000107
  name: "龙胆现世"
  itemId: 720733
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010730
  poolId: 6000107
  name: "追逃游戏"
  itemId: 720735
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010731
  poolId: 6000107
  name: "心动节拍"
  itemId: 720680
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010732
  poolId: 6000107
  name: "天翔影龙"
  itemId: 720681
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010733
  poolId: 6000107
  name: "轻松解决"
  itemId: 711074
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010734
  poolId: 6000107
  name: "为爱出发"
  itemId: 711138
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010735
  poolId: 6000107
  name: "魅力偶像"
  itemId: 711137
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010736
  poolId: 6000107
  name: "程序启动"
  itemId: 711132
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010737
  poolId: 6000107
  name: "龙魂再起"
  itemId: 711062
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010738
  poolId: 6000107
  name: "古灵精怪"
  itemId: 711061
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010739
  poolId: 6000107
  name: "小乔头像"
  itemId: 860079
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 600010740
  poolId: 6000107
  name: "兰陵王头像"
  itemId: 860080
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 24
    limit: 1
  }
}
rows {
  rewardId: 130003101
  poolId: 13000310
  name: "狼人尊贵大卡包*1"
  itemId: 310265
  itemNum: 1
  groupId: 2
  weight: 24
  limit: 1
}
rows {
  rewardId: 130003102
  poolId: 13000310
  name: "魔法扫帚-大奖"
  itemId: 240009
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003103
  poolId: 13000310
  name: "伪装狼三件套"
  itemId: 310266
  itemNum: 1
  groupId: 2
  weight: 75
  limit: 1
}
rows {
  rewardId: 130003104
  poolId: 13000311
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003105
  poolId: 13000311
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003106
  poolId: 13000311
  name: "丢鸡蛋*10"
  itemId: 240401
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003107
  poolId: 13000312
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003108
  poolId: 13000312
  name: "三选2选旧表情*1"
  itemId: 330079
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003109
  poolId: 13000312
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003110
  poolId: 13000312
  name: "阵营卡*5"
  itemId: 200101
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003111
  poolId: 13000312
  name: "拳击*10"
  itemId: 240402
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003112
  poolId: 13000313
  name: "丢盲盒*10"
  itemId: 240418
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003113
  poolId: 13000313
  name: "新年快乐*5"
  itemId: 240422
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003114
  poolId: 13000313
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003115
  poolId: 13000313
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003116
  poolId: 13000313
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003117
  poolId: 13000313
  name: "贴符*10"
  itemId: 240417
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003118
  poolId: 13000313
  name: "身份卡*4"
  itemId: 200102
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003201
  poolId: 13000320
  name: "狼人尊贵大卡包*1"
  itemId: 310265
  itemNum: 1
  groupId: 2
  weight: 24
  limit: 1
}
rows {
  rewardId: 130003202
  poolId: 13000320
  name: "爱心冲击"
  itemId: 240207
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003203
  poolId: 13000320
  name: "庆祝"
  itemId: 240627
  itemNum: 1
  groupId: 2
  weight: 75
  limit: 1
}
rows {
  rewardId: 130003204
  poolId: 13000321
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003205
  poolId: 13000321
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003206
  poolId: 13000321
  name: "炸弹*10"
  itemId: 240405
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003207
  poolId: 13000322
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003208
  poolId: 13000322
  name: "三选2选旧表情*1"
  itemId: 330081
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003209
  poolId: 13000322
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003210
  poolId: 13000322
  name: "暗影凝视饰品*1"
  itemId: 610253
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003211
  poolId: 13000322
  name: "手表麻醉针*8"
  itemId: 240421
  itemNum: 8
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003212
  poolId: 13000323
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003213
  poolId: 13000323
  name: "送花*10"
  itemId: 240404
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003214
  poolId: 13000323
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003215
  poolId: 13000323
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003216
  poolId: 13000323
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003217
  poolId: 13000323
  name: "祝福*10"
  itemId: 240419
  itemNum: 10
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003218
  poolId: 13000323
  name: "身份卡*4"
  itemId: 200102
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003301
  poolId: 13000330
  name: "琳琅摘星阁"
  itemId: 218151
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003302
  poolId: 13000330
  name: "福运小摊自选礼盒"
  itemId: 331016
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003303
  poolId: 13000330
  name: "福运小摊自选礼盒"
  itemId: 331016
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003304
  poolId: 13000330
  name: "福运小摊自选礼盒"
  itemId: 331016
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003305
  poolId: 13000330
  name: "笑口常开"
  itemId: 630504
  itemNum: 1
  groupId: 5
  weight: 30
  limit: 1
}
rows {
  rewardId: 130003306
  poolId: 13000330
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003307
  poolId: 13000330
  name: "骨感美鱼"
  itemId: 630513
  itemNum: 1
  groupId: 5
  weight: 70
  limit: 1
}
rows {
  rewardId: 130003308
  poolId: 13000330
  name: "幸运币*5"
  itemId: 3
  itemNum: 5
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003309
  poolId: 13000330
  name: "琳琅摘星头像"
  itemId: 860168
  itemNum: 1
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003310
  poolId: 13000330
  name: "春节表情"
  itemId: 711321
  itemNum: 1
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003311
  poolId: 13000331
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003312
  poolId: 13000331
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003313
  poolId: 13000331
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003314
  poolId: 13000331
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003315
  poolId: 13000331
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003316
  poolId: 13000331
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003317
  poolId: 13000331
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003318
  poolId: 13000332
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003319
  poolId: 13000332
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003320
  poolId: 13000332
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003321
  poolId: 13000332
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003322
  poolId: 13000332
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003323
  poolId: 13000332
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003324
  poolId: 13000332
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003325
  poolId: 13000333
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003326
  poolId: 13000333
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003327
  poolId: 13000333
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003328
  poolId: 13000333
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003329
  poolId: 13000333
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003330
  poolId: 13000333
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003331
  poolId: 13000333
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003332
  poolId: 13000334
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003333
  poolId: 13000334
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003334
  poolId: 13000334
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003335
  poolId: 13000334
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003336
  poolId: 13000334
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003337
  poolId: 13000334
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003338
  poolId: 13000334
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003339
  poolId: 13000335
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003340
  poolId: 13000335
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003341
  poolId: 13000335
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003342
  poolId: 13000335
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003343
  poolId: 13000335
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003344
  poolId: 13000335
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003345
  poolId: 13000335
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003346
  poolId: 13000336
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003347
  poolId: 13000336
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003348
  poolId: 13000336
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003349
  poolId: 13000336
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003350
  poolId: 13000336
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003351
  poolId: 13000336
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003352
  poolId: 13000336
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003353
  poolId: 13000337
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003354
  poolId: 13000337
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003355
  poolId: 13000337
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003356
  poolId: 13000337
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003357
  poolId: 13000337
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003358
  poolId: 13000337
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003359
  poolId: 13000337
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003360
  poolId: 13000338
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003361
  poolId: 13000338
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003362
  poolId: 13000338
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003363
  poolId: 13000338
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003364
  poolId: 13000338
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003365
  poolId: 13000338
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003366
  poolId: 13000338
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003367
  poolId: 13000339
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003368
  poolId: 13000339
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003369
  poolId: 13000339
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003370
  poolId: 13000339
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003371
  poolId: 13000339
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003372
  poolId: 13000339
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003373
  poolId: 13000339
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003374
  poolId: 13000340
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003375
  poolId: 13000340
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003376
  poolId: 13000340
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003377
  poolId: 13000340
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003378
  poolId: 13000340
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003379
  poolId: 13000340
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003380
  poolId: 13000340
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 100000201
  poolId: 10000019
  name: "小笼喵"
  itemId: 630507
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000202
  poolId: 10000019
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000203
  poolId: 10000019
  name: "星宝印章"
  itemId: 4
  itemNum: 1000
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 100000204
  poolId: 10000020
  name: "宝莲灯礼盒"
  itemId: 218157
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000205
  poolId: 10000020
  name: "配饰染色剂"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000206
  poolId: 10000020
  name: "服装染色剂"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000207
  poolId: 10000020
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000208
  poolId: 10000020
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000209
  poolId: 10000022
  name: "嘶嘶灵宝"
  itemId: 218155
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000210
  poolId: 10000022
  name: "瓶中阳光"
  itemId: 620755
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000211
  poolId: 10000022
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000212
  poolId: 10000022
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 15
  limit: 1
}
rows {
  rewardId: 100000213
  poolId: 10000022
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 30
  limit: 1
}
rows {
  rewardId: 100000214
  poolId: 10000022
  name: "云朵币"
  itemId: 6
  itemNum: 30
  groupId: 3
  weight: 20
  limit: 1
}
rows {
  rewardId: 100000215
  poolId: 10000022
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 50
  limit: 1
}
rows {
  rewardId: 130003501
  poolId: 13000350
  name: "年年有余头饰"
  itemId: 630472
  itemNum: 1
  groupId: 2
  weight: 39
  limit: 1
}
rows {
  rewardId: 130003502
  poolId: 13000350
  name: "钓了个鱼"
  itemId: 240213
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003503
  poolId: 13000350
  name: "大力狼三件套礼包"
  itemId: 310726
  itemNum: 1
  groupId: 3
  weight: 60
  limit: 1
}
rows {
  rewardId: 130003504
  poolId: 13000351
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003505
  poolId: 13000351
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003506
  poolId: 13000351
  name: "打call*5"
  itemId: 240413
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003507
  poolId: 13000351
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003508
  poolId: 13000351
  name: "红包*5"
  itemId: 240423
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003509
  poolId: 13000351
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003510
  poolId: 13000351
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003511
  poolId: 13000352
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003512
  poolId: 13000352
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003513
  poolId: 13000352
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003514
  poolId: 13000352
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003515
  poolId: 13000352
  name: "小猪撞击*5"
  itemId: 240410
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003516
  poolId: 13000352
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003517
  poolId: 13000352
  name: "身份卡*3"
  itemId: 200102
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003518
  poolId: 13000353
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003519
  poolId: 13000353
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003520
  poolId: 13000353
  name: "新年快乐*5"
  itemId: 240422
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003521
  poolId: 13000353
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003522
  poolId: 13000353
  name: "永恒之恋*5"
  itemId: 240409
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003523
  poolId: 13000353
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003524
  poolId: 13000353
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003601
  poolId: 13000360
  name: "金光火眼"
  itemId: 630473
  itemNum: 1
  groupId: 2
  weight: 39
  limit: 1
}
rows {
  rewardId: 130003602
  poolId: 13000360
  name: "花样滑冰"
  itemId: 240013
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003603
  poolId: 13000360
  name: "新年大礼包"
  itemId: 310727
  itemNum: 1
  groupId: 3
  weight: 60
  limit: 1
}
rows {
  rewardId: 130003604
  poolId: 13000361
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003605
  poolId: 13000361
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003606
  poolId: 13000361
  name: "打call*5"
  itemId: 240413
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003607
  poolId: 13000361
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003608
  poolId: 13000361
  name: "红包*5"
  itemId: 240423
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003609
  poolId: 13000361
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003610
  poolId: 13000361
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003611
  poolId: 13000362
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003612
  poolId: 13000362
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003613
  poolId: 13000362
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003614
  poolId: 13000362
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003615
  poolId: 13000362
  name: "小猪撞击*5"
  itemId: 240410
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003616
  poolId: 13000362
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003617
  poolId: 13000362
  name: "身份卡*3"
  itemId: 200102
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003618
  poolId: 13000363
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003619
  poolId: 13000363
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003620
  poolId: 13000363
  name: "新年快乐*5"
  itemId: 240422
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003621
  poolId: 13000363
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003622
  poolId: 13000363
  name: "永恒之恋*5"
  itemId: 240409
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003623
  poolId: 13000363
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003624
  poolId: 13000363
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000000231
  poolId: 10000023
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000000232
  poolId: 10000023
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000000233
  poolId: 10000023
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000000234
  poolId: 10000023
  name: "靴中百合"
  itemId: 620758
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000000235
  poolId: 10000023
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 1000000236
  poolId: 10000023
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000000237
  poolId: 10000023
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000000238
  poolId: 10000023
  name: "星夜魔法"
  itemId: 219301
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000000239
  poolId: 10000023
  name: "梦幻萌宠屋"
  itemId: 218160
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100002001
  poolId: 1000020
  name: "倒咖啡"
  itemId: 240406
  itemNum: 10
  groupId: 4
  weight: 20
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100002002
  poolId: 1000020
  name: "新年加油打CALL头饰"
  itemId: 630498
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
}
rows {
  rewardId: 100002003
  poolId: 1000020
  name: "狼人币"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 20
  limit: 1
}
rows {
  rewardId: 100002004
  poolId: 1000020
  name: "玫瑰999"
  itemId: 240409
  itemNum: 5
  groupId: 3
  weight: 15
  limit: 1
}
rows {
  rewardId: 100002005
  poolId: 1000020
  name: "狐狸甩手舞动作(甩爪爪)"
  itemId: 720984
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100002006
  poolId: 1000020
  name: "好运上上签"
  itemId: 861017
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100002007
  poolId: 1000020
  name: "莱恩特"
  itemId: 410170
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001111
  poolId: 60001111
  name: "绝世舞姬 貂蝉"
  itemId: 404640
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001112
  poolId: 60001111
  name: "炫光应援"
  itemId: 620730
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001113
  poolId: 60001111
  name: "花月喧天"
  itemId: 720966
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001114
  poolId: 60001111
  name: "宝宝心里苦"
  itemId: 711320
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001115
  poolId: 60001111
  name: "纤尘不染"
  itemId: 870039
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001116
  poolId: 60001111
  name: "峡谷币*150"
  itemId: 3541
  itemNum: 150
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001117
  poolId: 60001111
  name: "花舞貂蝉"
  itemId: 860180
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001118
  poolId: 60001111
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001119
  poolId: 60001111
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001120
  poolId: 60001112
  name: "冰雪之华 王昭君"
  itemId: 403780
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001121
  poolId: 60001112
  name: "冰霜法杖"
  itemId: 640079
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001122
  poolId: 60001112
  name: "凌霜傲雪"
  itemId: 720967
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001123
  poolId: 60001112
  name: "享用刨冰"
  itemId: 711317
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001124
  poolId: 60001112
  name: "冰雪之华"
  itemId: 870040
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001125
  poolId: 60001112
  name: "峡谷币*150"
  itemId: 3541
  itemNum: 150
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001126
  poolId: 60001112
  name: "王昭君头像"
  itemId: 860181
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001127
  poolId: 60001112
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001128
  poolId: 60001112
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100002102
  poolId: 1000021
  name: "倒咖啡*20"
  itemId: 240406
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002103
  poolId: 1000021
  name: "狼人币*400"
  itemId: 13
  itemNum: 400
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002101
  poolId: 1000021
  name: "大锤击飞"
  itemId: 240203
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100002106
  poolId: 1000021
  name: "保镖眼镜：暗影凝视"
  itemId: 610253
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002105
  poolId: 1000021
  name: "新年快乐*10"
  itemId: 240422
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002104
  poolId: 1000021
  name: "贴符*10"
  itemId: 240417
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002203
  poolId: 1000022
  name: "丢盲盒*10"
  itemId: 240418
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002204
  poolId: 1000022
  name: "手表麻醉针*10"
  itemId: 240421
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002201
  poolId: 1000022
  name: "敲锣打鼓"
  itemId: 240004
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100002206
  poolId: 1000022
  name: "馋哭了表情"
  itemId: 711032
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002205
  poolId: 1000022
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002202
  poolId: 1000022
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002303
  poolId: 1000023
  name: "四选1表情"
  itemId: 331014
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002304
  poolId: 1000023
  name: "手表麻醉针*10"
  itemId: 240421
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002301
  poolId: 1000023
  name: "小猪冲锋"
  itemId: 240208
  itemNum: 1
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100002305
  poolId: 1000023
  name: "新年快乐*10"
  itemId: 240422
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002306
  poolId: 1000023
  name: "身份卡/阵营卡包*1"
  itemId: 310287
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002302
  poolId: 1000023
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002402
  poolId: 1000024
  name: "疯狂点赞*20"
  itemId: 240416
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002405
  poolId: 1000024
  name: "新年快乐*10"
  itemId: 240422
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002401
  poolId: 1000024
  name: "时空特警"
  itemId: 240007
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100002404
  poolId: 1000024
  name: "庆祝 表情"
  itemId: 240627
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002406
  poolId: 1000024
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002403
  poolId: 1000024
  name: "打call*10"
  itemId: 240413
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130003701
  poolId: 13000370
  name: "告白熊梦幻屋"
  itemId: 218115
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003702
  poolId: 13000370
  name: "蔷薇花车"
  itemId: 218119
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003703
  poolId: 13000370
  name: "彩虹牧场"
  itemId: 218120
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003704
  poolId: 13000370
  name: "梦幻海洋屋"
  itemId: 218121
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003705
  poolId: 13000370
  name: "云朵奶油时钟"
  itemId: 218118
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003706
  poolId: 13000370
  name: "农田小飞侠"
  itemId: 630339
  itemNum: 1
  groupId: 6
  weight: 30
  limit: 1
}
rows {
  rewardId: 130003707
  poolId: 13000370
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003708
  poolId: 13000370
  name: "幸运币*15"
  itemId: 3
  itemNum: 15
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003709
  poolId: 13000370
  name: "小熊趴趴背包"
  itemId: 620461
  itemNum: 1
  groupId: 6
  weight: 70
  limit: 1
}
rows {
  rewardId: 130003710
  poolId: 13000370
  name: "幸运币*5"
  itemId: 3
  itemNum: 5
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003711
  poolId: 13000370
  name: "精品磷虾*8"
  itemId: 219000
  itemNum: 8
  groupId: 5
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003712
  poolId: 13000370
  name: "精品磷虾*16"
  itemId: 219000
  itemNum: 16
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003713
  poolId: 13000371
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003714
  poolId: 13000371
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003715
  poolId: 13000371
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003716
  poolId: 13000371
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003717
  poolId: 13000371
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003718
  poolId: 13000372
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003719
  poolId: 13000372
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003720
  poolId: 13000372
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003721
  poolId: 13000372
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003722
  poolId: 13000372
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003723
  poolId: 13000373
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003724
  poolId: 13000373
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003725
  poolId: 13000373
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003726
  poolId: 13000373
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003727
  poolId: 13000373
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003728
  poolId: 13000374
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003729
  poolId: 13000374
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003730
  poolId: 13000374
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003731
  poolId: 13000374
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003732
  poolId: 13000374
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003733
  poolId: 13000375
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003734
  poolId: 13000375
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003735
  poolId: 13000375
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003736
  poolId: 13000375
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003737
  poolId: 13000375
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003738
  poolId: 13000376
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003739
  poolId: 13000376
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003740
  poolId: 13000376
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003741
  poolId: 13000376
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003742
  poolId: 13000376
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003743
  poolId: 13000377
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003744
  poolId: 13000377
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003745
  poolId: 13000377
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003746
  poolId: 13000377
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003747
  poolId: 13000377
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003748
  poolId: 13000378
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003749
  poolId: 13000378
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003750
  poolId: 13000378
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003751
  poolId: 13000378
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003752
  poolId: 13000378
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003753
  poolId: 13000379
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003754
  poolId: 13000379
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003755
  poolId: 13000379
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003756
  poolId: 13000379
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003757
  poolId: 13000379
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003758
  poolId: 13000380
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003759
  poolId: 13000380
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003760
  poolId: 13000380
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003761
  poolId: 13000380
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003762
  poolId: 13000380
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003763
  poolId: 13000381
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003764
  poolId: 13000381
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003765
  poolId: 13000381
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003766
  poolId: 13000381
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003767
  poolId: 13000381
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003768
  poolId: 13000382
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003769
  poolId: 13000382
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003770
  poolId: 13000382
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003771
  poolId: 13000382
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003772
  poolId: 13000382
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 8
  limit: 99999
}
rows {
  rewardId: 130003901
  poolId: 13000390
  name: "烟雨小筑"
  itemId: 218161
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003902
  poolId: 13000390
  name: "桃坞小摊自选礼盒"
  itemId: 331018
  itemNum: 1
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003903
  poolId: 13000390
  name: "桃坞小摊自选礼盒"
  itemId: 331018
  itemNum: 1
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003904
  poolId: 13000390
  name: "桃坞小摊自选礼盒"
  itemId: 331018
  itemNum: 1
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003905
  poolId: 13000390
  name: "悠悠亭"
  itemId: 218166
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003906
  poolId: 13000390
  name: "日晷"
  itemId: 218167
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003907
  poolId: 13000390
  name: "花语叉"
  itemId: 620809
  itemNum: 1
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130003908
  poolId: 13000390
  name: "头像框-徽派"
  itemId: 840254
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003909
  poolId: 13000390
  name: "头像-徽派"
  itemId: 860182
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130003910
  poolId: 13000390
  name: "趴趴水豚"
  itemId: 630537
  itemNum: 1
  groupId: 4
  weight: 105
  limit: 1
}
rows {
  rewardId: 130003911
  poolId: 13000391
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003912
  poolId: 13000391
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003913
  poolId: 13000391
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003914
  poolId: 13000391
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003915
  poolId: 13000391
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003916
  poolId: 13000391
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003917
  poolId: 13000391
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003918
  poolId: 13000392
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003919
  poolId: 13000392
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003920
  poolId: 13000392
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003921
  poolId: 13000392
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003922
  poolId: 13000392
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003923
  poolId: 13000392
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003924
  poolId: 13000392
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003925
  poolId: 13000393
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003926
  poolId: 13000393
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003927
  poolId: 13000393
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003928
  poolId: 13000393
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003929
  poolId: 13000393
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003930
  poolId: 13000393
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003931
  poolId: 13000393
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003932
  poolId: 13000394
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003933
  poolId: 13000394
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003934
  poolId: 13000394
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003935
  poolId: 13000394
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003936
  poolId: 13000394
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003937
  poolId: 13000394
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003938
  poolId: 13000394
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003939
  poolId: 13000395
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003940
  poolId: 13000395
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003941
  poolId: 13000395
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003942
  poolId: 13000395
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003943
  poolId: 13000395
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003944
  poolId: 13000395
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003945
  poolId: 13000395
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003946
  poolId: 13000396
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003947
  poolId: 13000396
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003948
  poolId: 13000396
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003949
  poolId: 13000396
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003950
  poolId: 13000396
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003951
  poolId: 13000396
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003952
  poolId: 13000396
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003953
  poolId: 13000397
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003954
  poolId: 13000397
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003955
  poolId: 13000397
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003956
  poolId: 13000397
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003957
  poolId: 13000397
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003958
  poolId: 13000397
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003959
  poolId: 13000397
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003960
  poolId: 13000398
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003961
  poolId: 13000398
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003962
  poolId: 13000398
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003963
  poolId: 13000398
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003964
  poolId: 13000398
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003965
  poolId: 13000398
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003966
  poolId: 13000398
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003967
  poolId: 13000399
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003968
  poolId: 13000399
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003969
  poolId: 13000399
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003970
  poolId: 13000399
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003971
  poolId: 13000399
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003972
  poolId: 13000399
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003973
  poolId: 13000399
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130003974
  poolId: 13000400
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130003975
  poolId: 13000400
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003976
  poolId: 13000400
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003977
  poolId: 13000400
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003978
  poolId: 13000400
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003979
  poolId: 13000400
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130003980
  poolId: 13000400
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 600011201
  poolId: 6000112
  name: "冰雪圆舞曲 甄姬"
  itemId: 410330
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600011202
  poolId: 6000112
  name: "霓裳风华 杨玉环"
  itemId: 410320
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600011203
  poolId: 6000112
  name: "永冻蔷薇"
  itemId: 630522
  itemNum: 1
  groupId: 2
  weight: 2
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600011204
  poolId: 6000112
  name: "长乐未央"
  itemId: 620784
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600011205
  poolId: 6000112
  name: "冰雪邀约"
  itemId: 720972
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600011206
  poolId: 6000112
  name: "蓬莱飞仙"
  itemId: 720973
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600011207
  poolId: 6000112
  name: "滚雪球啰"
  itemId: 711376
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600011208
  poolId: 6000112
  name: "犹抱琵琶"
  itemId: 711374
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600011209
  poolId: 6000112
  name: "峡谷券*5"
  itemId: 218
  itemNum: 5
  groupId: 5
  weight: 4
  comboItemIds: 320063
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600011210
  poolId: 6000112
  name: "峡谷券*3"
  itemId: 218
  itemNum: 3
  groupId: 5
  weight: 11
  comboItemIds: 320062
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600011211
  poolId: 6000112
  name: "峡谷券*2"
  itemId: 218
  itemNum: 2
  groupId: 5
  weight: 85
  comboItemIds: 320061
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600011212
  poolId: 6000112
  name: "峡谷币*10"
  itemId: 3541
  itemNum: 10
  groupId: 6
  weight: 1
  limit: 20
}
rows {
  rewardId: 600011213
  poolId: 6000112
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600011214
  poolId: 6000112
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600011215
  poolId: 6000112
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600011216
  poolId: 6000112
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600011217
  poolId: 6000112
  name: "音浪鼓舞"
  itemId: 720946
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011218
  poolId: 6000112
  name: "键不离手"
  itemId: 720947
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011219
  poolId: 6000112
  name: "看好你哟"
  itemId: 711316
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011220
  poolId: 6000112
  name: "大脑宕机"
  itemId: 711312
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011221
  poolId: 6000112
  name: "天才亮相"
  itemId: 720891
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011222
  poolId: 6000112
  name: "剑圣奥义"
  itemId: 720897
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011223
  poolId: 6000112
  name: "终极表演"
  itemId: 711311
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011224
  poolId: 6000112
  name: "无敌寂寞"
  itemId: 711236
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011225
  poolId: 6000112
  name: "赛博行者"
  itemId: 720834
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011226
  poolId: 6000112
  name: "超时空追击"
  itemId: 720825
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011227
  poolId: 6000112
  name: "不耐烦"
  itemId: 711140
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011228
  poolId: 6000112
  name: "正义制裁"
  itemId: 711139
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011229
  poolId: 6000112
  name: "异界灵契"
  itemId: 720819
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011230
  poolId: 6000112
  name: "挚爱之约"
  itemId: 720758
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011231
  poolId: 6000112
  name: "初次公演"
  itemId: 720777
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011232
  poolId: 6000112
  name: "灵动之舞"
  itemId: 720776
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011233
  poolId: 6000112
  name: "龙胆现世"
  itemId: 720733
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011234
  poolId: 6000112
  name: "追逃游戏"
  itemId: 720735
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011235
  poolId: 6000112
  name: "心动节拍"
  itemId: 720680
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011236
  poolId: 6000112
  name: "天翔影龙"
  itemId: 720681
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011237
  poolId: 6000112
  name: "轻松解决"
  itemId: 711074
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011238
  poolId: 6000112
  name: "为爱出发"
  itemId: 711138
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011239
  poolId: 6000112
  name: "魅力偶像"
  itemId: 711137
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011240
  poolId: 6000112
  name: "程序启动"
  itemId: 711132
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011241
  poolId: 6000112
  name: "龙魂再起"
  itemId: 711062
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 600011242
  poolId: 6000112
  name: "古灵精怪"
  itemId: 711061
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 26
    limit: 1
  }
}
rows {
  rewardId: 100000251
  poolId: 10000025
  name: "幽灵巫师"
  itemId: 219302
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000252
  poolId: 10000025
  name: "爱心宝宝巾"
  itemId: 219605
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000253
  poolId: 10000025
  name: "啃啃乐"
  itemId: 620597
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000254
  poolId: 10000025
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000255
  poolId: 10000025
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000256
  poolId: 10000025
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000257
  poolId: 10000025
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000258
  poolId: 10000025
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000259
  poolId: 10000025
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000260
  poolId: 10000026
  name: "收获日"
  itemId: 219300
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000261
  poolId: 10000026
  name: "桃之夭夭"
  itemId: 219403
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000262
  poolId: 10000026
  name: "野营小精灵"
  itemId: 620694
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000263
  poolId: 10000026
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000264
  poolId: 10000026
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000265
  poolId: 10000026
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000266
  poolId: 10000026
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000267
  poolId: 10000026
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000268
  poolId: 10000026
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 130004101
  poolId: 13000410
  name: "伪装特效：水墨"
  itemId: 241207
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 130004102
  poolId: 13000410
  name: "萌动甜点"
  itemId: 241406
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004103
  poolId: 13000410
  name: "暗影黑喵"
  itemId: 610331
  itemNum: 1
  groupId: 2
  weight: 30
  limit: 1
}
rows {
  rewardId: 130004104
  poolId: 13000411
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004105
  poolId: 13000411
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004106
  poolId: 13000411
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004107
  poolId: 13000411
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004108
  poolId: 13000411
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004109
  poolId: 13000411
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004110
  poolId: 13000411
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004111
  poolId: 13000412
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004112
  poolId: 13000412
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004113
  poolId: 13000412
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004114
  poolId: 13000412
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004115
  poolId: 13000412
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004116
  poolId: 13000412
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004117
  poolId: 13000412
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004118
  poolId: 13000413
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004119
  poolId: 13000413
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004120
  poolId: 13000413
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004121
  poolId: 13000413
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004122
  poolId: 13000413
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004123
  poolId: 13000413
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004124
  poolId: 13000413
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004201
  poolId: 13000420
  name: "伪装特效：彩虹"
  itemId: 241208
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 130004202
  poolId: 13000420
  name: "量子魔方"
  itemId: 241407
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004203
  poolId: 13000420
  name: "勤劳小蜂"
  itemId: 630529
  itemNum: 1
  groupId: 2
  weight: 30
  limit: 1
}
rows {
  rewardId: 130004204
  poolId: 13000421
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004205
  poolId: 13000421
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004206
  poolId: 13000421
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004207
  poolId: 13000421
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004208
  poolId: 13000421
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004209
  poolId: 13000421
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004210
  poolId: 13000421
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004211
  poolId: 13000422
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004212
  poolId: 13000422
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004213
  poolId: 13000422
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004214
  poolId: 13000422
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004215
  poolId: 13000422
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004216
  poolId: 13000422
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004217
  poolId: 13000422
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004218
  poolId: 13000423
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004219
  poolId: 13000423
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004220
  poolId: 13000423
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004221
  poolId: 13000423
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004222
  poolId: 13000423
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004223
  poolId: 13000423
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004224
  poolId: 13000423
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004301
  poolId: 13000430
  name: "快乐涮涮屋"
  itemId: 218171
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004302
  poolId: 13000430
  name: "珍馐小摊自选礼盒"
  itemId: 331019
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130004303
  poolId: 13000430
  name: "珍馐小摊自选礼盒"
  itemId: 331019
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130004304
  poolId: 13000430
  name: "珍馐小摊自选礼盒"
  itemId: 331019
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130004305
  poolId: 13000430
  name: "果冻树莓"
  itemId: 630553
  itemNum: 1
  groupId: 5
  weight: 30
  limit: 1
}
rows {
  rewardId: 130004306
  poolId: 13000430
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130004307
  poolId: 13000430
  name: "切菜板板"
  itemId: 620769
  itemNum: 1
  groupId: 5
  weight: 70
  limit: 1
}
rows {
  rewardId: 130004308
  poolId: 13000430
  name: "幸运币*5"
  itemId: 3
  itemNum: 5
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130004309
  poolId: 13000430
  name: "珍馐百味头像"
  itemId: 860189
  itemNum: 1
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130004310
  poolId: 13000430
  name: "饿成丸子"
  itemId: 711379
  itemNum: 1
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130004311
  poolId: 13000431
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004312
  poolId: 13000431
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004313
  poolId: 13000431
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004314
  poolId: 13000431
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004315
  poolId: 13000431
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004316
  poolId: 13000431
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004317
  poolId: 13000431
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004318
  poolId: 13000432
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004319
  poolId: 13000432
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004320
  poolId: 13000432
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004321
  poolId: 13000432
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004322
  poolId: 13000432
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004323
  poolId: 13000432
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004324
  poolId: 13000432
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004325
  poolId: 13000433
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004326
  poolId: 13000433
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004327
  poolId: 13000433
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004328
  poolId: 13000433
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004329
  poolId: 13000433
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004330
  poolId: 13000433
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004331
  poolId: 13000433
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004332
  poolId: 13000434
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004333
  poolId: 13000434
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004334
  poolId: 13000434
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004335
  poolId: 13000434
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004336
  poolId: 13000434
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004337
  poolId: 13000434
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004338
  poolId: 13000434
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004339
  poolId: 13000435
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004340
  poolId: 13000435
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004341
  poolId: 13000435
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004342
  poolId: 13000435
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004343
  poolId: 13000435
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004344
  poolId: 13000435
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004345
  poolId: 13000435
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004346
  poolId: 13000436
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004347
  poolId: 13000436
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004348
  poolId: 13000436
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004349
  poolId: 13000436
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004350
  poolId: 13000436
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004351
  poolId: 13000436
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004352
  poolId: 13000436
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004353
  poolId: 13000437
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004354
  poolId: 13000437
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004355
  poolId: 13000437
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004356
  poolId: 13000437
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004357
  poolId: 13000437
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004358
  poolId: 13000437
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004359
  poolId: 13000437
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004360
  poolId: 13000438
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004361
  poolId: 13000438
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004362
  poolId: 13000438
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004363
  poolId: 13000438
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004364
  poolId: 13000438
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004365
  poolId: 13000438
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004366
  poolId: 13000438
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004367
  poolId: 13000439
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004368
  poolId: 13000439
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004369
  poolId: 13000439
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004370
  poolId: 13000439
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004371
  poolId: 13000439
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004372
  poolId: 13000439
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004373
  poolId: 13000439
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004374
  poolId: 13000440
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004375
  poolId: 13000440
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004376
  poolId: 13000440
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004377
  poolId: 13000440
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004378
  poolId: 13000440
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004379
  poolId: 13000440
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004380
  poolId: 13000440
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 100002602
  poolId: 1000026
  name: "倒咖啡*20"
  itemId: 240406
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002603
  poolId: 1000026
  name: "狼人币*400"
  itemId: 13
  itemNum: 400
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002601
  poolId: 1000026
  name: "阵营卡*8"
  itemId: 200101
  itemNum: 8
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002604
  poolId: 1000026
  name: "深水炸弹舞蹈a"
  itemId: 720994
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002605
  poolId: 1000026
  name: "小猪撞击*10"
  itemId: 240410
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002606
  poolId: 1000026
  name: "我胆子小"
  itemId: 240006
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100002702
  poolId: 1000027
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002703
  poolId: 1000027
  name: "丢盲盒*10"
  itemId: 240418
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002701
  poolId: 1000027
  name: "身份卡*4"
  itemId: 200102
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002704
  poolId: 1000027
  name: "深水炸弹舞蹈b"
  itemId: 720995
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002705
  poolId: 1000027
  name: "手表麻醉针*10"
  itemId: 240421
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100002706
  poolId: 1000027
  name: "遥控坦克"
  itemId: 240209
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001131
  poolId: 60001131
  name: "绝代智谋 诸葛亮"
  itemId: 410760
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001132
  poolId: 60001131
  name: "天机羽扇"
  itemId: 640143
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001133
  poolId: 60001131
  name: "参透天机"
  itemId: 720977
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
}
rows {
  rewardId: 60001134
  poolId: 60001131
  name: "运筹帷幄"
  itemId: 711375
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001135
  poolId: 60001131
  name: "羽扇清风"
  itemId: 820176
  itemNum: 1
  groupId: 4
  weight: 20
  limit: 1
}
rows {
  rewardId: 60001136
  poolId: 60001131
  name: "诸葛亮英雄礼盒"
  itemId: 310746
  itemNum: 1
  groupId: 2
  weight: 60
  limit: 1
}
rows {
  rewardId: 60001137
  poolId: 60001132
  name: "仁德义枪 刘备"
  itemId: 410770
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001138
  poolId: 60001132
  name: "以德服人"
  itemId: 620830
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001139
  poolId: 60001132
  name: "暖男气质"
  itemId: 722025
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
}
rows {
  rewardId: 60001140
  poolId: 60001132
  name: "呆在原地"
  itemId: 711382
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001141
  poolId: 60001132
  name: "民间帝胄"
  itemId: 820177
  itemNum: 1
  groupId: 4
  weight: 20
  limit: 1
}
rows {
  rewardId: 60001142
  poolId: 60001132
  name: "刘备英雄礼盒"
  itemId: 310747
  itemNum: 1
  groupId: 2
  weight: 60
  limit: 1
}
rows {
  rewardId: 100000271
  poolId: 10000028
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000272
  poolId: 10000028
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000273
  poolId: 10000028
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000274
  poolId: 10000028
  name: "樱花雪"
  itemId: 630562
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000275
  poolId: 10000028
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 35
  limit: 1
}
rows {
  rewardId: 100000276
  poolId: 10000028
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 50
  limit: 1
}
rows {
  rewardId: 100000277
  poolId: 10000028
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000278
  poolId: 10000028
  name: "披萨时钟"
  itemId: 218178
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000279
  poolId: 10000028
  name: "狐爷爷"
  itemId: 218183
  itemNum: 1
  groupId: 4
  weight: 15
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000291
  poolId: 10000029
  name: "星宝印章*1000"
  groupId: 5
  weight: 1
  limit: 1
  subRewards {
    itemId: 4
    itemNum: 1000
  }
}
rows {
  rewardId: 100000292
  poolId: 10000029
  name: "云朵币*20"
  groupId: 4
  weight: 1
  limit: 1
  subRewards {
    itemId: 6
    itemNum: 20
  }
}
rows {
  rewardId: 100000293
  poolId: 10000029
  name: "心心宝瓶*2"
  groupId: 5
  weight: 1
  limit: 1
  subRewards {
    itemId: 200016
    itemNum: 2
  }
}
rows {
  rewardId: 100000294
  poolId: 10000029
  name: "大力猫爪时钟;蜜糖饼庭院"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  subRewards {
    itemId: 218191
    itemNum: 1
  }
  subRewards {
    itemId: 218170
    itemNum: 1
  }
}
rows {
  rewardId: 100000295
  poolId: 10000029
  name: "友谊魔法;牛奶管家"
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
  subRewards {
    itemId: 219409
    itemNum: 1
  }
  subRewards {
    itemId: 620849
    itemNum: 1
  }
}
rows {
  rewardId: 100000296
  poolId: 10000029
  name: "咔咔大盗;甜萝卜兔"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
  subRewards {
    itemId: 630570
    itemNum: 1
  }
  subRewards {
    itemId: 219304
    itemNum: 1
  }
}
rows {
  rewardId: 100000297
  poolId: 10000029
  name: "星愿币*2"
  groupId: 4
  weight: 1
  limit: 1
  subRewards {
    itemId: 2
    itemNum: 2
  }
}
rows {
  rewardId: 100000298
  poolId: 10000029
  name: "配饰染色剂*2"
  groupId: 4
  weight: 1
  limit: 1
  subRewards {
    itemId: 200008
    itemNum: 2
  }
}
rows {
  rewardId: 100000299
  poolId: 10000029
  name: "时装染色剂*2"
  groupId: 5
  weight: 1
  limit: 1
  subRewards {
    itemId: 200006
    itemNum: 2
  }
}
rows {
  rewardId: 100000300
  poolId: 10000029
  name: "烟花礼盒*10"
  groupId: 5
  weight: 1
  limit: 1
  subRewards {
    itemId: 725201
    itemNum: 10
  }
}
rows {
  rewardId: 130004501
  poolId: 13000450
  name: "会议表情礼包*1"
  itemId: 310743
  itemNum: 1
  groupId: 2
  weight: 39
  limit: 1
}
rows {
  rewardId: 130004502
  poolId: 13000450
  name: "报告动画：都市疾驰"
  itemId: 240015
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004503
  poolId: 13000450
  name: "狼人精品礼包*1"
  itemId: 310741
  itemNum: 1
  groupId: 3
  weight: 60
  limit: 1
}
rows {
  rewardId: 130004504
  poolId: 13000451
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004505
  poolId: 13000451
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004506
  poolId: 13000451
  name: "打call*5"
  itemId: 240413
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004507
  poolId: 13000451
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004508
  poolId: 13000451
  name: "拳击*5"
  itemId: 240402
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004509
  poolId: 13000451
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004510
  poolId: 13000451
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004511
  poolId: 13000452
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004512
  poolId: 13000452
  name: "狼人币*150"
  itemId: 13
  itemNum: 150
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004513
  poolId: 13000452
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004514
  poolId: 13000452
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004515
  poolId: 13000452
  name: "小猪撞击*5"
  itemId: 240410
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004516
  poolId: 13000452
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004517
  poolId: 13000452
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004518
  poolId: 13000453
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004519
  poolId: 13000453
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004520
  poolId: 13000453
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004521
  poolId: 13000453
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004522
  poolId: 13000453
  name: "永恒之恋*5"
  itemId: 240409
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004523
  poolId: 13000453
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004524
  poolId: 13000453
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004601
  poolId: 13000460
  name: "臭鼬三件套礼包"
  itemId: 310744
  itemNum: 1
  groupId: 2
  weight: 39
  limit: 1
}
rows {
  rewardId: 130004602
  poolId: 13000460
  name: "攻击动画：胖哒出击"
  itemId: 240215
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004603
  poolId: 13000460
  name: "狼人优选礼包*1"
  itemId: 310742
  itemNum: 1
  groupId: 3
  weight: 60
  limit: 1
}
rows {
  rewardId: 130004604
  poolId: 13000461
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004605
  poolId: 13000461
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004606
  poolId: 13000461
  name: "送花*5"
  itemId: 240404
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004607
  poolId: 13000461
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004608
  poolId: 13000461
  name: "贴符*5"
  itemId: 240417
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004609
  poolId: 13000461
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004610
  poolId: 13000461
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004611
  poolId: 13000462
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004612
  poolId: 13000462
  name: "狼人币*150"
  itemId: 13
  itemNum: 150
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004613
  poolId: 13000462
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004614
  poolId: 13000462
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004615
  poolId: 13000462
  name: "小猪撞击*5"
  itemId: 240410
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004616
  poolId: 13000462
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004617
  poolId: 13000462
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004618
  poolId: 13000463
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004619
  poolId: 13000463
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004620
  poolId: 13000463
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004621
  poolId: 13000463
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004622
  poolId: 13000463
  name: "永恒之恋*5"
  itemId: 240409
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004623
  poolId: 13000463
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004624
  poolId: 13000463
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000301
  poolId: 10000030
  name: "颠倒之视"
  itemId: 610369
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000302
  poolId: 10000030
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000303
  poolId: 10000030
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000304
  poolId: 10000031
  name: "蒸蒸日上小窝"
  itemId: 218176
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000305
  poolId: 10000031
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000306
  poolId: 10000031
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000307
  poolId: 10000031
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000308
  poolId: 10000031
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000309
  poolId: 10000031
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000310
  poolId: 10000032
  name: "翡光仙灵庭"
  itemId: 218184
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000311
  poolId: 10000032
  name: "牛马发箍"
  itemId: 630573
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000312
  poolId: 10000032
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000313
  poolId: 10000032
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000314
  poolId: 10000032
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000315
  poolId: 10000032
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000316
  poolId: 10000032
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000317
  poolId: 10000032
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000318
  poolId: 10000032
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003102
  poolId: 1000031
  name: "丢炸弹*20"
  itemId: 240405
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003103
  poolId: 1000031
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003101
  poolId: 1000031
  name: "称号-头号侦探"
  itemId: 861015
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003104
  poolId: 1000031
  name: "表情-完美清晨"
  itemId: 711396
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003105
  poolId: 1000031
  name: "手表麻醉在*10"
  itemId: 240421
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003106
  poolId: 1000031
  name: "巨星登场"
  itemId: 241002
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004700
  poolId: 13000470
  name: "奶油云朵乐园"
  itemId: 218169
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004701
  poolId: 13000470
  name: "布丁狗小窝"
  itemId: 218193
  itemNum: 1
  groupId: 2
  weight: 99
  limit: 1
}
rows {
  rewardId: 130004702
  poolId: 13000470
  name: "快乐小丑"
  itemId: 219305
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 130004703
  poolId: 13000470
  name: "秀秀大鱼"
  itemId: 720844
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130004704
  poolId: 13000470
  name: "逗猫棒"
  itemId: 620892
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130004705
  poolId: 13000470
  name: "汪汪之家"
  itemId: 620891
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004711
  poolId: 13000471
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004712
  poolId: 13000471
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004713
  poolId: 13000471
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004714
  poolId: 13000471
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004715
  poolId: 13000471
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004716
  poolId: 13000471
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004717
  poolId: 13000471
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004721
  poolId: 13000472
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004722
  poolId: 13000472
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004723
  poolId: 13000472
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004724
  poolId: 13000472
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004725
  poolId: 13000472
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004726
  poolId: 13000472
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004727
  poolId: 13000472
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004731
  poolId: 13000473
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004732
  poolId: 13000473
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004733
  poolId: 13000473
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004734
  poolId: 13000473
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004735
  poolId: 13000473
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004736
  poolId: 13000473
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004737
  poolId: 13000473
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004741
  poolId: 13000474
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004742
  poolId: 13000474
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004743
  poolId: 13000474
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004744
  poolId: 13000474
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004745
  poolId: 13000474
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004746
  poolId: 13000474
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004747
  poolId: 13000474
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004751
  poolId: 13000475
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004752
  poolId: 13000475
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004753
  poolId: 13000475
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004754
  poolId: 13000475
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004755
  poolId: 13000475
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004756
  poolId: 13000475
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004757
  poolId: 13000475
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004761
  poolId: 13000476
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004762
  poolId: 13000476
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004763
  poolId: 13000476
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004764
  poolId: 13000476
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004765
  poolId: 13000476
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004766
  poolId: 13000476
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004767
  poolId: 13000476
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 100000331
  poolId: 10000033
  name: "快乐菠萝"
  itemId: 620852
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000332
  poolId: 10000033
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000333
  poolId: 10000033
  name: "星宝印章"
  itemId: 4
  itemNum: 1000
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 100000334
  poolId: 10000034
  name: "猪猪包礼盒"
  itemId: 218179
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000335
  poolId: 10000034
  name: "配饰染色剂"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000336
  poolId: 10000034
  name: "服装染色剂"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000337
  poolId: 10000034
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000338
  poolId: 10000034
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000339
  poolId: 10000035
  name: "煎饼果子超人"
  itemId: 218177
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000340
  poolId: 10000035
  name: "寿司旅行家"
  itemId: 620855
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000341
  poolId: 10000035
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000342
  poolId: 10000035
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 15
  limit: 1
}
rows {
  rewardId: 100000343
  poolId: 10000035
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 30
  limit: 1
}
rows {
  rewardId: 100000344
  poolId: 10000035
  name: "云朵币"
  itemId: 6
  itemNum: 30
  groupId: 3
  weight: 20
  limit: 1
}
rows {
  rewardId: 100000345
  poolId: 10000035
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 50
  limit: 1
}
rows {
  rewardId: 100000361
  poolId: 10000036
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000362
  poolId: 10000036
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000363
  poolId: 10000036
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 100000364
  poolId: 10000036
  name: "丰收浇灌者"
  itemId: 630343
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000365
  poolId: 10000036
  name: "仙人掌花屋"
  itemId: 218116
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000366
  poolId: 10000036
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000367
  poolId: 10000036
  name: "精品磷虾"
  itemId: 219000
  itemNum: 4
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000368
  poolId: 10000036
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 100000369
  poolId: 10000036
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 600011401
  poolId: 6000114
  name: "沧海之曜 大乔"
  itemId: 410940
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600011402
  poolId: 6000114
  name: "浮梦罗烟 海月"
  itemId: 410930
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600011403
  poolId: 6000114
  name: "望海长明"
  itemId: 620848
  itemNum: 1
  groupId: 2
  weight: 2
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600011404
  poolId: 6000114
  name: "玲珑宝器"
  itemId: 630569
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 600011405
  poolId: 6000114
  name: "鱼跃鸢飞"
  itemId: 720968
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600011406
  poolId: 6000114
  name: "罗烟海市"
  itemId: 722028
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600011407
  poolId: 6000114
  name: "安心摸鱼"
  itemId: 711373
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600011408
  poolId: 6000114
  name: "雅韵悠长"
  itemId: 711490
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 600011409
  poolId: 6000114
  name: "峡谷券*5"
  itemId: 218
  itemNum: 5
  groupId: 5
  weight: 4
  comboItemIds: 320063
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600011410
  poolId: 6000114
  name: "峡谷券*3"
  itemId: 218
  itemNum: 3
  groupId: 5
  weight: 11
  comboItemIds: 320062
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600011411
  poolId: 6000114
  name: "峡谷券*2"
  itemId: 218
  itemNum: 2
  groupId: 5
  weight: 85
  comboItemIds: 320061
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 600011412
  poolId: 6000114
  name: "峡谷币*10"
  itemId: 3541
  itemNum: 10
  groupId: 6
  weight: 1
  limit: 20
}
rows {
  rewardId: 600011413
  poolId: 6000114
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600011414
  poolId: 6000114
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600011415
  poolId: 6000114
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600011416
  poolId: 6000114
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 600011417
  poolId: 6000114
  name: "冰雪邀约"
  itemId: 720972
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011418
  poolId: 6000114
  name: "蓬莱飞仙"
  itemId: 720973
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011419
  poolId: 6000114
  name: "滚雪球啰"
  itemId: 711376
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011420
  poolId: 6000114
  name: "犹抱琵琶"
  itemId: 711374
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011421
  poolId: 6000114
  name: "音浪鼓舞"
  itemId: 720946
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011422
  poolId: 6000114
  name: "键不离手"
  itemId: 720947
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011423
  poolId: 6000114
  name: "看好你哟"
  itemId: 711316
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011424
  poolId: 6000114
  name: "大脑宕机"
  itemId: 711312
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011425
  poolId: 6000114
  name: "天才亮相"
  itemId: 720891
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011426
  poolId: 6000114
  name: "剑圣奥义"
  itemId: 720897
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011427
  poolId: 6000114
  name: "终极表演"
  itemId: 711311
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011428
  poolId: 6000114
  name: "无敌寂寞"
  itemId: 711236
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011429
  poolId: 6000114
  name: "赛博行者"
  itemId: 720834
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011430
  poolId: 6000114
  name: "超时空追击"
  itemId: 720825
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011431
  poolId: 6000114
  name: "不耐烦"
  itemId: 711140
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011432
  poolId: 6000114
  name: "正义制裁"
  itemId: 711139
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011433
  poolId: 6000114
  name: "异界灵契"
  itemId: 720819
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011434
  poolId: 6000114
  name: "挚爱之约"
  itemId: 720758
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011435
  poolId: 6000114
  name: "初次公演"
  itemId: 720777
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011436
  poolId: 6000114
  name: "灵动之舞"
  itemId: 720776
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011437
  poolId: 6000114
  name: "龙胆现世"
  itemId: 720733
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011438
  poolId: 6000114
  name: "追逃游戏"
  itemId: 720735
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011439
  poolId: 6000114
  name: "心动节拍"
  itemId: 720680
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011440
  poolId: 6000114
  name: "天翔影龙"
  itemId: 720681
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011441
  poolId: 6000114
  name: "轻松解决"
  itemId: 711074
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011442
  poolId: 6000114
  name: "为爱出发"
  itemId: 711138
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011443
  poolId: 6000114
  name: "魅力偶像"
  itemId: 711137
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011444
  poolId: 6000114
  name: "程序启动"
  itemId: 711132
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011445
  poolId: 6000114
  name: "龙魂再起"
  itemId: 711062
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 600011446
  poolId: 6000114
  name: "古灵精怪"
  itemId: 711061
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  inGroupLimit {
    period: 30
    limit: 1
  }
}
rows {
  rewardId: 130004801
  poolId: 13000480
  name: "蜜桃猫星星杯"
  itemId: 218185
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004802
  poolId: 13000480
  name: "蜜桃猫星礼盒"
  itemId: 218186
  itemNum: 1
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130004803
  poolId: 13000480
  name: "便携蜜桃猫"
  itemId: 620895
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 130004804
  poolId: 13000480
  name: "灰灰礼盒"
  itemId: 630594
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 130004805
  poolId: 13000481
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004806
  poolId: 13000481
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004807
  poolId: 13000481
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004808
  poolId: 13000481
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004809
  poolId: 13000481
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004810
  poolId: 13000481
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004811
  poolId: 13000481
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004812
  poolId: 13000481
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004813
  poolId: 13000482
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004814
  poolId: 13000482
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004815
  poolId: 13000482
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004816
  poolId: 13000482
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004817
  poolId: 13000482
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004818
  poolId: 13000482
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004819
  poolId: 13000482
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004820
  poolId: 13000482
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004821
  poolId: 13000483
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004822
  poolId: 13000483
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004823
  poolId: 13000483
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004824
  poolId: 13000483
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004825
  poolId: 13000483
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004826
  poolId: 13000483
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004827
  poolId: 13000483
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004828
  poolId: 13000483
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004829
  poolId: 13000484
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004830
  poolId: 13000484
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004831
  poolId: 13000484
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004832
  poolId: 13000484
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004833
  poolId: 13000484
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004834
  poolId: 13000484
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004835
  poolId: 13000484
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004836
  poolId: 13000484
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130004901
  poolId: 13000490
  name: "绮丽海螺城堡"
  itemId: 218123
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004902
  poolId: 13000490
  name: "蔚海绮梦小摊自选礼盒"
  itemId: 331005
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130004903
  poolId: 13000490
  name: "蔚海绮梦小摊自选礼盒"
  itemId: 331005
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130004904
  poolId: 13000490
  name: "蔚海绮梦小摊自选礼盒"
  itemId: 331005
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130004905
  poolId: 13000490
  name: "深海时钟"
  itemId: 218128
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 130004906
  poolId: 13000490
  name: "奇珍"
  itemId: 630376
  itemNum: 1
  groupId: 6
  weight: 30
  limit: 1
}
rows {
  rewardId: 130004907
  poolId: 13000490
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130004908
  poolId: 13000490
  name: "幸运币*15"
  itemId: 3
  itemNum: 15
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130004909
  poolId: 13000490
  name: "钓鱼全能王"
  itemId: 630344
  itemNum: 1
  groupId: 6
  weight: 70
  limit: 1
}
rows {
  rewardId: 130004910
  poolId: 13000490
  name: "幸运币*5"
  itemId: 3
  itemNum: 5
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130004911
  poolId: 13000490
  name: "精品磷虾*8"
  itemId: 219000
  itemNum: 8
  groupId: 5
  weight: 95
  limit: 1
}
rows {
  rewardId: 130004912
  poolId: 13000490
  name: "精品磷虾*16"
  itemId: 219000
  itemNum: 16
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130004913
  poolId: 13000491
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004914
  poolId: 13000491
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004915
  poolId: 13000491
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004916
  poolId: 13000491
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004917
  poolId: 13000491
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004918
  poolId: 13000491
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004919
  poolId: 13000491
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004920
  poolId: 13000492
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004921
  poolId: 13000492
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004922
  poolId: 13000492
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004923
  poolId: 13000492
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004924
  poolId: 13000492
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004925
  poolId: 13000492
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004926
  poolId: 13000492
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004927
  poolId: 13000493
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004928
  poolId: 13000493
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004929
  poolId: 13000493
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004930
  poolId: 13000493
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004931
  poolId: 13000493
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004932
  poolId: 13000493
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004933
  poolId: 13000493
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004934
  poolId: 13000494
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004935
  poolId: 13000494
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004936
  poolId: 13000494
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004937
  poolId: 13000494
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004938
  poolId: 13000494
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004939
  poolId: 13000494
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004940
  poolId: 13000494
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004941
  poolId: 13000495
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004942
  poolId: 13000495
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004943
  poolId: 13000495
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004944
  poolId: 13000495
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004945
  poolId: 13000495
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004946
  poolId: 13000495
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004947
  poolId: 13000495
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004948
  poolId: 13000496
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004949
  poolId: 13000496
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004950
  poolId: 13000496
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004951
  poolId: 13000496
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004952
  poolId: 13000496
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004953
  poolId: 13000496
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004954
  poolId: 13000496
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004955
  poolId: 13000497
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004956
  poolId: 13000497
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004957
  poolId: 13000497
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004958
  poolId: 13000497
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004959
  poolId: 13000497
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004960
  poolId: 13000497
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004961
  poolId: 13000497
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004962
  poolId: 13000498
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004963
  poolId: 13000498
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004964
  poolId: 13000498
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004965
  poolId: 13000498
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004966
  poolId: 13000498
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004967
  poolId: 13000498
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004968
  poolId: 13000498
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004969
  poolId: 13000499
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004970
  poolId: 13000499
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004971
  poolId: 13000499
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004972
  poolId: 13000499
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004973
  poolId: 13000499
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004974
  poolId: 13000499
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004975
  poolId: 13000499
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004976
  poolId: 13000500
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004977
  poolId: 13000500
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004978
  poolId: 13000500
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004979
  poolId: 13000500
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004980
  poolId: 13000500
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004981
  poolId: 13000500
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004982
  poolId: 13000500
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004983
  poolId: 13000501
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004984
  poolId: 13000501
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004985
  poolId: 13000501
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004986
  poolId: 13000501
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004987
  poolId: 13000501
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004988
  poolId: 13000501
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004989
  poolId: 13000501
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130004990
  poolId: 13000502
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130004991
  poolId: 13000502
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004992
  poolId: 13000502
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004993
  poolId: 13000502
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004994
  poolId: 13000502
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004995
  poolId: 13000502
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130004996
  poolId: 13000502
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005101
  poolId: 13000510
  name: "真相永远只有一个"
  itemId: 240011
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005102
  poolId: 13000510
  name: "二选一礼包"
  itemId: 331013
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 130005103
  poolId: 13000510
  name: "丢雷动作表情"
  itemId: 711313
  itemNum: 1
  groupId: 3
  weight: 79
  limit: 1
}
rows {
  rewardId: 130005104
  poolId: 13000511
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005105
  poolId: 13000511
  name: "拳击*5"
  itemId: 240402
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005106
  poolId: 13000511
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005107
  poolId: 13000511
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005108
  poolId: 13000511
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005109
  poolId: 13000511
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005110
  poolId: 13000511
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005111
  poolId: 13000512
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005112
  poolId: 13000512
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005113
  poolId: 13000512
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005114
  poolId: 13000512
  name: "新年快乐*5"
  itemId: 240422
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005115
  poolId: 13000512
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005116
  poolId: 13000512
  name: "身份卡*3"
  itemId: 200102
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005117
  poolId: 13000512
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005118
  poolId: 13000513
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005119
  poolId: 13000513
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005120
  poolId: 13000513
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005121
  poolId: 13000513
  name: "炸弹狼头像框"
  itemId: 840517
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005122
  poolId: 13000513
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005123
  poolId: 13000513
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005124
  poolId: 13000513
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005201
  poolId: 13000520
  name: "手表麻醉针攻击"
  itemId: 240212
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005202
  poolId: 13000520
  name: "饰品二选一"
  itemId: 331012
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 130005203
  poolId: 13000520
  name: "nice动作表情"
  itemId: 711314
  itemNum: 1
  groupId: 3
  weight: 79
  limit: 1
}
rows {
  rewardId: 130005204
  poolId: 13000521
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005205
  poolId: 13000521
  name: "送花*5"
  itemId: 240404
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005206
  poolId: 13000521
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005207
  poolId: 13000521
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005208
  poolId: 13000521
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005209
  poolId: 13000521
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005210
  poolId: 13000521
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005211
  poolId: 13000522
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005212
  poolId: 13000522
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005213
  poolId: 13000522
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005214
  poolId: 13000522
  name: "新年快乐*5"
  itemId: 240422
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005215
  poolId: 13000522
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005216
  poolId: 13000522
  name: "身份卡*3"
  itemId: 200102
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005217
  poolId: 13000522
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005218
  poolId: 13000523
  name: "狼人币*300"
  itemId: 13
  itemNum: 300
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005219
  poolId: 13000523
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005220
  poolId: 13000523
  name: "阵营卡*4"
  itemId: 200101
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005221
  poolId: 13000523
  name: "炸弹狼头像+昵称框"
  itemId: 310283
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005222
  poolId: 13000523
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005223
  poolId: 13000523
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005224
  poolId: 13000523
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005301
  poolId: 13000530
  name: "蜜糖喵巡游站"
  itemId: 218168
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005302
  poolId: 13000530
  name: "甜梦嘉年华自选礼盒"
  itemId: 331026
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130005303
  poolId: 13000530
  name: "甜梦嘉年华自选礼盒"
  itemId: 331026
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130005304
  poolId: 13000530
  name: "甜梦嘉年华自选礼盒"
  itemId: 331026
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130005305
  poolId: 13000530
  name: "晴空朵朵"
  itemId: 630576
  itemNum: 1
  groupId: 5
  weight: 30
  limit: 1
}
rows {
  rewardId: 130005306
  poolId: 13000530
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130005307
  poolId: 13000530
  name: "甜心法杖"
  itemId: 640061
  itemNum: 1
  groupId: 5
  weight: 70
  limit: 1
}
rows {
  rewardId: 130005308
  poolId: 13000530
  name: "幸运币*5"
  itemId: 3
  itemNum: 5
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130005309
  poolId: 13000530
  name: "甜梦嘉年华头像"
  itemId: 860200
  itemNum: 1
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130005310
  poolId: 13000530
  name: "夏日清凉猫"
  itemId: 711526
  itemNum: 1
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130005311
  poolId: 13000531
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005312
  poolId: 13000531
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005313
  poolId: 13000531
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005314
  poolId: 13000531
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005315
  poolId: 13000531
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005316
  poolId: 13000531
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005317
  poolId: 13000531
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005318
  poolId: 13000532
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005319
  poolId: 13000532
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005320
  poolId: 13000532
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005321
  poolId: 13000532
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005322
  poolId: 13000532
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005323
  poolId: 13000532
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005324
  poolId: 13000532
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005325
  poolId: 13000533
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005326
  poolId: 13000533
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005327
  poolId: 13000533
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005328
  poolId: 13000533
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005329
  poolId: 13000533
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005330
  poolId: 13000533
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005331
  poolId: 13000533
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005332
  poolId: 13000534
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005333
  poolId: 13000534
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005334
  poolId: 13000534
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005335
  poolId: 13000534
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005336
  poolId: 13000534
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005337
  poolId: 13000534
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005338
  poolId: 13000534
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005339
  poolId: 13000535
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005340
  poolId: 13000535
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005341
  poolId: 13000535
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005342
  poolId: 13000535
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005343
  poolId: 13000535
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005344
  poolId: 13000535
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005345
  poolId: 13000535
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005346
  poolId: 13000536
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005347
  poolId: 13000536
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005348
  poolId: 13000536
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005349
  poolId: 13000536
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005350
  poolId: 13000536
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005351
  poolId: 13000536
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005352
  poolId: 13000536
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005353
  poolId: 13000537
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005354
  poolId: 13000537
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005355
  poolId: 13000537
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005356
  poolId: 13000537
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005357
  poolId: 13000537
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005358
  poolId: 13000537
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005359
  poolId: 13000537
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005360
  poolId: 13000538
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005361
  poolId: 13000538
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005362
  poolId: 13000538
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005363
  poolId: 13000538
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005364
  poolId: 13000538
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005365
  poolId: 13000538
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005366
  poolId: 13000538
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005367
  poolId: 13000539
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005368
  poolId: 13000539
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005369
  poolId: 13000539
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005370
  poolId: 13000539
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005371
  poolId: 13000539
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005372
  poolId: 13000539
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005373
  poolId: 13000539
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005374
  poolId: 13000540
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005375
  poolId: 13000540
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005376
  poolId: 13000540
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005377
  poolId: 13000540
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005378
  poolId: 13000540
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005379
  poolId: 13000540
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005380
  poolId: 13000540
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005501
  poolId: 13000550
  name: "伪装者特效-赤芒晶核"
  itemId: 241209
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 130005502
  poolId: 13000550
  name: "繁星法杖"
  itemId: 241409
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005503
  poolId: 13000550
  name: "精英搜捕者"
  itemId: 850578
  itemNum: 1
  groupId: 2
  weight: 30
  limit: 1
}
rows {
  rewardId: 130005504
  poolId: 13000551
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005505
  poolId: 13000551
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005506
  poolId: 13000551
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005507
  poolId: 13000551
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005508
  poolId: 13000551
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005509
  poolId: 13000551
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005510
  poolId: 13000551
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005511
  poolId: 13000552
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005512
  poolId: 13000552
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005513
  poolId: 13000552
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005514
  poolId: 13000552
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005515
  poolId: 13000552
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005516
  poolId: 13000552
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005517
  poolId: 13000552
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005518
  poolId: 13000553
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005519
  poolId: 13000553
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005520
  poolId: 13000553
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005521
  poolId: 13000553
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005522
  poolId: 13000553
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005523
  poolId: 13000553
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005524
  poolId: 13000553
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005601
  poolId: 13000560
  name: "伪装者特效-弹跳布丁"
  itemId: 241210
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 130005602
  poolId: 13000560
  name: "嘟嘟号角"
  itemId: 241408
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005603
  poolId: 13000560
  name: "最强伪装者"
  itemId: 850576
  itemNum: 1
  groupId: 2
  weight: 30
  limit: 1
}
rows {
  rewardId: 130005604
  poolId: 13000561
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005605
  poolId: 13000561
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005606
  poolId: 13000561
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005607
  poolId: 13000561
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005608
  poolId: 13000561
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005609
  poolId: 13000561
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005610
  poolId: 13000561
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005611
  poolId: 13000562
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005612
  poolId: 13000562
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005613
  poolId: 13000562
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005614
  poolId: 13000562
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005615
  poolId: 13000562
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005616
  poolId: 13000562
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005617
  poolId: 13000562
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005618
  poolId: 13000563
  name: "钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005619
  poolId: 13000563
  name: "印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005620
  poolId: 13000563
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005621
  poolId: 13000563
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005622
  poolId: 13000563
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005623
  poolId: 13000563
  name: "心心糖果*2"
  itemId: 200015
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005624
  poolId: 13000563
  name: "云朵币 *20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003402
  poolId: 1000034
  name: "丢鸡蛋*20"
  itemId: 240401
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003403
  poolId: 1000034
  name: "狼人币*400"
  itemId: 13
  itemNum: 400
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003401
  poolId: 1000034
  name: "紧急播报"
  itemId: 240008
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100003404
  poolId: 1000034
  name: "潜行狼装饰礼盒"
  itemId: 310754
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003405
  poolId: 1000034
  name: "阵营卡*8"
  itemId: 200101
  itemNum: 8
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003406
  poolId: 1000034
  name: "小猪撞击*10"
  itemId: 240410
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003502
  poolId: 1000035
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003503
  poolId: 1000035
  name: "送花*10"
  itemId: 240404
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003501
  poolId: 1000035
  name: "宝葫芦"
  itemId: 240210
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100003504
  poolId: 1000035
  name: "潜行狼头像框礼盒"
  itemId: 310755
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003505
  poolId: 1000035
  name: "身份卡*4"
  itemId: 200102
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003506
  poolId: 1000035
  name: "手表麻醉针*10"
  itemId: 240421
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003602
  poolId: 1000036
  name: "点赞*20"
  itemId: 240416
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003603
  poolId: 1000036
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003601
  poolId: 1000036
  name: "称号-叫我MVP"
  itemId: 861023
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003604
  poolId: 1000036
  name: "表情-大脑过载"
  itemId: 711531
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003605
  poolId: 1000036
  name: "小猪撞击*10"
  itemId: 240410
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100003606
  poolId: 1000036
  name: "彩虹天桥"
  itemId: 241003
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005701
  poolId: 13000570
  name: "爆爆弹"
  itemId: 630603
  itemNum: 1
  groupId: 2
  weight: 39
  limit: 1
}
rows {
  rewardId: 130005702
  poolId: 13000570
  name: "报告动画: 星球大战"
  itemId: 240017
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005703
  poolId: 13000570
  name: "狼人精品礼包*1"
  itemId: 310741
  itemNum: 1
  groupId: 3
  weight: 60
  limit: 1
}
rows {
  rewardId: 130005704
  poolId: 13000571
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005705
  poolId: 13000571
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005706
  poolId: 13000571
  name: "祝福*5"
  itemId: 240419
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005707
  poolId: 13000571
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005708
  poolId: 13000571
  name: "丢盲盒*5"
  itemId: 240418
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005709
  poolId: 13000571
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005710
  poolId: 13000571
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005711
  poolId: 13000572
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005712
  poolId: 13000572
  name: "狼人币*150"
  itemId: 13
  itemNum: 150
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005713
  poolId: 13000572
  name: "饰品调色盘*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005714
  poolId: 13000572
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005715
  poolId: 13000572
  name: "小猪撞击*5"
  itemId: 240410
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005716
  poolId: 13000572
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005717
  poolId: 13000572
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005718
  poolId: 13000573
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005719
  poolId: 13000573
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005720
  poolId: 13000573
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005721
  poolId: 13000573
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005722
  poolId: 13000573
  name: "永恒之恋*5"
  itemId: 240409
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005723
  poolId: 13000573
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130005724
  poolId: 13000573
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000371
  poolId: 10000037
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000372
  poolId: 10000037
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000373
  poolId: 10000037
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000374
  poolId: 10000037
  name: "星河之果"
  itemId: 630626
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000375
  poolId: 10000037
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000376
  poolId: 10000037
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000377
  poolId: 10000037
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000378
  poolId: 10000037
  name: "暗夜伯爵"
  itemId: 219307
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000379
  poolId: 10000037
  name: "沙洲旅人石屋"
  itemId: 218202
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005801
  poolId: 13000580
  name: "冰晶星梦城堡"
  itemId: 218142
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005802
  poolId: 13000580
  name: "雪夜小摊自选礼盒"
  itemId: 331008
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130005803
  poolId: 13000580
  name: "雪夜小摊自选礼盒"
  itemId: 331008
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130005804
  poolId: 13000580
  name: "雪夜小摊自选礼盒"
  itemId: 331008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130005805
  poolId: 13000580
  name: "糖果松树时钟"
  itemId: 218138
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 130005806
  poolId: 13000580
  name: "喵来啦"
  itemId: 620682
  itemNum: 1
  groupId: 6
  weight: 30
  limit: 1
}
rows {
  rewardId: 130005807
  poolId: 13000580
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130005808
  poolId: 13000580
  name: "幸运币*15"
  itemId: 3
  itemNum: 15
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130005809
  poolId: 13000580
  name: "小暖狐"
  itemId: 620535
  itemNum: 1
  groupId: 6
  weight: 70
  limit: 1
}
rows {
  rewardId: 130005810
  poolId: 13000580
  name: "幸运币*5"
  itemId: 3
  itemNum: 5
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130005811
  poolId: 13000580
  name: "冰梦城堡头像"
  itemId: 860153
  itemNum: 1
  groupId: 5
  weight: 95
  limit: 1
}
rows {
  rewardId: 130005812
  poolId: 13000580
  name: "钓鱼大师"
  itemId: 720779
  itemNum: 1
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130005813
  poolId: 13000581
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005814
  poolId: 13000581
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005815
  poolId: 13000581
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005816
  poolId: 13000581
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005817
  poolId: 13000581
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005818
  poolId: 13000581
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005819
  poolId: 13000581
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005820
  poolId: 13000582
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005821
  poolId: 13000582
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005822
  poolId: 13000582
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005823
  poolId: 13000582
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005824
  poolId: 13000582
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005825
  poolId: 13000582
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005826
  poolId: 13000582
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005827
  poolId: 13000583
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005828
  poolId: 13000583
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005829
  poolId: 13000583
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005830
  poolId: 13000583
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005831
  poolId: 13000583
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005832
  poolId: 13000583
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005833
  poolId: 13000583
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005834
  poolId: 13000584
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005835
  poolId: 13000584
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005836
  poolId: 13000584
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005837
  poolId: 13000584
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005838
  poolId: 13000584
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005839
  poolId: 13000584
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005840
  poolId: 13000584
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005841
  poolId: 13000585
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005842
  poolId: 13000585
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005843
  poolId: 13000585
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005844
  poolId: 13000585
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005845
  poolId: 13000585
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005846
  poolId: 13000585
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005847
  poolId: 13000585
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005848
  poolId: 13000586
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005849
  poolId: 13000586
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005850
  poolId: 13000586
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005851
  poolId: 13000586
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005852
  poolId: 13000586
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005853
  poolId: 13000586
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005854
  poolId: 13000586
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005855
  poolId: 13000587
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005856
  poolId: 13000587
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005857
  poolId: 13000587
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005858
  poolId: 13000587
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005859
  poolId: 13000587
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005860
  poolId: 13000587
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005861
  poolId: 13000587
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005862
  poolId: 13000588
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005863
  poolId: 13000588
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005864
  poolId: 13000588
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005865
  poolId: 13000588
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005866
  poolId: 13000588
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005867
  poolId: 13000588
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005868
  poolId: 13000588
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005869
  poolId: 13000589
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005870
  poolId: 13000589
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005871
  poolId: 13000589
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005872
  poolId: 13000589
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005873
  poolId: 13000589
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005874
  poolId: 13000589
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005875
  poolId: 13000589
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005876
  poolId: 13000590
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005877
  poolId: 13000590
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005878
  poolId: 13000590
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005879
  poolId: 13000590
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005880
  poolId: 13000590
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005881
  poolId: 13000590
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005882
  poolId: 13000590
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005883
  poolId: 13000591
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005884
  poolId: 13000591
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005885
  poolId: 13000591
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005886
  poolId: 13000591
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005887
  poolId: 13000591
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005888
  poolId: 13000591
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005889
  poolId: 13000591
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130005890
  poolId: 13000592
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130005891
  poolId: 13000592
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005892
  poolId: 13000592
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005893
  poolId: 13000592
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005894
  poolId: 13000592
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005895
  poolId: 13000592
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130005896
  poolId: 13000592
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 100000381
  poolId: 10000038
  name: "永恒誓言"
  itemId: 620946
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000382
  poolId: 10000038
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000383
  poolId: 10000038
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000384
  poolId: 10000039
  name: "云晶花语时钟"
  itemId: 218206
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000385
  poolId: 10000039
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000386
  poolId: 10000039
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000387
  poolId: 10000039
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000388
  poolId: 10000039
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000389
  poolId: 10000039
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000390
  poolId: 10000040
  name: "怒海鲨王号"
  itemId: 218200
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000391
  poolId: 10000040
  name: "乌云绵绵"
  itemId: 630653
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000392
  poolId: 10000040
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000393
  poolId: 10000040
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000394
  poolId: 10000040
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000395
  poolId: 10000040
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000396
  poolId: 10000040
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000397
  poolId: 10000040
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000398
  poolId: 10000040
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 60001151
  poolId: 6000115
  name: "奇遇舞章 艾琳"
  itemId: 411360
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 60001152
  poolId: 6000115
  name: "桂影轻舞"
  itemId: 640173
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001153
  poolId: 6000115
  name: "童话闪翼"
  itemId: 620924
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001154
  poolId: 6000115
  name: "追爱之箭"
  itemId: 722053
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001155
  poolId: 6000115
  name: "优雅舞姿"
  itemId: 711527
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001156
  poolId: 6000115
  name: "艾琳-奇遇舞章"
  itemId: 870050
  itemNum: 1
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 60001157
  poolId: 6000115
  name: "峡谷币*150"
  itemId: 3541
  itemNum: 150
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001158
  poolId: 6000115
  name: "艾琳头像"
  itemId: 860215
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 60001159
  poolId: 6000115
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 130006001
  poolId: 13000600
  name: "胡言乱语"
  itemId: 610387
  itemNum: 1
  groupId: 2
  weight: 39
  limit: 1
}
rows {
  rewardId: 130006002
  poolId: 13000600
  name: "攻击动画：热血足球"
  itemId: 240217
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006003
  poolId: 13000600
  name: "狼人优选礼包*1"
  itemId: 310742
  itemNum: 1
  groupId: 3
  weight: 60
  limit: 1
}
rows {
  rewardId: 130006004
  poolId: 13000601
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006005
  poolId: 13000601
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006006
  poolId: 13000601
  name: "干杯*5"
  itemId: 240412
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006007
  poolId: 13000601
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006008
  poolId: 13000601
  name: "贴符*5"
  itemId: 240417
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006009
  poolId: 13000601
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006010
  poolId: 13000601
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006011
  poolId: 13000602
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006012
  poolId: 13000602
  name: "狼人币*150"
  itemId: 13
  itemNum: 150
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006013
  poolId: 13000602
  name: "时装染色膏*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006014
  poolId: 13000602
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006015
  poolId: 13000602
  name: "小猪撞击*5"
  itemId: 240410
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006016
  poolId: 13000602
  name: "星愿币 *2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006017
  poolId: 13000602
  name: "身份卡*2"
  itemId: 200102
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006018
  poolId: 13000603
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006019
  poolId: 13000603
  name: "狼人币*200"
  itemId: 13
  itemNum: 200
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006020
  poolId: 13000603
  name: "手表麻醉针*5"
  itemId: 240421
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006021
  poolId: 13000603
  name: "阵营卡*3"
  itemId: 200101
  itemNum: 3
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006022
  poolId: 13000603
  name: "永恒之恋*5"
  itemId: 240409
  itemNum: 5
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006023
  poolId: 13000603
  name: "星愿币 *3"
  itemId: 2
  itemNum: 3
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006024
  poolId: 13000603
  name: "身份卡*1"
  itemId: 200102
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000411
  poolId: 10000041
  name: "囧囧先生"
  itemId: 630656
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000412
  poolId: 10000041
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000413
  poolId: 10000041
  name: "星宝印章"
  itemId: 4
  itemNum: 1000
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 100000414
  poolId: 10000042
  name: "甜甜杯礼盒"
  itemId: 218192
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000415
  poolId: 10000042
  name: "配饰染色剂"
  itemId: 200008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000416
  poolId: 10000042
  name: "服装染色剂"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000417
  poolId: 10000042
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000418
  poolId: 10000042
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000419
  poolId: 10000043
  name: "甜心琪琪"
  itemId: 218194
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000420
  poolId: 10000043
  name: "跃动喵喵"
  itemId: 630650
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000421
  poolId: 10000043
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 100000422
  poolId: 10000043
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 15
  limit: 1
}
rows {
  rewardId: 100000423
  poolId: 10000043
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 30
  limit: 1
}
rows {
  rewardId: 100000424
  poolId: 10000043
  name: "云朵币"
  itemId: 6
  itemNum: 30
  groupId: 3
  weight: 20
  limit: 1
}
rows {
  rewardId: 100000425
  poolId: 10000043
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 50
  limit: 1
}
rows {
  rewardId: 130006101
  poolId: 13000610
  name: "梦幻熊礼盒"
  itemId: 218130
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
}
rows {
  rewardId: 130006102
  poolId: 13000610
  name: "招财喵"
  itemId: 218117
  itemNum: 1
  groupId: 1
  weight: 20
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006103
  poolId: 13000610
  name: "海底奇遇包"
  itemId: 620543
  itemNum: 1
  groupId: 2
  weight: 35
  limit: 1
}
rows {
  rewardId: 130006104
  poolId: 13000611
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006105
  poolId: 13000611
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006106
  poolId: 13000611
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006107
  poolId: 13000611
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006108
  poolId: 13000611
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006109
  poolId: 13000611
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006110
  poolId: 13000611
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006111
  poolId: 13000611
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006112
  poolId: 13000612
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006113
  poolId: 13000612
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006114
  poolId: 13000612
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006115
  poolId: 13000612
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006116
  poolId: 13000612
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006117
  poolId: 13000612
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006118
  poolId: 13000612
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006119
  poolId: 13000612
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006120
  poolId: 13000613
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006121
  poolId: 13000613
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006122
  poolId: 13000613
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006123
  poolId: 13000613
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006124
  poolId: 13000613
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006125
  poolId: 13000613
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006126
  poolId: 13000613
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006127
  poolId: 13000613
  name: "烟花礼盒*5"
  itemId: 725201
  itemNum: 5
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 100000441
  poolId: 10000044
  name: "杏花酒家"
  itemId: 218165
  itemNum: 1
  groupId: 4
  weight: 40
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100000442
  poolId: 10000044
  name: "格格吉祥"
  itemId: 219308
  itemNum: 1
  groupId: 3
  weight: 40
  limit: 1
}
rows {
  rewardId: 100000443
  poolId: 10000044
  name: "蛇小帕"
  itemId: 630659
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000444
  poolId: 10000044
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000445
  poolId: 10000044
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000446
  poolId: 10000044
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 100000447
  poolId: 10000044
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 60
  limit: 1
}
rows {
  rewardId: 100000448
  poolId: 10000044
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 60
  limit: 1
}
rows {
  rewardId: 100000449
  poolId: 10000044
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 100004101
  poolId: 1000041
  name: "倒咖啡*20"
  itemId: 240406
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100004102
  poolId: 1000041
  name: "身份卡*4"
  itemId: 200102
  itemNum: 4
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100004103
  poolId: 1000041
  name: "花样滑冰"
  itemId: 240013
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100004104
  poolId: 1000041
  name: "金光火眼"
  itemId: 630473
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100004105
  poolId: 1000041
  name: "小猪撞击*10"
  itemId: 240410
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100004106
  poolId: 1000041
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100004201
  poolId: 1000042
  name: "新年快乐*5"
  itemId: 240422
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100004202
  poolId: 1000042
  name: "阵营卡*8"
  itemId: 200101
  itemNum: 8
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100004203
  poolId: 1000042
  name: "钓了个鱼"
  itemId: 240213
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 100004204
  poolId: 1000042
  name: "大力狼三件套礼包"
  itemId: 310726
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 100004205
  poolId: 1000042
  name: "狼人币*400"
  itemId: 13
  itemNum: 400
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 100004206
  poolId: 1000042
  name: "扔炸弹*20"
  itemId: 240405
  itemNum: 20
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006200
  poolId: 13000620
  name: "热浪岛屿"
  itemId: 218195
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006201
  poolId: 13000620
  name: "石纹部落"
  itemId: 218196
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006202
  poolId: 13000620
  name: "冲浪鲨鲨"
  itemId: 219306
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 130006203
  poolId: 13000620
  name: "心心蟹"
  itemId: 630662
  itemNum: 1
  groupId: 3
  weight: 90
  limit: 1
}
rows {
  rewardId: 130006211
  poolId: 13000621
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006212
  poolId: 13000621
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006213
  poolId: 13000621
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006214
  poolId: 13000621
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006215
  poolId: 13000621
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006216
  poolId: 13000621
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006217
  poolId: 13000621
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006221
  poolId: 13000622
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006222
  poolId: 13000622
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006223
  poolId: 13000622
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006224
  poolId: 13000622
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006225
  poolId: 13000622
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006226
  poolId: 13000622
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006227
  poolId: 13000622
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006231
  poolId: 13000623
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006232
  poolId: 13000623
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006233
  poolId: 13000623
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006234
  poolId: 13000623
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006235
  poolId: 13000623
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006236
  poolId: 13000623
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006237
  poolId: 13000623
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006241
  poolId: 13000624
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006242
  poolId: 13000624
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006243
  poolId: 13000624
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006244
  poolId: 13000624
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006245
  poolId: 13000624
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006246
  poolId: 13000624
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006247
  poolId: 13000624
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006301
  poolId: 13000630
  name: "圣灵之庭"
  itemId: 218201
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006302
  poolId: 13000630
  name: "天穹小摊自选礼盒"
  itemId: 331035
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 130006303
  poolId: 13000630
  name: "天穹圣域自选礼盒"
  itemId: 331035
  itemNum: 1
  groupId: 3
  weight: 5
  limit: 1
}
rows {
  rewardId: 130006304
  poolId: 13000630
  name: "天穹圣域自选礼盒"
  itemId: 331035
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 130006305
  poolId: 13000630
  name: "晴空朵朵"
  itemId: 630576
  itemNum: 1
  groupId: 5
  weight: 30
  limit: 1
}
rows {
  rewardId: 130006306
  poolId: 13000630
  name: "幸运币*20"
  itemId: 3
  itemNum: 20
  groupId: 1
  weight: 95
  limit: 1
}
rows {
  rewardId: 130006307
  poolId: 13000630
  name: "黄金松果"
  itemId: 630624
  itemNum: 1
  groupId: 5
  weight: 70
  limit: 1
}
rows {
  rewardId: 130006308
  poolId: 13000630
  name: "幸运币*5"
  itemId: 3
  itemNum: 5
  groupId: 3
  weight: 95
  limit: 1
}
rows {
  rewardId: 130006309
  poolId: 13000630
  name: "甜梦嘉年华头像"
  itemId: 860200
  itemNum: 1
  groupId: 4
  weight: 95
  limit: 1
}
rows {
  rewardId: 130006310
  poolId: 13000630
  name: "夏日清凉猫"
  itemId: 711526
  itemNum: 1
  groupId: 2
  weight: 95
  limit: 1
}
rows {
  rewardId: 130006311
  poolId: 13000631
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006312
  poolId: 13000631
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006313
  poolId: 13000631
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006314
  poolId: 13000631
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006315
  poolId: 13000631
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006316
  poolId: 13000631
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006317
  poolId: 13000631
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006318
  poolId: 13000632
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006319
  poolId: 13000632
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006320
  poolId: 13000632
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006321
  poolId: 13000632
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006322
  poolId: 13000632
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006323
  poolId: 13000632
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006324
  poolId: 13000632
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006325
  poolId: 13000633
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006326
  poolId: 13000633
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006327
  poolId: 13000633
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006328
  poolId: 13000633
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006329
  poolId: 13000633
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006330
  poolId: 13000633
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006331
  poolId: 13000633
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006332
  poolId: 13000634
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006333
  poolId: 13000634
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006334
  poolId: 13000634
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006335
  poolId: 13000634
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006336
  poolId: 13000634
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006337
  poolId: 13000634
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006338
  poolId: 13000634
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006339
  poolId: 13000635
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006340
  poolId: 13000635
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006341
  poolId: 13000635
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006342
  poolId: 13000635
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006343
  poolId: 13000635
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006344
  poolId: 13000635
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006345
  poolId: 13000635
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006346
  poolId: 13000636
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006347
  poolId: 13000636
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006348
  poolId: 13000636
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006349
  poolId: 13000636
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006350
  poolId: 13000636
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006351
  poolId: 13000636
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006352
  poolId: 13000636
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006353
  poolId: 13000637
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006354
  poolId: 13000637
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006355
  poolId: 13000637
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006356
  poolId: 13000637
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006357
  poolId: 13000637
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006358
  poolId: 13000637
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006359
  poolId: 13000637
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006360
  poolId: 13000638
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006361
  poolId: 13000638
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006362
  poolId: 13000638
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006363
  poolId: 13000638
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006364
  poolId: 13000638
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006365
  poolId: 13000638
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006366
  poolId: 13000638
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006367
  poolId: 13000639
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006368
  poolId: 13000639
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006369
  poolId: 13000639
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006370
  poolId: 13000639
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006371
  poolId: 13000639
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006372
  poolId: 13000639
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006373
  poolId: 13000639
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}
rows {
  rewardId: 130006374
  poolId: 13000640
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 130006375
  poolId: 13000640
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006376
  poolId: 13000640
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006377
  poolId: 13000640
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006378
  poolId: 13000640
  name: "心心宝瓶*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006379
  poolId: 13000640
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 99999
}
rows {
  rewardId: 130006380
  poolId: 13000640
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 5
  limit: 99999
}

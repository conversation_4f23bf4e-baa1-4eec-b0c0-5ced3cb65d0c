com.tencent.wea.xlsRes.table_XiaowoPropsConf
excel/xls/Farm/X_小窝商品表.xlsx sheet:Sheet1
rows {
  ID: 1
  ItemName: "鲁班贩卖机"
  UGCItemID: 1108004004001001
  SystemProps: 200015
  JumpID: 67
  Desc: "我是一个说明"
  UpLimit: 1
  TurnOn: 0
}
rows {
  ID: 2
  ItemName: "鲁班餐厅招牌"
  UGCItemID: 1108006001001143
  SystemProps: 200015
  JumpID: 67
  Desc: "我是一个说明"
  UpLimit: 2
  TurnOn: 0
}
rows {
  ID: 3
  ItemName: "S4经典模式奖杯"
  UGCItemID: 1102015001001970
  SystemProps: 200501
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 4
}
rows {
  ID: 4
  ItemName: "S4谁是狼人奖杯"
  UGCItemID: 1102015001001971
  SystemProps: 200502
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 4
}
rows {
  ID: 5
  ItemName: "S4暗星奖杯"
  UGCItemID: 1102015001001972
  SystemProps: 200503
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 4
}
rows {
  ID: 6
  ItemName: "S4极速飞车奖杯"
  UGCItemID: 1102015001001973
  SystemProps: 200504
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 4
}
rows {
  ID: 7
  ItemName: "S4大乱斗奖杯"
  UGCItemID: 1102015001001974
  SystemProps: 200505
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 4
}
rows {
  ID: 8
  ItemName: "S4武器大师奖杯"
  UGCItemID: 1102015001001975
  SystemProps: 200506
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 4
}
rows {
  ID: 9
  ItemName: "S4躲猫猫奖杯"
  UGCItemID: 1102015001001976
  SystemProps: 200507
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 4
}
rows {
  ID: 10
  ItemName: "S4梦幻岛奖杯"
  UGCItemID: 1102015001001977
  SystemProps: 200508
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 4
}
rows {
  ID: 11
  ItemName: "S4星宝奖杯"
  UGCItemID: 1102015001001978
  SystemProps: 200509
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 4
}
rows {
  ID: 12
  ItemName: "S5经典模式奖杯"
  UGCItemID: 1102015001001988
  SystemProps: 200510
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 5
}
rows {
  ID: 13
  ItemName: "S5谁是狼人奖杯"
  UGCItemID: 1102015001001989
  SystemProps: 200511
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 5
}
rows {
  ID: 14
  ItemName: "S5暗星奖杯"
  UGCItemID: 1102015001001990
  SystemProps: 200512
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 5
}
rows {
  ID: 15
  ItemName: "S5极速飞车奖杯"
  UGCItemID: 1102015001001991
  SystemProps: 200513
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 5
}
rows {
  ID: 16
  ItemName: "S5大乱斗奖杯"
  UGCItemID: 1102015001001992
  SystemProps: 200514
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 5
}
rows {
  ID: 17
  ItemName: "S5武器大师奖杯"
  UGCItemID: 1102015001001993
  SystemProps: 200515
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 5
}
rows {
  ID: 18
  ItemName: "S5躲猫猫奖杯"
  UGCItemID: 1102015001001994
  SystemProps: 200516
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 5
}
rows {
  ID: 19
  ItemName: "S5梦幻岛奖杯"
  UGCItemID: 1102015001001995
  SystemProps: 200517
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 5
}
rows {
  ID: 20
  ItemName: "S5星宝奖杯"
  UGCItemID: 1102015001001996
  SystemProps: 200518
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 5
}
rows {
  ID: 21
  ItemName: "S6闪耀辉煌"
  UGCItemID: 1102015001002314
  SystemProps: 202001
  Desc: "闪电赛活动获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
}
rows {
  ID: 22
  ItemName: "S6经典模式奖杯"
  UGCItemID: 1102015001001997
  SystemProps: 200519
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 6
}
rows {
  ID: 23
  ItemName: "S6谁是狼人奖杯"
  UGCItemID: 1102015001001998
  SystemProps: 200520
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 6
}
rows {
  ID: 24
  ItemName: "S6暗星奖杯"
  UGCItemID: 1102015001001999
  SystemProps: 200521
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 6
}
rows {
  ID: 25
  ItemName: "S6极速飞车奖杯"
  UGCItemID: 1102015001002000
  SystemProps: 200522
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 6
}
rows {
  ID: 26
  ItemName: "S6大乱斗奖杯"
  UGCItemID: 1102015001002001
  SystemProps: 200523
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 6
}
rows {
  ID: 27
  ItemName: "S6武器大师奖杯"
  UGCItemID: 1102015001002002
  SystemProps: 200524
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 6
}
rows {
  ID: 28
  ItemName: "S6躲猫猫奖杯"
  UGCItemID: 1102015001002003
  SystemProps: 200525
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 6
}
rows {
  ID: 29
  ItemName: "S6梦幻岛奖杯"
  UGCItemID: 1102015001002004
  SystemProps: 200526
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 6
}
rows {
  ID: 30
  ItemName: "S6星宝奖杯"
  UGCItemID: 1102015001002005
  SystemProps: 200527
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 6
}
rows {
  ID: 31
  ItemName: "S7闪耀辉煌"
  UGCItemID: 1102015001002692
  SystemProps: 202002
  Desc: "闪电赛活动获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
}
rows {
  ID: 32
  ItemName: "S7经典模式奖杯"
  UGCItemID: 1102015001002006
  SystemProps: 200529
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 33
  ItemName: "S7谁是狼人奖杯"
  UGCItemID: 1102015001002007
  SystemProps: 200530
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 34
  ItemName: "S7暗星奖杯"
  UGCItemID: 1102015001002008
  SystemProps: 200531
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 35
  ItemName: "S7极速飞车奖杯"
  UGCItemID: 1102015001002009
  SystemProps: 200532
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 36
  ItemName: "S7大乱斗奖杯"
  UGCItemID: 1102015001002010
  SystemProps: 200533
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 37
  ItemName: "S7武器大师奖杯"
  UGCItemID: 1102015001002011
  SystemProps: 200534
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 38
  ItemName: "S7躲猫猫奖杯"
  UGCItemID: 1102015001002012
  SystemProps: 200535
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 39
  ItemName: "S7梦幻岛奖杯"
  UGCItemID: 1102015001002013
  SystemProps: 200536
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 40
  ItemName: "S7星宝奖杯"
  UGCItemID: 1102015001002014
  SystemProps: 200537
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 41
  ItemName: "S7峡谷3V3奖杯"
  UGCItemID: 1102015001002643
  SystemProps: 200538
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 42
  ItemName: "S7泡泡大战奖杯"
  UGCItemID: 1102015001002687
  SystemProps: 200539
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 7
}
rows {
  ID: 43
  ItemName: "S8闪耀辉煌"
  UGCItemID: 1102015001002820
  SystemProps: 202003
  Desc: "闪电赛活动获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 44
  ItemName: "S8经典模式奖杯"
  UGCItemID: 1102015001002015
  SystemProps: 200541
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 45
  ItemName: "S8谁是狼人奖杯"
  UGCItemID: 1102015001002016
  SystemProps: 200542
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 46
  ItemName: "S8暗星奖杯"
  UGCItemID: 1102015001002017
  SystemProps: 200543
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 47
  ItemName: "S8极速飞车奖杯"
  UGCItemID: 1102015001002018
  SystemProps: 200544
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 48
  ItemName: "S8大乱斗奖杯"
  UGCItemID: 1102015001002019
  SystemProps: 200545
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 49
  ItemName: "S8武器大师奖杯"
  UGCItemID: 1102015001002020
  SystemProps: 200546
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 50
  ItemName: "S8躲猫猫奖杯"
  UGCItemID: 1102015001002021
  SystemProps: 200547
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 51
  ItemName: "S8梦幻岛奖杯"
  UGCItemID: 1102015001002022
  SystemProps: 200548
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 52
  ItemName: "S8星宝奖杯"
  UGCItemID: 1102015001002023
  SystemProps: 200549
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 53
  ItemName: "S8峡谷3V3奖杯"
  UGCItemID: 1102015001002644
  SystemProps: 200550
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 54
  ItemName: "S8泡泡大战奖杯"
  UGCItemID: 1102015001002688
  SystemProps: 200551
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 8
}
rows {
  ID: 55
  ItemName: "S9经典模式奖杯"
  UGCItemID: 1102015001002024
  SystemProps: 200554
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 56
  ItemName: "S9谁是狼人奖杯"
  UGCItemID: 1102015001002025
  SystemProps: 200555
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 57
  ItemName: "S9暗星奖杯"
  UGCItemID: 1102015001002026
  SystemProps: 200556
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 58
  ItemName: "S9极速飞车奖杯"
  UGCItemID: 1102015001002027
  SystemProps: 200557
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 59
  ItemName: "S9大乱斗奖杯"
  UGCItemID: 1102015001002028
  SystemProps: 200558
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 60
  ItemName: "S9武器大师奖杯"
  UGCItemID: 1102015001002029
  SystemProps: 200559
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 61
  ItemName: "S9躲猫猫奖杯"
  UGCItemID: 1102015001002030
  SystemProps: 200560
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 62
  ItemName: "S9梦幻岛奖杯"
  UGCItemID: 1102015001002031
  SystemProps: 200561
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 63
  ItemName: "S9星宝奖杯"
  UGCItemID: 1102015001002032
  SystemProps: 200562
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 64
  ItemName: "S9峡谷3V3奖杯"
  UGCItemID: 1102015001002645
  SystemProps: 200563
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 65
  ItemName: "S9泡泡大战奖杯"
  UGCItemID: 1102015001002689
  SystemProps: 200564
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 66
  ItemName: "S9闪耀辉煌"
  UGCItemID: 1102015001002922
  SystemProps: 202004
  Desc: "闪电赛活动获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 9
}
rows {
  ID: 67
  ItemName: "S10闪耀辉煌"
  UGCItemID: 1102015001003088
  SystemProps: 202005
  Desc: "闪电赛活动获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 68
  ItemName: "S10经典模式奖杯"
  UGCItemID: 1102015001001601
  SystemProps: 200568
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 69
  ItemName: "S10谁是狼人奖杯"
  UGCItemID: 1102015001001602
  SystemProps: 200569
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 70
  ItemName: "S10暗星奖杯"
  UGCItemID: 1102015001001603
  SystemProps: 200570
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 71
  ItemName: "S10极速飞车奖杯"
  UGCItemID: 1102015001001604
  SystemProps: 200571
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 72
  ItemName: "S10大乱斗奖杯"
  UGCItemID: 1102015001001605
  SystemProps: 200572
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 73
  ItemName: "S10武器大师奖杯"
  UGCItemID: 1102015001001606
  SystemProps: 200573
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 74
  ItemName: "S10躲猫猫奖杯"
  UGCItemID: 1102015001001607
  SystemProps: 200574
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 75
  ItemName: "S10梦幻岛奖杯"
  UGCItemID: 1102015001001608
  SystemProps: 200575
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 76
  ItemName: "S10星宝奖杯"
  UGCItemID: 1102015001001609
  SystemProps: 200576
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 77
  ItemName: "S10峡谷奖杯"
  UGCItemID: 1102015001002602
  SystemProps: 200577
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 78
  ItemName: "S10泡泡大战奖杯"
  UGCItemID: 1102015001002646
  SystemProps: 200578
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 10
}
rows {
  ID: 79
  ItemName: "S11闪耀辉煌"
  UGCItemID: 1102015001003089
  SystemProps: 202006
  Desc: "闪电赛活动获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 80
  ItemName: "S11经典模式奖杯"
  UGCItemID: 1102015001001610
  SystemProps: 200579
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 81
  ItemName: "S11谁是狼人奖杯"
  UGCItemID: 1102015001001611
  SystemProps: 200580
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 82
  ItemName: "S11暗星奖杯"
  UGCItemID: 1102015001001612
  SystemProps: 200581
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 83
  ItemName: "S11极速飞车奖杯"
  UGCItemID: 1102015001001613
  SystemProps: 200582
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 84
  ItemName: "S11大乱斗奖杯"
  UGCItemID: 1102015001001614
  SystemProps: 200583
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 85
  ItemName: "S11武器大师奖杯"
  UGCItemID: 1102015001001615
  SystemProps: 200584
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 86
  ItemName: "S11躲猫猫奖杯"
  UGCItemID: 1102015001001616
  SystemProps: 200585
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 87
  ItemName: "S11梦幻岛奖杯"
  UGCItemID: 1102015001001617
  SystemProps: 200586
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 88
  ItemName: "S11星宝奖杯"
  UGCItemID: 1102015001001618
  SystemProps: 200587
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 89
  ItemName: "S11峡谷奖杯"
  UGCItemID: 1102015001002603
  SystemProps: 200588
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 90
  ItemName: "S11泡泡大战奖杯"
  UGCItemID: 1102015001002647
  SystemProps: 200589
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 11
}
rows {
  ID: 91
  ItemName: "S12经典模式奖杯"
  UGCItemID: 1102015001001619
  SystemProps: 200761
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 92
  ItemName: "S12谁是狼人奖杯"
  UGCItemID: 1102015001001620
  SystemProps: 200762
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 93
  ItemName: "S12暗星奖杯"
  UGCItemID: 1102015001001621
  SystemProps: 200763
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 94
  ItemName: "S12极速飞车奖杯"
  UGCItemID: 1102015001001622
  SystemProps: 200764
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 95
  ItemName: "S12大乱斗奖杯"
  UGCItemID: 1102015001001623
  SystemProps: 200765
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 96
  ItemName: "S12武器大师奖杯"
  UGCItemID: 1102015001001624
  SystemProps: 200766
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 97
  ItemName: "S12躲猫猫奖杯"
  UGCItemID: 1102015001001625
  SystemProps: 200767
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 98
  ItemName: "S12梦幻岛奖杯"
  UGCItemID: 1102015001001626
  SystemProps: 200768
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 99
  ItemName: "S12星宝奖杯"
  UGCItemID: 1102015001001627
  SystemProps: 200769
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 100
  ItemName: "S12峡谷奖杯"
  UGCItemID: 1102015001002604
  SystemProps: 200770
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 101
  ItemName: "S12闪耀辉煌"
  UGCItemID: 1102015001003090
  SystemProps: 202007
  Desc: "闪电赛活动获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 12
}
rows {
  ID: 102
  ItemName: "S13经典模式奖杯"
  UGCItemID: 1102015001001628
  SystemProps: 200816
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}
rows {
  ID: 103
  ItemName: "S13谁是狼人奖杯"
  UGCItemID: 1102015001001629
  SystemProps: 200817
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}
rows {
  ID: 104
  ItemName: "S13暗星奖杯"
  UGCItemID: 1102015001001630
  SystemProps: 200818
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}
rows {
  ID: 105
  ItemName: "S13极速飞车奖杯"
  UGCItemID: 1102015001001631
  SystemProps: 200819
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}
rows {
  ID: 106
  ItemName: "S13大乱斗奖杯"
  UGCItemID: 1102015001001632
  SystemProps: 200820
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}
rows {
  ID: 107
  ItemName: "S13武器大师奖杯"
  UGCItemID: 1102015001001633
  SystemProps: 200821
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}
rows {
  ID: 108
  ItemName: "S13躲猫猫奖杯"
  UGCItemID: 1102015001001634
  SystemProps: 200822
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}
rows {
  ID: 109
  ItemName: "S13梦幻岛奖杯"
  UGCItemID: 1102015001001635
  SystemProps: 200823
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}
rows {
  ID: 110
  ItemName: "S13星宝奖杯"
  UGCItemID: 1102015001001636
  SystemProps: 200824
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}
rows {
  ID: 111
  ItemName: "S13峡谷奖杯"
  UGCItemID: 1102015001002605
  SystemProps: 200825
  JumpID: 306
  Desc: "赛季兑换商店获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}
rows {
  ID: 112
  ItemName: "S13闪耀辉煌"
  UGCItemID: 1102015001003500
  SystemProps: 202008
  Desc: "闪电赛活动获得相应道具后，自动解锁（解锁后可在地图创作使用）"
  UpLimit: 1
  TurnOn: 1
  Season: 13
}

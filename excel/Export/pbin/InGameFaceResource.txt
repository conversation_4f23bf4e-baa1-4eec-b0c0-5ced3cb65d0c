com.tencent.wea.xlsRes.table_InGameFaceResource
excel/xls/D_道具表_脸部.xlsx sheet:局内脸部资源
rows {
  id: 830001
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_001_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830002
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_002_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830003
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_003_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830004
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_004_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830005
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_005_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830006
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_006_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830007
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_007_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830008
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_008_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830009
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_009_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830010
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_010_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830011
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_011_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830012
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_012_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830013
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_013_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830014
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_014_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830015
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_015_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830016
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_016_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830017
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_017_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830018
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_018_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830019
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_019_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830020
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_020_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830021
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_021_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830022
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_022_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830023
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_023_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830024
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_024_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830025
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_025_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830026
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_026_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830027
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_027_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830028
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_028_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830029
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_029_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830030
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_030_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830031
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_031_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830032
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_032_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830033
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_033_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830034
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_034_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830035
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_035_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830036
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_036_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830037
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_037_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830038
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_038_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830039
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_039_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830040
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_040_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830041
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_041_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830042
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_042_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830043
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_043_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830044
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_044_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830045
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_045_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830046
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_046_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830047
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_047_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830048
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_048_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830049
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_049_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830050
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_050_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830051
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_051_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830052
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_052_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830053
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_053_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830054
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_054_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830055
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_055_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830056
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_056_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830057
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_057_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830058
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_058_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830059
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_059_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830060
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_060_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830061
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_061_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830062
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_062_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830063
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_063_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830064
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_064_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830065
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_065_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830066
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_066_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830067
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_067_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830068
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_068_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830069
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_069_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830070
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_070_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830071
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_071_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830072
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_072_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
    textureBaseColorTag: "BU_082"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830073
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_073_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830074
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_074_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830075
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_96_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830076
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_044_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830077
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_045_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830078
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_048_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830079
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_050_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830080
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_052_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830081
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_056_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830082
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_057_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830083
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_062_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830084
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_016_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830085
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_018_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830086
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_021_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830087
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_022_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830088
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_077_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830089
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_078_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830090
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_079_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830091
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_080_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830092
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_081_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830093
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_082_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830094
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_083_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830095
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_084_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830096
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_085_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830097
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_086_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830098
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_087_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830099
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_088_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830100
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_089_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830101
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_090_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830102
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_091_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830103
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_092_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830104
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_093_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830105
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_094_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830106
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_095_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830107
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_096_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830108
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_097_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830109
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_098_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830110
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_099_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830111
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_102_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830112
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_101_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830113
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_100_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}
rows {
  id: 830114
  faceEmotionConf {
    textureBaseColor: "T_Body_Face_100_D_LOD"
    textureNE: "T_Body_Face_001_N_LOD"
    textureARM: "T_Body_Face_001_ARM_LOD"
  }
  type: ItemType_Face
  quality: 3
}

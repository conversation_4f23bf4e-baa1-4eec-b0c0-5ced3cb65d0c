com.tencent.wea.xlsRes.table_TradingCardCollectionConfig
excel/xls/K_卡牌.xlsx sheet:卡集
rows {
  id: 101
  deckIdList: 10102
  deckIdList: 10105
  deckIdList: 10107
  deckIdList: 10110
  sortId: 200
  name: "田园牧歌"
  icon: "T_CardCom_RankIcon"
  beginTime {
    seconds: 1733932800
  }
  endTime {
    seconds: 1736092799
  }
  version: "1.3.36.1"
  tagGroupList: 1
  tagGroupList: 2
  tagGroupList: 3
  tagGroupList: 4
  reward {
    itemIdList: 850527
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 15
    numList: 50
    expireTimestamps {
      seconds: 1737302399
    }
  }
  showHistory: false
  cycleIds: 10001
  cardIcon: "T_CardCom_BigCard_muchangwuyu"
  tradeShare: SSST_CARD_TRADE
  cardbagiconskin: "S01"
  limitExchangeCardCount: 0
  joinTradRank: 0
}
rows {
  id: 102
  deckIdList: 10201
  deckIdList: 10202
  deckIdList: 10203
  deckIdList: 10204
  deckIdList: 10205
  deckIdList: 10206
  deckIdList: 10207
  deckIdList: 10208
  deckIdList: 10209
  deckIdList: 10210
  deckIdList: 10211
  deckIdList: 10212
  type: CCT_Common
  sortId: 210
  name: "田园牧歌"
  icon: "T_CardCom_RankIcon"
  beginTime {
    seconds: 1737583200
  }
  endTime {
    seconds: 1746115199
  }
  version: "1.3.68.1"
  tagGroupList: 1
  tagGroupList: 2
  tagGroupList: 3
  tagGroupList: 4
  reward {
    itemIdList: 640093
    itemIdList: 850527
    itemIdList: 3800
    numList: 1
    numList: 1
    numList: 2
  }
  showHistory: true
  cycleIds: 10002
  cycleIds: 10003
  cycleIds: 10004
  cardIcon: "T_CardCom_BigCard_muchangwuyu"
  getWay: "【活动】获得;每日奖杯挑战;收集奖杯奖励;额外兑换;卡牌频道等索要/赠送/交换"
  jumpId: "20;902;902;902;902"
  jumpargs: "0;JumpToCup;0;UI_CardSystems_AllPowerful;JumpToCardChat"
  itemTipsIcon: "T_CardSystemsCom_Img_ConvertIcon"
  exchangeMallId: 173
  tradeShare: SSST_CARD_TRADE
  cardbagiconskin: "S01"
  limitExchangeCardCount: 1
  joinTradRank: 0
}
rows {
  id: 103
  deckIdList: 10301
  deckIdList: 10302
  deckIdList: 10303
  deckIdList: 10304
  deckIdList: 10305
  deckIdList: 10306
  deckIdList: 10307
  deckIdList: 10308
  deckIdList: 10309
  deckIdList: 10310
  deckIdList: 10311
  deckIdList: 10312
  type: CCT_Common
  sortId: 220
  name: "缤纷市集"
  icon: "T_CardCom_RankIcon_binfenshiji"
  beginTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1755791999
  }
  version: "********"
  tagGroupList: 1
  tagGroupList: 2
  tagGroupList: 3
  tagGroupList: 4
  tagGroupList: 5
  tagGroupList: 6
  reward {
    itemIdList: 640139
    itemIdList: 850625
    itemIdList: 3801
    numList: 1
    numList: 1
    numList: 6
  }
  showHistory: true
  cycleIds: 10005
  cardIcon: "T_CardCom_BigCard_binfenshiji"
  getWay: "【活动】获得;每日奖杯挑战;收集奖杯奖励;额外兑换;卡牌频道等索要/赠送/交换"
  jumpId: "20;902;902;902;902"
  jumpargs: "0;JumpToCup;0;UI_CardSystems_AllPowerful;JumpToCardChat"
  itemTipsIcon: "T_CardSystemsCom_Img_ConvertIcon"
  exchangeMallId: 177
  tradeShare: SSST_CARD_TRADE_B
  backgroundImg: "CDN:CommonBg_binfenjishi_103"
  cardbagiconskin: "S02"
  limitExchangeCardCount: 1
  joinTradRank: 1
  tabIcon: "CDN:T_CardCom_Tab_binfenshiji"
}
rows {
  id: 104
  deckIdList: 10401
  deckIdList: 10402
  deckIdList: 10403
  deckIdList: 10404
  deckIdList: 10405
  deckIdList: 10406
  deckIdList: 10407
  deckIdList: 10408
  deckIdList: 10409
  deckIdList: 10410
  type: CCT_Activity
  sortId: 230
  name: "夏日联动"
  beginTime {
    seconds: 1746288000
  }
  endTime {
    seconds: 1755791999
  }
  version: "********"
  tagGroupList: 1
  tagGroupList: 2
  tagGroupList: 3
  tagGroupList: 4
  tagGroupList: 5
  tagGroupList: 6
  showHistory: false
  getWay: "【活动】获得;每日奖杯挑战;收集奖杯奖励;额外兑换;卡牌频道等索要/赠送/交换"
  jumpId: "20;902;902;902;902"
  jumpargs: "0;JumpToCup;0;UI_CardSystems_AllPowerful;JumpToCardChat"
  itemTipsIcon: "T_CardSystemsCom_Img_ConvertIcon"
  tradeShare: SSST_CARD_TRADE_B
  backgroundImg: "CDN:CommonBg_xiariliandong_104"
  limitExchangeCardCount: 1
  joinTradRank: 1
  tabIcon: "CDN:T_CardCom_Tab_xiariliandong"
}

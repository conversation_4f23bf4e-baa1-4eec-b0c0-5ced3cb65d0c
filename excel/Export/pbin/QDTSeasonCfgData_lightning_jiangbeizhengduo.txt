com.tencent.wea.xlsRes.table_SeasonCfgData
excel/xls/P_排位段位&保分配置_lightning_闪电赛.xlsx sheet:赛季相关
rows {
  id: 100002
  desInfo: "闪电赛"
  startTime {
    seconds: 1721318400
  }
  endTime {
    seconds: 1724947199
  }
  qualifyingID: 100001
  qualifyType: 10
  seasonId: 6
  qualifyIconStyle: 1
  taskGroupId: 9501
}
rows {
  id: 100003
  desInfo: "闪电赛"
  startTime {
    seconds: 1724947200
  }
  endTime {
    seconds: 1729180799
  }
  qualifyingID: 100001
  qualifyType: 10
  seasonId: 7
  qualifyIconStyle: 1
  taskGroupId: 9502
}
rows {
  id: 100004
  desInfo: "闪电赛"
  startTime {
    seconds: 1729180800
  }
  endTime {
    seconds: 1732809599
  }
  qualifyingID: 100001
  qualifyType: 10
  seasonId: 8
  qualifyIconStyle: 1
  taskGroupId: 9503
}
rows {
  id: 100005
  desInfo: "闪电赛"
  startTime {
    seconds: 1732809600
  }
  endTime {
    seconds: 1737043199
  }
  qualifyingID: 100001
  qualifyType: 10
  seasonId: 9
  qualifyIconStyle: 1
  taskGroupId: 9504
}
rows {
  id: 100006
  desInfo: "闪电赛"
  startTime {
    seconds: 1737043200
  }
  endTime {
    seconds: 1741881599
  }
  qualifyingID: 100001
  qualifyType: 10
  seasonId: 10
  qualifyIconStyle: 1
  taskGroupId: 9505
}
rows {
  id: 100007
  desInfo: "闪电赛"
  startTime {
    seconds: 1741881600
  }
  endTime {
    seconds: 1746115199
  }
  qualifyingID: 100001
  qualifyType: 10
  seasonId: 11
  qualifyIconStyle: 1
  taskGroupId: 9506
}
rows {
  id: 100008
  desInfo: "闪电赛"
  startTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1750953599
  }
  qualifyingID: 100001
  qualifyType: 10
  seasonId: 12
  qualifyIconStyle: 1
  taskGroupId: 9507
}
rows {
  id: 100009
  desInfo: "闪电赛"
  startTime {
    seconds: 1750953600
  }
  endTime {
    seconds: 1755791999
  }
  qualifyingID: 100001
  qualifyType: 10
  seasonId: 13
  qualifyIconStyle: 1
  taskGroupId: 9508
}
rows {
  id: 100010
  desInfo: "闪电赛"
  qualifyingID: 100001
  qualifyType: 10
  seasonId: 14
  qualifyIconStyle: 1
  taskGroupId: 9509
  playModeSeasonId: 1007
}

syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCondition.proto";

message ChunkGroupData {// @noSvr
  option (resKey) = "groupId";
  optional int32 groupId = 1;     //包组id
  optional string name = 2;     //包组名字
  optional string desc = 3;     //包组描述
  optional string type = 4;     //包组类型
  optional bool isUGC = 5;     //是否元件包组
  optional bool isAutoDownlod = 6;     //是否自动下载
  optional bool isAutoDownlodOnCloud = 7;     //云环境下是否自动下载
  optional int32 autoDownloadOrder = 8;     //自动下载顺序
  repeated string chunkGroup = 9;  // 包含的包体
  optional bool isShowInMgr = 10;     //是否显示在管理界面
  optional int32 showInMgrGroupId = 11;     //显示在管理界面包组
  optional int32 showOrder = 12;     //显示顺序
  optional int32 taskId = 13;     //下载奖励任务
  optional string image = 14;     //预览图
  optional int32 downloadSize = 15;     //默认下载大小
  optional bool staticMount = 16;     //是否下载完成静默挂载
  repeated string downloadPlatform = 17;  // 需要进行下载的平台
  optional int32 downloadScene = 18;     //可下载场景
  optional bool CanBeClean = 19;     //是否能被清理
  optional bool forceAutoDownload = 20; //是否强制下载
  optional int32 downloadLevel = 21; //下载优先级
  optional int32 Tabid = 22;  // 所属页签
  optional int32 replaceMountGroupId = 23; // 替代Group
  optional int32 downisShowInMgr = 24;
  optional string LaunchDate = 25;     //上线日期
  optional string groupWindow = 26;     //资源包窗口
  repeated int32 frontgroup = 27;     //前置包组
  repeated string Tags = 28;     //包组Tags
  optional bool ListStatus = 29;  //上架状态
}

message table_ChunkGroupData {// @noSvr
  repeated ChunkGroupData rows = 1;
}

message table_ChunkGroupDataVA {// @noSvr
  repeated ChunkGroupData rows = 1;
}

message ChunkGroupCatalogue {// @noSvr
  option (resKey) = "Tabid";
  optional int32 Tabid = 1; //页签id
  optional string Tabname = 2; //页签名字
}

message table_ChunkGroupCatalogue {// @noSvr
  repeated ChunkGroupCatalogue rows = 1;
}

enum AvatarChunkMeshType {// @noSvr
  LOD1 = 0;     // 使用LOD1的模型
  TEMP = 1;     // 使用临时模型(xxx_pak)
  ORIGIN = 2; // 使用原本的模型
}

message AvatarChunkData {// @noSvr
  option (resKey) = "itemId";
  optional int32 groupId = 1;     // 包组ID
  optional int32 itemId = 2;      // 时装道具ID
  optional string chunkId = 3;    // 分包ID
  optional AvatarChunkMeshType meshType = 4; // 资源未下载时使用的模型资源类型
  optional bool includePV = 5;  // 是否分离pv
  optional int32 lod1GroupId = 6;   // LOD1包组ID
  optional string lod1ChunkId = 7;  // LOD1分包ID
  optional int32 mergedGroupId = 8; // 合并包组ID
}

message table_AvatarChunkData {// @noSvr
  repeated AvatarChunkData rows = 1;
}

message StaticMountPakData {// @noSvr
  option (resKey) = "pakName";
  optional string pakName = 1;     //pak名字
  optional bool staticMount = 2;     //是否下载完成静默挂载
}

message table_StaticMountPakData {// @noSvr
  repeated StaticMountPakData rows = 1;
}

message MountPakOrderData {// @noSvr
  option (resKey) = "pakName";
  optional string pakName = 1;    //pak名字
  optional int32 mountOrder = 2;  //挂载优先级
}

message table_MountPakOrderData {// @noSvr
  repeated MountPakOrderData rows = 1;
}

message LevelChunkData {// @noSvr
  option (resKey) = "name";
  optional int32 groupId = 1;     // 包组ID
  optional string name = 2;      // 关卡大图名字
  optional string nameSmall = 3;    // 对应关卡小图名字
}

message table_LevelChunkData {// @noSvr
  repeated LevelChunkData rows = 1;
}

message MainUIGroupData {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;    //id
  optional int32 groupId = 2;     //包组id
  optional string name = 3;     //包组名字
  repeated string chunkGroup = 4;  // 包含的包体
  optional bool bCheck = 5;     //是否开启分包检测
}

message table_MainUIGroupData {// @noSvr
  repeated MainUIGroupData rows = 1;
}

message AvatarDanceData {// @noSvr
  option (resKey) = "itemid";
  optional int32 itemid = 1;     //道具id
  optional string name = 2;     //道具名称
  optional int32 groupId = 3;     //包组id
  optional string chunkid = 4;     //分包ID
  optional bool bCheck = 5;     //是否开启分包检测
  optional string AssetName = 6;   //资源名称
}

message table_AvatarDanceData {// @noSvr
  repeated AvatarDanceData rows = 1;
}

message ResourceDefaultShowData {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;  //id
  optional int32 type = 2;  //处理类型
  optional int32 itemType = 3;  //道具类型
  repeated string chunkGroup = 4;  //包组
  optional string defaultImg = 5;  //默认图片
  repeated int32 whiteList = 6;  //不处理的白名单id列表
}

message table_ResourceDefaultShowData {// @noSvr
  repeated ResourceDefaultShowData rows = 1;
}

message GroupWindowData {// @noSvr
  option (resKey) = "groupWindow";
  optional string groupWindow = 1;  //资源包窗口字段，链接包组表使用
  optional string name = 2;     //名字
  optional string desc = 3;     //描述
  optional string image = 4;     //预览图
}

message table_GroupWindowData {// @noSvr
  repeated GroupWindowData rows = 1;
}

message RecommendDownloadData {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;  //集合id
  optional string name = 2;     //名字
  optional string desc = 3;     //描述
  repeated int32 groupIds = 4;     //集合包含的包组id
}

message table_RecommendDownloadData {// @noSvr
  repeated RecommendDownloadData rows = 1;
}
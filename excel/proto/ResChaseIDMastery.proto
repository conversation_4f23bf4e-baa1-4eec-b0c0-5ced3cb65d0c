syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCommon.proto";
import "google/protobuf/timestamp.proto";

/////////////////////////////////////////////////////////////////////  身份专精  /////////////////////////////////////////////////////////////////////

message ChaseIDMasteryBoss {
    option (resKey) = "ActorID";
    optional int32 ActorID = 1;
    optional int32 IDType = 2;
    optional string IDIcon = 3;// @noSvr
    optional string IDName = 4;
    optional int32 ShowSort = 5;// @noSvr
    optional int32 SkinSort = 6;
    optional string IDStory = 7;// @noSvr
    repeated int32 IDTag = 8;
    optional string SkinIcon = 9;
    repeated int32 Skill = 10;
    optional string BG = 11;// @noSvr
    optional string Icon = 12;// @noSvr
    optional string HalfViewIcon = 13;// @noSvr
    optional string FullViewIcon = 14;// @noSvr
    repeated int32 SkinIds = 15;
    optional int32 CameRoleType = 16;
    optional string AttackIcon = 17;
    optional string CommonSkillIcon = 18;
    optional string BossSkillIcon = 19;
    optional string InlevelPrepareIcon = 20;
	optional string EnterSceneAnim = 21;
}

message table_ChaseIDMasteryBoss {
    repeated ChaseIDMasteryBoss rows = 1;
}

message ChaseIDMasteryPlayer {
    option (resKey) = "ActorID";
    optional int32 ActorID = 1;
    optional int32 IDType = 2;
    optional string IDIcon = 3;// @noSvr
    optional string IDName = 4;
    optional int32 ShowSort = 5;// @noSvr
    optional int32 SkinSort = 6;
    optional string IDStory = 7;// @noSvr
    repeated int32 IDTag = 8;
    optional string SkinIcon = 9;
    repeated int32 Skill = 10;
    optional string BG = 11;// @noSvr
    optional string Icon = 12;// @noSvr
    optional string HalfViewIcon = 13;// @noSvr
    optional string FullViewIcon = 14;// @noSvr
    repeated int32 SkinIds = 15;
    optional int32 CameRoleType = 16;
    repeated float PreviewOffsetPos = 17;
    repeated float PreviewOffsetRot = 18;
    optional string InlevelPrepareIcon = 19;
    repeated float InlevelPrepareOffsetPos = 20;
    repeated float InlevelPrepareOffsetRot = 21;
    repeated float InlevelPrepareOffsetScale = 22;
}

message table_ChaseIDMasteryPlayer {
    repeated ChaseIDMasteryPlayer rows = 1;
}

message ChaseIDMasteryBossSkill {
    option (resKey) = "SkillID";
    optional int32 SkillID = 1;
    optional string SkillName = 2;
    optional string IDName = 3;
    optional int32 ActorID = 4;
    optional int32 SkillType = 5;
    optional string SkillIcon = 6;
    repeated int32 SkillTag = 7;
    optional string SkillCD = 8;
    optional string SkillSimpleDesc = 9;// @noSvr
    optional string SkillDetailDesc = 10;// @noSvr
    optional string SkillIconBg = 11;// @noSvr
}

message table_ChaseIDMasteryBossSkill {
    repeated ChaseIDMasteryBossSkill rows = 1;
}

message ChaseIDMasteryPlayerSkill {
    option (resKey) = "SkillID";
    optional int32 SkillID = 1;
    optional string SkillName = 2;
    optional string IDName = 3;
    optional int32 ActorID = 4;
    optional int32 SkillType = 5;
    optional string SkillIcon = 6;
    repeated int32 SkillTag = 7;
    optional string SkillCD = 8;
    optional string SkillSimpleDesc = 9;// @noSvr
    optional string SkillDetailDesc = 10;// @noSvr
    optional string SkillIconBg = 11;// @noSvr
}

message table_ChaseIDMasteryPlayerSkill {
    repeated ChaseIDMasteryPlayerSkill rows = 1;
}

message ChaseIDMasteryBossPublicSkill {
    option (resKey) = "SkillID";
    optional int32 SkillID = 1;
    optional string SkillName = 2;
    optional string SkillIcon = 3;
    optional string SkillIconBg = 4;// @noSvr
    optional string SkillCD = 5;
    optional string SkillSimpleDesc = 6;// @noSvr
    optional string SkillDetailDesc = 7;// @noSvr
}

message table_ChaseIDMasteryBossPublicSkill {
    repeated ChaseIDMasteryBossPublicSkill rows = 1;
}

message ChaseIDMasteryIDTag {
    option (resKey) = "TagID";
    optional int32 TagID = 1;
    optional string TagName = 2;
    optional int32 TagType = 3;
    optional string BackgroundColor = 4;
    optional string FontFrameColor = 5;
}

message table_ChaseIDMasteryIDTag {
    repeated ChaseIDMasteryIDTag rows = 1;
}

message ChaseIDMasteryRankIcon {// @noSvr
    option (resKey) = "IconLevel";
    optional int32 IconLevel = 1;
    optional string Icon = 2;
}

message table_ChaseIDMasteryRankIcon {// @noSvr
    repeated ChaseIDMasteryRankIcon rows = 1;
}

message ChaseIDMasteryMatchTypeGroup {
    option (resKey) = "matchTypeGroupId";
    optional int32 matchTypeGroupId = 1;
    repeated int32 matchType = 2;
}
// 专精玩法分组
message table_ChaseIDMasteryMatchTypeGroup {// @noCli
    repeated ChaseIDMasteryMatchTypeGroup rows = 1;
}

message ChaseIDMasteryBattleRankScore {
    option (resKey) = "rankNo";
    optional int32 rankNo = 1;
    repeated ChaseIDMasteryBattleRankGroupScoreInfo groupScoreInfo = 2;
}
message ChaseIDMasteryBattleRankGroupScoreInfo {
    optional int32 matchTypeGroupId = 1;
    optional int32 score = 2;
}
// 对局排名分
message table_ChaseIDMasteryBattleRankScore {// @noCli
    repeated ChaseIDMasteryBattleRankScore rows = 1;
}

message ChaseIDMasteryBattleTeamRankScore {
    option (resKey) = "teamResult";
    optional int32 teamResult = 1;
    optional int32 battleScore = 2;
    optional int32 performanceScore = 3;
}
// 队伍名次分
message table_ChaseIDMasteryBattleTeamRankScore {// @noCli
    repeated ChaseIDMasteryBattleTeamRankScore rows = 1;
}

message ChaseIDMasteryGlobalRankScore {
    option (resKey) = "globalRank";
    optional int32 globalRank = 1;
    optional int32 score = 2;
}
// 对局队伍名次分
message table_ChaseIDMasteryGlobalRankScore {// @noCli
    repeated ChaseIDMasteryGlobalRankScore rows = 1;
}

message ChaseIDMasteryScoreRankTitleInfo {
    optional int32 identityId = 1;          // 身份ID
    optional int32 titleId = 2;         // 奖励称号
}

message ChaseIDMasteryScoreRank {
    option (resKey) = "rankLevel";
    optional int32 rankLevel = 1;                               // 排行榜级别（级别越高值越小）
    optional int32 geoLevel = 2;                                // 行政区级别（enum GeoLevel，全服默认0）
    optional int32 intervalLeft = 3;                            // 最高名次
    optional int32 intervalRight = 4;                           // 最低名次
    optional int32 badgeId = 5;                                 // 奖励徽章
    repeated ChaseIDMasteryScoreRankTitleInfo title = 6;   // 奖励称号
    optional string rankName = 7;                               // 排行榜名称（用于结算邮件）
}
// 排行榜
message table_ChaseIDMasteryScoreRank {
    repeated ChaseIDMasteryScoreRank rows = 1;
}



// 杂项配置
message ChaseIDMasteryMisc {
    option (resKey) = "id";
    optional ChaseIDMasteryMiscConfigType id = 1;
    optional int32 value = 2;
    repeated int32 valueList = 3;
}

message table_ChaseIDMasteryMisc {
    repeated ChaseIDMasteryMisc rows = 1;
}

message ChaseIDMasteryLowAcceleration {
    option (resKey) = "lowAccId";
    optional int32 lowAccId = 1;                                // 加速ID
    optional int32 percentageLeft = 2;                          // 专精占段位上限百分比左区间
    optional int32 percentageRight = 3;                         // 专精占段位上限百分比右区间
    optional int32 accPercentage = 4;                           // 加速比例（百分定点数）
}
// 低专精加速
message table_ChaseIDMasteryLowAcceleration {
    repeated ChaseIDMasteryLowAcceleration rows = 1;
}

message ChaseIDMasteryProtection {
    option (resKey) = "protectionId";
    optional int32 protectionId = 1;                            // 保护ID
    optional int32 scoreLimit = 2;                              // 专精分上限
    optional int32 decLimit = 3;                                // 扣减上限
}
// 低专精保护
message table_ChaseIDMasteryProtection {
    repeated ChaseIDMasteryProtection rows = 1;
}

/////////////////////////////////////////////////////////////////////  身份解锁  /////////////////////////////////////////////////////////////////////

enum EChaseIDMasteryUnlockCondition {
    Free = 1 [(name) = "免费"];
    ChaseCoin = 2 [(name) = "大王币"];
    Diamond = 3 [(name) = "星钻"];
    PlayerVictory = 4 [(name) = "星宝胜利"];
    BossVictory = 5 [(name) = "暗星胜利"];
}
// 身份解锁条件
message ChaseIDMasteryUnlockCondition {
    optional EChaseIDMasteryUnlockCondition Condition = 1;
    optional int32 Value = 2;
    optional google.protobuf.Timestamp DiscountBeginTime = 3;
    optional google.protobuf.Timestamp DiscountEndTime = 4;
    optional int32 Discount = 5;
	optional int32 Value2 = 6;
}
// 身份解锁
message ChaseIDMasteryUnlock {
    option (resKey) = "ActorId";
    optional int32 ActorId = 1;
    repeated ChaseIDMasteryUnlockCondition Conditions = 2;
    optional string UnlockDesc = 3;
    optional string PreviewUnlockDesc = 4;
    optional int32 unlockItemId = 5;
}
// 身份解锁
message table_ChaseIDMasteryUnlock {
    repeated ChaseIDMasteryUnlock rows = 1;
}
// 限免
message ChaseIDMasteryLimitFree {
    option (resKey) = "Id";
    optional int32 Id = 1;
    optional google.protobuf.Timestamp BeginTime = 2;       // 限免开始时间
    optional google.protobuf.Timestamp EndTime = 3;         // 限免结束时间
    repeated int32 MatchTypes = 4;                          // 限免生效玩法
    repeated int32 ActorIds = 5;                            // 身份ID
}
// 限免
message table_ChaseIDMasteryLimitFree {
    repeated ChaseIDMasteryLimitFree rows = 1;
}
// 货币配置
enum EChaseCoinConfig {// @noSvr
    Victory = 1 [(name) = "胜利"];
    Defeat = 2 [(name) = "失败"];
    Even = 3 [(name) = "平局"];
    DailyFirstGame = 4 [(name) = "每日首局"];
    WeekendCoefficient = 5 [(name) = "周末加成系数"];
    WeekLimit = 6 [(name) = "每周上限"];
}
message ChaseCoinConfig {// @noSvr
    option (resKey) = "Config";
    optional EChaseCoinConfig Config = 1;
    optional int32 Value = 2;
}
// 大王币
message table_ChaseCoinConfig {// @noSvr
    repeated ChaseCoinConfig rows = 1;
}

// 老玩家解锁
message ChaseIDMasteryOldPlayerUnlock {//@nocli
    option (resKey) = "ActorId";
    optional int32 ActorId = 1;
    optional int32 unlockMasteryScore = 2;
}
// 限免
message table_ChaseIDMasteryOldPlayerUnlock {//@nocli
    repeated ChaseIDMasteryOldPlayerUnlock rows = 1;
}

/////////////////////////////////////////////////////////////////////  熟练度  /////////////////////////////////////////////////////////////////////

message ChaseIdentityBiographyConfig{
    option (resKey) = "ActorId,biographyId";
    optional int32 ActorId = 1;
    optional int32 biographyId = 2;
    repeated int32 unlockTaskIds = 4;
    optional int32 defaultUnlock = 5;
    optional string biographyText = 6; //@noSvr
    repeated int32 targetTaskIds = 7;
}

message table_ChaseIdentityBiographyConfig{
    repeated ChaseIdentityBiographyConfig rows = 1;
}

message ChaseIdentityProficiencyConfig{
    option (resKey) = "ActorId,ProficiencyId";
    optional int32 ActorId = 1;
    optional int32 ProficiencyId = 2;
    optional int32 TaskId = 3;  //不使用
    optional int32 unLockFightPower    = 4;
    optional int32 bigReward   = 5;
    repeated Item rewardItem = 6; //奖励
    optional int32 needProficiency = 7;
    optional int32 needUnlockBiography = 8;
}

message table_ChaseIdentityProficiencyConfig{
    repeated ChaseIdentityProficiencyConfig rows = 1;
}

message ChaseIdentityMvpAddProficiencyConfig{
    option (resKey) = "id";
    optional int32 id = 1;
    optional int32 mvpScore = 2;
    optional float addCoefficient = 3;
}

message table_ChaseIdentityMvpAddProficiencyConfig{
    repeated ChaseIdentityMvpAddProficiencyConfig rows = 1;
}

message ChaseIdentityProficiencyMiscConfig{
    option (resKey) = "id";
    optional ChaseIdentityProficiencyConfigType id = 1;
    optional int32 value = 2;
    repeated int32 valueList = 3;
}

message table_ChaseIdentityProficiencyMiscConfig{
    repeated ChaseIdentityProficiencyMiscConfig rows = 1;
}
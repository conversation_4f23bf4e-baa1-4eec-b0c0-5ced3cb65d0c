syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;

// @noCheck
import "ResKeywords.proto";

// 农场等级称号配置
message FarmLevelTitleConf {
  option (resKey) = "lv";
  optional int32 lv = 1; // 等级
  optional string titleName = 2; // 称号名称
  optional int32 titleLv = 3; // 称号等级
  optional string titleIcon = 4; // 称号图标
  optional string titleIcon2 = 5; // 称号图标2
  optional int32 titleStage = 6; // 称号阶段
  optional string titleShowLv = 7; // 社交神农等级显示
}

message table_FarmLevelTitleConf {
  repeated FarmLevelTitleConf rows = 1;
}
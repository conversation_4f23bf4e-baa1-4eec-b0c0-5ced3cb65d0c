syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;

// @noCheck
import "ResKeywords.proto";
import "google/protobuf/timestamp.proto";


// 农场模块开放时间配置
message FarmModuleOpenTimeConf { // @noCli
  option (resKey) = "moduleId";
  optional int32 moduleId = 1;      // 模块id
  optional string openTime = 2;     // 开放时间
  optional string allowWhite = 4;   // 允许白名单
}

message table_FarmModuleOpenTimeConf {  // @noCli
  repeated FarmModuleOpenTimeConf rows = 1;
}

// 农场模块id枚举
enum FarmModuleIdEnum {
  FMI_Unknown = 0;
  FMI_Animal = 1;                             // 畜牧
  FMI_Fertilize = 2;                          // 祈福
  FMI_MonthCard = 3;                          // 月卡
  FMI_UAV = 4;                                // 无人机
  FMI_Fish = 5;                               // 钓鱼
  FMI_FishCard = 6;                           // 鱼卡
  FMI_BuildingSkin = 7;                       // 建筑皮肤
  FMI_Gift = 8;                               // 玩家送礼
  FMI_House = 9;                              // 小屋
  FMI_Pet = 10;                               // 宠物
  FMI_Aquarium = 11;                          // 水族馆
  FMI_Collection = 12;                        // 收藏品
  FMI_Event = 13;                             // 事件
  FMI_EventFish = 14;                         // 事件系统-钓鱼女神
  FMI_EventWeather = 15;                      // 事件系统-天气之子
  FMI_ReportFish = 16;                        // 钓鱼周报
  FMI_Talent = 17;                            // 神农宝典
  FMI_PetMenu = 18;                           // 宠物三期菜单
  FMI_EventNPCWater = 19;                     // 事件系统-水精灵
  FMI_TalentVile = 20;                        // 神农宝典-霸王花
  FMI_NPCFox = 21;                            // 村民一期-小红狐(特殊)开启时间
  FMI_FishMerge = 22;                         // 鱼类合成
  FMI_EventWeatherSnow = 23;                  // 事件系统-天气之子-下雪
  FMI_FishGuarantee = 24;                     // 钓鱼金额保底
  FMI_EventMarket = 25;                       // 事件系统-云游商人
  FMI_Villager = 26;                          // 村民二期
  FMI_UAVLow = 27;                            // 有人车
  FMI_FurnitureClockSelect = 28;              // 物件点选功能
  FMI_Statue = 29;                            // 神像功能（召唤+许愿）
  FMI_StatueMagic = 30;                       // 宝典2个仙术
  FMI_HotSpring = 31;                         // 温泉
  FMI_UAVUltra = 32;                          // 超级无人机
  FMI_FriendListSignature = 33;               // 好友列表签名
  FMI_FriendListSignatureStranger = 34;       // 好友列表（陌生人）签名
  FMI_NPCCow = 35;                            // NPC奶牛一期
  FMI_NPCCowTrans = 36;                       // NPC奶牛二期-牛语翻译器收藏品掉落事件
  FMI_RedFoxFarm = 38;                        // 正式小红狐农场是否开启
  FMI_NpcGiveFishCard = 39;                   // 神农莹草阶段可以选择让水精灵补鱼卡
  FMI_Kirin = 40;                             // 仙麒麟
  FMI_MagicRevive = 41;                       // 枯木逢春
  FMI_MagicDelay = 42;                        // 缓时咒
  FMI_Blueprint = 44;                         // 图纸
  FMI_VillagerStolenGift = 45;                // 村民被偷补偿
  FMI_Cat = 46;                               // 宠物猫
  FMI_CookBluePrint = 47;                     // 餐厅图纸
  FMI_NpcCowThird = 48;                       // NPC奶牛三期-高等级回流玩家快速完成牛牛任务体验餐厅
  FMI_RedFoxFertilize = 49;                   // 小红狐祈福
  FMI_RedFoxFarmUnlock = 50;                  // 小红狐农场解锁
  FMI_NpcCowFourth = 51;                      // NPC奶牛四期-小红狐农场奶牛对话发放藏品
  FMI_PartyKeywordWeight = 53;                // 派对关键字权重
  FMI_Cook = 66;                              // 餐厅
  FMI_CloudTax = 67;                          // 云游商人收税
  FMI_MyStealRecord = 68;                     // 我的偷菜记录
  FMI_SampleRoom = 69;                        // 样板间
  FMI_RecruitmentMarketFireItem = 70;         // 解雇员工获得人才信
  FMI_HighRecruitmentMarket = 71;             // 高级人才市场
}

// 农场实时键值配置
message FarmRealTimeKVConf { // @noCli
  option (resKey) = "K";
  optional string K = 1;
  optional string V = 2;
}

message table_FarmRealTimeKVConf { // @noCli
  repeated FarmRealTimeKVConf rows = 1;
}
syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCommon.proto";
import "google/protobuf/timestamp.proto";
import "ResCondition.proto";


// 大师之路解锁
message MasterPathLockConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional MasterPatchEnum masterPatchEnum = 2;//大师之路类型
  optional int32 cycle = 3;//周目
  optional ResConditionGroup conditionGroup = 4;      //解锁条件组
  optional string RoundName = 5;             // 解锁条件文本 @noSvr
}

message table_MasterPathLockConfig {
  repeated MasterPathLockConfig rows = 1;
}



// 总进度奖杯阶段配置
message MainMaterPathStageConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional MasterPatchEnum masterPatchEnum = 2;
  optional int32 cycleId = 3;
  optional int32 progress = 4;              // 需要进度
  repeated KeyValueInt32 rewardList = 6;         // 奖励列表
  optional string name = 20;               // 名称 @noSvr
  optional string icon = 21;               // 节点图标 @noSvr
  optional string numIcon = 22;            // 数字图标 @noSvr
  optional int32 isBigReward = 23;        // 是否是大奖 @noSvr
  optional string awardHint = 24;         // 奖品提示   @noSvr
  optional int32 openAnimation = 25;      // 开奖动画    @noSvr
  optional string boxUmg = 26;            // 活动界面的宝箱umg  @noSvr
  optional string boxImgage = 27;         // 主界面宝箱图片  @noSvr
}

message table_MainMaterPathStageConfig {
  repeated MainMaterPathStageConfig rows = 1;
}



// 杂项配置
message MainMaterPathConfig {
  option (resKey) = "id";
  optional MasterPatchConfigEnum id = 1;
  optional int32 value = 2;                             // 数字值
  optional string text = 3;                             // 文本
  optional google.protobuf.Timestamp time = 4;          // 时间
}

message table_MainMaterPathConfig {
  repeated MainMaterPathConfig rows = 1;
}
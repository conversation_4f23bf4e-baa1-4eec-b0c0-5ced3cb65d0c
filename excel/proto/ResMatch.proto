syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
// @noCheck
import "google/protobuf/timestamp.proto";

import "ResKeywords.proto";
import "ResCommon.proto";
import "ResCondition.proto";

enum MatchTypeSettleProc {
  MTSC_Default = 0;
  MTSC_Common = 1 [(name) = "通用"];
  MTSC_Rank = 2 [(name) = "排位"];
  MTSC_CommonWinsAndLosses = 3 [(name) = "通用胜平负"];
  MTSC_IdentityV = 4 [(name) = "第五人格"];
  MTSC_Camp = 5 [(name) = "阵营"];
}

message LobbyMatchCfgData {
  optional bool isLobbyMatchMode = 1; //是否大厅匹配玩法
  optional string openHourS = 2; //格式12:14;18:20
  optional google.protobuf.Timestamp startTime = 3; // 开始时间
  optional google.protobuf.Timestamp endTime = 4;  // 结束时间
}

message MatchType {
  option (resKey) = "id";
  optional int32 id = 1; //玩法ID
  optional int32 modeID = 2; //模式ID
  optional string desc = 3; //描述
  optional int32 maxTeamMember = 4; //最大组队人数MatchModeSortInfo
  optional ResConditionGroup conditionGroup = 5; //解锁条件
  optional bool isPermanent = 6; //是否常驻
  optional int32 sort = 7; //排序
  optional string thumbImage = 8; //模式缩略图
  optional string image = 9; //模式大图
  optional string showType = 10; //展示样式
  optional string detailDesc = 11; //玩法规则
  optional int32 beDefault = 15; //
  optional int32 beRecruitDefault = 16; // 招募发布时的默认模式
  optional string categoryDescription = 17; // 玩法品类描述
  optional string modeGroup = 20;
  optional MatchTypeSettleProc settleProc = 29;
  repeated int32 matchTeamNum = 30;
  optional string teamTag = 31;
  optional string unlockRule = 32;
  optional int32 battleRecordCnt = 33;
  optional int32 matchRuleId = 34;
  optional int32 ReCampNum = 35; // DS 重新分配阵营数量
  optional bool TeamMatchGame = 36;
  optional int32 battlePlayerNum = 37; // 对局玩家人数（包含AI）
  optional int32 dropId = 38; // 玩法结算掉落
  optional MMRScoreType mmrType = 39; // MMR算子
  optional MMRScoreAggrType mmrAggrType = 40; // 组队下 MMR算子聚合类型
  optional MMRScoreSettlementType mmrSettlementType = 41; // MMR算子结算方式
  optional WarmRoundScoreType warmRoundType = 42; // 温暖局类型
  optional bool NeedShowProcessDisplay = 43; // 是否显示展示流程
  optional bool isShowBattleRecord = 44;// 是显示战斗记录
  optional string playTypeDesc = 45;// 显示玩法模式类型描述
  optional string descShort = 46;// 简单描述
  optional int32 warmDropId = 47; // 温暖局玩法结算掉落
  optional string battleRecordStyle = 48; // 历史战绩中结果样式
  optional string outImage = 49; // 模式缩略图
  optional int32 pakGroup = 50; // 分包包组
  optional int32 recordType = 51; // BattleHistory => type
  optional string buttonDesc = 52; // 确认按钮描述
  optional string accountUIName = 53; // 局外结算UI名字，默认为UI_InLevelFinalAccount，默认情况可不填
  optional int32 gameTypeId = 54; // 玩法类型id， 决定启用的 ABaseGame 实例
  optional int32 layoutID = 55; // 自定义布局ID
  optional int32 isMainGame = 56; // 是否是主玩法
  optional int32 isShowEmotionEntrance = 57; // 是否表情互动入口按钮
  optional bool EnableRepGraph = 58;
  optional int32 warmRoundRoomInfoId = 59;    // 温暖局房间Id(非新手温暖局)
  optional string LowestVersion = 60; //最低版本号
  optional string HighestVersion = 61; //最高版本号
  repeated string ExcludeVersions = 62; //黑名单版本号
  optional int32 otherType = 63;
  optional int32 linkID = 64;// 跳转id
  optional string overrideDesc = 65; //覆写右侧玩法名
  optional string overrideButton = 66; //覆写右下脚按钮名字
  optional bool isHiddenWhenClose   = 68; // 是否开启活动关闭的时候不显示
  optional bool isOpenGrayScale     = 69; // 是否开启灰度
  optional int32 openPercent        = 70; // 灰度百分比
  optional int32 warmRoundMatchRuleId = 71;    // 温暖局规则Id(非新手温暖局)
  // optional int32 matchTypeGroup = 72;    // 模式类型分组
  optional bool noShowMatchNum     =  73; //不显示匹配人数
  optional int32 gameModeType = 74; //  游戏模式类型
  optional int32 videoLinkId = 75; //  视频链接Id
  optional int32 isDisableWarmRoundWhenMultiplayer = 76;  // 多人匹配时是否禁用温暖局
  optional string overrideImageDesc = 77;  // 缩略图名称覆写
  optional int32 isEnableAiLabIntervention = 78;  // 是否是可以参与ailab匹配的模式
  optional int32 randomSideType = 79;  // 随机阵营类型，应用的是ResKeywords.proto中的RandomSideType枚举值
  optional bool isOwnerStart = 80;  //自定义房间中，是否房主开启下一轮
  optional bool qualifyTimeIsShow = 81;  //控制排位玩法倒计时是否显示
  optional bool beCollect = 82;  //是否能被收藏
  optional bool UseDefaultChampionDisplayScene = 83; //是否使用默认的冠军展示场景
  optional int32 detailLinkId = 84;  //玩法详情页跳转
  optional string detailLinkDesc = 85;  //玩法详情页跳转描述
  optional int32 allowMidJoinBattle = 86;  // 是否允许中途加入对局
  optional int32 allowRemoveAfterEndBattle = 87;  // 结算之后移除玩家（为了玩家可以重复进出）
  repeated int32 roguelikeDifficulty = 88; //允许匹配的肉鸽难度
  optional bool bShowTeamEnter = 89; //是否进行队友展示
  optional string displayLevelPath = 90; //展示关卡路径
  optional int32 isShowTotalWin = 91; //是否显示胜局数
  optional int32 testRoomInfoId = 92;    // 测试环境的房间Id(通过gm命令打开)
  optional int32 detailLinkRedDot = 93;    // 玩法详情页跳转红点id
  optional bool isLightningGame = 94; // 是否是奖杯赛
  repeated int32 specialGroup = 95; //特殊玩法分组-奖杯赛 --已废弃！
  optional int32 ComplicatedPlay = 96; // 备战ID
  optional int32 secondaryNewbieWarmRoundRoomInfoId = 97;    // 副玩法新手温暖局房间Id(非guide新手温暖局)
  repeated string whitelistModuleId = 98;  // 白名单标签
  optional bool ignoreBestRecord = 99; // 不保存最佳记录
  optional string overridePreShowText = 100; //预告态按钮文本覆写
  optional bool bSkipFlyEnter = 101; //是否展示星梭飞行
  optional string previewShowIcon = 102; // 大厅左上角进入组队秀的图标
  optional string previewShowName = 103; // 大厅左上角进入组队秀的名称
  optional int32 battleBehaviorRankReport = 104; // UGC地图匹配时使用的MMR排序上报方式
  optional bool canDSMigrate = 106; //是否可以ds迁移
  optional string brifeBattleRecordDataLua = 107;  // 简要战绩数据源路径
  optional int32 detailBattleRecordPakId = 108;  // 详细战绩分包ID
  optional int32 aiLabThreeTeamHumanWarmRoundRoomInfoId = 109; // AiLab投放的三队真人温暖局房间Id
  optional int32 aiLabTwoTeamHumanWarmRoundRoomInfoId = 110; // AiLab投放的两队真人温暖局房间Id
  optional int32 aiLabOneTeamHumanWarmRoundRoomInfoId = 111; // AiLab投放的单队真人温暖局房间Id
  optional int32 aiLabOneHumanWarmRoundRoomInfoId     = 112; // AiLab投放的单个真人温暖局房间Id
  optional bool isEnableAiLabArenaWarmRound = 113; // 是否开启AiLab Arena温暖局
  optional int32 isEnableAiDifficultyTest = 114;  // 是否启用Ai难度实验组
  optional int32 aiLabWarmRoundRoomInfoId = 115; // AiLab投放的温暖局房间Id 不与投放玩家数量挂钩
  optional int32 aiLabWarmRoundMatchRuleId = 116; // AiLab投放的温暖局匹配规则Id
  repeated int32 AutoDownloadPakGroup = 117; // 自动下载包组
  repeated int32 AutoDeletePakGroup = 118; // 自动删除包组
  optional int32 PakPlayID = 119; //包体玩法ID

  optional string playName = 201; // 对应的玩法名称，比如JS,StarP
  optional bool useBasicMusicGroup = 202; //是否使用基础音乐包
  repeated int32 extraPakGroups = 203; //玩法需要额外mount的pakGroup
  optional bool preEndShowMatchName = 204; //玩法剩余时间时是否显示玩法名称
  optional string activityStyleParam = 205; // 活动样式参数
  optional bool bCloseMaxFPSOptimize = 206; // 是否关闭限帧优化(0-开启优化，1-关闭优化)
  optional bool customEqualTeamOnBattle = 207; //自定义房间开局和匹配房间一致

  optional google.protobuf.Timestamp detailLinkCard_starttime = 208; // 细节气泡开始时间
  optional google.protobuf.Timestamp detailLinkcard_endtime = 209;  // 细节气泡结束时间
  optional string detailLinkCardContent = 210;//细节气泡文本

  optional string detailLinkIcon = 211; // 玩法详情页跳转图标
  repeated int32 NonessentialpakGroup = 212; // 非必要包组
  repeated string playTagArr = 213; // 玩法tag组
  optional string playShow = 214; // 玩法特征显示
  optional string SimpleModelMatchDesc = 215; // 简单模式选择界面 玩法页签右上角描述



  optional int32 loginResetCheckLock = 216; // 玩法登录重置解锁 1-每次登陆要重新判定是否解锁
  repeated int32 loginPlatList = 217; // 登录屏蔽平台(不填为所有平台)
  optional bool isBan = 218; // 是否开启平台屏蔽(0-不屏蔽，1-屏蔽)
  optional bool useDynamicCfg = 219; //允许使用动态配置
  optional bool isOpenRandEvent = 220; //开启随机事件逻辑
  optional int32 preparationViewId = 221; // 通用备战页面ID
  optional LobbyMatchCfgData lobbyMatchCfgData = 222; //万松书院大厅匹配配置

  repeated int32 cloudSysList = 223; // 登录屏蔽云游平台(不填为所有平台)
  optional bool cloudIsBan = 224; // 是否开启云游平台屏蔽(0-不屏蔽，1-屏蔽)

  optional bool isArena = 225;//是否为Arena
  optional bool jumpRandomLevelSequence = 226; //sequence是否跳过关卡随机镜头
  optional bool bUseDetailedRecordDisplay = 227; //冲线破纪录时，使用新版layout
  optional bool openAIHosting = 228; //是否开启AI托管

  optional bool isEnableAiLabChaseWarmRound = 229; // 是否开启AiLab Chase温暖局
  optional int32 aiLabChaseWarmRoundRoomInfoId = 230; // AiLab投放的温暖局房间Id
  optional int32 aiLabChaseWarmRoundMatchRuleId = 231; // AiLab投放的温暖局规则Id

  optional int32 aiLabChaseWarmRoundDarkStarRoomInfoId = 232; // AiLab投放的温暖局房间Id 阵营id为1的暗星全ai
  optional int32 aiLabChaseWarmRoundDarkStarMatchRuleId = 233; // AiLab投放的温暖局规则Id 阵营id为1的暗星全ai

  optional bool enableAICommentary = 234; //是否开启AI解说

  repeated int32 aiLabEquivalentRoomInfo = 235; // AiLab投放的对等真人局房间
  repeated int32 aiLabEquivalentMatchRule = 236; // AiLab投放的对等真人局的匹配规则
  optional string battleDetailStyle = 237; // 对局详情样式
  // 当randomSideType存在时, 是否禁止room svr侧的预先为选择随机阵营的玩家设置阵营(假的随机)
  optional int32 isDisableRoomSideFakeRandomSelect = 238;
}

message table_MatchTypeData {
  repeated MatchType rows = 1;
}

message MatchRecommendPageData {
  option (resKey) = "id";
  optional int32 id = 1;                  // id
  optional int32 row = 2;                 // 推荐行数
  optional int32 position = 3;            // 推荐位置
  optional int32 type = 4;                // 推荐类型. 参考 MatchRecommendType
  repeated int64 publicParam = 5;         // 推荐公共参数[;] (0==type:推荐玩法id 1:==type:..)
  optional google.protobuf.Timestamp beginEpochMillis = 6;    // 开始时间戳毫秒
  optional google.protobuf.Timestamp endEpochMillis = 7;      // 结束时间戳毫秒
  optional string thumbImageBig = 8;      // 玩法缩略大图
  optional bool isRemoveDuplication = 9;  // 是否去重
  optional bool isGuaranteeSlot = 10;     // 是否保底配置
  optional bool isShowRankIcon = 11;      // 是否显示排位图标
  optional int32 showType = 13;           // 显示类型
  optional string labelBackboard = 14;    // 推荐标签底板
  optional string labelText = 15;         // 推荐标签文本
  optional string icon = 16;              // 推荐icon
  optional string recommend = 17;         // 推荐语
  optional int32 itemType = 18;           // item类型
  optional string categoryDescription = 19; // 玩法品类描述
}

// 玩法推荐首页, 如果拉不到管理端配置, 则使用本保底配置
message table_MatchRecommendPageData {
  repeated MatchRecommendPageData rows = 1;
}

message MatchRecommendDefaultData {
  option (resKey) = "id";
  optional int32 id = 1;                  // 玩法id
  optional int32 showType = 2;            // 显示类型
  optional string labelBackboard = 3;     // 推荐标签底板
  optional string labelText = 4;          // 推荐标签文本
  optional string icon = 5;               // 推荐icon
  optional string recommend = 6;          // 推荐语
}

// 玩法推荐首页, 如果拉不到管理端配置, 则使用本保底配置
message table_MatchRecommendDefaultData {
  repeated MatchRecommendDefaultData rows = 1;
}

message MatchModeType {
  option (resKey) = "modeID";
  optional int32 modeID = 1;      // 玩法ID
  optional string modeDesc = 2;   // 描述
  optional int32 modeSort = 3;    // 排序
  optional int32 taskGroup = 4;   // 任务组id
  optional int32 bShow = 5;       // 是否展示
  optional string modeIcon = 6;   // 展示图标
  optional string modeTagIcon = 7;   // 页签图标
  optional int32 foldShowLimit = 8;  //展示数量限制
  repeated int32 showSceneId = 9; // 需要展示的场景
  optional int32 defaultModeId = 10; //默认玩法
  optional bool saveUnfoldStatus = 11; //保存展开状态
  repeated int32 forbidShowPlatforms = 12; //禁止展示的平台
  optional bool isDefaultTab = 13; //是默认页签
}

message table_MatchModeTypeData {
  repeated MatchModeType rows = 1;
}

message DisplayPointMap {
  option (resKey) = "id";         //
  optional int32 id = 1;      	  //映射id
  optional int32 playerNum = 2;   //人数
  optional string bindPointList = 3; //绑点映射下标
}

message table_DisplayPointMapData {
  repeated DisplayPointMap rows = 1;
}

message OpenPeriod {
  repeated int32 openWeek = 1;
  optional google.protobuf.Timestamp startTime = 2;
  optional google.protobuf.Timestamp endTime = 3;
  optional google.protobuf.Timestamp preShowTime = 4;
}

message MatchDate {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 playId = 2;
  optional google.protobuf.Timestamp startTime = 3;
  optional google.protobuf.Timestamp endTime = 4;
  optional google.protobuf.Timestamp preShowTime = 5;
  optional int32 RegionalGroupCommon = 6; //地区分组_通用
  optional OpenPeriod openPeriod = 7;
  optional bool bShowTime = 8; // @noSvr
  optional bool startTimeUseSeason = 9;         // 使用赛季时间作为开始时间
  optional bool endTimeUseSeason = 10;          // 使用赛季时间作为结束时间
  optional int32 playModeSeasonId = 11;         // 关联的玩法赛季id
}

message table_MatchDateData {
  repeated MatchDate rows = 1;
}

message GameLoseRule {
  optional int32 loseTimes = 1;
  optional int32 ruleId = 2;
  optional int32 roomInfoId = 3;
}

message GameTimeRule {
  optional int32 gameTimes = 1;
  repeated GameLoseRule gameLoseRules = 2;
}

//
message QualifyingRule {
  optional string qualifyingIntegral = 1;
  repeated GameTimeRule gameTimeRules = 2;
  repeated int32 qualifyingIntegralData = 3; // 辅助字段
}

message MatchRuleRangeExtra {
  optional int32 isWereWolfSideIdentity = 1;  //狼人杀指定身份和阵营
}

message MatchRuleRange {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated QualifyingRule qualifyingRule = 2;
  optional MatchRuleRangeExtra extra = 3;
}

message table_MatchRuleRangeData {
  repeated MatchRuleRange rows = 1;
}

message table_CustomRoomData{
  repeated CustomRoomRule rows = 1;
}

message MatchRecommendData {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 matchId = 2;
  optional google.protobuf.Timestamp startTime = 3;
  optional google.protobuf.Timestamp endTime = 4;
  optional string thumbImageBig = 5;
  optional bool bOrigin = 6;
  optional int32 jumpId = 7;
  optional int32 recOrder = 8;
  optional bool bSign = 9;
  optional int32 slot = 10;//推荐位槽位
  optional int32 guaranteeSlot = 11;//保底配置槽位
  optional bool isRemoveDuplication = 12;//是否去重
}

message table_MatchRecommendData {
  repeated MatchRecommendData rows = 1;
}

message table_MatchPublicInfoData{
  repeated MatchPublicInfo rows = 1;
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////




message DimensionRelation {
  optional string dimensionsStr = 1;
  repeated int32 dimensions = 2; // 维度集id
  optional MatchRelationOperType operation = 3; // 运算规则
}

//维度超时放宽
message DimensionRelax {
  optional int32 dimensionID = 1; // 维度id
  optional int32 scopeLeft = 2; // 放宽左开区间值
  optional int32 scopeRight = 3; // 放宽右开区间值
  optional int32 addWaitSec = 4; // 第N波添加等待的时间
}

message SpecialRuleCfg {
  optional SpecialRuleType specialRuleType = 1; //特殊规则类型关联特殊规则表具体sheet
  optional int32 specialRuleID = 2; //特殊规则配置id
}

message MatchRule {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string descInfo = 2; // 描述信息
  repeated int32 dimensions = 3; // 维度集id
  repeated DimensionRelation relations = 5;    //运算规则
  repeated DimensionRelax dimensionRelax = 6;    //维度超时放宽
  repeated DimensionRelax dimensionRelax2 = 7;    //维度超时放宽
  optional SpecialRuleCfg specialRuleCfg = 8; //特殊规则配置关联特殊规则表
}

message table_MatchRuleData {// @noCli
  repeated MatchRule rows = 1;
}

//匹配查找索引查找规则
message MatchFindRule {
  optional MatchFindIndexOper compare = 1; // 维度集id
  optional int32 scopeLeft = 2; // 匹配查找左区间
  optional int32 scopeRight = 3; // 匹配查找右区间
  optional int32 defaultValue = 4; // 随机时默认的插入值
  optional int32 expandScopeID = 5; // 可放宽区间配置ID
}

message MatchDimension {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string descInfo = 2; // 描述信息
  optional string dimName = 3; // 维度名字
  optional MatchCreateIndexOper createIndexOper = 4;     //建立索引运算规则
  optional MatchFindRule relations = 5;    //运算规则
  optional int32 isFilterValType = 6;      // 是否过滤器值型维度(没有范围比较, 不建立索引, 用过滤器实现细节)
}

message table_MatchDimensionData {// @noCli
  repeated MatchDimension rows = 1;
}

//阵营信息
message MatchSideInfo {
  optional int32 sideID = 1; // 阵营ID
  optional int32 teamPlayers = 2; // 阵营人数
}

//基本配置
message MatchBaseRule {
  optional string teamPlayersStr = 1; // 组队阵营信息
  optional int32 acceptRequired = 2; // 是否开启成功确认
  optional int32 accpetTimeout = 3; // 超时回到队列(开启匹配确认时生效)
  optional int32 minMatchNum = 4; // 最少可开启人数
  optional int32 allowModMatch = 5; // 是否允许Mod中匹配新队伍
  optional int32 allowMerge = 6; //匹配是否可以合并队伍
  optional int32 canStartNum = 7; //房间启动时  最少需要的人数 少于添加机器人
  optional string teamRobotsStr = 8; // 最少机器人阵营信息
  repeated MatchSideInfo teamPlayers = 9; // 组队阵营信息
  repeated int32 teamRobots = 10; // 最少机器人阵营信息(已废弃)
  repeated int32 reCamp = 11; // 重新分配阵营数
  optional string reCampStr = 12; // 重新分配阵营数
  optional int32 dynamicTeamRobotsID = 13; // 动态AI配置ID
  optional int32 warmScriptID = 14; // 普通温暖局剧本ID
  optional int32 isNotFillTeammate = 15; // 是否不撮合队友
  optional int32 maxNotFillTeammateNum = 16; // 最多不允许填充队友队伍数
  optional int32 isPlayerNotFillRobot = 17; // 是否真人阵营不补足AI
  optional bool joinOnBattle = 18; // 是否可以战斗中加入,随进随出
  optional int32 warmControlGroupScriptID = 19; // 普通温暖局对照组剧本ID
  optional int32 mPTeamOccupyAIPos = 20; // 多人队伍是否允许挤占AI坑位
  repeated MatchSideInfo teamRobotsSideInfo = 21; // 最少机器人阵营信息
  optional MatchingResultProcType matchingResultProcType = 22; // 匹配撮合结果处理类型
  optional bool priorityDsJoin   = 23; // 匹配的时候，优先已经存在的ds找空位加入
  optional int32 fillBackCfgID   = 24; // 回填配置ID
  optional int64 roomMaxBattleTimeSec = 25; //room维持最大的战斗时间，不配置走默认
  optional bool useDynamicCfg = 26; //允许使用动态配置
  optional int32 dynamicMinCntID = 27; //最小真人动态配置ID
}


//基本ds配置
message MatchDsBase {
  optional int64 maxBattleTimeSec = 1; //ds维持最大的战斗时间，不配置走默认
  optional int32 advancedDelDSTimesOnTerminate = 2; //ds有一个最大生存时间，在强杀之前通知ds删除rpc的时间，不配置就不设置
}

//队伍人数配置
//message MatchTeamInfoConf {
//  optional int32 maxNum = 1; // 每个队伍最大人数
//  optional int32 minNum = 2; // 每个队伍最少人数
//}

//超时处理
message MatchTimeoutHandler {
  optional int32 maxTimeoutSec = 1; // 房间建好之后，空置时间的控制，最大超时时间
  optional MatchTimeoutOper matchTimeoutOper = 2; // 超过最大超时时间处理
  optional int32 maxSideTimeoutSec = 3; // 最大阵营补足机器人时间
  optional int32 isEventHumanPlayerCnt = 4;  // 超时后是否平均阵营真人玩家数量
  optional int32 dynamicMatchTimeoutID = 5; // 动态最大超时配置ID
  optional int32 simulatorDynamicMatchTimeoutID = 6; // 模拟器动态最大超时配置ID
  optional int32 singleSideFullPlayerSucc = 7; // 最大阵营超时是否单阵营全真人也成功
}

message HOKMatchRoomConf {
  optional int32 aiType = 1; //指定该房间内机器人的类型，1表示行为树，2表示learningAI，不配置就不设置
  optional int32 sideId = 2; //指定该房间真人队伍阵营id，只在至少五机器人的情况下配置，不配置就不设置 1 蓝 2 红
  optional int32 trainAI = 3; //是否触发训练测试AI，目前是每日第三局打开开关，其它的不配置
  optional int32 aiInvite = 4; //是否触发AI邀请 只有排位才生效
}


message MatchRoomInfo {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string descInfo = 2; // 描述信息
  optional MatchBaseRule baseRule = 3; // 基本配置
  //  optional MatchTeamInfoConf teamInfo = 4;     //队伍配置
  optional MatchTimeoutHandler timeoutHandler = 5;    //超时处理
  optional MatchDsBase matchDsBase = 6; // ds配置
  optional HOKMatchRoomConf hokConf = 7; // hok 玩法专属配置
}

message table_MatchRoomInfoData {
  repeated MatchRoomInfo rows = 1;
}

message DsMapSet {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string descInfo = 2;
  optional int32 modId = 3;
  optional int32 ruleId = 4;
  optional string mapName = 5;
  optional string aliase = 6;
  optional string levelSeq = 7;
  optional int32 isPrivate = 8;
  optional int32 randomId = 9;
  repeated string versionList = 10;
  repeated int32 versionIdLists = 11;
  repeated int32 dropId = 12;
  optional int32 levelGuideId = 13;
}

message table_DsMapSetData {
  repeated DsMapSet rows = 1;
}

message MatchLevelPassScoreInfo {
  option (resKey) = "matchType";
  optional int32 matchType = 1;
  repeated int32 eliminateRoundsScore = 2;
  optional int32 championScore = 3;
}

message table_MatchLevelPassScoreData {
  repeated MatchLevelPassScoreInfo rows = 1;
}


message LevelPerformanceRankScore {
  repeated int32 rankRange = 1;
  optional int32 score = 2;
  optional int32 evaluateType = 3;
}

message LevelPerformanceScoreInfo {
  option (resKey) = "levelType";
  optional LevelType levelType = 1;
  optional int32 killScore = 2;
  optional int32 aliveScore = 3;
  repeated LevelPerformanceRankScore rankScore = 4;
  optional int32 winScore = 5;
  optional int32 saveScore = 6;
}

message table_LevelPerformanceScoreData {
  repeated LevelPerformanceScoreInfo rows = 1;
}

message LevelScoreGradeInfo {
  option (resKey) = "score";
  optional int32 score = 1;
  optional MatchGrade grade = 2;
  optional string pic = 3;
  optional string txtcont = 4;
}

message table_LevelScoreGradeData {
  repeated LevelScoreGradeInfo rows = 1;
}

message MatchCampConfig {
  option (resKey) = "id,campId";
  optional int32 id = 1;
  optional int32 campId = 2;
  optional int32 faction = 3;
  optional string campRole = 4;
  optional string icon = 5;
  optional string inLevelIcon = 6;
}

message table_MatchCampData {
  repeated MatchCampConfig rows = 1;
}

message MatchConstsInfo {// @noCli
  option (resKey) = "id";
  optional MatchConstEnum id = 1;
  optional int32 value = 2;
}

// 匹配常量表
message table_MatchConstsData {// @noCli
  repeated MatchConstsInfo rows = 1;
}

message DimensionExpandScopeItem {
  optional int32 scopeLeft = 1; // 匹配算子左区间
  optional int32 scopeRight = 2; // 匹配算子右区间
  optional int32 scopeLeftLimit = 3; // 可放宽左开区间值
  optional int32 scopeRightLimit = 4; // 可放宽右开区间值
  repeated int32 matchTypeId = 5;     // 关联的玩法模式id 可以为空 表示和玩法无关
}

message MatchExpandScopeLimit {
  option (resKey) = "id";
  optional int32 id = 1; //ID
  repeated DimensionExpandScopeItem expandScopeItem = 2; // 扩展信息
}

//维度上下限限制
message table_MatchExpandScopeLimitData {
  repeated MatchExpandScopeLimit rows = 1;
}

message MatchDynamicTeamRobotsItem {
  optional int32 scopeLeft = 1; // 匹配算子左区间
  optional int32 scopeRight = 2; // 匹配算子右区间
  optional string teamRobotsStr = 3; // 动态最少机器人阵营信息
  repeated int32 teamRobots = 4; // 动态最少机器人阵营信息(已废弃)
  repeated MatchSideInfo teamRobotsSideInfo = 5; // 动态最少机器人阵营信息
}

message MatchDynamicTeamRobots {
  option (resKey) = "id";
  optional int32 id = 1; //ID
  repeated MatchDynamicTeamRobotsItem dynamicTeamRobotsItem = 2; // 动态AI配置
}

//维度上下限限制
message table_MatchDynamicTeamRobotsData {
  repeated MatchDynamicTeamRobots rows = 1;
}

message GradeCondition {
  optional string descInfo = 1;    //描述信息
  repeated int32 dimensions = 2; // 维度集id
  optional MatchRelationOperType operation = 3;  //运算规则
  optional MatchGrade grade = 4;
}

message MatchCampGradeConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated GradeCondition gradeCondition = 2;
  optional int32 multiLevelsGradeType = 3; //  多关卡评级选择类型，默认选择第一关
}

message table_MatchCampGradeData {
  repeated MatchCampGradeConfig rows = 1;
}

message MatchCampGradeDimensionConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string descInfo = 2;
  optional BattleEventType battleEventType = 3;
  optional MatchCreateIndexOper conditionOper = 4;     //运算规则
  optional DropConditionRule conditions = 5;    //运算规则
}

message table_MatchCampGradeDimensionData {
  repeated MatchCampGradeDimensionConfig rows = 1;
}

message CustomRoomRobotSetting {
  optional int32 robotType = 1;         // 机器人类型
  optional int32 robotLevel = 2;        //机器人难度
  optional string robotLevelDes = 3;     //机器人难度描述
}

message CustomRoomRule {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string robotLevel = 2;       //机器人难度
  optional string robotLevelDes = 3;    //机器人难度描述
  optional int32 sideMemberNumber = 4;  //单阵营人数
  optional int32 minNumber = 5;         //最小开局人数
  optional string image = 6;            //模式大图
  optional string thumbImage = 7;       //模式缩略图
  optional string minImage = 8;         //房间邀请的小图
  repeated CustomRoomRobotSetting robotSettings = 9; // 机器人设置
  optional string QRCode_QQ = 10; //  自定义房间二维码
  optional string QRCode_WX = 11; //  自定义房间二维码
  optional google.protobuf.Timestamp beginTime = 12;  //开始时间
  optional google.protobuf.Timestamp endTime = 13;    //结束时间
  optional string sideMemberDetails = 14; // 阵营成员分布细节
  optional string sideNames = 15; // 阵营的名字
  optional int32 maxObserverNum = 16; // 最大观战人数
  optional int32 sortOrder = 17; // 排序的权重值
  optional bool bShowLifeRecord = 18; // 是否显示生涯记录
  optional int32 TeamMemberViewType = 19; // 位置类型
  optional bool bShowIdentity = 20;       // 显示身份
  optional bool bUsingPakVersion = 21; //自定义房间-关卡轮次-玩法隔离暂用字段
  repeated int32 banPlatformList = 22; // 需要屏蔽的平台
}

message table_TeamMemberViewTemplateData{// @noSvr
  repeated TeamMemberViewTemplate rows = 1;
}

message TeamMemberViewTemplate {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  repeated string itemName =2; //item名字
  repeated int32 row = 3;      //一排的数量
  optional string LayoutPadding = 4; // 列表padding参数
  optional string ItemPadding = 5; // 子项padding参数
}

message MatchPublicInfo{
  option (resKey) = "id";
  optional int32 id = 1;
  optional string showName = 2;             // 外显的名称
  optional bool allowPlayTogether = 3;      // 是否允许邀请/求加入
}

message MatchSvrDevAlloc {// @noCli
  option (resKey) = "id";
  optional int32 id = 1; //ID
  optional int32 roomInfoID = 2; // 房间信息ID
  repeated int32 instanceID = 3; // matchsvr实例ID, instanceid第四位 1.1.1.instanceID
  optional int32 disallowDefaultInstance = 4; // 是否不允许使用默认实例, (没有配置就是允许使用默认区)
}

//匹配服测试环境分配表
message table_MatchSvrDevAllocData {// @noCli
  repeated MatchSvrDevAlloc rows = 1;
}

message MatchSvrAlloc {// @noCli
  option (resKey) = "id";
  optional int32 id = 1; //ID
  optional int32 roomInfoID = 2; // 房间信息ID
  repeated int32 instanceID = 3; // matchsvr实例ID, instanceid第四位 1.1.1.instanceID
  optional int32 disallowDefaultInstance = 4; // 是否不允许使用默认实例, (没有配置就是允许使用默认区)
}

//匹配服正式环境分配表
message table_MatchSvrAllocData {// @noCli
  repeated MatchSvrAlloc rows = 1;
}

message MatchNewTagConfig {
  option (resKey) = "id";
  optional int32 playId = 1;
  optional google.protobuf.Timestamp startTime = 2;
  optional google.protobuf.Timestamp endTime = 3;
  optional bool bVisible = 4;
  optional string labelText = 5;
  optional string labelBackground = 6;
  optional int32 id = 7;
  optional string labelDetail = 8;
}

message table_MatchNewTagDateData {
  repeated MatchNewTagConfig rows = 1;
}

message MatchTypeConflictOutlookConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated int32 conflictOutlookGroupIds = 2;
}

message table_MatchTypeConflictOutlookData {
  repeated MatchTypeConflictOutlookConfig rows = 1;
}

message MatchBattleEventConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated BattleEventType events = 2;
}

message table_MatchBattleEventListData {
  repeated MatchBattleEventConfig rows = 1;
}

message MatchDynamicTeamPlayersItem {
  optional int32 dsaCPULowerLimit = 1; // DSACPU下限
  optional int32 dsaCPUUpperLimit = 2; // DSACPU上限
  optional int32 warmPlayers = 3; // 主玩法温暖局真人数(扩大100倍)
  optional int32 normalPlayers = 4; // 主玩法非温暖局真人数(扩大100倍)
  optional int32 secondaryWarmExpandFactor = 5; // 副玩法温暖局真人数扩大比例(扩大100倍)
  optional int32 secondaryNormalExpandFactor = 6; // 副玩法非温暖局真人数扩大比例(扩大100倍)
  optional int32 dsaCPUBoundryValue = 7; // DSACPU边界值配置(扩大100倍)
}

message MatchDynamicTeamPlayers {
  option (resKey) = "id";
  optional int32 id = 1; //ID
  repeated MatchDynamicTeamPlayersItem dynamicTeamPlayersItem = 2; // 动态AI配置
  optional google.protobuf.Timestamp startTime = 3; //开始时间
  optional google.protobuf.Timestamp endTime = 4; //开始时间
  repeated int32 filterRoomInfoIDS = 5; //过滤的房间配置ID
}

//维度上下限限制
message table_MatchDynamicTeamPlayersData {// @noCli
  repeated MatchDynamicTeamPlayers rows = 1;
}


message UgcTemplateConflictOutlookConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated int32 conflictOutlookGroupIds = 2;
}

message table_UgcTemplateConflictOutlookData {
  repeated UgcTemplateConflictOutlookConfig rows = 1;
}

message MatchDynamicMaxTimeoutItem {
  optional int32 scopeLeft = 1; // 段位分左开区间值
  optional int32 scopeRight = 2; // 段位分右开区间值
  optional int32 maxTimeoutSec = 3; // 最大超时时间
}

message MatchDynamicMaxTimeout {
  option (resKey) = "id";
  optional int32 id = 1; //ID
  repeated int32 roomInfoID = 2; //roomInfoID
  repeated MatchDynamicMaxTimeoutItem dynamicMaxTimeoutItem = 3; // 动态最大超时配置
}

//动态最大超时配置
message table_MatchDynamicMaxTimeoutData {// @noCli
  repeated MatchDynamicMaxTimeout rows = 1;
}

// ailab匹配对照组使用独立的配置

// P_匹配AiLab对照组配置.xlsx AiLab对照组动态AI配置
message table_MatchAiLabCGrpDynamicTeamRobotsData {  // @noCli
  repeated MatchDynamicTeamRobots rows = 1;
}

// P_匹配AiLab对照组配置.xlsx AiLab对照组维度上下限限制
message table_MatchAiLabCGrpExpandScopeLimitData {  // @noCli
  repeated MatchExpandScopeLimit rows = 1;
}

message RoomSideRole {
  option (resKey) = "id";
  optional int32 id = 1; //条目id, 逻辑里没实际作用
  optional int32 matchTypeId = 2; //房间玩法Id, 530、531等
  optional int32 sideId = 3; //阵营id
  optional string sideName = 4; //阵营名称
  optional int32 sideLimit = 5; //阵营人数上限
  optional int32 sideRandom = 6; //阵营随机概率
  optional string sideIcon = 7; //阵营icon
}

// W_玩法模式_阵营.xlsx 玩法阵营角色
message table_RoomSideRoleData {
  repeated RoomSideRole rows = 1;
}

message MatchSimulatorDynamicMaxTimeoutItem {// @noCli
  optional int32 scopeLeft = 1; // 段位分左开区间值
  optional int32 scopeRight = 2; // 段位分右开区间值
  optional int32 maxTimeoutSec = 3; // 最大超时时间
}

message MatchSimulatorDynamicMaxTimeout {// @noCli
  option (resKey) = "id";
  optional int32 id = 1; //ID
  repeated int32 roomInfoID = 2; //roomInfoID
  repeated MatchSimulatorDynamicMaxTimeoutItem dynamicMaxTimeoutItem = 3; // 动态最大超时配置
}

//模拟器动态最大超时配置
message table_MatchSimulatorDynamicMaxTimeoutData {// @noCli
  repeated MatchSimulatorDynamicMaxTimeout rows = 1;
}

message MatchFillBackItem {// @noCli
  option (resKey) = "id";
  optional int32 id = 1; //ID
  optional int32 maxTimeout = 2; //最大超时
  optional MatchFillBackTimeoutOper matchTimeoutOper = 3; // 超过最大超时时间处理(废弃)
  optional MatchingResultProcType matchingResultProcType = 4;

  optional MatchFillBackStrategy matchOper = 5; // 没有超时计算处理
  optional MatchFillBackStrategy timeoutMatchOper = 6; // 超过最大超时时间计算处理

  optional MatchmakingFuncType matchmakingFunc = 7; // 匹配撮合结果处理类型
}

//维度上下限限制
message table_MatchFillBackResData {// @noCli
  repeated MatchFillBackItem rows = 1;
}

// P_匹配_XX.xlsx 过滤器值型维度放宽规则
message MatchFilterTypeDimeExpandDetail {  // @noCli
  optional int32 closeLeftSec = 1;              // 超时时间左闭区间(秒)
  optional int32 openRightSec = 2;              // 超时时间右开区间(秒)
  repeated string excludeDimeValInStr = 3;      // 排除值
  optional MatchFindIndexOper oper = 4;         // 过滤方法
}

// P_匹配_XX.xlsx 过滤器值型维度放宽规则
message MatchFilterValTypeDimeExpandInfo {  // @noCli
  option (resKey) = "roomInfoId,dimeId,dimeValInStr";
  optional int32 roomInfoId = 1;                            // 房间配置Id
  optional int32 dimeId = 2;                                // 过滤维度ID
  optional string dimeValInStr = 3;                         // 维度值
  repeated MatchFilterTypeDimeExpandDetail detail = 4;
}

// P_匹配_XX.xlsx 过滤器值型维度放宽规则
message table_MatchFilterValTypeDimeExpandData {  // @noCli
  repeated MatchFilterValTypeDimeExpandInfo rows = 1;
}

// W_玩法模式_排序 玩法模式排序信息
message MatchModeSortInfo {
  option (resKey) = "id";
  optional int32 id = 1;                                // 自增id
  repeated int32 idGroup = 2;                           // 玩法id组
  optional string name = 3;                             // 玩法名
  optional int32 sort = 4;                              // 排序
  optional int32 modeGroupId = 5;                       // 赛季模式组ID
  optional int32 isWxgameShow = 6;                      // 赛季模式组ID
  optional int32 WxgameDefaultId = 7;                   // 赛季模式组ID
  optional int32 isWxgameSort = 8;                      // 赛季模式组ID
  optional string desc = 9;                             // 玩法简单描述
  repeated int32 platform = 10;                         // 生效平台
  optional int32 isAppgameSort = 11;                    // 在app下的排序
  optional bool showInNewView = 12;                     // @noSvr 在新界面显示 
  optional int32 recIndex = 13;                         // @noSvr 主推位置 
  optional string recImage = 14;                        // @noSvr 推荐位图片 
  optional int32 miniGameSort = 15;                     // @noSvr 小游戏排序
  optional bool isRec = 16;                             // @noSvr 是否会被推荐
}

// W_玩法模式_排序 玩法模式排序信息
message table_MatchModeSortInfo {
    repeated MatchModeSortInfo rows = 1;
}

//超时放宽
message TeamSizeMatchingRelax {
  optional int32 scopeLeftDivide = 1; // 放宽左开区间除以(参数扩大100倍)
  optional int32 scopeRightMultiply = 2; // 放宽右开区间乘以(参数扩大100倍)
  optional int32 addWaitSec = 3; // 第N波添加等待的时间
}

// P_匹配_特殊撮合规则.xlsx 队伍人数撮合特殊规则
message MatchTeamSizeMatchingRule { // @noCli
  option (resKey) = "id";
  optional int32 id = 1;          // 自增id
  repeated TeamSizeMatchingRelax teamSizeMatchingRelax = 2; // 超时放宽配置
  optional int32 priorityOppositeSex = 3; // 优先撮合异性
  optional int32 priorityOppositeSexDuration = 4; // 优先撮合异性持续时间
  optional int32 commonAddWaitSec = 5; // 通用维度放宽等待时间
}

// W_玩法模式_排序 玩法模式排序信息
message table_MatchTeamSizeMatchingRuleData {   // @noCli
  repeated MatchTeamSizeMatchingRule rows = 1;
}

// P_匹配.xlsx 特殊局虚拟匹配房间
message MatchSpecialRoundRobotSideDiffVirtualRoomInfo {
  option (resKey) = "matchTypeId,specialRoundType";
  optional int32 matchTypeId = 1;                             // 玩法模式Id
  optional MatchSpecialRoundType specialRoundType = 2;        // 特殊房间类型
  optional string specialRoundTypeArgs = 3;                   // 特殊房间参数
  repeated int32 virtualRoomInfoIds = 4;                      // 虚拟房间配置ID(读取AI数量用于)
  optional int32 overrideMatchRuleDataId = 5;                 // 覆盖规则ID
  optional int32 isOverrideRealRoomInfoId = 6;                // 是否直接覆盖匹配池房间
}

// P_匹配.xlsx 特殊局虚拟匹配房间
message table_MatchSpecialRoundRobotSideDiffVirtualRoomInfoData {
	repeated MatchSpecialRoundRobotSideDiffVirtualRoomInfo rows = 1;
}

message MatchRouteWhiteList  // @noCli
{
  option (resKey) = "id";
  optional int64 id = 1;  //uid
  optional string name = 2; //名字
  optional int32 instId = 3; //matchSvr实例Id
}

message table_MatchRouteWhiteListData // @noCli
{
  repeated MatchRouteWhiteList rows = 1;
}


message MatchUnlockConditionConfig {
  option (resKey) = "matchTypeId";
  optional int32 matchTypeId = 1;
  repeated MatchTypeUnlockConditionConfig unlockConfig = 2;
}

message MatchTypeUnlockConditionConfig {
  optional MatchUnlockType unlockType = 2;
  optional ResConditionGroup conditionGroup = 3; // @noCli
  optional string desc = 4; // @noSvr
}

message table_MatchUnlockConditionData {
  repeated MatchUnlockConditionConfig rows = 1;
}

message MatchSideLimitItem {
  optional int32 sideID = 1;      //阵营id
  optional int32 cnt = 2;       //阵营人数上限
}

message MatchSideLimitConfig {
  option (resKey) = "matchTypeId";
  optional int32 matchTypeId = 1;   //玩法模式id
  repeated MatchSideLimitItem item = 2;
}

message table_MatchSideLimitConfigData {
  repeated MatchSideLimitConfig rows = 1;
}

// 玩法详情页分组信息
message MatchTypeDetailPageGroupInfo {
  option (resKey) = "id";
  optional int32 id = 1;                // 玩法id
  optional int32 groupId = 2;           // 分组id
  optional int32 showAtDetailPage = 3;  // 是否在详情页展示，0不展示，1展示
  optional string buttonDesc = 4;       // 按钮描述
  optional bool isCloseButton = 5;      // 是否关闭，1关闭，0开启
}

message table_MatchTypeDetailPageGroupInfoData {
  repeated MatchTypeDetailPageGroupInfo rows = 1;
}
message ReadyGoTipsConfig { // @noSvr
  optional string text = 1;
  optional string title = 2;
}

message ReadyGoTipsItem { // @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  optional ReadyGoTipsConfig racing = 2;
  optional ReadyGoTipsConfig survival = 3;
  optional ReadyGoTipsConfig score = 4;
}

message table_ReadyGoTipsConfig { // @noSvr
  repeated  ReadyGoTipsItem rows = 1;
}

message RecommendMatchItem {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 playId = 2;
  optional int32 sort = 3;
}

message table_RecommendMatchInTeam {// @noSvr
  repeated RecommendMatchItem rows = 1;
}

message PinBlockInfo {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string pinStr = 2;
}

message table_PinBlockInfoData {
  repeated PinBlockInfo rows = 1;
}


message MatchTypeOutlookReplaceConfig {
  option (resKey) = "itemId";
  optional int32 itemId = 1;
  repeated int32 matchTypeId = 2;
  optional int32 replaceItemId = 3;
}

message table_MatchTypeOutlookReplaceData {
  repeated MatchTypeOutlookReplaceConfig rows = 1;
}

message TeamShowConfig {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 bCanInviteCount = 2;  // 允许邀请的玩家数量
}

message table_TeamShowData {// @noSvr
  repeated TeamShowConfig rows = 1;
}

message TeamModePlaySwitchRule {
  option (resKey) = "id";
  optional int32 id = 1; // 序号
  repeated int32 matchTypeIdList = 2; // 玩法id列表
}

message table_TeamModePlaySwitchRuleData {
  repeated TeamModePlaySwitchRule rows = 1;
}

message TeamShowVehicleAnimData {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;                  // 道具id
  optional string playerEnterAnim = 2;    // 玩家的入场动画
  optional string vehicleEnterAnim = 3;   // 载具的入场动画
  optional string playerExitAnim = 4;     // 玩家的出场动画
  optional string vehicleExitAnim = 5;    // 载具的出场动画
}

message table_TeamShowVehicleAnimData {// @noSvr
  repeated TeamShowVehicleAnimData rows = 1;
}

message InLevelTogetherConstConfigData {
  option (resKey) = "key";
  optional string key = 1;
  optional int32 value = 2;
}

message table_InLevelTogetherConstConfigData {
  repeated InLevelTogetherConstConfigData rows = 1;
}

message InLevelTogetherTextConfigData {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string textTitle = 2;
  optional string textContent = 3;
}

message table_InLevelTogetherTextConfigData {
  repeated InLevelTogetherTextConfigData rows = 1;
}

message InLevelPerformExcellentDimension {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string descInfo = 2;
  optional string BattleEventType = 3;
  optional PerformCompareOpr compare = 4;
}

message table_InLevelPerformExcellentDimensionData {
  repeated InLevelPerformExcellentDimension rows = 1;
}

message InLevelPerformExcellentConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string descInfo = 2;
  optional ResPerformExcellentCondition performCondition = 3;
}

message table_InLevelPerformExcellentConfigData {
  repeated InLevelPerformExcellentConfig rows = 1;
}

message InLevelCoMatchBattleTogetherDimension {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string descInfo = 2;
  optional string BattleEventType = 3;
  optional PerformCompareOpr compare = 4;
}

message table_InLevelCoMatchBattleTogetherDimensionData {
  repeated InLevelCoMatchBattleTogetherDimension rows = 1;
}

message InLevelCoMatchBattleTogetherConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string descInfo = 2;
  optional ResPerformExcellentCondition performCondition = 3;
}

message table_InLevelCoMatchBattleTogetherConfigData {
  repeated InLevelCoMatchBattleTogetherConfig rows = 1;
}

message MatchTypePakConfig {//@noSvr
  option (resKey) = "id";
  optional int32 id = 1; //@noSvr 玩法ID
  optional int32 pakGroup = 2;  //@noSvr 包组
}

message table_MatchTypePakConfig {//@noSvr
  repeated MatchTypePakConfig rows = 1;
}


message MatchDynamicMinCntItem { // @noCli
  optional int32 startSec = 1; // 开始时间戳
  optional int32 minCnt = 2; // 最小真人数
}

message MatchDynamicMinCntCfg { // @noCli
  option (resKey) = "id";
  optional int32 id = 1; //ID
  repeated MatchDynamicMinCntItem dynamicMinCntItem = 2; // 动态AI配置
}

//维度上下限限制
message table_MatchDynamicMinCntCfgData { // @noCli
  repeated MatchDynamicMinCntCfg rows = 1;
}

message MatchNewImageData {//@noSvr
  option (resKey) = "id";
  optional int32 id = 1; //玩法ID
  optional string thumbImage = 2; //模式缩略图
  optional string image = 3; //模式大图
  optional google.protobuf.Timestamp startTime = 4; //开始时间
  optional google.protobuf.Timestamp endTime = 5; //结束时间
}

message table_MatchNewImageData {//@noSvr
  repeated MatchNewImageData rows = 1;
}

message TeamDegreeCond {
  optional int32 qualifyingIntLow = 1;      // 玩家段位分左区间
  optional int32 qualifyingIntHigh = 2;     // 玩家段位分右区间
  optional int32 multiMatchIntLow = 3;      // 多人排位段位分左区间
  optional int32 multiMatchIntHigh = 4;     // 多人排位段位分右区间
  optional string multiMatchDegreeLow = 5;  // 多人排位段位下限
  optional string multiMatchDegreeHigh = 6; // 多人排位段位上限
  optional int32 fullMatchIntLow = 7;       // 五人排位段位分左区间
  optional int32 fullMatchIntHigh = 8;      // 五人排位段位分右区间
  optional string fullMatchDegreeLow = 9;   // 五人排位段位下限
  optional string fullMatchDegreeHigh = 10; // 五人排位段位上限
}

message MatchRuleTeamDegreeCondData {
  option (resKey) = "matchType";
  optional int32 matchType = 1;
  repeated TeamDegreeCond teamDegreeCond = 2; // 队伍段位限制条件
}

message table_MatchRuleTeamDegreeCondData {
  repeated MatchRuleTeamDegreeCondData rows = 1;
}

message RecruitModeInfoData {
  option (resKey) = "id";
  optional int32 id = 1;        // 模式ID
  optional int32 sort = 2;      // 排序
  optional bool show = 3;       // 是否在筛选下拉框展示，1展示
  optional string name = 4;     // 名称
}

message table_RecruitModeInfoData {//@noSvr
  repeated RecruitModeInfoData rows = 1;
}

message  LobbyLeftTopTipRecommendData {//@noSvr
  option (resKey) = "id";
  optional int32 id = 1;        // 自增ID
  optional int32 matchId = 2;       // 推荐玩法Id
  optional bool isGuaranteeSlot = 3;     // 是否保底配置
  optional google.protobuf.Timestamp startTime = 4; //开始时间
  optional google.protobuf.Timestamp endTime = 5; //结束时间
}

message table_LobbyLeftTopTipRecommendData {//@noSvr
  repeated LobbyLeftTopTipRecommendData rows = 1;
}

message MatchInfoPassThroughData {//@noSvr
  option (resKey) = "id";
  optional int32 id = 1;                              // 自增id
  optional int32 matchId = 2;                         // 玩法id
  optional int32 modelInfoType = 3;                   // 信息透传枚举 1.通用,2.赛事
  optional string desc = 4;                           // 透传描述
  optional google.protobuf.Timestamp startTime = 5;   // 开始时间
  optional google.protobuf.Timestamp endTime = 6;     // 结束时间
  optional string icon = 7;                           // 透传icon
  optional int32 bgType = 8;                          // 底图颜色
  optional int32 sortId = 9;                          // 透传优先级
  optional int32 jumpId = 10;                         // 跳转id
  optional int32 redDotId = 11;                       // 红点id
  optional bool bVisible = 12;                        // 是否显示
  optional int32 bindActivityId = 13;                 // 绑定的活动id
}

message table_MatchInfoPassThroughData{//@noSvr
  repeated MatchInfoPassThroughData rows = 1;
}

message MatchTypeRootConfig {
  option (resKey) = "gameTypeId";
  optional int32 gameTypeId = 1;  // 自增id
  optional string name = 2; //名称
  repeated int32 matchTypeIds = 3;  //玩法类别分组id
  optional bool isAll = 4; // 是否服务器玩法类型全量集合
  optional GameRootType gameRootType = 5;// 玩法类型枚举
}

message table_MatchTypeRootData {
  repeated MatchTypeRootConfig rows = 1;
}

enum TeamRecruitModeType {
  TRMT_Match = 1 [(name) = "经典娱乐"];
  TRMT_AllUGCRecommend = 2 [(name) = "星世界推荐"];
  TRMT_AllUGCNotRecommend = 3 [(name) = "星世界地图"];
}

message TeamRecruitModeItem {
  option (resKey) = "id";
  optional int32 id = 1;                            // 模式类型
  optional string des = 2;                          // 名称
  optional bool bShowInPublic = 3;                  // 是否在发布页面显示
  optional bool bHasNoLimit = 4;                    // 是否有不限选项
}

message table_TeamRecruitMode {
  repeated TeamRecruitModeItem rows = 1;
}

enum TeamRecruitMatchCatoryType {
  TRMCT_TTJJS = 1 [(name) = "天天晋级赛"];
  TRMCT_XG = 2 [(name) = "峡谷"];
  TRMCT_SSLR = 3 [(name) = "谁是狼人"];
  TRMCT_DWBZW = 4 [(name) = "大王别抓我"];
  TRMCT_DMM = 5 [(name) = "躲猫猫"];
  TRMCT_WQDS = 6 [(name) = "武器大师"];
  TRMCT_JSFC = 7 [(name) = "极速飞车"];
  TRMCT_WDXD = 8 [(name) = "卧底行动"];
  TRMCT_SDS = 9 [(name) = "闪电赛"];
  TRMCT_SRBXS = 10 [(name) = "兽人必须死"];
  TRMCT_WJRG = 11 [(name) = "无尽肉鸽"];
  TRMCT_TTPD = 12 [(name) = "逃脱派对"];
  TRMCT_TWMHD = 13 [(name) = "突围梦幻岛"];
  TRMCT_DBQX = 14 [(name) = "夺宝奇星"];
  TRMCT_JXJS = 15 [(name) = "极限竞速"];
  TRMCT_CFJJ = 16 [(name) = "冲锋竞技"];
  TRMCT_PPDZ = 17 [(name) = "泡泡大战"];
}

message TeamRecruitMatchCatoryItem {
  option (resKey) = "id";
  optional int32 id = 1;                                // 分类类型
  optional string des = 2;                              // 名称
}

message table_TeamRecruitMatchCatory {
  repeated TeamRecruitMatchCatoryItem rows = 1;
}

enum RecruitViewType {
  RVT_AllRecruit = 0 [(name) = "所有招募"];
  RVT_TeamRecruit = 1 [(name) = "组队招募"];
  RVT_RoomRecruit = 2 [(name) = "房间招募"];
}

message TeamRecruitMatchItem {
  option (resKey) = "id";
  optional int32 id = 1;                                // 自增Id
  optional int32 matchId = 2;                           // 玩法Id
  optional int32 modeType = 3;                          // 模式类型
  optional int32 catoryType = 4;                        // 分类类型
  optional string optionDes = 5;                        // 选项名称
  optional string outDes = 6;                           // 展示名称
  optional bool isDefault = 7;                          // 是否是默认模式
  optional int32 recruitType = 8;                       // 招募类型
}

message table_TeamRecruitMatch {
  repeated TeamRecruitMatchItem rows = 1;
}

enum MatchPakType {
  MPT_BasePlay = 1 [(name) = "体验玩法包"];
  MPT_CorePlay = 2 [(name) = "核心玩法包"];
  MPT_OtherPlay = 3 [(name) = "其他玩法包"];
  MPT_System = 4 [(name) = "非玩法功能包"];
  MPT_RankNewer = 5 [(name) = "段位包-新手"];
  MPT_RankBronze = 6 [(name) = "段位包-青铜"];
  MPT_RankGold = 7 [(name) = "段位包-黄金"];
  MPT_RankTotal = 8 [(name) = "段位包-总包"];
}

message MatchPakDetailItem {
  option (resKey) = "id";
  optional int32 id = 1;                                // 自增Id
  optional int32 type = 2;                              // 类型
  repeated int32 matchIds = 3;                          // 匹配的玩法列表
  repeated int32 pakGroupIds = 4;                       // 包组列表
  repeated int32 allowMatchTypes = 5;                   // 允许一起匹配的类型
  optional int32 matchDimenValue = 6;                   // 匹配维度值 
  optional int32 priority = 7;                          // 优先级 
  optional string minVersion = 8;                       // 最小版本号
  optional string maxVersion = 9;                       // 最大版本号
  optional int32 qualifyIntLimit = 10;                  // 段位要求
  optional int32 minQualifyIntLimit = 11;               // 最低段位要求
}

message table_MatchPakDetail {
  repeated MatchPakDetailItem rows = 1;
}

enum MatchPakRuleType {
  MPRT_Default = 1 [(name) = "默认的大小包逻辑"];
  MPRT_Rank = 2 [(name) = "以天天晋级赛为主的段位包逻辑"];
}

message MatchPakTypeItem {
  option (resKey) = "id";
  optional int32 id = 1;                                // 自增Id
  optional string des = 2;                              // 描述
  optional MatchPakRuleType ruleType = 3;               // 规则类型
  optional string ruleScript = 4;                       // 客户端实现规则脚本
}

message table_MatchPakType {
  repeated MatchPakTypeItem rows = 1;
}

message MatchPakDetailKVItem {
  option (resKey) = "key";
  optional string key = 1;
  optional string value = 2;
}

message table_MatchPakDetailKVConf {// @noSvr
  repeated MatchPakDetailKVItem rows = 1;
}
//generated by tool
package com.tencent.wea.rpc.idiphandler;
import com.tencent.nk.idip.IdipMsgHandler;
import com.tencent.wea.protocol.idip.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class AqDoPlayerItaBagResetReqHandler implements IdipMsgHandler {
    public static final Logger LOGGER = LogManager.getLogger(AqDoPlayerItaBagResetReqHandler.class);

    @Override
    public IdipResponse.Builder handle(IdipRequest.Builder request) {
        AqDoPlayerItaBagResetReq.Builder req =  request.getAqDoPlayerItaBagResetReqBuilder();
        AqDoPlayerItaBagResetRsp.Builder res = AqDoPlayerItaBagResetRsp.newBuilder();
        IdipResponse.Builder idipResponse = IdipResponse.newBuilder().setAqDoPlayerItaBagResetRsp(res);
        return idipResponse;
    }
}
package com.tencent.wea.cshandler.handler;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.cshandler.AbstractFsClientRequestHandler;
import com.tencent.wea.farmservice.store.Farm;
import com.tencent.wea.protocol.CsFarm;
import com.tencent.wea.protocol.CsHouse;
import com.tencent.wea.protocol.common.TlogRequiredFields;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * MsgHandler_in_farmsvr.java.template
 */

public class FarmsvrHouseItemInteractMsgHandler extends AbstractFsClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(FarmsvrHouseItemInteractMsgHandler.class);

    @Override
    public Message.Builder handle(Farm farm, Message request, long playerUid, TlogRequiredFields tlogRequiredFields)  {
        //CsHouse.HouseItemInteract_C2S_Msg reqMsg = (CsHouse.HouseItemInteract_C2S_Msg)request;
        CsHouse.HouseItemInteract_S2C_Msg.Builder rspMsg = CsHouse.HouseItemInteract_S2C_Msg.newBuilder();

        NKErrorCode.UnknownError.throwError("HouseItemInteractMsgHandler not implemented");
        return rspMsg;
    }
}
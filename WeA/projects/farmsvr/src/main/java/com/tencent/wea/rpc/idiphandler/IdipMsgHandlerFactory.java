//generated by tools, do not modify any thing ok?
package com.tencent.wea.rpc.idiphandler;

import com.tencent.nk.idip.IdipMsgHandler;
import com.tencent.wea.protocol.common.NetCmdId;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class IdipMsgHandlerFactory {
    private static final Logger LOGGER = LogManager.getLogger(IdipMsgHandlerFactory.class);
    private static final Int2ObjectMap<IdipMsgHandler> handlerMap = new Int2ObjectOpenHashMap<>();

    static {
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_CLEAR_FARM_REQ_VALUE, new AqDoClearFarmReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_BAN_CHANGE_PET_NAME_REQ_VALUE, new AqDoFarmBanChangePetNameReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_BLOCK_GIFT_MSG_REQ_VALUE, new AqDoFarmBlockGiftMsgReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_COOK_PUT_DOWN_REQ_VALUE, new AqDoFarmCookPutDownReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_COOK_SCREEN_BAN_REQ_VALUE, new AqDoFarmCookScreenBanReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_COOK_SCREEN_CLEAR_REQ_VALUE, new AqDoFarmCookScreenClearReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_COOK_SCREEN_SET_FREE_REQ_VALUE, new AqDoFarmCookScreenSetFreeReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_FORBID_GIFT_SENDING_REQ_VALUE, new AqDoFarmForbidGiftSendingReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_HOUSE_PICK_ALL_FURNITURE_REQ_VALUE, new AqDoFarmHousePickAllFurnitureReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_HOUSE_PUT_DOWN_REQ_VALUE, new AqDoFarmHousePutDownReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_LIU_YAN_MESSAGE_DELETE_REQ_VALUE, new AqDoFarmLiuYanMessageDeleteReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_PARTY_BAN_REQ_VALUE, new AqDoFarmPartyBanReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_PARTY_CLEAR_REQ_VALUE, new AqDoFarmPartyClearReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_PARTY_SET_FREE_REQ_VALUE, new AqDoFarmPartySetFreeReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_PUT_DOWN_REQ_VALUE, new AqDoFarmPutDownReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_RESET_PET_NAME_REQ_VALUE, new AqDoFarmResetPetNameReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_WELCOME_BAN_REQ_VALUE, new AqDoFarmWelcomeBanReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_WELCOME_CLEAR_REQ_VALUE, new AqDoFarmWelcomeClearReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_AQ_DO_FARM_WELCOME_SET_FREE_REQ_VALUE, new AqDoFarmWelcomeSetFreeReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_ADD_CROP_EXP_REQ_VALUE, new DoFarmAddCropExpReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_ADD_FISH_EXP_REQ_VALUE, new DoFarmAddFishExpReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_ADD_ITEM_REQ_VALUE, new DoFarmAddItemReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_ADJUST_BUILDING_LEVEL_REQ_VALUE, new DoFarmAdjustBuildingLevelReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_ADJUST_MAIN_EXP_REQ_VALUE, new DoFarmAdjustMainExpReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_COOK_ADD_EMPLOYEE_REQ_VALUE, new DoFarmCookAddEmployeeReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_COOK_UPDATE_EXTEND_INFO_REQ_VALUE, new DoFarmCookUpdateExtendInfoReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_DEL_BUILDING_SKIN_REQ_VALUE, new DoFarmDelBuildingSkinReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_DEL_ITEM_REQ_VALUE, new DoFarmDelItemReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_LAYOUT_OP_REQ_VALUE, new DoFarmLayoutOpReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_MAGIC_ADD_OR_DEL_REQ_VALUE, new DoFarmMagicAddOrDelReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_MAGIC_ADJUST_MP_REQ_VALUE, new DoFarmMagicAdjustMpReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_DO_FARM_MODIFY_VILLAGER_REQ_VALUE, new DoFarmModifyVillagerReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_QUERY_FARM_COOK_QUERY_EXTEND_INFO_REQ_VALUE, new QueryFarmCookQueryExtendInfoReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_QUERY_FARM_GET_BASICINFO_REQ_VALUE, new QueryFarmGetBasicinfoReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_QUERY_FARM_GET_ITEM_LIST_REQ_VALUE, new QueryFarmGetItemListReqHandler());
        handlerMap.put(NetCmdId.NCI_IDIP_QUERY_FARM_LIU_YAN_MESSAGE_REQ_VALUE, new QueryFarmLiuYanMessageReqHandler());
    }

    public static IdipMsgHandler getHandler(int type) {
        IdipMsgHandler handler = handlerMap.getOrDefault(type, null);
        if (handler == null) {
            throw new IllegalArgumentException(String.format("no such handler as type %d", type));
        } else {
            return handler;
        }
    }
}
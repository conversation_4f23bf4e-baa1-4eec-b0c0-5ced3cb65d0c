package com.tencent.wea.cshandler.handler;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.cshandler.AbstractFsClientRequestHandler;
import com.tencent.wea.farmservice.store.Farm;
import com.tencent.wea.protocol.CsFarm;
import com.tencent.wea.protocol.CsHouse;
import com.tencent.wea.protocol.common.TlogRequiredFields;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * MsgHandler_in_farmsvr.java.template
 */

public class FarmsvrCookHireFriendMsgHandler extends AbstractFsClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(FarmsvrCookHireFriendMsgHandler.class);

    @Override
    public Message.Builder handle(Farm farm, Message request, long playerUid, TlogRequiredFields tlogRequiredFields)  {
        //CsCook.CookHireFriend_C2S_Msg reqMsg = (CsCook.CookHireFriend_C2S_Msg)request;
        CsCook.CookHireFriend_S2C_Msg.Builder rspMsg = CsCook.CookHireFriend_S2C_Msg.newBuilder();

        NKErrorCode.UnknownError.throwError("CookHireFriendMsgHandler not implemented");
        return rspMsg;
    }
}
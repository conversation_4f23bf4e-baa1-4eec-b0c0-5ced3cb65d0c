// generated by tools, DO NOT MODIFY ANYTHING
package com.tencent.wea.cshandler;

import com.tencent.wea.cshandler.handler.*;
import com.tencent.wea.protocol.MsgTypes;

import java.util.HashMap;
import java.util.Map;

/**
 * maintain the msg handlers
 */
public class FarmsvrCsMsgHandlerFactory {
    public static final Map<Integer, AbstractFsClientRequestHandler> MSG_HANDLERS = new HashMap<>();

    static {
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMOP_C2S_MSG, new FarmsvrFarmOpMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMBADGUYSLIST_C2S_MSG, new FarmsvrFarmBadGuysListMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMEVICTBADGUY_C2S_MSG, new FarmsvrFarmEvictBadGuyMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMSENDLIUYANMESSAGE_C2S_MSG, new FarmsvrFarmSendLiuYanMessageMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMDELETELIUYANMESSAGE_C2S_MSG, new FarmsvrFarmDeleteLiuYanMessageMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMGETLIUYANMESSAGE_C2S_MSG, new FarmsvrFarmGetLiuYanMessageMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMSETWELCOMEINFO_C2S_MSG, new FarmsvrFarmSetWelcomeInfoMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMTAGFRIEND_C2S_MSG, new FarmsvrFarmTagFriendMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMSENDGIFT_C2S_MSG, new FarmsvrFarmSendGiftMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMPICKGIFT_C2S_MSG, new FarmsvrFarmPickGiftMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMBUILDINGSETSKIN_C2S_MSG, new FarmsvrFarmBuildingSetSkinMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_HOUSEDECORATE_C2S_MSG, new FarmsvrHouseDecorateMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_HOUSEFORWARDTODS_C2S_MSG, new FarmsvrHouseForwardToDSMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_HOUSEFURNITUREBUY_C2S_MSG, new FarmsvrHouseFurnitureBuyMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMPETEVICT_C2S_MSG, new FarmsvrFarmPetEvictMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMSCENEDROPPICK_C2S_MSG, new FarmsvrFarmSceneDropPickMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMPETFEED_C2S_MSG, new FarmsvrFarmPetFeedMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_HOUSEITEMINTERACT_C2S_MSG, new FarmsvrHouseItemInteractMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMLIGHTNINGSTRIKEREPORT_C2S_MSG, new FarmsvrFarmLightningStrikeReportMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMPETCHANGENAME_C2S_MSG, new FarmsvrFarmPetChangeNameMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMEVENTGETREWARD_C2S_MSG, new FarmsvrFarmEventGetRewardMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMEVENTSAVEDATA_C2S_MSG, new FarmsvrFarmEventSaveDataMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMSELECTPOOL_C2S_MSG, new FarmsvrFarmSelectPoolMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_COOKHIREFRIEND_C2S_MSG, new FarmsvrCookHireFriendMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_COOKSENDCOMMENT_C2S_MSG, new FarmsvrCookSendCommentMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_COOKVISITANTSTEAL_C2S_MSG, new FarmsvrCookVisitantStealMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMENTERHOTSPRING_C2S_MSG, new FarmsvrFarmEnterHotSpringMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMGETHOTSPRINGBUFF_C2S_MSG, new FarmsvrFarmGetHotSpringBuffMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMCHANGESIGNATURE_C2S_MSG, new FarmsvrFarmChangeSignatureMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMPRAYTOGODFIGURE_C2S_MSG, new FarmsvrFarmPrayToGodFigureMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_COOKSETFLOATINGSCREENCONTENT_C2S_MSG, new FarmsvrCookSetFloatingScreenContentMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_HOUSESAVELAYOUT_C2S_MSG, new FarmsvrHouseSaveLayoutMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMPARTYPUBLISH_C2S_MSG, new FarmsvrFarmPartyPublishMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMPARTYCLOSE_C2S_MSG, new FarmsvrFarmPartyCloseMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMGETPARTYLIST_C2S_MSG, new FarmsvrFarmGetPartyListMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_COOKCALCULATECOOKOPENINCOME_C2S_MSG, new FarmsvrCookCalculateCookOpenIncomeMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMTAGREDFOX_C2S_MSG, new FarmsvrFarmTagRedFoxMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_FARMGMCOMMAND_C2S_MSG, new FarmsvrFarmGMCommandMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_HOUSEBUYSAMPLEROOM_C2S_MSG, new FarmsvrHouseBuySampleRoomMsgHandler());
    }

    /**
     *  get msg handler
     * @param type
     * @return
     */
    public static AbstractFsClientRequestHandler getMsgHandler(int type) {
           return MSG_HANDLERS.get(type);
    }
}

package com.tencent.wea.cshandler.handler;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.cshandler.AbstractFsClientRequestHandler;
import com.tencent.wea.farmservice.store.Farm;
import com.tencent.wea.protocol.CsFarm;
import com.tencent.wea.protocol.CsHouse;
import com.tencent.wea.protocol.common.TlogRequiredFields;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * MsgHandler_in_farmsvr.java.template
 */

public class FarmsvrHouseBuySampleRoomMsgHandler extends AbstractFsClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(FarmsvrHouseBuySampleRoomMsgHandler.class);

    @Override
    public Message.Builder handle(Farm farm, Message request, long playerUid, TlogRequiredFields tlogRequiredFields)  {
        //CsHouse.HouseBuySampleRoom_C2S_Msg reqMsg = (CsHouse.HouseBuySampleRoom_C2S_Msg)request;
        CsHouse.HouseBuySampleRoom_S2C_Msg.Builder rspMsg = CsHouse.HouseBuySampleRoom_S2C_Msg.newBuilder();

        NKErrorCode.UnknownError.throwError("HouseBuySampleRoomMsgHandler not implemented");
        return rspMsg;
    }
}
package com.tencent.wea.cshandler.handler;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.cshandler.AbstractFsClientRequestHandler;
import com.tencent.wea.farmservice.store.Farm;
import com.tencent.wea.protocol.CsFarm;
import com.tencent.wea.protocol.CsHouse;
import com.tencent.wea.protocol.common.TlogRequiredFields;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * MsgHandler_in_farmsvr.java.template
 */

public class FarmsvrFarmGetPartyListMsgHandler extends AbstractFsClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(FarmsvrFarmGetPartyListMsgHandler.class);

    @Override
    public Message.Builder handle(Farm farm, Message request, long playerUid, TlogRequiredFields tlogRequiredFields)  {
        //CsFarm.FarmGetPartyList_C2S_Msg reqMsg = (CsFarm.FarmGetPartyList_C2S_Msg)request;
        CsFarm.FarmGetPartyList_S2C_Msg.Builder rspMsg = CsFarm.FarmGetPartyList_S2C_Msg.newBuilder();

        NKErrorCode.UnknownError.throwError("FarmGetPartyListMsgHandler not implemented");
        return rspMsg;
    }
}
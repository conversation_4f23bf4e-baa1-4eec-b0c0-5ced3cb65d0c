//generated by tools, do not modify any thing ok?
package com.tencent.wea.rpc.idiphandler;

import com.tencent.nk.idip.IdipMsgHandler;
import com.tencent.wea.protocol.common.NetCmdId;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class IdipMsgHandlerFactory {
    private static final Logger LOGGER = LogManager.getLogger(IdipMsgHandlerFactory.class);
    private static final Int2ObjectMap<IdipMsgHandler> handlerMap = new Int2ObjectOpenHashMap<>();

    static {
        handlerMap.put(NetCmdId.NCI_IDIP_UGC_DATA_STORE_SVR_TEST_REQ_VALUE, new UgcDataStoreSvrTestReqHandler());
    }

    public static IdipMsgHandler getHandler(int type) {
        IdipMsgHandler handler = handlerMap.getOrDefault(type, null);
        if (handler == null) {
            throw new IllegalArgumentException(String.format("no such handler as type %d", type));
        } else {
            return handler;
        }
    }
}
// generated by tools, DO NOT MODIFY ANYTHING
package com.tencent.wea.cshandler;

import com.tencent.wea.cshandler.handler.*;
import com.tencent.wea.protocol.MsgTypes;

import java.util.HashMap;
import java.util.Map;

/**
 * maintain the msg handlers
 */
public class UgcCsMsgHandlerFactory {
    public static final Map<Integer, AbstractUsClientRequestHandler> MSG_HANDLERS = new HashMap<>();

    static {
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCFORWARDTEST_C2S_MSG, new Ugc_UgcForwardTestMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCAPPRECIATEGETMAPS_C2S_MSG, new Ugc_UgcAppreciateGetMapsMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCAPPRECIATEMAP_C2S_MSG, new Ugc_UgcAppreciateMapMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCMATCHLOBBYDETAILGETONE_C2S_MSG, new Ugc_UgcMatchLobbyDetailGetOneMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCAPPLYCOVERURL_C2S_MSG, new Ugc_UgcApplyCoverUrlMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCMAPEXTRACONFIGEDIT_C2S_MSG, new Ugc_UgcMapExtraConfigEditMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCMAPEXTRACONFIGGET_C2S_MSG, new Ugc_UgcMapExtraConfigGetMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCGETCREATORBADGE_C2S_MSG, new Ugc_UgcGetCreatorBadgeMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCGETCREATORHOMEPAGE_C2S_MSG, new Ugc_UgcGetCreatorHomePageMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCMATCHLOBBYDETAILEXMULTI_C2S_MSG, new Ugc_UgcMatchLobbyDetailExMultiMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCMATCHLOBBYDETAILEXCHANGE_C2S_MSG, new Ugc_UgcMatchLobbyDetailExChangeMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCMATCHLOBBYSUMMERY_C2S_MSG, new Ugc_UgcMatchLobbySummeryMsgHandler());
    
           MSG_HANDLERS.put(MsgTypes.MSG_TYPE_UGCMATCHLOBBYSUMMERYSPECIFIED_C2S_MSG, new Ugc_UgcMatchLobbySummerySpecifiedMsgHandler());
    }

    /**
     *  get msg handler
     * @param type
     * @return
     */
    public static AbstractUsClientRequestHandler getMsgHandler(int type) {
           return MSG_HANDLERS.get(type);
    }
}

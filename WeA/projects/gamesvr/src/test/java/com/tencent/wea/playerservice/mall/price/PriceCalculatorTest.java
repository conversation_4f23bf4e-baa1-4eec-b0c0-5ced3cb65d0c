package com.tencent.wea.playerservice.mall.price;

import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 价格计算器单元测试
 */
public class PriceCalculatorTest {
    
    @Mock
    private Player mockPlayer;
    
    @Mock
    private MallCommodity mockCommodity;
    
    private PriceCalculator priceCalculator;
    private PriceModifierRegistry registry;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 重置注册表
        registry = PriceModifierRegistry.getInstance();
        registry.clear();
        
        priceCalculator = new PriceCalculator();
        
        // 设置模拟商品配置
        when(mockCommodity.getPrice()).thenReturn(1000);
        when(mockCommodity.getDiscountPrice()).thenReturn(0);
        when(mockCommodity.getCommodityId()).thenReturn(12345);
        when(mockCommodity.getCoinType()).thenReturn(1);
        
        // 设置模拟玩家
        when(mockPlayer.getUid()).thenReturn(123456789L);
    }
    
    @Test
    void testBasicPriceCalculation() {
        // 测试基础价格计算（无修改器）
        long finalPrice = priceCalculator.calculateFinalPrice(
            mockPlayer, mockCommodity, 1, false, "test-bill");
        
        assertEquals(1000, finalPrice, "基础价格应该等于商品配置价格");
    }
    
    @Test
    void testDiscountPriceCalculation() {
        // 测试折扣价格计算
        when(mockCommodity.getDiscountPrice()).thenReturn(800);
        
        long finalPrice = priceCalculator.calculateFinalPrice(
            mockPlayer, mockCommodity, 1, false, "test-bill");
        
        assertEquals(800, finalPrice, "应该使用折扣价格");
    }
    
    @Test
    void testSingleModifierApplication() {
        // 注册一个测试修改器
        TestPriceModifier testModifier = new TestPriceModifier("TestModifier", 100, 0.8);
        registry.registerModifier(testModifier);
        
        long finalPrice = priceCalculator.calculateFinalPrice(
            mockPlayer, mockCommodity, 1, false, "test-bill");
        
        assertEquals(800, finalPrice, "应该应用8折修改器");
        assertTrue(testModifier.wasApplied(), "修改器应该被应用");
    }
    
    @Test
    void testMultipleModifiersApplication() {
        // 注册多个修改器
        TestPriceModifier modifier1 = new TestPriceModifier("Modifier1", 100, 0.9); // 9折
        TestPriceModifier modifier2 = new TestPriceModifier("Modifier2", 200, 0.8); // 8折
        
        registry.registerModifier(modifier1);
        registry.registerModifier(modifier2);
        
        long finalPrice = priceCalculator.calculateFinalPrice(
            mockPlayer, mockCommodity, 1, false, "test-bill");
        
        // 应该先应用modifier1(9折)，再应用modifier2(8折)
        // 1000 * 0.9 * 0.8 = 720
        assertEquals(720, finalPrice, "应该按优先级顺序应用多个修改器");
    }
    
    @Test
    void testModifierPriorityOrder() {
        // 测试修改器优先级顺序
        TestPriceModifier lowPriority = new TestPriceModifier("LowPriority", 300, 0.5);
        TestPriceModifier highPriority = new TestPriceModifier("HighPriority", 100, 0.9);
        
        registry.registerModifier(lowPriority);
        registry.registerModifier(highPriority);
        
        long finalPrice = priceCalculator.calculateFinalPrice(
            mockPlayer, mockCommodity, 1, false, "test-bill");
        
        // 应该先应用高优先级修改器(100)，再应用低优先级修改器(300)
        // 1000 * 0.9 * 0.5 = 450
        assertEquals(450, finalPrice, "应该按优先级顺序应用修改器");
    }
    
    @Test
    void testNonApplicableModifier() {
        // 测试不适用的修改器
        TestPriceModifier nonApplicableModifier = new TestPriceModifier("NonApplicable", 100, 0.8);
        nonApplicableModifier.setApplicable(false);
        
        registry.registerModifier(nonApplicableModifier);
        
        long finalPrice = priceCalculator.calculateFinalPrice(
            mockPlayer, mockCommodity, 1, false, "test-bill");
        
        assertEquals(1000, finalPrice, "不适用的修改器不应该被应用");
        assertFalse(nonApplicableModifier.wasApplied(), "不适用的修改器不应该被调用");
    }
    
    @Test
    void testPriceCalculationWithDetail() {
        // 测试带详情的价格计算
        TestPriceModifier modifier = new TestPriceModifier("TestModifier", 100, 0.8);
        registry.registerModifier(modifier);
        
        PriceCalculationDetail detail = priceCalculator.calculateFinalPriceWithDetail(
            mockPlayer, mockCommodity, 1, false, "test-bill");
        
        assertEquals(1000, detail.getOriginalPrice(), "原始价格应该正确");
        assertEquals(800, detail.getFinalPrice(), "最终价格应该正确");
        assertTrue(detail.isPriceChanged(), "价格应该被修改");
        assertEquals(200, detail.getPriceReduction(), "价格减少应该正确");
        assertEquals(1, detail.getModifications().size(), "应该有一个修改记录");
    }
    
    /**
     * 测试用的价格修改器
     */
    private static class TestPriceModifier implements PriceModifier {
        private final String name;
        private final int priority;
        private final double discountRate;
        private boolean applicable = true;
        private boolean applied = false;
        
        public TestPriceModifier(String name, int priority, double discountRate) {
            this.name = name;
            this.priority = priority;
            this.discountRate = discountRate;
        }
        
        @Override
        public String getName() {
            return name;
        }
        
        @Override
        public int getPriority() {
            return priority;
        }
        
        @Override
        public boolean isApplicable(PriceContext context) {
            return applicable;
        }
        
        @Override
        public PriceModificationResult modifyPrice(PriceContext context, long currentPrice) {
            applied = true;
            long newPrice = Math.round(currentPrice * discountRate);
            return PriceModificationResult.changed(newPrice, 
                String.format("测试折扣%.0f%%", (1.0 - discountRate) * 100), name);
        }
        
        public void setApplicable(boolean applicable) {
            this.applicable = applicable;
        }
        
        public boolean wasApplied() {
            return applied;
        }
    }
}

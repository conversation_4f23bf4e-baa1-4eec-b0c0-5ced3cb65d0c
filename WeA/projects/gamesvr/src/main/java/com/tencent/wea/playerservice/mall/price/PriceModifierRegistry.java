package com.tencent.wea.playerservice.mall.price;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 价格修改器注册表
 * 管理所有的价格修改器
 */
public class PriceModifierRegistry {
    private static final Logger LOGGER = LogManager.getLogger(PriceModifierRegistry.class);
    
    private static final PriceModifierRegistry INSTANCE = new PriceModifierRegistry();
    
    private final Map<String, PriceModifier> modifiers = new ConcurrentHashMap<>();
    private volatile List<PriceModifier> sortedModifiers = new ArrayList<>();
    
    private PriceModifierRegistry() {
    }
    
    public static PriceModifierRegistry getInstance() {
        return INSTANCE;
    }
    
    /**
     * 注册价格修改器
     * @param modifier 价格修改器
     */
    public void registerModifier(PriceModifier modifier) {
        if (modifier == null) {
            LOGGER.warn("Attempted to register null price modifier");
            return;
        }
        
        String name = modifier.getName();
        if (name == null || name.trim().isEmpty()) {
            LOGGER.warn("Attempted to register price modifier with null or empty name");
            return;
        }
        
        PriceModifier existing = modifiers.put(name, modifier);
        if (existing != null) {
            LOGGER.warn("Price modifier '{}' was replaced", name);
        } else {
            LOGGER.info("Registered price modifier: {}", name);
        }
        
        // 重新排序
        updateSortedModifiers();
    }
    
    /**
     * 注销价格修改器
     * @param modifierName 修改器名称
     */
    public void unregisterModifier(String modifierName) {
        PriceModifier removed = modifiers.remove(modifierName);
        if (removed != null) {
            LOGGER.info("Unregistered price modifier: {}", modifierName);
            updateSortedModifiers();
        }
    }
    
    /**
     * 获取所有适用的价格修改器
     * @param context 价格计算上下文
     * @return 适用的修改器列表，按优先级排序
     */
    public List<PriceModifier> getApplicableModifiers(PriceContext context) {
        List<PriceModifier> applicable = new ArrayList<>();
        for (PriceModifier modifier : sortedModifiers) {
            try {
                if (modifier.isApplicable(context)) {
                    applicable.add(modifier);
                }
            } catch (Exception e) {
                LOGGER.error("Error checking applicability for modifier '{}': {}", 
                           modifier.getName(), e.getMessage(), e);
            }
        }
        return applicable;
    }
    
    /**
     * 获取指定名称的修改器
     * @param name 修改器名称
     * @return 修改器实例，如果不存在则返回null
     */
    public PriceModifier getModifier(String name) {
        return modifiers.get(name);
    }
    
    /**
     * 获取所有注册的修改器名称
     * @return 修改器名称集合
     */
    public Set<String> getRegisteredModifierNames() {
        return new HashSet<>(modifiers.keySet());
    }
    
    /**
     * 更新排序后的修改器列表
     */
    private void updateSortedModifiers() {
        List<PriceModifier> newSorted = new ArrayList<>(modifiers.values());
        newSorted.sort(Comparator.comparingInt(PriceModifier::getPriority));
        this.sortedModifiers = newSorted;
    }
    
    /**
     * 清空所有修改器（主要用于测试）
     */
    public void clear() {
        modifiers.clear();
        sortedModifiers = new ArrayList<>();
        LOGGER.info("Cleared all price modifiers");
    }
}

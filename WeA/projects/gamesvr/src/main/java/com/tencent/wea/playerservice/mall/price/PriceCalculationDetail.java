package com.tencent.wea.playerservice.mall.price;

import java.util.List;

/**
 * 价格计算详情
 * 包含价格计算的完整过程信息
 */
public class PriceCalculationDetail {
    private final long originalPrice;
    private final long finalPrice;
    private final List<PriceModificationResult> modifications;
    
    public PriceCalculationDetail(long originalPrice, long finalPrice, List<PriceModificationResult> modifications) {
        this.originalPrice = originalPrice;
        this.finalPrice = finalPrice;
        this.modifications = modifications;
    }
    
    public long getOriginalPrice() {
        return originalPrice;
    }
    
    public long getFinalPrice() {
        return finalPrice;
    }
    
    public List<PriceModificationResult> getModifications() {
        return modifications;
    }
    
    public boolean isPriceChanged() {
        return originalPrice != finalPrice;
    }
    
    public long getPriceReduction() {
        return originalPrice - finalPrice;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("PriceCalculationDetail{");
        sb.append("original=").append(originalPrice);
        sb.append(", final=").append(finalPrice);
        sb.append(", changed=").append(isPriceChanged());
        if (isPriceChanged()) {
            sb.append(", reduction=").append(getPriceReduction());
        }
        sb.append(", modifications=").append(modifications.size());
        sb.append("}");
        return sb.toString();
    }
}

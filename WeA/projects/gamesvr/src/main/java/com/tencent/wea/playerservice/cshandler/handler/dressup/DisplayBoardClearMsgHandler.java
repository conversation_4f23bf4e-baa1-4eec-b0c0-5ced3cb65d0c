package com.tencent.wea.playerservice.cshandler.handler.dressup;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.cshandler.GamesvrPbMsgHandlerFactory;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsDressup;
import com.tencent.wea.protocol.CsHead;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.protocol.MsgTypes;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class DisplayBoardClearMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(DisplayBoardClearMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        //CsDressup.DisplayBoardClear_C2S_Msg reqMsg = (CsDressup.DisplayBoardClear_C2S_Msg)request;
        NKErrorCode.UnknownError.throwError("DisplayBoardClearMsgHandler not implemented");
        return null;
    }
}

package com.tencent.wea.playerservice.cshandler.handler.masterpath;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.cshandler.GamesvrPbMsgHandlerFactory;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsMasterPath;
import com.tencent.wea.protocol.CsHead;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.protocol.MsgTypes;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ReceiveMasterPatchRewardMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(ReceiveMasterPatchRewardMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        //CsMasterPath.ReceiveMasterPatchReward_C2S_Msg reqMsg = (CsMasterPath.ReceiveMasterPatchReward_C2S_Msg)request;
        NKErrorCode.UnknownError.throwError("ReceiveMasterPatchRewardMsgHandler not implemented");
        return null;
    }
}

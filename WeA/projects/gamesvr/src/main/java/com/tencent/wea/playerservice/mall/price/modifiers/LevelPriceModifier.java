package com.tencent.wea.playerservice.mall.price.modifiers;

import com.tencent.wea.playerservice.mall.price.PriceContext;
import com.tencent.wea.playerservice.mall.price.PriceModificationResult;
import com.tencent.wea.playerservice.mall.price.PriceModifier;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 等级价格修改器
 * 根据玩家等级提供价格折扣
 */
public class LevelPriceModifier implements PriceModifier {
    private static final Logger LOGGER = LogManager.getLogger(LevelPriceModifier.class);
    
    private static final String MODIFIER_NAME = "LevelPriceModifier";
    private static final int PRIORITY = 300;
    
    // 等级折扣配置
    private static final int[] LEVEL_THRESHOLDS = {10, 20, 30, 50, 80, 100};
    private static final double[] LEVEL_DISCOUNTS = {0.98, 0.95, 0.92, 0.88, 0.85, 0.80};
    
    @Override
    public String getName() {
        return MODIFIER_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean isApplicable(PriceContext context) {
        // 检查是否启用等级折扣功能
        if (!isLevelDiscountEnabled()) {
            return false;
        }
        
        // 检查商品是否支持等级折扣
        if (!isCommoditySupportLevelDiscount(context)) {
            return false;
        }
        
        // 检查玩家等级是否达到最低要求
        int playerLevel = getPlayerLevel(context);
        return playerLevel >= LEVEL_THRESHOLDS[0];
    }
    
    @Override
    public PriceModificationResult modifyPrice(PriceContext context, long currentPrice) {
        int playerLevel = getPlayerLevel(context);
        double discount = getLevelDiscount(playerLevel);
        
        if (discount >= 1.0) {
            return PriceModificationResult.noChange(currentPrice);
        }
        
        long newPrice = Math.round(currentPrice * discount);
        
        // 确保价格不为负数
        if (newPrice < 0) {
            newPrice = 0;
        }
        
        String reason = String.format("等级%d折扣%.0f%%", playerLevel, (1.0 - discount) * 100);
        
        LOGGER.debug("Level price modification: uid={}, commodityId={}, level={}, " +
                    "originalPrice={}, newPrice={}, discount={}", 
                    context.getUid(), context.getCommodityId(), playerLevel,
                    currentPrice, newPrice, discount);
        
        return PriceModificationResult.changed(newPrice, reason, MODIFIER_NAME);
    }
    
    /**
     * 检查是否启用等级折扣功能
     * @return 是否启用
     */
    private boolean isLevelDiscountEnabled() {
        // 这里可以从配置文件或开关控制
        return true; // 暂时默认启用
    }
    
    /**
     * 检查商品是否支持等级折扣
     * @param context 价格计算上下文
     * @return 是否支持
     */
    private boolean isCommoditySupportLevelDiscount(PriceContext context) {
        // 可以通过商品配置的特定字段或标签来判断
        // 例如：某些特殊商品不参与等级折扣
        return true; // 暂时默认所有商品都支持
    }
    
    /**
     * 获取玩家等级
     * @param context 价格计算上下文
     * @return 玩家等级
     */
    private int getPlayerLevel(PriceContext context) {
        try {
            // 这里需要根据实际的等级系统获取玩家等级
            // return context.getPlayer().getLevel();
            return 1; // 暂时返回1
        } catch (Exception e) {
            LOGGER.error("Error getting player level: {}", e.getMessage(), e);
            return 1;
        }
    }
    
    /**
     * 根据等级获取折扣率
     * @param level 玩家等级
     * @return 折扣率
     */
    private double getLevelDiscount(int level) {
        for (int i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--) {
            if (level >= LEVEL_THRESHOLDS[i]) {
                return LEVEL_DISCOUNTS[i];
            }
        }
        return 1.0; // 无折扣
    }
}

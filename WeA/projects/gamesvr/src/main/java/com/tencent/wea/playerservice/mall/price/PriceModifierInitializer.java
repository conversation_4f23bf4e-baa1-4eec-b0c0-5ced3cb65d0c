package com.tencent.wea.playerservice.mall.price;

import com.tencent.wea.playerservice.mall.price.modifiers.ActivityPriceModifier;
import com.tencent.wea.playerservice.mall.price.modifiers.LevelPriceModifier;
import com.tencent.wea.playerservice.mall.price.modifiers.VipPriceModifier;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 价格修改器初始化器
 * 负责注册所有的价格修改器
 */
public class PriceModifierInitializer {
    private static final Logger LOGGER = LogManager.getLogger(PriceModifierInitializer.class);
    
    private static volatile boolean initialized = false;
    
    /**
     * 初始化所有价格修改器
     */
    public static void initialize() {
        if (initialized) {
            LOGGER.warn("Price modifiers already initialized");
            return;
        }
        
        synchronized (PriceModifierInitializer.class) {
            if (initialized) {
                return;
            }
            
            try {
                PriceModifierRegistry registry = PriceModifierRegistry.getInstance();
                
                // 注册VIP价格修改器
                registry.registerModifier(new VipPriceModifier());
                
                // 注册活动价格修改器
                registry.registerModifier(new ActivityPriceModifier());
                
                // 注册等级价格修改器
                registry.registerModifier(new LevelPriceModifier());
                
                // 可以在这里注册更多的价格修改器
                // registry.registerModifier(new SeasonPriceModifier());
                // registry.registerModifier(new CustomPriceModifier());
                
                initialized = true;
                LOGGER.info("Price modifiers initialized successfully");
                
            } catch (Exception e) {
                LOGGER.error("Failed to initialize price modifiers: {}", e.getMessage(), e);
                throw new RuntimeException("Price modifier initialization failed", e);
            }
        }
    }
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    public static boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 重置初始化状态（主要用于测试）
     */
    public static void reset() {
        synchronized (PriceModifierInitializer.class) {
            PriceModifierRegistry.getInstance().clear();
            initialized = false;
            LOGGER.info("Price modifiers reset");
        }
    }
}

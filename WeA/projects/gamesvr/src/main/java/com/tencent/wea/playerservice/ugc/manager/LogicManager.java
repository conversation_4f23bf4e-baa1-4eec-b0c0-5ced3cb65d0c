package com.tencent.wea.playerservice.ugc.manager;

import com.tencent.resourceloader.resclass.UgcUploadConfig;
import com.tencent.wea.playerservice.ugc.map.*;
import com.tencent.wea.playerservice.ugc.secret.*;
import com.tencent.wea.protocol.common.UgcInstanceType;
import com.tencent.wea.xlsRes.keywords.CosOperateScene;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.ConcurrentHashMap;


/**
 * willwizhang
 */
public class LogicManager {

    private static final Logger LOGGER = LogManager.getLogger(LogicManager.class);
    private static final ConcurrentHashMap<Integer, BaseSecret> allSecret = new ConcurrentHashMap<>();

    static {
        allSecret.put(CosOperateScene.Map_Scene_VALUE, new MapSecret());
        allSecret.put(CosOperateScene.Report_Scene_VALUE, new ReportSecret());
        allSecret.put(CosOperateScene.Aigc_Scene_VALUE, new AigcSecret());
        allSecret.put(CosOperateScene.Share_Scene_VALUE, new ShareSecret());
        allSecret.put(CosOperateScene.Xiaowo_Scene_VALUE, new XiaowoParty());
        allSecret.put(CosOperateScene.Party_Scene_VALUE, new XiaowoParty());
//        allSecret.put(CosOperateScene.Xiaowo_Map_Scene_VALUE, new XiaowoMapSecret());
        allSecret.put(CosOperateScene.Home_Scene_VALUE, new HomeSecret());
        allSecret.put(CosOperateScene.Layout_Scene_VALUE, new LayoutSecret());
        allSecret.put(CosOperateScene.MulPlay_Scene_VALUE, new MultiPlaySecret());
        allSecret.put(CosOperateScene.PreAudit_Scene_VALUE, new PreAuditSecret());
        allSecret.put(CosOperateScene.MulCover_Scene_VALUE, new MulCoverSecret());
        allSecret.put(CosOperateScene.PhotoAlbum_Scene_VALUE, new PhotoAlbumSecret());
        allSecret.put(CosOperateScene.CommonShare_Scene_VALUE, new CommonShareSecret());
        allSecret.put(CosOperateScene.Map_Publish_Scene_VALUE, new PublishReviewSecret());
        allSecret.put(CosOperateScene.Ds_Upload_Scene_VALUE, new DsUploadSecret());
        allSecret.put(CosOperateScene.Custom_Scene_VALUE, new CustomSecret());
        allSecret.put(CosOperateScene.FarmCookComment_Scene_VALUE, new FarmCookCommentSecret());
        allSecret.put(CosOperateScene.Zuoyebang_Scene_VALUE, new ZuoyebangSecret());
        allSecret.put(CosOperateScene.ZuoyebangCopy_Scene_VALUE, new ZuoyebangCopySecret());
        allSecret.put(CosOperateScene.Res_Scene_VALUE, new ResSecret());
        allSecret.put(CosOperateScene.Group_Scene_VALUE, new GroupSecret());
        allSecret.put(CosOperateScene.ItaBag_Scene_VALUE, new ItaBagSecret());
    }

    private final WorkshopMap workshop = new WorkshopMap();
    private final WorkshopGroup workshopGroup = new WorkshopGroup();
    private final WorkshopHome workshopHome = new WorkshopHome();
    private final WorkshopLayout workshopLayout = new WorkshopLayout();
    private final WorkshopRes workshopRes = new WorkshopRes();

    public static LogicManager getInstance() {
        return InstanceHolder.instance;
    }

    public BaseSecret getSecretByReason(int reason) {
        int scene = UgcUploadConfig.getInstance().getReasonScene(reason);
        return allSecret.get(scene);
    }

    public HomeSecret getHomeSecret() {
        return (HomeSecret) allSecret.get(CosOperateScene.Home_Scene_VALUE);
    }

    public LayoutSecret getLayoutSecret() {
        return (LayoutSecret) allSecret.get(CosOperateScene.Layout_Scene_VALUE);
    }

    public WorkshopMap getWorkshopMap() {
        return workshop;
    }

    public WorkshopGroup getWorkShopGroup() {
        return this.workshopGroup;
    }

    public WorkshopHome getWorkshopHome() {
        return this.workshopHome;
    }

    public WorkshopLayout getWorkshopLayout() {
        return this.workshopLayout;
    }

    public WorkshopRes getWorkshopRes() {
        return this.workshopRes;
    }

    public ItaBagSecret getItaBagSecret() {
        return (ItaBagSecret) allSecret.get(CosOperateScene.ItaBag_Scene_VALUE);
    }

    public BaseMap getWorkshop(UgcInstanceType type) {
        switch (type.getNumber()) {
            case UgcInstanceType.GroupInstance_VALUE:
                return getWorkShopGroup();
            case UgcInstanceType.CommonInstance_VALUE:
                return getWorkshopMap();
            case UgcInstanceType.ResInstance_VALUE:
                return getWorkshopRes();
            case UgcInstanceType.HomeInstance_VALUE:
                //小窝保存草稿没有图片审核，而且必须保证保存草稿和发布一起成功，因此不可以是异步
                return getWorkshopHome();
            case UgcInstanceType.LayoutInstance_VALUE:
                return getWorkshopLayout();
        }
        return null;
    }

    public BaseSecret getSecret(UgcInstanceType type){
        switch (type.getNumber()) {
            case UgcInstanceType.HomeInstance_VALUE:
                return getHomeSecret();
            case UgcInstanceType.ResInstance_VALUE:
                return getGroupSecret();
            case UgcInstanceType.GroupInstance_VALUE:
                return getResSecret();
            case UgcInstanceType.LayoutInstance_VALUE:
                return getLayoutSecret();
            default:
                return getMapSecret();
        }
    }

    public MapSecret getMapSecret() {
        return (MapSecret) allSecret.get(CosOperateScene.Map_Scene_VALUE);
    }

    public ResSecret getResSecret() {
        return (ResSecret) allSecret.get(CosOperateScene.Res_Scene_VALUE);
    }

    public GroupSecret getGroupSecret() {
        return (GroupSecret) allSecret.get(CosOperateScene.Group_Scene_VALUE);
    }

    public AigcSecret getAigcSecret() {
        return (AigcSecret) allSecret.get(CosOperateScene.Aigc_Scene_VALUE);
    }

    public ShareSecret getShareSecret() {
        return (ShareSecret) allSecret.get(CosOperateScene.Share_Scene_VALUE);
    }

    public ZuoyebangCopySecret getZuoyebangCopySecret() {
        return (ZuoyebangCopySecret) allSecret.get(CosOperateScene.ZuoyebangCopy_Scene_VALUE);
    }
    private static class InstanceHolder {

        public static LogicManager instance = new LogicManager();
    }

    class BelongScene {

        public final static int Map_Scene_VALUE = 0;
        public final static int Report_Scene_VALUE = 0;

    }

}

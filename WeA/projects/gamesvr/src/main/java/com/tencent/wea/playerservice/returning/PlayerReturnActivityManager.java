package com.tencent.wea.playerservice.returning;

import static com.tencent.wea.xlsRes.keywords.ReturningUserConstsEnum.RUCE_DefaultReturningUserConfId;
import static com.tencent.wea.xlsRes.keywords.ReturningUserConstsEnum.RUCE_FriendshipFireGetNumLimitWeekly;
import static com.tencent.wea.xlsRes.keywords.ReturningUserConstsEnum.RUCE_FriendshipFireGetNumPerRound;
import static com.tencent.wea.xlsRes.keywords.ReturningUserConstsEnum.RUCE_FriendshipFireItemCfgId;
import static com.tencent.wea.xlsRes.keywords.ReturningUserConstsEnum.RUCE_OptionalRewardShowCount;
import static com.tencent.wea.xlsRes.keywords.ReturningUserConstsEnum.RUCE_TeamerReturningPrivilegeDayCount;

import com.google.gson.Gson;
import com.tencent.eventbuspro.*;
import com.tencent.eventcenter.*;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.*;
import com.tencent.nk.util.random.*;
import com.tencent.nk.util.random.util.*;
import com.tencent.recommend.RecommendMgr;
import com.tencent.resourceloader.resclass.*;
import com.tencent.resourceloader.resclass.FarmBuffConf.Source;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.timiutil.tool.*;
import com.tencent.util.CollectionUtil;
import com.tencent.util.VersionUtil;
import com.tencent.wea.attr.*;
import com.tencent.wea.framework.GSConfig;
import com.tencent.wea.midas.MidasProductUtil.MidasProductParam;
import com.tencent.wea.playerservice.activity.implement.BaseTaskActivity;
import com.tencent.wea.playerservice.activity.implement.ReturningDiffersActivity;
import com.tencent.wea.playerservice.activity.implement.ReturningTaskActivity;
import com.tencent.wea.playerservice.activity.implement.ReturningTaskDailyGroupActivity;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.event.common.PlayerGotReturnActivityChargeGiftTicketEvent;
import com.tencent.wea.playerservice.event.common.PlayerGotReturnActivityChargeSignInTicketEvent;
import com.tencent.wea.playerservice.event.common.farm.*;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.task.RunTask;
import com.tencent.wea.protocol.*;
import com.tencent.wea.protocol.CsActivity;
import com.tencent.wea.protocol.CsActivity.BuyReturnChargeSignInActivityTicketNtf;
import com.tencent.wea.protocol.CsActivity.ReturnActivityBuyChargeGiftTicketNtf;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.protocol.common.G6Common.LevelDropItemInfo;
import com.tencent.wea.room.RoomUtil.UpdateMemberBaseInfoSource;
import com.tencent.wea.rpc.service.FarmService;
import com.tencent.wea.xlsRes.ResActivity;
import com.tencent.wea.xlsRes.ResCommon;
import com.tencent.wea.xlsRes.ResCommon.Item;
import com.tencent.wea.xlsRes.ResReturningUser;
import com.tencent.wea.xlsRes.ResReturningUser.ReturningUserBait;
import com.tencent.wea.xlsRes.ResReturningUser.ReturningUserConf;
import com.tencent.wea.xlsRes.ResReturningUser.ReturningUserConstsInfo;
import com.tencent.wea.xlsRes.ResTask;
import com.tencent.wea.xlsRes.ResTask.TaskRewardConf;
import com.tencent.wea.xlsRes.keywords.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class PlayerReturnActivityManager extends PlayerModule {

    private static final Logger LOGGER = LogManager.getLogger(PlayerReturnActivityManager.class);

    final private Gson gson = new Gson();

    private static final Integer ReturningUserConfVersion1 = 1; // 回流活动版本1
    private static final Integer ReturningUserConfVersion2 = 2; // 回流活动版本2
    private static final Integer ReturningUserConfVersion3 = 3; // 回流活动版本3
    private static final Integer ReturningUserConfVersion4 = 4; // 回流活动版本4
    private static final Integer ReturningUserConfVersion5 = 5; // 回流活动版本5
    private static final Integer ReturningUserConfVersion6 = 6; // 回流活动版本6

    public static final Integer ON_LOGIN = 0;
    public static final Integer ON_MIDNIGHT = 1;
    public static final Integer CS_MSG = 2;

    public PlayerReturnActivityManager(Player player) {
        super(GameModuleId.GMI_ReturnActivityMgr, player);
        player.getEventSwitch().register(new OnFarmCreate(player));
    }

    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    @Override
    public void onRegister() throws NKCheckedException {

    }

    @Override
    public void afterRegister() throws NKCheckedException {

    }

    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    @Override
    public void onLoad() throws NKCheckedException {

    }

    @Override
    public void afterLoad() {

    }

    @Override
    public void prepareLogin() throws NKCheckedException {

    }

    @Override
    public void onLogin() throws NKCheckedException {
        try {
            // 当前环境回流总开关。开关关闭时，所有玩家不再开启回流，已经开启回流的玩家登录时立即关闭回流，终极防刷保底，非必要勿用
            if (!GSConfig.isReturningActivityEnable()) {
                stopReturnActivity(true, true);
                return;
            }

            freshReturnActivityStatus(true);
            tryStartReturnActivity();
            registerStartMatchGiftTask(null, true);
        } catch (Exception e) {
            LOGGER.error("player:{} PlayerReturnActivityManager onLogin exception", player.getUid(), e);
        }
    }

    /**
     * 异步从算法获取回流相关的玩法推荐，异步从Redis获取回流相关的玩家活跃度
     *
     * @param todayFirstLogin 是否为当天首次登录
     */
    @Override
    public void afterLogin(boolean todayFirstLogin) {

    }

    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {

    }

    @Override
    public void onLogout() {

    }

    @Override
    public void onMidNight() {
        freshReturnActivityStatus(false);
        dailyClear();
        freshAlgoRecommendMatchInfo();
        sendAttrChangeNtf();
    }

    public void refreshRecommendReturnMatchType() {
        AlgoRecommendMatchInfo playerAlgoInfo = player.getUserAttr().getAlgoRecommendMatchInfo();
        playerAlgoInfo.clear();
        var rspParams = RecommendMgr.getInstance()
                .reqRecommendReturnMatchType(player.getOpenId(), player.getUid());
        if (rspParams != null) {
            playerAlgoInfo.getMatchTypes().addAll(rspParams.recommendIds);
            playerAlgoInfo.setRecId(rspParams.recId);
            playerAlgoInfo.setExpTag(rspParams.expTag);
        }
    }

    private void freshAlgoRecommendMatchInfo(){
        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                refreshRecommendReturnMatchType();
                if (!isPrivilegeValid()) {
                    // 特权期间不刷新活跃度数据，以免造成推荐与任务不一致
                    int activeLevel = ActiveLevelManager.getInstance().syncGetActiveLevelByUid(player.getUid());
                    if (activeLevel >= 0) {
                        getPlayerReturnActivity().setActiveLevel(activeLevel);
                    }
                }
                return null;
            }, "afterLogin", true);
        } catch (Exception e) {
            LOGGER.error("afterLogin failed");
        }
    }

    @Override
    public void onWeekStart() {
        // 清理友谊火种周数量限制
        LOGGER.info("player:{} FireGotNumWeekly clear", player.getUid());
        getPlayerReturnActivity().getPrivilege().setFireGotNumWeekly(0);
    }

    @Override
    public void onReload() {
        if (!GSConfig.isReturningActivityEnable()) {
            stopReturnActivity(true, false);
        }
    }

    /**
     * 玩家处于回流时，刷新相关状态
     *
     * @param onLogin 是否是登录时调用
     */
    private void freshReturnActivityStatus(boolean onLogin) {
        // 回流期间登陆次数加一，如果是午夜刷新就清空次数
        freshReturnActivityLoginCount(onLogin);
        if (isReturnActivityActive()) {
            tryReturnActivityEnd(onLogin);
            if (isReturnActivityActive()) {
                freshReturnPrivilege(onLogin);
                if (onLogin) {
                    sendReturningActivityStartNtf();
                }
                trySendAutoDailyReward(onLogin ? ON_LOGIN : ON_MIDNIGHT);
            }
        }
    }

    /**
     * 回流期间登陆次数加一，如果是午夜刷新就清空次数
     * @param onLogin
     */
    private void freshReturnActivityLoginCount(boolean onLogin) {
        // 获取ReturnActivity对象，减少重复访问
        ReturnActivity returnActivity = player.getUserAttr().getReturningInfo().getReturnActivity();

        if (onLogin) {
            // 增加登录次数
            int curLoginCount = returnActivity.getDayLoginCount() + 1;
            returnActivity.setDayLoginCount(curLoginCount);
        } else {
            // 重置登录次数
            returnActivity.setDayLoginCount(0);
        }

        // 发送属性变更通知
        sendAttrChangeNtf();
    }

    /**
     * 登录及登出流水中回流打点
     *
     * @return 组织为json，只占用tLog一个字段，方便修改和扩展
     */
    public String getTlogKeyValueString() {
        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put("beginTime", getPlayerReturnActivity().getBeginTime()); // 回流开始时间戳Sec, !=0 表示回流激活状态
        jsonMap.put("endTime", getPlayerReturnActivity().getEndTime()); // 回流结束时间戳Sec, 已结束会保留，直到再次开启回流更新
        jsonMap.put("version", getPlayerReturnActivity().getUserConfId()); // 触发的回流版本，version=1,2,3
        jsonMap.put("baitIndex", getPlayerReturnActivity().getUserBaitIndex()); // 触发的档位索引, baitIndex=0,1,2
        jsonMap.put("signActivityId", getPlayerReturnActivity().getSignInActivityId()); // 签到活动ID
        jsonMap.put("privilegeActivityId", getPlayerReturnActivity().getPrivilegeActivityId()); // 特权活动ID
        jsonMap.put("taskActivityId", getPlayerReturnActivity().getTaskActivityId()); // 任务活动ID
        jsonMap.put("chargeSignInActivityId", getPlayerReturnActivity().getChargeSignInActivityId()); // 1元活动ID
        jsonMap.put("chargeGiftActivityId", getPlayerReturnActivity().getChargeGiftActivityId()); // 6元活动ID
        jsonMap.put("dailyRewardActivityId", getPlayerReturnActivity().getDailyReward().getActivityId()); // 每日福利活动ID
        jsonMap.put("activeLevel", getPlayerReturnActivity().getActiveLevel()); // 活跃度, 0:低 1:中 2:高
        jsonMap.put("firstGameActivityId", getPlayerReturnActivity().getFirstGameActivityId());
        ArrayList<Integer> recommendMatchTypes = new ArrayList<>();
        for (Integer confId : getPlayerReturnActivity().getDailyReward().getExclusiveRecommendList()) {
            recommendMatchTypes.add(ReturningRecommend2Data.getInstance().getMatchTypeByResId(confId));
        }
        jsonMap.put("dailyRewardRecommend", recommendMatchTypes); // 每日福利玩法推荐
        return gson.toJson(jsonMap);
    }

    /**
     * 回流特权概念解释: 回流分两个阶段，按照现有配置，前7天属于回流特权期，部分牵引的活动只在特权期间存在，特权也对应回流标志
     * 按照现有配置，特权结束后，会再持续7天，部分活动会持续7+7=14天，14天后本轮回流关闭
     *
     * @return true:回流特权生效中、false:回流特权已失效
     */
    public boolean isPrivilegeValid() {
        if (!isReturnActivityActive()) {
            return false;
        }
        ReturningUserBait returningUserBait = getResReturningUserBait(getPlayerReturnActivity().getUserConfId(),
                getPlayerReturnActivity().getUserBaitIndex());
        if (null == returningUserBait) {
            LOGGER.error("player:{} getReturningUserBaitByIndex failed confId:{} baitIndex:{}", player.getUid(),
                    getPlayerReturnActivity().getUserConfId(), getPlayerReturnActivity().getUserBaitIndex());
            return false;
        }

        long curDayEndSec = DateUtils.getDayEndTimeSec(Framework.currentTimeSec());
        long returnStartDaySec = DateUtils.getDayBeginTimeSec(getPlayerReturnActivity().getBeginTime());
        long returnDayNo = TimeUnit.SECONDS.toDays(curDayEndSec - returnStartDaySec);

        // 当回归天编号小于等于配置时，特权生效
        return returnDayNo <= returningUserBait.getPrivilegeDays();
    }

    /**
     * 玩家回归活动是否激活
     * @return true表示已激活 false表示未激活
     */
    public boolean isReturnActivityActive() {
        if (!GSConfig.isReturningActivityEnable()) {
            return false;
        }
        return getPlayerReturnActivity().getBeginTime() > 0;
    }

    /**
     * @return 回归一次性大奖是否已领取
     */
    public boolean isRewardImmediateAvailed() {
        return !getPlayerReturnActivity().getCustomGift();
    }

    private ReturnActivity getPlayerReturnActivity() {
        return player.getUserAttr().getReturningInfo().getReturnActivity();
    }

    private static ReturningUserConstsInfo getResConstData(ReturningUserConstsEnum type) {
        return ReturningUserConstsData.getInstance().get(type);
    }

    private ReturningUserBait getResReturningUserBait(int userConfId, int baitIndex) {
        return ReturningUserConfData.getInstance().getReturningUserBaitByIndex(userConfId, baitIndex);
    }

    private ResReturningUser.ReturnPushFaceSettingData getReturnPushFaceSettingDataV2(int activeLevel, java.util.Collection<Integer> matchTypesList) {
        return ReturnPushFaceSettingDataV2.getInstance().getReturnPushFaceSettingData(activeLevel, matchTypesList);
    }


    /**
     * @return 回归双倍对局奖励是否可用
     */
    public boolean isDailyDoubleRewardValid() {
        // 双倍对局限次数
        int returnPrivilegeRoundLimitPerDay = 0;

        if (!isPrivilegeValid()) {
            ReturningUserConstsInfo cfgVal = getResConstData(RUCE_TeamerReturningPrivilegeDayCount);
            if (null == cfgVal) {
                LOGGER.error("player:{} getReturningUserConstInfo:{} failed", player.getUid(),
                        RUCE_TeamerReturningPrivilegeDayCount);
                return false;
            }
            returnPrivilegeRoundLimitPerDay = cfgVal.getValue();
        } else {
            ReturningUserBait returningUserBait = getResReturningUserBait(getPlayerReturnActivity().getUserConfId(),
                    getPlayerReturnActivity().getUserBaitIndex());
            if (null == returningUserBait) {
                LOGGER.error("player:{} getReturningUserBaitByIndex failed confId:{} baitIndex:{}", player.getUid(),
                        getPlayerReturnActivity().getUserConfId(), getPlayerReturnActivity().getUserBaitIndex());
                return false;
            }
            returnPrivilegeRoundLimitPerDay = returningUserBait.getPrivilegeRoundCount();
        }

        // 当天对局次数小于配置时，双倍生效
        return getPlayerReturnActivity().getPrivilege().getDailyDoubleRewardCnt() < returnPrivilegeRoundLimitPerDay;
    }

    /**
     * @param value 增加限次数
     */
    public void addDailyDoubleReward(int value) {
        int cnt = getPlayerReturnActivity().getPrivilege().getDailyDoubleRewardCnt() + value;
        getPlayerReturnActivity().getPrivilege().setDailyDoubleRewardCnt(cnt);
        LOGGER.info("player:{} DailyDoubleRewardCnt:{}", player.getUid(), cnt);
    }

    /**
     * 刷新回流特相关状态
     * 特权生效期间: 刷新每日福利任务组、重置玩法推荐、幂等添加农场buff
     * 特权首次过期时: 移除回归标志、关闭特权活动、关闭未付费的付费礼物活动、关闭每日福利活动、幂等移除农场buff
     *
     * @param onLogin 是否在登陆时触发
     */
    private void freshReturnPrivilege(boolean onLogin) {
        if (isPrivilegeValid()) {
            long dayOffset = TimeUnit.SECONDS.toDays(Framework.currentTimeSec() - DateUtils.getDayBeginTimeSec(
                    getPlayerReturnActivity().getBeginTime()));
            getPlayerReturnActivity().getDailyReward().setTaskGroupIndex((int) dayOffset);
            resetRecommendInfo(false);
            asyncAddFarmBuffs();

        } else if (player.getUserAttr().getPlayerPublicProfileInfo().getReturning()) {
            // 特权新失效的, 着手处理以下流程
            player.getUserAttr().getPlayerPublicProfileInfo().setReturning(false);
            player.getUserAttr().getPlayerPublicProfileInfo().setReturnExpiredSec(0);
            player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.ReturnPrivilegeChange);

            removeReturnActivity(getPlayerReturnActivity().getPrivilegeActivityId(), onLogin);
            getPlayerReturnActivity().setPrivilegeActivityId(0);

            if (!getPlayerReturnActivity().getChargeGiftTicketGot()) {
                removeReturnActivity(getPlayerReturnActivity().getChargeGiftActivityId(), onLogin);
                getPlayerReturnActivity().setChargeGiftActivityId(0);
            }

            removeReturnDailyTaskGroupActivity(getPlayerReturnActivity().getDailyReward().getActivityId(), onLogin);
            getPlayerReturnActivity().getDailyReward().clear();

            asyncRemoveFarmBuffs();
        }
    }

    /**
     * 清理每日双倍奖励限次
     */
    private void dailyClear() {
        getPlayerReturnActivity().getPrivilege().setDailyDoubleRewardCnt(0);
        LOGGER.info("player:{} DailyDoubleRewardCnt clear", player.getUid());
    }

    /**
     * 寻找适合当前客户端版本的配置
     * TODO: 将 returningUserConf.xxxTime 的判断也纳入此函数，防止此处确定配置后时间不满足滑档，具体可咨询 mitchellxie
     *
     * @return 回流配置
     */
    private ReturningUserConf getValidReturningUserConf() {
        ReturningUserConstsInfo cfgVal = getResConstData(RUCE_DefaultReturningUserConfId);
        if (null == cfgVal) {
            LOGGER.error("RUCE_DefaultReturningUserConfId not exist");
            return null;
        }
        long curSec = Framework.currentTimeSec();
        for (int cfgId = cfgVal.getValue(); cfgId >= 1; cfgId--) {
            ReturningUserConf returningUserConf = ReturningUserConfData.getInstance().get(cfgId);
            if (returningUserConf == null) {
                LOGGER.debug("get returningUserConf err, uid:{} cfgId:{}", player.getUid(), cfgId);
                continue;
            }
            if (player.isRobot() || VersionUtil.checkClientVersion(returningUserConf.getLowestVersion(),
                    returningUserConf.getHighestVersion(), player.getClientVersion64())) {
                LOGGER.debug("check version pass, uid:{} cfgId:{}", player.getUid(), returningUserConf.getId());
                if (curSec >= returningUserConf.getStartTime().getSeconds() &&
                        curSec < returningUserConf.getEndTime().getSeconds()) {
                    LOGGER.debug("check time pass, uid:{} cfgId:{}", player.getUid(), returningUserConf.getId());
                    return returningUserConf;
                } else {
                    LOGGER.debug("check time err, uid:{} cfgId:{}", player.getUid(), returningUserConf.getId());
                }
            } else {
                LOGGER.debug("check version err continue, uid:{} cfgId:{}", player.getUid(), returningUserConf.getId());
            }
        }

        return null;
    }

    /**
     * @param bait 档位资源配置
     * @param lossDays 玩家流失天数
     * @param gapDays 玩家上次回流结束间隔天数
     * @return 当前档位配置是否满足开启条件
     */
    private boolean isBaitConditionsOk(ReturningUserBait bait, long lossDays, long gapDays) {
        return bait.getBar() <= lossDays && bait.getTriggerCdDays() <= gapDays
                && bait.getPlayerLevelLimit() <= player.getLevel();
    }

    /**
     * 尝试开启回流。进行一系列的判断和寻找，筛选满足的回流配置，若不满足，则不开启回流
     */
    private void tryStartReturnActivity() {
        // 仍然是开启的，直接退出检查
        if (isReturnActivityActive()) {
            LOGGER.debug("player:{} returnActivity is already Active return", player.getUid());
            return;
        }

        try {
            if (GSConfig.isForceReturningEnable()) {
                LOGGER.debug("player:{} ForceReturningEnable", player.getUid());
                forceStartReturnActivity();
                return;
            }

            ReturningUserConf returningUserConf = getValidReturningUserConf();
            if (null == returningUserConf) {
                LOGGER.error("ReturningUserConf for clientVersion:{} not exists", player.getClientVersion64());
                return;
            }

            long lastLogoutSec = TimeUnit.MILLISECONDS.toSeconds(
                    player.getUserAttr().getPlayerPublicBasicInfo().getLogoutTimeMs());
            if (lastLogoutSec <= 0) {
                LOGGER.debug("player:{} lastLogoutSec <= 0 confirm as a new player", player.getUid());
                return;
            }
            long curSec = Framework.currentTimeSec();

            long curDayStartSec = DateUtils.getDayBeginTimeSec(curSec);

            // 流失天数
            long lossDays = TimeUnit.SECONDS.toDays(curDayStartSec - DateUtils.getDayEndTimeSec(lastLogoutSec));
            // 冷却天数
            long gapDays = TimeUnit.SECONDS.toDays(
                    curDayStartSec - DateUtils.getDayEndTimeSec(getPlayerReturnActivity().getEndTime()));
            LOGGER.debug("player:{} curSec:{} lossDays:{} gapDays:{}", player.getUid(), curSec, lossDays, gapDays);

            ReturningUserBait targetReturningUserBait = null;
            ReturningUserBait highReturningUserBait = null;
            for (ReturningUserBait returningUserBait : returningUserConf.getBaitsList()) {
                if (isBaitConditionsOk(returningUserBait, lossDays, gapDays)) {
                    targetReturningUserBait = returningUserBait;
                }
                if (highReturningUserBait == null || highReturningUserBait.getBar() < returningUserBait.getBar()) {
                    highReturningUserBait = returningUserBait;
                }
            }

            if (targetReturningUserBait != null && getPlayerReturnActivity().getEndTime() == 0) {
                targetReturningUserBait = highReturningUserBait;
            }
            if (null != targetReturningUserBait) {
                // 回归活动开启
                startReturnActivity(lossDays, targetReturningUserBait,
                        returningUserConf.getId(), returningUserConf.getBaitsList().indexOf(targetReturningUserBait));
            }
        } catch (Exception e) {
            LOGGER.error("player:{} tryStartReturnActivity exception", player.getUid(), e);
        }
    }

    /**
     * @param defaultActivityId 默认活动ID，保底
     * @param restrictedActivityIdList 待判断ABTest参数的活动列表
     * @return 满足条件的活动ID，若从列表中查询不到合适的活动ID，则返回默认
     */
    private int getSatisfiedActivityId(int defaultActivityId, List<Integer> restrictedActivityIdList) {
        // 兼容M9
        if (!player.isRobot() && !VersionUtil.checkClientVersion("1.2.100.1", "", player.getClientVersion64())) {
            return defaultActivityId;
        }
        int activityABTestGroupId = player.getAbTestMgr().getTABTestGroupId(CsPlayer.ABTestType.ABTT_RETURN_ACTIVITY);
        for (Integer activityId : restrictedActivityIdList) {
            ResActivity.ActivityMainConfig mainConfig = ActivityMainConfig.getInstance().get(activityId);
            if (mainConfig == null) {
                continue;
            }
            if (mainConfig.getAbtestGroupListList().isEmpty() ||
                    mainConfig.getAbtestGroupListList().contains(activityABTestGroupId)) {
                return mainConfig.getId();
            }
        }
        return defaultActivityId;
    }

    /**
     * @param defaultId 默认的活动ID
     * @param candidateIds 根据不同回流活跃度划分的活动列表
     * @return 满足条件的活动ID，若按照当前用户活跃度与候选列表不匹配，则返回默认
     */
    private int getSatisfiedActivityIdByActiveLevel(int defaultId, List<Integer> candidateIds) {
        int activeLevel = getPlayerReturnActivity().getActiveLevel();
        LOGGER.debug("activeLevel:{} defaultId:{} candidateIds:{}", activeLevel, defaultId, candidateIds);
        return activeLevel < candidateIds.size() ? candidateIds.get(activeLevel) : defaultId;
    }

    /**
     * 真正的开启回流接口，GM也会走此接口
     *
     * @param lossDays 流失天数
     * @param returningUserBait 回流具体档位配置
     * @param confId 回流配置ID
     * @param baitIndex 回流档位编号
     */
    public void startReturnActivity(long lossDays, ReturningUserBait returningUserBait, int confId, int baitIndex) {
        if (isReturnActivityActive()) {
            return;
        }

        long curSec = Framework.currentTimeSec();
        long curDayBeginSec = DateUtils.getDayBeginTimeSec(curSec);
        long EndSec = curDayBeginSec + TimeUnit.DAYS.toSeconds(returningUserBait.getDurationDays()) - 1;
        long privilegeEndSec = curDayBeginSec + TimeUnit.DAYS.toSeconds(returningUserBait.getPrivilegeDays()) - 1;

        getPlayerReturnActivity().setUserConfId(confId);
        getPlayerReturnActivity().setUserBaitIndex(baitIndex);
        getPlayerReturnActivity().setBeginTime(curSec);
        getPlayerReturnActivity().setEndTime(EndSec);
        getPlayerReturnActivity().setLossDays((int) lossDays);
        getPlayerReturnActivity().setPrivilegeEndTime(privilegeEndSec);
        getPlayerReturnActivity().setOptionalRewardIndex(-1);

        LOGGER.info("player:{} StartReturnActivity confId:{} baitIndex:{} curSec:{} EndSec:{} lossDays:{}",
                player.getUid(), confId, baitIndex, curSec, EndSec, lossDays);

        // 设置publicInfo中的回归标识，标识跟随特权逻辑，特权消失时需置false
        player.getUserAttr().getPlayerPublicProfileInfo().setReturning(true);
        player.getUserAttr().getPlayerPublicProfileInfo().setReturnExpiredSec(privilegeEndSec);

        // 点燃友谊之火任务切换
        ReturningDiffersActivity returningDiffersActivity = player.getActivityManager()
                .getRunningActivityByActivityType(ActivityType.ATReturningDiffers, ReturningDiffersActivity.class);
        if (returningDiffersActivity != null) {
            returningDiffersActivity.refreshActivityTask();
            LOGGER.info("player:{} refresh returningDiffersActivity, activityId:{}", player.getUid(),
                    returningDiffersActivity.getActivityMainConfig().getId());
        }

        // 回归开始清理相应的道具限制
        for (int itemId : returningUserBait.getResetLimitItemListList()) {
            player.getUserAttr().removeItemPackageLimit(itemId);
        }

        // 添加回归签到活动
        int signInActivityId = getSatisfiedActivityId(returningUserBait.getSignInActivityId(),
                returningUserBait.getRestrictedSignInActivityIdsList());
        if (signInActivityId != 0) {
            getPlayerReturnActivity().setSignInActivityId(signInActivityId);
            player.getActivityManager().forceAddActivity(signInActivityId);
            LOGGER.info("player:{} forceAddActivity SignInActivity:{}", player.getUid(), signInActivityId);
        }

        // 添加回归特权活动
        int privilegeActivityId = getSatisfiedActivityId(returningUserBait.getPrivilegeActivityId(),
                returningUserBait.getRestrictedPrivilegeActivityIdsList());
        if (privilegeActivityId != 0) {
            getPlayerReturnActivity().setPrivilegeActivityId(privilegeActivityId);
            player.getActivityManager().forceAddActivity(privilegeActivityId);
            LOGGER.info("player:{} forceAddActivity PrivilegeActivity:{}", player.getUid(), privilegeActivityId);
        }

        // 添加回归任务活动
        int taskActivityId = getSatisfiedActivityId(returningUserBait.getTaskActivityId(),
                returningUserBait.getRestrictedTaskActivityIdList());
        if (taskActivityId != 0) {
            getPlayerReturnActivity().setTaskActivityId(taskActivityId);
            player.getActivityManager().forceAddActivity(taskActivityId);
            LOGGER.info("player:{} forceAddActivity TaskActivity:{}", player.getUid(), taskActivityId);
        }

        // 添加回归付费签到活动
        if (returningUserBait.getChargeSignInActivityId() != 0) {
            getPlayerReturnActivity().setChargeSignInActivityId(returningUserBait.getChargeSignInActivityId());
            player.getActivityManager().forceAddActivity(getPlayerReturnActivity().getChargeSignInActivityId());
            LOGGER.info("player:{} forceAddActivity chargeSignInActivity:{}", player.getUid(),
                    getPlayerReturnActivity().getChargeSignInActivityId());
        }

        // 添加回归付费礼包活动
        if (returningUserBait.getChargeGiftActivityId() != 0) {
            getPlayerReturnActivity().setChargeGiftActivityId(returningUserBait.getChargeGiftActivityId());
            player.getActivityManager().forceAddActivity(getPlayerReturnActivity().getChargeGiftActivityId());
            LOGGER.info("player:{} forceAddActivity chargeGiftActivity:{}", player.getUid(),
                    getPlayerReturnActivity().getChargeGiftActivityId());
        }

        int dailyRewardActivityId = getSatisfiedActivityIdByActiveLevel(returningUserBait.getDailyRewardActivityId(),
                returningUserBait.getDailyRewardActiveActivityIdsList());
        if (dailyRewardActivityId != 0) {
            getPlayerReturnActivity().getDailyReward().setActivityId(dailyRewardActivityId);
            player.getActivityManager().forceAddActivity(getPlayerReturnActivity().getDailyReward().getActivityId());
            LOGGER.info("player:{} forceAddActivity DailyRewardActivity:{}", player.getUid(),
                    getPlayerReturnActivity().getDailyReward().getActivityId());
        }

        // 添加首局活动
        if (returningUserBait.getFirstGameTaskActivityId() != 0) {
            getPlayerReturnActivity().setFirstGameActivityId(returningUserBait.getFirstGameTaskActivityId());
            player.getActivityManager().forceAddActivity(getPlayerReturnActivity().getFirstGameActivityId());
            LOGGER.info("player:{} forceAddActivity firstGameTaskActivityId:{}", player.getUid(),
                    getPlayerReturnActivity().getFirstGameActivityId());
        }

        // 添加回归手册活动
        if (returningUserBait.getReturnBookActivityId() != 0) {
            getPlayerReturnActivity().setReturnBookActivityId(returningUserBait.getReturnBookActivityId());
            player.getActivityManager().forceAddActivity(returningUserBait.getReturnBookActivityId());
            LOGGER.info("player:{} forceAddActivity returnBookActivityId:{}", player.getUid(),
                    getPlayerReturnActivity().getReturnBookActivityId());
        }

        // 添加登录送全套活动
        if (returningUserBait.getLoginFreeActivityId() != 0) {
            getPlayerReturnActivity().setLoginFreeActivityId(returningUserBait.getLoginFreeActivityId());
            player.getActivityManager().forceAddActivity(returningUserBait.getLoginFreeActivityId());
            LOGGER.info("player:{} forceAddActivity loginFreeActivityId:{}", player.getUid(),
                    getPlayerReturnActivity().getLoginFreeActivityId());
        }
        // 重置回流问卷状态
        ReturningUserConstsInfo cfgVal = getResConstData(ReturningUserConstsEnum.RECE_ReturnInvestId);
        QAInvestInfo returnQaInvest = player.getUserAttr().getQaInvest(cfgVal.getValue());
        if (returnQaInvest != null) {
            returnQaInvest.clear();
        }

        resetRecommendInfo(true);
        initOptionalRewardTask(returningUserBait);
        asyncAddFarmBuffs();

        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.ReturnPrivilegeChange);
        player.getFriendManager().addChangeField(PlayerPublicInfoField.RETURNING);
        player.getFriendManager().addChangeField(PlayerPublicInfoField.RETURN_EXPIRED_SEC);

        sendReturningActivityStartNtf();
    }

    /**
     * 发送回流开始Ntf，与拍脸图功能对应，一次性大奖领取后不再发送Ntf，因为不再需要拍脸图
     */
    private void sendReturningActivityStartNtf() {
        if (!isReturnActivityActive()) {
            LOGGER.debug("player:{} isReturnActivityActive:{} isRewardImmediateAvailed:{}", player.getUid(),
                    isReturnActivityActive(), isRewardImmediateAvailed());
            return;
        }
        // 版本6改为每次登录都发ntf
        ReturningUserConstsInfo cfgVal = getResConstData(RUCE_DefaultReturningUserConfId);
        if (!isRewardImmediateAvailed() && getPlayerReturnActivity().getUserConfId() < ReturningUserConfVersion6) {
            LOGGER.debug("player:{} isReturnActivityActive:{} isRewardImmediateAvailed:{} userConfId:{} congVal:{}",
                    player.getUid(), isReturnActivityActive(), isRewardImmediateAvailed(),
                    getPlayerReturnActivity().getUserConfId(), cfgVal.getValue());
            return;
        }

        CsActivity.ReturningActiviyStartNtf.Builder ntf = CsActivity.ReturningActiviyStartNtf.newBuilder();
        if (isRewardImmediateAvailed()) { // 一次性奖励未领取才组织数据
            ReturningUserBait returningUserBait = getResReturningUserBait(getPlayerReturnActivity().getUserConfId(),
                    getPlayerReturnActivity().getUserBaitIndex());
            if (returningUserBait == null) {
                LOGGER.error("player:{} getReturningUserBaitByIndex failed UserConfId:{} UserBaitIndex:{}",
                        player.getUid(),
                        getPlayerReturnActivity().getUserConfId(), getPlayerReturnActivity().getUserBaitIndex());
                return;
            }

            if (getPlayerReturnActivity().getUserConfId() < ReturningUserConfVersion6) {
                // 新需求，回流一次性奖励会单独配置 和回归配置分开，会根据推荐玩法、活跃度来计算
                List<ResCommon.Item> itemList = getReturnPushFaceRewardByReturningUserBait(returningUserBait);
                if (CollectionUtil.isNullOrEmpty(itemList)) {
                    LOGGER.error("player:{} getReturnPushFaceReward failed ActiveLevel:{} returningUserBait:{}",
                            player.getUid(),
                            getPlayerReturnActivity().getActiveLevel(), returningUserBait);
                    return;
                }
                for (ResCommon.Item item : itemList) {
                    ntf.addItemId(item.getItemId());
                    ntf.addItemNum(item.getItemNum());
                }
            } else {
                // 发开始消息
                for (ResCommon.Item item : returningUserBait.getReward().getItemsImmediateList()) {
                    ntf.addItemId(item.getItemId());
                    ntf.addItemNum(item.getItemNum());
                }
                fillOptionalReward(returningUserBait, ntf);
            }
        }

        player.sendNtfMsg(MsgTypes.MSG_TYPE_RETURNINGACTIVIYSTARTNTF, ntf);
    }

    private void fillOptionalReward(ReturningUserBait returningUserBait,
            CsActivity.ReturningActiviyStartNtf.Builder ntf) {
        ReturningUserConstsInfo cfgVal = getResConstData(RUCE_OptionalRewardShowCount);
        if (null == cfgVal) {
            return;
        }
        for (int index = 0; index < returningUserBait.getReward().getOptionalRewardCount(); index++) {
            int rewardId = returningUserBait.getReward().getOptionalReward(index);
            var rewardInfo = TaskOptionalRewardData.getInstance().get(rewardId);
            if (rewardInfo == null) {
                continue;
            }
            boolean alreadyExist = false;
            for (ResCommon.RewardConf rewardConf : rewardInfo.getRewardInfoList()) {
                if (player.getBagManager().getItemNumByItemIdIgnoreTemp(rewardConf.getItemId()) > 0) {
                    alreadyExist = true;
                    break;
                }
            }
            if (alreadyExist) {
                continue;
            }
            ntf.addOptionalReward(CsActivity.ReturningOptionalReward.newBuilder().setIndex(index)
                    .addAllRewardInfo(rewardInfo.getRewardInfoList()));
            if (getPlayerReturnActivity().getOptionalRewardIndex() == -1) { // 默认选择装扮
                getPlayerReturnActivity().setOptionalRewardIndex(index);
            }
            if (ntf.getOptionalRewardCount() == cfgVal.getValue()) {
                break;
            }
        }
        if (ntf.getOptionalRewardCount() < cfgVal.getValue()) {
            for (int index = 0; index < returningUserBait.getReward().getOptionalRewardCount(); index++) {
                int rewardId = returningUserBait.getReward().getOptionalReward(index);
                var rewardInfo = TaskOptionalRewardData.getInstance().get(rewardId);
                if (rewardInfo == null) {
                    continue;
                }
                ntf.addOptionalReward(CsActivity.ReturningOptionalReward.newBuilder().setIndex(index)
                        .addAllRewardInfo(rewardInfo.getRewardInfoList()));
                if (getPlayerReturnActivity().getOptionalRewardIndex() == -1) { // 默认选择装扮
                    getPlayerReturnActivity().setOptionalRewardIndex(index);
                }
                if (ntf.getOptionalRewardCount() == cfgVal.getValue()) {
                    break;
                }
            }
        }
    }

    private List<ResCommon.Item> getReturnPushFaceRewardByReturningUserBait(ReturningUserBait returningUserBait){
        // 新需求，回流一次性奖励会单独配置 和回归配置分开，会根据推荐玩法、活跃度来计算
        java.util.Collection<Integer> matchTypesList =  player.getUserAttr().getAlgoRecommendMatchInfo().getMatchTypesList();
        ResReturningUser.ReturnPushFaceSettingData data = getReturnPushFaceSettingDataV2(getPlayerReturnActivity().getActiveLevel(), matchTypesList);
        if (data == null) {
            LOGGER.error("player:{} getReturnPushFaceSettingDataV2 failed ActiveLevel:{} matchTypesList:{}", player.getUid(),
                    getPlayerReturnActivity().getActiveLevel(), matchTypesList);
            return null;
        }
        List<ResCommon.Item> itemList = null;
        // 如果匹配到了玩法，需要根据档位获取奖品配置
        if (data.getMatchType() > 0){
            int barIndex = ReturningUserConfData.getInstance().getBarIndex(returningUserBait.getBar());
            itemList = ReturnPushFaceReward.getInstance().get(data.getItemConfigList(barIndex)).getRewardsList();
        } else {
            itemList = ReturnPushFaceReward.getInstance().get(data.getItemConfigList(0)).getRewardsList();
        }
        return itemList;
    }

    /**
     * 回流结束Ntf
     */
    private void sendReturnActivityEndNtf() {
        player.getUserAttrMgr().collectAndSyncDirtyToClient();

        CsActivity.ReturnActivityEndNtf.Builder ntf = CsActivity.ReturnActivityEndNtf.newBuilder();
        player.sendNtfMsg(MsgTypes.MSG_TYPE_RETURNACTIVITYENDNTF, ntf);
    }

    /**
     * 尝试关闭回流
     *
     * @param onLogin 是否在登录时触发
     */
    private void tryReturnActivityEnd(boolean onLogin) {
        if (!isReturnActivityActive()) {
            LOGGER.debug("player:{} isReturnActivityActive equal to false", player.getUid());
            return;
        }

        long curSec = Framework.currentTimeSec();
        ReturnActivity returnActivity = player.getUserAttr().getReturningInfo().getReturnActivity();
        if (returnActivity.getEndTime() > curSec) {
            return;
        }

        stopReturnActivity(false, onLogin);
    }

    /**
     * 移除回流活动
     *
     * @param activityId 回流活动Id
     * @param syncDeleteTasks 是否同步移除Task (登录触发时可同步移除Task，midnight过期时同步移除可能会有风险)
     */
    private void removeReturnActivity(int activityId, boolean syncDeleteTasks) {
        if (activityId == 0) {
            return;
        }
        ReturningTaskActivity activity = player.getActivityManager()
                .getRunningActivity(activityId, ReturningTaskActivity.class);
        if (activity != null) {
            activity.setSyncDeleteTaskOnTaskRemove(syncDeleteTasks);
        }
        player.getActivityManager().removeActivity(activityId);
        LOGGER.info("player:{} removeReturnActivity:{}", player.getUid(), activityId);
    }

    /**
     * 移除回流每日刷新任务组活动
     *
     * @param activityId 活动ID
     * @param syncDeleteTasks 是否同步移除Task (登录触发时可同步移除Task，midnight过期时同步移除可能会有风险)
     */
    private void removeReturnDailyTaskGroupActivity(int activityId, boolean syncDeleteTasks) {
        if (activityId == 0) {
            return;
        }
        ReturningTaskDailyGroupActivity activity = player.getActivityManager()
                .getRunningActivity(activityId, ReturningTaskDailyGroupActivity.class);
        if (activity != null) {
            activity.setSyncDeleteTaskOnTaskRemove(syncDeleteTasks);
        }
        player.getActivityManager().removeActivity(activityId);
        LOGGER.info("player:{} removeReturnDailyTaskGroupActivity:{}", player.getUid(), activityId);
    }


    /**
     * 真正关闭回流，清理一系列活动，清理大部分属性字段，保留一小部分属性字段
     *
     * @param clearEndTime 是否清理结束时间
     * @param syncDeleteActivityTasks 是否同步移除活动所牵引的任务
     */
    public void stopReturnActivity(boolean clearEndTime, boolean syncDeleteActivityTasks) {
        ReturnActivity returnActivity = player.getUserAttr().getReturningInfo().getReturnActivity();
        // 清理回归活动数据，过期时间保留用作计算触发CD
        LOGGER.info("player:{} beginSec:{} endSec:{} clearEndTime:{}", player.getUid(), returnActivity.getBeginTime(),
                returnActivity.getEndTime(), clearEndTime);

        // 移除回归签到活动, 属性字段下面会统一清理
        removeReturnActivity(returnActivity.getSignInActivityId(), syncDeleteActivityTasks);
        // 移除回归特权活动, 属性字段下面会统一清理
        removeReturnActivity(returnActivity.getPrivilegeActivityId(), syncDeleteActivityTasks);
        // 移除回归任务活动, 属性字段下面会统一清理
        removeReturnActivity(returnActivity.getTaskActivityId(), syncDeleteActivityTasks);
        // 移除付费签到活动，属性字段下面会统一清理
        removeReturnActivity(returnActivity.getChargeSignInActivityId(), syncDeleteActivityTasks);
        // 移除付费礼包活动，属性字段下面会统一清理
        removeReturnActivity(returnActivity.getChargeGiftActivityId(), syncDeleteActivityTasks);
        // 移除首局福利
        removeReturnActivity(returnActivity.getFirstGameActivityId(), syncDeleteActivityTasks);
        // 移除回归手册活动
        removeReturnActivity(returnActivity.getReturnBookActivityId(), syncDeleteActivityTasks);
        // 移除登录送全套活动
        removeReturnActivity(returnActivity.getLoginFreeActivityId(), syncDeleteActivityTasks);
        // 移除每日福利活动，属性字段下面会统一清理
        removeReturnDailyTaskGroupActivity(returnActivity.getDailyReward().getActivityId(), syncDeleteActivityTasks);
        // 移除开局送配饰任务
        removeStartMatchTaskGroup();
        // 保底:移除特权期间的农场buff
        asyncRemoveFarmBuffs();

        // 清理ReturnActivity的数据，非GM情况下，不清理 lastEndTime不清理保证CD期内不触发
        long lastEndTime = Math.min(Framework.currentTimeSec(), returnActivity.getEndTime());
        int fireGotNumWeekly = returnActivity.getPrivilege().getFireGotNumWeekly();
        int activeLevel = returnActivity.getActiveLevel();
        returnActivity.clear();
        returnActivity.getPrivilege().setFireGotNumWeekly(fireGotNumWeekly);
        if (!clearEndTime) {
            returnActivity.setEndTime(lastEndTime);
        }
        returnActivity.setActiveLevel(activeLevel);

        player.getUserAttr().getPlayerPublicProfileInfo().setReturning(false);
        player.getUserAttr().getPlayerPublicProfileInfo().setReturnExpiredSec(0);
        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.ReturnPrivilegeChange);

        player.getFriendManager().addChangeField(PlayerPublicInfoField.RETURNING);
        player.getFriendManager().addChangeField(PlayerPublicInfoField.RETURN_EXPIRED_SEC);

        sendReturnActivityEndNtf();
    }

    /**
     * 发放回流一次性奖励
     *
     * @return 反正真正发放的奖励列表
     */
    public List<ResCommon.Item> sendReturnActivityReward(int optionalRewardIndex) {
        if (!isReturnActivityActive()) {
            NKErrorCode.UnknownError.throwError("SendReturnActivityReward not in retuning state");
        }

        ReturnActivity returnActivity = player.getUserAttr().getReturningInfo().getReturnActivity();
        if (returnActivity.getCustomGift()) {
            NKErrorCode.UnknownError.throwError("SendReturnActivityReward already get retuning reward");
        }

        ReturningUserBait returningUserBait = getResReturningUserBait(returnActivity.getUserConfId(),
                returnActivity.getUserBaitIndex());
        if (null == returningUserBait) {
            LOGGER.error("player:{} getReturningUserBaitByIndex failed confId:{} baitIndex:{}", player.getUid(),
                    returnActivity.getUserConfId(), returnActivity.getUserBaitIndex());
            NKErrorCode.UnknownError.throwError("SendReturnActivityReward getReturningUserBaitByIndex failed");
            return null;
        }
        List<ResCommon.Item> itemList = new ArrayList<>();
        if (returnActivity.getUserConfId() < ReturningUserConfVersion6) {
            // 根据推荐玩法、活跃度来计算的奖励
            itemList = getReturnPushFaceRewardByReturningUserBait(returningUserBait);
            if (CollectionUtil.isNullOrEmpty(itemList)) {
                LOGGER.error("player:{} getReturnPushFaceReward failed ActiveLevel:{} returningUserBait:{}",
                        player.getUid(),
                        getPlayerReturnActivity().getActiveLevel(), returningUserBait);
                NKErrorCode.UnknownError.throwError(
                        "SendReturnActivityReward getReturnPushFaceRewardByReturningUserBait failed");
                return null;
            }
        } else {
            // 固定奖励
            itemList = returningUserBait.getReward().getItemsImmediateList();
        }
        ChangedItems changedItems = new ChangedItems(itemList,
                ItemChangeReason.ICR_ReturningActivityCustomReward.getNumber(), "");

        if (returnActivity.getUserConfId() >= ReturningUserConfVersion6) {
            // 选择的奖励
            if (optionalRewardIndex < returningUserBait.getReward().getOptionalRewardCount()) {
                int rewardId = returningUserBait.getReward().getOptionalReward(optionalRewardIndex);
                var rewardInfo = TaskOptionalRewardData.getInstance().get(rewardId);
                if (rewardInfo != null) {
                    for (var optionalReward : rewardInfo.getRewardInfoList()) {
                        ItemInfo.Builder itemInfo = ItemInfo.newBuilder().setItemId(optionalReward.getItemId())
                                .setItemNum(optionalReward.getItemNum());
                        if (optionalReward.getValidPeriod() > 0) {
                            itemInfo.setExpireType(ItemExpireType.IET_RELATIVE_VALUE).setExpireTimeMs(
                                    optionalReward.getValidPeriod() * DateUtils.ONE_DAY_MILLIS
                                            + DateUtils.currentTimeMillis());
                        } else if (optionalReward.getExpireTimestamps().getSeconds() > 0) {
                            itemInfo.setExpireType(ItemExpireType.IET_ABSOLUTE_VALUE)
                                    .setExpireTimeMs(optionalReward.getExpireTimestamps().getSeconds());
                        }
                        changedItems.mergeItemInfo(itemInfo.build());
                    }
                }
            }
        }
        // 检查道具版本
        player.getBagManager().preCheckAddItemsVersion(changedItems)
                .throwErrorIfNotOk("SendReturnActivityReward preCheckAddItemsVersion err");
        // 检查背包空位
        if (player.getItemManager().getAvailableGrids() < changedItems.getChangeItems().size()) {
            NKErrorCode.CheckBagGridNotEnough.throwError("SendReturnActivityReward bag grid not enough");
        }

        // 置标记
        returnActivity.setCustomGift(true);
        returnActivity.setOptionalRewardIndex(optionalRewardIndex);
        // 加道具
        NKPair<NKErrorCode, ItemChangeDetails> ret = player.getBagManager()
                .AddItems2(changedItems);
        if (ret.getKey() != NKErrorCode.OK) {
            LOGGER.error("SendReturnActivityReward AddItems2 failed:{}", ret.getKey());
            ret.getKey().throwError("SendReturnActivityReward AddItems2 failed");
        }

        return itemList;
    }

    /**
     * 配合is_force_returning_enable的开关，仅做测试使用
     */
    public void forceStartReturnActivity() {
        if (isReturnActivityActive()) {
            return;
        }

        ReturningUserConstsInfo cfgVal = getResConstData(RUCE_DefaultReturningUserConfId);
        if (null == cfgVal) {
            LOGGER.error("player:{} default RUCE_DefaultReturningUserConfId not exists", player.getUid());
            return;
        }

        // 测试使用配置第一行
        ReturningUserBait returningUserBait = getResReturningUserBait(cfgVal.getValue(), 0);

        if (null == returningUserBait) {
            LOGGER.error("player:{} getReturningUserBaitByIndex index:{} failed", player.getUid(), 0);
            return;
        }

        player.getReturnActivityManager().startReturnActivity(7, returningUserBait, cfgVal.getValue(), 0);
    }

    /**
     * 购买付费签到活动门票
     */
    public NKErrorCode buyChargeSignInActivityTicket() {
        if (!isReturnActivityActive()) {
            LOGGER.error("ReturnActivityAlreadyDisable player:{}", player.getUid());
            return NKErrorCode.ReturnActivityAlreadyDisable;
        }

        ReturnActivity returnActivity = player.getUserAttr().getReturningInfo().getReturnActivity();
        if (returnActivity.getChargeSignInActivityId() == 0) {
            LOGGER.error("ReturnActivityChargeSignInActivityNotExists player:{}", player.getUid());
            return NKErrorCode.ReturnActivityChargeSignInActivityNotExists;
        }

        if (returnActivity.getChargeSignInTicketGot()) {
            LOGGER.error("ReturnActivityChargeSignInTicketAlreadyGot player:{}", player.getUid());
            return NKErrorCode.ReturnActivityChargeSignInTicketAlreadyGot;
        }

        ReturningUserBait returningUserBait = getResReturningUserBait(
                returnActivity.getUserConfId(), returnActivity.getUserBaitIndex());
        if (null == returningUserBait) {
            LOGGER.error("getReturningUserBaitByIndex failed player:{} UserConfId:{} BaitIndex:{}",
                    player.getUid(), returnActivity.getUserConfId(), returnActivity.getUserBaitIndex());
            return NKErrorCode.ReturnActivityResUserBaitNotExists;
        }

        if (returningUserBait.getChargeSignInTicket().isEmpty()) {
            LOGGER.error("ReturnActivityChargeSignInTicketCfgNotExists player:{}", player.getUid());
            return NKErrorCode.ReturnActivityChargeSignInTicketCfgNotExists;
        }

        if (isBaseTaskActivityTaskInvalid(returnActivity.getChargeSignInActivityId())) {
            LOGGER.error("isBaseTaskActivityTaskInvalid player:{} activityId:{}", player.getUid(),
                    returnActivity.getChargeSignInActivityId());
            return NKErrorCode.ReturnActivityCommon;
        }

        try {
            player.getPlayerMoneyMgr().midasDirectBuy(returningUserBait.getChargeSignInTicket(),
                    DeliverGoodsMetaData.newBuilder().setActivityId(returnActivity.getChargeSignInActivityId()),
                    ItemChangeReason.ICR_ReturnChargeSignInTicket);
        } catch (Exception e) {
            LOGGER.error("buyChargeSignInActivityTicket failed, player:{} midasId:{}", player.getUid(),
                    returnActivity.getChargeSignInActivityId(), e);
            NKErrorCode errorCode = (e instanceof IEnumedException) ?
                    (NKErrorCode) ((IEnumedException) e).getEnumErrCode() : NKErrorCode.UnknownError;
            NKErrorCode.MidasBuyGoodsFailed.throwError(
                    "buyChargeSignInActivityTicket failed to buy midas product reason:{}", errorCode);
        }

        return NKErrorCode.OK;
    }

    /**
     * 确认购买签到活动门票
     */
    public NKErrorCode confirmBuyChargeSignInActivityTicket(MidasProductParam productParam, DeliverGoodsMetaData metaData) {
        if (!player.getReturnActivityManager().isReturnActivityActive()) {
            LOGGER.error("ReturnActivityAlreadyDisable player:{}", player.getUid());
            return NKErrorCode.ReturnActivityAlreadyDisable;
        }

        ReturnActivity returnActivity = player.getUserAttr().getReturningInfo().getReturnActivity();
        if (returnActivity.getChargeSignInActivityId() == 0) {
            LOGGER.error("ReturnActivityChargeSignInActivityNotExists player:{}", player.getUid());
            return NKErrorCode.ReturnActivityChargeSignInActivityNotExists;
        }

        ReturningUserBait returningUserBait = getResReturningUserBait(returnActivity.getUserConfId(),
                returnActivity.getUserBaitIndex());
        if (null == returningUserBait) {
            LOGGER.error("getReturningUserBaitByIndex failed  player:{} UserConfId:{} BaitIndex:{}",
                    player.getUid(), returnActivity.getUserConfId(), returnActivity.getUserBaitIndex());
            return NKErrorCode.ReturnActivityResUserBaitNotExists;
        }

        if (!returningUserBait.getChargeSignInTicket().equalsIgnoreCase(productParam.getProductId())) {
            LOGGER.error("ReturnActivityChargeSignInTicketCfgNotExists player:{} buy midas:{} but cfgMidas:{}",
                    player.getUid(), productParam.getProductId(), returningUserBait.getChargeSignInTicket());
            return NKErrorCode.ReturnActivityChargeSignInTicketCfgNotExists;
        }

        if (returnActivity.getChargeSignInTicketGot()) {
            LOGGER.error("ReturnActivityChargeSignInTicketRepeat player:{} midas:{}", player.getUid(),
                    productParam.getProductId());
            // 重复购买记录midas限次TLog
            player.getPlayerMoneyMgr().sendMidasDeliverFailCauseCountLimitFlow(productParam, metaData);
            return NKErrorCode.ReturnActivityChargeSignInTicketRepeat;
        }

        returnActivity.setChargeSignInTicketGot(true);
        LOGGER.info("confirmBuyChargeSignInActivityTicket player:{} midas:{}", player.getUid(),
                productParam.getProductId());

        // 抛出玩家购买回归付费签到活动门票成功的事件
        new PlayerGotReturnActivityChargeSignInTicketEvent(player).dispatch();

        player.sendNtfMsg(MsgTypes.MSG_TYPE_BUYRETURNCHARGESIGNINACTIVITYTICKETNTF,
                BuyReturnChargeSignInActivityTicketNtf.newBuilder()
                        .setActivityId(returnActivity.getChargeSignInActivityId()));
        return NKErrorCode.OK;
    }

    /**
     * 特定条件下对局结束添加友谊火种道具
     */
    public void addFriendshipFireAfterRound(ChangedItems items, CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder) {
        ReturningUserConstsInfo userCfgVal = getResConstData(RUCE_DefaultReturningUserConfId);
        ReturningUserConstsInfo itemCfgVal = getResConstData(RUCE_FriendshipFireItemCfgId);
        ReturningUserConstsInfo limitWeeklyCfgVal = getResConstData(RUCE_FriendshipFireGetNumLimitWeekly);
        ReturningUserConstsInfo numPerRoundCfgVal = getResConstData(RUCE_FriendshipFireGetNumPerRound);
        if (userCfgVal == null || itemCfgVal == null || limitWeeklyCfgVal == null || numPerRoundCfgVal == null) {
            return;
        }

        int itemCfgId = itemCfgVal.getValue();
        int oldFireNum = getPlayerReturnActivity().getPrivilege().getFireGotNumWeekly();
        int addFireNum = Integer.min(numPerRoundCfgVal.getValue(), limitWeeklyCfgVal.getValue() - oldFireNum);
        if (userCfgVal.getValue() < 2 || addFireNum <= 0) {
            return;
        }
        
        items.mergeItemInfo(itemCfgId, addFireNum);
        ntfBuilder.addItemList(LevelDropItemInfo.newBuilder().setItemId(itemCfgId).setItemCount(addFireNum).build());
    }

    public long fixFriendShipFireItemNum(long hopeNum) {
        ReturningUserConstsInfo limitWeeklyCfgVal = getResConstData(RUCE_FriendshipFireGetNumLimitWeekly);
        if (limitWeeklyCfgVal == null) {
            return 0;
        }
        int oldFireNum = getPlayerReturnActivity().getPrivilege().getFireGotNumWeekly();
        long addFireNum = Long.min(hopeNum, limitWeeklyCfgVal.getValue() - oldFireNum);
        getPlayerReturnActivity().getPrivilege().setFireGotNumWeekly(oldFireNum + (int) addFireNum);
        return addFireNum;
    }

    public static int getFriendShipFireItemId() {
        ReturningUserConstsInfo itemCfgVal = getResConstData(RUCE_FriendshipFireItemCfgId);
        if (itemCfgVal == null) {
            return -1;
        }
        return itemCfgVal.getValue();
    }

    /**
     * 购买付费礼物门票
     */
    public NKErrorCode buyChargeGiftTicket() {
        if (!isPrivilegeValid()) {
            LOGGER.error("player:{} isPrivilegeValid:0 ", player.getUid());
            return NKErrorCode.ReturnActivityCommon;
        }

        if (getPlayerReturnActivity().getChargeGiftActivityId() == 0) {
            LOGGER.error("player:{} ReturnActivity.ChargeGiftActivity dont exist", player.getUid());
            return NKErrorCode.ReturnActivityCommon;
        }

        if (getPlayerReturnActivity().getChargeGiftTicketGot()) {
            LOGGER.error("player:{} ReturnActivity.ChargeGiftTicketAlreadyGot ", player.getUid());
            return NKErrorCode.ReturnActivityCommon;
        }

        ReturningUserBait returningUserBait = getResReturningUserBait(getPlayerReturnActivity().getUserConfId(),
                getPlayerReturnActivity().getUserBaitIndex());
        if (returningUserBait == null) {
            LOGGER.error("getReturningUserBaitByIndex failed player:{} UserConfId:{} BaitIndex:{}", player.getUid(),
                    getPlayerReturnActivity().getUserConfId(), getPlayerReturnActivity().getUserBaitIndex());
            return NKErrorCode.ReturnActivityResUserBaitNotExists;
        }

        if (returningUserBait.getChargeGiftTicket().isEmpty()) {
            LOGGER.error("player:{} ReturnActivity.ChargeGiftTicketCfgNotExists ", player.getUid());
            return NKErrorCode.ReturnActivityCommon;
        }

        if (isBaseTaskActivityTaskInvalid(getPlayerReturnActivity().getChargeGiftActivityId())) {
            LOGGER.error("isBaseTaskActivityTaskInvalid player:{} activityId:{}", player.getUid(),
                    getPlayerReturnActivity().getChargeGiftActivityId());
            return NKErrorCode.ReturnActivityCommon;
        }

        try {
            player.getPlayerMoneyMgr().midasDirectBuy(returningUserBait.getChargeGiftTicket(),
                    DeliverGoodsMetaData.newBuilder()
                            .setActivityId(getPlayerReturnActivity().getChargeGiftActivityId()),
                    ItemChangeReason.ICR_ReturnChargeSignInTicket);
        } catch (Exception e) {
            LOGGER.error("buyChargeGiftTicket failed, player:{} midasId:{}", player.getUid(),
                    getPlayerReturnActivity().getChargeGiftActivityId(), e);
            NKErrorCode errorCode = (e instanceof IEnumedException) ?
                    (NKErrorCode) ((IEnumedException) e).getEnumErrCode() : NKErrorCode.UnknownError;
            NKErrorCode.MidasBuyGoodsFailed.throwError(
                    "buyChargeGiftTicket failed to buy midas product reason:{}", errorCode);
        }

        return NKErrorCode.OK;
    }

    /**
     * 确认购买付费礼物门票
     */
    public NKErrorCode confirmBuyChargeGiftTicket(MidasProductParam productParam, DeliverGoodsMetaData metaData) {
        if (!isPrivilegeValid()) {
            LOGGER.error("player:{} isPrivilegeValid:0 ", player.getUid());
            return NKErrorCode.ReturnActivityCommon;
        }

        if (getPlayerReturnActivity().getChargeGiftActivityId() == 0) {
            LOGGER.error("player:{} ReturnActivity.ChargeGiftActivity dont exist", player.getUid());
            return NKErrorCode.ReturnActivityCommon;
        }

        ReturningUserBait returningUserBait = getResReturningUserBait(getPlayerReturnActivity().getUserConfId(),
                getPlayerReturnActivity().getUserBaitIndex());
        if (returningUserBait == null) {
            LOGGER.error("getReturningUserBaitByIndex failed  player:{} UserConfId:{} BaitIndex:{}", player.getUid(),
                    getPlayerReturnActivity().getUserConfId(), getPlayerReturnActivity().getUserBaitIndex());
            return NKErrorCode.ReturnActivityResUserBaitNotExists;
        }

        if (!returningUserBait.getChargeGiftTicket().equalsIgnoreCase(productParam.getProductId())) {
            LOGGER.error("player:{} buy midas:{} but cfgMidas:{}", player.getUid(), productParam.getProductId(),
                    returningUserBait.getChargeGiftTicket());
            return NKErrorCode.ReturnActivityCommon;
        }

        if (getPlayerReturnActivity().getChargeGiftTicketGot()) {
            LOGGER.error("ReturnActivityChargeGiftTicketRepeat player:{} midas:{}", player.getUid(),
                    productParam.getProductId());
            // 重复购买记录midas限次TLog
            player.getPlayerMoneyMgr().sendMidasDeliverFailCauseCountLimitFlow(productParam, metaData);
            return NKErrorCode.ReturnActivityCommon;
        }

        getPlayerReturnActivity().setChargeGiftTicketGot(true);
        LOGGER.info("confirmBuyChargeGiftTicket player:{} midas:{}", player.getUid(), productParam.getProductId());

        sendAttrChangeNtf();

        // 购买门票后重新刷新下任务，签到天数重新计数
        refreshBaseTaskActivity(getPlayerReturnActivity().getChargeGiftActivityId());

        // 抛出玩家购买回归付费签到活动门票成功的事件
        new PlayerGotReturnActivityChargeGiftTicketEvent(player).dispatch();

        // 不自动执行任务发奖
        // rewardActivityFirstTask(getPlayerReturnActivity().getChargeGiftActivityId());

        player.sendNtfMsg(MsgTypes.MSG_TYPE_RETURNACTIVITYBUYCHARGEGIFTTICKETNTF,
                ReturnActivityBuyChargeGiftTicketNtf.newBuilder());
        return NKErrorCode.OK;
    }

    /**
     * 初始化可自选任务奖励
     *
     * @param returningUserBait 回流档位配置
     */
    private void initOptionalRewardTask(ReturningUserBait returningUserBait) {
        var mapTaskInfo = new ReturnActivityRewardOptionalTask();
        for (var taskInfo : returningUserBait.getTaskOptionalRewardList()) {
            mapTaskInfo.setTaskId(taskInfo.getTaskId());
            for (var reward : taskInfo.getRewardsList()) {
                mapTaskInfo.addItemId(reward.getItemId());
                mapTaskInfo.addItemNum(reward.getItemNum());
            }
            getPlayerReturnActivity().putRewardOptionalTask(mapTaskInfo.getTaskId(), mapTaskInfo);
        }
    }

    /**
     * 重置回流玩法推荐
     */
    private void resetRecommendInfo(boolean isStart) {
        // V6版本不需要每天重置刷新
        if (!isStart && getPlayerReturnActivity().getUserConfId() >= ReturningUserConfVersion6) {
            return;
        }
        // 清理新内容推荐
        getPlayerReturnActivity().getExclusiveNewsRecommend().clear();

        // 尝试重新推荐新内容
        if (getPlayerReturnActivity().getUserConfId() < ReturningUserConfVersion6) {
            List<Integer> newsParamList = ReturningTabConfigData.getInstance()
                    .getTabConfigActiveLevelParamList("ReturnRecommend", getPlayerReturnActivity().getActiveLevel());
            newsParamList.forEach((resId) -> {
                if (ReturningRecommend2Data.getInstance().isTimeValid(resId)) {
                    getPlayerReturnActivity().getExclusiveNewsRecommend().add(resId);
                }
            });
            if (newsParamList.size() >= 2) {
                for (Integer matchType : player.getUserAttr().getAlgoRecommendMatchInfo().getMatchTypesList()) {
                    int resNewsIndex = ReturningRecommend2Data.getInstance().getResIdByMatchType(matchType);
                    if (resNewsIndex >= 0 && !newsParamList.contains(resNewsIndex)) {
                        getPlayerReturnActivity().getExclusiveNewsRecommend().set(1, resNewsIndex);
                        break;
                    }
                }
            } else {
                LOGGER.debug("newsParamList:{}", newsParamList);
            }
            // 清理每日福利推荐
            getPlayerReturnActivity().getDailyReward().clearExclusiveRecommend();
            getPlayerReturnActivity().getDailyReward().clearExclusiveRecommendExcept();

            // 尝试重新推荐每日福利
            List<Integer> dailyParamList = ReturningTabConfigData.getInstance().getTabConfigActiveLevelParamList(
                    "ReturnBenefits", getPlayerReturnActivity().getActiveLevel());
            if (!dailyParamList.isEmpty() && getPlayerReturnActivity().getDailyReward().getActivityId() != 0) {
                ReturnActivityDailyReward dailyReward = getPlayerReturnActivity().getDailyReward();
                dailyReward.getExclusiveRecommend().addAll(dailyParamList);
                dailyReward.getExclusiveRecommendExcept().addAll(dailyParamList);
                dailyReward.getExclusiveRecommendExcept().remove(dailyParamList.get(0));

                try {
                    freshRecommendDailyMatchType();
                } catch (Exception e) {
                    LOGGER.error("freshRecommendDailyMatchType failed player:{} {}", player.getUid(),
                            FunctionUtil.exceptionToString(e));
                }
            }
        } else {
            try {
                freshRecommendDailyMatchType();
            } catch (Exception e) {
                LOGGER.error("freshRecommendDailyMatchType failed player:{} {}", player.getUid(),
                        FunctionUtil.exceptionToString(e));
            }
        }
    }

    // 版本6刷新逻辑 最偏好>次偏好>三偏好>活跃度>纯随机
    public boolean freshRecommendDailyMatchTypeV6() {
        if (getPlayerReturnActivity().getUserConfId() < ReturningUserConfVersion6) {
            return false;
        }
        var returnStartMatchConfIns = ReturnStartMatchGiftData.getInstance();
        int id = getPlayerReturnActivity().getStartMatchGiftId();
        int beforeTaskId = 0;
        ResReturningUser.ReturnStartMatchGiftConfig config = null;
        if (id > 0) {
            var currentConfig = returnStartMatchConfIns.get(id);
            var runningTask = player.getTaskManager().getTask(currentConfig.getTaskId());
            if (runningTask != null) {
                if (runningTask.isComplete()) {
//                NKErrorCode.AlreadyCompleted
                    LOGGER.error("refresh config task already complete err, uid:{} taskId:{}", player.getUid(),
                            currentConfig.getTaskId());
                    return true;
                }
                beforeTaskId = runningTask.getTaskId();
            }
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("refresh debug, uid:{} currentId:{} algoRecommendMatchInfo:{} exclusiveNewsRecommend:{}",
                    player.getUid(), id, player.getUserAttr().getAlgoRecommendMatchInfo().toString(),
                    getPlayerReturnActivity().toString());
        }
        // 偏好推荐
        OUTER:
        for (int matchTypeId : player.getUserAttr().getAlgoRecommendMatchInfo().getMatchTypesList()) {
            for (var conf : returnStartMatchConfIns.getArrayList()) {
                if (conf.getMatchTypeIdsList().isEmpty()) {
                    continue;
                }
                if (getPlayerReturnActivity().getExclusiveNewsRecommend().contains(conf.getId())) { // 已经命中的过滤
                    continue;
                }
                if (conf.getMatchTypeIdsList().contains(matchTypeId)) {
                    config = conf;
                    getPlayerReturnActivity().getExclusiveNewsRecommend().add(config.getId()); // 下次刷新屏蔽
                    LOGGER.debug("refresh catch recommend debug, uid:{} currentId:{} afterId:{}",
                            player.getUid(), id, config.getId());
                    break OUTER;
                }
            }
        }
        // 活跃度推荐
        if (config == null) {
            for (var conf : returnStartMatchConfIns.getArrayList()) {
                if (getPlayerReturnActivity().getExclusiveNewsRecommend().contains(conf.getId())) { // 已经命中的过滤
                    continue;
                }
                if (conf.hasActiveLevel() && getPlayerReturnActivity().getActiveLevel() < conf.getActiveLevel()) {
                    config = conf;
                    getPlayerReturnActivity().getExclusiveNewsRecommend().add(config.getId()); // 下次刷新屏蔽
                    LOGGER.debug("refresh catch active debug, uid:{} currentId:{} afterId:{}",
                            player.getUid(), id, config.getId());
                    break;
                }
            }
        }
        // 随机
        if (config == null) {
            RandomByWeight<ResReturningUser.ReturnStartMatchGiftConfig> randomList = new RandomByWeight<>(false);
            for (var conf : returnStartMatchConfIns.getArrayList()) {
                if (getPlayerReturnActivity().getExclusiveNewsRecommend().contains(conf.getId())) { // 已经命中的过滤
                    continue;
                }
                randomList.add(conf, 1);
            }
            if (randomList.size() == 0) { // 纯随机
                if (id > 0) {
                    for (var conf : returnStartMatchConfIns.getArrayList()) {
                        if (id == conf.getId()) {
                            continue;
                        }
                        randomList.add(conf, 1);
                    }
                } else {
                    randomList.add(returnStartMatchConfIns.getArrayList());
                }
            }
            config = randomList.rand();
            if (!getPlayerReturnActivity().getExclusiveNewsRecommend().contains(config.getId())) {
                getPlayerReturnActivity().getExclusiveNewsRecommend().add(config.getId()); // 下次刷新屏蔽
            }
            LOGGER.debug("refresh catch random debug, uid:{} currentId:{} afterId:{}",
                    player.getUid(), id, config.getId());
        }
        if (config == null) {
            LOGGER.error("refresh config err, uid:{}", player.getUid());
            return true;
        }
        var taskConf = TaskConfData.getInstance().getTaskConf(config.getTaskId());
        if (taskConf == null) {
            LOGGER.error("get task conf err, uid:{} taskId:{}", player.getUid(), config.getTaskId());
            return true;
        }
        registerStartMatchGiftTask(config, false);
        player.getTaskManager().resetTask(config.getTaskId());
        getPlayerReturnActivity().setStartMatchGiftId(config.getId());
        if (beforeTaskId > 0) {
            player.getTaskManager().resetTask(beforeTaskId);
        }
        LOGGER.debug("registerTask task, uid:{} taskId:{}", player.getUid(), taskConf.getId());
        // 注册任务
        sendAttrChangeNtf();
        return true;
    }

    private void removeStartMatchTaskGroup() {
        var returnStartMatchConfIns = ReturnStartMatchGiftData.getInstance();
        for (var conf : returnStartMatchConfIns.getArrayList()) {
            player.getTaskManager().removeTask(conf.getTaskId());
            player.getTaskManager().deleteRunningTask(conf.getTaskId());
        }
    }

    private void registerStartMatchGiftTask(ResReturningUser.ReturnStartMatchGiftConfig config, boolean isLogin) {
        if (config == null && getPlayerReturnActivity().getStartMatchGiftId() == 0) {
            return;
        }
        if (config == null) {
            config = ReturnStartMatchGiftData.getInstance().get(getPlayerReturnActivity().getStartMatchGiftId());
        }
        if (config == null) {
            LOGGER.error("get ReturnStartMatchGift conf err, uid:{} startGiftMatchGiftId:{}", player.getUid(),
                    getPlayerReturnActivity().getStartMatchGiftId());
            return;
        }
        var taskConf = TaskConfData.getInstance().getTaskConf(config.getTaskId());
        if (taskConf == null) {
            LOGGER.error("get task conf err, uid:{} taskId:{}", player.getUid(), config.getTaskId());
            return;
        }
        var taskTimeInfo = new TaskLifeTime().setDoBeginTime(
                        getPlayerReturnActivity().getBeginTime() * DateUtils.ONE_SECOND_MILLIS)
                .setDoEndTime(getPlayerReturnActivity().getEndTime() * DateUtils.ONE_SECOND_MILLIS)
                .setShowBeginTime(getPlayerReturnActivity().getBeginTime() * DateUtils.ONE_SECOND_MILLIS)
                .setShowEndTime(getPlayerReturnActivity().getEndTime() * DateUtils.ONE_SECOND_MILLIS);
        player.getTaskManager().registerTask(config.getTaskId(), taskConf, taskTimeInfo);
        var runningTask = player.getTaskManager().getTask(config.getTaskId());
        if (runningTask != null) {
            CsTask.TaskChangeNtf.Builder ntf = CsTask.TaskChangeNtf.newBuilder();
            ntf.addChangeTask(runningTask.getTaskChangeInfo(false));
            player.sendNtfMsg(MsgTypes.MSG_TYPE_TASKCHANGENTF, ntf);
        }
    }

    /**
     * 刷新回流玩法推荐相关信息
     */
    public void freshRecommendDailyMatchType() {
        // 新版本推荐
        if (freshRecommendDailyMatchTypeV6()) {
            return;
        }
        if (getPlayerReturnActivity().getDailyReward().getExclusiveRecommendList().isEmpty()) {
            NKErrorCode.ReturnActivityCommon.throwError("ExclusiveRecommendList is empty");
            return;
        }

        int targetResDailyId = -1;
        ReturnActivityDailyReward dailyReward = getPlayerReturnActivity().getDailyReward();
        for (Integer matchType : player.getUserAttr().getAlgoRecommendMatchInfo().getMatchTypesList()) {
            int resDailyIndex = ReturningDailyBenefitsLimitData.getInstance().getResIdByMatchType(matchType);
            if (resDailyIndex >= 0 && !dailyReward.getExclusiveRecommendExcept().contains(resDailyIndex)) {
                targetResDailyId = resDailyIndex;
                break;
            }
        }

        List<Integer> dailyParamList = ReturningTabConfigData.getInstance().getTabConfigActiveLevelParamList(
                "ReturnBenefits", getPlayerReturnActivity().getActiveLevel());
        if (targetResDailyId == -1 && !dailyParamList.isEmpty() && !dailyReward.getExclusiveRecommendExcept()
                .contains(dailyParamList.get(0)) && ReturningDailyBenefitsLimitData.getInstance()
                .isConfIndexValid(dailyParamList.get(0))) {
            targetResDailyId = dailyParamList.get(0);
        }

        if (targetResDailyId == -1) {
            targetResDailyId = ReturningDailyBenefitsLimitData.getInstance()
                    .getValidMatchKey(dailyReward.getExclusiveRecommendExcept());
        }

        if (targetResDailyId == -1) {
            dailyReward.getExclusiveRecommendExcept().clear();
            dailyReward.getExclusiveRecommendExcept().addAll(dailyReward.getExclusiveRecommendList());
            dailyReward.getExclusiveRecommendExcept().remove(dailyReward.getExclusiveRecommend().get(0));
            targetResDailyId = ReturningDailyBenefitsLimitData.getInstance()
                    .getValidMatchKey(dailyReward.getExclusiveRecommendExcept());
        }

        if (targetResDailyId != -1) {
            dailyReward.getExclusiveRecommend().set(0, targetResDailyId);
            dailyReward.getExclusiveRecommendExcept().add(targetResDailyId);
        } else {
            dailyReward.getExclusiveRecommendExcept().add(dailyReward.getExclusiveRecommend().get(0));
            NKErrorCode.ReturnActivityCommon.throwError("too little ReturningDailyBenefitsLimitData");
        }

        sendAttrChangeNtf();
    }

    private void sendAttrChangeNtf() {
        player.getUserAttrMgr().collectAndSyncDirtyToClient();
        CsActivity.ReturnActivityAttrChangeNtf.Builder ntf = CsActivity.ReturnActivityAttrChangeNtf.newBuilder();
        player.sendNtfMsg(MsgTypes.MSG_TYPE_RETURNACTIVITYATTRCHANGENTF, ntf);
    }

    /**
     * 重置回流相关活动所牵引的任务进度
     *
     * @param activityId 活动ID
     */
    private void refreshBaseTaskActivity(int activityId) {
        BaseTaskActivity activity = player.getActivityManager().getRunningActivity(activityId, BaseTaskActivity.class);
        if (activity != null) {
            activity.refresh();
        } else {
            LOGGER.error("no BaseTaskActivity id:{}", activityId);
        }
    }

    private boolean isBaseTaskActivityTaskInvalid(int activityId) {
        BaseTaskActivity activity = player.getActivityManager().getRunningActivity(activityId, BaseTaskActivity.class);
        if (activity == null) {
            return true;
        }

        for (int taskGroupId : activity.getActivityTaskGroupList()) {
            ResTask.TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(taskGroupId);
            if (taskGroup == null) {
                continue;
            }

            for (int taskId : taskGroup.getTaskIdListList()) {
                if (player.getTaskManager().getTask(taskId) == null) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 定制奖励任务选取奖励
     *
     * @param taskId 任务Id
     * @param itemId 奖励Id
     * @param itemNum 奖励数量
     * @return 成功或失败错误码
     */
    @Deprecated // 任务系统已经支持可选取奖励
    public NKErrorCode setOptionalRewardTask(int taskId, int itemId, int itemNum) {
        RunTask runTask = player.getTaskManager().getTask(taskId);
        if (runTask == null) {
            LOGGER.error("runTask:{} not found", taskId);
            return NKErrorCode.ReturnActivitySetOptionalRewardTaskFailed;
        }

        var rewardTaskInfo = getPlayerReturnActivity().getRewardOptionalTask().get(taskId);
        if (rewardTaskInfo == null) {
            LOGGER.error("invalid taskId:{}", taskId);
            return NKErrorCode.ReturnActivitySetOptionalRewardTaskFailed;
        }

        if (rewardTaskInfo.getItemIdList().size() <= 1) {
            LOGGER.error("already set or reward too less taskId:{}", taskId);
            return NKErrorCode.ReturnActivitySetOptionalRewardTaskFailed;
        }

        for (int i = 0; i < rewardTaskInfo.getItemIdList().size() && i < rewardTaskInfo.getItemNumList().size(); ++i) {
            if (rewardTaskInfo.getItemId(i) != itemId) {
                continue;
            }
            if (itemNum != rewardTaskInfo.getItemNum(i)) {
                LOGGER.error("taskId:{} itemId:{} receivedNum:{} not equal to confNum:{}",
                        taskId, itemId, itemNum, rewardTaskInfo.getItemNum(i));
                return NKErrorCode.ReturnActivitySetOptionalRewardTaskFailed;
            }
            rewardTaskInfo.clearItemId();
            rewardTaskInfo.clearItemNum();
            rewardTaskInfo.addItemId(itemId);
            rewardTaskInfo.addItemNum(itemNum);

            player.getUserAttrMgr().collectAndSyncDirtyToClient();
            player.getTaskManager().addChangeTaskInfo(runTask.getTaskChangeInfo(false));
            player.getTaskManager().sendTaskChangeNtf();

            return NKErrorCode.OK;
        }

        LOGGER.error("taskId:{} received invalid itemId:{}", taskId, itemId);
        return NKErrorCode.ReturnActivitySetOptionalRewardTaskFailed;
    }

    /**
     * 获取可选奖励任务的奖励信息
     *
     * @param taskId 任务Id
     * @param item 奖励信息
     * @return 成功或者失败错误码
     */
    public NKErrorCode fillOptionalRewardTaskInfo(int taskId, TaskRewardConf.Builder item) {
        var rewardTaskInfo = getPlayerReturnActivity().getRewardOptionalTask().get(taskId);
        if (rewardTaskInfo == null) {
            LOGGER.error("invalid taskId:{}", taskId);
            return NKErrorCode.ReturnActivityFillInOptionalRewardTaskFailed;
        }

        if (rewardTaskInfo.getItemIdList().isEmpty()) {
            LOGGER.error("taskId:{} rewardList id empty", taskId);
            return NKErrorCode.ReturnActivityFillInOptionalRewardTaskFailed;
        }

        item.clearItemIdList();
        item.clearNumList();
        item.addItemIdList(rewardTaskInfo.getItemId(0));
        item.addNumList(rewardTaskInfo.getItemNum(0));

        return NKErrorCode.OK;
    }

    /**
     * 特权期间每日累计奖励（不要求在线）
     *
     * @param returningUserBait 档位配置
     * @param dayNum 回流日期偏移
     * @return 成功或失败错误码
     */
    private NKErrorCode sendAutoDailyReward(ReturningUserBait returningUserBait, int dayNum, DAILY_REWARD_REASON reason) {
        List<Item> items = new ArrayList<>();
        for (Item item : returningUserBait.getAutoDailyRewardList()) {
            items.add(
                    Item.newBuilder().setItemId(item.getItemId()).setItemNum(item.getItemNum() * dayNum).build());
        }
        ChangedItems changedItems = new ChangedItems(items, ItemChangeReason.ICR_ReturnActivityAutoDailyReward_VALUE,
                "");

        boolean needNtf = false;
        if (reason != DAILY_REWARD_REASON.DAILY_REWARD
                || getPlayerReturnActivity().getUserConfId() >= ReturningUserConfVersion6) {
            needNtf = true;
        }

        // 加道具
        NKPair<NKErrorCode, ItemChangeDetails> ret = player.getBagManager().AddItems2(changedItems, needNtf);
        if (!ret.getKey().isOk()) {
            LOGGER.error("SendReturnActivityReward AddItems2 failed:{}", ret.getKey());
            return ret.getKey();
        }

        return NKErrorCode.OK;
    }

    /**
     * 发送设置日历提醒奖励
     *
     * @return 成功或失败错误码
     */
    public NKErrorCode sendCalendarReward() {
        if (!isPrivilegeValid()) {
            LOGGER.error("player:{} returnPrivilege is invalid", player.getUid());
            return NKErrorCode.ReturnActivityAlreadyDisable;
        }

        if (getPlayerReturnActivity().getCalendarRewardReceived()) {
            LOGGER.error("player:{} calendarReward already Received", player.getUid());
            return NKErrorCode.ReturnActivityCalendarRewardAlreadyGot;
        }

        ReturningUserBait returningUserBait = getResReturningUserBait(getPlayerReturnActivity().getUserConfId(),
                getPlayerReturnActivity().getUserBaitIndex());
        if (null == returningUserBait) {
            LOGGER.error("player:{} getReturningUserBaitByIndex failed confId:{} baitIndex:{}", player.getUid(),
                    getPlayerReturnActivity().getUserConfId(), getPlayerReturnActivity().getUserBaitIndex());
            return NKErrorCode.ReturnActivityResUserBaitNotExists;
        }

        getPlayerReturnActivity().setCalendarRewardReceived(true);
        return sendAutoDailyReward(returningUserBait, 1, DAILY_REWARD_REASON.CALENDAR_REWARD);
    }

    /**
     * 尝试发送每日自动奖励
     */
    public void trySendAutoDailyReward(int sourceType) {
        if (!isReturnActivityActive()) {
            if (sourceType == CS_MSG) {
                NKErrorCode.UnknownError.throwError("isReturnActivityActive err");
            }
            return;
        }

        ReturningUserBait returningUserBait = getResReturningUserBait(getPlayerReturnActivity().getUserConfId(),
                getPlayerReturnActivity().getUserBaitIndex());
        if (null == returningUserBait) {
            LOGGER.error("player:{} getReturningUserBaitByIndex failed confId:{} baitIndex:{}", player.getUid(),
                    getPlayerReturnActivity().getUserConfId(), getPlayerReturnActivity().getUserBaitIndex());
            if (sourceType == CS_MSG) {
                NKErrorCode.ReturnActivityBookExpRewardNotExist.throwError("returningUserBait null");
            }
            return;
        }

        //版本6改为手动领奖
        if (sourceType != CS_MSG && getPlayerReturnActivity().getUserConfId() >= ReturningUserConfVersion6) {
            LOGGER.debug("uid:{} userConf:{}", player.getUid(), getPlayerReturnActivity().getUserConfId());
            return;
        }

        if (getPlayerReturnActivity().getUserConfId() < ReturningUserConfVersion6) {
            // 任务完成之后不再自动发奖
            if (returningUserBait.getTaskOptionalRewardList().isEmpty() || player.getTaskManager()
                    .getTask(returningUserBait.getTaskOptionalReward(0).getTaskId()).isComplete()) {
                if (sourceType == CS_MSG) {
                    NKErrorCode.ReturnActivityBookRewardAlreadyGot.throwError("all task completed");
                }
                return;
            }
        }

        int dayOffset = (int) TimeUnit.SECONDS.toDays(DateUtils.currentTimeSec() - DateUtils.getDayBeginTimeSec(
                getPlayerReturnActivity().getBeginTime()));
        int dayOffsetChangeValue = dayOffset - getPlayerReturnActivity().getBeginDayOffset();
        if (dayOffsetChangeValue == 0) {
            LOGGER.debug("dayOffsetChangeValue equal 0, uid:{} dayOffset:{} beginDayOffset:{}", player.getUid(),
                    dayOffset, getPlayerReturnActivity().getBeginDayOffset());
            if (sourceType == CS_MSG) {
                NKErrorCode.ReturnActivityBookExpRewardNotExist.throwError("dayOffsetChangeValue equal 0");
            }
            return;
        }
        getPlayerReturnActivity().setBeginDayOffset(dayOffset);
        NKErrorCode sendDailyRewardRet = sendAutoDailyReward(returningUserBait, dayOffsetChangeValue, DAILY_REWARD_REASON.DAILY_REWARD);
        if (!sendDailyRewardRet.isOk()) {
            LOGGER.error("player:{} sendAutoDailyReward failed", player.getUid());
        }
    }

    /**
     * 构建农场buff相关协议参数
     *
     * @return 农场buff交互协议信息
     */
    private ArrayList<SsFarmsvr.FarmBuffArgs> buildFarmBuffArgs() {
        ArrayList<SsFarmsvr.FarmBuffArgs> buffList = new ArrayList<>();
        for (Integer buffId : getPlayerReturnActivity().getActiveFarmBuffIdsList()) {
            SsFarmsvr.FarmBuffArgs.Builder farmBuffArgs = SsFarmsvr.FarmBuffArgs.newBuilder()
                    .setKey(Integer.toString(buffId));
            SsFarmsvr.FarmBuffArg.Builder farmBuffArg = SsFarmsvr.FarmBuffArg.newBuilder()
                    .setStartTimeSec(getPlayerReturnActivity().getBeginTime())
                    .setEndTimeSec(getPlayerReturnActivity().getPrivilegeEndTime());
            farmBuffArg.setBuffId(buffId);
            farmBuffArgs.addBuffArgs(farmBuffArg);
            buffList.add(farmBuffArgs.build());
        }
        return buffList;
    }

    private boolean isPlayerFarmValid() {
        return player.getUserAttr().getFarmInfo().getMyFarmInfo().getHasFarm();
    }

    /**
     * 异步幂等添加农场buff
     */
    private void asyncAddFarmBuffs() {
        if (!isPlayerFarmValid() || !getPlayerReturnActivity().getActiveFarmBuffIdsList().isEmpty()) {
            LOGGER.debug("isPlayerFarmValid:{}", isPlayerFarmValid());
            return;
        }

        ReturningUserBait returningUserBait = getResReturningUserBait(getPlayerReturnActivity().getUserConfId(),
                getPlayerReturnActivity().getUserBaitIndex());
        if (null == returningUserBait) {
            return;
        }
        getPlayerReturnActivity().getActiveFarmBuffIds().addAll(returningUserBait.getPrivilegeFarmBuffListList());
        SsFarmsvr.RpcFarmBuffAddReq.Builder req = SsFarmsvr.RpcFarmBuffAddReq.newBuilder()
                .setFarmId(player.getUid())
                .setSource(Source.PlayerReturnActivity.toString())
                .addAllBuffArgs(buildFarmBuffArgs());

        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                var ret = FarmService.get().rpcFarmBuffAdd(req);
                if (!ret.isOK()) {
                    LOGGER.error("player:{} rpcFarmBuffAdd failed", player.getUid());
                }
                return null;
            }, "rpcFarmBuffAdd", true);
        } catch (Exception e) {
            LOGGER.error("rpcFarmBuffAdd failed");
        }
    }

    /**
     * 异步幂等移除农场buff
     */
    private void asyncRemoveFarmBuffs() {
        if (!isPlayerFarmValid() || getPlayerReturnActivity().getActiveFarmBuffIdsList().isEmpty()) {
            LOGGER.debug("isPlayerFarmValid:{}", isPlayerFarmValid());
            return;
        }

        SsFarmsvr.RpcFarmBuffDelReq.Builder req = SsFarmsvr.RpcFarmBuffDelReq.newBuilder()
                .setFarmId(player.getUid())
                .setSource(Source.PlayerReturnActivity.toString())
                .addAllBuffArgs(buildFarmBuffArgs());

        getPlayerReturnActivity().getActiveFarmBuffIds().clear();
        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                var ret = FarmService.get().rpcFarmBuffDel(req);
                if (!ret.isOK()) {
                    LOGGER.error("player:{} rpcFarmBuffDel failed", player.getUid());
                }
                return null;
            }, "rpcFarmBuffDel", true);
        } catch (Exception e) {
            LOGGER.error("rpcFarmBuffDel failed");
        }
    }
    public enum DAILY_REWARD_REASON {
        DAILY_REWARD,
        CALENDAR_REWARD,
    }

    public static boolean isReturningRelation(DBRelation dbRelation) {
        return dbRelation.getReturning() && dbRelation.getReturnExpiredSec() > Framework.currentTimeSec();
    }

    public void playerStartGame() {
        player.getUserAttr().getTaskInfo().getExtraInfo().getReturnTaskInfo()
                .setLastGameTime((int) Framework.currentTimeSec());
    }

    private class OnFarmCreate implements EventConsumer {

        private final Player player;

        public OnFarmCreate(Player player) {
            this.player = player;
        }

        @SubscribeEvent(routers = EventRouterType.ERT_ConsumerPlayerFarmCreate)
        protected NKErrorCode onEvent(PlayerFarmCreateEvent event) throws NKRuntimeException {
            if (isPrivilegeValid()) {
                asyncAddFarmBuffs();
            }
            return NKErrorCode.OK;
        }

        @Override
        public boolean isDestroyed() {
            return false;
        }
    }
}

package com.tencent.wea.playerservice.mall.price;

import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;

/**
 * 价格计算上下文
 * 包含价格计算所需的所有信息
 */
public class PriceContext {
    private final Player player;
    private final MallCommodity commodityConf;
    private final int buyNum;
    private final long originalPrice;
    private final boolean isDirectBuy;
    private final String businessBillNo;
    
    public PriceContext(Player player, MallCommodity commodityConf, int buyNum, 
                       long originalPrice, boolean isDirectBuy, String businessBillNo) {
        this.player = player;
        this.commodityConf = commodityConf;
        this.buyNum = buyNum;
        this.originalPrice = originalPrice;
        this.isDirectBuy = isDirectBuy;
        this.businessBillNo = businessBillNo;
    }
    
    public Player getPlayer() {
        return player;
    }
    
    public MallCommodity getCommodityConf() {
        return commodityConf;
    }
    
    public int getBuyNum() {
        return buyNum;
    }
    
    public long getOriginalPrice() {
        return originalPrice;
    }
    
    public boolean isDirectBuy() {
        return isDirectBuy;
    }
    
    public String getBusinessBillNo() {
        return businessBillNo;
    }
    
    public int getCommodityId() {
        return commodityConf.getCommodityId();
    }
    
    public int getCoinType() {
        return commodityConf.getCoinType();
    }
    
    public long getUid() {
        return player.getUid();
    }
}

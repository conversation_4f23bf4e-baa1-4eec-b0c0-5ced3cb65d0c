package com.tencent.wea.playerservice.mall.price;

/**
 * 价格修改结果
 */
public class PriceModificationResult {
    private final long modifiedPrice;
    private final boolean priceChanged;
    private final String reason;
    private final String modifierName;
    
    public PriceModificationResult(long modifiedPrice, boolean priceChanged, String reason, String modifierName) {
        this.modifiedPrice = modifiedPrice;
        this.priceChanged = priceChanged;
        this.reason = reason;
        this.modifierName = modifierName;
    }
    
    public static PriceModificationResult noChange(long originalPrice) {
        return new PriceModificationResult(originalPrice, false, "No modification", "None");
    }
    
    public static PriceModificationResult changed(long newPrice, String reason, String modifierName) {
        return new PriceModificationResult(newPrice, true, reason, modifierName);
    }
    
    public long getModifiedPrice() {
        return modifiedPrice;
    }
    
    public boolean isPriceChanged() {
        return priceChanged;
    }
    
    public String getReason() {
        return reason;
    }
    
    public String getModifierName() {
        return modifierName;
    }
    
    @Override
    public String toString() {
        return String.format("PriceModificationResult{price=%d, changed=%s, reason='%s', modifier='%s'}", 
                           modifiedPrice, priceChanged, reason, modifierName);
    }
}

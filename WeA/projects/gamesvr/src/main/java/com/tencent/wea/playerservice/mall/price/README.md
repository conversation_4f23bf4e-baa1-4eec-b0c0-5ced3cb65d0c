# 商品价格扩展系统

## 概述

商品价格扩展系统是一个基于策略模式的可扩展架构，允许根据不同的业务逻辑动态修改商品购买价格。系统支持多种价格修改器，可以根据优先级顺序应用，实现复杂的价格计算逻辑。

## 架构设计

### 核心组件

1. **PriceCalculator** - 价格计算器，负责协调所有价格修改器
2. **PriceModifierRegistry** - 价格修改器注册表，管理所有修改器
3. **PriceModifier** - 价格修改器接口，定义修改器的基本行为
4. **PriceContext** - 价格计算上下文，包含计算所需的所有信息
5. **PriceModificationResult** - 价格修改结果，包含修改后的价格和原因

### 工作流程

```
商品购买请求 -> PriceCalculator -> 获取适用的修改器 -> 按优先级应用修改器 -> 返回最终价格
```

## 使用方法

### 1. 创建自定义价格修改器

实现 `PriceModifier` 接口：

```java
public class CustomPriceModifier implements PriceModifier {
    @Override
    public String getName() {
        return "CustomPriceModifier";
    }
    
    @Override
    public int getPriority() {
        return 500; // 数值越小优先级越高
    }
    
    @Override
    public boolean isApplicable(PriceContext context) {
        // 判断是否适用于当前上下文
        return true;
    }
    
    @Override
    public PriceModificationResult modifyPrice(PriceContext context, long currentPrice) {
        // 实现价格修改逻辑
        long newPrice = currentPrice * 0.8; // 8折
        return PriceModificationResult.changed(newPrice, "自定义折扣20%", getName());
    }
}
```

### 2. 注册价格修改器

在 `PriceModifierInitializer` 中注册：

```java
public static void initialize() {
    PriceModifierRegistry registry = PriceModifierRegistry.getInstance();
    
    // 注册自定义修改器
    registry.registerModifier(new CustomPriceModifier());
}
```

### 3. 使用价格计算器

在 `MallManager` 中使用：

```java
// 计算最终价格
long finalPrice = calculateFinalUnitPrice(commodityConf, buyNum, isDirectBuy, businessBillNo);

// 获取详细计算信息
PriceCalculationDetail detail = priceCalculator.calculateFinalPriceWithDetail(
    player, commodityConf, buyNum, isDirectBuy, businessBillNo);
```

## 内置价格修改器

### 1. VipPriceModifier (优先级: 100)
- 根据玩家VIP等级提供价格折扣
- VIP等级越高，折扣越大

### 2. ActivityPriceModifier (优先级: 200)
- 根据当前进行的活动提供价格折扣
- 支持多种活动类型

### 3. LevelPriceModifier (优先级: 300)
- 根据玩家等级提供价格折扣
- 等级达到特定阈值时享受折扣

### 4. SeasonPriceModifier (优先级: 400)
- 根据当前赛季提供特殊价格折扣
- 赛季期间的特殊优惠

## 扩展指南

### 添加新的价格修改器

1. **创建修改器类**
   - 实现 `PriceModifier` 接口
   - 定义修改器名称和优先级
   - 实现适用性检查逻辑
   - 实现价格修改逻辑

2. **注册修改器**
   - 在 `PriceModifierInitializer.initialize()` 方法中注册
   - 或者在运行时动态注册

3. **测试修改器**
   - 编写单元测试验证修改器逻辑
   - 测试与其他修改器的交互

### 配置管理

建议通过配置文件管理修改器的开关和参数：

```java
private boolean isModifierEnabled() {
    return PropertyFileReader.getRealTimeBooleanItem("custom_price_modifier_enabled", true);
}

private double getDiscountRate() {
    return PropertyFileReader.getRealTimeDoubleItem("custom_discount_rate", 0.8);
}
```

## 注意事项

1. **优先级设计**
   - 数值越小优先级越高
   - 建议按100的间隔设置优先级
   - 避免优先级冲突

2. **性能考虑**
   - 修改器的 `isApplicable` 方法应该高效
   - 避免在价格计算中进行复杂的数据库查询
   - 考虑缓存计算结果

3. **错误处理**
   - 修改器应该优雅处理异常
   - 异常不应该影响其他修改器的执行
   - 记录详细的错误日志

4. **向后兼容**
   - 新增修改器不应该影响现有功能
   - 保持原有价格计算逻辑作为基础

## 监控和调试

系统提供了详细的日志记录：

```java
// 启用调试日志
LOGGER.debug("Price modified by '{}' for commodity {} from {} to {}: {}", 
           modifier.getName(), context.getCommodityId(), 
           originalPrice, newPrice, result.getReason());
```

可以通过 `PriceCalculationDetail` 获取完整的价格计算过程信息，用于调试和监控。

package com.tencent.wea.playerservice.mall.price.modifiers;

import com.tencent.wea.playerservice.mall.price.PriceContext;
import com.tencent.wea.playerservice.mall.price.PriceModificationResult;
import com.tencent.wea.playerservice.mall.price.PriceModifier;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * VIP价格修改器
 * 根据玩家VIP等级提供价格折扣
 */
public class VipPriceModifier implements PriceModifier {
    private static final Logger LOGGER = LogManager.getLogger(VipPriceModifier.class);
    
    private static final String MODIFIER_NAME = "VipPriceModifier";
    private static final int PRIORITY = 100;
    
    // VIP折扣配置 - 可以从配置文件读取
    private static final double[] VIP_DISCOUNTS = {
        1.0,    // VIP 0 - 无折扣
        0.95,   // VIP 1 - 5%折扣
        0.90,   // VIP 2 - 10%折扣
        0.85,   // VIP 3 - 15%折扣
        0.80,   // VIP 4 - 20%折扣
        0.75,   // VIP 5 - 25%折扣
        0.70,   // VIP 6 - 30%折扣
        0.65,   // VIP 7 - 35%折扣
        0.60,   // VIP 8 - 40%折扣
        0.55,   // VIP 9 - 45%折扣
        0.50    // VIP 10+ - 50%折扣
    };
    
    @Override
    public String getName() {
        return MODIFIER_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean isApplicable(PriceContext context) {
        // 检查是否启用VIP折扣功能
        if (!isVipDiscountEnabled()) {
            return false;
        }
        
        // 检查商品是否支持VIP折扣
        if (!isCommoditySupportVipDiscount(context)) {
            return false;
        }
        
        // 检查玩家VIP等级
        int vipLevel = getPlayerVipLevel(context);
        return vipLevel > 0;
    }
    
    @Override
    public PriceModificationResult modifyPrice(PriceContext context, long currentPrice) {
        int vipLevel = getPlayerVipLevel(context);
        double discount = getVipDiscount(vipLevel);
        
        if (discount >= 1.0) {
            return PriceModificationResult.noChange(currentPrice);
        }
        
        long newPrice = Math.round(currentPrice * discount);
        
        // 确保价格不为负数
        if (newPrice < 0) {
            newPrice = 0;
        }
        
        String reason = String.format("VIP%d折扣%.0f%%", vipLevel, (1.0 - discount) * 100);
        
        LOGGER.debug("VIP price modification: uid={}, commodityId={}, vipLevel={}, " +
                    "originalPrice={}, newPrice={}, discount={}", 
                    context.getUid(), context.getCommodityId(), vipLevel, 
                    currentPrice, newPrice, discount);
        
        return PriceModificationResult.changed(newPrice, reason, MODIFIER_NAME);
    }
    
    /**
     * 检查是否启用VIP折扣功能
     * @return 是否启用
     */
    private boolean isVipDiscountEnabled() {
        // 这里可以从配置文件或开关控制
        // return PropertyFileReader.getRealTimeBooleanItem("vip_price_discount_enabled", true);
        return true; // 暂时默认启用
    }
    
    /**
     * 检查商品是否支持VIP折扣
     * @param context 价格计算上下文
     * @return 是否支持
     */
    private boolean isCommoditySupportVipDiscount(PriceContext context) {
        // 可以通过商品配置的特定字段或标签来判断
        // 例如：检查商品是否有特定的shopTag
        // 这里暂时默认所有商品都支持VIP折扣
        return true;
    }
    
    /**
     * 获取玩家VIP等级
     * @param context 价格计算上下文
     * @return VIP等级
     */
    private int getPlayerVipLevel(PriceContext context) {
        // 这里需要根据实际的VIP系统获取玩家VIP等级
        // 暂时返回一个示例值
        try {
            // 假设有VIP管理器
            // return context.getPlayer().getVipManager().getVipLevel();
            return 0; // 暂时返回0，表示非VIP
        } catch (Exception e) {
            LOGGER.error("Error getting player VIP level: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 获取VIP折扣率
     * @param vipLevel VIP等级
     * @return 折扣率
     */
    private double getVipDiscount(int vipLevel) {
        if (vipLevel <= 0) {
            return 1.0; // 无折扣
        }
        
        if (vipLevel >= VIP_DISCOUNTS.length) {
            return VIP_DISCOUNTS[VIP_DISCOUNTS.length - 1]; // 最高等级折扣
        }
        
        return VIP_DISCOUNTS[vipLevel];
    }
}

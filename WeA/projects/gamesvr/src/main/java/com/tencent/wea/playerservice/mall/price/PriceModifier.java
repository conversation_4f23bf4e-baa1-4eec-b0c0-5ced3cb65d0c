package com.tencent.wea.playerservice.mall.price;

/**
 * 价格修改器接口
 * 用于实现各种特殊的价格修改逻辑
 */
public interface PriceModifier {
    
    /**
     * 获取修改器名称
     * @return 修改器名称
     */
    String getName();
    
    /**
     * 获取修改器优先级，数值越小优先级越高
     * @return 优先级
     */
    int getPriority();
    
    /**
     * 检查是否适用于当前上下文
     * @param context 价格计算上下文
     * @return 是否适用
     */
    boolean isApplicable(PriceContext context);
    
    /**
     * 修改价格
     * @param context 价格计算上下文
     * @param currentPrice 当前价格
     * @return 价格修改结果
     */
    PriceModificationResult modifyPrice(PriceContext context, long currentPrice);
}

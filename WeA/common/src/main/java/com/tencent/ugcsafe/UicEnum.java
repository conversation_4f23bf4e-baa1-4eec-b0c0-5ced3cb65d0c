package com.tencent.ugcsafe;

import java.util.HashMap;
import java.util.Map;

// Author wenhuazheng
public class UicEnum {

    public static UicEnum.SUB_SCENE getDefaultSubScene(UicEnum.SCENE scene) {
        switch (scene) {
            case INFORMATION:
                return UicEnum.SUB_SCENE.DEFAULT_INFORMATION_SUB_ID;
            case IM:
                return UicEnum.SUB_SCENE.DEFAULT_IM_SUB_ID;
            case FORUM:
                return UicEnum.SUB_SCENE.DEFAULT_FORUM_SUB_ID;
            default:
                return UicEnum.SUB_SCENE.DEFAULT_IM_SUB_ID;
        }
    }

    public static UicEnum.PLATFORM transferPlatId(int platId) {
        switch (platId) {
            case 0:
                return PLATFORM.IOS;
            case 1:
                return PLATFORM.ANDROID;
            default:
                return PLATFORM.OTHERS;
        }
    }

    // 旧版的, 新版是否兼容未知, 保留, 但停用
    @Deprecated
    public enum TEXT_ENCODING {
        INPUT_UTF8(1),
        INPUT_URL(2),
        INPUT_BASE64(3),
        INPUT_GBK(4),
        ;

        private final int value;

        TEXT_ENCODING(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }
    }


    public enum URI {
        CHECK_TEXT("/check/batch_text"),                    // 文本检测接口
        CHECK_PIC("/check/syn/batch_picture"),              // 图片检测接口
        CHECK_TEXT_AND_PIC("/check/syn/text_and_picture"),  // 图文混合检测接口
        ;

        private final String url;

        URI(String url) {
            this.url = url;
        }

        public String getUrl() {
            return this.url;
        }
    }

    // 旧版的, 新版是否兼容未知, 保留, 但停用
    @Deprecated
    public enum TEXT_LANG {
        EN(1)/*英语*/,
        CHT(2)/*繁体中文*/,
        VN(3)/*越南语*/,
        CHS(4)/*简体中文*/,
        TH(5)/*泰文*/,
        KO(6)/*韩文*/,
        FR(7)/*法文*/,
        DE(8)/*德文*/,
        RU(9)/*俄文*/,
        TR(10)/*土耳其*/,
        PTEU(11)/*葡萄牙(欧洲)*/,
        PTLT(12)/*葡萄牙(拉美)*/,
        ESEU(13)/*西班牙(欧洲)*/,
        ESLT(14)/*西班牙(拉美)*/,
        IT(15)/*意大利*/,
        NL(16)/*荷兰语*/,
        MY(17)/*马来语*/,
        ID(18)/*印度尼西亚语*/,
        AR(19)/*阿拉伯语*/,
        INDIA(20)/*印度语*/,
        JP(21)/*日本语*/,
        ;

        private final int value;

        TEXT_LANG(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }
    }


    // UGC场景大类ID,不同类型检测标准并不一样，上报的时候务必区分开来
    public enum SCENE {
        INFORMATION(1), // 1：资料类, 比如昵称，签名，房间名，队伍名等；
        IM(2),          // 2：即时通讯类, 比如游戏聊天等
        FORUM(3),       // 3：社区类, 比如论坛、帖子，评论等
        ;

        private final int value;

        SCENE(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }
    }

    /**
     * 业务场景 ID：表示业务任意 UGC 内容产生的场景 ID，由业务填写，并需要在验收时给出业务全量场景 ID 列表；
     * 注意：任何用户产生的 UGC 内容必须审核并且定义独立的 ID，记录到表格给到安全验收
     * <p>
     * 资料类：100-999
     * 即时通讯类：1000-1999
     * 社区类：2000-2999
     */
    public enum
    SUB_SCENE {
        DEFAULT_INFORMATION_SUB_ID(100),            // 资料类默认值
        UGC_MAP_NAME_PUBLISH(101),                  // ugc 地图发布名称
        UGC_MAP_DESC(102),                          // ugc 地图描述
        UGC_MAP_PIC(103),                           // ugc 地图缩略图
        UGC_MAP_WORD(104),                          // ugc地图写字板
        LETS_GO_SIGNATURE(105),                     // 爆笑向前冲个性签名
        DANCING_KING_SIGNATURE(106),                // 超舞王者个性签名
        USER_HEAD_URL(107),                         // 用户头像
        USER_NICK(108),                             // 用户昵称
        UGC_MAP_NAME_CREATE(111),                   // 创建地图名称
        UGC_EVENT(112),                             // 事件命名
        UGC_SPORT_NAME(113),                        // 运动器名字修改
        UGC_BUTTON(114),                            // 修改按钮名字
        UGC_TEXT_BOX(115),                          // 修改文本框内容
        UGC_GROUP_NAME(116),                        // 创建DIY组件
        UGC_ROOM(117),                              // 创建房间
        AI_TEXT(118),                               // AI生成参考图文本
        AI_PIC(119),                                // AI生成参考图图片
        UGC_MAP_CHECK(120),                         // UGC地图审核(管理平台侧使用)
        UGC_SPORT_EVENT(121),                       // 运动器组件调用事件
        UGC_MAP_NAME(122),                          // 设置界面修改地图名称
        ENTER_RECOM_POOL_PERSON_REV(123),           // 进入推荐池人审(管理平台侧使用)
        UGC_GROUP_PUB_PIC(124),                     // 发布组合缩略图
        UGC_GROUP_PUB_NAME(125),                    // 发布组合名称
        UGC_GROUP_PUB_DESC(126),                    // 发布组合描述
        QQ_MUSIC_SEARCH(127),                       // 音乐搜索(管理平台侧使用)
        COMBINATION_PIC_CHECK(129),                 // 组合拍照审核(管理平台侧使用)
        XIAOWO_DESC_PUBLISH(131),                   // 家园描述
        XIAOWO_IMAGE_PUBLISH(132),                  // 家园封面
        FIREWORKS_SHOW_TEXT(134),                   // 烟花秀文本
        AI_CHANGE_COLOR_TEXT(135),                  // AI换色文本
        AI_CHANGE_COLOR_PIC(136),                   // AI换色参考图
        XIAOWO_PARTY_INSTRUCTION_PUBLISH(138),      // 家园派对描述
        XIAOWO_PARTY_IMAGE_PUBLISH(139),            // 家园派对封面
        UGC_MAP_TOPIC(143),                         // UGC地图话题
        UGC_MAP_YUAN_JIAN_BIAO_QIAN_CREATE(145),    // UGC创建元件标签
        XIAWO_LAYOUT_NAME(147),                     // 家园方案名称
        XIAWO_FIREWORK(148),                        // 星家园烟花秀
        UGC_CREATEARCHIVING(149),                   // 地图存档
        CLUB_NAME(152),                             // 社团自定义名称
        CLUB_BRIEF(153),                            // 社团自定义宣言
        CONVERSATION_EDITOR_NPC_NAME(154),          // 编辑器内给NPC命名
        CUSTOM_ACTIONS_NAME(155),                   // 自定义动作命名
        CONVERSATION_EDITOR_SPEAKER_NAME(156),      // 对话编辑器说话者名称
        CONVERSATION_EDITOR_SPEAK_CONTENT(157),     // 对话编辑器说话内容
        CONVERSATION_EDITOR_OPTION_BRANCH(158),     // 对话编辑器选项分支
        CONVERSATION_SCRIPT_NAME(160),              // 对话剧本命名
        CAMERA_LEN_TEMPLATE_NAME(161),              // 相机镜头模板命名
        UGC_FIREARM_NAME(163),                      // ugc枪械名称编辑
        UGC_AI_GEN_ANICAP(164),                     // 视频动捕视频审核
        XIAOWO_WELCOME_CONTENT(165),                // 家园欢迎词
        XIAOWO_LIUYAN_MESSAGE(166),                 // 家园留言
        UGC_COLLECTION_NAME(181),                   // UGC合集名
        UGC_COLLECTION_DESC(182),                   // UGC合集简介
        UGC_PRE_AUDIT_COVER(183),                   // ugc前置地图
        UGC_DIY_SHOP_NAME(184),                     // UGC自定义商品名
        UGC_DIY_SHOP_DESC(185),                     // UGC自定义商品描述
        UGC_DIY_TASK_NAME(187),                     // UGC自定义任务名
        UGC_DIY_TASK_DESC(188),                     // UGC自定义任务描述
        UGC_SKILL_NAME(193),                        // UGC技能名称
        UGC_BUFF_NAME(194),                         // UGC buff名称
        UGC_SKILL_EFFECT_NAME(195),                 // UGC技能效果器名称
        UGC_SKILL_SWITCH_BUTTON(196),               // 技能切换器按钮显示
        UGC_SKILL_RELEASE_BUTTON(197),              // UGC技能释放按钮
        ACTIVITY_SQUAD_NAME(198),                   // 小队名称
        UGC_RANK_TITLE(199),                        // UGC排行榜名称
        UGC_RANK_SCORE_TITLE(200),                  // UGC 排行榜 积分 名称
        ALBUM_PIC(201),                             // 相册
        FARM_LIUYAN_MESSAGE(202),                   // 农场留言
        FARM_WELCOME_CONTENT(203),                  // 农场寄语
        CUSTOM_SKELETON_ACTIONS_NAME(204),          // 自制骨骼动作名称
        SKELETON_NAME(205),                         // 骨骼名称
        SKELETON_MODEL_NAME(206),                   // 骨骼模型命名
        COMPONENT_CUSTOM_ATTR_NAME(208),            // 元件自定义属性名称
        COMPONENT_CUSTOM_ATTR_PROMPT(209),          // 元件自定义属性提示
        CODING_COMMUNITY_TEMPLATE_NAME(211),        // 可视化编程模板名称
        CODING_COMMUNITY_TEMPLATE_DESC(212),        // 可视化编程模板描述
        USER_CATCHPHRASE(215),                      // 个性签名
        COMPETITION_SAVE_INFO_NAME(216),            // 赛事信息登记姓名
        FARM_GIFT_MESSAGE(218),                     // 农场礼物留言
        BAG_INTERACT_COM_CHAT(222),                 // 背包互动组合聊天内容
        BAG_INTERACT_COM_NAME(223),                 // 背包互动组合名称
        HOUSE_ITEM_GRID_INFO(224),                  // 小屋家具占格
        UGC_GENERATE_SMALL_MAP(227),                // ugc生成小地图
        FARM_PET_NAME(232),                         // 农场宠物名字
        UGC_AI_EDIT_ASSISTANT_CHAT(233),            // AI编辑助手聊天
        MALL_GIFT_CARD_WORDS(234),                  // 外观类商品赠礼卡自定义祝福语
        COC_VILLAGER_CUSTOM_NAME(236),              // coc村民自定义名称
        AIGC_NPC_PROFILE(239),                      // AI伙伴资料
        AIGC_NPC_PLAYER_PROFILE(240),               // AI伙伴个人信息
        MULTI_SIDE_NAME(243),                       // 多阵营-阵营名称
        FARM_SIGNATURE(247),                        // 农场个性签名
        CUSTOM_LOADING(251),                        // 自定义loading
        LOBBY_COVER(252),                           // 自定义导航图
        ALBUM_PIC_EDIT_TEXT(254),                   // 相册编辑文字检测
        FARM_PARTY_IMAGE(253),                      // 农场派对封面图
        FARM_PARTY_DESC(255),                       // 农场派对描述
        FARM_COOK_FLOATING_SCREEN_CONTENT(266),     // 农场餐厅流动显示屏自定义文本

        SEARCH_GROUP(4001),                         // 搜索DIY组件
        SEARCH_MAP(4002),                           // 搜索地图
        SEARCH_FRIEND(4003),                        // 搜索好友
        SEARCH_PARTY(4007),                         // 搜索派对
        SEARCH_CLUB_MEMBER(4008),                   // 社团内搜索社团成员
        SEARCH_CLUB_INVITE(4009),                   // 邀请朋友进社团时搜索好友
        SEARCH_CLUB(4010),                          // 搜索社团名称

        DEFAULT_IM_SUB_ID(1000),                    // 即时通讯类默认值
        WORLD_CHAT_MSG(1001),                       // 世界聊天
        FRIEND_CHAT_MSG(1002),                      // 好友聊天
        ISLAND_CHAT_MSG(1003),                      // 岛屿聊天
        TEAM_CHAT_MSG(1004),                        // 组队聊天
        MAP_CHAT_MSG(1005),                         // 地图聊天
        NEW_STAR_CHAT_MSG(1007),                    // 新人聊天
        NEW_XIAOWO_CHAT_MSG(1008),                  // 家园聊天
        RECRUIT_PUBLISH_MSG(1009),                  // 招募发布
        CLUB_CHAT_MSG(1011),                        // 社团聊天
        DANMU_MSG(1014),                            // 自定义弹幕
        MAP_DANMU_MSG(1015),                        // 地图弹幕
        AIGC_ANSWER_PROMPT(1016),                   // 智能对话
        AIGC_ANSWER_RESPONSE(1017),                 // 智能对话回答
        NEW_FARM_CHAT_MSG(1018),                    // 农场聊天
        NEW_NR3E8_CHAT_MSG(1019),                    // NR3E8聊天
        STRANGER_CHAT_MSG(1023),                    // 陌生人聊天
        WOLFKILL_COMMUNITY_CHANNEL_CHAT_MSG(1024),  // 狼人杀社区频道
        FARM_COMMUNITY_CHANNEL_CHAT_MSG(1025),      // 农场社区频道
        ARENA_COMMUNITY_CHANNEL_CHAT_MSG(1026),     // 峡谷社区频道
        TRADING_CARD_COMMUNITY_CHANNEL_CHAT_MSG(1027),  // 卡牌社区频道
        AIGC_NPC_PAL_PROMPT(1028),                  // AI伙伴聊天
        BIRTHDAY_CARD_CUSTOM_WORDS(1029),           // 生日贺卡自定义祝福语

        STARP_ROOM_NAME_MSG(237),                   // 啾灵-房间名
        STARP_ROOM_DESC_MSG(238),                   // 啾灵-房间描述
        STARP_SEARCH_ROOM_PIC(239),                 // 啾灵房间图片使用用户自拍图片
        STARP_SEARCH_ROOM_MSG(4011),                // 啾灵搜索房间
        STARP_CHAT_MSG(1037),                       // 啾灵世界频道
        STARP_GUILD_CHAT_MSG(1038),                 // 啾灵公会频道
        STARP_GROUP_NAME_MSG(1039),                   // 啾灵-宗门名
        STARP_GROUP_DESC_MSG(1040),                   // 啾灵-宗门描述
        STARP_PETTRADE_NOTES_MSG(1051),             // 啾灵-啾灵交换备注

        //  下面的安全那边都没有提供。但是代码有调用。暂时就先放着吧
        LC_HALL_CHAT_MSG(10007),                    // 王者新次元大厅聊天
        LC_PRIVATE_CHAT_MSG(10008),                 // 王者新次元好友私聊
        DEFAULT_FORUM_SUB_ID(20000),                // 社区类默认值
        PRIVATE_CHAT_MSG(10004),                    // 私聊

        ZUOYEBANG_IMAGE(5002), // 作业帮图片
        ZUOYEBANG_TEXT(5003), // 作业帮文字
        // 4000~5000段留给客户端那边上报用了,这里不要使用
        ;

        private static final Map<Integer, SUB_SCENE> intToTypeMap = new HashMap<Integer, SUB_SCENE>();

        static {
            for (SUB_SCENE type : SUB_SCENE.values()) {
                intToTypeMap.put(type.value, type);
            }
        }

        private final int value;

        SUB_SCENE(int value) {
            this.value = value;
        }

        public static SUB_SCENE fromInt(int v) {
            SUB_SCENE type = intToTypeMap.get(v);
            if (type != null) {
                return type;
            }
            return null;
        }

        public int getValue() {
            return this.value;
        }
    }

    public enum ACCOUNT_TYPE {
        ACCOUNT_TYPE_QQ(1),         // QQ(32 位)
        ACCOUNT_TYPE_WECHAT(2),     // 微信 openid(可以是 28、32 个字节)
        ACCOUNT_TYPE_QQ_OPENID(4),  // QQopenid
        ACCOUNT_TYPE_QQ_VISITOR(7), // 游客账号
        ACCOUNT_TYPE_GOPENID(8),    // Gopenid
        ACCOUNT_TYPE_COMMON(601),   // 通用账号
        ACCOUNT_TYPE_UUID(1013),    // UUID,一般是游戏自己的账号体系
        ;

        private final int value;

        ACCOUNT_TYPE(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }
    }

    public enum PLATFORM {
        IOS(0),
        ANDROID(1),
        OTHERS(2),
        PC(3),
        ;

        private final int value;

        PLATFORM(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }
    }

    public enum UIC_CHECK_URL {
        ZPLAN_ACE("https://ucn-cn.uic.anticheatexpert.com"),        // zplan接入
        CN_INTRANET("http://ucn-in.uic.anticheatexpert.com/uic"),   // deprecated, 中国(内网), 文本+图片
        CN_INTERNET("http://ucn-cn.uic.anticheatexpert.com/uic"),   // deprecated, 中国(公网), 文本+图片
        US("http://uovs-us.uic.anticheatexpert.com/uic"),           // deprecated, 美国-硅谷, 文本
        DE("http://uovs-de.uic.anticheatexpert.com/uic"),           // deprecated, 德国-法兰克福, 文本
        SG("http://uovs-sg.uic.anticheatexpert.com/uic"),           // deprecated, 新加坡, 文本
        KR("http://uovs-kr.uic.anticheatexpert.com/uic"),           // deprecated, 韩国-首尔, 文本
        ;


        private final String url;

        UIC_CHECK_URL(String url) {
            this.url = url;
        }

        public String getUrl() {
            return this.url;
        }
    }
}
